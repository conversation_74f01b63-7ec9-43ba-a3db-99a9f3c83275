// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.olympian.client

import anduin.standaloneapp.client.{ReactApp, StandaloneAppUtils}
import scala.scalajs.js.annotation.{JSExport, JSExportTopLevel}

@JSExportTopLevel("OlympianMain")
object OlympianMainApp {

  @JSExport
  def main(args: Array[String]): Unit = {
    ReactApp(
      name = "olympian",
      router = _ => StandaloneAppUtils.getReactRouter(OlympianRouter.olympianRouter),
      containerId = "olympianContainer"
    ).run()
  }

}
