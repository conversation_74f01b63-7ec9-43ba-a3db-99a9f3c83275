// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.dataprotection.modal

import design.anduin.components.button.Button
import design.anduin.components.checkbox.Checkbox
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.modal.{ConfirmationModal, Modal, ModalBody, ModalFooterWCancel}
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import anduin.portal.dataprotection.component.ExportJobConfig
import anduin.entity.model.EntityModel
import anduin.id.entity.EntityId
import anduin.portal.dataprotection.{CreateJobParams, DataExportJobEndpointClient}
import anduin.protobuf.dataprotection.ExportEntity
import design.anduin.components.toast.Toast

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import com.anduin.stargazer.client.utils.ZIOUtils

private[dataprotection] final case class ExportEntityDataConfigModal(
  entityId: EntityId,
  entityModel: EntityModel,
  onClose: Callback,
  onCreated: Callback
) {

  def apply(): VdomElement = ExportEntityDataConfigModal.component(this)
}

private[dataprotection] object ExportEntityDataConfigModal {

  private type Props = ExportEntityDataConfigModal

  private final case class State(
    shouldExportDb: Boolean = true,
    shouldExportFiles: Boolean = true,
    shouldExportTrxns: Boolean = true,
    shouldExportUsers: Boolean = true,
    shouldDeleteExported: Boolean = false,
    isCreating: Boolean = false
  )

  private final class Backend(scope: BackendScope[Props, State]) {

    private def toRequest(props: Props, state: State): ExportEntity = {
      ExportEntity(
        props.entityId,
        shouldExportDb = state.shouldExportDb,
        shouldExportFiles = state.shouldExportFiles,
        shouldExportTrxns = state.shouldExportTrxns,
        shouldExportUsers = state.shouldExportUsers,
        shouldDeleteDb = state.shouldDeleteExported && state.shouldExportDb,
        shouldDeleteFiles = state.shouldDeleteExported && state.shouldExportFiles,
        shouldDeleteTrxns = state.shouldDeleteExported && state.shouldExportTrxns
      )
    }

    private def onCreateRequest: Callback = {
      for {
        props <- scope.props
        state <- scope.state
        _ <- scope.modState(_.copy(isCreating = true))
        _ <- ZIOUtils.toReactCallback {
          val request = toRequest(props, state)
          DataExportJobEndpointClient
            .createRequest(CreateJobParams(request))
            .map(
              _.fold(
                _ =>
                  scope.modState(
                    _.copy(isCreating = false),
                    Toast.errorCallback("An error occurred when creating the request")
                  ),
                _ =>
                  for {
                    _ <- scope.modState(_.copy(isCreating = false))
                    _ <- Toast.successCallback("Entity request created")
                    _ <- props.onCreated
                  } yield ()
              )
            )
        }
      } yield ()
    }

    private def renderExportOption(state: State): VdomElement = {
      React.Fragment(
        <.div(tw.mb4.mt12.fontSemiBold, "Export data"),
        <.div(
          tw.ml12,
          Checkbox(
            isChecked = state.shouldExportDb,
            onChange = _ => Callback.empty,
            isDisabled = true // Do not allow disable this selection
          )("Export the entity in database"),
          Checkbox(
            isChecked = state.shouldExportTrxns,
            onChange = checked => scope.modState(_.copy(shouldExportTrxns = checked))
          )("Export all transactions"),
          Checkbox(
            isChecked = state.shouldExportFiles,
            onChange = checked => scope.modState(_.copy(shouldExportFiles = checked))
          )("Export all files"),
          Checkbox(
            isChecked = state.shouldExportUsers,
            onChange = checked => scope.modState(_.copy(shouldExportUsers = checked))
          )("Export all users")
        )
      )
    }

    private def copyIdWarning(props: Props) = {
      <.div(
        tw.flex.inlineFlex.textWarning4.mt4.ml12,
        IconR(name = Icon.Glyph.Warning, size = Icon.Size.Px16)(),
        <.span(
          tw.ml4.italic,
          "Please note down the entity ID (",
          <.span(tw.fontSemiBold, props.entityId.idString),
          ") to query the request result in the Request view of the Data Protection tool."
        )
      )
    }

    private def renderDeleteOptions(props: Props, state: State): VdomElement = {
      React.Fragment(
        <.div(tw.mt12.mb4.fontSemiBold, "Delete data"),
        <.div(
          tw.ml12,
          Checkbox(
            isChecked = state.shouldDeleteExported,
            onChange = checked => scope.modState(_.copy(shouldDeleteExported = checked)),
            isDisabled = !state.shouldExportDb
          )("Delete exported data"),
          TagMod.when(state.shouldDeleteExported) {
            <.div(
              <.div(
                tw.flex.inlineFlex.textWarning4.mt4.ml12,
                IconR(name = Icon.Glyph.Warning, size = Icon.Size.Px16)(),
                <.span(
                  tw.ml4.italic,
                  "Users will not be deleted. Please manually review the users and" +
                    "delete each of the individuals separately."
                )
              ),
              copyIdWarning(props)
            )
          }
        )
      )
    }

    private def renderExportButton(props: Props, state: State): VdomElement = {
      val createReqBtn = (onClick: Callback) =>
        Button(
          style = Button.Style.Full(color = Button.Color.Primary, isBusy = state.isCreating),
          onClick = onClick,
          isDisabled = state.isCreating
        )("Create a new request")

      if (state.shouldDeleteExported) {
        Modal(
          title = "Delete data requested",
          renderTarget = createReqBtn,
          renderContent = close =>
            ConfirmationModal(
              content = React.Fragment(
                <.p("The following entity data will be cleared and unrecoverable. Please confirm."),
                <.div(
                  tw.ml8.mt8,
                  ExportJobConfig(toRequest(props, state))()
                ),
                copyIdWarning(props)
              ),
              onConfirm = onCreateRequest,
              confirmBtnLabel = "Confirm",
              confirmBtnColor = Button.Color.Primary,
              onCloseModal = close
            )()
        )()
      } else {
        createReqBtn(onCreateRequest)
      }
    }

    def render(props: Props, state: State): VdomElement = {
      React.Fragment(
        ModalBody()(
          <.div(
            tw.mt12.text15,
            s"Select the following options to create a data export request for entity ${props.entityId.idString}."
          ),
          renderExportOption(state),
          renderDeleteOptions(props, state)
        ),
        ModalFooterWCancel(
          props.onClose
        )(
          renderExportButton(props, state)
        )
      )
    }

  }

  private val component = ScalaComponent
    .builder[ExportEntityDataConfigModal](getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
