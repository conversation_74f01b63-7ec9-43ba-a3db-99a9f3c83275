// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.dataprotection

import anduin.service.GeneralServiceException
import anduin.tapir.client.AuthenticatedEndpointClient
import zio.Task

object DataExportJobEndpointClient extends AuthenticatedEndpointClient {

  val createRequest: CreateJobParams => Task[Either[GeneralServiceException, CreateJobResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataExportJobEndpoints.createRequest)

  val cancelRequest: CancelJobParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(DataExportJobEndpoints.cancelRequest)

  val queryJobResult: QueryJobResultsParams => Task[Either[GeneralServiceException, QueryJobResultsResponse]] =
    toClientThrowDecodeAndSecurityFailures(DataExportJobEndpoints.queryJobResult)

}
