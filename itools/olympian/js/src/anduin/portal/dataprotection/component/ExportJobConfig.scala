// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.dataprotection.component

import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import anduin.protobuf.dataprotection.{DataExportRequest, ExportEntity, ExportUser}

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[dataprotection] final case class ExportJobConfig(
  request: DataExportRequest
) {
  def apply(): VdomElement = ExportJobConfig.component(this)
}

private[dataprotection] object ExportJobConfig {
  private type Props = ExportJobConfig

  private def renderConfigItem(
    shouldRender: Boolean,
    isDeletion: Boolean,
    text: String
  ) = {
    TagMod.when(shouldRender) {
      val icon = if (isDeletion) Icon.Glyph.Minus else Icon.Glyph.FileExport
      <.div(
        tw.flex.itemsCenter.my4,
        <.div(
          if (isDeletion) tw.textDanger3 else tw.textPrimary3,
          IconR(name = icon, size = Icon.Size.Px16)()
        ),
        <.p(tw.ml4, text)
      )
    }
  }

  private def renderEntityExportConfig(config: ExportEntity) = {
    <.div(
      renderConfigItem(
        config.shouldExportDb,
        false,
        "Export entity data in databases"
      ),
      renderConfigItem(
        config.shouldExportTrxns,
        false,
        "Export all entity's transactions"
      ),
      renderConfigItem(
        config.shouldExportFiles,
        false,
        "Export all entity's files"
      ),
      renderConfigItem(
        config.shouldExportUsers,
        false,
        "Export all entity's users"
      ),
      renderConfigItem(
        config.shouldDeleteDb,
        true,
        "Delete entity data in databases"
      ),
      renderConfigItem(
        config.shouldDeleteTrxns,
        true,
        "Delete all entity's transactions"
      ),
      renderConfigItem(
        config.shouldDeleteFiles,
        true,
        "Delete all entity's files"
      )
    )
  }

  private def renderUserExportConfig(config: ExportUser) = {
    <.div(
      renderConfigItem(
        shouldRender = true,
        false,
        "Export personal data"
      ),
      renderConfigItem(
        config.shouldExportFiles,
        false,
        "Export all user's files"
      ),
      renderConfigItem(
        config.shouldDeleteUser,
        true,
        "Delete personal data"
      ),
      renderConfigItem(
        config.shouldDeleteFiles,
        true,
        "Delete user's files"
      )
    )
  }

  private def render(props: Props): VdomElement = {
    props.request match {
      case entity: ExportEntity => renderEntityExportConfig(entity)
      case user: ExportUser     => renderUserExportConfig(user)
      case _                    => <.div()
    }
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
