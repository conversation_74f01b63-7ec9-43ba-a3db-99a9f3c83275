// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.dataprotection.component

import anduin.account.admin.SearchUserInfo
import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.modal.{Modal, ModalBody}
import design.anduin.components.tab.deprecated.DeprecatedTab
import design.anduin.components.table.Table

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[dataprotection] final case class UserPersonalDataTable(
  users: Seq[SearchUserInfo],
  afterUserManageCloseCb: Callback
) {

  def apply(): VdomElement = UserPersonalDataTable.component(this)
}

private[dataprotection] object UserPersonalDataTable {

  private val UserTable = (new Table[SearchUserInfo])()
  private type Props = UserPersonalDataTable

  private def renderManageUser(props: Props, user: SearchUserInfo): VdomElement = {
    Modal(
      title = s"Manage ${user.userInfo.fullNameString} (ID: ${user.userId.idString}) personal data",
      size = Modal.Size(Modal.Width.Px1160),
      renderTarget = open => {
        Button(
          style = Button.Style.Minimal(icon = Some(Icon.Glyph.Cog)),
          onClick = open
        )("Manage")
      },
      renderContent = _ => {
        ModalBody()(
          DeprecatedTab(
            panels = Seq(
              DeprecatedTab.Panel(
                title = "Export data requests",
                renderContent = (_: DeprecatedTab.RenderPanelContent) => PersonalDataExportRequestPerUser(user.userId)()
              ),
              DeprecatedTab.Panel(
                title = "User personal data",
                renderContent = (_: DeprecatedTab.RenderPanelContent) => ManageUserPersonalData(user.userId)()
              )
            )
          )()
        )
      },
      afterUserClose = props.afterUserManageCloseCb
    )()
  }

  private def render(props: Props): VdomElement = {
    UserTable(
      rows = props.users,
      getKey = _.userId.idString,
      columns = List(
        Table.Column(
          "ID",
          user => Table.Cell(user.userId.idString)
        ),
        Table.Column(
          "Email",
          user => Table.Cell(user.userInfo.emailAddressStr)
        ),
        Table.Column(
          "Full name",
          user => Table.Cell(user.userInfo.fullNameString)
        ),
        Table.Column(
          "Is zombie",
          user => Table.Cell(if (user.userAttributes.isZombie) "Zombie" else "")
        ),
        Table.Column(
          "",
          user => Table.Cell(renderManageUser(props, user))
        )
      )
    )()
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
