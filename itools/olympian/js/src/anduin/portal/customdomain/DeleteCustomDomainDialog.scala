// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.customdomain

import anduin.customdomain.{CustomDomainClient, DeleteCustomDomainParams}
import anduin.id.customdomain.CustomDomainId
import design.anduin.components.button.Button
import design.anduin.components.modal.{<PERSON>dalBody, ModalFooter}
import design.anduin.components.toast.Toast
import design.anduin.components.well.Well
import design.anduin.components.well.react.WellR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import com.raquo.laminar.api.L.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import com.anduin.stargazer.client.utils.ZIOUtils

final case class DeleteCustomDomainDialog(
  onClose: Callback,
  customDomainId: CustomDomainId,
  domainName: String
) {
  def apply(): VdomElement = DeleteCustomDomainDialog.component(this)
}

object DeleteCustomDomainDialog {

  type Props = DeleteCustomDomainDialog

  private case class State(
    btnBusy: Boolean
  )

  private class Backend(scope: BackendScope[Props, State]) {

    private def deleteCustomDomain(props: Props) = {
      val task = CustomDomainClient.deleteCustomDomain(DeleteCustomDomainParams(props.customDomainId)).map {
        _.fold(
          error => Toast.errorCallback(error.getMessage),
          _ => Toast.successCallback("Custom domain deleted").flatMap(_ => props.onClose)
        )
      }
      for {
        _ <- scope.modState(_.copy(btnBusy = true))
        _ <- ZIOUtils.toReactCallback(task)
        _ <- scope.modState(_.copy(btnBusy = false))
      } yield ()
    }

    def render(props: Props, state: State): VdomElement = {
      React.Fragment(
        ModalBody()(
          WellR(style = Well.Style.Warning())(
            "Are you sure you want to remove custom domain ",
            span(tw.fontBold, props.domainName),
            "? This action cannot be undone."
          )
        ),
        ModalFooter()(
          <.div(
            tw.flex.justifyEnd,
            Button(
              style = Button.Style.Full(color = Button.Color.Danger, isBusy = state.btnBusy),
              onClick = deleteCustomDomain(props)
            )("Delete this custom domain")
          )
        )
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State(btnBusy = false))
    .renderBackend[Backend]
    .build

}
