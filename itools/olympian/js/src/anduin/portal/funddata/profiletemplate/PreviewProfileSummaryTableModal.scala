// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.funddata.profiletemplate

import com.raquo.laminar.api.L.*
import design.anduin.components.layout.HStack
import design.anduin.components.modal.laminar.ModalBodyL
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.style.tw.*

import anduin.forms.{FormData, FormTableRenderer}
import anduin.forms.engine.{GaiaEngine, GaiaState}
import anduin.forms.engine.GaiaEngine.{EngineConfiguration, EngineContext}
import anduin.forms.utils.FormFillingProgressCalculationUtils
import anduin.funddata.api.WithFirmProfileForm
import anduin.id.funddata.FundDataFirmId

private[profiletemplate] final case class PreviewProfileSummaryTableModal(
  firmId: FundDataFirmId
) {

  def apply(): HtmlElement = {
    ModalBodyL(
      WithFirmProfileForm(
        firmId,
        render = renderProps =>
          div(
            height.px(600),
            child <-- renderProps.isGettingSignal.map(
              if (_) {
                BlockIndicatorL(title = Val(Some("Please wait while we generate a summary mode preview...")))()
              } else {
                div(
                  tw.flex.flexCol,
                  div(
                    "This is how your profile will be display in the ",
                    span(tw.fontSemiBold, "Profile"),
                    " tab"
                  ),
                  child.maybe <-- renderProps.profileFormSignal.map { profileFormOpt =>
                    for {
                      profileForm <- profileFormOpt
                      gaiaEngine <- GaiaEngine
                        .make(
                          profileForm.form,
                          EngineConfiguration.default,
                          EngineContext.default
                        )
                        .toOption
                      profileData <- gaiaEngine.replay(List.empty).map(_._1).toOption
                    } yield renderProfileSummaryTable(
                      profileForm,
                      profileData,
                      gaiaEngine
                    ).amend(tw.flexFill.mt24)
                  }
                )
              }
            )
          )
      )()
    )
  }

  private def renderProfileSummaryTable(
    profileForm: FormData,
    profileData: GaiaState,
    gaiaEngine: GaiaEngine
  ) = {
    FormTableRenderer(
      form = profileForm.form,
      formState = profileData,
      engine = gaiaEngine,
      layout = Some(layout =>
        HStack(
          modifier = HStack
            .Modifier()
            .content((ele: HtmlElement) => ele.amend(tw.mr20))
            .withRightSidebar()
            .withStickySidebar(width = Option(300))
        )(
          HStack.Slots.Sidebar(layout.renderTableOfContents),
          HStack.Slots.Content(layout.renderContainer.amend(layout.renderMainForm))
        )
      ),
      fillingProgressCalculationMode = FormFillingProgressCalculationUtils.IgnoreDisabledField
    )()
  }

}
