// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.funddata.environment

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.toast.Toast
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.wrapper.laminar.WrapperL
import design.anduin.style.tw.*
import japgolly.scalajs.react.*
import zio.ZIO

import anduin.funddata.client.FundDataFirmEndpointClient
import anduin.funddata.endpoint.firm.FundDataFirm
import anduin.funddata.endpoint.firm.environment.SetFirmEnvironmentParams
import anduin.id.environment.EnvironmentId
import anduin.id.offering.OfferingId
import anduin.portal.environment.EnvironmentAdminProtocols.{
  EnvironmentWithPrimaryPlatformDomain,
  GetEnvironmentWithPrimaryPlatformDomainParams
}
import anduin.portal.environment.{EnvironmentAdminClient, EnvironmentTable, SelectEnvironmentModal}
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.utils.ZIOUtils
import stargazer.component.routing.laminar.WithReactRouterL

private[funddata] final case class FundDataEnvironmentSetting(
  firm: FundDataFirm
) {

  private val isLoadingEnvVar = Var[Boolean](false)
  private val environmentOptVar = Var[Option[EnvironmentWithPrimaryPlatformDomain]](None)

  private val isUpdatingEnvVar = Var[Boolean](false)

  def apply(): HtmlElement = {
    div(
      onMountCallback { _ =>
        firm.environmentIdOpt.foreach(getEnvironment)
      },
      tw.flex.flexCol,
      renderHeader,
      child <-- isLoadingEnvVar.signal.distinct.splitBoolean(
        whenTrue = _ => BlockIndicatorL()(),
        whenFalse = _ =>
          div(
            child <-- environmentOptVar.signal.map(
              _.fold(
                div(
                  tw.flexFill.flex.itemsCenter.justifyCenter,
                  NonIdealStateL(
                    icon = img(src := "/web/gondor/images/portal/search-environment.svg"),
                    title = "No environment linked",
                    description = "Link an environment to apply its settings to this IDM firm",
                    action = renderSelectOrUpdateEnvironment(None)
                  )()
                )
              ) { environment =>
                div(
                  renderSelectOrUpdateEnvironment(Some(environment)),
                  WithReactRouterL { router =>
                    WrapperL(
                      EnvironmentTable(
                        renderMode = EnvironmentTable.View(
                          onDeleteEnvironment = Some(_ => Callback { updateEnvironment(None) }),
                          offeringName = firm.name
                        ),
                        environments = Seq(environment),
                        router = router
                      )()
                    ).amend(tw.mt20)
                  }
                )
              }
            )
          )
      )
    )
  }

  private def renderHeader = {
    div(
      div(tw.fontSemiBold.text23.mt16, s"Environment"),
      div(tw.bgGray3.my24.wPc100, height := "1px")
    )
  }

  private def renderSelectOrUpdateEnvironment(
    environmentOpt: Option[EnvironmentWithPrimaryPlatformDomain]
  ) = {
    ModalL(
      renderTitle = _ => "Select environment",
      renderTarget = openModal =>
        environmentOpt.fold(
          ButtonL(
            style = ButtonL.Style.Full(color = ButtonL.Color.Primary, isBusy = isUpdatingEnvVar.signal),
            onClick = openModal.contramap(_ => ())
          )("Select environment")
        ) { _ =>
          ButtonL(
            style = ButtonL.Style.Ghost(icon = Some(Icon.Glyph.Eye), isBusy = isUpdatingEnvVar.signal),
            onClick = openModal.contramap(_ => ())
          )("Change environment")
        },
      renderContent = closeModal =>
        WrapperL(
          SelectEnvironmentModal(
            targetOfferingIdOpt = Some(OfferingId.IDM),
            selectedEnvironmentOpt = firm.environmentIdOpt,
            onSelectEnvironment = { environmentId =>
              Callback { updateEnvironment(Some(environmentId)) }
            },
            closeModal = Callback { closeModal.onNext(()) }
          )()
        ),
      size = ModalL.Size(width = ModalL.Width.Px960)
    )()
  }

  private def getEnvironment(environmentId: EnvironmentId): Unit = {
    val task = for {
      _ <- ZIO.attempt(isLoadingEnvVar.set(true))
      _ <- EnvironmentAdminClient
        .getEnvironmentsWithPrimaryPlatformDomain(
          GetEnvironmentWithPrimaryPlatformDomainParams(
            GetEnvironmentWithPrimaryPlatformDomainParams.Single(environmentId),
            targetOfferingIdOpt = Some(OfferingId.IDM)
          )
        )
        .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        .map(
          _.fold(
            ex => {
              isLoadingEnvVar.set(false)
              Toast.error(s"Failed to get environment. Error: ${ex.getMessage}")
            },
            resp =>
              Var.set(
                isLoadingEnvVar -> false,
                environmentOptVar -> resp.environments.headOption
              )
          )
        )
    } yield ()
    ZIOUtils.runAsync(task)
  }

  private def updateEnvironment(environmentIdOpt: Option[EnvironmentId]): Unit = {
    val task = for {
      _ <- ZIO.attempt(isUpdatingEnvVar.set(true))
      _ <- FundDataFirmEndpointClient
        .setFirmEnvironment(
          SetFirmEnvironmentParams(firm.firmId, environmentIdOpt)
        )
        .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        .map(
          _.fold(
            ex => {
              isUpdatingEnvVar.set(false)
              Toast.error(s"Failed to update firm environment. Error: ${ex.getMessage}")
            },
            _ => {
              isUpdatingEnvVar.set(false)
              environmentIdOpt.fold(environmentOptVar.set(None)) { environmentId =>
                getEnvironment(environmentId)
              }
            }
          )
        )
    } yield ()
    ZIOUtils.runAsync(task)
  }

}
