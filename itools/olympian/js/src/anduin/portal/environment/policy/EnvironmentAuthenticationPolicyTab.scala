// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.environment.policy

import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.portal.environment.policy.enforcement.EnvironmentEnforcementPolicyTab
import anduin.protobuf.environment.EnvironmentData

final case class EnvironmentAuthenticationPolicyTab(environmentData: EnvironmentData) {
  def apply(): VdomElement = EnvironmentAuthenticationPolicyTab.component(this)
}

object EnvironmentAuthenticationPolicyTab {

  private type Props = EnvironmentAuthenticationPolicyTab

  private final case class State()

  private class Backend(scope: BackendScope[Props, State]) { // scalafix:ok

    def render(props: Props): VdomElement = {
      <.div(
        tw.spaceY16,
        EnvironmentEnforcementPolicyTab(props.environmentData)(),
        EnvironmentReauthenticationPolicyTab(props.environmentData)()
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
