// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.environment.policy

import anduin.id.account.EnterpriseLoginConfigId
import anduin.portal.account.enterprise.EnterpriseLoginConfigItem
import anduin.protobuf.environment.policy.EnvironmentReauthenticationPolicy
import anduin.protobuf.fundsub.environment.policy.FundSubAppReauthenticationPolicyBindingType
import design.anduin.components.button.Button
import design.anduin.components.modal.{ModalBody, ModalFooter}
import design.anduin.components.well.Well
import design.anduin.components.well.react.WellR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import com.raquo.laminar.api.L.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.portal.environment.policy.FundSubAppConfig.SelectedSSOConfig

final case class EnvironmentReauthenticationApplicationConfigStep(
  oldPolicy: Option[EnvironmentReauthenticationPolicy],
  selectedSSOConfigs: Map[EnterpriseLoginConfigId, EnterpriseLoginConfigItem],
  existingAppConfig: Option[EnvironmentReauthenticationApplicationConfigStep.AppConfigData],
  onBack: Option[Callback],
  onNext: EnvironmentReauthenticationApplicationConfigStep.AppConfigData => Callback,
  nextButtonText: String
) {
  def apply(): VdomElement = EnvironmentReauthenticationApplicationConfigStep.component(this)
}

object EnvironmentReauthenticationApplicationConfigStep {

  private type Props = EnvironmentReauthenticationApplicationConfigStep

  final case class FundSubAppConfigData(
    roleItems: Seq[FundSubAppConfig.RoleItem],
    isSharableLinkEnabled: Boolean
  )

  final case class AppConfigData(
    fundSubAppConfig: FundSubAppConfigData
  )

  sealed trait ValidationError {
    def message: String
  }

  object ValidationError {

    case object FundSubEmptyRoleBinding extends ValidationError {
      override def message: String = "Empty role binding in Fund Sub settings"
    }

    case object FundSubSharableLinkInvalidLpBinding extends ValidationError {
      override def message: String = "Cannot set Lp Binding to Fallback when sharable link is enabled"
    }

  }

  private final case class State(
    fundSubAppConfig: FundSubAppConfigData,
    errors: Seq[ValidationError]
  )

  private class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      <.div(
        ModalBody()(
          <.div(
            tw.fontSemiBold.text17.leading28,
            "Application SSO configurations"
          ),
          <.div(
            tw.text13.leading20.textGray7,
            "Configure SSO configurations for user roles per application. Drag to reorder the priority of each configuration."
          ),
          <.div(
            tw.mt16,
            FundSubAppConfig(
              oldPolicy = props.oldPolicy,
              selectedSSOConfigs = props.selectedSSOConfigs,
              existingConfig = props.existingAppConfig.map(_.fundSubAppConfig.roleItems),
              onChange = (roleItems, isSharableLinkEnabled) =>
                scope.modState(
                  _.copy(fundSubAppConfig = FundSubAppConfigData(roleItems, isSharableLinkEnabled)),
                  runValidate()
                )
            )()
          )
        ),
        ModalFooter()(
          state.errors.headOption.fold(EmptyVdom) { firstError =>
            <.div(
              tw.mb12,
              WellR(style = Well.Style.Danger(icon = None))(firstError.message)
            )
          },
          <.div(
            tw.flex.justifyBetween,
            <.div(
              props.onBack.fold(EmptyVdom) { onBack =>
                Button(onClick = onBack)("Back")
              }
            ),
            <.div(
              Button(
                style = Button.Style.Full(color = Button.Color.Primary),
                isDisabled = state.errors.nonEmpty,
                onClick = props.onNext(AppConfigData(state.fundSubAppConfig))
              )(props.nextButtonText)
            )
          )
        )
      )
    }

    private def validate(state: State): Seq[ValidationError] = {
      Seq(
        validateFundSubApp(state.fundSubAppConfig)
      ).flatten
    }

    private def validateFundSubApp(fundSubAppConfig: FundSubAppConfigData): Seq[ValidationError] = {
      val hasEmptyRoleBinding = fundSubAppConfig.roleItems.map(_.bindingType).toSet != Set(
        FundSubAppReauthenticationPolicyBindingType.Gp,
        FundSubAppReauthenticationPolicyBindingType.Lp,
        FundSubAppReauthenticationPolicyBindingType.GpCollaborator,
        FundSubAppReauthenticationPolicyBindingType.LpCollaborator
      ) ||
        fundSubAppConfig.roleItems.map(_.selectedSSOConfig).exists(_.isEmpty)
      val invalidLpRoleBindingWhenEnableSharableLInk = if (fundSubAppConfig.isSharableLinkEnabled) {
        fundSubAppConfig.roleItems
          .find(_.bindingType == FundSubAppReauthenticationPolicyBindingType.Lp)
          .flatMap(_.selectedSSOConfig)
          .fold(false) {
            case SelectedSSOConfig.SomeSSOConfig(_)     => false
            case SelectedSSOConfig.AnduinAuthentication => false
            case SelectedSSOConfig.FallbackPolicy       => true
          }
      } else {
        false
      }
      Seq(
        if (hasEmptyRoleBinding) {
          Some(ValidationError.FundSubEmptyRoleBinding)
        } else {
          None
        },
        if (invalidLpRoleBindingWhenEnableSharableLInk) {
          Some(ValidationError.FundSubSharableLinkInvalidLpBinding)
        } else {
          None
        }
      ).flatten
    }

    def runValidate(): Callback = {
      for {
        state <- scope.state
        validationErrors = validate(state)
        _ <- scope.modState(_.copy(errors = validationErrors))
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps { props =>
      State(
        fundSubAppConfig =
          props.existingAppConfig.map(_.fundSubAppConfig).getOrElse(FundSubAppConfigData(Seq.empty, false)),
        errors = Seq.empty
      )
    }
    .renderBackend[Backend]
    .componentDidMount(_.backend.runValidate())
    .build

}
