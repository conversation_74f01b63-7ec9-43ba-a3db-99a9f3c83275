// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.environment.policy

import anduin.id.account.EnterpriseLoginConfigId
import anduin.id.environment.EnvironmentId
import anduin.portal.account.enterprise.EnterpriseLoginConfigItem
import anduin.protobuf.environment.policy.{
  EnvironmentPolicyReauthenticationFallbackConfig,
  EnvironmentPolicyReauthenticationGlobalConfig,
  EnvironmentReauthenticationPolicy
}
import anduin.protobuf.environment.EnvironmentPolicyReauthenticationFallbackPolicy
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

final case class EnvironmentReauthenticationPolicyConfig(
  environmentId: EnvironmentId,
  oldPolicy: Option[EnvironmentReauthenticationPolicy],
  onSave: Callback,
  saveButtonText: String
) {
  def apply(): VdomElement = EnvironmentReauthenticationPolicyConfig.component(this)
}

object EnvironmentReauthenticationPolicyConfig {

  private type Props = EnvironmentReauthenticationPolicyConfig

  private enum Step {
    case ConfigSSO
    case GlobalEnforcementConfig
    case EmailDomainConfig
    case ApplicationConfig
    case FallbackPolicyConfig
    case MainConfigSave
    case EmailDomainConfigSave
    case AppConfigSave
  }

  private final case class State(
    step: Step,
    selectedSSOConfigs: Option[Map[EnterpriseLoginConfigId, EnterpriseLoginConfigItem]],
    globalEnforcement: Option[EnvironmentPolicyReauthenticationGlobalConfig],
    emailDomainBindings: Option[Map[String, EnvironmentReauthenticationPolicyEmailDomainStep.SelectedSSOConfig]],
    selectedPolicy: Option[EnvironmentPolicyReauthenticationFallbackPolicy],
    fallbackConfig: Option[EnvironmentPolicyReauthenticationFallbackConfig],
    appConfigData: Option[EnvironmentReauthenticationApplicationConfigStep.AppConfigData]
  )

  private class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      state.step match {
        case Step.ConfigSSO =>
          EnvironmentReauthenticationPolicySSOStep(
            oldConfig = props.oldPolicy,
            existingConfig = state.selectedSSOConfigs.map(_.keySet),
            onNext = selectedSSOConfigs =>
              scope.modState(_.copy(selectedSSOConfigs = Some(selectedSSOConfigs), step = Step.GlobalEnforcementConfig))
          )()
        case Step.GlobalEnforcementConfig =>
          EnvironmentReauthenticationGlobalEnforcementStep(
            oldConfig = props.oldPolicy,
            existingGlobalConfig = state.globalEnforcement,
            selectedSSOConfigs = state.selectedSSOConfigs.getOrElse(Map.empty),
            onBack = Some(scope.modState(_.copy(globalEnforcement = None, step = Step.ConfigSSO))),
            onNext = data =>
              scope.modState(
                _.copy(
                  globalEnforcement = Some(data.globalConfig),
                  step = Step.EmailDomainConfig
                )
              ),
            nextButtonTitle = "Next"
          )()
        case Step.EmailDomainConfig =>
          EnvironmentReauthenticationPolicyEmailDomainStep(
            oldPolicy = props.oldPolicy,
            existingEmailDomainBinding = state.emailDomainBindings,
            selectedSSOConfigs = state.selectedSSOConfigs.getOrElse(Map.empty),
            onBack = Some(scope.modState(_.copy(emailDomainBindings = None, step = Step.GlobalEnforcementConfig))),
            onNext = data => scope.modState(_.copy(emailDomainBindings = Some(data), step = Step.ApplicationConfig)),
            warnBeforeSave = false,
            nextButtonTitle = "Next"
          )()
        case Step.ApplicationConfig =>
          EnvironmentReauthenticationApplicationConfigStep(
            oldPolicy = props.oldPolicy,
            selectedSSOConfigs = state.selectedSSOConfigs.getOrElse(Map.empty),
            existingAppConfig = state.appConfigData,
            onBack = Some(scope.modState(_.copy(appConfigData = None, step = Step.EmailDomainConfig))),
            onNext = data => scope.modState(_.copy(appConfigData = Some(data), step = Step.FallbackPolicyConfig)),
            nextButtonText = "Next"
          )()
        case Step.FallbackPolicyConfig =>
          EnvironmentReauthenticationFallbackPolicyStep(
            oldConfig = props.oldPolicy,
            existingSelectedPolicy = state.selectedPolicy,
            existingFallbackConfig = state.fallbackConfig,
            selectedSSOConfigs = state.selectedSSOConfigs.getOrElse(Map.empty),
            onBack =
              Some(scope.modState(_.copy(selectedPolicy = None, fallbackConfig = None, step = Step.ApplicationConfig))),
            onNext = data =>
              scope.modState(
                _.copy(
                  selectedPolicy = Some(data.selectedPolicy),
                  fallbackConfig = Some(data.fallbackConfig),
                  step = Step.MainConfigSave
                )
              ),
            nextButtonTitle = props.saveButtonText
          )()
        case Step.MainConfigSave =>
          EnvironmentReauthenticationPolicySaveMainStep(
            environmentId = props.environmentId,
            oldConfig = props.oldPolicy,
            selectedSSOConfigs = state.selectedSSOConfigs,
            globalEnforcement = state.globalEnforcement,
            selectedPolicy = state.selectedPolicy,
            fallbackConfig = state.fallbackConfig,
            onSuccess = scope.modState(_.copy(step = Step.EmailDomainConfigSave))
          )()
        case Step.EmailDomainConfigSave =>
          EnvironmentReauthenticationPolicySaveEmailDomainStep(
            environmentId = props.environmentId,
            emailDomainBinding = state.emailDomainBindings,
            onSuccess = scope.modState(_.copy(step = Step.AppConfigSave))
          )()
        case Step.AppConfigSave =>
          EnvironmentReauthenticationPolicySaveAppStep(
            environmentId = props.environmentId,
            appConfigData = state.appConfigData,
            onSuccess = props.onSave
          )()
      }
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(
      State(
        step = Step.ConfigSSO,
        selectedSSOConfigs = None,
        globalEnforcement = None,
        emailDomainBindings = None,
        selectedPolicy = None,
        fallbackConfig = None,
        appConfigData = None
      )
    )
    .renderBackend[Backend]
    .build

}
