// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.environment.policy

import anduin.environment.EnvironmentPolicyCommonProtocols.PolicyBinding
import anduin.id.account.EnterpriseLoginConfigId
import anduin.portal.account.enterprise.EnterpriseLoginConfigItem
import anduin.portal.environment.EnvironmentPolicyAdminProtocols.GetEnvironmentGlobalEmailDomainBindingsParams
import anduin.protobuf.environment.policy.EnvironmentReauthenticationPolicy
import com.anduin.stargazer.client.utils.ZIOUtils
import design.anduin.components.button.Button
import design.anduin.components.dropdown.Dropdown
import design.anduin.components.icon.Icon
import design.anduin.components.modal.{Modal, ModalBody, ModalFooter}
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.components.table.Table
import design.anduin.components.textbox.TextBox
import design.anduin.components.toast.Toast
import design.anduin.components.well.Well
import design.anduin.components.well.react.WellR
import design.anduin.components.wrapper.laminar.WrapperL
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

final case class EnvironmentReauthenticationPolicyEmailDomainStep(
  oldPolicy: Option[EnvironmentReauthenticationPolicy],
  existingEmailDomainBinding: Option[Map[String, EnvironmentReauthenticationPolicyEmailDomainStep.SelectedSSOConfig]],
  selectedSSOConfigs: Map[EnterpriseLoginConfigId, EnterpriseLoginConfigItem],
  onBack: Option[Callback],
  onNext: Map[String, EnvironmentReauthenticationPolicyEmailDomainStep.SelectedSSOConfig] => Callback,
  warnBeforeSave: Boolean,
  nextButtonTitle: String
) {
  def apply(): VdomElement = EnvironmentReauthenticationPolicyEmailDomainStep.component(this)
}

object EnvironmentReauthenticationPolicyEmailDomainStep {

  private type Props = EnvironmentReauthenticationPolicyEmailDomainStep

  sealed trait SelectedSSOConfig derives CanEqual

  object SelectedSSOConfig {

    final case class SomeSSOConfig(enterpriseLoginConfigId: EnterpriseLoginConfigId) extends SelectedSSOConfig

    object AnduinAuthentication extends SelectedSSOConfig

  }

  private final case class EmailDomainItem(
    domain: String,
    ssoConfig: Option[SelectedSSOConfig]
  )

  private final case class State(
    bindings: Option[Seq[EmailDomainItem]]
  )

  def getSelectedSSOConfig(policy: PolicyBinding): Option[SelectedSSOConfig] = {
    policy match {
      case PolicyBinding.FallbackPolicy       => None
      case PolicyBinding.AnduinAuthentication => Some(SelectedSSOConfig.AnduinAuthentication)
      case PolicyBinding.SomeSSOConfig(enterpriseLoginConfigId) =>
        Some(SelectedSSOConfig.SomeSSOConfig(enterpriseLoginConfigId))
    }
  }

  private class Backend(scope: BackendScope[Props, State]) {

    private val EmailDomainItemTable = (new Table[(EmailDomainItem, Int)])()
    private val SSOConfigDropdown = (new Dropdown[SelectedSSOConfig])()

    def render(props: Props, state: State): VdomElement = {
      state.bindings.fold(BlockIndicatorR()()) { bindings =>
        <.div(
          ModalBody()(
            <.div(
              tw.fontSemiBold.text17.leading28,
              "Email domain enforcement"
            ),
            <.div(
              tw.textGray7,
              "Optional: You can enforce SSO based on email domains. This setting takes the highest priority and overrides user role configurations if the user's domain is listed."
            ),
            <.div(
              tw.mt16,
              addDomainButton()
            ),
            <.div(
              tw.mt24,
              if (bindings.isEmpty) {
                renderEmpty
              } else {
                renderNonEmpty(props, bindings)
              }
            )
          ),
          ModalFooter()(
            <.div(
              tw.flex.justifyBetween,
              <.div(
                props.onBack.fold(EmptyVdom) { onBack =>
                  Button(
                    onClick = onBack
                  )("Back")
                }
              ),
              <.div(
                if (props.warnBeforeSave) {
                  Modal(
                    renderTarget = open =>
                      Button(
                        style = Button.Style.Full(color = Button.Color.Primary),
                        isDisabled = shouldDisableNextButton(props, bindings),
                        onClick = open
                      )(props.nextButtonTitle),
                    renderContent = _ => renderConfirmModal(props, bindings),
                    title = "Confirm configuration update",
                    size = Modal.Size(Modal.Width.Px480)
                  )()
                } else {
                  Button(
                    style = Button.Style.Full(color = Button.Color.Primary),
                    isDisabled = shouldDisableNextButton(props, bindings),
                    onClick = props.onNext(bindingItemsToMap(bindings))
                  )(props.nextButtonTitle)
                }
              )
            )
          )
        )
      }
    }

    private def bindingItemsToMap(bindings: Seq[EmailDomainItem]): Map[String, SelectedSSOConfig] = {
      bindings
        .flatMap(item =>
          if (item.domain.isEmpty) {
            None
          } else {
            item.ssoConfig.map(item.domain -> _)
          }
        )
        .toMap
    }

    private def renderConfirmModal(props: Props, bindings: Seq[EmailDomainItem]): VdomElement = {
      <.div(
        ModalBody()(
          <.div(
            "SSO configurations for this environment will be updated immediately. This may require some users to re-authenticate right away."
          )
        ),
        ModalFooter()(
          <.div(
            tw.flex.justifyEnd,
            Button(
              style = Button.Style.Full(color = Button.Color.Primary),
              onClick = props.onNext(bindingItemsToMap(bindings))
            )("Confirm")
          )
        )
      )
    }

    private def addDomainButton(): VdomElement = {
      Button(
        style = Button.Style.Full(
          color = Button.Color.Primary,
          icon = Some(Icon.Glyph.Plus)
        ),
        onClick =
          scope.modState(oldState => oldState.copy(bindings = oldState.bindings.map(_ :+ EmailDomainItem("", None))))
      )("Add domain")
    }

    private def renderEmpty: VdomElement = {
      <.div(
        WellR(style = Well.Style.Gray(icon = None))(WrapperL("No email domains configured."))
      )
    }

    private def renderNonEmpty(
      props: Props,
      bindings: Seq[EmailDomainItem]
    ): VdomElement = {
      EmailDomainItemTable(
        rows = bindings.zipWithIndex,
        columns = Seq(
          Table.Column(
            head = "Email domain",
            render = item =>
              Table.Cell(
                TextBox(
                  value = item._1.domain,
                  onChange = value =>
                    scope.modState(oldState =>
                      oldState.copy(bindings = oldState.bindings.map(_.updated(item._2, item._1.copy(domain = value))))
                    )
                )()
              ),
            width = "40%"
          ),
          Table.Column(
            head = "SSO configuration",
            render = item =>
              Table.Cell(
                SSOConfigDropdown(
                  value = item._1.ssoConfig.flatMap {
                    case SelectedSSOConfig.SomeSSOConfig(enterpriseLoginConfigId) =>
                      if (props.selectedSSOConfigs.contains(enterpriseLoginConfigId)) {
                        Some(SelectedSSOConfig.SomeSSOConfig(enterpriseLoginConfigId))
                      } else {
                        None
                      }
                    case SelectedSSOConfig.AnduinAuthentication => Some(SelectedSSOConfig.AnduinAuthentication)
                  },
                  valueToString = value => dropdownItemValueToString(props, value),
                  onChange = value =>
                    scope.modState(oldState =>
                      oldState.copy(bindings =
                        oldState.bindings.map(_.updated(item._2, item._1.copy(ssoConfig = Some(value))))
                      )
                    ),
                  items = props.selectedSSOConfigs
                    .map((enterpriseLoginConfigId, _) =>
                      Dropdown.Item[SelectedSSOConfig](SelectedSSOConfig.SomeSSOConfig(enterpriseLoginConfigId))
                    )
                    .toSeq ++ Seq(
                    Dropdown.Item(SelectedSSOConfig.AnduinAuthentication)
                  ),
                  button = Dropdown.Button(
                    placeholder = <.span(tw.textGray5, "Select SSO configuration"),
                    appearance = Dropdown.Appearance.Full(isFullWidth = true)
                  )
                )()
              ),
            width = "40%"
          ),
          Table.Column(
            "",
            item =>
              Table.Cell(
                Button(
                  style = Button.Style.Minimal(icon = Some(Icon.Glyph.Cross)),
                  onClick = scope.modState(oldState =>
                    oldState.copy(bindings = oldState.bindings.map(_.patch(item._2, Seq.empty, 1)))
                  )
                )()
              )
          )
        ),
        getKey = _._2.toString
      )()
    }

    private def dropdownItemValueToString(props: Props, selectedSSOConfigs: SelectedSSOConfig): String = {
      selectedSSOConfigs match {
        case SelectedSSOConfig.SomeSSOConfig(enterpriseLoginConfigId) =>
          props.selectedSSOConfigs.get(enterpriseLoginConfigId).map(_.name).getOrElse("")
        case SelectedSSOConfig.AnduinAuthentication => "Anduin credentials"
      }
    }

    private def shouldDisableNextButton(props: Props, bindings: Seq[EmailDomainItem]): Boolean = {
      val shouldEnable = bindings.forall { item =>
        item.domain.nonEmpty && item.ssoConfig.fold(false) {
          case SelectedSSOConfig.SomeSSOConfig(enterpriseLoginConfigId) =>
            props.selectedSSOConfigs.contains(enterpriseLoginConfigId)
          case SelectedSSOConfig.AnduinAuthentication => true
        }
      }
      !shouldEnable
    }

    def didMount(props: Props): Callback = {
      props.existingEmailDomainBinding.fold(
        props.oldPolicy.fold(scope.modState(_.copy(bindings = Some(Seq.empty)))) { oldPolicy =>
          ZIOUtils.toReactCallbackWithErrorHandler(
            EnvironmentPolicyAdminClient
              .getEnvironmentEmailDomainBinding(GetEnvironmentGlobalEmailDomainBindingsParams(oldPolicy.environmentId))
              .map(
                _.fold(
                  error => Toast.errorCallback(error.message),
                  response =>
                    scope.modState(_.copy(bindings = Some(response.bindings.toSeq.flatMap { case (domain, policy) =>
                      getSelectedSSOConfig(policy).fold(None) { ssoConfig =>
                        Some(EmailDomainItem(domain, Some(ssoConfig)))
                      }
                    })))
                )
              )
          )
        }
      ) { existingEmailDomainBinding =>
        scope.modState(_.copy(bindings = Some(existingEmailDomainBinding.map { case (domain, ssoConfig) =>
          EmailDomainItem(domain, Some(ssoConfig))
        }.toSeq)))
      }
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState {
      State(
        bindings = None
      )
    }
    .renderBackend[Backend]
    .componentDidMount(scope => scope.backend.didMount(scope.props))
    .build

}
