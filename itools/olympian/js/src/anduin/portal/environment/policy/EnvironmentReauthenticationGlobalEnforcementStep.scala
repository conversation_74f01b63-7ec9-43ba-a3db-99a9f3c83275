// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.environment.policy

import anduin.id.account.EnterpriseLoginConfigId
import anduin.portal.account.enterprise.EnterpriseLoginConfigItem
import anduin.protobuf.environment.policy.{
  EnvironmentPolicyReauthenticationGlobalConfig,
  EnvironmentReauthenticationPolicy
}
import design.anduin.components.button.Button
import design.anduin.components.dropdown.Dropdown
import design.anduin.components.modal.{ModalBody, ModalFooter}
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

final case class EnvironmentReauthenticationGlobalEnforcementStep(
  oldConfig: Option[EnvironmentReauthenticationPolicy],
  existingGlobalConfig: Option[EnvironmentPolicyReauthenticationGlobalConfig],
  selectedSSOConfigs: Map[EnterpriseLoginConfigId, EnterpriseLoginConfigItem],
  onBack: Option[Callback],
  onNext: EnvironmentReauthenticationGlobalEnforcementStep.GlobalEnforcementData => Callback,
  nextButtonTitle: String
) {
  def apply(): VdomElement = EnvironmentReauthenticationGlobalEnforcementStep.component(this)
}

object EnvironmentReauthenticationGlobalEnforcementStep {

  private type Props = EnvironmentReauthenticationGlobalEnforcementStep

  private final case class State(
    globalConfig: EnvironmentPolicyReauthenticationGlobalConfig
  )

  final case class GlobalEnforcementData(
    globalConfig: EnvironmentPolicyReauthenticationGlobalConfig
  )

  sealed trait EnforcedProviderValue derives CanEqual

  object EnforcedProviderValue {

    case object NoEnforce extends EnforcedProviderValue

    final case class EnforceProvider(configId: EnterpriseLoginConfigId) extends EnforcedProviderValue
  }

  private class Backend(scope: BackendScope[Props, State]) {

    private val SSOConfigDropdown = (new Dropdown[EnforcedProviderValue])()

    def render(props: Props, state: State): VdomElement = {
      <.div(
        ModalBody()(
          <.div(
            tw.text17.leading28.fontSemiBold,
            "Global enforcement"
          ),
          <.div(
            tw.text13.leading20.textGray7,
            "Global configuration for entire environment"
          ),
          <.div(
            tw.mt12,
            renderEnforceProvider(props, state)
          )
        ),
        ModalFooter()(
          <.div(
            tw.flex.justifyBetween,
            <.div(
              props.onBack.fold(EmptyVdom) { onBack =>
                Button(
                  style = Button.Style.Full(),
                  onClick = onBack
                )("Back")
              }
            ),
            <.div(
              Button(
                style = Button.Style.Full(color = Button.Color.Primary),
                onClick = props.onNext(GlobalEnforcementData(state.globalConfig))
              )(props.nextButtonTitle)
            )
          )
        )
      )
    }

    private def renderEnforceProvider(props: Props, state: State): VdomElement = {
      <.div(
        tw.flex.itemsCenter,
        <.div(
          tw.wPc30,
          "Enforce provider for unauthenticated users"
        ),
        <.div(
          tw.wPc70,
          SSOConfigDropdown(
            value = Some(
              state.globalConfig.enforcedProvider
                .fold(EnforcedProviderValue.NoEnforce)(EnforcedProviderValue.EnforceProvider(_))
            ),
            valueToString = {
              case EnforcedProviderValue.NoEnforce => "No enforcement"
              case EnforcedProviderValue.EnforceProvider(configId) =>
                props.selectedSSOConfigs.get(configId).map(_.name).getOrElse("")
            },
            onChange = value =>
              scope.modState(oldState =>
                oldState.copy(globalConfig = oldState.globalConfig.copy(enforcedProvider = value match {
                  case EnforcedProviderValue.NoEnforce                 => None
                  case EnforcedProviderValue.EnforceProvider(configId) => Some(configId)
                }))
              ),
            items =
              List(Dropdown.Item[EnforcedProviderValue](EnforcedProviderValue.NoEnforce)) :++ props.selectedSSOConfigs
                .map((enterpriseLoginConfigId, _) =>
                  Dropdown.Item[EnforcedProviderValue](EnforcedProviderValue.EnforceProvider(enterpriseLoginConfigId))
                )
                .toList
          )()
        )
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps { props =>
      val reauthenticationConfig = props.oldConfig
      State(
        globalConfig = props.existingGlobalConfig
          .orElse(
            reauthenticationConfig
              .flatMap(_.globalConfig)
          )
          .getOrElse(EnvironmentPolicyReauthenticationGlobalConfig())
      )
    }
    .renderBackend[Backend]
    .build

}
