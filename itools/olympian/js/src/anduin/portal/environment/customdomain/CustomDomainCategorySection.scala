// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.environment.customdomain

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.radio.Radio
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import anduin.protobuf.environment.EnvironmentCustomDomainCategory

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[customdomain] case class CustomDomainCategorySection(
  selectedCategory: Option[EnvironmentCustomDomainCategory],
  onChangeCategory: EnvironmentCustomDomainCategory => Callback
) {
  def apply(): VdomElement = CustomDomainCategorySection.component(this)
}

private[customdomain] object CustomDomainCategorySection {
  private type Props = CustomDomainCategorySection

  private def render(props: Props) = {
    React.Fragment(
      <.div(
        tw.fontSemiBold,
        "Category"
      ),
      <.div(
        tw.flex.itemsStart.py12.px8.spaceX8.mt8.bgGray2.rounded4,
        <.div(tw.textGray7, IconR(name = Icon.Glyph.Info)()),
        <.div(
          tw.textGray8,
          "If you select a new primary domain, your current primary domain will automatically be switched to a secondary domain",
          <.span(
            tw.ml4,
            Button(
              style = Button.Style.Text(),
              tpe = Button.Tpe.Link(
                href =
                  "https://www.notion.so/anduin/Draft-Help-Doc-Custom-Domain-for-Advanced-White-labeling-10d8b45e703f495c893b8f5f850e4bb2",
                target = Button.Target.Blank
              )
            )("Learn more")
          )
        )
      ),
      <.div(
        tw.mt8.flex.spaceX12,
        Seq(
          EnvironmentCustomDomainCategory.Primary,
          EnvironmentCustomDomainCategory.Secondary,
          EnvironmentCustomDomainCategory.Tertiary
        ).toVdomArray(
          using { category =>
            <.div(
              ^.key := category.name,
              Radio(
                isChecked = props.selectedCategory.contains(category),
                onChange = props.onChangeCategory(category)
              )(category.name)
            )
          }
        )
      )
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
