// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.environment.customdomain

import java.time.{Instant, LocalDateTime}
import design.anduin.components.button.Button
import design.anduin.components.clipboard.react.CopyToClipboardR
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.modal.Modal
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.components.table.Table
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.well.Well
import design.anduin.components.well.react.WellR
import design.anduin.components.wrapper.TargetWrapper
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import anduin.component.date.DateTime
import anduin.environment.EnvironmentConstants
import anduin.id.offering.{GlobalOfferingId, OfferingId}
import anduin.portal.environment.{EnvironmentAdminClient, EnvironmentAdminProtocols}
import anduin.protobuf.environment.{EnvironmentCustomDomainType, EnvironmentData}
import anduin.utils.DateTimeUtils
import com.raquo.laminar.api.L.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import com.anduin.stargazer.client.utils.ZIOUtils

private[environment] final case class EnvironmentDomainManager(environmentData: EnvironmentData) {
  def apply(): VdomElement = EnvironmentDomainManager.component(this)
}

private[environment] object EnvironmentDomainManager {

  type Props = EnvironmentDomainManager

  private final case class State(
    domains: Option[Seq[EnvironmentAdminProtocols.EnvironmentDomainResponse]]
  )

  private class Backend(scope: BackendScope[Props, State]) {

    private val DomainTable = (new Table[EnvironmentAdminProtocols.EnvironmentDomainResponse])()

    private def renderCard(icon: Icon.Name, title: String, content: VdomElement): VdomElement = {
      <.div(
        <.div(
          tw.text17.leading24.fontBold.flex.itemsCenter,
          IconR(icon)(),
          <.div(
            tw.ml8,
            title
          )
        ),
        <.div(
          tw.mt20,
          <.div(
            tw.p20.borderAll.borderGray3.rounded8,
            content
          )
        )
      )
    }

    private def actionCell(
      props: Props,
      row: EnvironmentAdminProtocols.EnvironmentDomainResponse,
      allowDeletePrimary: Boolean
    ): VdomElement = {
      <.div(
        tw.flex.itemsCenter,
        TagMod.when(!row.environmentDomain.category.isPrimary) {
          <.div(
            Modal(
              title = "Edit domain category",
              size = Modal.Size(Modal.Width.Px600),
              renderTarget = open =>
                TooltipR(
                  renderTarget = Button(
                    style = Button.Style.Minimal(icon = Some(Icon.Glyph.Edit)),
                    onClick = open
                  )(),
                  renderContent = _("Edit category")
                )(),
              renderContent = close =>
                EditDomainCategoryModal(
                  domainInfo = row,
                  onEdit = refetch(props),
                  close
                )()
            )()
          )
        },
        TagMod.when(allowDeletePrimary || !row.environmentDomain.category.isPrimary) {
          <.div(
            if (!row.environmentDomain.category.isPrimary) tw.ml4 else TagMod.empty,
            Modal(
              title = "Delete domain",
              size = Modal.Size(Modal.Width.Px480),
              renderTarget = open =>
                TooltipR(
                  renderTarget = Button(
                    style = Button.Style.Minimal(icon = Some(Icon.Glyph.Trash)),
                    onClick = open
                  )(),
                  renderContent = _("Delete")
                )(),
              renderContent = close => DeleteDomainModal(domain = row, close = close >> refetch(props))()
            )()
          )
        }
      )
    }

    private def renderDomainsTable(
      props: Props,
      domains: Seq[EnvironmentAdminProtocols.EnvironmentDomainResponse],
      allowDeletePrimary: Boolean = false,
      showCategory: Boolean = true,
      showOffering: Boolean = false,
      showAction: Boolean = true,
      allowEmpty: Boolean = false
    ): VdomElement = {
      val categoryColumn = Table.Column[EnvironmentAdminProtocols.EnvironmentDomainResponse](
        "Category",
        row => Table.Cell(row.environmentDomain.category.name)
      )
      val offeringColumn = Table.Column[EnvironmentAdminProtocols.EnvironmentDomainResponse](
        "App",
        row =>
          Table.Cell(
            row.environmentDomain.offeringId.flatMap(_.toOfferingId).map(_.prettyName).getOrElse("Platform domain")
          )
      )
      val actionColumn = Table.Column[EnvironmentAdminProtocols.EnvironmentDomainResponse](
        "",
        row =>
          Table.Cell(
            actionCell(
              props,
              row,
              allowDeletePrimary && (allowEmpty || domains.size <= 1)
            )
          )
      )
      val extraColumns: Seq[Table.Column[EnvironmentAdminProtocols.EnvironmentDomainResponse]] =
        Seq.empty ++ {
          if (showCategory) {
            Seq(categoryColumn)
          } else {
            Seq.empty
          }
        } ++ {
          if (showOffering) {
            Seq(offeringColumn)
          } else {
            Seq.empty
          }
        } ++ {
          if (showAction) {
            Seq(actionColumn)
          } else {
            Seq.empty
          }
        }
      DomainTable(
        rows = domains,
        getKey = _.environmentDomain.customDomainId.idString,
        sortColumn = Some(1),
        sortIsAsc = false,
        columns = Seq[Table.Column[EnvironmentAdminProtocols.EnvironmentDomainResponse]](
          Table.Column(
            "Domain",
            row =>
              Table.Cell(
                <.div(
                  tw.flex,
                  CopyToClipboardR(
                    content = row.customDomain.domain,
                    renderTarget = content => {
                      div(
                        tw.cursorPointer,
                        content
                      )
                    },
                    targetWrapper = TargetWrapper.Block
                  )()
                )
              ),
            sortBy = Table.ColumnOrderingCaseInsensitive(_.customDomain.domain)
          ),
          Table.Column(
            head = "Created at",
            render = row =>
              Table.Cell(
                DateTime(
                  value = LocalDateTime
                    .ofInstant(Instant.ofEpochMilli(row.customDomain.createdAt), DateTimeUtils.getTimezone(None)),
                  date = DateTime.DateFull
                )()
              ),
            sortBy = Table.ColumnOrdering(_.customDomain.createdAt)
          ),
          Table.Column(
            "Created by",
            row =>
              Table.Cell(
                if (row.creator.fullNameString.isEmpty) {
                  row.creator.emailAddressStr
                } else {
                  row.creator.fullNameString
                }
              )
          )
        ) ++ extraColumns
      )()
    }

    private def renderFallbackDomain(
      props: Props,
      domains: Seq[EnvironmentAdminProtocols.EnvironmentDomainResponse]
    ): VdomElement = {
      renderDomainsTable(
        props,
        domains,
        showCategory = false,
        showAction = false
      )
    }

    private def addPlatformDomainButton(props: Props, forcePrimary: Boolean): VdomElement = {
      Modal(
        title = "Create new platform domain",
        size = Modal.Size(Modal.Width.Px600),
        renderTarget = open => {
          Button(
            style = Button.Style.Full(color = Button.Color.Primary),
            onClick = open
          )("Create new platform domain")
        },
        renderContent = close =>
          AddPlatformDomainModal(
            environmentId = props.environmentData.id,
            forcePrimary = forcePrimary,
            onClose = close >> refetch(props)
          )()
      )()
    }

    private def renderPlatformDomains(
      props: Props,
      domains: Seq[EnvironmentAdminProtocols.EnvironmentDomainResponse]
    ): VdomElement = {
      if (domains.isEmpty) {
        <.div(
          <.div(
            tw.flex.justifyCenter.itemsCenter.py32,
            addPlatformDomainButton(props, forcePrimary = true)
          )
        )
      } else {
        <.div(
          <.div(
            addPlatformDomainButton(props, forcePrimary = false)
          ),
          <.div(
            tw.mt20,
            renderDomainsTable(props, domains)
          )
        )
      }
    }

    private def addOfferingDomainButton(props: Props): VdomElement = {
      Modal(
        title = "Create new offering domain",
        size = Modal.Size(Modal.Width.Px600),
        renderTarget = open => {
          Button(
            style = Button.Style.Full(color = Button.Color.Primary),
            onClick = open
          )("Create new offering domain")
        },
        renderContent = close =>
          AddOfferingDomainModal(
            environmentId = props.environmentData.id,
            close = close >> refetch(props)
          )()
      )()
    }

    private def renderOfferingDomain(
      props: Props,
      offeringId: OfferingId,
      domains: Seq[EnvironmentAdminProtocols.EnvironmentDomainResponse]
    ): VdomElement = {
      <.div(
        tw.mt32,
        <.div(
          tw.fontBold.text15.leading20,
          offeringId.prettyName
        ),
        <.div(
          tw.mt20,
          if (domains.isEmpty) {
            "No domain found"
          } else {
            renderDomainsTable(
              props,
              domains,
              allowDeletePrimary = true
            )
          }
        )
      )
    }

    private def renderOfferingDomains(
      props: Props,
      allOfferingDomains: Seq[EnvironmentAdminProtocols.EnvironmentDomainResponse]
    ): VdomElement = {
      if (allOfferingDomains.isEmpty) {
        <.div(
          WellR(style = Well.Style.Primary())(
            "No offering domain found!"
          ),
          <.div(
            tw.flex.justifyCenter.itemsCenter.py32,
            addOfferingDomainButton(props)
          )
        )
      } else {
        <.div(
          <.div(
            addOfferingDomainButton(props)
          ),
          EnvironmentConstants.availableOfferings.toTagMod(
            using { offeringId =>
              renderOfferingDomain(
                props,
                offeringId,
                allOfferingDomains.filter(
                  _.environmentDomain.offeringId.contains(GlobalOfferingId.fromOfferingId(offeringId))
                )
              )
            }
          )
        )
      }
    }

    private def renderMultiRegionDomains(
      props: Props,
      domains: Seq[EnvironmentAdminProtocols.EnvironmentDomainResponse]
    ): VdomElement = {
      if (domains.isEmpty) {
        <.div(
          <.div(
            WellR(style = Well.Style.Warning())(
              "No multi region domain found!"
            )
          )
        )
      } else {
        <.div(
          tw.mt20,
          renderDomainsTable(
            props,
            domains,
            showCategory = true,
            showAction = false,
            showOffering = true
          )
        )
      }
    }

    def render(props: Props, state: State): VdomElement = {
      state.domains.fold(BlockIndicatorR()()) { domains =>
        <.div(
          if (props.environmentData.environmentType.isShadow) {
            <.div(
              renderCard(
                Icon.Glyph.Dashboard,
                "Multi region domains",
                renderMultiRegionDomains(
                  props,
                  domains.filter(_.environmentDomain.domainType == EnvironmentCustomDomainType.MultiRegion)
                )
              )
            )
          } else {
            <.div(
              <.div(
                tw.hidden,
                renderCard(
                  Icon.Glyph.Layer,
                  "Fallback domains",
                  renderFallbackDomain(
                    props,
                    domains.filter(_.environmentDomain.domainType == EnvironmentCustomDomainType.Fallback)
                  )
                )
              ),
              <.div(
                tw.mt32.hidden,
                renderCard(
                  Icon.Glyph.Home,
                  "Platform domains",
                  renderPlatformDomains(
                    props,
                    domains.filter(_.environmentDomain.domainType == EnvironmentCustomDomainType.Platform)
                  )
                )
              ),
              <.div(
                renderCard(
                  Icon.Glyph.Dashboard,
                  "Offering domains",
                  renderOfferingDomains(
                    props,
                    domains.filter(_.environmentDomain.domainType == EnvironmentCustomDomainType.Offering)
                  )
                )
              )
            )
          }
        )
      }
    }

    def refetch(props: Props): Callback = {
      ZIOUtils.toReactCallbackWithErrorHandler(
        EnvironmentAdminClient
          .getEnvironmentDomains(EnvironmentAdminProtocols.GetEnvironmentDomainsParams(props.environmentData.id))
          .map(
            _.fold(
              error => Toast.errorCallback(error.message),
              response => scope.modState(_.copy(domains = Some(response.domains)))
            )
          )
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(
      State(
        domains = None
      )
    )
    .renderBackend[Backend]
    .componentDidMount(scope => scope.backend.refetch(scope.props))
    .build

}
