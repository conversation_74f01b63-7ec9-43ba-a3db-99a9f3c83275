// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.environment.whitelabel

import design.anduin.components.switcher.react.SwitcherR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.protobuf.environment.EnvironmentData

object EnvironmentWhitelabelSwitcher {

  def apply(
    environmentData: EnvironmentData,
    title: String,
    value: Boolean,
    onChange: Boolean => Callback
  ): VdomElement = {
    <.div(
      tw.flex.itemsCenter.py8,
      <.div(
        tw.wPx480,
        title
      ),
      <.div(
        tw.ml16,
        SwitcherR(
          isChecked = value,
          onChange = onChange,
          isDisabled = environmentData.environmentType.isShadow
        )()
      )
    )
  }

}
