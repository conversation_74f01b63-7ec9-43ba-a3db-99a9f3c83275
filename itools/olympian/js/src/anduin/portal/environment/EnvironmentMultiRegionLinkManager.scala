// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.environment

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.modal.Modal
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.components.table.Table
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.id.environment.EnvironmentId
import anduin.portal.environment.EnvironmentAdminProtocols.{
  GetLinkedEnvironmentFromMainRegionParams,
  GetLinkedEnvironmentFromMainRegionResponse
}
import anduin.protobuf.environment.EnvironmentData
import com.anduin.stargazer.client.utils.ZIOUtils

final case class EnvironmentMultiRegionLinkManager(environmentId: EnvironmentId) {
  def apply(): VdomElement = EnvironmentMultiRegionLinkManager.component(this)
}

object EnvironmentMultiRegionLinkManager {

  type Props = EnvironmentMultiRegionLinkManager

  private final case class State(
    response: Option[GetLinkedEnvironmentFromMainRegionResponse]
  )

  private class Backend(scope: BackendScope[Props, State]) {

    private val LinkedTable = (new Table[EnvironmentData])()

    private def renderAddLink(props: Props): VdomElement = {
      <.div(
        tw.flex.itemsCenter.justifyCenter,
        Modal(
          title = "Link with main region",
          size = Modal.Size(Modal.Width.Px480),
          renderContent = close =>
            AddOrUpdateMultiRegionLinkModal(
              environmentId = props.environmentId,
              mainRegionEnvironmentId = None,
              onClose = close >> fetch(props)
            )(),
          renderTarget = open =>
            Button(
              style = Button.Style.Full(color = Button.Color.Primary),
              onClick = open
            )("Link with main region")
        )()
      )
    }

    private def renderActionCell(props: Props, environment: EnvironmentData): VdomElement = {
      <.div(
        tw.flex.itemsCenter,
        <.div(
          Modal(
            title = "Link with main region",
            size = Modal.Size(Modal.Width.Px480),
            renderContent = close =>
              AddOrUpdateMultiRegionLinkModal(
                environmentId = props.environmentId,
                mainRegionEnvironmentId = Some(environment.id),
                onClose = close >> fetch(props)
              )(),
            renderTarget = open =>
              TooltipR(
                renderTarget = Button(
                  style = Button.Style.Minimal(icon = Some(Icon.Glyph.Edit)),
                  onClick = open
                )(),
                renderContent = _("Update")
              )()
          )()
        ),
        <.div(
          tw.ml4,
          Modal(
            title = "Unlink main region environment",
            size = Modal.Size(Modal.Width.Px480),
            renderContent = close =>
              DeleteMultiRegionLinkModal(
                environmentId = props.environmentId,
                mainRegionName = environment.name,
                onClose = close >> fetch(props)
              )(),
            renderTarget = open =>
              TooltipR(
                renderTarget = Button(
                  style = Button.Style.Minimal(icon = Some(Icon.Glyph.Trash)),
                  onClick = open
                )(),
                renderContent = _("Remove")
              )()
          )()
        )
      )
    }

    private def renderTable(props: Props, environment: EnvironmentData): VdomElement = {
      LinkedTable(
        rows = Seq(environment),
        getKey = _.id.idString,
        columns = Seq(
          Table.Column(
            "ID",
            row => Table.Cell(row.id.idString)
          ),
          Table.Column(
            "Name",
            row => Table.Cell(row.name)
          ),
          Table.Column(
            "",
            row => Table.Cell(renderActionCell(props, environment))
          )
        )
      )()
    }

    def render(props: Props, state: State): VdomElement = {
      <.div(
        <.div(
          tw.text17.leading24.fontBold.flex.itemsCenter,
          IconR(Icon.Glyph.Link)(),
          <.div(
            tw.ml8,
            "Link with main region"
          )
        ),
        <.div(
          tw.mt20,
          <.div(
            tw.p20.borderAll.borderGray3.rounded8,
            state.response.fold(BlockIndicatorR()()) { response =>
              response.environment.fold(renderAddLink(props)) { environment =>
                renderTable(props, environment)
              }
            }
          )
        )
      )

    }

    def fetch(props: Props): Callback = {
      ZIOUtils.toReactCallbackWithErrorHandler(
        EnvironmentAdminClient
          .getLinkedEnvironmentFromMainRegion(GetLinkedEnvironmentFromMainRegionParams(props.environmentId))
          .map(
            _.fold(
              error => Toast.errorCallback(error.message),
              response => scope.modState(_.copy(response = Some(response)))
            )
          )
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State(None))
    .renderBackend[Backend]
    .componentDidMount(scope => scope.backend.fetch(scope.props))
    .build

}
