// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.environment

import design.anduin.components.modal.{<PERSON><PERSON><PERSON><PERSON>, ModalFooter}
import design.anduin.components.well.Well
import design.anduin.components.well.react.WellR

import anduin.id.environment.EnvironmentId
import design.anduin.components.button.Button
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import com.raquo.laminar.api.L.*
import design.anduin.components.toast.Toast
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.portal.environment.EnvironmentAdminProtocols.RemoveLinkedMainRegionEnvironmentParams
import com.anduin.stargazer.client.utils.ZIOUtils

final case class DeleteMultiRegionLinkModal(
  environmentId: EnvironmentId,
  mainRegionName: String,
  onClose: Callback
) {
  def apply(): VdomElement = DeleteMultiRegionLinkModal.component(this)
}

object DeleteMultiRegionLinkModal {

  type Props = DeleteMultiRegionLinkModal

  private final case class State(
    isBusy: Boolean
  )

  private class Backend(scope: BackendScope[Props, State]) {

    private def unlinkEnvironment(props: Props): Callback = {
      ZIOUtils.toReactCallbackWithErrorHandler(
        EnvironmentAdminClient
          .removeLinkedMainRegionEnvironment(RemoveLinkedMainRegionEnvironmentParams(props.environmentId))
          .map(
            _.fold(
              error => Toast.errorCallback(error.message) >> scope.modState(_.copy(isBusy = false)),
              _ => Toast.successCallback("Successfully remove linked environment") >> props.onClose
            )
          ),
        _ => Toast.errorCallback("Something wrong happened, please try again!") >> scope.modState(_.copy(isBusy = false))
      )
    }

    def render(props: Props, state: State): VdomElement = {
      React.Fragment(
        ModalBody()(
          WellR(style = Well.Style.Danger())(
            s"Do you want to unlink the environment ${props.mainRegionName}?"
          )
        ),
        ModalFooter()(
          <.div(
            tw.flex.justifyEnd,
            <.div(
              Button(
                onClick = props.onClose,
                isDisabled = state.isBusy
              )("Cancel")
            ),
            <.div(
              tw.ml4,
              Button(
                style = Button.Style.Full(
                  color = Button.Color.Danger,
                  isBusy = state.isBusy
                ),
                isDisabled = state.isBusy,
                onClick = scope.modState(_.copy(isBusy = true), unlinkEnvironment(props))
              )("Unlink")
            )
          )
        )
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State(isBusy = false))
    .renderBackend[Backend]
    .build

}
