// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.emailsendingmanager

import anduin.id.role.portal.PortalSectionId
import anduin.portal.{AdminPortalLayout, AdminPortalNavBar}
import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.vdom.html_<^.*
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import design.anduin.components.button.Button
import design.anduin.components.dropdown.Dropdown
import design.anduin.components.icon.Icon
import design.anduin.components.modal.Modal
import design.anduin.components.pagination.react.PaginationR
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.components.table.Table
import design.anduin.components.toast.Toast

import anduin.email.sending.EmailSendingManagerAdminProtocols.{
  EmailProviderData,
  ScanEmailProvidersParams,
  ScanEmailProvidersResponse
}
import anduin.email.sending.EmailSendingProviderStatusEnum
import com.anduin.stargazer.client.utils.ZIOUtils
import stargazer.model.routing.DynamicAuthPage.Portal.EmailSendingManager
import stargazer.model.routing.Page

final case class Provider(routerCtl: RouterCtl[Page]) {
  def apply(): VdomElement = Provider.component(this)
}

object Provider {

  private type Props = Provider

  private sealed trait DataState derives CanEqual

  private case object DataStateInitial extends DataState

  private case object DataStateLoading extends DataState

  private final case class DataStateLoaded(
    response: ScanEmailProvidersResponse
  ) extends DataState

  private sealed trait StatusFilter derives CanEqual

  private case object StatusFilterAll extends StatusFilter

  private final case class StatusFilterByStatus(status: EmailSendingProviderStatusEnum) extends StatusFilter

  private final case class State(
    status: Option[StatusFilter],
    page: Int,
    dataState: DataState
  )

  private class Backend(scope: BackendScope[Props, State]) {

    private val StatusDropdown = (new Dropdown[StatusFilter])()
    private val EmailProviderTable = (new Table[EmailProviderData])()

    private def doFilter(param: ScanEmailProvidersParams): Callback = {
      ZIOUtils.toReactCallbackWithErrorHandler(
        EmailSendingManagerAdminClient
          .scanEmailProviders(param)
          .map(
            _.fold(
              error => scope.modState(_.copy(dataState = DataStateInitial), Toast.errorCallback(error.message)),
              response => scope.modState(_.copy(dataState = DataStateLoaded(response)))
            )
          ),
        _ =>
          scope
            .modState(_.copy(dataState = DataStateInitial), Toast.errorCallback("Please check your network connection"))
      )
    }

    private def filter: Callback = {
      for {
        state <- scope.state
        paramOpt = for {
          status <- state.status
        } yield ScanEmailProvidersParams(
          status = status match {
            case StatusFilterAll              => None
            case StatusFilterByStatus(status) => Some(status)
          },
          page = state.page
        )
        _ <- paramOpt.fold(Callback.empty) { param =>
          scope.modState(_.copy(dataState = DataStateLoading), doFilter(param))
        }
      } yield ()
    }

    private def renderFilter(state: State): VdomElement = {
      <.div(
        tw.flex.itemsCenter,
        <.div(
          tw.ml8,
          StatusDropdown(
            value = state.status,
            valueToString = {
              case StatusFilterAll              => "All"
              case StatusFilterByStatus(status) => status.name
            },
            onChange = status => scope.modState(_.copy(status = Some(status))),
            items = Seq(
              Dropdown.Item(StatusFilterAll),
              Dropdown.Item(StatusFilterByStatus(EmailSendingProviderStatusEnum.PROVIDER_TEMPORARY_FAILED)),
              Dropdown.Item(StatusFilterByStatus(EmailSendingProviderStatusEnum.PROVIDER_PERMANENT_FAILED))
            )
          )()
        ),
        <.div(
          tw.ml8,
          Button(
            style = Button.Style.Full(
              color = Button.Color.Gray0,
              isBusy = state.dataState == DataStateLoading
            ),
            isDisabled = state.status.isEmpty,
            onClick = scope.modState(_.copy(page = 1), filter)
          )("Filter")
        )
      )
    }

    private def renderOperationsCell(state: State, emailProvider: EmailProviderData): VdomNode = {
      <.div(
        tw.flex.itemsCenter,
        if (
          emailProvider.status == EmailSendingProviderStatusEnum.PROVIDER_TEMPORARY_FAILED || emailProvider.status == EmailSendingProviderStatusEnum.PROVIDER_PERMANENT_FAILED
        ) {
          <.div(
            tw.mr8,
            Modal(
              renderTarget = open =>
                Button(
                  style = Button.Style.Minimal(icon = Some(Icon.Glyph.Reopen)),
                  onClick = open
                )("Rescue"),
              renderContent = close => RescueProviderModal(emailProvider, close, close >> filter)(),
              size = Modal.Size(width = Modal.Width.Px600),
              title = "Rescue provider"
            )()
          )
        } else {
          EmptyVdom
        },
        if (
          emailProvider.status == EmailSendingProviderStatusEnum.PROVIDER_OK || emailProvider.status == EmailSendingProviderStatusEnum.PROVIDER_TEMPORARY_FAILED
        ) {
          <.div(
            tw.mr8,
            Modal(
              renderTarget = open =>
                Button(
                  style = Button.Style.Minimal(icon = Some(Icon.Glyph.Unlink)),
                  onClick = open
                )("Mark as failed"),
              renderContent = close => FailProviderModal(emailProvider, close, close >> filter)(),
              size = Modal.Size(width = Modal.Width.Px600),
              title = "Mark provider as failed"
            )()
          )
        } else {
          EmptyVdom
        }
      )
    }

    private def renderData(state: State): VdomNode = {
      <.div(
        tw.mt20,
        state.dataState match {
          case DataStateInitial => EmptyVdom
          case DataStateLoading => BlockIndicatorR()()
          case DataStateLoaded(response) =>
            if (response.providers.isEmpty) {
              <.div(
                tw.fontBold.text23.textCenter.mt20,
                "No providers found"
              )
            } else {
              <.div(
                <.div(
                  tw.fontBold.text15.mt20,
                  s"Providers found: ${response.total}"
                ),
                <.div(
                  tw.mt20,
                  EmailProviderTable(
                    rows = response.providers,
                    getKey = _.id.idString,
                    columns = Seq(
                      Table.Column(
                        head = "Id",
                        render = value => Table.Cell(value.id.idString)
                      ),
                      Table.Column(
                        head = "Name",
                        render = value => Table.Cell(value.name)
                      ),
                      Table.Column(
                        head = "Status",
                        render = value => Table.Cell(value.status.name)
                      ),
                      Table.Column(
                        head = "Failed count",
                        render = value => Table.Cell(value.failedCount.toString)
                      ),
                      Table.Column(
                        head = "",
                        render = value =>
                          Table.Cell(
                            renderOperationsCell(state, value)
                          )
                      )
                    )
                  )()
                ),
                if (response.totalPages > 1) {
                  <.div(
                    tw.mt12,
                    PaginationR(
                      totalPage = response.totalPages,
                      currentPage = state.page,
                      onJumpToPage = page => scope.modState(_.copy(page = page), filter),
                      showLastButton = true,
                      showFirstButton = true
                    )()
                  )
                } else {
                  EmptyVdom
                }
              )
            }
        }
      )
    }

    def render(props: Props, state: State): VdomElement = {
      AdminPortalLayout(
        requiredPortalSectionIdOpt = Some(PortalSectionId.Environment),
        renderSidebar = Some(Sidebar(props.routerCtl, EmailSendingManager.Provider))
      )(
        AdminPortalNavBar("Email sending manager | Provider")(),
        renderFilter(state),
        renderData(state)
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(
      State(
        status = Some(StatusFilterAll),
        page = 1,
        dataState = DataStateInitial
      )
    )
    .renderBackend[Backend]
    .build

}
