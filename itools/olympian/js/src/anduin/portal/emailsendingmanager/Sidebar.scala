// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.emailsendingmanager

import design.anduin.components.icon.Icon
import design.anduin.components.list.react.ListR
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import stargazer.model.routing.DynamicAuthPage.Portal.EmailSendingManager
import stargazer.model.routing.Page

private[emailsendingmanager] object Sidebar {

  private def renderItem(routerCtl: RouterCtl[Page], currentPage: EmailSendingManager, targetPage: EmailSendingManager)
    : ListR.Item = {
    ListR.Item(
      icon = Option(
        targetPage match {
          case EmailSendingManager.Home      => Icon.Glyph.Dashboard
          case EmailSendingManager.Urgent    => Icon.Glyph.LightBolt
          case EmailSendingManager.Scheduled => Icon.Glyph.Clock
          case EmailSendingManager.Space     => Icon.Glyph.Channel
          case EmailSendingManager.Provider  => Icon.Glyph.Cloud
        }
      ),
      isSelected = Option(currentPage == targetPage),
      renderContent = () =>
        targetPage match {
          case EmailSendingManager.Home      => "Dashboard"
          case EmailSendingManager.Urgent    => "Urgent"
          case EmailSendingManager.Scheduled => "Scheduled"
          case EmailSendingManager.Space     => "Space"
          case EmailSendingManager.Provider  => "Provider"
        },
      onClick = Callback.when(currentPage != targetPage)(routerCtl.set(targetPage))
    )
  }

  def apply(routerCtl: RouterCtl[Page], currentPage: EmailSendingManager): VdomNode = {
    ListR(
      items = Seq(
        renderItem(routerCtl, currentPage, EmailSendingManager.Home),
        renderItem(routerCtl, currentPage, EmailSendingManager.Urgent),
        renderItem(routerCtl, currentPage, EmailSendingManager.Scheduled),
        renderItem(routerCtl, currentPage, EmailSendingManager.Space),
        renderItem(routerCtl, currentPage, EmailSendingManager.Provider)
      )
    )()
  }

}
