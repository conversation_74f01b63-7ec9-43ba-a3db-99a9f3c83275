// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.emailsendingmanager

import design.anduin.components.modal.{<PERSON><PERSON><PERSON><PERSON>, ModalFooter}
import design.anduin.components.button.Button
import design.anduin.components.well.Well
import design.anduin.components.well.react.WellR
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import com.raquo.laminar.api.L.*
import design.anduin.components.toast.Toast

import anduin.email.sending.EmailSendingManagerAdminProtocols.{EmailProviderData, FailProviderParams}
import com.anduin.stargazer.client.utils.ZIOUtils

case class FailProviderModal(emailProviderData: EmailProviderData, close: Callback, closeAndRefresh: Callback) {
  def apply(): VdomElement = FailProviderModal.component(this)
}

object FailProviderModal {

  private type Props = FailProviderModal

  private final case class State(
    formBusy: Boolean
  )

  private class Backend(scope: BackendScope[Props, State]) {

    private def failProvider(props: Props): Callback = {
      ZIOUtils.toReactCallbackWithErrorHandler(
        EmailSendingManagerAdminClient
          .failProvider(FailProviderParams(props.emailProviderData.id))
          .map(
            _.fold(
              error => scope.modState(_.copy(formBusy = false), Toast.errorCallback(error.message)),
              _ =>
                scope.modState(
                  _.copy(formBusy = false),
                  Toast.successCallback("Provider marked as failed") >> props.closeAndRefresh
                )
            )
          ),
        _ => scope.modState(_.copy(formBusy = false), Toast.errorCallback("Please check your network connection"))
      )
    }

    def render(props: Props, state: State): VdomElement = {
      React.Fragment(
        ModalBody()(
          WellR(style = Well.Style.Warning())(
            div(
              "Do you want to mark this email provider as failed?",
              div(
                tw.fontBold.mt12,
                s"${props.emailProviderData.name} (${props.emailProviderData.id})"
              )
            )
          )
        ),
        ModalFooter()(
          <.div(
            tw.flex.justifyEnd.itemsCenter,
            <.div(
              Button(
                style = Button.Style.Full(
                  color = Button.Color.Gray0,
                  isBusy = state.formBusy
                ),
                onClick = props.close
              )("Cancel")
            ),
            <.div(
              tw.ml4,
              Button(
                style = Button.Style.Full(
                  color = Button.Color.Primary,
                  isBusy = state.formBusy
                ),
                isDisabled = state.formBusy,
                onClick = scope.modState(_.copy(formBusy = true), failProvider(props))
              )("Mark as failed")
            )
          )
        )
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(State(formBusy = false))
    .renderBackend[Backend]
    .build

}
