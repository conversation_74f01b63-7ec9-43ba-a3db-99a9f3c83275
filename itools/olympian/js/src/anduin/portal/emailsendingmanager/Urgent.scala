// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.emailsendingmanager

import java.time.{Instant, LocalDate}

import design.anduin.components.button.Button
import design.anduin.components.date.react.DatePickerR
import design.anduin.components.dropdown.Dropdown
import design.anduin.components.pagination.react.PaginationR
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.components.table.Table
import design.anduin.components.toast.Toast

import anduin.id.role.portal.PortalSectionId
import anduin.portal.{AdminPortalLayout, AdminPortalNavBar}
import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.vdom.html_<^.*
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import anduin.email.sending.EmailSendingManagerAdminProtocols.{
  ScanEmailByPriorityAndStatusParams,
  ScanEmailByPriorityAndStatusResponse
}
import anduin.email.sending.{EmailSendingTask, EmailSendingTaskPriority, EmailSendingTaskStatus}
import anduin.utils.DateTimeUtils
import com.anduin.stargazer.client.utils.ZIOUtils
import stargazer.model.routing.DynamicAuthPage.Portal.EmailSendingManager
import stargazer.model.routing.Page

final case class Urgent(routerCtl: RouterCtl[Page]) {
  def apply(): VdomElement = Urgent.component(this)
}

object Urgent {

  private type Props = Urgent

  private sealed trait DataState derives CanEqual

  private case object DataStateInitial extends DataState
  private case object DataStateLoading extends DataState

  private final case class DataStateLoaded(
    response: ScanEmailByPriorityAndStatusResponse
  ) extends DataState

  private final case class State(
    date: Option[LocalDate],
    status: Option[EmailSendingTaskStatus],
    page: Int,
    dataState: DataState
  )

  private class Backend(scope: BackendScope[Props, State]) {

    private val StatusDropdown = (new Dropdown[EmailSendingTaskStatus])()
    private val EmailTaskTable = (new Table[EmailSendingTask])()

    private def doFilter(param: ScanEmailByPriorityAndStatusParams): Callback = {
      ZIOUtils.toReactCallbackWithErrorHandler(
        EmailSendingManagerAdminClient
          .scanEmailByPriorityAndStatus(param)
          .map(
            _.fold(
              error => scope.modState(_.copy(dataState = DataStateInitial), Toast.errorCallback(error.message)),
              response => scope.modState(_.copy(dataState = DataStateLoaded(response)))
            )
          ),
        _ =>
          scope
            .modState(_.copy(dataState = DataStateInitial), Toast.errorCallback("Please check your network connection"))
      )
    }

    private def filter: Callback = {
      for {
        state <- scope.state
        paramOpt = for {
          date <- state.date
          status <- state.status
        } yield ScanEmailByPriorityAndStatusParams(
          priority = EmailSendingTaskPriority.URGENT,
          status = status,
          date = date,
          page = state.page
        )
        _ <- paramOpt.fold(Callback.empty) { param =>
          scope.modState(_.copy(dataState = DataStateLoading), doFilter(param))
        }
      } yield ()
    }

    private def renderFilter(state: State): VdomElement = {
      <.div(
        tw.flex.itemsCenter,
        <.div(
          DatePickerR(
            date = state.date,
            onSelectDate = date => scope.modState(_.copy(date = date)),
            maxDate = LocalDate.now(),
            showTodayButton = true
          )()
        ),
        <.div(
          tw.ml8,
          StatusDropdown(
            value = state.status,
            valueToString = _.name,
            onChange = status => scope.modState(_.copy(status = Some(status))),
            items = EmailSendingTaskStatus.values.filter(_ != EmailSendingTaskStatus.UNKNOWN).map(Dropdown.Item(_))
          )()
        ),
        <.div(
          tw.ml8,
          Button(
            style = Button.Style.Full(
              color = Button.Color.Gray0,
              isBusy = state.dataState == DataStateLoading
            ),
            isDisabled = state.status.isEmpty || state.date.isEmpty,
            onClick = scope.modState(_.copy(page = 1), filter)
          )("Filter")
        )
      )
    }

    private def renderData(state: State): VdomNode = {
      <.div(
        tw.mt20,
        state.dataState match {
          case DataStateInitial => EmptyVdom
          case DataStateLoading => BlockIndicatorR()()
          case DataStateLoaded(response) =>
            if (response.tasks.isEmpty) {
              <.div(
                tw.fontBold.text23.textCenter.mt20,
                "No tasks found"
              )
            } else {
              <.div(
                <.div(
                  tw.fontBold.text15.mt20,
                  s"Email tasks found: ${response.total}"
                ),
                <.div(
                  tw.mt20,
                  EmailTaskTable(
                    rows = response.tasks,
                    getKey = _.id.idString,
                    columns = Seq(
                      Table.Column(
                        head = "Id",
                        render = value => Table.Cell(value.id.idString)
                      ),
                      Table.Column(
                        head = "Sender",
                        render = value => Table.Cell(value.sender)
                      ),
                      Table.Column(
                        head = "Receivers",
                        render = value => Table.Cell(value.receivers.mkString(", "))
                      ),
                      Table.Column(
                        head = "Subject",
                        render = value => Table.Cell(value.subject)
                      ),
                      Table.Column(
                        head = "Created at",
                        render = value =>
                          Table.Cell(
                            DateTimeUtils
                              .formatInstant(Instant.ofEpochMilli(value.createdAt), DateTimeUtils.DateAndTimeFormatter)(
                                using DateTimeUtils.defaultTimezone
                              )
                          )
                      ),
                      Table.Column(
                        head = "Space Id",
                        render = value => Table.Cell(value.spaceId.idString)
                      ),
                      Table.Column(
                        head = "Provider Id",
                        render = value => Table.Cell(value.providerId.idString)
                      ),
                      Table.Column(
                        head = "Attempts",
                        render = value => Table.Cell(value.systemAttempts.toString)
                      ),
                      Table.Column(
                        head = "Last attempt message",
                        render = value => Table.Cell(value.lastAttemptMessage)
                      )
                    )
                  )()
                ),
                if (response.totalPages > 1) {
                  <.div(
                    tw.mt12,
                    PaginationR(
                      totalPage = response.totalPages,
                      currentPage = state.page,
                      onJumpToPage = page => scope.modState(_.copy(page = page), filter),
                      showLastButton = true,
                      showFirstButton = true
                    )()
                  )
                } else {
                  EmptyVdom
                }
              )
            }
        }
      )
    }

    def render(props: Props, state: State): VdomElement = {
      AdminPortalLayout(
        requiredPortalSectionIdOpt = Some(PortalSectionId.Environment),
        renderSidebar = Some(Sidebar(props.routerCtl, EmailSendingManager.Urgent))
      )(
        AdminPortalNavBar("Email sending manager | Urgent")(),
        <.div(
          renderFilter(state),
          renderData(state)
        )
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(
      State(
        date = Some(LocalDate.now()),
        status = Some(EmailSendingTaskStatus.PENDING),
        page = 1,
        dataState = DataStateInitial
      )
    )
    .renderBackend[Backend]
    .build

}
