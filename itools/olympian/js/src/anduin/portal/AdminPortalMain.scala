// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal

import design.anduin.components.icon.react.IconR
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import anduin.id.role.portal.PortalSectionId
import anduin.portal.admin.PortalUserClient
import anduin.portal.common.PortalTool
import com.anduin.stargazer.client.utils.ZIOUtils
import stargazer.component.routing.react.WithReactRouterR
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.tapir.endpoint.CommonParams

final case class AdminPortalMain() {
  def apply(): VdomElement = AdminPortalMain.component(this)
}

object AdminPortalMain {

  private type Props = AdminPortalMain

  private final case class State(
    isInitialized: Boolean,
    portalSectionIds: Set[PortalSectionId]
  )

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState {
      State(
        isInitialized = false,
        Set.empty
      )
    }
    .render_S(render)
    .componentDidMount { scope =>
      ZIOUtils.toReactCallback {
        PortalUserClient
          .getCurrentUser(CommonParams.Empty())
          .map {
            _.fold(
              _ => Callback.empty, // already handle in AdminPortalLayout
              resp =>
                scope.modState {
                  _.copy(
                    isInitialized = true,
                    portalSectionIds = resp.sectionIds
                  )
                }
            )
          }
      }
    }
    .build

  private def render(state: State): VdomElement = {
    val (activeTools, disabledTools) = PortalTool.AllPortalTools.partition { portalTool =>
      state.portalSectionIds.exists(sectionId => portalTool.sectionId.forall(_ == sectionId))
    }
    AdminPortalLayout(None)(
      AdminPortalNavBar(prevPageOpt = None)(),
      if (!state.isInitialized) {
        BlockIndicatorR(Some("Checking your permissions..."))()
      } else {
        <.div(
          tw.flex.flexCol.mxAuto.pt32,
          ^.width := 1160.px,
          // Active tools
          renderActiveTools(activeTools),
          // Disabled tools
          TagMod.when(disabledTools.nonEmpty) {
            renderDisabledTools(disabledTools)
          }
        )
      }
    )
  }

  private def renderActiveTools(activeTools: Seq[PortalTool]) = {
    React.Fragment(
      <.div(
        tw.flex.itemsCenter.mb12,
        <.div(
          ^.height := 10.px,
          ^.width := 10.px,
          tw.bgSuccess3.rounded2.mr8
        ),
        <.div(
          tw.uppercase.textGray7.fontMedium,
          "Active tools"
        )
      ),
      WithReactRouterR { router =>
        <.div(
          tw.mb48.grid.gridCols3.gapX16.gapY16,
          activeTools
            .sortBy(_.name)
            .toVdomArray(
              using { tool =>
                <.a(
                  ^.key := tool.name,
                  tw.bgGray0.borderAll.border1.borderGray2.rounded4.p16,
                  tw.flex.itemsCenter.noUnderline.cursorPointer.hover(tw.noUnderline),
                  tw.hover(tw.shadow2.borderPrimary4),
                  ^.href := router.urlFor(tool.page).value,
                  <.div(
                    tw.hPx48.wPx48.roundedFull.mr16,
                    tw.flex.itemsCenter.justifyCenter,
                    tw.bgPrimary4.bgOpacity10.textPrimary4,
                    IconR(name = tool.iconName)()
                  ),
                  <.div(
                    <.div(
                      tw.text15.leading20.fontMedium.textGray8.capitalize,
                      tool.name
                    ),
                    <.div(
                      tw.textGray7,
                      tool.description
                    )
                  )
                )
              }
            )
        )
      }
    )
  }

  private def renderDisabledTools(disabledTools: Seq[PortalTool]) = {
    React.Fragment(
      React.Fragment(
        <.div(
          tw.flex.itemsCenter.mb12,
          <.div(
            ^.height := 10.px,
            ^.width := 10.px,
            tw.bgWarning3.rounded2.mr8
          ),
          <.div(
            tw.uppercase.textGray7.fontMedium,
            "Disabled tools"
          )
        ),
        <.div(
          tw.mb48.grid.gridCols3.gapX16.gapY16,
          disabledTools
            .sortBy(_.name)
            .toVdomArray(
              using { tool =>
                <.div(
                  ^.key := tool.name,
                  tw.bgGray0.borderAll.border1.borderGray2.rounded4.p16,
                  tw.flex.itemsCenter.noUnderline.hover(tw.noUnderline),
                  tw.opacity60,
                  <.div(
                    tw.hPx48.wPx48.roundedFull.mr16,
                    tw.flex.itemsCenter.justifyCenter,
                    tw.bgWarning4.bgOpacity10.textWarning4,
                    IconR(name = tool.iconName)()
                  ),
                  <.div(
                    <.div(
                      tw.text15.leading20.fontMedium.textGray8.capitalize,
                      tool.name
                    ),
                    <.div(
                      tw.textGray7,
                      tool.description
                    )
                  )
                )
              }
            )
        )
      )
    )
  }

}
