// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.integplatform

import zio.Task

import anduin.tapir.client.AuthenticatedEndpointClient
import anduin.integplatform.endpoints.*
import anduin.service.GeneralServiceException
import anduin.tapir.endpoint.EmptyResponse

object IntegPlatformPortalClient extends AuthenticatedEndpointClient {

  val getAllEntities
    : GetAllIntegPlatformEntitiesParams => Task[Either[GeneralServiceException, GetAllIntegPlatformEntitiesResponse]] =
    toClientThrowDecodeAndSecurityFailures(IntegPlatformAdminPortalEndpoint.getAllEntities)

  val getFeatureAccess
    : GetIntegPlatformFeatureAccessParams => Task[Either[GeneralServiceException, IntegPlatformEntityFeatureAccess]] =
    toClientThrowDecodeAndSecurityFailures(IntegPlatformAdminPortalEndpoint.getFeatureAccess)

  val queryMarketplaceIntegration
    : QueryMarketplaceIntegrationParams => Task[Either[GeneralServiceException, QueryMarketplaceIntegrationResponse]] =
    toClientThrowDecodeAndSecurityFailures(IntegPlatformAdminPortalEndpoint.queryMarketplaceIntegration)

  val activatePremiumIntegrationAccess
    : ActivatePremiumIntegrationAccessParams => Task[Either[GeneralServiceException, EmptyResponse]] =
    toClientThrowDecodeAndSecurityFailures(IntegPlatformAdminPortalEndpoint.activatePremiumIntegrationAccess)

}
