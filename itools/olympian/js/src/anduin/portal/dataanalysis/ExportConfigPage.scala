// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.portal.dataanalysis

import java.time.ZoneId
import java.time.format.{DateTimeFormatter, FormatStyle}

import anduin.analytics.endpoint.JobDetail
import anduin.tapir.endpoint.CommonParams
import design.anduin.components.button.Button
import design.anduin.components.icon.{Icon, IconGlyph}
import design.anduin.components.modal.Modal
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.components.table.Table
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import com.anduin.stargazer.client.utils.ZIOUtils
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.analytics.endpoint.JobDetail.Status.{Paused, Running, Unspecified}

final case class ExportConfigPage() {
  def apply(): VdomElement = ExportConfigPage.component()
}

object ExportConfigPage {

  private final case class State(
    configLoaded: Boolean,
    btnBusy: Boolean,
    btnActionEventBusy: Boolean,
    jobDetails: Seq[JobDetail],
    defaultS3Prefix: String
  )

  private class Backend(scope: BackendScope[Unit, State]) {

    def refetch: Callback = {
      ZIOUtils.toReactCallbackWithErrorHandler {
        DataAnalysisClient.getAllWorkflowCronJob(CommonParams.Empty()).map {
          _.fold(
            error => Toast.errorCallback(error.message),
            resp => {
              scope.modState(_.copy(jobDetails = resp.cronJobs, defaultS3Prefix = resp.defaultS3PathPrefix))
            }
          )
        }
      }
    }

    private val JobDetailTable = (new Table[JobDetail])()

    private def actionModal(
      jobDetail: JobDetail,
      action: CronJobActionModal.ActionType,
      icon: IconGlyph
    ) = {
      val actionText = action.actionText.capitalize
      Modal(
        title = s"$actionText cron job",
        size = Modal.Size(Modal.Width.Px480),
        renderTarget = open =>
          TooltipR(
            renderTarget = Button(
              style = Button.Style.Minimal(icon = Some(icon)),
              onClick = open
            )(),
            renderContent = _(actionText)
          )(),
        renderContent = onClose =>
          CronJobActionModal(
            jobDetail.temporalWorkflowId,
            jobDetail.workflow,
            action,
            onClose >> refetch
          )()
      )()
    }

    private def viewRunsModal(jobDetail: JobDetail) = {
      Modal(
        title = jobDetail.temporalWorkflowId.idString,
        size = Modal.Size(Modal.Width.Px960),
        renderTarget = open =>
          TooltipR(
            renderTarget = Button(
              style = Button.Style.Minimal(icon = Some(Icon.Glyph.Eye)),
              onClick = open
            )(),
            renderContent = _("View runs")
          )(),
        renderContent = onClose => CronJobRunModal(jobDetail.temporalWorkflowId, onClose)()
      )()
    }

    private def actionCell(jobDetail: JobDetail) = {
      <.div(
        tw.flex,
        <.div(
          tw.ml8,
          viewRunsModal(jobDetail)
        ),
        <.div(
          tw.ml8,
          actionModal(jobDetail, CronJobActionModal.Trigger, Icon.Glyph.StepForward)
        ),
        <.div(
          tw.ml8,
          jobDetail.status match {
            case Running     => actionModal(jobDetail, CronJobActionModal.Pause, Icon.Glyph.Square)
            case Paused      => actionModal(jobDetail, CronJobActionModal.Unpause, Icon.Glyph.Reopen)
            case Unspecified => actionModal(jobDetail, CronJobActionModal.Delete, Icon.Glyph.Trash)
          }
        ),
        if (jobDetail.status == Paused) {
          // Allow deleting Paused workflows
          <.div(
            tw.ml8,
            actionModal(jobDetail, CronJobActionModal.Delete, Icon.Glyph.Trash)
          )
        } else {
          EmptyVdom
        }
      )
    }

    private def renderTable(state: State) = {
      <.div(
        tw.mt16,
        JobDetailTable(
          rows = state.jobDetails,
          getKey = _.temporalWorkflowId.idString,
          sortColumn = Some(0),
          columns = Seq(
            Table.Column(
              head = "Workflow name",
              render = jobDetail => Table.Cell(jobDetail.workflow.name),
              sortBy = Table.ColumnOrderingCaseInsensitive(_.workflow.name)
            ),
            Table.Column(
              head = "Schedule",
              render = jobDetail => Table.Cell(<.div(tw.fontMono, jobDetail.schedule))
            ),
            Table.Column(
              head = "Status",
              render = jobDetail => Table.Cell(ScheduleStatusTag(jobDetail.status)()),
              sortBy = Table.ColumnOrdering(_.status.ordinal)
            ),
            Table.Column(
              head = "S3 path",
              render = jobDetail =>
                Table.Cell(
                  jobDetail.customS3PathOpt.map(path => s"${state.defaultS3Prefix}$path")
                )
            ),
            Table.Column(
              head = "Created at",
              render = jobDetail =>
                Table.Cell(
                  jobDetail.createdAt.map { at =>
                    DateTimeFormatter
                      .ofLocalizedDateTime(FormatStyle.MEDIUM, FormatStyle.SHORT)
                      .withZone(ZoneId.systemDefault())
                      .format(at)
                  }
                ),
              sortBy = Table.ColumnOrdering(_.createdAt.map(_.getEpochSecond).getOrElse(0L))
            ),
            Table.Column(
              head = "Created by",
              render = jobDetail =>
                Table.Cell(
                  <.div(
                    ^.title := jobDetail.createdBy.idString,
                    jobDetail.createdByInfo.fullNameString
                  )
                ),
              sortBy = Table.ColumnOrdering { job =>
                job.createdByInfo.fullNameString -> job.createdBy.idString
              }
            ),
            Table.Column(
              render = jobDetail => Table.Cell(actionCell(jobDetail))
            )
          )
        )()
      )
    }

    private def addJobButton(state: State) = {
      Modal(
        title = "Add job",
        size = Modal.Size(Modal.Width.Px720),
        renderTarget = open =>
          Button(
            style = Button.Style.Full(color = Button.Color.Primary, icon = Some(Icon.Glyph.PlusCircleLine)),
            onClick = open
          )("Add job"),
        renderContent = onClose =>
          AddJobModal(
            onClose >> refetch,
            state.defaultS3Prefix
          )()
      )()
    }

    private def resetPublishStateButton(state: State): VdomElement = {
      <.div(
        tw.ml8,
        Modal(
          title = "Reset publish state",
          renderTarget = open =>
            Button(
              style = Button.Style.Full(
                color = Button.Color.Danger,
                isBusy = state.btnActionEventBusy,
                icon = Some(Icon.Glyph.Refresh)
              ),
              onClick = open
            )("Reset publishing state"),
          renderContent = onClose => ResetPublishingStateModal(onClose)()
        )()
      )
    }

    def render(state: State): VdomElement = {
      if (!state.configLoaded) {
        BlockIndicatorR()()
      } else {
        <.div(
          <.div(
            tw.flex,
            addJobButton(state),
            resetPublishStateButton(state)
          ),
          renderTable(state)
        )
      }
    }

    def didMount: Callback = {
      refetch >> scope.modState(_.copy(configLoaded = true, btnActionEventBusy = false))
    }

  }

  private val component = ScalaComponent
    .builder[Unit](getClass.getSimpleName)
    .initialState(
      State(
        configLoaded = false,
        btnBusy = true,
        btnActionEventBusy = true,
        jobDetails = Seq(),
        defaultS3Prefix = ""
      )
    )
    .renderBackend[Backend]
    .componentDidMount(_.backend.didMount)
    .build

}
