// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.client.component.viewer

import scala.concurrent.Promise
import scala.scalajs.js
import scala.util.{Failure, Success}

import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.Ref.Simple
import japgolly.scalajs.react.vdom.html_<^.*
import org.scalajs.dom.{HTMLElement, IntersectionObserver, IntersectionObserverEntry, IntersectionObserverInit}
import org.scalajs.macrotaskexecutor.MacrotaskExecutor.Implicits.global

import anduin.facades.viewer.pdfjsDist.mod.PDFDocumentProxy
import anduin.facades.viewer.pdfjsDist.typesSrcDisplayApiMod.{GetViewportParameters, PDFPageProxy}

final case class PageLayer(
  currentPage: Int,
  id: String,
  doc: PDFDocumentProxy,
  pageIndex: Int,
  additionalStyle: TagMod,
  initialHeight: Double,
  initialWidth: Double,
  scale: Double,
  getPageNode: Int => Option[HTMLElement],
  // For highlighting text
  onSizeCalculated: PageSize => Callback,
  onVisible: PageLayer.VisibilityChanged => Callback,
  onDrop: (ReactDragEvent, PageLayer, PageSize) => Callback = (_, _, _) => Callback.empty,
  onClick: (ReactMouseEvent, PageLayer, PageSize) => Callback = (_, _, _) => Callback.empty,
  // should render signature and prep layer or not
  shouldRenderPageLayer: Boolean = true,
  renderPageLayer: (PageLayer, PageSize) => TagMod = (_, _) => EmptyVdom,
  onTextLayerRendered: (HTMLElement, HTMLElement) => Callback = (_, _) => Callback.empty,
  key: Option[String] = None
) {

  def apply(): VdomElement = {
    val keyStr = key.getOrElse(s"PageLayer-$pageIndex")
    PageLayer.component.withKey(keyStr)(this)
  }

}

object PageLayer {

  // The number of pages that are before and after the current page
  // They will be pre-rendered, so that the experience is better when users scroll up and down
  private val PreRenderPages = 3

  private final case class State(
    pageOpt: Option[PDFPageProxy] = None,
    width: Double = 0,
    height: Double = 0
  )

  case class VisibilityChanged(
    pageIndex: Int,
    isVisible: Boolean,
    ratio: Double
  )

  private val Threshold = js.Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0)

  private class Backend(scope: BackendScope[PageLayer, State]) {

    private val containerRef: Simple[HTMLElement] = Ref[HTMLElement]
    private var visibleTracker = Option.empty[IntersectionObserver] // scalafix:ok DisableSyntax.var

    private def handleIntersection(entries: js.Array[IntersectionObserverEntry], instance: IntersectionObserver) = {
      val _ = instance

      // Find the entry that has the biggest ratio
      val (ratio, isVisible) = entries
        .filter(_.isIntersecting)
        .minByOption(_.intersectionRatio)
        .map(entry => (entry.intersectionRatio, entry.isIntersecting))
        .getOrElse((0.0, false))

      val cb = for {
        props <- scope.props
        _ <- props.onVisible(
          VisibilityChanged(
            props.pageIndex,
            isVisible,
            ratio
          )
        )
        // Only render the page when it's visible
        _ <- Callback.when(isVisible) {
          calculateSize
        }
      } yield ()
      cb.runNow()
    }

    private def onTextLayerRendered(textLayerNode: HTMLElement) = {
      for {
        props <- scope.props
        _ <- containerRef.foreachCB { container =>
          props.onTextLayerRendered(container, textLayerNode)
        }
      } yield ()
    }

    private def calculateSize: Callback = {
      for {
        props <- scope.props
        state <- scope.state
        _ <- Callback.when(state.pageOpt.isEmpty) {
          // Calculate size of page, because not all pages have the same size
          Callback.future {
            val promise = Promise[Callback]()
            props.doc.getPage(props.pageIndex).toFuture.onComplete {
              case Success(page) =>
                promise.success {
                  // Need to determine the width/height of each page because not all pages have the same size
                  val viewport = page.getViewport(GetViewportParameters(scale = props.scale))
                  val width = viewport.width / props.scale
                  val height = viewport.height / props.scale

                  scope.modState(
                    _.copy(
                      pageOpt = Option(page),
                      width = width,
                      height = height
                    ),
                    props.onSizeCalculated(
                      PageSize(
                        props.pageIndex,
                        width,
                        height,
                        props.scale
                      )
                    )
                  )
                }
              case Failure(_) =>
                promise.success {
                  Callback.log(s"Can't render the page layer ${props.pageIndex}")
                }
            }

            promise.future
          }
        }
      } yield ()
    }

    def onMount(): Callback = {
      for {
        containerEleOpt <- containerRef.get
        _ <- Callback.traverseOption(containerEleOpt) { containerEle =>
          Callback {
            // scalafix:off DisableSyntax.asInstanceOf
            val visibleTrackerInstance = new IntersectionObserver(
              callback = handleIntersection(_, _),
              options = js.Dynamic
                .literal(
                  threshold = Threshold
                )
                .asInstanceOf[IntersectionObserverInit]
            )
            // scalafix:on DisableSyntax.asInstanceOf
            visibleTrackerInstance.observe(containerEle)
            visibleTracker = Option(visibleTrackerInstance)
          }
        }
        _ <- renderWhenInRange()
      } yield ()
    }

    def renderWhenInRange(): Callback = {
      for {
        props <- scope.props
        // Check if the page is in the current page's range
        inRange =
          props.pageIndex >= props.currentPage - PreRenderPages && props.pageIndex <= props.currentPage + PreRenderPages
        _ <- Callback.when(inRange) {
          calculateSize
        }
      } yield ()
    }

    def onUnmount(): Callback = {
      Callback {
        visibleTracker.foreach {
          _.disconnect()
        }
      }
    }

    def render(props: PageLayer, state: State): VdomElement = {
      val pageSize = PageSize(
        props.pageIndex,
        state.width,
        state.height,
        props.scale
      )
      val w = pageSize.scaledWidth
      val h = pageSize.scaledHeight

      <.div.withRef(containerRef)(
        ComponentUtils.testId(PageLayer, "DocumentPage"),
        VdomAttr("data-current-page") := props.currentPage,
        tw.relative,
        props.additionalStyle,
        ^.width := s"${w}px",
        ^.height := s"${h}px",
        ^.id := props.id,
        // Need to prevent the default behaviour of `onDragOver` to get `onDrop` working
        ^.onDragOver ==> (_.preventDefaultCB),
        ^.onDrop ==> { e =>
          props.onDrop(
            e,
            props,
            pageSize
          )
        },
        ^.onClick ==> { e =>
          props.onClick(
            e,
            props,
            pageSize
          )
        },
        state.pageOpt.map { page =>
          <.div(
            // Canvas layer
            CanvasLayer(page, pageSize)(),
            TagMod.when(props.shouldRenderPageLayer) {
              props.renderPageLayer(props, pageSize)
            },
            // Text layer
            // In order to make the text selectable, we need to place the text layer at the very end of stack
            TextLayer(
              page = page,
              pageSize = pageSize,
              onRendered = onTextLayerRendered
            )(),
            // Annotation layer
            AnnotationLayer(props.doc, page, pageSize)()
          )
        }
      )
    }

  }

  private val component = ScalaComponent
    .builder[PageLayer](getClass.getSimpleName)
    .initialStateFromProps { props =>
      State(
        pageOpt = None,
        width = props.initialWidth,
        height = props.initialHeight
      )
    }
    .renderBackend[Backend]
    .componentDidMount(_.backend.onMount())
    .componentWillUnmount(_.backend.onUnmount())
    .componentDidUpdate { scope =>
      Callback.when(scope.currentProps.currentPage != scope.prevProps.currentPage) {
        scope.backend.renderWhenInRange()
      }
    }
    .build

}
