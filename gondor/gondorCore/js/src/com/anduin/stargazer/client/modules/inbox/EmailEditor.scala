// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.client.modules.inbox

import anduin.file.upload.{FileUpload, FileUploadModal}
import anduin.id.TransactionId
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.email.{EmailTag<PERSON>ey, TagMap}
import anduin.protobuf.email.EmailTagValues
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.services.email.EmailClient
import com.anduin.stargazer.utils.EmailAddressJsUtils
import com.raquo.airstream.core.Observer
import com.raquo.airstream.eventbus.EventBus
import com.raquo.airstream.ownership.{OneTimeOwner, Subscription}
import com.raquo.laminar.api.L
import design.anduin.components.button.Button
import design.anduin.components.editor.react.RichEditorR
import design.anduin.components.editor.{EditorRenderer, TransformerUtil}
import design.anduin.components.icon.Icon
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.progress.react.CircleIndicatorR
import design.anduin.components.suggest.MultiSuggest
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.facades.dompurify.DomPurifyUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.extra.TimerSupport
import com.anduin.stargazer.client.utils.ZIOUtils
import zio.{Task, ZIO}
import scala.language.postfixOps

import anduin.model.id.*
import com.anduin.stargazer.service.EmailEndpoints.*
import design.anduin.components.editor.laminar.*
import design.anduin.components.portal.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import scala.concurrent.duration.*

final case class EmailEditor(
  trxnIdOpt: Option[TransactionId],
  currentUserEmailAddress: EmailAddress,
  replyTo: Option[EmailId],
  // emailId and messageId may not be available during generateEmailId api call.
  emailId: Option[EmailId],
  messageId: Option[String],
  initialRecipients: Seq[EmailAddress],
  initialCcRecipients: Seq[EmailAddress],
  initialSubject: String,
  initialHtmlBody: String,
  initialAttachments: Seq[FileId],
  allowEditRecipients: Boolean,
  allowEditSubject: Boolean,
  allowRemoveInitialAttachment: Boolean,
  showInputSubject: Boolean,
  cancelText: String = "Cancel",
  onCancel: Callback,
  onSendSuccess: EmailId => Callback,
  renderEmailFn: EmailAddress => TagMod,
  allContacts: Seq[EmailWithPartyName],
  includeTrxnLink: Boolean = true, // whether to include trxn link at email footer
  isForMobile: Boolean = false,
  enablePreview: Boolean = false
) {

  def apply(): VdomElement = {
    EmailEditor.component(this)
  }

}

object EmailEditor {

  private val ComponentName = getClass.getSimpleName

  private type Props = EmailEditor

  private sealed trait SaveStatus derives CanEqual

  private object SaveStatus {

    case object NotSaving extends SaveStatus

    case object Saving extends SaveStatus

    case object SaveError extends SaveStatus
  }

  private final case class State(
    dirty: Boolean = false,
    saveStatus: SaveStatus = SaveStatus.NotSaving,
    recipients: Seq[EmailAddress],
    ccRecipients: Seq[EmailAddress],
    subject: String,
    editorValue: String,
    sendingIsBusy: Boolean = false,
    storedFiles: Seq[FileId] = Seq(),
    showingCc: Boolean = false,
    isTagSelectedFromModal: Boolean = false
  ) {

    lazy val attachments: Seq[FileId] = {
      storedFiles
    }

  }

  private final class Backend(scope: BackendScope[Props, State]) extends TimerSupport {

    def render(props: Props, state: State): VdomNode = {

      props.emailId.fold(EmptyVdom) { _ =>
        <.div(
          ComponentUtils.testId(EmailEditor, "ComposeContainer"),
          ^.cls := "compose-message",
          tw.bgGray0,
          <.div(
            ^.cls := s"-inline ${tw.relative}",
            renderSaving(state),
            <.div(
              ComponentUtils.testId(EmailEditor, "FromField"),
              tw.relative.flex.itemsCenter.px20,
              tw.bgGray2.hPx40.cursorNotAllowed,
              <.span(tw.fontSemiBold, "From:"),
              TooltipR(
                renderTarget = <.span(
                  tw.ml8,
                  props.renderEmailFn(props.currentUserEmailAddress),
                  " via Anduin"
                ),
                renderContent = _(
                  props.trxnIdOpt.fold(props.currentUserEmailAddress.address) { trxnId =>
                    EmailAddressJsUtils
                      .getTrxnEmailAddress(trxnId, "")
                      .address
                  }
                )
              )()
            ),
            renderRecipients(props, state),
            TagMod.when(state.showingCc)(renderCc(props, state)),
            TagMod.when(props.showInputSubject) {
              <.div(
                ComponentUtils.testId(EmailEditor, "SubjectField"),
                tw.flex.itemsCenter,
                tw.borderGray3.borderTop.hPx40.px20,
                <.div(
                  tw.flexFill,
                  if (props.allowEditSubject) {
                    <.input(
                      tw.wPc100,
                      ^.placeholder := "Subject...",
                      ^.tpe := "text",
                      ^.value := state.subject,
                      ^.onChange ==> onChangeSubject
                    )
                  } else {
                    <.span(
                      tw.wPc100,
                      props.initialSubject
                    )
                  }
                )
              )
            },
            renderMessageBox(props, state)
          )
        )
      }
    }

    private def renderEditor(editorRenderer: EditorRenderer) = {
      val editorInstance = editorRenderer.editorInstance
      L.div(
        tw.borderTop.borderGray3,
        // Toolbar
        L.div(
          tw.flex.itemsCenter.flexWrap.p4,
          tw.borderBottom.borderGray3,
          L.div(
            tw.mr2,
            UndoButton(
              editorInstance = editorInstance
            )()
          ),
          L.div(
            tw.mr2,
            RedoButton(
              editorInstance = editorInstance
            )()
          ),
          L.div(
            tw.mr2,
            FontSizeButton(
              editorInstance = editorInstance
            )()
          ),
          L.div(
            tw.mr2,
            TextFormatButton(
              editorInstance = editorInstance,
              formatType = TextFormatButton.FormatType.Bold
            )()
          ),
          L.div(
            tw.mr2,
            TextFormatButton(
              editorInstance = editorInstance,
              formatType = TextFormatButton.FormatType.Italic
            )()
          ),
          L.div(
            tw.mr2,
            TextFormatButton(
              editorInstance = editorInstance,
              formatType = TextFormatButton.FormatType.Underline
            )()
          ),
          L.div(
            tw.mr2,
            TextFormatButton(
              editorInstance = editorInstance,
              formatType = TextFormatButton.FormatType.StrikeThrough
            )()
          ),
          L.div(
            tw.mr2,
            TextAlignButton(
              editorInstance = editorInstance
            )()
          ),
          L.div(
            tw.mr2,
            TextFormatButton(
              editorInstance = editorInstance,
              formatType = TextFormatButton.FormatType.Blockquote
            )()
          ),
          L.div(
            tw.mr2,
            BulletListButton(
              editorInstance = editorInstance
            )()
          ),
          L.div(
            tw.mr2,
            NumberListButton(
              editorInstance = editorInstance
            )()
          ),
          L.div(
            tw.mr2,
            LinkButton(
              editorInstance = editorInstance
            )()
          ),
          L.div(
            tw.mr2,
            ImageButton(
              editorInstance = editorInstance
            )()
          ),
          L.div(
            tw.mr2,
            BackgroundColorButton(
              editorInstance = editorInstance
            )()
          ),
          L.div(
            tw.mr2,
            TextColorButton(
              editorInstance = editorInstance
            )()
          )
        ),
        // Editor content
        editorRenderer.editorNode
      )
    }

    private def renderMessageBox(props: Props, state: State) = {
      <.div(
        RichEditorR(
          initialValue = state.editorValue,
          placeholder = "Write your email ...",
          onChange = data => {
            scope.modState(_.copy(editorValue = data.value, dirty = true), saveDraft)
          },
          render = renderEditor
        )(),
        // Attachments
        renderAttachments(props, state),
        <.div(
          tw.flex.itemsCenter.p12.borderTop.borderGray3,
          TagMod.unless(props.isForMobile) {
            <.div(
              tw.mrAuto,
              PopoverR(
                position = PortalPosition.TopCenter,
                renderTarget = (toggle, isOpened) => {
                  TooltipR(
                    targetWrapper = PortalWrapper.Inline,
                    renderTarget = Button(
                      style = Button.Style.Minimal(isSelected = isOpened, icon = Some(Icon.Glyph.Attachment)),
                      onClick = saveDraft >> toggle
                    )(),
                    renderContent = _("Upload documents")
                  )()
                },
                renderContent = closePopover =>
                  <.div(
                    tw.p4,
                    getUppyUploadBtn(props, closePopover)
                  )
              )()
            )
          },
          renderSendEmailBtn(props, state)
        )
      )
    }

    private def createFolderIfNecessary(emailId: EmailId) = {
      val task = for {
        _ <- EmailClient.getEmailFolder(GetEmailFolderParams(emailId))
      } yield ()
      ZIOUtils.toReactCallbackUnit(task)
    }

    private def afterUpload(emailId: EmailId, folderId: FolderId)(selectedFileIds: Seq[FileId]) = {
      val (emailChannelFiles, otherChannelsFiles) = selectedFileIds.partition(_.folder.idString == folderId.idString)

      val allEmailChannelsFiles = for {
        apiRes <- EmailClient.shareFilesToEmail(
          ShareFilesToEmailParams(
            emailId,
            otherChannelsFiles
          )
        )
        fileRes <- ZIO.fromEither(apiRes)
      } yield emailChannelFiles ++ fileRes.fileRevIds.map(_._2)

      ZIOUtils.toReactCallback {
        allEmailChannelsFiles.map { fileIds =>
          scope.modState { state =>
            state.copy(storedFiles = state.storedFiles ++ fileIds)
          }
        }
      }
    }

    def getUppyUploadBtn(props: Props, closePopover: Callback): TagMod = {
      props.emailId.whenDefined(
        using { emailId =>
          val folderId = FolderId.channelSystemFolderId(emailId)
          FileUploadModal(
            FileUpload.default(
              folderId = folderId,
              onUpload = (fileSeq, _) => afterUpload(emailId, folderId)(fileSeq) >> closePopover
            ),
            renderTarget = openCb => {
              Button(
                style = Button.Style.Minimal(),
                onClick = openCb >> createFolderIfNecessary(emailId)
              )(<.span(ComponentUtils.testId(EmailEditor, "UploadButton"), "Upload documents"))
            }
          )()
        }
      )
    }

    private def renderSaving(state: State) = {
      <.div(
        ^.bottom := "48px",
        tw.absolute.right0.pr24,
        state.saveStatus match {
          case SaveStatus.Saving =>
            <.div(
              ^.cls := s"txt-grey ${tw.flex.itemsCenter}",
              <.span("Saving"),
              <.div(tw.ml4, CircleIndicatorR()())
            )
          case SaveStatus.SaveError =>
            <.div(
              ^.cls := "txt-danger",
              <.span("Something went wrong, your email could not be saved.")
            )
          case SaveStatus.NotSaving =>
            ""
        }
      )
    }

    private def renderRecipients(props: Props, state: State) = {
      <.div(
        ComponentUtils.testId(EmailEditor, "ToField"),
        tw.flex.itemsCenter.py4.px20,
        <.span(tw.fontSemiBold, "To:"),
        if (props.allowEditRecipients) {
          <.div(
            tw.mr16.wPc100,
            RecipientInput(
              onChange = onChangeRecipients,
              selectedRecipients = state.recipients,
              allContacts = props.allContacts,
              validator = Some(EmailAddress.isValid),
              appearance = MultiSuggest.Appearance.Minimal
            )()
          )
        } else {
          <.div(
            ^.cls := "recipient-input",
            tw.flex.itemsCenter.flexWrap.wPc100,
            props.initialRecipients.distinct.toVdomArray(
              using { emailAddress =>
                <.div(
                  ^.cls := "recipient-tag small",
                  tw.flex.itemsCenter.roundedFull.fontSemiBold.mr4.px8,
                  ^.key := s"$ComponentName-recipient-$emailAddress",
                  props.renderEmailFn(emailAddress)
                )
              }
            )
          )
        },
        <.div(
          tw.mlAuto.fontSemiBold.cursorPointer,
          TagMod.when(state.showingCc)(tw.hidden),
          ^.onClick --> scope.modState(_.copy(showingCc = true)),
          "Cc"
        )
      )
    }

    private def renderCc(props: Props, state: State) = {
      <.div(
        <.div(tw.borderTop.borderGray3),
        <.div(
          tw.flex.itemsCenter.py4.px20,
          <.span(tw.fontSemiBold, "Cc:"),
          <.div(
            tw.mr16.wPc100,
            RecipientInput(
              onChange = onChangeCc,
              selectedRecipients = state.ccRecipients,
              allContacts = props.allContacts,
              validator = Some(EmailAddress.isValid),
              placeholder = "Add Cc recipients...",
              appearance = MultiSuggest.Appearance.Minimal
            )()
          )
        )
      )
    }

    private def renderAttachments(props: Props, state: State): TagMod = {
      val attachmentList = state.storedFiles.map { file =>
        EmailAttachments.EmailAttachment(
          fileId = file,
          allowRemove = props.allowRemoveInitialAttachment
        )
      }

      TagMod.when(attachmentList.nonEmpty)(
        <.div(
          ^.onClick ==> (_.stopPropagationCB),
          tw.mx4,
          EmailAttachments(
            emailAttachments = attachmentList.toList,
            onRemoveAttachment = fileId =>
              scope.modState { state =>
                state.copy(storedFiles = state.storedFiles.filterNot(_ == fileId))
              }
          )()
        )
      )
    }

    private def onClickCancel = {
      for {
        props <- scope.props
        _ <- props.onCancel
      } yield ()
    }

    private def renderSendEmailBtn(props: Props, state: State) = {
      <.div(
        tw.flex.ml8,
        <.div(
          tw.mr8,
          Button(onClick = onClickCancel)(<.span(ComponentUtils.testId(EmailEditor, "CancelButton"), props.cancelText))
        ),
        Button(
          style = Button.Style.Full(
            color = Button.Color.Primary,
            isBusy = state.sendingIsBusy
          ),
          isDisabled = state.recipients.isEmpty,
          onClick = onSendEmail
        )(<.span(ComponentUtils.testId(EmailEditor, "SendButton"), "Send email"))
      )
    }

    private def onSendEmail = {
      for {
        props <- scope.props
        state <- scope.state
        _ <- scope.modState(_.copy(sendingIsBusy = true))
        _ <- waitForChangesToSave {
          saveAndSend(props, state).map {
            _.fold[Callback](
              _ => scope.modState(_.copy(sendingIsBusy = false)),
              response => onSendSuccess(response.emailId)
            )
          }
        }
      } yield ()
    }

    private def onSendSuccess(emailId: EmailId): Callback = {
      for {
        props <- scope.props
        _ <- scope.modState(
          _.copy(
            sendingIsBusy = false,
            editorValue = ""
          )
        )
        _ <- props.onSendSuccess(emailId)
      } yield ()
    }

    private val taskEventBus = new EventBus[Task[Callback]]

    private val taskEventStream = taskEventBus.events
      .debounce((2 seconds).toMillis.toInt)
      .flatMapSwitch { task =>
        ZIOUtils.toEventStreamUnsafeDEPRECATED(task.map(_.runNow()))
      }

    private var subscription: Option[Subscription] = None // scalafix:ok DisableSyntax.var

    def start: Callback = {
      Callback {
        subscription.foreach(_.kill())
        val owner = new OneTimeOwner(() => scribe.error("EmailEditor owner accessed after killed"))
        subscription = Option(
          taskEventStream.addObserver(Observer.empty)(
            using owner
          )
        )
      }
    }

    def stop: Callback = {
      Callback {
        subscription.foreach(_.kill())
        subscription = None
      }
    }

    def saveDraft: Callback = {
      for {
        props <- scope.props
        state <- scope.state
      } yield {
        taskEventBus.writer.onNext {
          for {
            _ <- ZIOUtils.fromReactCallback {
              scope.modState(_.copy(saveStatus = SaveStatus.Saving))
            }
            response <- saveDraftTask(props, state)
          } yield {
            val saveStatus = response.fold(
              _ => SaveStatus.SaveError,
              _ => SaveStatus.NotSaving
            )
            scope.modState(_.copy(saveStatus = saveStatus, dirty = false))
          }
        }
      }
    }

    private def saveDraftTask(
      props: Props,
      state: State
    ): Task[Either[GeneralServiceException, SaveDraftResponse]] = {

      val opts = for {
        emailId <- props.emailId
        messageId <- props.messageId
      } yield (emailId, messageId)

      opts.fold[Task[Either[GeneralServiceException, SaveDraftResponse]]] {
        ZIO.attempt(Left(GeneralServiceException("token, emailId or messageId is empty")))
      } { case (emailId, messageId) =>
        EmailClient.saveDraft(
          SaveDraftParams(
            emailId = emailId,
            replyingTo = props.replyTo,
            messageId = messageId,
            recipients = state.recipients,
            ccRecipients = state.ccRecipients,
            subject = state.subject,
            plainTextBody = DomPurifyUtils.getRawText(state.editorValue),
            htmlBody = Some(state.editorValue),
            attachments = state.attachments,
            tagMap = props.trxnIdOpt.fold[TagMap](Map.empty) { trxnId =>
              Map(EmailTagKey.TransactionTag -> EmailTagValues(Set(trxnId.idString)))
            }
          )
        )
      }
    }

    private def sendEmailTask(props: Props): Task[Either[GeneralServiceException, SendEmailResponse]] = {
      props.emailId.fold[Task[Either[GeneralServiceException, SendEmailResponse]]] {
        ZIO.attempt(Left(GeneralServiceException("token or emailId is empty")))
      } { emailId =>
        EmailClient.sendEmail(
          SendEmailParams(
            emailId = emailId,
            props.includeTrxnLink
          )
        )
      }
    }

    private def saveAndSend(
      props: Props,
      state: State
    ): Task[Either[GeneralServiceException, SendEmailResponse]] = {
      for {
        saveResult <- saveDraftTask(props, state)
        sendResult <- saveResult.fold(
          error => ZIO.attempt(Left(error)),
          _ => sendEmailTask(props)
        )
      } yield sendResult
    }

    private def onChangeRecipients(recipients: Seq[EmailAddress]) = {
      for {
        state <- scope.state
        _ <- Callback.when(state.recipients != recipients) {
          scope.modState(_.copy(recipients = recipients, dirty = true), saveDraft)
        }
      } yield ()
    }

    private def onChangeCc(cc: Seq[EmailAddress]) = {
      for {
        state <- scope.state
        _ <- Callback.when(state.ccRecipients != cc) {
          scope.modState(_.copy(ccRecipients = cc, dirty = true), saveDraft)
        }
      } yield ()
    }

    private def onChangeSubject(e: ReactEventFromInput) = {
      e.extract(_.target.value) { value =>
        for {
          state <- scope.state
          _ <- Callback.when(value != state.subject) {
            scope.modState(_.copy(subject = value, dirty = true), saveDraft)
          }
        } yield ()
      }
    }

    // Wait until all changes made are saved and then run the task.
    private def waitForChangesToSave(cb: Task[Callback]) = {
      def loop: Callback = {
        for {
          state <- scope.state
          _ <-
            if (state.dirty) {
              setTimeout(loop, 50 milliseconds)
            } else {
              ZIOUtils.toReactCallback(cb)
            }
        } yield ()
      }
      loop
    }

  }

  private val component = ScalaComponent
    .builder[Props](ComponentName)
    .initialStateFromProps { props =>
      State(
        recipients = props.initialRecipients,
        ccRecipients = props.initialCcRecipients,
        subject = props.initialSubject,
        editorValue = TransformerUtil.transformSlateEditorContent(props.initialHtmlBody),
        storedFiles = props.initialAttachments,
        showingCc = props.initialCcRecipients.nonEmpty
      )
    }
    .renderBackend[Backend]
    .componentDidMount { scope =>
      scope.backend.start
    }
    .componentWillUnmount { scope =>
      scope.backend.stop
    }
    .componentDidUpdate { scope =>
      val attachmentChanged = scope.currentState.attachments != scope.prevState.attachments
      Callback.when(attachmentChanged) {
        scope.backend.saveDraft
      }
    }
    .build

}
