// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.client.modules.inbox

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.PortalPosition
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import anduin.file.explorer.FileLoader
import anduin.model.id.FileId
import anduin.scalajs.pluralize.Pluralize
import com.anduin.stargazer.client.component.viewer.fullscreen.FullScreenViewer

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[inbox] final case class EmailAttachments(
  emailAttachments: List[EmailAttachments.EmailAttachment],
  onRemoveAttachment: FileId => Callback
) {
  def apply(): VdomElement = EmailAttachments.component(this)
}

private[inbox] object EmailAttachments {

  private type Props = EmailAttachments

  final case class EmailAttachment(
    fileId: FileId,
    allowRemove: Boolean = true
  )

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

  private def render(props: Props) = {
    val displayAttachments = props.emailAttachments.take(9)
    val remainingAttachments = props.emailAttachments.drop(9)
    <.div(
      tw.flex.flexWrap,
      displayAttachments.toVdomArray(
        using renderFile(props, _)
      ),
      TagMod.when(remainingAttachments.nonEmpty) {
        PopoverR(
          position = PortalPosition.RightBottom,
          renderTarget = (open, _) =>
            <.div(
              tw.ml16.mt4.flex,
              ^.height := "34px",
              Button(
                style = Button.Style.Text(),
                onClick = open
              )(s"+ ${Pluralize(
                  "attachment",
                  remainingAttachments.size,
                  true
                )}")
            ),
          renderContent = _ =>
            <.div(
              tw.px16.py12,
              ^.width := "350px",
              remainingAttachments.toVdomArray(
                using { attachment =>
                  <.div(
                    ^.key := attachment.fileId.idString,
                    TagMod.unless(remainingAttachments.headOption.contains(attachment))(tw.mt8),
                    renderFile(
                      props,
                      attachment,
                      isInPopover = true
                    )
                  )
                }
              )
            )
        )()
      }
    )
  }

  private def renderFile(
    props: Props,
    attachment: EmailAttachment,
    isInPopover: Boolean = false
  ): VdomElement = {
    val fileId = attachment.fileId
    FileLoader(Seq(fileId)) { (resp, _, isLoading) =>
      <.div(
        ComponentUtils.testId(EmailAttachments, "attachmentItem"),
        ^.key := fileId.idString,
        if (isInPopover) {
          tw.wPc100
        } else {
          TagMod(
            tw.m4,
            ^.width := "48%"
          )
        },
        if (isLoading) {
          TagMod(
            ^.cls := "loading-block",
            ^.height := "34px"
          )
        } else {
          resp.data.headOption.fold[TagMod](EmptyVdom) { file =>
            TagMod(
              tw.flex.itemsCenter.px8.py4,
              tw.bgGray1.borderAll.borderGray3,
              IconR(name = Icon.File.ByExtension(file.name), size = Icon.Size.Px24)(),
              <.span(
                tw.ml4.mr8.flexFill.fontSemiBold,
                FullScreenViewer(
                  fileName = file.name,
                  fileId = fileId
                )()
              ),
              TooltipR(
                renderTarget = Button(
                  style = Button.Style.Minimal(
                    icon = Some(Icon.Glyph.Cross),
                    height = Button.Height.Fix24
                  ),
                  isDisabled = !attachment.allowRemove,
                  onClick = props.onRemoveAttachment(fileId)
                )(),
                renderContent = _("Remove attachment")
              )()
            )
          }
        }
      )
    }
  }

}
