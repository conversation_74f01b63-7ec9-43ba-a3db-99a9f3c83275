// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.client.modules.routing

import scala.reflect.TypeTest

import japgolly.scalajs.react.Callback
import japgolly.scalajs.react.extra.router.{BaseUrl, RouterConfigDsl, RouterCtl, StaticDsl}
import urldsl.errors.DummyError
import urldsl.vocabulary.{FromString, Printer}

import anduin.id.ModelIdRegistry
import anduin.id.signature.SignatureRequestId
import anduin.model.id.ModelIdUtils
import anduin.protobuf.page.FundPageTab
import anduin.radix.RadixId
import stargazer.model.routing.Page

object RoutingUtils {

  type Dsl = RouterConfigDsl[Page, Unit]
  type Router = RouterCtl[Page]

  val AlphanumericRegex = "[A-Za-z0-9]+"
  val AlphanumericPeriodRegex = "[A-Za-z0-9.]+"

  def bool(
    using dsl: Dsl
  ): StaticDsl.RouteB[Boolean] = {
    dsl
      .string("[a-z]+")
      .pmap { str =>
        if (str == "true") {
          Some(true)
        } else if (str == "false") {
          Some(false)
        } else {
          None
        }
      } {
        _.toString
      }
  }

  def radixId[I <: RadixId](
    using dsl: Dsl,
    tt: TypeTest[RadixId, I]
  ): StaticDsl.RouteB[I] = {
    dsl
      .string(ModelIdUtils.ModelIdRegex)
      .pmap(idStr => ModelIdRegistry.parser.parseAs[I](idStr))(_.idString)
  }

  // build a baseUrl, this method works for both local and server addresses (assuming you use #)
  val baseUrl: BaseUrl = BaseUrl(CommonRoutingUtils.baseUrl)

  def gotoPageWithoutBrowserHistory(routerCtl: RouterCtl[Page], page: Page): Callback =
    Callback {
      org.scalajs.dom.window.location.replace(routerCtl.urlFor(page).value)
    }

  def signatureRequestId(
    using dsl: RouterConfigDsl[Page, Unit]
  ): StaticDsl.RouteB[SignatureRequestId] = {
    RoutingUtils.radixId[SignatureRequestId]
  }

  // Laminar router utils

  given radixIdPrinter: [I <: RadixId] => (tt: TypeTest[RadixId, I]) => Printer[I] = Printer.factory[I](_.idString)

  given radixIdFromString: [I <: RadixId] => (tt: TypeTest[RadixId, I]) => FromString[I, DummyError] =
    FromString.factory { str =>
      ModelIdRegistry.parser
        .parseAs[I](str)
        .toRight(DummyError.dummyError)
    }

  given FromString[FundPageTab, DummyError] = FromString.factory { str =>
    FundPageTab.values.find(_.name.toLowerCase == str).toRight(DummyError.dummyError)
  }

  given Printer[FundPageTab] = Printer.factory(_.name.toLowerCase)

}
