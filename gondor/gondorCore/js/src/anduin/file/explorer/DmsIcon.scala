// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.file.explorer

import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

final case class DmsIcon(
  iconType: DmsIcon.Type,
  size: Icon.Size = Icon.Size.Px32
) {
  def apply(): VdomElement = DmsIcon.component(this)
}

object DmsIcon {

  sealed trait Type derives CanEqual

  final case class Folder(childCount: Int) extends Type

  final case class File(fileName: String) extends Type

  private type Props = DmsIcon

  private def render(props: Props) = {
    props.iconType match {
      case Folder(childCount) =>
        val color = if (childCount == 0) Icon.Folder.Gray else Icon.Folder.Brown
        IconR(name = Icon.Folder(color = color), size = props.size)()
      case File(fileName) =>
        if (fileName.endsWith("url")) {
          IconR(name = Icon.File.Link, size = props.size)()
        } else {
          IconR(name = Icon.File.ByExtension(fileName), size = props.size)()
        }
    }
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
