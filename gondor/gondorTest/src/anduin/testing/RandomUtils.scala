// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.testing

import java.security.SecureRandom

import org.hashids.Hashids

import anduin.model.common.user.UserId
import anduin.model.id.UserIdFactory

object RandomUtils {

  private val NumericAlphabet: String = ('0' to '9').mkString("")
  private val LowerCaseAlphabet: String = ('a' to 'z').mkString("")
  private val Rand: SecureRandom = new SecureRandom()
  private val maxNumIds = 10000000
  private val DefaultAlphabet: String = LowerCaseAlphabet + NumericAlphabet
  private final def randomValue(): Long = Rand.nextInt(maxNumIds).toLong

  private def random(randomLength: Int): String = {
    Hashids(
      Rand.nextDouble().toString,
      randomLength,
      DefaultAlphabet
    )
      .encode(randomValue())
      .substring(0, randomLength)
  }

  private val EmailDomain = "@anduin.email"

  def generateEmailAddress: String = {
    random(10) + EmailDomain
  }

  def generateRandomUserId: UserId = {
    UserIdFactory.unsafeRandomId
  }

  def generateEmailAddress(length: Int, domain: String): String = {
    random(length) + domain
  }

  def generateRandomString(length: Int): String = {
    random(length)
  }

  // Generate random number between min and max inclusive
  def generateRandomInt(min: Int, max: Int): Int = {
    (randomValue() % (max - min + 1)).toInt + min
  }

}
