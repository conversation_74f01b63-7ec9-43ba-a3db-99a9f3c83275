// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.log.extras

import com.github.plokhotnyuk.jsoniter_scala.core.*
import com.github.plokhotnyuk.jsoniter_scala.macros.*
import scribe.mdc.MDC
import scribe.json.ScribeJsonSupport
import scribe.throwable.{Trace, TraceLoggableMessage}

private case class JsonLogEntry(
  level: String,
  levelValue: Double,
  service: Option[String],
  message: String,
  fileName: String,
  className: String,
  methodName: Option[String],
  line: Option[Int],
  thread: String,
  timestamp: Long,
  stackTrace: Option[String],
  mdc: Map[String, String],
  data: Map[String, String]
)

class ScribeJsoniterSupport(msgFormatter: scribe.format.Formatter) extends ScribeJsonSupport[JsonLogEntry] {

  given jsonCodec: JsonValueCodec[JsonLogEntry] = JsonCodecMaker.make

  def json2String(json: JsonLogEntry): String = {
    writeToString(json)
  }

  def logRecord2Json(record: scribe.LogRecord): JsonLogEntry = {
    val noErrorMessage = record.copy(
      messages = record.messages.filter {
        case _: TraceLoggableMessage => false
        case _                       => true
      }
    )
    val traces = record.messages
      .collect { case trace: TraceLoggableMessage =>
        Trace
          .toLogOutput(scribe.output.EmptyOutput, trace.value)
          .plainText
      }

    JsonLogEntry(
      level = record.level.name,
      levelValue = record.levelValue,
      service = MDC.get("service").map(_.toString),
      message = msgFormatter.format(noErrorMessage).plainText,
      fileName = record.fileName,
      className = record.className,
      methodName = record.methodName,
      line = record.line,
      thread = record.thread.getName,
      timestamp = record.timeStamp,
      stackTrace = Option.when(traces.nonEmpty)(traces.mkString(scribe.lineSeparator)),
      mdc = scribe.mdc.MDC.map.map { case (key, value) =>
        key -> value().toString
      },
      data = record.data.map { case (key, value) =>
        key -> value().toString
      }
    )
  }

}

object ScribeJsoniterSupport {

  private val defaultMsgFormatter: scribe.format.Formatter = {
    import scribe.format.*
    formatter"$messages"
  }

  def apply(msgFormatter: scribe.format.Formatter = defaultMsgFormatter): ScribeJsoniterSupport = {
    new ScribeJsoniterSupport(msgFormatter)
  }

}
