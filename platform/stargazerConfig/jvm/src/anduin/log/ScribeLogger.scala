// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.log

import java.io.File
import java.nio.file.{Files, Paths}

import moduload.Moduload
import scribe.*
import scribe.file.*
import scribe.handler.{<PERSON>g<PERSON><PERSON><PERSON>, LogHandlerBuilder}
import scribe.writer.ConsoleWriter

import anduin.buildinfo.stargazerJvmBuildInfo

object ScribeLogger extends Moduload {

  override def error(t: Throwable): Unit = scribe.error("Error loading ScribeLogger", t)

  private lazy val disableVerboseLogEnvs = Set(
    "Integ",
    "Unit",
    "Development"
  )

  override def load(): Unit = {

    val isDebug = sys.env.get("ANDUIN_LOG_DEBUG").contains("true")

    val isJsonLogging = sys.env.get("ANDUIN_LOG_JSON").contains("true")

    val dailyRolling = rolling("-" % daily("-"))

    val currentDirectory = new java.io.File(".").getCanonicalPath

    val logsFolder = Paths.get(currentDirectory, "logs")
    println(s"using log folder: $logsFolder") // scalafix:ok

    if (!Files.isDirectory(logsFolder)) {
      val isOK = new File(logsFolder.toUri).mkdirs()
      if (!isOK) {
        println(s"cannot create log folder directory: $logsFolder") // scalafix:ok
      }
    }

    val logExt = ".log"

    val mdcFormatter = {
      import scribe.format.*
      formatter"$dateFull [$threadName] $levelColoredPaddedRight ${green(position)} - ${messages}${multiLine(mdcMultiLine)}"
    }
    val jsonFormatter = {
      import scribe.format.*
      formatter"[$threadName] ${level} ${position} - ${messages}"
    }

    val jsonHandleWraper = (h: LogHandlerBuilder) => {
      if (isJsonLogging) {
        h.copy(
          writer = extras
            .ScribeJsoniterSupport(jsonFormatter)
            .writer(h.writer),
          outputFormat = scribe.output.format.ASCIIOutputFormat
        )
      } else {
        h
      }
    }

    val sttpFilter = scribe.filter.className.startsWith("com.anduin.stargazer.service.http") ||
      scribe.filter.className.startsWith("sttp.client3.logging.scribe")

    val disableVerboseLogMods = if (disableVerboseLogEnvs.contains(stargazerJvmBuildInfo.buildEnv)) {
      List(
        scribe.filter.exclude(
          scribe.filter.className.startsWith("anduin.flow"),
          scribe.filter.className.startsWith("anduin.dms"),
          scribe.filter.className.startsWith("io.temporal.serviceclient"),
          scribe.filter.className.startsWith("io.temporal.internal"),
          scribe.filter.className.startsWith("io.temporal.worker"),
          scribe.filter.className.startsWith("com.anduin.stargazer.service.database.syncgateway.SyncGatewayJvmClient")
        )
      )
    } else {
      List.empty
    }

    val appliedLogMods = List(
      scribe.filter.exclude(
        // cron
        scribe.filter.className.startsWith("com.anduin.stargazer.service.cron"),
        // fdb
        scribe.filter.className.startsWith("com.apple.foundationdb"),
        scribe.filter.className.startsWith("anduin.fdb.subspace"),
        // sttp - only include level error and above
        (sttpFilter && scribe.filter.level < Level.Error),
        // role
        scribe.filter.className.startsWith("anduin.role"),
        // kafka
        scribe.filter.className.startsWith("org.apache.kafka"),
        // others
        scribe.filter.className.startsWith("com.zaxxer.hikari"),
        scribe.filter.className.startsWith("org.quartz"),
        scribe.filter.className.startsWith("akka"),
        scribe.filter.className.startsWith("kamon"),
        scribe.filter.className("com.typesafe.akka.extension.quartz.QuartzSchedulerExtension"),
        scribe.filter.className.startsWith("org.flywaydb"),
        scribe.filter.className.startsWith("com.datastax.driver.core"),
        scribe.filter.className.startsWith("com.outworkers.phantom"),
        scribe.filter.className.startsWith("com.linecorp.armeria.internal"),
        scribe.filter.className.startsWith("com.linecorp.armeria.common.Flags"),
        scribe.filter.className.startsWith("com.linecorp.armeria.client.circuitbreaker.NonBlockingCircuitBreaker")
      )
    ) ++ disableVerboseLogMods

    val consoleHandler = jsonHandleWraper(
      LogHandler(
        writer = ConsoleWriter,
        formatter = mdcFormatter,
        modifiers = appliedLogMods,
        minimumLevel = Option(if (isDebug) Level.Debug else Level.Info)
      )
    )

    val fileHandler = jsonHandleWraper(
      consoleHandler.copy(
        writer = FileWriter(logsFolder / ("gondor" % dailyRolling % logExt)).flushNever
      )
    )

    val baseLogger =
      Logger.root
        .clearHandlers()
        .clearModifiers()
        .withHandler(consoleHandler)
        .withMinimumLevel(Level.Debug)

    val enableLogToFile = sys.env.get("ANDUIN_ENABLE_LOG_TO_FILE").contains("true")
    val logger = if (enableLogToFile) {
      baseLogger.withHandler(fileHandler)
    } else {
      baseLogger
    }
    val _ = logger.replace()

    scribe.info("Anduin Scribe logger successfully configured!")

  }

}
