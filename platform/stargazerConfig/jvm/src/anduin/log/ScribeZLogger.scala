// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.log

import scribe.message.LoggableMessage
import zio.*

import anduin.log.ScribeZLogger.logLevelMapping

private[log] final case class ScribeZLogger() extends ZLogger[String, Unit] {

  given CanEqual[Cause[Any], Cause[Nothing]] = CanEqual.derived

  override def apply(
    trace: Trace,
    fiberId: FiberId,
    logLevel: LogLevel,
    message: () => String,
    cause: Cause[Any],
    context: FiberRefs,
    spans: List[LogSpan],
    annotations: Map[String, String]
  ): Unit = {
    val stackTraceOpt = Trace.toJava(trace)
    val messages: List[String] =
      List(message()) ++ Option.when(cause != null && cause != Cause.empty)(cause.prettyPrint) // scalafix:ok
    logLevelMapping.get(logLevel).foreach { scribeLogLevel =>
      val now = scribe.util.Time()
      val data = annotations.map((key, value) => (key, () => value)) ++ Map(
        ("FiberId", () => fiberId.threadName)
      )
      val spanData = if (spans.nonEmpty) {
        Map(
          ("Spans", () => spans.map(_.render(now)).mkString("|"))
        )
      } else {
        Map.empty
      }
      val logRecord = scribe.LogRecord(
        level = scribeLogLevel,
        levelValue = scribeLogLevel.value,
        messages = messages,
        fileName = stackTraceOpt.map(_.getFileName).getOrElse(""),
        className = stackTraceOpt.map(_.getClassName).getOrElse(""),
        methodName = stackTraceOpt.map(_.getMethodName),
        line = stackTraceOpt.map(_.getLineNumber),
        column = None,
        data = data ++ spanData,
        timeStamp = now
      )
      scribe.log(logRecord)
    }
  }

}

object ScribeZLogger {

  private val logLevelMapping: Map[LogLevel, scribe.Level] = Map(
    LogLevel.All -> scribe.Level.Trace,
    LogLevel.Trace -> scribe.Level.Trace,
    LogLevel.Debug -> scribe.Level.Debug,
    LogLevel.Info -> scribe.Level.Info,
    LogLevel.Warning -> scribe.Level.Warn,
    LogLevel.Error -> scribe.Level.Error,
    LogLevel.Fatal -> scribe.Level.Fatal
  )

  val default = ScribeZLogger()

  def make: ZLayer[Any, Nothing, Unit] = Runtime.addLogger(default)

}
