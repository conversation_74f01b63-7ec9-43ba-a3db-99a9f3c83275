// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.config

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.{Duration, FiniteDuration}

import squants.information.Information
import zio.config.magnolia.DeriveConfig

object CommonDeriveConfig {

  given javaDurationDesc: (zioDurationDesc: DeriveConfig[zio.Duration]) => DeriveConfig[Duration] =
    zioDurationDesc.map(zioDuration => Duration(zioDuration.toNanos, TimeUnit.NANOSECONDS))

  given finiteDurationDesc: (zioDurationDesc: DeriveConfig[zio.Duration]) => DeriveConfig[FiniteDuration] =
    zioDurationDesc.map(zioDuration => FiniteDuration.apply(zioDuration.toNanos, TimeUnit.NANOSECONDS))

  given informationDesc: (stringDesc: DeriveConfig[String]) => DeriveConfig[Information] =
    stringDesc.map { s =>
      Information.parseString(s).get
    }

}
