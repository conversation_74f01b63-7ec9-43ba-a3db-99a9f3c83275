// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.utils

import java.net.InetAddress
import java.util.UUID

import anduin.buildinfo.stargazerJvmBuildInfo
import com.anduin.stargazer.apps.stargazer.StargazerSettings

object DeploymentUtils {

  private val backendConfig = StargazerSettings.gondorConfig.backendConfig

  private val multiTenantConfig = StargazerSettings.gondorConfig.commonConfig.multiTenantConfig

  def isProductionMode: Boolean = {
    stargazerJvmBuildInfo.productionMode.toBoolean
  }

  def isCanary: Boolean = {
    isProductionMode && backendConfig.server.deployment.toLowerCase.contains("canary")
  }

  def isInternal: Boolean = {
    isProductionMode && backendConfig.server.deployment.toLowerCase.contains("internal")
  }

  def isDemo: Boolean = {
    isProductionMode && backendConfig.server.deployment.toLowerCase.contains("demo")
  }

  def isDeals: Boolean = {
    isProductionMode && backendConfig.server.deployment.toLowerCase.contains("public")
  }

  def isE2e: Boolean = {
    isProductionMode && backendConfig.server.deployment.toLowerCase.contains("minas")
  }

  def isTestServer: Boolean = {
    !isProductionMode || isCanary || isInternal || isE2e
  }

  def isSimulator: Boolean = {
    isProductionMode && backendConfig.server.deployment.toLowerCase.contains("simulator")
  }

  def isLocal: Boolean = {
    backendConfig.server.deployment.toLowerCase.contains("local")
  }

  def getHostName: Option[String] = {
    Option(System.getenv("DEPLOYMENT_POD_NAME")) orElse {
      Option(System.getenv("COMPUTERNAME")) orElse {
        Option(System.getenv("HOSTNAME")) orElse {
          try {
            Option(InetAddress.getLocalHost.getHostName)
          } catch {
            case _: Throwable => None
          }
        }
      }
    }
  }

  def mustGetHostname: String = getHostName.getOrElse(randomUUID.toString)

  lazy val tenantSuffix: String = {
    val suffix = if (multiTenantConfig.tenantSuffix.nonEmpty) {
      multiTenantConfig.tenantSuffix
    } else if (isLocal) {
      mustGetHostname
    } else {
      ""
    }

    if (suffix.isEmpty) {
      ""
    } else {
      s"__${suffix.stripPrefix("__")}"
    }

  }

  lazy val deploymentName: String = backendConfig.server.deployment
  lazy val appName: String = StargazerSettings.gondorConfig.svcName

  private lazy val randomUUID: UUID = UUID.randomUUID()
}
