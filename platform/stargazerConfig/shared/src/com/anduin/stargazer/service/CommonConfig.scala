// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service

import java.time.Duration
import scala.concurrent.duration.FiniteDuration

import io.circe.Codec

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.model.codec.FiniteDurationCodecs.given
import com.anduin.stargazer.service.CommonConfig.*

final case class CommonConfig(
  s3Config: S3Config,
  pageLoginSessionDuration: PageLoginSessionDuration,
  captchaCommonConfig: CaptchaCommonConfig,
  gdprCommonConfig: GDPRCommonConfig,
  otpAuthenticationCommonConfig: OTPAuthenticationCommonConfig,
  rememberEmailCookieConfig: RememberEmailCookieConfig,
  multiTenantConfig: MultiTenantConfig,
  multiRegionConfig: MultiRegionConfig,
  prismaticConfig: PrismaticConfig,
  documentServiceConfig: DocumentServiceConfig,
  fundSubSchwabSignatureIntegrationConfig: FundSubSchwabSignatureIntegrationConfig,
  cueModuleConfig: CueModuleConfig
)

object CommonConfig {

  given Codec.AsObject[CommonConfig] = deriveCodecWithDefaults

  final case class S3Config(
    linkExpiration: Duration,
    publicApiLinkExpiration: Duration
  )

  object S3Config {
    given Codec.AsObject[S3Config] = deriveCodecWithDefaults
  }

  final case class PageLoginSessionDuration(
    defaultDuration: FiniteDuration
  )

  object PageLoginSessionDuration {
    given Codec.AsObject[PageLoginSessionDuration] = deriveCodecWithDefaults
  }

  final case class CaptchaCommonConfig(
    isEnabled: Boolean,
    bypassDuration: FiniteDuration
  )

  object CaptchaCommonConfig {
    given Codec.AsObject[CaptchaCommonConfig] = deriveCodecWithDefaults
  }

  final case class CookieConsentCommonConfig(
    name: String,
    expires: FiniteDuration
  )

  object CookieConsentCommonConfig {
    given Codec.AsObject[CookieConsentCommonConfig] = deriveCodecWithDefaults
  }

  final case class GDPRCommonConfig(
    cookieConsentCommonConfig: CookieConsentCommonConfig
  )

  object GDPRCommonConfig {
    given Codec.AsObject[GDPRCommonConfig] = deriveCodecWithDefaults
  }

  final case class OTPAuthenticationCommonConfig(
    requestDelay: FiniteDuration,
    codeTimeout: FiniteDuration,
    enableRevertOTP: Boolean,
    revertOTPCodeTimeout: FiniteDuration
  )

  object OTPAuthenticationCommonConfig {
    given Codec.AsObject[OTPAuthenticationCommonConfig] = deriveCodecWithDefaults
  }

  final case class RememberEmailCookieConfig(
    name: String,
    expire: FiniteDuration
  )

  object RememberEmailCookieConfig {
    given Codec.AsObject[RememberEmailCookieConfig] = deriveCodecWithDefaults
  }

  final case class MultiTenantConfig(
    tenantSuffix: String
  )

  object MultiTenantConfig {
    given Codec.AsObject[MultiTenantConfig] = deriveCodecWithDefaults
  }

  final case class MultiRegionConfig(
    isSecondaryRegion: Boolean,
    mainRegionEndpoint: String,
    regionCode: String,
    regionName: String
  )

  object MultiRegionConfig {
    given Codec.AsObject[MultiRegionConfig] = deriveCodecWithDefaults
  }

  final case class PrismaticConfig(
    organizationUrl: String,
    jwtTimeToLive: Duration
  )

  object PrismaticConfig {
    given Codec.AsObject[PrismaticConfig] = deriveCodecWithDefaults
  }

  final case class DocumentServiceConfig(
    maxFileSizeInMbs: Long
  )

  object DocumentServiceConfig {
    given Codec.AsObject[DocumentServiceConfig] = deriveCodecWithDefaults
  }

  final case class FundSubSchwabSignatureIntegrationConfig(
    schwabCarbonCopyEmail: String
  )

  object FundSubSchwabSignatureIntegrationConfig {
    given Codec.AsObject[FundSubSchwabSignatureIntegrationConfig] = deriveCodecWithDefaults
  }

  final case class CueModuleConfig(
    commonDataTypes: CueModulePaths,
    softValidationDataTypes: CueModulePaths,
    tableCommonSchema: CueModulePaths,
    fundsubTypes: CueModulePaths,
    dataextractCommonSchema: CueModulePaths,
    dataLayerCommonSchema: CueModulePaths
  )

  object CueModuleConfig {
    given Codec.AsObject[CueModuleConfig] = deriveCodecWithDefaults
  }

  final case class CueModulePaths(
    modulePath: String,
    packagePath: String
  )

  object CueModulePaths {
    given Codec.AsObject[CueModulePaths] = deriveCodecWithDefaults
  }

}
