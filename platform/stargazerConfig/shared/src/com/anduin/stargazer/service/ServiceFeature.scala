// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service

import anduin.id.servicefeature.ServiceFeatureId

final case class ServiceFeature(gondorConfig: GondorConfig) {

  lazy val features: Set[ServiceFeatureId] = gondorConfig.backendConfig.server.serviceFeatures
    .split(",")
    .flatMap(idString => ServiceFeatureId.values.find(_.value == idString.trim))
    .toSet

  def hasFeature(feature: ServiceFeatureId): Boolean = {
    features.contains(feature)
  }

  def hasServerRunning: Boolean = {
    features.exists(_ != ServiceFeatureId.Initializer)
  }

  def hasSandbox: Boolean = {
    features
      .intersect(
        Set(
          ServiceFeatureId.FundSubSimulator,
          ServiceFeatureId.DataRoomSimulator,
          ServiceFeatureId.LpProfileSimulator,
          ServiceFeatureId.FundDataSimulator
        )
      )
      .nonEmpty
  }

}
