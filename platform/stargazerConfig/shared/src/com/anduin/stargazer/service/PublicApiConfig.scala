// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service

import com.anduin.stargazer.service.PublicApiConfig.CustomIntegrationFlowConfig

import scala.concurrent.duration.FiniteDuration

final case class PublicApiConfig(
  endpointConfig: PublicApiConfig.EndpointConfig,
  syncWorkflowConfig: PublicApiConfig.SyncWorkflowConfig,
  asyncWorkflowConfig: PublicApiConfig.AsyncWorkflowConfig,
  webhookConfig: PublicApiConfig.WebhookConfig,
  customIntegrationFlowConfig: CustomIntegrationFlowConfig
)

object PublicApiConfig {

  final case class EndpointConfig(
    defaultTimeout: FiniteDuration,
    maxPendingRequests: Int,
    commitFileUploadTimeout: FiniteDuration
  )

  final case class SyncWorkflowConfig(
    workflowExecutionDefaultTimeout: FiniteDuration,
    workflowRunDefaultTimeout: FiniteDuration,
    workflowTaskDefaultTimeout: FiniteDuration,
    activityStartToCloseDefaultTimeout: FiniteDuration
  )

  final case class AsyncWorkflowConfig(
    maxConcurrentActivityExecutionSize: Int,
    maxConcurrentTaskExecutionSize: Int,
    workflowExecutionTimeout: FiniteDuration,
    workflowRunTimeout: FiniteDuration,
    workflowTaskTimeout: FiniteDuration,
    activityStartToCloseTimeout: FiniteDuration
  )

  final case class WebhookConfig(
    challengeHeader: String,
    challengeKeyLength: Int,
    requestTimeout: FiniteDuration,
    brokenCounterLimit: Int,
    notificationEmailAddress: String,
    webhookPayloadKafkaTopic: String,
    webhookPayloadAndEndpointKafkaTopic: String,
    webhookIntegrationMessageKafkaTopic: String,
    dataRoomPayloadKafkaTopic: String
  )

  final case class CustomIntegrationFlowConfig(
    endpoint: String,
    apiKey: String
  )

}
