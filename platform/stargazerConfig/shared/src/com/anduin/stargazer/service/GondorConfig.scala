// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service

import scala.concurrent.duration.FiniteDuration

final case class RetryConfig(
  maxCount: Int
)

// FIX: 1. This should aggregate config options exposed by modules, instead of declaring those for them. 2. This should
// be in top-level app, not core.
final case class GondorConfig(
  hostname: String,
  port: Int,
  internalPort: Int,
  endpointHostname: String,
  timeout: FiniteDuration,
  staticWeb: String,
  svcName: String,
  enableMetric: Boolean,
  commonConfig: CommonConfig,
  backendConfig: GondorBackendConfig,
  frontendConfig: GondorFrontendConfig,
  e2eConfig: GondorE2EConfig,
  customerSupport: CustomerSupport,
  publicApiConfig: PublicApiConfig
)
