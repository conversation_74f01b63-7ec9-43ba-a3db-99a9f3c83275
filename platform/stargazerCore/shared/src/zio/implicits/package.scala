// Copyright (C) 2014-2025 Anduin Transactions Inc.

package zio

import scala.concurrent.TimeoutException
import scala.concurrent.duration.FiniteDuration

package object implicits {

  given defaultRuntime: zio.Runtime[Any] = zio.Runtime.default

  extension [A](task: Task[A]) {

    def runToFuture(
      using runtime: zio.Runtime[Any]
    ): CancelableFuture[A] = {
      zio.Unsafe.unsafely {
        runtime.unsafe.runToFuture(task)
      }
    }

    def bracket[B](use: A => Task[B])(release: A => UIO[Unit]): Task[B] = {
      ZIO.acquireReleaseWith(task)(release)(use)
    }

    def bracketE[B](use: A => Task[B])(release: (A, Exit[Throwable, B]) => UIO[Unit]): Task[B] = {
      ZIO.acquireReleaseExitWith(task)(release)(use)
    }

    def timeout(after: FiniteDuration): Task[A] = {
      task.timeoutFail(
        new TimeoutException(s"ZIO effect timed-out after ${after.toSeconds} seconds")
      )(zio.Duration.fromSeconds(after.toSeconds))
    }

  }

  extension [E, A](task: RIO[E, A]) {

    def onErrorHandleWith[B >: A](f: Throwable => RIO[E, B]): RIO[E, B] = {
      task.catchAll(f).catchAllDefect(f)
    }

  }

}
