// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.asyncapiv2

import io.circe.{<PERSON><PERSON><PERSON>, <PERSON><PERSON>}

import anduin.circe.generic.semiauto.CirceCodec
import anduin.model.codec.EitherCodec.given

sealed trait AsyncApiStateData[Err, Resp] {
  def isPending: Boolean
}

object AsyncApiStateData {

  final case class AsyncApiStateCreated[Err, Resp]() extends AsyncApiStateData[Err, Resp] {
    override def isPending: Boolean = true
  }

  final case class AsyncApiStateRunning[Err, Resp]() extends AsyncApiStateData[Err, Resp] {
    override def isPending: Boolean = true
  }

  final case class AsyncApiStateSuccess[Err, Resp](result: Either[Err, Resp]) extends AsyncApiStateData[Err, Resp] {
    override def isPending: Boolean = false
  }

  final case class AsyncApiStateFailed[Err, Resp]() extends AsyncApiStateData[Err, Resp] {
    override def isPending: Boolean = false
  }

  def fromProtocol[Err: Decoder, Resp: Decoder](protocol: Protocol.AsyncApiStateData)
    : Either[Throwable, AsyncApiStateData[Err, Resp]] = {
    protocol match {
      case Protocol.AsyncApiStateCreated() =>
        Right(
          AsyncApiStateCreated[Err, Resp]()
        )
      case Protocol.AsyncApiStateRunning() =>
        Right(
          AsyncApiStateRunning[Err, Resp]()
        )
      case Protocol.AsyncApiStateSuccess(resp) =>
        resp
          .as[Either[Err, Resp]]
          .map(result =>
            AsyncApiStateSuccess(
              result = result
            )
          )
      case Protocol.AsyncApiStateFailed() =>
        Right(
          AsyncApiStateFailed[Err, Resp]()
        )
    }
  }

  object Protocol {
    sealed trait AsyncApiStateData derives CanEqual, CirceCodec.WithDefaultsAndTypeName

    final case class AsyncApiStateCreated() extends AsyncApiStateData derives CirceCodec.WithDefaults

    final case class AsyncApiStateRunning() extends AsyncApiStateData derives CirceCodec.WithDefaults

    final case class AsyncApiStateSuccess(resp: Json) extends AsyncApiStateData derives CirceCodec.WithDefaults

    final case class AsyncApiStateFailed() extends AsyncApiStateData derives CirceCodec.WithDefaults
  }

}
