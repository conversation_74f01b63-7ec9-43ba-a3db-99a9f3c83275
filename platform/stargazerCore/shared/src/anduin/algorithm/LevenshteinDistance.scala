// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.algorithm

object LevenshteinDistance {

  def compare(left: String, right: String): Int = {
    if (left.isEmpty) {
      right.length
    } else if (right.isEmpty) {
      left.length
    } else if (left.length > right.length) {
      compare(right, left)
    } else {
      val p = Array.ofDim[Int](left.length + 1);
      Range.inclusive(0, left.length).foreach { i =>
        p(i) = i
      }

      Range.inclusive(1, right.length).foreach { j =>
        // scalafix:off DisableSyntax.var
        var upperLeft = p(0)
        // scalafix:on
        val rightJ = right.charAt(j - 1) // jth character of right
        p(0) = j

        Range.inclusive(1, left.length).foreach { i =>
          val upper = p(i)
          val cost = if (left.charAt(i - 1) == rightJ) 0 else 1
          // minimum of cell to the left+1, to the top+1, diagonally left and up +cost
          p(i) = Math.min(Math.min(p(i - 1) + 1, p(i) + 1), upperLeft + cost)
          upperLeft = upper
        }
      }

      p(left.length)
    }
  }

}
