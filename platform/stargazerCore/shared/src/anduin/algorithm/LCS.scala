// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.algorithm

import cats.data.Chain

import scala.annotation.tailrec

object LCS {

  trait Diffable[A] {
    def computeSimilarity(x: A, y: A): Double
    def isSame(x: A, y: A): Bo<PERSON>an
  }

  final case class Matching[A](lhs: Option[A], rhs: Option[A], isSame: Boolean = false)

  final case class Score[A](
    score: Double,
    matching: Matching[A],
    posX: Int,
    posY: Int,
    prevX: Int,
    prevY: Int
  ) {

    def add(newScore: Double, matching: Matching[A], newPosX: Int, newPosY: Int): Score[A] = {
      Score(
        score + newScore,
        matching,
        newPosX,
        newPosY,
        posX,
        posY
      )
    }

  }

  object Score {
    given ordering: [B <: Score[?]] => Ordering[B] = (x: B, y: B) => x.score.compareTo(y.score)
  }

  def computeMatching[A](
    xs: Vector[A],
    ys: Vector[A]
  )(
    using diffable: Diffable[A]
  ): Seq[Matching[A]] = {
    val cache = Array.ofDim[Score[A]](xs.size + 1, ys.size + 1)

    @inline def getScore(oldLength: Int, newLength: Int): Score[A] = cache(oldLength)(newLength)

    for {
      oldLength <- 0 to xs.size
      newLength <- 0 to ys.size
    } yield {
      val result = if (oldLength == 0 && newLength == 0) {
        Score[A](
          score = 0.0,
          matching = Matching(None, None),
          0,
          0,
          -1,
          -1
        )
      } else if (oldLength == 0) {
        Score[A](
          score = 0.0,
          matching = Matching(None, Some(ys(newLength - 1))),
          oldLength,
          newLength,
          0,
          newLength - 1
        )
      } else if (newLength == 0) {
        Score[A](
          score = 0.0,
          matching = Matching(Some(xs(oldLength - 1)), None),
          oldLength,
          newLength,
          oldLength - 1,
          0
        )
      } else {
        val oldSection = xs(oldLength - 1)
        val newSection = ys(newLength - 1)

        val sc1 = getScore(oldLength, newLength - 1)
        val sc2 = getScore(oldLength - 1, newLength)
        val sc3 = getScore(oldLength - 1, newLength - 1)

        val similarity = diffable.computeSimilarity(oldSection, newSection)

        val v1 = sc1.score
        val v2 = sc2.score
        val v3 = sc3.score + similarity
        if (v1 > v2) {
          if (v1 >= v3) {
            sc1.add(
              0,
              Matching(None, Some(newSection)),
              oldLength,
              newLength
            )
          } else {
            sc3.add(
              similarity,
              Matching(
                Some(oldSection),
                Some(newSection),
                diffable.isSame(oldSection, newSection)
              ),
              oldLength,
              newLength
            )
          }
        } else {
          if (v2 >= v3) {
            sc2.add(
              0,
              Matching(Some(oldSection), None),
              oldLength,
              newLength
            )
          } else {
            sc3.add(
              similarity,
              Matching(
                Some(oldSection),
                Some(newSection),
                diffable.isSame(oldSection, newSection)
              ),
              oldLength,
              newLength
            )
          }
        }
      }

      cache(oldLength)(newLength) = result
    }

    @tailrec
    def collect(score: Score[A], curMatching: Chain[Matching[A]]): Chain[Matching[A]] = {
      if (score.posX == 0 && score.posY == 0) {
        curMatching
      } else {
        val prevScore = getScore(score.prevX, score.prevY)
        val newMatching = score.matching +: curMatching
        collect(prevScore, newMatching)
      }
    }
    collect(getScore(xs.size, ys.size), Chain.empty).toList
  }

}
