// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.service

import io.circe.Codec
import anduin.circe.generic.semiauto.deriveCodecWithDefaults

import anduin.exception.BaseException

abstract class ServiceException extends BaseException

sealed trait ErrorCode derives CanEqual

case object Undefined extends ErrorCode

object ErrorCode {

  given Codec.AsObject[ErrorCode] = deriveCodecWithDefaults
  given Codec.AsObject[Undefined.type] = deriveCodecWithDefaults
}

final case class GeneralServiceException(message: String, errorCode: ErrorCode = Undefined) extends ServiceException
    derives CanEqual {
  override def toString: String = message
  override def getMessage: String = message
}

object GeneralServiceException {
  given Codec.AsObject[GeneralServiceException] = deriveCodecWithDefaults
}
