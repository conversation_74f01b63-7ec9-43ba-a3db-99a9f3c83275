// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.service

import io.circe.Codec

import anduin.circe.generic.semiauto.{deriveCodecWithDefaults, deriveEnumCodec}

abstract class ServiceResponse

enum ServiceResponseCode {
  case Ok, NotOk, NonExistingId, MarkedAsDeleted, NoPermission
}

object ServiceResponseCode {
  given Codec[ServiceResponseCode] = deriveEnumCodec
}

final case class GeneralServiceResponse(message: String) extends ServiceResponse

object GeneralServiceResponse {
  given Codec.AsObject[GeneralServiceResponse] = deriveCodecWithDefaults
}

final case class CommonResponse(ok: Boolean, message: String)

object CommonResponse {
  given Codec.AsObject[CommonResponse] = deriveCodecWithDefaults
}

final case class CommonCodeResponse(responseCode: ServiceResponseCode, message: String)

object CommonCodeResponse {
  given Codec.AsObject[CommonCodeResponse] = deriveCodecWithDefaults
}
