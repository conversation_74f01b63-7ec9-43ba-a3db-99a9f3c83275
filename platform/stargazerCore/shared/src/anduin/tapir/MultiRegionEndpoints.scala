// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.tapir

import sttp.tapir.*
import scala.reflect.{ClassTag, Typeable}
import scala.compiletime.asMatchable

import io.circe.{Decoder, Encoder}
import sttp.model.StatusCode

import anduin.tapir.AnduinEndpoints.circeCodec
import anduin.tapir.MultiRegionEndpoints.*
import anduin.utils.{ScalaUtils, TypeDescriptor}

abstract class MultiRegionEndpoints {

  inline def multiRegionEndpoint[
    I: {Encoder, Decoder, ClassTag},
    E: {Encoder, Decoder, Typeable, ClassTag},
    O: {Encoder, Decoder, ClassTag}
  ](
    path: EndpointInput[Unit]
  ): BaseMultiRegionEndpoint[I, E, O] = {
    BaseMultiRegionEndpoint(
      multiRegionEndpointImpl(path),
      ScalaUtils.explainType[I],
      ScalaUtils.explainType[E],
      ScalaUtils.explainType[O]
    )
  }

  private def multiRegionEndpointImpl[
    I: {Encoder, Decoder, ClassTag},
    E: {Encoder, Decoder, Typeable, ClassTag},
    O: {Encoder, Decoder, ClassTag}
  ](
    path: EndpointInput[Unit]
  ) = {
    AnduinEndpoints.baseEndpoint.post
      .in(path)
      .in(customCodecJsonBody[I])
      .out(customCodecJsonBody[O])
      .errorOut(
        oneOf[MultiRegionEndpointError[E]](
          oneOfVariantFromMatchType(StatusCode.Forbidden, stringBody.mapTo[Unauthorized]),
          oneOfVariantValueMatcher(StatusCode.InternalServerError, customCodecJsonBody[E].map(ServiceError(_))(_.error)) {
            case x: Any =>
              x.asMatchable match {
                case ServiceError(_: E) => true
              }
          }
        )
      )
      .securityIn(auth.bearer[String]().map(BearerToken.apply)(_.token))
  }

}

object MultiRegionEndpoints {
  sealed trait MultiRegionEndpointError[-E] // scalafix:ok DisableSyntax.contravariant

  case class Unauthorized(message: String) extends MultiRegionEndpointError[Any]

  case class ServiceError[E](error: E) extends MultiRegionEndpointError[E]

  final case class BaseMultiRegionEndpoint[Params, Err, Resp](
    endpoint: RawBaseMultiRegionEndpoint[Params, Err, Resp],
    input: TypeDescriptor,
    error: TypeDescriptor,
    output: TypeDescriptor
  ) extends BaseEndpoint[BearerToken, Params, MultiRegionEndpointError[Err], Resp, Any]

  type RawBaseMultiRegionEndpoint[Params, Err, Resp] =
    Endpoint[BearerToken, Params, MultiRegionEndpointError[Err], Resp, Any]

  final case class BearerToken(token: String) extends AnyVal
}
