// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.tapir

import anduin.id.ModelIdRegistry
import anduin.radix.RadixId

import scala.reflect.TypeTest

import sttp.tapir.*

object RadixIdTapirCodecs {

  def decode[T <: RadixId](
    idStr: String
  )(
    using TypeTest[RadixId, T]
  ): DecodeResult[T] = ModelIdRegistry.parser
    .parseAs[T](idStr)
    .fold[DecodeResult[T]] {
      DecodeResult.Error(idStr, new RuntimeException("Invalid ID"))
    } { id =>
      DecodeResult.Value(id)
    }

  def encode[T <: RadixId](id: T): String = id.idString

  given radixIdCodec: [T <: RadixId] => (TypeTest[RadixId, T]) => Codec[String, T, CodecFormat.TextPlain] =
    Codec.string.mapDecode(
      decode[T]
    )(encode[T])

}
