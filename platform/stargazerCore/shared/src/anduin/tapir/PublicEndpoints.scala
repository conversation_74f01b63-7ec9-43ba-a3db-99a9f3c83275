// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.tapir

import scala.reflect.ClassTag

import anduin.tapir.PublicEndpoints.BasePublicEndpoint
import io.circe.{Decoder, Encoder}

import anduin.tapir.AnduinEndpoints.circeCodec
import sttp.tapir.*

import anduin.utils.{ScalaUtils, TypeDescriptor}

abstract class PublicEndpoints {

  inline def publicEndpoint[
    I: {Encoder, Decoder, ClassTag},
    E: {Encoder, Decoder, ClassTag},
    O: {Encoder, Decoder, ClassTag}
  ](
    path: EndpointInput[Unit]
  ): BasePublicEndpoint[I, E, O] = {
    BasePublicEndpoint(
      publicEndpointImpl(path),
      ScalaUtils.explainType[I],
      ScalaUtils.explainType[E],
      ScalaUtils.explainType[O]
    )
  }

  private def publicEndpointImpl[
    I: {Encoder, Decoder, ClassTag},
    E: {Encoder, Decoder, ClassTag},
    O: {Encoder, Decoder, ClassTag}
  ](
    path: EndpointInput[Unit]
  ) = {
    AnduinEndpoints.baseEndpoint.post
      .in(path)
      .in(customCodecJsonBody[I])
      .out(customCodecJsonBody[O])
      .errorOut(customCodecJsonBody[E])
  }

}

object PublicEndpoints {

  final case class BasePublicEndpoint[Params, Error, Resp](
    endpoint: PublicEndpoint[Params, Error, Resp, Any],
    input: TypeDescriptor,
    error: TypeDescriptor,
    output: TypeDescriptor
  ) extends BaseEndpoint[Unit, Params, Error, Resp, Any]

}
