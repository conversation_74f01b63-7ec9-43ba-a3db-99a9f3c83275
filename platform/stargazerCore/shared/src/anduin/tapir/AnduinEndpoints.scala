// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.tapir

import scala.reflect.ClassTag

import cats.data.Validated
import io.circe.syntax.EncoderOps
import io.circe.{CursorOp, Decoder, DecodingFailure, Encoder, Errors, ParsingFailure, Printer}
import sttp.tapir.Codec.JsonCodec
import sttp.tapir.DecodeResult.{Error, Value}
import sttp.tapir.DecodeResult.Error.{JsonDecodeException, JsonError}
import sttp.tapir.*

object AnduinEndpoints {

  private[tapir] val baseEndpoint: PublicEndpoint[Unit, Unit, Unit, Any] = endpoint.in("api" / "v3")

  given circeCodec: [T: {Encoder, Decoder, ClassTag}] => JsonCodec[T] = {
    given schema: Schema[T] = Schema.schemaForString.as[T]

    sttp.tapir.Codec.json[T] { s =>
      io.circe.parser.decodeAccumulating[T](s) match {
        case Validated.Valid(v) => Value(v)
        case Validated.Invalid(circeFailures) =>
          val tapirJsonErrors = circeFailures.map {
            case ParsingFailure(msg, _) => JsonError(msg, path = List.empty)
            case failure: DecodingFailure =>
              val path = CursorOp.opsToPath(failure.history)
              val fields = path.split("\\.").toList.filter(_.nonEmpty).map(FieldName.apply)
              JsonError(failure.message, fields)
          }

          Error(
            original = s,
            error = JsonDecodeException(
              errors = tapirJsonErrors.toList,
              underlying = Errors(circeFailures)
            )
          )
      }
    } { t =>
      Printer.noSpaces.print(t.asJson)
    }
  }

}
