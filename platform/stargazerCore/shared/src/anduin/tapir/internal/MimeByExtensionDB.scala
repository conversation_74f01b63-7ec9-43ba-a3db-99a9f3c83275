// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.tapir.internal

import scala.io.Source

import sttp.model.MediaType

object MimeByExtensionDB {

  private val mimeTypes: Map[String, MediaType] = {
    val s = Source.fromURL(getClass.getResource("/mimeByExtensions.txt"))
    val pairs =
      try
        s
          .getLines()
          .toList
          .flatMap(line =>
            line.split(" ", 2) match {
              case Array(ext, mimeType) => MediaType.parse(mimeType).map(ext -> _).toOption.toList
              case _                    => Nil
            }
          )
      finally s.close()

    Map(pairs*)
  }

  def apply(extension: String): Option[MediaType] = mimeTypes.get(extension.toLowerCase)
}
