// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.tapir

import scala.reflect.{ClassTag, Typeable}
import scala.compiletime.asMatchable

import io.circe.{Decoder, Encoder}
import sttp.model.StatusCode

import anduin.tapir.AnduinEndpoints.circeCodec
import sttp.tapir.*

import anduin.circe.generic.semiauto.CirceCodec
import anduin.tapir.AuthenticatedEndpoints.*
import anduin.utils.{ScalaUtils, TypeDescriptor}

abstract class AuthenticatedEndpoints {

  inline def authEndpoint[
    I: {Encoder, Decoder, ClassTag},
    E: {Encoder, Decoder, Typeable, ClassTag},
    O: {Encoder, Decoder, ClassTag}
  ](
    path: EndpointInput[Unit]
  ): BaseAuthenticatedEndpoint[I, E, O] = {
    BaseAuthenticatedEndpoint(
      authEndpointImpl(path),
      ScalaUtils.explainType[I],
      ScalaUtils.explainType[E],
      ScalaUtils.explainType[O]
    )
  }

  private def authEndpointImpl[
    I: {Encoder, Decoder, ClassTag},
    E: {Encoder, Decoder, Typeable, ClassTag},
    O: {Encoder, Decoder, ClassTag}
  ](
    path: EndpointInput[Unit]
  ) = {
    AnduinEndpoints.baseEndpoint.post
      .in(path)
      .in(customCodecJsonBody[I])
      .out(customCodecJsonBody[O])
      .errorOut(
        oneOf[AuthenticatedEndpointError[E]](
          oneOfVariantFromMatchType(StatusCode.Forbidden, stringBody.mapTo[Unauthorized]),
          oneOfVariantValueMatcher(StatusCode.InternalServerError, customCodecJsonBody[E].map(ServiceError(_))(_.error)) {
            _.asMatchable match {
              case ServiceError(_: E) => true
            }
          }
        )
      )
      .securityIn(auth.bearer[String]().map(BearerToken.apply)(_.token))
  }

  def successAuthEndpoint[I: {Encoder, Decoder, ClassTag}, O: {Encoder, Decoder, ClassTag}](
    path: EndpointInput[Unit]
  ): BaseAuthenticatedEndpoint[I, Unit, O] = {
    authEndpoint[I, Unit, O](path)
  }

}

object AuthenticatedEndpoints {

  sealed trait AuthenticatedEndpointError[-E] // scalafix:ok DisableSyntax.contravariant
  case class Unauthorized(message: String) extends AuthenticatedEndpointError[Any]
  case class ServiceError[E](error: E) extends AuthenticatedEndpointError[E]

  final case class UnauthorizedResponse(
    anduinCode: Int,
    error: String
  ) derives CirceCodec.WithDefaults

  final case class BaseAuthenticatedEndpoint[Params, Err, Resp](
    endpoint: RawBaseAuthenticatedEndpoint[Params, Err, Resp],
    input: TypeDescriptor,
    error: TypeDescriptor,
    output: TypeDescriptor
  ) extends BaseEndpoint[BearerToken, Params, AuthenticatedEndpointError[Err], Resp, Any]

  type RawBaseAuthenticatedEndpoint[Params, Err, Resp] =
    Endpoint[BearerToken, Params, AuthenticatedEndpointError[Err], Resp, Any]

  type SuccessAuthenticatedEndpoint[Params, Resp] = BaseAuthenticatedEndpoint[Params, Unit, Resp]

  final case class BearerToken(token: String) extends AnyVal

}
