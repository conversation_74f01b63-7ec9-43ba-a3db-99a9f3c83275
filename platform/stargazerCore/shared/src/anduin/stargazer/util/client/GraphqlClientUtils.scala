// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.stargazer.util.client

import java.time.{Instant, ZonedDateTime}

import anduin.stargazer.util.date.ZonedDateTimeUtil
import io.circe.Decoder.Result
import scalapb.{GeneratedEnum, GeneratedEnumCompanion, UnknownFieldSet}
import scala.util.Try

import io.circe.*

object GraphqlClientUtils {

  object ProtoEnumCodecs {

    given protoEnumEncoder: [A <: GeneratedEnum] => Encoder[A] = { (a: A) =>
      Json.fromString(a.name)
    }

    given protoEnumDecoder: [A <: GeneratedEnum] => (companion: GeneratedEnumCompanion[A]) => Decoder[A] = {
      (c: HCursor) =>
        c.value.asString
          .flatMap(companion.fromName)
          .fold[Result[A]] {
            Left(DecodingFailure("Failed to decode enum type", ops = List.empty))
          } { a =>
            Right(a)
          }
    }

  }

  object IgnoreUnknownFieldSetCodecs {

    given unknownFieldSetEncoder: Encoder[UnknownFieldSet] = Encoder.instance(_ => Json.Null)

    given unknownFieldSetDecoder: Decoder[UnknownFieldSet] = Decoder.const(UnknownFieldSet())
  }

  object ZonedDateTimeCodecs {

    given zonedDateTimeEncoder: Encoder[ZonedDateTime] = { (time: ZonedDateTime) =>
      Json.fromString(time.toString)
    }

    given zonedDateTimeDecoder: Decoder[ZonedDateTime] = { (c: HCursor) =>
      c.value.asString
        .flatMap(ZonedDateTimeUtil.fromString)
        .fold[Result[ZonedDateTime]] {
          Left(DecodingFailure("Failed to decode zoned date time", ops = List.empty))
        } { result =>
          Right(result)
        }
    }

  }

  object InstantCodecs {

    given instantEncoder: Encoder[Instant] = { (time: Instant) =>
      Json.fromString(time.toString)
    }

    private def instantFromString(str: String) = {
      Try(Instant.parse(str)).toOption
    }

    given instantDecoder: Decoder[Instant] = { (c: HCursor) =>
      c.value.asString
        .flatMap(instantFromString)
        .fold[Result[Instant]] {
          Left(DecodingFailure("Failed to decode instant", ops = List.empty))
        } { result =>
          Right(result)
        }
    }

  }

}
