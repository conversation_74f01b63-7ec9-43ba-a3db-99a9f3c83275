// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.circe

import io.circe.{Decoder, Encoder}

import anduin.circe.ProtoIsoCodec.*
import anduin.optics.iso.utils.DateTimeIsoInstances.*
import java.time.*

object DateTimeCodec {

  given localDateEncoder: Encoder[LocalDate] = encoderFromIso(localDateIso)
  given localDateDecoder: Decoder[LocalDate] = decoderFromIso(localDateIso)

  given localDateTimeEncoder: Encoder[LocalDateTime] = encoderFromIso(localDateTimeIso)
  given localDateTimeDecoder: Decoder[LocalDateTime] = decoderFromIso(localDateTimeIso)

  given zoneIdEncoder: Encoder[ZoneId] = encoderFromIso(zoneIdIso)
  given zoneIdDecoder: Decoder[ZoneId] = decoderFromIso(zoneIdIso)

  given zonedDateTimeEncoder: Encoder[ZonedDateTime] = encoderFromIso(zonedDateTimeIso)
  given zonedDateTimeDecoder: Decoder[ZonedDateTime] = decoderFromIso(zonedDateTimeIso)

  given durationEncoder: Encoder[Duration] = encoderFromIso(durationIso)
  given durationDecoder: Decoder[Duration] = decoderFromIso(durationIso)

  given instantEncoder: Encoder[Instant] = encoderFromIso(instantIso)
  given instantDecoder: Decoder[Instant] = decoderFromIso(instantIso)
}
