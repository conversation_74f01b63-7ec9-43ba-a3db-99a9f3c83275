// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.circe

import io.circe.{Decoder, Encoder}
import monocle.Iso
import scalapb_circe.JsonFormat.{protoToDecoder, protoToEncoder}

import scalapb.*

object ProtoIsoCodec {

  def encoderFromIso[A, B <: GeneratedMessage](iso: Iso[A, B]): Encoder[A] = {
    protoToEncoder.contramap(iso.get)
  }

  def decoderFromIso[A, B <: GeneratedMessage: GeneratedMessageCompanion](
    iso: Iso[A, B]
  ): Decoder[A] = {
    protoToDecoder.map(iso.reverseGet)
  }

}
