// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.util

import java.text.{DecimalFormat, DecimalFormatSymbols}
import java.util.Locale

import squants.market.*

import anduin.protobuf.external.squants.{CurrencyMessage, MoneyMessage}

object CurrencyUtils {

  private val ThousandGroupSeparator = ','

  val ZeroMoneyString: String = "0"

  val SupportedCurrencies: Seq[Currency] = Seq(
    // Prioritized
    USD,
    EUR,
    GBP,
    CAD,
    // Sort by alphabetical
    AUD,
    CHF,
    CNY,
    HKD,
    JPY,
    NZD
  )

  val SupportedCurrencyMessages: Seq[CurrencyMessage] = SupportedCurrencies.flatMap { currency =>
    CurrencyMessage.values.find(_.name == currency.code)
  }

  val MoneyFormat = "#"
  val MoneyFormat2 = "#,##0.##"

  private val decimalFormatter = new DecimalFormat(MoneyFormat, DecimalFormatSymbols.getInstance(Locale.US))
  decimalFormatter.setMaximumFractionDigits(20)

  private val decimalFormatter2 = new DecimalFormat(MoneyFormat2, DecimalFormatSymbols.getInstance(Locale.US))

  def getCurrencySymbol(currency: CurrencyMessage): String = {
    SupportedCurrencies.find(_.code == currency.name).map(_.symbol).getOrElse("")
  }

  def formatDecimalValueString(str: String): String = {
    str.toLongOption.fold {
      str.toDoubleOption.fold(str) { decimalValue =>
        decimalFormatter.format(decimalValue)
      }
    } { long =>
      long.toString
    }
  }

  def convertMoneyStringToFloatString(moneyStr: String, emptyMoneyValueString: String = ZeroMoneyString): String = {
    val charsToFilter = SupportedCurrencies.map(_.symbol).mkString("") + ThousandGroupSeparator
    val filteredMoneySymbol = moneyStr.trim.filterNot(charsToFilter.contains(_))
    if (filteredMoneySymbol.isEmpty) emptyMoneyValueString else formatDecimalValueString(filteredMoneySymbol)
  }

  def convertMoneyStringToMoneyMessage(
    moneyStr: String,
    currencyOpt: Option[CurrencyMessage] = None
  ): MoneyMessage = {
    val currency = currencyOpt
      .orElse {
        val supportedCurrencyOpt = SupportedCurrencies.find(currency => moneyStr.contains(currency.symbol))
        CurrencyMessage.values.find { currencyMessage =>
          supportedCurrencyOpt.exists(_.code == currencyMessage.name)
        }
      }
      .getOrElse(CurrencyMessage.USD)

    MoneyMessage(
      unit = currency,
      value = convertMoneyStringToFloatString(moneyStr)
    )
  }

  def convertMoneyMessageToMoneyString(money: MoneyMessage): String = {
    getCurrencySymbol(money.unit) + formatDecimalValueString(money.value)
  }

  /** `includeThousandGroupSeparator = true` might cause Excel to treat the data as a string instead of a number. */
  def convertToMoneyString(
    value: Double,
    currency: CurrencyMessage,
    includeThousandGroupSeparator: Boolean = true
  ): String = {
    val formattedValue = if (includeThousandGroupSeparator) {
      decimalFormatter2.format(value)
    } else {
      decimalFormatter.format(value)
    }
    val maybeSpace = currency match {
      case CurrencyMessage.CHF => " "
      case _                   => ""
    }
    s"${getCurrencySymbol(currency)}$maybeSpace$formattedValue"
  }

  def convertToCurrency(currencyMessage: CurrencyMessage): Option[Currency] = {
    SupportedCurrencies.find(_.code == currencyMessage.name)
  }

  def convertToCurrencyMessage(currency: Currency): Option[CurrencyMessage] = {
    CurrencyMessage.values.find(_.name == currency.code)
  }

}
