// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.util

import java.nio.file.Paths
import scala.util.Try
import scala.util.matching.Regex

import anduin.util.FilenameUtils.Extension

object FilenameUtils {

  object Extension {

    val Pdf = "pdf"

    val TextFileExtensions: Seq[String] = Seq(
      "rtf",
      "txt",
      "odt"
    )

    // Should be .doc, .docm, .docx, .dot, .dotm, .dotx, .epub, .fodt,
    // .htm, .html, .mht, .odt, .ott, .pdf, .rtf, .txt, .djvu, .xps
    val MsWordExtensions: Seq[String] = Seq(
      "doc",
      "docm",
      "docx"
    )

    // Should be .csv, .fods, .ods, .ots, .xls, .xlsm, .xlsx, .xlt, .xltm, .xltx
    val MsExcelExtensions: Seq[String] = Seq("xls", "xlsx")

    val MsPowerpointExtensions: Seq[String] = {
      Seq(
        "fodp",
        "odp",
        "otp",
        "pot",
        "potm",
        "potx",
        "pps",
        "ppsm",
        "ppsx",
        "ppt",
        "pptm",
        "pptx"
      )
    }

    val Jpeg = "jpeg"
    val Jpg = "jpg"
    val Png = "png"

    val SignaturesSupportedImageExtensions: Seq[String] = Seq(
      Jpeg,
      Jpg,
      Png
    )

    val ImageExtensions: Seq[String] = Seq(
      "gif",
      "jpeg",
      "jpg",
      "png",
      "svg",
      "webp",
      "bmp",
      "ico"
    )

    val VideoExtensions: Seq[String] = Seq(
      "mov",
      "mp4",
      "ogv",
      "webm"
    )

    val Csv = "csv"

    val AudioExtensions: Seq[String] = Seq(
      "mp3",
      "wav",
      "ogg",
      "oga"
    )

    val Htm = "htm"
    val Html = "html"
    val InternetFileExtensions: Seq[String] = Seq(Htm, Html)

    val TarGz = "tar.gz"
    val TarBz = "tar.bz2"
    val CompressedFileExtensions: Seq[String] = Seq(TarGz, TarBz)

    val PreviewedByPdfExtensions: Seq[String] = Seq(Pdf) ++
      MsWordExtensions ++
      MsPowerpointExtensions ++
      InternetFileExtensions ++
      TextFileExtensions

    // extensions that can be converted to pdf using LibreOffice
    val PdfConvertibleExtensions: Seq[String] = MsWordExtensions ++
      InternetFileExtensions ++
      TextFileExtensions ++
      MsExcelExtensions ++
      MsPowerpointExtensions

    val PdfAndPdfConvertibleExtensions: Seq[String] = Seq(Pdf) ++ PdfConvertibleExtensions

    val UrlExtensions: Seq[String] = Seq("url")

    val Mp4: String = "mp4"

    val Json: String = "json"

    val Cue: String = "cue"

    val AllFileTypes: Seq[Seq[String]] = Seq(
      TextFileExtensions,
      MsWordExtensions,
      MsExcelExtensions,
      MsExcelExtensions,
      ImageExtensions,
      VideoExtensions,
      AudioExtensions,
      InternetFileExtensions,
      CompressedFileExtensions
    )

  }

  def getFileName(path: String): String = {
    Paths.get(path).getFileName.toString
  }

  def getParentFolderName(path: String): Option[String] = {
    Try(Paths.get(path).getParent.getFileName.toString).toOption
  }

  def getName(filename: String): String = {
    Filename.unapply(filename).basename
  }

  def getExtension(filename: String): Option[String] = {
    Filename.unapply(filename).extension
  }

  def getNameAndExtension(fileName: String): (String, Option[String]) = {
    val file = Filename.unapply(fileName)
    file.basename -> file.extension
  }

  // Example: appendSuffix("term-sheet.pdf", "-signed") -> "term-sheet-signed.pdf"
  def appendSuffix(filenameString: String, suffix: String): String = {
    val filename = Filename.unapply(filenameString)
    filename.copy(basename = filename.basename + suffix).fullName
  }

  // This should only be used for handling file name on backend
  // Please read the implementation if you want to customize `maxCharacter` and `nameSuffixLength`
  // Example: truncate("right-of-first-refusal-version-2.pdf") -> "right-of...ion-2.pdf"
  def truncate(
    fileNameString: String,
    maxCharacter: Int = 20,
    nameSuffixLength: Int = 5
  ): String = {
    val filename = Filename.unapply(fileNameString)
    if (maxCharacter < 20 || fileNameString.length <= maxCharacter) {
      fileNameString
    } else {
      val extensionLength = filename.extension.map(_.length + 1).getOrElse(0)
      val namePrefixLength = maxCharacter - extensionLength - nameSuffixLength - 3
      if (namePrefixLength <= 0) {
        fileNameString
      } else {
        val prefix = filename.basename.take(namePrefixLength)
        val suffix = filename.basename.takeRight(nameSuffixLength)
        filename.copy(basename = s"$prefix...$suffix").fullName
      }
    }
  }

  def isMsWordFile(filename: String): Boolean = {
    getExtension(filename).exists(ext => Extension.MsWordExtensions.contains(ext.toLowerCase))
  }

  def isPdfFile(filename: String): Boolean = {
    getExtension(filename).exists(ext => ext.toLowerCase == Extension.Pdf)
  }

  def isMsExcelFile(filename: String): Boolean = {
    getExtension(filename).exists(ext => Extension.MsExcelExtensions.contains(ext.toLowerCase))
  }

  def isMsPowerpointFile(filename: String): Boolean = {
    getExtension(filename).exists(ext => Extension.MsPowerpointExtensions.contains(ext.toLowerCase))
  }

  def isMsOfficeFile(filename: String): Boolean = {
    isMsWordFile(filename) || isMsExcelFile(filename) || isMsPowerpointFile(filename)
  }

  def isPdfConvertible(filename: String): Boolean = {
    getExtension(filename).exists(ext => Extension.PdfConvertibleExtensions.contains(ext.toLowerCase))
  }

  def isPdfOrTextToPdfConvertible(filename: String): Boolean = {
    getExtension(filename).exists(ext => Extension.PdfAndPdfConvertibleExtensions.contains(ext.toLowerCase))
  }

  def isPreviewedByPdf(filename: String): Boolean = {
    getExtension(filename).exists(ext => Extension.PreviewedByPdfExtensions.contains(ext.toLowerCase))
  }

  def isMp4File(filename: String): Boolean = {
    getExtension(filename).exists(_.toLowerCase == Extension.Mp4)
  }

  def isTrackingSupported(filename: String): Boolean = {
    isPreviewedByPdf(filename) || isMp4File(filename)
  }

  def isUrlFile(filename: String): Boolean = {
    getExtension(filename).exists(ext => Extension.UrlExtensions.contains(ext.toLowerCase))
  }

  def isSignaturesSupportedImageFile(filename: String): Boolean = {
    getExtension(filename).exists(ext => Extension.SignaturesSupportedImageExtensions.contains(ext.toLowerCase))
  }

  def isImageFile(filename: String): Boolean = {
    getExtension(filename).exists(ext => Extension.ImageExtensions.contains(ext.toLowerCase))
  }

  def isVideoFile(filename: String): Boolean = {
    getExtension(filename).exists(ext => Extension.VideoExtensions.contains(ext.toLowerCase))
  }

  def isAudioFile(filename: String): Boolean = {
    getExtension(filename).exists(ext => Extension.AudioExtensions.contains(ext.toLowerCase))
  }

  def isPreviewable(filename: String): Boolean = {
    isPreviewedByPdf(filename) ||
    isMsOfficeFile(filename) ||
    isUrlFile(filename) ||
    isImageFile(filename) ||
    isAudioFile(filename) ||
    isVideoFile(filename)
  }

  def isCueFile(filename: String): Boolean = {
    getExtension(filename).exists(_.toLowerCase == Extension.Cue)
  }

  def isJsonFile(filename: String): Boolean = {
    getExtension(filename).exists(_.toLowerCase == Extension.Json)
  }

  def isCueOrJsonFile(filename: String): Boolean = {
    isCueFile(filename) || isJsonFile(filename)
  }

  val AvoidedChars: String = "\\/:*?\"<>|"
  val AvoidedCharsRegex: String = "[\\\\\\/:*?\"<>|]"

  def sanitizeFileFolderName(filename: String): String = {
    def isAcsiiControlChars(char: Char): Boolean = 0 <= char && char <= 31

    val adjustedFilename = filename
      .trim()
      .filterNot(isAcsiiControlChars)
      .replaceAll(AvoidedCharsRegex, "_")
    Aws3Utils.sanitizeForWindows(
      Aws3Utils.sanitizeForUnix(adjustedFilename)
    )
  }

  def isValidFileName(filename: String): Boolean = {
    filename.nonEmpty && filename.trim() == sanitizeFileFolderName(filename)
  }

  def autoSanitizeName(name: String): String = {
    if (isValidFileName(name)) name else sanitizeFileFolderName(name)
  }

  def updateFileName(newValue: String): String = {
    newValue.filterNot(char => AvoidedChars.contains(char))
  }

  def getPdfFilename(filename: String): String = {
    s"${getName(filename)}.${Extension.Pdf}"
  }

  def getJsonFilename(filename: String): String = {
    s"${getName(filename)}.${Extension.Json}"
  }

  def getOcrFileName(filename: String): String = {
    s"${getName(filename)}_ocr.${Extension.Pdf}"
  }

  def getWatermarkFileName(filename: String): String = {
    s"${getName(filename)}_watermark.${Extension.Pdf}"
  }

  def getThumbnailFileName(filename: String): String = {
    s"${getName(filename)}_thumb.${Extension.Jpg}"
  }

  def getFilename(basename: String, extension: Option[String]): String = {
    val fileName = Filename(basename, extension)
    fileName.fullName
  }

  def replaceFileExtension(fileName: String, newExtension: Option[String]): String = {
    Filename(getName(fileName), newExtension).fullName
  }

  def getSameFileTypeByExtension(fileName: String): Option[Seq[String]] = {
    getExtension(fileName).map { extension =>
      val extensionLowercase = extension.toLowerCase
      Extension.AllFileTypes
        .find { fileType =>
          fileType.map(_.toLowerCase).contains(extensionLowercase)
        }
        .getOrElse(Seq(extension))
    }
  }

}

private case class Filename(basename: String, extension: Option[String]) {

  def fullName: String = {
    basename + extension.map("." + _).getOrElse("")
  }

}

private object Filename {

  private val fileNamePattern: Regex = """\.[A-Za-z0-9]+$""".r

  def unapply(filename: String): Filename = {
    Extension.CompressedFileExtensions
      .foldLeft[Option[Filename]](None) { (r, ext) =>
        // In case filename ends with a special extension
        if (filename.endsWith(s".$ext")) {
          Some(Filename(filename.dropRight(ext.length + 1), Some(ext))) // +1 to exclude the dot
        } else {
          r
        }
      }
      .getOrElse {
        val extension = fileNamePattern findFirstIn filename
        val basename = extension.fold(filename) { ext =>
          filename.dropRight(ext.length)
        }
        Filename(basename, extension.map(_.drop(1)))
      }
  }

}
