// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.validator

/** Describes a simple validation rule violation (i.e. one without hierarchy). Most built-in combinators emit this type
  * of violation.
  *
  * @param value
  *   The value of the object which failed the validation rule.
  * @param constraint
  *   A textual description of the constraint being violated (for example, "must not be empty").
  */
case class Violation(value: Any, constraint: String) {

  override def toString: String = s"$value $constraint"

}
