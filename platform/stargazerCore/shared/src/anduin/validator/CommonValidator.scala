// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.validator

import scala.util.matching.Regex

import zio.prelude.Validation
import anduin.model.common.emailaddress.EmailAddress

object CommonValidator {

  type Validator[T] = T => Result

  type Result = Validation[Violation, Unit]

  given SelfEqual[Result] = scala.CanEqual.derived

  val Success: Result = Validation.unit

  type Failure = Validation.Failure[Nothing, Violation]

  private def matchRegex(regex: Regex): String => Boolean = { value =>
    regex.pattern.matcher(value).matches()
  }

  private def matchRegex(regex: String): String => Boolean = {
    matchRegex(new Regex(regex))
  }

  def validator[T](constraint: String)(predicate: T => Boolean): Validator[T] = { value =>
    if (predicate(value)) {
      Success
    } else {
      Validation.fail(Violation(value, constraint))
    }
  }

  def isSuccess[T](validation: Validation[Violation, T]): Boolean = validation match {
    case Validation.Success(_, _) => true
    case _                        => false
  }

  def isFailure[T](validation: Validation[Violation, T]): Boolean = validation match {
    case Validation.Failure(_, _) => true
    case _                        => false
  }

  // Regex that matches spaces, alphanumeric, underscore and dash only. Currently, we use this same regex for
  // validating name, company and funding type. If this requirement changes in the future, we might need to
  // have separate regex for each of them.
  private val NoDigitRegex = "^([^0-9]*)$"

  val noValidator: Validator[String] = _ => Success

  def combine[T](first: Validator[T], second: Validator[T]): Validator[T] = { value =>
    val result = first(value)
    if (result != Success) {
      result
    } else {
      second(value)
    }
  }

  def and(first: Result, second: Result): Result = {
    (first <&> second).map(_ => ())
  }

  def or[T](first: Validator[T], second: Validator[T]): Validator[T] = { value =>
    if (first(value) == Success) {
      Success
    } else {
      second(value)
    }
  }

  def nonEmptyStringValidator(errorMsg: String): Validator[String] = validator[String](errorMsg)(_.trim.nonEmpty)

  def stringAfterTrimWithMinMaxLengthValidator(
    minLength: Int,
    maxLength: Int,
    underMinLengthErrMsg: String,
    overMaxLengthErrMsg: String
  ): Validator[String] = {
    combine(
      validator(underMinLengthErrMsg)(_.trim.length >= minLength),
      validator(overMaxLengthErrMsg)(_.trim.length <= maxLength)
    )
  }

  val nameValidator: Validator[String] = combine(
    nonEmptyStringValidator("Name must not be empty"),
    validator("Name must not contain digits")(matchRegex(NoDigitRegex))
  )

  val simpleEmailValidator: Validator[String] = {
    combine(
      nonEmptyStringValidator("Email must not be empty"),
      validator("Please enter a valid email address")(matchRegex(EmailAddress.validEmail))
    )
  }

  val emailDomainValidator: Validator[String] =
    validator("Invalid Email Domain format")(matchRegex(EmailAddress.validDomain))

  val emptyStringValidator: Validator[String] = validator("String value must be empty")(_.trim.isEmpty)

  def notContainValidator(s: Seq[String], message: String): Validator[String] =
    validator[String](message)(value => !s.contains(value))

  def regexValidator(regex: Regex, message: String): Validator[String] =
    validator[String](message)(matchRegex(regex))

}
