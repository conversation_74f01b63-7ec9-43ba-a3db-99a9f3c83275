// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.model.optics.iso

import java.time.LocalDate
import monocle.Iso
import scalapb.{GeneratedEnum, GeneratedEnumCompanion, GeneratedMessage, GeneratedMessageCompanion}
import anduin.model.{DateTimeModelUtils, DefaultValue}
import anduin.optics.iso.utils.BidirectionalIso

private[iso] trait DerivedIsoInstances extends LowPriorityDerivedIsoInstances {

  given listToSeqIso: [A, B] => (iso: BidirectionalIso[A, B]) => Iso[List[A], Seq[B]] =
    Iso[List[A], Seq[B]] {
      _.map(iso.get).toSeq
    } {
      _.map(iso.reverseGet).toList
    }

  given setToSeqIso: [A, B] => (iso: BidirectionalIso[A, B]) => Iso[Set[A], Seq[B]] =
    Iso[Set[A], Seq[B]] {
      _.map(iso.get).toSeq
    } {
      _.map(iso.reverseGet).toSet
    }

  given seqToSeq: [A, B] => (iso: BidirectionalIso[A, B]) => Iso[Seq[A], Seq[B]] = {
    Iso[Seq[A], Seq[B]] { a =>
      a.map(iso.get)
    } { b =>
      b.map(iso.reverseGet)
    }
  }

  given optionToOptionIso: [A, B] => (iso: BidirectionalIso[A, B]) => Iso[Option[A], Option[B]] = {
    Iso[Option[A], Option[B]] { a =>
      a.map(iso.get)
    } { b =>
      b.map(iso.reverseGet)
    }
  }

}

private[iso] trait LowPriorityDerivedIsoInstances extends AdditionalDefaultValueImplicits {

  given optionToNonOptionIsoA
    : [A: SelfEqual, B] => (defaultValue: DefaultValue[A], iso: BidirectionalIso[A, B]) => Iso[A, Option[B]] =
    Iso[A, Option[B]] { a =>
      if (a != defaultValue.get) {
        Some(iso.get(a))
      } else {
        None
      }
    } { optB =>
      optB.map(iso.reverseGet).getOrElse(defaultValue.get)
    }

}

private[iso] trait AdditionalDefaultValueImplicits extends LowerPriorityDerivedIsoInstances {

  given protobufMessage: [A <: GeneratedMessage] => (companion: GeneratedMessageCompanion[A]) => DefaultValue[A] =
    DefaultValue.instance(companion.defaultInstance)

  given protobufEnum: [A <: GeneratedEnum] => (companion: GeneratedEnumCompanion[A]) => DefaultValue[A] =
    DefaultValue.instance(companion.fromValue(-1))

  given defaultLocalDate: DefaultValue[LocalDate] = DefaultValue.instance(DateTimeModelUtils.EmptyLocalDate)

}

private[iso] trait LowerPriorityDerivedIsoInstances extends LowestPriorityDerivedIsoInstances {

  given optionToNonOptionIsoB
    : [A, B] => (defaultValue: DefaultValue[B], iso: BidirectionalIso[A, B]) => Iso[A, Option[B]] =
    Iso[A, Option[B]] { a =>
      Some(iso.get(a))
    } { optB =>
      iso.reverseGet(optB.getOrElse(defaultValue.get))
    }

}

private[iso] trait LowestPriorityDerivedIsoInstances {
  given identityIso: [T] => Iso[T, T] = Iso.id
}

object DerivedIsoInstances extends DerivedIsoInstances
