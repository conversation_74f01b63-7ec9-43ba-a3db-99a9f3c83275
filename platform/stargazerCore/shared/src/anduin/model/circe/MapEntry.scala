// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.model.circe

import io.circe.{Decoder, Encoder}

import scala.annotation.unused

import anduin.circe.generic.semiauto.*

final case class MapEntry[K, V](
  key: K,
  value: V
)

object MapEntry {

  given entryEncoder: [K, V] => (@unused kE: Encoder[K], @unused vE: Encoder[V]) => Encoder[MapEntry[K, V]] =
    deriveEncoderWithDefaults

  given entryDecoder: [K, V] => (@unused kE: Decoder[K], @unused vE: Decoder[V]) => Decoder[MapEntry[K, V]] =
    deriveDecoderWithDefaults

  given mapEncoder: [K: Encoder, V: Encoder] => Encoder[Map[K, V]] = {
    Encoder[List[MapEntry[K, V]]].contramap(_.toList.map(entry => MapEntry(entry._1, entry._2)))
  }

  given mapDecoder: [K: Decoder, V: Decoder] => Decoder[Map[K, V]] = {
    Decoder[List[MapEntry[K, V]]].map(_.map(entry => entry.key -> entry.value).toMap)
  }

}
