// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.common.cache

private[cache] final class DoubleLinkedList[K, V] {

  // scalafix:off DisableSyntax.var
  var first: Option[Node[K, V]] = Option.empty[Node[K, V]]
  var last: Option[Node[K, V]] = Option.empty[Node[K, V]]
  // scalafix:on

  def remove(node: Node[K, V]): K = {
    val prevNode = node.prev
    val nextNode = node.next
    if (first.contains(node)) {
      first = nextNode
    }
    if (last.contains(node)) {
      last = prevNode
    }
    prevNode.foreach(_.setNext(nextNode))
    nextNode.foreach(_.setPrev(prevNode))
    node.setPrev(None)
    node.setNext(None)
    node.key
  }

  def append(node: Node[K, V]): Node[K, V] = {
    last
      .fold {
        first = Some(node)
        last = Some(node)
      } { lastNode =>
        lastNode.setNext(Some(node))
        node.setPrev(last)
        last = Some(node)
      }
    node
  }

  def removeFirst(): Option[K] = {
    first.fold[Option[K]](None) { firstNode =>
      Some(remove(firstNode))
    }
  }

  def removeThenAppend(node: Node[K, V]): Node[K, V] = {
    val newNode = new Node(node.key, node.value)
    remove(node)
    append(newNode)
    newNode
  }

  def toSeq: Seq[(K, V)] = toSeqRecursive(first, Seq.empty)

  @scala.annotation.tailrec
  private def toSeqRecursive(nodeOpt: Option[Node[K, V]], curList: Seq[(K, V)]): Seq[(K, V)] = {
    nodeOpt match {
      case None       => curList
      case Some(node) => toSeqRecursive(node.next, curList ++ List((node.key, node.value)))
    }
  }

}

private[cache] final class Node[K, V](
  val key: K,
  // scalafix:off DisableSyntax.var
  var value: V,
  var next: Option[Node[K, V]] = None,
  var prev: Option[Node[K, V]] = None
  // scalafix:on
) derives CanEqual {

  def setNext(nextNode: Option[Node[K, V]]): Unit = {
    next = nextNode
  }

  def setPrev(prevNode: Option[Node[K, V]]): Unit = {
    prev = prevNode
  }

  def setValue(newValue: V): V = {
    val currentValue = value
    value = newValue
    currentValue
  }

}
