// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.common.cache

import scala.collection.mutable

final class LRUCache[K, V](maxCacheSize: Int) {

  private val linkedList: DoubleLinkedList[K, V] = new DoubleLinkedList[K, V]
  private val hashTable: mutable.HashMap[K, Node[K, V]] = mutable.HashMap()

  def put(key: K, value: V): Unit = {
    val cachedNode = hashTable.get(key)
    val newNode = new Node(key, value)
    cachedNode.fold {
      val appendedNode = linkedList.append(newNode)
      hashTable.put(key, appendedNode)
      ()
    } { node =>
      hashTable.remove(key)
      linkedList.remove(node)
      val appendedNode = linkedList.append(newNode)
      hashTable.put(key, appendedNode)
      ()
    }
    reduceCacheSize()
  }

  def get(key: K): Option[V] = {
    val cachedNode = hashTable.get(key)
    cachedNode.fold[Option[V]](None) { node =>
      val newNode = linkedList.removeThenAppend(node)
      hashTable.update(key, newNode)
      Some(node.value)
    }
  }

  def size: Int = {
    hashTable.size
  }

  def toSeq: Seq[(K, V)] = linkedList.toSeq

  private def reduceCacheSize(): Unit = {
    if (hashTable.size > maxCacheSize) {
      val removedKey = linkedList.removeFirst()
      removedKey.fold(()) { key =>
        hashTable.remove(key)
        ()
      }
      reduceCacheSize()
    }
  }

}
