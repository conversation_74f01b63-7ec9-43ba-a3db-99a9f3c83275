// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.optics.iso.utils

import monocle.Iso

import anduin.model.id.protobuf.DateTimeMappersInstance

import java.time.*

import anduin.optics.iso.utils.IsoUtils.*
import anduin.protobuf.*

private[iso] trait DateTimeIsoInstances {

  val localDateIso: Iso[LocalDate, LocalDateMessage] = fromTypeMapper(DateTimeMappersInstance.localDateMapper)

  val localDateTimeIso: Iso[LocalDateTime, LocalDateTimeMessage] = fromTypeMapper(
    DateTimeMappersInstance.localDateTimeMapper
  )

  val zoneIdIso: Iso[ZoneId, ZoneIdMessage] = fromTypeMapper(DateTimeMappersInstance.zoneIdMapper)

  val zonedDateTimeIso: Iso[ZonedDateTime, ZonedDateTimeMessage] = fromTypeMapper(
    DateTimeMappersInstance.zonedDateTimeMapper
  )

  val durationIso: Iso[Duration, DurationMessage] = fromTypeMapper(DateTimeMappersInstance.durationMapper)

  val instantIso: Iso[Instant, InstantMessage] = fromTypeMapper(DateTimeMappersInstance.instantMapper)
}

object DateTimeIsoInstances extends DateTimeIsoInstances
