// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.optics.iso.utils

import monocle.Iso

trait BidirectionalIso[A, B] extends AbstractIso[A, B]

object BidirectionalIso extends LowPriorityBidirectionalIsoInstances {

  given defaultIso: [A, B] => (iso0: Iso[A, B]) => BidirectionalIso[A, B] =
    new BidirectionalIso[A, B] {
      val iso: Iso[A, B] = iso0
    }

}

private[iso] trait LowPriorityBidirectionalIsoInstances {

  given reverseIso: [A, B] => (iso0: Iso[B, A]) => BidirectionalIso[A, B] =
    new BidirectionalIso[A, B] {
      val iso: Iso[A, B] = iso0.reverse
    }

}
