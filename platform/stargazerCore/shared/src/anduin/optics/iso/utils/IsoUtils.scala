// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.optics.iso.utils

import scala.deriving.Mirror
import monocle.Iso

import scalapb.*

object IsoUtils {

  def iso[A, B](
    using iso: BidirectionalIso[A, B]
  ): Iso[A, B] = {
    iso.iso
  }

  def productIso[A <: Product, B <: Product](
    using iso: ProductIso[A, B]
  ): Iso[A, B] = {
    iso.iso
  }

  class BoxIsoBuilder[A, B <: GeneratedMessage, O <: GeneratedOneof] {

    def apply(
      f: A => O
    )(
      g: O => A
    )(
      using bMirror: Mirror.ProductOf[B] { type MirroredElemTypes = O *: EmptyTuple }
    ): Iso[A, B] = {
      Iso[A, B] { a =>
        bMirror.fromProductTyped(f(a) *: EmptyTuple)
      } { b =>
        g(Tuple.fromProductTyped(b).head)
      }
    }

  }

  def boxIso[A, B <: GeneratedMessage, O <: GeneratedOneof]: BoxIsoBuilder[A, B, O] = {
    new BoxIsoBuilder[A, B, O]
  }

  def dummyIso[A, B <: GeneratedMessage](
    using aMirror: Mirror.ProductOf[A] { type MirrorElemTypes = EmptyTuple },
    bMirror: Mirror.ProductOf[B] { type MirroredElemTypes = Int *: EmptyTuple }
  ): Iso[A, B] = {
    Iso[A, B] { _ =>
      bMirror.fromProductTyped(0 *: EmptyTuple)
    } { _ =>
      aMirror.fromProduct(EmptyTuple)
    }
  }

  def fromTypeMapper[A <: GeneratedMessage, B](typeMapper: TypeMapper[A, B]): Iso[B, A] = {
    Iso(typeMapper.toBase)(typeMapper.toCustom)
  }

  def fromEnumTypeMapper[A <: GeneratedEnum, B](typeMapper: TypeMapper[A, B]): Iso[B, A] = {
    Iso(typeMapper.toBase)(typeMapper.toCustom)
  }

  // Ops classes

  extension [A](a: A) {

    def isomorphInto[B](
      using iso: BidirectionalIso[A, B]
    ): B = iso.get(a)

  }

  def throwBoxException(x: Any): Nothing = {
    throw new NoIsoImplementationException(s"No Iso implementation for $x (class = ${x.getClass.getName}")
  }

  class NoIsoImplementationException(val message: String) extends Exception(message)
}
