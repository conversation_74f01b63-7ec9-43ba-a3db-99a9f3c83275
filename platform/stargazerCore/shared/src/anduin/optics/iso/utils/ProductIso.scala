// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.optics.iso.utils

import scala.deriving.Mirror
import monocle.Iso

private[iso] trait ProductIso[A <: Product, B <: Product] extends AbstractIso[A, B]

private[iso] object ProductIso {

  given ProductIso[EmptyTuple, EmptyTuple] = new ProductIso[EmptyTuple, EmptyTuple] {
    override val iso: Iso[EmptyTuple, EmptyTuple] = Iso.id[EmptyTuple]
  }

  given [AH, BH, AT <: Tuple, BT <: Tuple] => (headIso: BidirectionalIso[AH, BH], tailIso: ProductIso[AT, BT])
    => ProductIso[AH *: AT, BH *: BT] =
    new ProductIso[AH *: AT, BH *: BT] {

      override val iso: Iso[AH *: AT, BH *: BT] = Iso[AH *: AT, BH *: BT] { case ah *: at =>
        headIso.get(ah) *: tailIso.get(at)
      } { case bh *: bt =>
        headIso.reverseGet(bh) *: tailIso.reverseGet(bt)
      }

    }

  given [A <: Product, B <: Product] => (
    aMirror: Mirror.ProductOf[A],
    bMirror: Mirror.ProductOf[B]
  )
    => (
      genericIso: ProductIso[aMirror.MirroredElemTypes, bMirror.MirroredElemTypes]
  ) => ProductIso[A, B] = {
    new ProductIso[A, B] {
      override val iso: Iso[A, B] = Iso[A, B] { a =>
        val aTuple = Tuple.fromProductTyped(a)
        val bTuple = genericIso.get(aTuple)
        bMirror.fromProduct(bTuple)
      } { b =>
        val bTuple = Tuple.fromProductTyped(b)
        val aTuple = genericIso.reverseGet(bTuple)
        aMirror.fromProduct(aTuple)
      }
    }
  }

}
