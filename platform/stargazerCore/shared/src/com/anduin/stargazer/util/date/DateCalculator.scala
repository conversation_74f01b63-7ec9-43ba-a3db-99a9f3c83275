// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.util.date

import java.time.*

object DateCalculator {

  /** Return the Duration difference between the two ZonedDateTime, and the start date is included E.g.:
    * dayDiffInclusive(today, today) = Duration.ofDays(1)
    */
  private def dayDiffInclusive(startDate: ZonedDateTime, endDate: ZonedDateTime): Duration = {
    // Note: we convert ZonedDateTime to LocalDate so that if the startDate is near the end of a day (e.g. 23:00 Jun 20),
    // and endDate is the beginning of the next day (e.g. 1:00 Jun 21), this will return 2 days instead of 1 day
    dayDiffInclusive(startDate.withZoneSameInstant(endDate.getZone).toLocalDate, endDate.toLocalDate)
  }

  /** Return the Duration difference between the two LocalDate, and the start date is included E.g.:
    * dayDiffInclusive(today, today) = Duration.ofDays(1)
    */
  private def dayDiffInclusive(startDay: LocalDate, endDay: LocalDate): Duration = {
    val duration = Duration.between(startDay.atStartOfDay, endDay.atStartOfDay)
    if (duration.isNegative) {
      duration.minusDays(1)
    } else {
      duration.plusDays(1)
    }
  }

  /** Add a duration to a start date to get an end date. The start date is included E.g. addDuration(1/1/2017, 6 days) =
    * 6/1/2017
    */
  def addDuration(startDate: ZonedDateTime, duration: Duration): ZonedDateTime = {
    startDate.plusDays(duration.toDays - 1)
  }

  /** Add a duration to a start date to get an end date. The start date is included E.g. addDuration(1/1/2017, 6 days) =
    * 6/1/2017
    */
  def addDuration(startDate: LocalDate, duration: Duration): LocalDate = {
    startDate.plusDays(duration.toDays - 1)
  }

  /** Return the number of day differences between the two ZonedDateTime. The start date is included
    */
  def dayDiff(startDate: ZonedDateTime, endDate: ZonedDateTime): Long = {
    dayDiffInclusive(startDate, endDate).toDays
  }

  /** Return the number of day differences between the two LocalDate. The start date is included
    */
  def dayDiff(startDay: LocalDate, endDay: LocalDate): Long = {
    dayDiffInclusive(startDay, endDay).toDays
  }

  /** Return the number of day differences between the two Instant. The start date is included
    */
  def dayDiff(startDay: Instant, endDay: Instant): Long = {
    dayDiffInclusive(startDay.atZone(ZoneId.systemDefault), endDay.atZone(ZoneId.systemDefault)).toDays
  }

  /** Return the number of day differences between the two ZonedDateTime, exclude the redundant hours.
    */
  def dayDiffExclusive(startDate: ZonedDateTime, endDate: ZonedDateTime): Long = {
    Duration.between(startDate, endDate).toDays
  }

  def isWeekend(date: ZonedDateTime): Boolean = {
    date.getDayOfWeek.getValue >= 6 // value number is from 1 (Monday) to 7 (Sunday)
  }

  /** If the `date` if a weekend date, we'll return the next Monday. Else return the same `date`
    */
  def rollToNextMondayIfWeekend(date: ZonedDateTime): ZonedDateTime = {
    date.getDayOfWeek match {
      case DayOfWeek.SATURDAY => date.plusDays(2)
      case DayOfWeek.SUNDAY   => date.plusDays(1)
      case _                  => date
    }
  }

  def rollBackToLastFridayIfWeekend(date: ZonedDateTime): ZonedDateTime = {
    date.getDayOfWeek match {
      case DayOfWeek.SATURDAY => date.minusDays(1)
      case DayOfWeek.SUNDAY   => date.minusDays(2)
      case _                  => date
    }
  }

  // Represent 1970-01-01T00:00:00Z
  val BeginningOfEpoch: ZonedDateTime = ZonedDateTime.ofInstant(Instant.EPOCH, ZoneOffset.UTC)

  /** return today date in system time zone
    */
  def today: ZonedDateTime = {
    ZonedDateTime.now(ZoneId.systemDefault())
  }

  def today(zoneId: ZoneId): ZonedDateTime = {
    ZonedDateTime.now(zoneId)
  }

  def todayLocalDate: LocalDate = {
    LocalDate.now()
  }

  def instantNow: Instant = Instant.now

  def min(date1: ZonedDateTime, date2: ZonedDateTime): ZonedDateTime = {
    if (date1.compareTo(date2) > 0) date2 else date1
  }

  def max(date1: ZonedDateTime, date2: ZonedDateTime): ZonedDateTime = {
    if (date1.compareTo(date2) > 0) date1 else date2
  }

  def min(date1: LocalDate, date2: LocalDate): LocalDate = {
    if (date1.compareTo(date2) > 0) date2 else date1
  }

  def max(date1: LocalDate, date2: LocalDate): LocalDate = {
    if (date1.compareTo(date2) > 0) date1 else date2
  }

}
