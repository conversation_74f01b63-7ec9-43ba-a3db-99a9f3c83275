// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.service

/** All the shared constants.
  */
object Service {
  val API = "api"
  val API_VERSION_V2 = "v2"
  val API_PATH_V2: String = s"$API/$API_VERSION_V2"

  trait ServiceParams // TODO: def token: String
  trait ServiceResponse

  trait ServiceError extends Throwable {
    def statusText: String // short description of the error
    def errorMessage: String // detail of the error
  }

}
