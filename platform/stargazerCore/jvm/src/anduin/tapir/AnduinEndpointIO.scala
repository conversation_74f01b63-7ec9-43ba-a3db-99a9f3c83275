// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.tapir

import java.time.{Instant, LocalDate, ZoneId}

import sttp.model.*
import sttp.model.headers.{CookieValueWithMeta, CookieWithMeta}
import sttp.tapir.*

object AnduinEndpointIO {

  private val expiredCookieTime: Instant =
    LocalDate
      .of(
        1800,
        1,
        1
      )
      .atStartOfDay(ZoneId.systemDefault())
      .toInstant

  def redirect(code: StatusCode): EndpointOutput[Uri] =
    statusCode(code).and(header[Uri]("Location"))

  def fixedSetCookie(cookie: CookieWithMeta): EndpointIO.FixedHeader[Unit] = {
    header(Header.setCookie(cookie))
  }

  def deleteCookieValueWithMeta(domain: String = "", path: String = ""): CookieValueWithMeta = {
    CookieValueWithMeta.unsafeApply(
      "deleted",
      expires = Some(expiredCookieTime),
      domain = Some(domain),
      path = Some(path)
    )
  }

  def deleteCookie(name: String, domain: String = "", path: String = ""): EndpointIO.FixedHeader[Unit] = {
    fixedSetCookie(
      CookieWithMeta(
        name,
        deleteCookieValueWithMeta(domain, path)
      )
    )
  }

  def optionalCookie(name: String): EndpointInput.Cookie[Option[String]] = {
    cookie[Option[String]](name)
  }

  lazy val jsBodyUtf8: EndpointIO[String] =
    header(
      Header.contentType(
        MediaType(
          "text",
          "javascript",
          Some("utf8")
        )
      )
    ).and(stringBody("utf8"))

  lazy val optionalUserAgent: EndpointIO.Header[Option[String]] = header[Option[String]](HeaderNames.UserAgent)

  lazy val uri: EndpointInput.ExtractFromRequest[Uri] = extractFromRequest(_.uri)

  lazy val host: EndpointInput.ExtractFromRequest[Option[String]] = extractFromRequest(_.uri.host)

  lazy val optionalClientIp: EndpointInput.ExtractFromRequest[Option[String]] = extractFromRequest(request =>
    request
      .header(HeaderNames.XForwardedFor)
      .flatMap(_.split(",").headOption)
      .orElse(request.header("Remote-Address"))
      .orElse(request.header("X-Real-Ip"))
  )

}
