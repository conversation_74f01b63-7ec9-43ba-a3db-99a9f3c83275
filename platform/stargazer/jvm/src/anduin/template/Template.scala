// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.template

import scala.io.Source

import zio.{Task, ZIO}
import yamusca.data.{Element, Section, Variable}
import yamusca.imports.{Context, Value}
import io.circe.{<PERSON><PERSON>, JsonNumber, JsonObject}

case class Template private (private val mustacheTemplate: yamusca.data.Template) {

  def execute(context: Context): Task[String] = {
    for {
      (missingKeys, _, result) <- ZIO.attempt {
        yamusca.imports.mustache.expandWithMissingKeys(mustacheTemplate)(context)
      }
      res <-
        if (missingKeys.isEmpty) {
          ZIO.succeed(result)
        } else {
          ZIO.fail(TemplateMissingContext(s"missing keys: $missingKeys", missingKeys.toSet))
        }
    } yield res
  }

  private def getKeys(elems: Seq[Element]): Seq[String] = {
    elems.flatMap {
      case v: Variable => Seq(v.key)
      case s: Section  => getKeys(s.inner) :+ s.key
      case _           => Seq.empty
    }
  }

  def variableKeys: Set[String] = {
    getKeys(mustacheTemplate.els).toSet
  }

}

sealed abstract class TemplateError(message: String) extends RuntimeException(message)
case class TemplateParseError(message: String) extends TemplateError(message)
case class TemplateMissingContext(message: String, missingKeys: Set[String]) extends TemplateError(message)

object Template {

  def raw(rawTemplate: String): Task[Template] = {
    yamusca.imports.mustache
      .parse(rawTemplate)
      .fold(
        left => {
          val (parseInput, s) = left
          ZIO.fail(
            TemplateParseError(
              s"parse error: $s. pos: ${parseInput.pos}, end: ${parseInput.end}, delim: ${parseInput.delim}"
            )
          )
        },
        t => ZIO.attempt(Template(t))
      )
  }

  def resource(uri: String): Task[Template] = {
    for {
      content <- ZIO.attempt {
        Source.fromInputStream(getClass.getResourceAsStream(uri)).mkString
      }
      res <- raw(content)
    } yield res
  }

  private val jsonToContextFolder: Json.Folder[Value] = new Json.Folder[Value] {
    override def onNull: Value = Value.of(false)

    override def onBoolean(value: Boolean): Value = Value.of(value)

    override def onNumber(value: JsonNumber): Value = Value.of(value.toDouble.toString)

    override def onString(value: String): Value = Value.of(value)

    override def onArray(value: Vector[Json]): Value = {
      Value.fromSeq(value.map(_.foldWith(this)))
    }

    override def onObject(value: JsonObject): Value = {
      Value.fromMap(value.toMap.map { case (k: String, v: Json) =>
        k -> v.foldWith(this)
      })
    }

  }

  def toContext(json: Json): Context = {
    val value = json.foldWith[Value](jsonToContextFolder)
    value.asContext
  }

}
