// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.memcached

import java.time.Instant
import scala.concurrent.duration.{Duration, FiniteDuration, given}
import scala.jdk.CollectionConverters.given
import io.opentelemetry.api.trace.SpanKind
import net.spy.memcached
import net.spy.memcached.internal.{
  BulkGetCompletionListener,
  BulkGetFuture,
  GetCompletionListener,
  GetFuture,
  OperationCompletionListener,
  OperationFuture
}
import net.spy.memcached.ops.{OperationStatus, StatusCode}
import net.spy.memcached.{AddrUtil, ConnectionFactoryBuilder}
import zio.telemetry.opentelemetry.tracing.Tracing
import zio.{Task, ZIO}
import anduin.model.BinaryModel
import anduin.telemetry.TelemetryEnvironment
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.utils.{ZIOTelemetryUtils, ZIOUtils}

import scala.annotation.nowarn

final case class MemcachedClient(backendConfig: GondorBackendConfig, tracingEnvironment: TelemetryEnvironment.Tracing) {

  private given canEqualDuration: CanEqual[FiniteDuration, Duration] = CanEqual.derived
  private given canEqualStatus: SelfEqual[StatusCode] = CanEqual.derived

  private val connectionFactory =
    new ConnectionFactoryBuilder()
      .setProtocol(ConnectionFactoryBuilder.Protocol.BINARY)
      .setDaemon(true)
      .build()

  private val memcachedClient =
    new memcached.MemcachedClient(
      connectionFactory,
      AddrUtil.getAddresses(s"${backendConfig.memcachedConfig.host}:${backendConfig.memcachedConfig.port}")
    )

  private val MULTIGET_MAX = 100

  private val tracingSpanName = "service/memcached"
  private val tracingSpanKind = SpanKind.CLIENT
  private val tracingAttributePrefix = "service.data.memcached"
  private val tracingAttributeOperation = s"$tracingAttributePrefix.operation"
  private val tracingAttributeKey = s"$tracingAttributePrefix.key"
  private val tracingAttributeSize = s"$tracingAttributePrefix.size"

  private def injectTracing[R, E, A](attributes: Map[String, String])(effect: ZIO[R, E, A]): ZIO[R & Tracing, E, A] = {
    ZIOTelemetryUtils.traceWithChildSpan(
      tracingSpanName,
      tracingSpanKind,
      attributes
    )(effect)
  }

  private def toMemcachedExpiry(ttl: Option[Duration]): Int = {
    ttl.map(durationToExpiry).getOrElse(0)
  }

  /** Convert an optional `Duration` to an int suitable for passing to Memcached.
    *
    * From the Memcached protocol spec:
    *
    * The actual value sent may either be Unix time (number of seconds since January 1, 1970, as a 32-bit value), or a
    * number of seconds starting from current time. In the latter case, this number of seconds may not exceed
    * 60*60*24*30 (number of seconds in 30 days); if the number sent by a client is larger than that, the server will
    * consider it to be real Unix time value rather than an offset from current time.
    */

  private def durationToExpiry(duration: Duration): Int = duration match {
    case Duration.Zero => 0

    case d if d < 1.second =>
      1

    case d if d <= 30.days => d.toSeconds.toInt

    case d =>
      val expiryTime = Instant.now().plusSeconds(d.toSeconds.toInt)
      (expiryTime.toEpochMilli / 1000).toInt
  }

  @nowarn
  private def setInternal[V: BinaryModel](key: String, value: V, expiration: Option[Duration] = None): Task[Unit] = {
    ZIO.async { cb =>
      val binaryModel = summon[BinaryModel[V]]
      binaryModel
        .toByteArray(value)
        .fold(
          error => cb(ZIO.fail(error)),
          bytes => {
            val f = memcachedClient.set(key, toMemcachedExpiry(expiration), bytes)
            f.addListener((future: OperationFuture[?]) => {
              if (future.getStatus.isSuccess) {
                cb(ZIO.unit)
              } else {
                cb(ZIO.fail(MemcachedClient.MemcachedException(future.getStatus)))
              }
            })
          }
        )
    }
  }

  def set[V: BinaryModel](key: String, value: V, expiration: Option[Duration] = None): Task[Unit] = {
    injectTracing(
      Map(
        tracingAttributeOperation -> "set",
        tracingAttributeKey -> key
      )
    )(
      setInternal(key, value, expiration)
    ).provideEnvironment(tracingEnvironment.environment)
  }

  def mset[V: BinaryModel](values: Map[String, V], expiration: Option[Duration] = None): Task[Unit] = {
    // Memcached does not support multi-set :(
    injectTracing(
      Map(
        tracingAttributeOperation -> "mset",
        tracingAttributeSize -> values.size.toString
      )
    )(
      ZIOUtils
        .foreachParN(4)(values) { case (k, v) =>
          setInternal(k, v, expiration)
        }
        .unit
    ).provideEnvironment(tracingEnvironment.environment)

  }

  @nowarn
  def get[V: BinaryModel](key: String): Task[Option[V]] = {
    val task = ZIO.async[Any, Throwable, Option[V]] { cb =>
      val f = memcachedClient.asyncGet(key)
      f.addListener((future: GetFuture[?]) => {
        if (future.getStatus.isSuccess) {
          val bytesEither = future.get() match {
            case a: Array[Byte] => Right(a)
            case _              => Left(new ClassCastException("Cannot cast to byte array"))
          }
          bytesEither.fold(
            error => cb(ZIO.fail(error)),
            bytes => {
              val binaryModel = summon[BinaryModel[V]]
              cb(ZIO.fromEither(binaryModel.fromByteArray(bytes)).map(Some.apply).catchAllCause { error =>
                ZIO.logWarningCause(s"Cannot decode model from cache for $key", error).as(None)
              })
            }
          )
        } else {
          future.getStatus.getStatusCode match {
            case StatusCode.ERR_NOT_FOUND => cb(ZIO.succeed(None))
            case _                        => cb(ZIO.fail(MemcachedClient.MemcachedException(future.getStatus)))
          }
        }
      })
    }
    injectTracing(
      Map(
        tracingAttributeOperation -> "get",
        tracingAttributeKey -> key
      )
    )(task).provideEnvironment(tracingEnvironment.environment)
  }

  @nowarn
  private def mgetInternal[V: BinaryModel](keys: Seq[String]): Task[Map[String, V]] = {
    ZIO.async { cb =>
      val f = memcachedClient.asyncGetBulk(keys*)
      f.addListener((future: BulkGetFuture[?]) => {
        if (future.getStatus.isSuccess) {
          val rawValues = future.get().asScala.collect { case (key, value) =>
            value match {
              case a: Array[Byte] => key -> a
            }
          }
          val binaryModel = summon[BinaryModel[V]]
          val values = rawValues
            .map { case (key, rawValue) =>
              val valueOpt = binaryModel
                .fromByteArray(rawValue)
                .fold(
                  error => {
                    scribe.warn(s"Cannot decode model from cache for $key", error)
                    None
                  },
                  value => Some(value)
                )
              valueOpt.map(key -> _)
            }
            .flatten
            .toMap
          cb(ZIO.succeed(values))
        } else {
          cb(ZIO.fail(MemcachedClient.MemcachedException(future.getStatus)))
        }
      })
    }
  }

  def mget[V: BinaryModel](keys: Seq[String]): Task[Map[String, V]] = {
    injectTracing(
      Map(
        tracingAttributeOperation -> "mget",
        tracingAttributeSize -> keys.size.toString
      )
    )(
      ZIO
        .foreach(keys.sliding(MULTIGET_MAX, MULTIGET_MAX).toList)(mgetInternal)
        .map(
          _.reduceLeftOption((accumulate, value) => accumulate ++ value).getOrElse(Map.empty)
        )
    ).provideEnvironment(tracingEnvironment.environment)
  }

  @nowarn
  def remove(key: String): Task[Unit] = {
    val task = ZIO.async[Any, Throwable, Unit] { cb =>
      val f = memcachedClient.delete(key)
      f.addListener((future: OperationFuture[?]) => {
        if (future.getStatus.isSuccess) {
          cb(ZIO.unit)
        } else {
          future.getStatus.getStatusCode match {
            case StatusCode.ERR_NOT_FOUND => cb(ZIO.unit)
            case _                        => cb(ZIO.fail(MemcachedClient.MemcachedException(future.getStatus)))
          }
        }
      })
    }
    injectTracing(
      Map(
        tracingAttributeOperation -> "remove",
        tracingAttributeKey -> key
      )
    )(task).provideEnvironment(tracingEnvironment.environment)
  }

  def shutdown(): Unit = {
    memcachedClient.shutdown()
  }

}

object MemcachedClient {

  final case class MemcachedException(status: OperationStatus) extends Exception {
    override def getMessage: String = status.getMessage
  }

}
