// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.globaldatabase

import anduin.telemetry.TelemetryEnvironment
import io.getquill.*
import io.getquill.context.qzio.ZioJdbcContext
import zio.{Task, ZIO}

final case class GlobalDatabaseService(
  globalDatabaseEnvironment: GlobalDatabase.ZioEnvironment,
  tracingEnvironment: TelemetryEnvironment.Tracing
) {

  inline def query[T](context: ZioJdbcContext[?, ?], query: Quoted[Query[T]]): Task[List[T]] = {
    ZIO
      .serviceWithZIO[GlobalDatabase] { globalDatabase =>
        globalDatabase.query(context, query)
      }
      .provideEnvironment(globalDatabaseEnvironment.environment ++ tracingEnvironment.environment)

  }

  inline def write[T](context: ZioJdbcContext[?, ?], write: Quoted[Action[T]]): Task[Long] = {
    ZIO
      .serviceWithZIO[GlobalDatabase] { globalDatabase =>
        globalDatabase.write(context, write)
      }
      .provideEnvironment(globalDatabaseEnvironment.environment ++ tracingEnvironment.environment)
  }

  inline def writeBatch[I, A <: Action[I] & QAC[I, Nothing]](
    context: ZioJdbcContext[?, ?],
    writes: Quoted[BatchAction[A]]
  ): Task[List[Long]] = {
    ZIO
      .serviceWithZIO[GlobalDatabase] { globalDatabase =>
        globalDatabase.writeBatch(context, writes)
      }
      .provideEnvironment(globalDatabaseEnvironment.environment ++ tracingEnvironment.environment)
  }

}
