// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.temporal

import io.micrometer.core.instrument.MeterRegistry
import zio.ZEnvironment
import zio.temporal.schedules.ZScheduleClient
import zio.temporal.workflow.{ZWorkflowClient, ZWorkflowServiceStubs}

import anduin.telemetry.TelemetryEnvironment.AggregatedTracing
import anduin.workflow.TemporalWorkflowService

final case class TemporalEnvironment(
  workflowService: ZEnvironment[TemporalWorkflowService],
  workflowClient: ZEnvironment[ZWorkflowClient],
  scheduleClient: ZEnvironment[ZScheduleClient],
  serviceStubs: ZEnvironment[ZWorkflowServiceStubs],
  metricRegistry: ZEnvironment[MeterRegistry],
  tracing: ZEnvironment[AggregatedTracing]
)
