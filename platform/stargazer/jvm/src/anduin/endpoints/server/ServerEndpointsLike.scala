// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.endpoints.server

import anduin.execution.ZIOExecutor
import com.anduin.stargazer.apps.stargazer.StargazerSettings
import com.anduin.stargazer.service.utils.ZIOUtils
import zio.Runtime

import scala.concurrent.duration.FiniteDuration

object ServerEndpointsLike {

  private val executor = ZIOExecutor.io(
    name = "ServerEndpointsLike"
  )

  given runtime: zio.Runtime[Any] = ZIOUtils.unsafeBuildRuntimeFromLayer {
    Runtime.setExecutor(executor) ++ Runtime.setBlockingExecutor(executor)
  }

  lazy val defaultTimeout: FiniteDuration = StargazerSettings.gondorConfig.backendConfig.servicesTimeout.default
}
