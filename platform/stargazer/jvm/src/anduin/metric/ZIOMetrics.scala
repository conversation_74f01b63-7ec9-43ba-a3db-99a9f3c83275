// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.metric

import io.micrometer.prometheusmetrics.{PrometheusConfig, PrometheusMeterRegistry}
import sttp.tapir.server.armeria.zio.ArmeriaZioServerInterpreter
import sttp.tapir.ztapir.*
import zio.*
import zio.metrics.{Metric, MetricLabel}
import zio.metrics.connectors.micrometer
import zio.metrics.jvm.*

import anduin.buildinfo.stargazerJvmBuildInfo
import anduin.endpoints.server.ServerEndpointsLike
import anduin.metric.ZIOMetrics.metricsEndpoint
import anduin.tapir.server.EndpointServer.{CustomServerService, TapirServerService}
import com.anduin.stargazer.apps.stargazer.StargazerSettings

final case class ZIOMetrics(
  interpreter: ArmeriaZioServerInterpreter[Any],
  metricsEnvironment: ZIOMetrics.Environment
) {

  private lazy val metricsRoute = CustomServerService(
    interpreter.toService(
      metricsEndpoint.zServerLogic[Any] { _ =>
        ZIO
          .serviceWith[PrometheusMeterRegistry] { registry =>
            registry.scrape()
          }
          .provideEnvironment(metricsEnvironment.registry)
      }
    )(
      using ServerEndpointsLike.runtime
    )
  )

  lazy val services: List[TapirServerService] = List(
    metricsRoute
  )

}

object ZIOMetrics {

  lazy val isEnabled: Boolean = StargazerSettings.gondorConfig.enableMetric

  def toLabels(tags: Map[String, String]): Set[MetricLabel] = {
    tags.map { case (key: String, value: String) =>
      MetricLabel(key, value)
    }.toSet
  }

  final case class Environment(
    registry: ZEnvironment[PrometheusMeterRegistry]
  )

  val registryLayer: ZLayer[Any, Throwable, PrometheusMeterRegistry] = ZLayer.make[PrometheusMeterRegistry](
    ZLayer.succeed(micrometer.MicrometerConfig.default),
    ZLayer.succeed(new PrometheusMeterRegistry(PrometheusConfig.DEFAULT)),
    micrometer.micrometerLayer ++ Runtime.enableRuntimeMetrics ++ DefaultJvmMetrics.liveV2.unit ++ AnduinVersionInfo.live
  )

  private lazy val metricsEndpoint = endpoint.get.in("metrics").out(stringBody)

  private object AnduinVersionInfo {

    private def versionInfo(version: String): Metric.Gauge[Unit] =
      Metric
        .gauge(
          "gondor_version"
        )
        .tagged(
          MetricLabel("version", version)
        )
        .fromConst(1.0)

    private def reportVersions(
    )(
      using trace: Trace
    ): ZIO[Any, Throwable, Unit] =
      for {
        _ <- versionInfo(stargazerJvmBuildInfo.version).set(()).delay(zio.Duration.fromSeconds(2)).forkDaemon
      } yield ()

    val live: ZLayer[Any, Throwable, Unit] = ZLayer(reportVersions())
  }

}
