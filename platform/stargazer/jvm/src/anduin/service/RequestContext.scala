// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.service

import io.circe.*
import org.uaparser.scala.{Client, Parser}

import anduin.circe.generic.semiauto.*
import anduin.stargazer.util.HttpContextUtils

sealed trait RequestContext derives CanEqual {
  def headers: Seq[(String, String)]
  def cookies: Seq[(String, String)]

  def getClientIP: Option[String] = {
    HttpContextUtils.getClientIP(headers)
  }

  def getUserAgent: Option[String] = {
    headers.find(_._1.toLowerCase == "user-agent").map(_._2)
  }

  def getHost: Option[String] = {
    HttpContextUtils.getHost(headers)
  }

  lazy val userAgent: Option[Client] = getUserAgent.map(Parser.default.parse)

}

case class PublicRequestContext(
  headers: Seq[(String, String)],
  cookies: Seq[(String, String)]
) extends RequestContext

object PublicRequestContext {

  val defaultInstance: PublicRequestContext = PublicRequestContext(
    Seq.empty,
    Seq.empty
  )

}

case class AuthenticatedRequestContext(
  actor: ServiceActor,
  headers: Seq[(String, String)],
  cookies: Seq[(String, String)]
) extends RequestContext

object AuthenticatedRequestContext {

  given Codec.AsObject[AuthenticatedRequestContext] = deriveCodecWithDefaults
  given decoder: Decoder[AuthenticatedRequestContext] = deriveDecoderWithDefaults[AuthenticatedRequestContext]

  val defaultInstance: AuthenticatedRequestContext = AuthenticatedRequestContext(
    ServiceActor.defaultServiceActor,
    Seq.empty,
    Seq.empty
  )

}
