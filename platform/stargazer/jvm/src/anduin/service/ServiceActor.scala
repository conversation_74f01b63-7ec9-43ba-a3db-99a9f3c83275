// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.service

import anduin.model.common.user.{UserAttributes, UserId, UserInfo}
import io.circe.Codec
import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.id.account.UserSessionId
import anduin.id.environment.EnvironmentId

// NOTE:
// Normally you should never have to create a ServiceActor yourself
// com.anduin.stargazer.service.authorization.AuthorizationService.authenticateSession
// is the function you should use to create a new ServiceActor
final case class ServiceActor(
  userId: UserId,
  userInfo: UserInfo,
  userAttributes: UserAttributes,
  userSessionIdOpt: Option[UserSessionId],
  environmentIdOpt: Option[EnvironmentId]
)

object ServiceActor {

  given Codec.AsObject[ServiceActor] = deriveCodecWithDefaults

  lazy val defaultServiceActor: ServiceActor =
    ServiceActor(
      UserId.defaultValue.get,
      UserInfo.DEFAULT,
      UserAttributes.DEFAULT,
      None,
      None
    )

}
