// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.psql

import zio.Task

import com.anduin.stargazer.service.GondorBackendConfig.PostgresqlConfig

object TimescaleDatabase {

  private val migrationScriptLocation = "/db/migration/timescaledb"

  def ensureLatestDatabaseSchema(
    config: PostgresqlConfig
  ): Task[Unit] = {
    PostgresqlDatabase.ensureLatestDatabaseSchema(config, migrationScriptLocation)
  }

}
