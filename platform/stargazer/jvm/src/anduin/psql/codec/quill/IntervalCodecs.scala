// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.psql.codec.quill

import java.sql.Types
import java.time.Duration
import java.time.temporal.ChronoUnit

import io.getquill.context.jdbc.JdbcContextTypes
import org.postgresql.util.PGInterval

// https://github.com/vladmihalcea/hypersistence-utils - PostgreSQLIntervalType.java
trait IntervalCodecs { this: JdbcContextTypes[?, ?] =>

  private val MICROS_IN_SECOND: Double = 1000000

  given durationEncoder: Encoder[Duration] = {
    encoder(
      Types.OTHER,
      (index, value, row) => {
        val days = value.toDays.toInt
        val hours = (value.toHours % 24).toInt
        val minutes = (value.toMinutes % 60).toInt
        val seconds = (value.getSeconds % 60).toInt
        val micros = value.getNano / 1000
        val secondsWithFraction = seconds + (micros / MICROS_IN_SECOND)
        row.setObject(
          index,
          new PGInterval(
            0,
            0,
            days,
            hours,
            minutes,
            secondsWithFraction
          ),
          Types.OTHER
        )
      }
    )
  }

  given durationDecoder: Decoder[Duration] = {
    decoder((index, row, _) => {
      val interval = row.getObject(index, classOf[PGInterval])
      val days = interval.getDays.toLong
      val hours = interval.getHours.toLong
      val minutes = interval.getMinutes.toLong
      val seconds = interval.getSeconds.toLong
      val micros = Math.round((interval.getSeconds - seconds) * MICROS_IN_SECOND)

      Duration
        .ofDays(days)
        .plus(hours, ChronoUnit.HOURS)
        .plus(minutes, ChronoUnit.MINUTES)
        .plus(seconds, ChronoUnit.SECONDS)
        .plus(micros, ChronoUnit.MICROS)
    })
  }

}
