// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.psql.codec.quill

import java.sql.Types
import scala.reflect.Enum

import io.getquill.context.jdbc.JdbcContextTypes

trait EnumCodecs { this: JdbcContextTypes[?, ?] =>

  given enumEncoder: [A <: Enum] => Encoder[A] = {
    encoder(
      Types.OTHER,
      (index, value, row) => {
        row.setObject(
          index,
          value.toString,
          Types.OTHER
        )
      }
    )
  }

  def enumDecoder[A <: Enum](
    valueOf: String => A
  ): Decoder[A] = {
    decoder((index, row, _) => {
      valueOf(row.getObject(index, classOf[String]))
    })
  }

}
