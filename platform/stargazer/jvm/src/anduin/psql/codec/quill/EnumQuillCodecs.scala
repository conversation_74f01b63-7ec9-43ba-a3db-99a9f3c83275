// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.psql.codec.quill

import java.sql.Types
import scala.reflect.TypeTest

import io.getquill.context.jdbc.JdbcContextTypes
import izumi.reflect.Tag

import anduin.enumeration.{StringEnum, StringEnumCompanion}
import anduin.utils.ScalaUtils

trait EnumQuillCodecs { this: JdbcContextTypes[?, ?] =>

  def stringEnumEncoder[A <: StringEnum]: Encoder[A] = {
    encoder(
      Types.OTHER,
      (index, value, row) => {
        row.setObject(
          index,
          value.value,
          Types.OTHER
        )
      }
    )
  }

  def stringEnumDecoder[A <: StringEnum](
    defaultValue: A
  )(
    using companion: StringEnumCompanion[A]
  ): Decoder[A] = {
    decoder((index, row, _) => {
      companion.fromValueOpt(row.getObject(index, classOf[String])).getOrElse(defaultValue)
    })
  }

  def stringEnumDecoderThrowError[A <: StringEnum](
    using companion: StringEnumCompanion[A]
  ): Decoder[A] = {
    decoder((index, row, _) => {
      companion.fromValue(row.getObject(index, classOf[String]))
    })
  }

  def stringEnumCaseDecoder[A <: StringEnum, B <: A](
    using companion: StringEnumCompanion[A],
    tt: TypeTest[A, B],
    aTag: Tag[A],
    bTag: Tag[B]
  ): Decoder[B] = {
    decoder((index, row, _) => {
      ScalaUtils.downcast[A, B](companion.fromValue(row.getObject(index, classOf[String])))
    })
  }

}
