// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.cache

import scala.concurrent.duration.Duration

import zio.{Task, ZIO}

import anduin.memcached.MemcachedClient
import anduin.model.BinaryModel
import anduin.telemetry.TelemetryEnvironment

final case class AnduinCacheManager(
  memcachedClient: MemcachedClient,
  tracingEnvironment: TelemetryEnvironment.Tracing
) {

  def make[K, V: BinaryModel](
    name: String,
    expiration: Duration,
    makeKey: K => Task[String],
    retrieve: K => Task[Option[V]],
    mustRevalidateWhen: (K, V) => Boolean = (_: K, _: V) => false,
    throwErrorInMultiGet: Boolean = false
  ): AnduinCache[K, V] = {
    val retrieveMulti: Seq[K] => Task[Map[K, V]] = keys =>
      ZIO
        .foreach(keys)(key =>
          retrieve(key).map(_.map(value => key -> value)).catchAll { error =>
            if (throwErrorInMultiGet) {
              ZIO.fail(error)
            } else {
              ZIO.logWarningCause(s"Cache multi get failed", error.toCause).as(None)
            }
          }
        )
        .map(_.flatten.toMap)
    AnduinCacheMemcached(
      name,
      memcachedClient,
      expiration,
      makeKey,
      retrieve,
      retrieveMulti,
      mustRevalidateWhen,
      tracingEnvironment
    )
  }

  def makeMulti[K, V: BinaryModel](
    name: String,
    expiration: Duration,
    makeKey: K => Task[String],
    retrieve: K => Task[Option[V]],
    retrieveMulti: Seq[K] => Task[Map[K, V]],
    mustRevalidateWhen: (K, V) => Boolean = (_: K, _: V) => false
  ): AnduinCache[K, V] = {
    AnduinCacheMemcached(
      name,
      memcachedClient,
      expiration,
      makeKey,
      retrieve,
      retrieveMulti,
      mustRevalidateWhen,
      tracingEnvironment
    )
  }

}
