// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.cache

import scala.concurrent.duration.Duration

import io.opentelemetry.api.trace.SpanKind
import zio.{Task, ZIO}

import anduin.memcached.MemcachedClient
import anduin.model.BinaryModel
import com.anduin.stargazer.service.utils.{ZIOTelemetryUtils, ZIOUtils}
import zio.metrics.Metric
import zio.telemetry.opentelemetry.tracing.Tracing

import anduin.metric.ZIOMetrics
import anduin.telemetry.TelemetryEnvironment

private[cache] final case class AnduinCacheMemcached[K, V: BinaryModel](
  name: String,
  memcachedClient: MemcachedClient,
  expiration: Duration,
  makeKey: K => Task[String],
  retrieve: K => Task[Option[V]],
  retrieveMulti: Seq[K] => Task[Map[K, V]],
  mustRevalidateWhen: (K, V) => Boolean,
  tracingEnvironment: TelemetryEnvironment.Tracing
) extends AnduinCache[K, V] {

  private val memcacheKeyPrefix = "cache:"
  private val metricName = "gondor-cache"
  private val hitMetric = Metric.counter(metricName).tagged(ZIOMetrics.toLabels(Map("name" -> name, "op" -> "hit")))
  private val missMetric = Metric.counter(metricName).tagged(ZIOMetrics.toLabels(Map("name" -> name, "op" -> "miss")))

  private val refreshMetric =
    Metric.counter(metricName).tagged(ZIOMetrics.toLabels(Map("name" -> name, "op" -> "refresh")))

  private val invalidateMetric =
    Metric.counter(metricName).tagged(ZIOMetrics.toLabels(Map("name" -> name, "op" -> "invalidate")))

  private val tracingSpanName = "service/cache"
  private val tracingSpanKind = SpanKind.CLIENT
  private val tracingAttributePrefix = "service.data.cache"
  private val tracingAttributeOperation = s"$tracingAttributePrefix.operation"
  private val tracingAttributeName = s"$tracingAttributePrefix.name"
  private val tracingAttributeKey = s"$tracingAttributePrefix.key"
  private val tracingAttributeSize = s"$tracingAttributePrefix.size"

  private def injectTracing[R, E, A](attributes: Map[String, String])(effect: ZIO[R, E, A]): ZIO[R & Tracing, E, A] = {
    ZIOTelemetryUtils.traceWithChildSpan(
      tracingSpanName,
      tracingSpanKind,
      attributes
    )(effect)
  }

  private def createMemcachedKey(key: K): Task[String] = {
    makeKey(key).map(memcacheKey => s"$memcacheKeyPrefix$memcacheKey")
  }

  private def retrieveFromUpstream(key: K, memcacheKey: String): Task[Option[V]] = {
    for {
      valueFromRetrieverOpt <- retrieve(key)
      _ <- ZIOUtils.traverseOptionUnit(valueFromRetrieverOpt) { valueFromRetriever =>
        memcachedClient
          .set(memcacheKey, valueFromRetriever, Some(expiration))
          .catchAllCause { error =>
            ZIO.logWarningCause("Failed to save value to cache ", error)
          }
          .tap(_ => missMetric.increment)
      }
    } yield valueFromRetrieverOpt
  }

  override def getOpt(key: K): Task[Option[V]] = {
    val task = for {
      memcacheKey <- createMemcachedKey(key)
      valueFromCacheOpt <- memcachedClient.get(memcacheKey)
      valueOpt <- valueFromCacheOpt.fold {
        retrieveFromUpstream(key, memcacheKey)
      } { valueFromCache =>
        if (mustRevalidateWhen(key, valueFromCache)) {
          retrieveFromUpstream(key, memcacheKey)
        } else {
          ZIO.succeed(valueFromCacheOpt).tap(_ => hitMetric.increment)
        }
      }
    } yield valueOpt
    injectTracing(
      Map(
        tracingAttributeOperation -> "getOpt",
        tracingAttributeName -> name,
        tracingAttributeKey -> key.toString
      )
    )(task).provideEnvironment(tracingEnvironment.environment)
  }

  override def mget(keys: Seq[K]): Task[Map[K, V]] = {
    val task = for {
      memcacheKeyMap <- ZIO.foreach(keys)(key => createMemcachedKey(key).map(_ -> key)).map(_.toMap)
      valuesFromCache <- memcachedClient
        .mget(memcacheKeyMap.keys.toSeq)
        .map(
          _.map { case (rawKey, value) =>
            memcacheKeyMap.get(rawKey).map(_ -> value)
          }.flatten.toMap
        )
      keysNotInCache = keys.filterNot(valuesFromCache.contains)
      keysMustRevalidated = valuesFromCache.filter { case (key, value) =>
        mustRevalidateWhen(key, value)
      }.keySet
      keysToRetrieveFromBackend = (keysNotInCache ++ keysMustRevalidated).distinct
      valuesFromBackend <-
        if (keysToRetrieveFromBackend.isEmpty) {
          ZIO.succeed(Map.empty)
        } else {
          for {
            valuesFromBackend <- retrieveMulti(keysToRetrieveFromBackend)
            valuesToAddToCache <- ZIO.foreach(valuesFromBackend) { case (key, value) =>
              createMemcachedKey(key).map(_ -> value)
            }
            _ <- memcachedClient.mset(valuesToAddToCache).catchAllCause { error =>
              ZIO.logWarningCause("Failed to save value to cache ", error)
            }
          } yield valuesFromBackend
        }
      _ <- ZIO.when(valuesFromCache.nonEmpty)(hitMetric.incrementBy(valuesFromCache.size))
      _ <- ZIO.when(valuesFromBackend.nonEmpty)(missMetric.incrementBy(valuesFromBackend.size))
    } yield valuesFromCache ++ valuesFromBackend
    injectTracing(
      Map(
        tracingAttributeOperation -> "mget",
        tracingAttributeName -> name,
        tracingAttributeSize -> keys.size.toString
      )
    )(task).provideEnvironment(tracingEnvironment.environment)
  }

  override def refresh(key: K): Task[Unit] = {
    val task = for {
      memcacheKey <- createMemcachedKey(key)
      valueFromRetrieverOpt <- retrieve(key)
      _ <- valueFromRetrieverOpt.fold {
        memcachedClient.remove(memcacheKey)
      } { valueFromRetriever =>
        memcachedClient.set(memcacheKey, valueFromRetriever, Some(expiration))
      }
      _ <- refreshMetric.increment
    } yield ()
    injectTracing(
      Map(
        tracingAttributeOperation -> "refresh",
        tracingAttributeName -> name,
        tracingAttributeKey -> key.toString
      )
    )(task).provideEnvironment(tracingEnvironment.environment)
  }

  override def invalidate(key: K): Task[Unit] = {
    val task = for {
      memcacheKey <- createMemcachedKey(key)
      _ <- memcachedClient.remove(memcacheKey)
      _ <- invalidateMetric.increment
    } yield ()
    injectTracing(
      Map(
        tracingAttributeOperation -> "invalidate",
        tracingAttributeName -> name,
        tracingAttributeKey -> key.toString
      )
    )(task).provideEnvironment(tracingEnvironment.environment)
  }

}
