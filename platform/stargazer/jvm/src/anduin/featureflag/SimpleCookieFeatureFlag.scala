// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.featureflag

import anduin.service.RequestContext

// Naive implementation for testing CDN, will remove soon
object SimpleCookieFeatureFlag {

  private val cookieName = "stargazer-feature-flag"
  private val cookieValue = "on"

  def isEnabled(requestContext: RequestContext): Boolean = {
    requestContext.cookies.exists { case (name, value) =>
      name == cookieName && value == cookieValue
    }
  }

}
