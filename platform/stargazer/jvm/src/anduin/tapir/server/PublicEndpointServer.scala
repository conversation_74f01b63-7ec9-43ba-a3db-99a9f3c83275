// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.tapir.server

import scala.concurrent.duration.FiniteDuration

import sttp.model.headers.Cookie
import sttp.model.{<PERSON><PERSON>, <PERSON><PERSON>}
import sttp.tapir.PublicEndpoint
import sttp.tapir.ztapir.*
import zio.{Task, ZIO}

import anduin.service.{GeneralServiceException, PublicRequestContext}
import anduin.tapir.AnduinEndpointIO
import anduin.tapir.PublicEndpoints.BasePublicEndpoint
import anduin.tapir.server.EndpointServer.{PublicServerService, TapirServerService}
import com.anduin.stargazer.service.utils.{ZIOLoggingUtils, ZIOUtils}

abstract class PublicEndpointServer extends EndpointServer {

  protected def collectPublicContext[I, E, O](
    endpoint: PublicEndpoint[I, E, O, Any]
  ): PublicEndpoint[(I, PublicRequestContext, Uri), E, O, Any] = {
    endpoint
      .in(headers)
      .in(cookies)
      .in(AnduinEndpointIO.uri)
      .mapIn { v =>
        val ctx = PublicRequestContext(
          headers = v._2.map { r => r.name -> r.value },
          cookies = v._3.map { c => c.name -> c.value }
        )
        (v._1, ctx, v._4)
      } { v =>
        (
          v._1,
          v._2.headers.map { h => Header(h._1, h._2) }.toList,
          v._2.cookies.map(c => Cookie(c._1, c._2)).toList,
          v._3
        )
      }
  }

  protected def publicRoute[I, E, O](
    endpoint: BasePublicEndpoint[I, E, O],
    timeout: FiniteDuration = defaultTimeout
  )(
    impl: (I, PublicRequestContext) => Task[Either[E, O]]
  ): TapirServerService = {
    val service = interpreter.toService {
      collectPublicContext(endpoint.endpoint).zServerLogic[Any] { input =>
        val (params, ctx, uri) = input
        ZIOLoggingUtils.annotateRequest(uri, input._2.headers.map(h => Header(h._1, h._2)).toList) {
          injectTelemetry(uri, ctx)(
            ZIOUtils
              .timeout(timeout)(
                impl(params, ctx).orDie
                  .flatMap(ZIO.fromEither(_))
              )
              .tapDefect(cause => ZIO.logErrorCause(cause))
          )
            .provideEnvironment(tracingEnvironment.environment)
        }
      }
    }
    PublicServerService(
      service,
      endpoint
    )
  }

  protected def publicRouteCatchError[I, O](
    endpoint: BasePublicEndpoint[I, GeneralServiceException, O],
    timeout: FiniteDuration = defaultTimeout
  )(
    impl: (I, PublicRequestContext) => Task[O]
  ): TapirServerService = {
    val service = interpreter.toService {
      collectPublicContext(endpoint.endpoint).zServerLogic[Any] { input =>
        val (params, ctx, uri) = input
        ZIOLoggingUtils.annotateRequest(uri, input._2.headers.map(h => Header(h._1, h._2)).toList) {
          injectTelemetry(uri, ctx)(
            ZIOUtils.timeout(timeout)(impl(params, ctx)).tapDefect(cause => ZIO.logErrorCause(cause)).flatMapError {
              ex =>
                catchError(ex)
            }
          )
            .provideEnvironment(tracingEnvironment.environment)
        }
      }
    }
    PublicServerService(
      service,
      endpoint
    )
  }

  protected def publicRoutePure[I, E, O](
    endpoint: BasePublicEndpoint[I, E, O]
  )(
    impl: (I, PublicRequestContext) => Either[E, O]
  ): TapirServerService = {
    val service = interpreter.toService {
      collectPublicContext(endpoint.endpoint).zServerLogic[Any] { input =>
        val (params, ctx, uri) = input
        injectTelemetry(uri, ctx)(ZIO.fromEither(impl(params, ctx)).tapDefect(cause => ZIO.logErrorCause(cause)))
          .provideEnvironment(tracingEnvironment.environment)
      }
    }
    PublicServerService(
      service,
      endpoint
    )
  }

}
