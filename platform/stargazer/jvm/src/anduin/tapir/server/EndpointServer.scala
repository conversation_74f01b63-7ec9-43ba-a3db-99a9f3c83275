// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.tapir.server

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration

import sttp.capabilities.zio.ZioStreams
import sttp.client3.SttpClientException.TimeoutException
import sttp.model.Uri
import sttp.tapir.server.armeria.TapirService
import sttp.tapir.server.armeria.zio.ArmeriaZioServerInterpreter
import zio.{UIO, ZIO}

import anduin.asyncapiv2.execution.{AsyncApiHandler, AsyncApiWorkflowType}
import anduin.telemetry.TelemetryEnvironment.AggregatedTracing
import anduin.endpoints.server.ServerEndpointsLike
import anduin.service.{GeneralServiceException, RequestContext}
import anduin.tapir.AsyncEndpoint.AsyncAuthenticatedEndpoint
import anduin.tapir.AuthenticatedEndpoints.BaseAuthenticatedEndpoint
import anduin.tapir.MultiRegionEndpoints.BaseMultiRegionEndpoint
import anduin.tapir.PublicEndpoints.BasePublicEndpoint
import anduin.tapir.endpoint.AuthenticatedEndpointValidationParams
import anduin.telemetry.TelemetryEnvironment
import com.anduin.stargazer.service.utils.ZIOTelemetryUtils

abstract class EndpointServer {

  private val metricName = "endpoint"
  private val operationTag = "op"
  private val slowTime = Some(zio.Duration(5, TimeUnit.SECONDS).toNanos)

  protected lazy val defaultTimeout: FiniteDuration = ServerEndpointsLike.defaultTimeout

  protected given runtime: zio.Runtime[Any] = ServerEndpointsLike.runtime

  protected def interpreter: ArmeriaZioServerInterpreter[Any]

  protected def tracingEnvironment: TelemetryEnvironment.Tracing

  private def getOperation(uri: Uri): String = {
    uri.path.mkString("/")
  }

  protected final def injectTelemetry[R, E, A](
    uri: Uri,
    context: RequestContext
  )(
    effect: zio.ZIO[R, E, A]
  ): zio.ZIO[R & AggregatedTracing, E, A] = {
    val operation = getOperation(uri)
    ZIOTelemetryUtils
      .injectTracingToTapirWithContext(
        operation,
        Some(context)
      )(
        ZIOTelemetryUtils.injectMetrics(
          metricName,
          Map(operationTag -> operation),
          slowTime
        )(effect)
      )
  }

  protected def catchError(error: Throwable): UIO[GeneralServiceException] = {
    error match {
      case ex: GeneralServiceException =>
        ZIO.logErrorCause("GeneralServiceException: ", ex.toCause).as(ex)
      case ex: TimeoutException =>
        ZIO.logErrorCause(ex.toCause).as(GeneralServiceException("Request timeout"))
      case ex: Throwable =>
        ZIO.logErrorCause(s"Exception: ", ex.toCause).as(GeneralServiceException("Unknown server error"))
    }
  }

  protected def forwardError(error: Throwable): UIO[GeneralServiceException] = {
    ZIO.logErrorCause(s"Exception: ", error.toCause).as(GeneralServiceException(error.getMessage))
  }

}

object EndpointServer {

  type TapirZioService = TapirService[ZioStreams, zio.Task]

  sealed trait TapirServerService derives CanEqual {
    def tapirService: TapirZioService
  }

  sealed trait ValidatedServerService[Params <: AuthenticatedEndpointValidationParams] {

    def validator: AuthenticatedEndpointValidator[Params]
  }

  final case class PublicServerService(
    tapirService: TapirZioService,
    endpoint: BasePublicEndpoint[?, ?, ?]
  ) extends TapirServerService

  final case class CustomServerService(
    tapirService: TapirZioService
  ) extends TapirServerService

  final case class AuthenticatedServerService(
    tapirService: TapirZioService,
    endpoint: BaseAuthenticatedEndpoint[?, ?, ?]
  ) extends TapirServerService

  final case class PortalServerService(
    tapirService: TapirZioService,
    endpoint: BaseAuthenticatedEndpoint[?, ?, ?]
  ) extends TapirServerService

  final case class ValidatedPortalServerService[Params <: AuthenticatedEndpointValidationParams](
    tapirService: TapirZioService,
    endpoint: BaseAuthenticatedEndpoint[?, ?, ?],
    validator: AuthenticatedEndpointValidator[Params]
  ) extends TapirServerService
      with ValidatedServerService[Params]

  final case class ValidatedAuthenticationServerService[Params <: AuthenticatedEndpointValidationParams](
    tapirService: TapirZioService,
    endpoint: BaseAuthenticatedEndpoint[?, ?, ?],
    validator: AuthenticatedEndpointValidator[Params]
  ) extends TapirServerService
      with ValidatedServerService[Params]

  final case class EnvironmentValidatedAuthenticationServerService[Params <: AuthenticatedEndpointValidationParams](
    tapirService: TapirZioService,
    endpoint: BaseAuthenticatedEndpoint[?, ?, ?],
    validator: AuthenticatedEndpointValidator[Params]
  ) extends TapirServerService
      with ValidatedServerService[Params]

  final case class PublicApiServerService(
    tapirService: TapirZioService
  ) extends TapirServerService

  final case class MultiRegionServerService(
    tapirService: TapirZioService,
    endpoint: BaseMultiRegionEndpoint[?, ?, ?]
  ) extends TapirServerService

  final case class AsyncAuthenticatedServerService(
    tapirService: TapirZioService,
    endpoint: AsyncAuthenticatedEndpoint[?, ?, ?]
  ) extends TapirServerService

  final case class ValidatedAsyncAuthenticationServerService[Params <: AuthenticatedEndpointValidationParams](
    tapirService: TapirZioService,
    endpoint: AsyncAuthenticatedEndpoint[?, ?, ?],
    validator: AuthenticatedEndpointValidator[Params]
  ) extends TapirServerService
      with ValidatedServerService[Params]

  final case class AsyncTapirServerService(
    endpoint: AsyncAuthenticatedEndpoint[?, ?, ?],
    tapirServices: List[TapirServerService],
    handlerMap: Map[AsyncApiWorkflowType, AsyncApiHandler]
  )

}
