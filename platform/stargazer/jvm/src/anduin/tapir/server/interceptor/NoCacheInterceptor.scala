// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.tapir.server.interceptor

import sttp.model.headers.CacheDirective
import sttp.model.{Header, StatusCode}
import sttp.tapir.model.ServerRequest
import sttp.tapir.server.interceptor.{RequestInterceptor, RequestResult}
import sttp.tapir.server.model.ServerResponse
import zio.{Task, ZIO}

object NoCacheInterceptor {

  private def addNoCacheHeader[B](resp: ServerResponse[B]) = {
    resp.addHeaders(Seq(Header.cacheControl(CacheDirective.NoCache)))
  }

  val responseInterceptor: RequestInterceptor[Task] =
    RequestInterceptor.transformResult(
      new RequestInterceptor.RequestResultTransform[Task] {
        override def apply[B](request: ServerRequest, result: RequestResult[B]): Task[RequestResult[B]] = {
          result match {
            case RequestResult.Response(resp) =>
              val injectedResp = if (resp.code != StatusCode.Ok || request.pathSegments.headOption.contains("api")) {
                addNoCacheHeader(resp)
              } else {
                resp
              }
              ZIO.succeed(RequestResult.Response(injectedResp))
            case RequestResult.Failure(_) => ZIO.succeed(result)
          }
        }

      }
    )

}
