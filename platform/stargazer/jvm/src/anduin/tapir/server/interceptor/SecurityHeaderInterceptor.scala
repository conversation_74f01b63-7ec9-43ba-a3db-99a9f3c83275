// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.tapir.server.interceptor

import sttp.model.Header
import sttp.tapir.model.ServerRequest
import sttp.tapir.server.interceptor.{RequestInterceptor, RequestResult}
import sttp.tapir.server.model.ServerResponse
import zio.{Task, ZIO}

object SecurityHeaderInterceptor {

  private def addSecurityHeaders[B](resp: ServerResponse[B]) = {
    resp.addHeaders(Seq(Header("Referrer-Policy", "strict-origin"), Header("X-Content-Type-Options", "nosniff")))
  }

  val responseInterceptor: RequestInterceptor[Task] =
    RequestInterceptor.transformResult(
      new RequestInterceptor.RequestResultTransform[Task] {
        override def apply[B](request: ServerRequest, result: RequestResult[B]): Task[RequestResult[B]] = {
          result match {
            case RequestResult.Response(resp) =>
              ZIO.succeed(RequestResult.Response(addSecurityHeaders(resp)))
            case RequestResult.Failure(_) => ZIO.succeed(result)
          }
        }

      }
    )

}
