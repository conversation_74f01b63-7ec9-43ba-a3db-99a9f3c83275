// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.tapir.server

import zio.{Task, ZIO}
import anduin.model.common.user.UserId
import anduin.tapir.endpoint.{AuthenticatedEndpointValidationParams, EmptyEndpointValidationParams}

import scala.util.NotGiven

// scalafix:off DisableSyntax.contravariant

abstract class AuthenticatedEndpointValidator[-Params <: AuthenticatedEndpointValidationParams](
  val id: String
)(
  using NotGiven[Params =:= AuthenticatedEndpointValidationParams]
) {
  self =>

  def validate(params: Params, userId: UserId): Task[Unit]

  def check(params: Params, userId: UserId): Task[Boolean] = {
    validate(params, userId)
      .as(true)
      .catchAllCause(_ => ZIO.succeed(false))
  }

  def apply(params: Params, userId: UserId): Task[Unit] = validate(params, userId)

  def &&[OtherParams <: AuthenticatedEndpointValidationParams](other: AuthenticatedEndpointValidator[OtherParams])
    : AuthenticatedEndpointValidator[Params & OtherParams] = {
    new AuthenticatedEndpointValidator[Params & OtherParams](s"(${self.id} && ${other.id})") {
      override def validate(params: Params & OtherParams, userId: UserId) = {
        self.validate(params, userId).zipPar(other.validate(params, userId)).unit
      }
    }
  }

  def ||[OtherParams <: AuthenticatedEndpointValidationParams](other: AuthenticatedEndpointValidator[OtherParams])
    : AuthenticatedEndpointValidator[Params & OtherParams] = {
    new AuthenticatedEndpointValidator[Params & OtherParams](s"(${self.id} || ${other.id})") {
      override def validate(params: Params & OtherParams, userId: UserId) = {
        self.validate(params, userId).orElse(other.validate(params, userId))
      }
    }
  }

}

object AuthenticatedEndpointValidator {

  lazy val empty: AuthenticatedEndpointValidator[EmptyEndpointValidationParams] =
    new AuthenticatedEndpointValidator[EmptyEndpointValidationParams]("tapir.empty") {
      override def validate(params: EmptyEndpointValidationParams, userId: UserId): Task[Unit] = ZIO.unit
    }

}
