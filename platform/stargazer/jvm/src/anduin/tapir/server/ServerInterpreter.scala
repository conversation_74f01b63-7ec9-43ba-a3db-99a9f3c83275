// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.tapir.server

import anduin.tapir.server.interceptor.{NoCacheInterceptor, SecurityHeaderInterceptor}
import io.circe.Json
import sttp.model.Header
import sttp.tapir.DecodeResult.{Error, InvalidValue}
import sttp.tapir.DecodeResult.Error.JsonDecodeException
import sttp.tapir.server.armeria.zio.{ArmeriaZioServerInterpreter, ArmeriaZioServerOptions}
import sttp.tapir.server.interceptor.cors.CORSConfig.ExposedHeaders
import sttp.tapir.server.interceptor.cors.{CORSConfig, CORSInterceptor}
import sttp.tapir.server.interceptor.decodefailure.DefaultDecodeFailureHandler
import sttp.tapir.server.interceptor.log.{DefaultServerLog, ServerLog}
import zio.{Task, UIO, ZIO}
import anduin.telemetry.RequestId

object ServerInterpreter {

  private def debugLog(msg: String, exOpt: Option[Throwable]): UIO[Unit] =
    ZIO.succeed(exOpt match {
      case None     => scribe.debug(msg)
      case Some(ex) => scribe.debug(s"$msg: ", ex)
    })

  private val serverLog: ServerLog[Task] = DefaultServerLog(
    doLogWhenReceived = debugLog(_, None),
    doLogWhenHandled = debugLog,
    doLogAllDecodeFailures = debugLog,
    doLogExceptions = (msg: String, ex: Throwable) => ZIO.logErrorCause(s"$msg: ", ex.toCause),
    noLog = ZIO.unit
  )

  private val armeriaZioServerOptions: ArmeriaZioServerOptions[Task] = ArmeriaZioServerOptions.customiseInterceptors
    .decodeFailureHandler(
      DefaultDecodeFailureHandler(
        ctx =>
          DefaultDecodeFailureHandler.respond(ctx).map { (statusCode, headers) =>
            ctx.failure match {
              case _: InvalidValue =>
                (statusCode, headers.appended(Header("content-type", "application/json")))
              case _ =>
                (statusCode, headers)
            }
          },
        ctx =>
          ctx.failure match {
            case Error(_, JsonDecodeException(errors, _)) =>
              val message = errors.map(e => s"${e.msg} at ${e.path.map(_.name).mkString(", ")}").mkString("; ")
              Json
                .obj(
                  "message" -> Json.fromString(message),
                  "errorType" -> Json.fromString("ValidationResponse")
                )
                .toString
            case InvalidValue(errors) =>
              Json
                .obj(
                  "message" -> Json.fromString(
                    errors
                      .map(error =>
                        s"${error.path.map(_.name).mkString(".")}: ${error.customMessage.getOrElse("Invalid")}"
                      )
                      .mkString("; ")
                  ),
                  "errorType" -> Json.fromString("ValidationResponse")
                )
                .toString
            case _ =>
              DefaultDecodeFailureHandler.FailureMessages
                .failureSourceMessage(ctx.failingInput)
          },
        DefaultDecodeFailureHandler.failureResponse
      )
    )
    .serverLog(serverLog)
    .corsInterceptor(CORSInterceptor.customOrThrow(CORSConfig.default.copy(exposedHeaders = ExposedHeaders.All)))
    .prependInterceptor(RequestId.requestInterceptor)
    .addInterceptor(RequestId.responseInterceptor)
    .addInterceptor(NoCacheInterceptor.responseInterceptor)
    .addInterceptor(SecurityHeaderInterceptor.responseInterceptor)
    .options

  val armeriaZioServerInterpreter: ArmeriaZioServerInterpreter[Any] = ArmeriaZioServerInterpreter(
    armeriaZioServerOptions
  )

}
