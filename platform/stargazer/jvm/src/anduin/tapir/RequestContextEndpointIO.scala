// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.tapir

import sttp.model.Header
import sttp.model.headers.Cookie
import anduin.service.{AuthenticatedRequestContext, PublicRequestContext, RequestContext, ServiceActor}
import sttp.tapir.*

object RequestContextEndpointIO {

  def requestContext(): EndpointIO[RequestContext] =
    headers
      .and(cookies)
      .map { input =>
        PublicRequestContext(
          input._1.map(header => (header.name, header.value)),
          input._2.map(cookie => (cookie.name, cookie.value))
        )
      } { ctx =>
        val headers = ctx.headers.map(header => Header(header._1, header._2)).toList
        val cookies = ctx.cookies.map(cookie => Cookie(cookie._1, cookie._2)).toList
        headers -> cookies
      }

  def authenticatedContext(
    actor: ServiceActor = ServiceActor.defaultServiceActor
  ): EndpointIO[AuthenticatedRequestContext] =
    headers
      .and(cookies)
      .map { input =>
        AuthenticatedRequestContext(
          actor,
          input._1.map(header => (header.name, header.value)),
          input._2.map(cookie => (cookie.name, cookie.value))
        )
      } { ctx =>
        val headers = ctx.headers.map(header => Header(header._1, header._2)).toList
        val cookies = ctx.cookies.map(cookie => Cookie(cookie._1, cookie._2)).toList
        headers -> cookies
      }

}
