// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.tapir.client

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.{Duration, FiniteDuration}

import sttp.tapir.client.sttp.SttpClientInterpreter

object ClientEndpointGlobals {
  val clientInterpreter: SttpClientInterpreter = SttpClientInterpreter()
  val maxTimeOut: Duration = FiniteDuration(30, TimeUnit.SECONDS)
}
