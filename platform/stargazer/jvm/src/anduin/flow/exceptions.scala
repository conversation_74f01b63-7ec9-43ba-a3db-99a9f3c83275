// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.flow

import anduin.exception.BaseException

sealed abstract class EventHandlerException[Event] extends BaseException

final case class NoInitialTransitionException[Event](
  event: Event
) extends EventHandlerException[Event] {
  def message: String = s"There is no initial transition with event = $event."
}

final case class NoTransitionException[State, Event](
  state: State,
  event: Event
) extends EventHandlerException[Event] {
  def message: String = s"There is no transition between state = $state and event = $event."
}
