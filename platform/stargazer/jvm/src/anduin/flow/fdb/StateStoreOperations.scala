// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.flow.fdb

import com.apple.foundationdb.record.provider.foundationdb.FDBStoredRecord
import com.google.protobuf.Message

import anduin.fdb.record.model.{RecordIO, RecordReadTask, RecordTask}
import anduin.fdb.record.{FDBRecordEnum, FDBRecordStore}

/** Flow states are unique by key of type K. We need to pass separate types U and T because current EventHandler is
  * requiring a superclass which has to be a sealed trait and a subclass extending that super class.
  *
  * @tparam S
  *   Enum corresponding to state store's subspace.
  * @tparam K
  *   Key for the flow. E.g, DataRoomWorkflowId for each data room.
  * @tparam U
  *   Superclass of the state, which is flow's type parameter.
  * @tparam T
  *   Subclass of the state, which is the type loaded from database.
  */
abstract class StateStoreOperations[S <: FDBRecordEnum, K, U, T <: U](
  provider: StateStoreProviderCompanion[S, K, U, T]
) {

  import provider.{*, given}

  def store: FDBRecordStore[S]

  def add(state: T): RecordTask[FDBStoredRecord[Message]] = {
    store.create(state)
  }

  def upsert(state: T): RecordTask[FDBStoredRecord[Message]] = {
    store.upsert(state)
  }

  def getState(key: K): RecordReadTask[T] = {
    store.get(key)
  }

  def getStateOpt(key: K): RecordReadTask[Option[T]] = {
    store.getOpt(key)
  }

  def update(
    key: K
  )(
    func: Option[U] => Either[Throwable, U]
  ): RecordTask[T] = {
    for {
      currentStateOpt <- store.getOpt(key).toRecordIO
      updatedState <- func(currentStateOpt).fold[RecordTask[U]](
        error => RecordIO.fail(error),
        res => RecordIO.succeed(res)
      )
      zoomed = zoom(updatedState)
      _ <- currentStateOpt.fold {
        store.create(zoomed)
      } { _ =>
        store.updateWithoutCheckTypeChanged(zoomed)
      }
    } yield zoomed
  }

  def update(state: T): RecordTask[Unit] = {
    store.update(state).unit
  }

  def updateWithoutCheckTypeChanged(state: T): RecordTask[Unit] = {
    store.updateWithoutCheckTypeChanged(state).unit
  }

  def delete(key: K): RecordTask[Unit] = {
    store.delete(key).unit
  }

}
