// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.flow.fdb

import anduin.fdb.record.FDBRecordEnum

/** Flow states are unique by key of type K. We need to pass separate types U and T because current EventHandler is
  * requiring a superclass which has to be a sealed trait and a subclass extending that super class.
  *
  * @tparam S
  *   Enum corresponding to state store's subspace.
  * @tparam K
  *   Key for the flow. E.g, DataRoomWorkflowId for each data room.
  * @tparam U
  *   State type
  */
abstract class MultiStateStoreOperations[S <: FDBRecordEnum, K, U](
  provider: MultiStateStoreProviderCompanion[S, K, U]
) extends StateStoreOperations[S, K, U, U](provider)
