// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.flow.fdb

import anduin.fdb.record.{FDBRecordEnum, FDBStoreProviderCompanion}

/** Flow states are unique by key of type K. We need to pass separate types U and T because current EventHandler is
  * requiring a superclass which has to be a sealed trait and a subclass extending that super class.
  *
  * @tparam S
  *   Enum corresponding to state store's subspace.
  * @tparam K
  *   Key for the flow. E.g, DataRoomWorkflowId for each data room.
  * @tparam U
  *   Superclass of the state, which is flow's type parameter.
  * @tparam T
  *   Subclass of the state, which is the type loaded from database.
  */
trait StateStoreProviderCompanion[S <: FDBRecordEnum, K, U, T <: U] extends FDBStoreProviderCompanion[S] {

  def zoom(state: U): T

  given mapping: Mapping[K, T] = compiletime.deferred
}
