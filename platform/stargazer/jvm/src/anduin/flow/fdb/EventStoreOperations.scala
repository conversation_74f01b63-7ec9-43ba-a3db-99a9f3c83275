// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.flow.fdb

import com.apple.foundationdb.record.provider.foundationdb.FDBStoredRecord
import com.apple.foundationdb.record.{IsolationLevel, TupleRange}
import com.google.protobuf.Message

import anduin.fdb.record.model.{RecordReadTask, RecordTask}
import anduin.fdb.record.{FDBRecordEnum, FDBRecordStore}

/** Flow events are unique by key of type K and an incremental index.
  *
  * @tparam S
  *   Enum corresponding to event store's subspace.
  * @tparam K
  *   Key for the flow. E.g, DataRoomWorkflowId for each data room.
  * @tparam E
  *   Base type of events.
  * @tparam F
  *   Type of the first event.
  */
abstract class EventStoreOperations[S <: FDBRecordEnum, K, E, F <: E](
  provider: EventStoreProviderCompanion[S, K, E, F]
) {

  import provider.{*, given}

  def store: FDBRecordStore[S]

  def add(event: E): RecordTask[FDBStoredRecord[Message]] = {
    store.create(event)
  }

  def upsert(event: E): RecordTask[FDBStoredRecord[Message]] = {
    store.upsert(event)
  }

  def getLastIndexOpt(flowKey: K): RecordTask[Option[Long]] = {
    store.maxEverOpt(
      Indexes.ByFlowKey.maxEver,
      EventStoreProviderCompanion.SecondaryKey(flowKey),
      isolationLevel = IsolationLevel.SNAPSHOT
    )
  }

  def getFirstEvent(flowKey: K): RecordReadTask[F] = {
    store.get(EventStoreProviderCompanion.PrimaryKey(flowKey, 0), snapshot = true)(
      using firstEventMapping
    )
  }

  def getLastEvent(flowKey: K): RecordTask[E] = {
    for {
      lastIndex <- getLastIndexOpt(flowKey).map(_.getOrElse(0L))
      lastEvent <- store.get(EventStoreProviderCompanion.PrimaryKey(flowKey, lastIndex), snapshot = true)
    } yield lastEvent
  }

  def getAllEvents(
    flowKey: K
  ): RecordTask[List[(EventStoreProviderCompanion.PrimaryKey[K], E)]] = {
    store.scanL(
      mapping = allEventUnionMapping,
      tupleRange = TupleRange.allOf(flowKeyTupleConverter.toTuple(flowKey))
    )
  }

  def deleteEvent(key: EventStoreProviderCompanion.PrimaryKey[K]): RecordTask[Unit] = {
    store.delete[EventStoreProviderCompanion.PrimaryKey[K], E](key).unit
  }

}
