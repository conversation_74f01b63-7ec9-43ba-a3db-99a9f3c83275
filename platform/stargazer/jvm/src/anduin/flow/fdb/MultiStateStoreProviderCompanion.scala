// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.flow.fdb

import scala.util.{Failure, Try}

import com.google.protobuf.Message

import anduin.fdb.record.FDBRecordEnum
import anduin.fdb.record.model.{FDBRecordModel, FDBTupleConverter}

/** Flow states are unique by key of type K. We need to pass separate types U and T because current EventHandler is
  * requiring a superclass which has to be a sealed trait and a subclass extending that super class.
  *
  * @tparam S
  *   Enum corresponding to state store's subspace.
  * @tparam K
  *   Key for the flow. E.g, DataRoomWorkflowId for each data room.
  * @tparam U
  *   State type
  */
trait MultiStateStoreProviderCompanion[S <: FDBRecordEnum, K, U] extends StateStoreProviderCompanion[S, K, U, U] {

  def zoom(state: U): U = state

  protected def allCases: List[Mapping[K, ? <: U]]

  protected def toJavaMessage: U => Message

  given allStateRecordModel: FDBRecordModel[U] = {
    val modelMap = {
      for {
        mapping <- allCases
        typeName <- mapping.recordModel.typeNames
      } yield typeName -> mapping.recordModel
    }.toMap
    FDBRecordModel[U](allCases.flatMap(_.recordModel.typeNames)) {
      toJavaMessage
    } { javaMsg =>
      val typeName = javaMsg.getDescriptorForType.getName
      modelMap
        .get(typeName)
        .fold[Try[U]] {
          Failure(new RuntimeException(s"Unable to find correct FDBRecordModel for type $typeName"))
        } { recordModel =>
          recordModel.fromJavaMessage(javaMsg)
        }
    }
  }

  given primaryKeyTupleConverter: FDBTupleConverter[K] = compiletime.deferred

  override given mapping: Mapping[K, U] = mappingInstance
}
