// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.flow.fdb

import scala.util.{Failure, Try}

import com.apple.foundationdb.record.metadata.expressions.KeyExpression
import com.apple.foundationdb.record.metadata.{Index, IndexTypes, Key}
import com.google.protobuf.Message

import anduin.fdb.record.model.{FDBRecordModel, FDBTupleConverter}
import anduin.fdb.record.{FDBRecordEnum, FDBStoreProviderCompanion}

/** Flow events are unique by key of type K and an incremental index.
  *
  * @tparam S
  *   Enum corresponding to event store's subspace.
  * @tparam K
  *   Key for the flow. E.g, DataRoomWorkflowId for each data room.
  * @tparam E
  *   Base type of events.
  * @tparam F
  *   Type of the first event.
  */
trait EventStoreProviderCompanion[S <: FDBRecordEnum, K, E, F <: E] extends FDBStoreProviderCompanion[S] {

  type PrimaryKey = EventStoreProviderCompanion.PrimaryKey[K]

  def Indexes: BaseIndexes

  protected def flowKeyExpression: KeyExpression

  given flowKeyTupleConverter: FDBTupleConverter[K] = compiletime.deferred

  def firstEventMapping: Mapping[PrimaryKey, F]

  private val indexKeyExpression = scalarFieldNotNull("index")

  def primaryKeyKeyExpression: KeyExpression =
    Key.Expressions.concat(
      flowKeyExpression,
      indexKeyExpression
    )

  protected def allCases: List[Mapping[PrimaryKey, ? <: E]]

  protected def toJavaMessage: E => Message

  given allEventUnionModel: FDBRecordModel[E] = {
    val modelMap = {
      for {
        mapping <- allCases
        typeName <- mapping.recordModel.typeNames
      } yield typeName -> mapping.recordModel
    }.toMap
    FDBRecordModel[E](allCases.flatMap(_.recordModel.typeNames)) {
      toJavaMessage
    } { javaMsg =>
      val typeName = javaMsg.getDescriptorForType.getName
      modelMap
        .get(typeName)
        .fold[Try[E]] {
          Failure(new RuntimeException(s"Unable to find correct FDBRecordModel for type $typeName"))
        } { recordModel =>
          recordModel.fromJavaMessage(javaMsg)
        }
    }
  }

  given allEventUnionMapping: Mapping[PrimaryKey, E] = mappingInstance

  trait BaseIndexes {

    object ByFlowKey {

      type SecondaryKey = EventStoreProviderCompanion.SecondaryKey[K]

      val maxEver: AggregateMapping[SecondaryKey, E, Long] = {
        aggregateMappingInstance(
          new Index(
            "max_ever_index",
            indexKeyExpression.groupBy(flowKeyExpression),
            IndexTypes.MAX_EVER_TUPLE
          )
        )
      }

      def getMaxEverByType[A <: E](
        using recordModel: FDBRecordModel[A]
      ): AggregateMapping[SecondaryKey, A, Long] = {
        val typeName = recordModel.typeNames.mkString("_")
        aggregateMappingInstance(
          new Index(
            s"_max_ever_index_$typeName",
            indexKeyExpression.groupBy(flowKeyExpression),
            IndexTypes.MAX_EVER_TUPLE
          )
        )
      }

    }

  }

}

object EventStoreProviderCompanion {

  final case class PrimaryKey[K](flowKey: K, index: Long)

  final case class SecondaryKey[K](flowKey: K)
}
