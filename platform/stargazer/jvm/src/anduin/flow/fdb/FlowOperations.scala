// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.flow.fdb

import java.time.Instant

import anduin.fdb.record.FDBRecordEnum
import anduin.fdb.record.model.RecordTask
import anduin.flow.EventHandler

abstract class FlowOperations[SS <: FDBRecordEnum, ES <: FDBRecordEnum, K, U, T <: U, E, F <: E](
  stateOps: StateStoreOperations[SS, K, U, T],
  eventOps: EventStoreOperations[ES, K, E, F],
  flowHandler: EventHandler[U, E]
) {

  def executeEvent(
    flowKey: K
  )(
    getEvent: (Long, Option[Instant]) => E
  ): RecordTask[T] = {
    for {
      lastIndexOpt <- eventOps.getLastIndexOpt(flowKey)
      index = lastIndexOpt.fold(0L)(_ + 1)
      event = getEvent(index, Some(Instant.now))
      _ <- eventOps.add(event)
      res <- stateOps.update(flowKey) { currentStateOpt =>
        flowHandler(
          event,
          currentStateOpt,
          skipBadEvents = false
        )
      }
    } yield res
  }

}
