// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.flow

import scala.annotation.tailrec

import zio.prelude.NonEmptyList

abstract class EventHandler[State, Event] {

  protected final type Result = Either[EventHandlerException[Event], State]

  protected def initialState: Event => Result
  protected def nextState: State => Event => Result

  private def resumeBadState(
    result: Result,
    skipBadEvents: Boolean
  ): Result = {
    if (skipBadEvents) {
      result.fold(
        {
          // Fallback to the previous state if case of a bad event
          case badTransitionExp: NoTransitionException[State @unchecked, Event @unchecked] =>
            Right(badTransitionExp.state)
          // Not applied to the first event
          case ex: NoInitialTransitionException[Event] => Left(ex)
        },
        Right.apply
      )
    } else {
      result
    }
  }

  def apply(
    event: Event,
    state: Option[State] = None,
    skipBadEvents: Boolean
  ): Result = {
    val result = state.fold(initialState)(nextState)(event)
    resumeBadState(result, skipBadEvents)
  }

  @tailrec
  final def fold(
    events: NonEmptyList[Event],
    state: Option[State] = None,
    skipBadEvents: Boolean
  ): Result = {
    val nextResult = apply(
      events.head,
      state,
      skipBadEvents
    )
    nextResult match {
      case Left(_) => nextResult
      case Right(nextState) => {
        events.tailNonEmpty match {
          case None => nextResult
          case Some(restEvents) =>
            fold(
              restEvents,
              Some(nextState),
              skipBadEvents
            )
        }
      }
    }
  }

}
