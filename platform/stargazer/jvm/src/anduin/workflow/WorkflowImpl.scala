// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow

import java.util.UUID
import scala.reflect.ClassTag

import zio.temporal.schedules.*
import zio.temporal.worker.ZWorkerFactory
import zio.temporal.workflow.*
import zio.{URIO, ZIO}

import anduin.model.id.TemporalWorkflowId
import anduin.workflow.common.{TemporalWorkflow, TemporalWorkflowCompanion}

// scalafix:off DisableSyntax.asInstanceOf
sealed abstract class WorkflowImpl[
  Interface: {ClassTag, IsWorkflow},
  Implementation <: Interface: {ClassTag, ExtendsWorkflow, IsConcreteClass, HasPublicNullaryConstructor}
](
  val queue: WorkflowQueue
) {

  val queueName: String = queue.queueName

  val interfaceClass: Class[Interface] = summon[ClassTag[Interface]].runtimeClass.asInstanceOf[Class[Interface]]

  val implClass: Class[Implementation] =
    summon[ClassTag[Implementation]].runtimeClass.asInstanceOf[Class[Implementation]]

  def register(factory: ZWorkerFactory): ZIO[Any, Throwable, Unit] = {
    for {
      workerOpt <- factory.getWorker(queueName)
      worker <- ZIO.getOrFailWith(new NoSuchElementException(s"No worker for queue ${queueName}"))(workerOpt)
      _ <- worker.addWorkflow[Implementation].fromClass(implClass)
    } yield ()
  }

  def getWorkflowStub(
    workflowId: TemporalWorkflowId = TemporalWorkflowId.unsafeFromSuffix(UUID.randomUUID().toString),
    transformWorkflowOptions: ZWorkflowOptions => ZWorkflowOptions = identity
  ): URIO[ZWorkflowClient, ZWorkflowStub.Of[Interface]] = {
    ZIO.serviceWithZIO[ZWorkflowClient](client =>
      client.newWorkflowStub[Interface](
        transformWorkflowOptions(ZWorkflowOptions.withWorkflowId(workflowId.idString).withTaskQueue(queueName))
      )
    )
  }

  def getScheduleWorkflowStub(
    workflowId: TemporalWorkflowId = TemporalWorkflowId.unsafeFromSuffix(UUID.randomUUID().toString),
    transformWorkflowOptions: ZWorkflowOptions => ZWorkflowOptions = identity
  ): URIO[ZScheduleClient, ZScheduleStartWorkflowStub.Of[Interface]] = {
    ZIO.serviceWith[ZScheduleClient](client =>
      client.newScheduleStartWorkflowStub[Interface](
        transformWorkflowOptions(ZWorkflowOptions.withWorkflowId(workflowId.idString).withTaskQueue(queueName))
      )
    )
  }

}

object WorkflowImpl {

  def apply[
    Interface: {ClassTag, IsWorkflow},
    Implementation <: Interface: {ClassTag, ExtendsWorkflow, IsConcreteClass, HasPublicNullaryConstructor}
  ](
    queue: WorkflowQueue
  ): WorkflowImpl[Interface, Implementation] = {
    new WorkflowImpl[Interface, Implementation](queue) {}
  }

  def derived[
    Interface <: TemporalWorkflow[?, ?]: {ClassTag, IsWorkflow},
    Implementation <: Interface: {ClassTag, ExtendsWorkflow, IsConcreteClass, HasPublicNullaryConstructor}
  ](
    using companion: TemporalWorkflowCompanion[Interface]
  ): WorkflowImpl[Interface, Implementation] = {
    new WorkflowImpl[Interface, Implementation](companion.workflowQueue) {}
  }

  def derived[
    Interface: {ClassTag, IsWorkflow},
    Implementation <: Interface: {ClassTag, ExtendsWorkflow, IsConcreteClass, HasPublicNullaryConstructor}
  ](
    workflowQueue: WorkflowQueue
  ): WorkflowImpl[Interface, Implementation] = {
    new WorkflowImpl[Interface, Implementation](workflowQueue) {}
  }

}
