// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.cron

import scala.concurrent.duration.FiniteDuration

import zio.{Task, ZIO}
import zio.temporal.ZRetryOptions
import zio.temporal.schedules.{
  ZSchedule,
  ZScheduleClient,
  ZScheduleHandle,
  ZSchedulePolicy,
  ZScheduleSpec,
  ZScheduleStartWorkflowStub,
  ZScheduleUpdate
}

import anduin.temporal.TemporalEnvironment
import anduin.workflow.WorkflowImpl
import com.anduin.stargazer.service.GondorConfig
import scala.jdk.DurationConverters.*

import io.temporal.api.enums.v1.ScheduleOverlapPolicy
import io.temporal.client.schedules.ScheduleAlreadyRunningException

final case class TemporalScheduleUtils(
  gondorConfig: GondorConfig
)(
  using temporalEnvironment: TemporalEnvironment
) {

  private val cronConfig = gondorConfig.backendConfig.temporalCron

  private def updateSchedule(
    scheduleId: String,
    schedule: ZSchedule,
    workflowType: String
  ) = {
    for {
      handle <- getSchedule(scheduleId)
      _ <- handle.update { _ =>
        ZScheduleUpdate(schedule)
      }
      _ <- ZIO.logInfo(
        s"Updated temporal schedule ${scheduleId}, action: ${workflowType}, spec: ${schedule.spec}"
      )
    } yield handle
  }

  def getSchedule(scheduleId: String): Task[ZScheduleHandle] = {
    ZIO
      .serviceWithZIO[ZScheduleClient](
        _.getHandle(scheduleId)
      )
      .provideEnvironment(temporalEnvironment.scheduleClient)
  }

  def triggerSchedule(
    scheduleId: String,
    overlapPolicy: ScheduleOverlapPolicy = ScheduleOverlapPolicy.SCHEDULE_OVERLAP_POLICY_ALLOW_ALL
  ) = {
    for {
      handle <- getSchedule(scheduleId)
      _ <- handle.trigger(Some(overlapPolicy))
    } yield handle
  }

  inline def startSchedule[Interface, Implementation <: Interface](
    scheduleId: String,
    scheduleSpec: ZScheduleSpec,
    instance: WorkflowImpl[Interface, Implementation],
    workflowRunTimeout: FiniteDuration = cronConfig.workflowTaskTimeout,
    maximumAttempts: Int = 1,
    updateIfRunning: Boolean = true,
    policy: ZSchedulePolicy = ZSchedulePolicy.default
  )(
    inline start: ZScheduleStartWorkflowStub.Of[Interface] => Any
  ): Task[ZScheduleHandle] = {
    val workflowType = instance.interfaceClass.getSimpleName
    val task = for {
      scheduleClient <- ZIO.service[ZScheduleClient]
      workflowStub <- instance.getScheduleWorkflowStub(
        transformWorkflowOptions = _.withRetryOptions(ZRetryOptions.default.withMaximumAttempts(maximumAttempts))
          .withWorkflowTaskTimeout(cronConfig.workflowTaskTimeout.toJava)
          .withWorkflowRunTimeout(workflowRunTimeout.toJava)
      )
      schedule = ZSchedule
        .withAction(ZScheduleStartWorkflowStub.start(start(workflowStub)))
        .withSpec(scheduleSpec)
        .withPolicy(policy)
      handle <- scheduleClient
        .createSchedule(scheduleId, schedule)
        .catchSome { case error: ScheduleAlreadyRunningException =>
          for {
            _ <- ZIO.logWarning(s"Temporal schedule ${scheduleId} is already running")
            handle <-
              if (updateIfRunning) {
                updateSchedule(scheduleId, schedule, workflowType)
              } else {
                ZIO.fail(error)
              }
          } yield handle
        }
      _ <- ZIO.logInfo(s"Started temporal schedule $scheduleId on queue ${instance.queueName}")
    } yield handle
    task
      .provideEnvironment(temporalEnvironment.scheduleClient)
      .tapErrorCause { error =>
        ZIO.logErrorCause(
          s"Failed to start temporal schedule ${scheduleId}, action: ${workflowType}, spec: $scheduleSpec",
          error
        )
      }
  }

  def pauseSchedule(scheduleId: String, note: Option[String] = None): Task[Unit] = {
    getSchedule(scheduleId)
      .flatMap(_.pause(note))
      .tapErrorCause(ex => ZIO.logErrorCause(s"Failed to pause temporal schedule $scheduleId, note = $note", ex))
      .tap(_ => ZIO.logInfo(s"Paused temporal schedule $scheduleId, note = $note"))
  }

  def unpauseSchedule(scheduleId: String, note: Option[String] = None): Task[Unit] = {
    getSchedule(scheduleId)
      .flatMap(_.unpause(note))
      .tapErrorCause(ex => ZIO.logErrorCause(s"Failed to unpause temporal schedule $scheduleId, note = $note", ex))
      .tap(_ => ZIO.logInfo(s"Unpaused temporal schedule $scheduleId, note = $note"))
  }

  def deleteSchedule(scheduleId: String): Task[Unit] = {
    getSchedule(scheduleId)
      .flatMap(_.delete())
      .tapErrorCause(ex => ZIO.logErrorCause(s"Failed to delete temporal schedule $scheduleId", ex))
      .tap(_ => ZIO.logInfo(s"Deleted temporal schedule $scheduleId"))
  }

  inline def startEvery10Min[Interface, Implementation <: Interface](
    scheduleId: String,
    instance: WorkflowImpl[Interface, Implementation],
    workflowRunTimeout: FiniteDuration = cronConfig.workflowRunTimeout,
    updateIfRunning: Boolean = true
  )(
    inline start: ZScheduleStartWorkflowStub.Of[Interface] => Any
  ): Task[ZScheduleHandle] = {
    startSchedule(
      scheduleId,
      ZScheduleSpec.cronExpressions(cronConfig.cron10MinSchedule),
      instance,
      workflowRunTimeout,
      updateIfRunning = updateIfRunning
    )(start)
  }

  inline def startHourly[Interface, Implementation <: Interface](
    scheduleId: String,
    instance: WorkflowImpl[Interface, Implementation],
    workflowRunTimeout: FiniteDuration = cronConfig.workflowRunTimeout,
    updateIfRunning: Boolean = true
  )(
    inline start: ZScheduleStartWorkflowStub.Of[Interface] => Any
  ): Task[ZScheduleHandle] = {
    startSchedule(
      scheduleId,
      ZScheduleSpec.cronExpressions(cronConfig.hourlySchedule),
      instance,
      workflowRunTimeout,
      updateIfRunning = updateIfRunning
    )(start)
  }

  inline def startFirstMinuteOfEveryHour[Interface, Implementation <: Interface](
    scheduleId: String,
    instance: WorkflowImpl[Interface, Implementation],
    workflowRunTimeout: FiniteDuration = cronConfig.workflowRunTimeout,
    updateIfRunning: Boolean = true
  )(
    inline start: ZScheduleStartWorkflowStub.Of[Interface] => Any
  ): Task[ZScheduleHandle] = {
    startSchedule(
      scheduleId,
      ZScheduleSpec.cronExpressions(cronConfig.firstMinuteOfEveryHourSchedule),
      instance,
      workflowRunTimeout,
      updateIfRunning = updateIfRunning
    )(start)
  }

  inline def startDaily[Interface, Implementation <: Interface](
    scheduleId: String,
    instance: WorkflowImpl[Interface, Implementation],
    workflowRunTimeout: FiniteDuration = cronConfig.workflowRunTimeout,
    updateIfRunning: Boolean = true
  )(
    inline start: ZScheduleStartWorkflowStub.Of[Interface] => Any
  ): Task[ZScheduleHandle] = {
    startSchedule(
      scheduleId,
      ZScheduleSpec.cronExpressions(cronConfig.dailySchedule),
      instance,
      workflowRunTimeout,
      updateIfRunning = updateIfRunning
    )(start)
  }

  inline def startWeekly[Interface, Implementation <: Interface](
    scheduleId: String,
    instance: WorkflowImpl[Interface, Implementation],
    workflowRunTimeout: FiniteDuration = cronConfig.workflowRunTimeout,
    updateIfRunning: Boolean = true
  )(
    inline start: ZScheduleStartWorkflowStub.Of[Interface] => Any
  ): Task[ZScheduleHandle] = {
    startSchedule(
      scheduleId,
      ZScheduleSpec.cronExpressions(cronConfig.weeklySchedule),
      instance,
      workflowRunTimeout,
      updateIfRunning = updateIfRunning
    )(start)
  }

}
