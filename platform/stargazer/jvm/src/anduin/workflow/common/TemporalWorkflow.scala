// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.common

import java.time.Duration
import java.util.UUID
import scala.reflect.ClassTag

import io.temporal.client.WorkflowOptions
import zio.temporal.workflow.*

import anduin.model.id.TemporalWorkflowId
import anduin.workflow.data.TemporalData
import anduin.workflow.{WorkflowQueue, WorkflowTask}

// scalafix:off
trait TemporalWorkflow[I: {TemporalData, ClassTag}, O: {TemporalData, ClassTag}] {

  protected def runAsync(input: I): WorkflowTask[O]

  def run(input: I): O

}

trait TemporalWorkflowCompanion[Interface <: TemporalWorkflow[?, ?]: {ClassTag, IsWorkflow}] {

  def workflowQueue: WorkflowQueue

  given TemporalWorkflowCompanion[Interface] = this

  val workflowName: String = summon[ClassTag[Interface]].toString

  def workflowId(presetOpt: Option[TemporalWorkflowId] = None): TemporalWorkflowId = {
    presetOpt.getOrElse(TemporalWorkflowId.unsafeFromSuffix(UUID.randomUUID().toString))
  }

  val workflowTaskTimeout: Option[Duration] = Some(Duration.ofSeconds(60))

  val workflowRunTimeout: Option[Duration] = Some(Duration.ofMinutes(60))

  val workflowExecutionTimeout: Option[Duration] = Some(Duration.ofMinutes(60))

  val maximumRetryAttempts: Int = 1

  def transformJavaOptions: WorkflowOptions.Builder => WorkflowOptions.Builder = identity
}

type TemporalWorkflowStub[+T] = ZWorkflowStub.Of[T]

type TemporalChildWorkflowStub[+T] = ZChildWorkflowStub.Of[T]

type TemporalUntypedWorkflowStub = ZWorkflowStub.Untyped

trait TemporalWorkflowImplCompanion[
  Interface <: TemporalWorkflow[?, ?]: {ClassTag, IsWorkflow},
  Implementation <: Interface: {ClassTag, ExtendsWorkflow, IsConcreteClass, HasPublicNullaryConstructor}
]
