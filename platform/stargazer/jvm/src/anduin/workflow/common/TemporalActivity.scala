// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.common

import java.time.Duration

import zio.temporal.activity.ZActivityStub

import anduin.workflow.ActivityQueue

trait TemporalActivity

// scalafix:off
type TemporalActivityStub[+T] = ZActivityStub.Of[T]

trait TemporalActivityCompanion[T <: TemporalActivity](val activityQueue: ActivityQueue) {

  given TemporalActivityCompanion[T] = this

  val startToCloseTimeout: Duration = Duration.ofMinutes(60)

  val maximumRetryAttempts: Int = 1

  val heartbeatTimeout: Option[Duration] = None

}
