package anduin.workflow.common

import anduin.id.ModelIdRegistry
import anduin.radix.RadixId
import com.google.protobuf.ByteString
import io.temporal.api.common.v1.Payload
import io.temporal.common.converter.{EncodingKeys, PayloadConverter}

import java.lang.reflect.Type
import java.nio.charset.StandardCharsets
import java.util as ju

class RadixIdDataConverter extends PayloadConverter {

  override val getEncodingType: String = "binary/radixId"
  private val encodingMetaValue = ByteString.copyFrom(getEncodingType, StandardCharsets.UTF_8)

  override def toData(value: Any): ju.Optional[Payload] = {
    val matchable = value.asInstanceOf[Matchable] // scalafix:ok DisableSyntax.asInstanceOf
    matchable match {
      case radixId: RadixId =>
        val payload = Payload
          .newBuilder()
          .putMetadata(EncodingKeys.METADATA_ENCODING_KEY, encodingMetaValue)
          .putMetadata(
            EncodingKeys.METADATA_MESSAGE_TYPE_KEY,
            ByteString.copyFrom(radixId.getClass.getTypeName, StandardCharsets.UTF_8)
          )
          .setData(
            ByteString.copyFrom(radixId.idString, StandardCharsets.UTF_8)
          )
          .build()

        ju.Optional.of(payload)
      case _ => ju.Optional.empty()
    }
  }

  override def fromData[T](content: Payload, valueType: Class[T], valueGenericType: Type): T = {
    val idStr = content.getData.toString(StandardCharsets.UTF_8)

    ModelIdRegistry.parser
      .parse(idStr)
      .fold {
        throw new IllegalStateException(s"Cannot deserialized payload [$idStr] to RadixId")
      } { radixId =>
        val typeName = radixId.getClass.getTypeName
        if (typeName == valueType.getTypeName) {
          radixId.asInstanceOf[T] // scalafix:ok DisableSyntax.asInstanceOf
        } else {
          throw new IllegalStateException(
            s"Invalid type for id $radixId. Expected ${valueType.getTypeName}, found $typeName"
          )
        }
      }
  }

}
