// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.common

import com.uber.m3.tally.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Scope}
import io.micrometer.core.instrument.MeterRegistry
import io.opentelemetry.api
import io.opentelemetry.opentracingshim.OpenTracingShim
import io.temporal.common.reporter.MicrometerClientStatsReporter
import io.temporal.opentracing.{OpenTracingClientInterceptor, OpenTracingOptions}
import zio.temporal.schedules.{ZScheduleClient, ZScheduleClientOptions}
import zio.temporal.workflow.*
import zio.{Config, ZLayer}

import com.anduin.stargazer.service.GondorBackendConfig.TemporalConfig

object AnduinZWorkflowClient {

  @scala.annotation.unused
  private def getTracingInterceptor(sdk: api.OpenTelemetry) = {
    val tracer = OpenTracingShim.createTracerShim(sdk)
    val tracingOption = OpenTracingOptions.newBuilder().setTracer(tracer).build()
    new OpenTracingClientInterceptor(tracingOption)
  }

  def make: <PERSON><PERSON><PERSON><PERSON>[
    ZWorkflowServiceStubs & TemporalConfig,
    Config.<PERSON>r,
    ZWorkflowClient
  ] = {
    val clientOptions = ZLayer.environment[TemporalConfig].flatMap { environment =>
      val temporalConfig = environment.get[TemporalConfig]
      ZWorkflowClientOptions.make @@
        ZWorkflowClientOptions.withNamespace(temporalConfig.namespace) @@
        ZWorkflowClientOptions.withDataConverter(AnduinTemporalDataConverter.make())
    }
    clientOptions >>> ZWorkflowClient.make
  }

}

object AnduinZWorkflowServiceStubs {

  private def metricScopeLayer: ZLayer[MeterRegistry, Nothing, Scope] = {
    ZLayer.service[MeterRegistry].project { metricRegistry =>
      val metricReporter = new MicrometerClientStatsReporter(metricRegistry)
      RootScopeBuilder()
        .reporter(metricReporter)
        .reportEvery(com.uber.m3.util.Duration.ofSeconds(1))
    }
  }

  def make: ZLayer[
    MeterRegistry & TemporalConfig,
    Config.Error,
    ZWorkflowServiceStubs
  ] = {
    (metricScopeLayer ++ ZLayer.service[TemporalConfig]).flatMap { environment =>
      val scope = environment.get[Scope]
      val temporalConfig = environment.get[TemporalConfig]
      val serviceOptions = ZWorkflowServiceStubsOptions.make @@
        ZWorkflowServiceStubsOptions.withServiceUrl(s"${temporalConfig.host}:${temporalConfig.port}") @@
        ZWorkflowServiceStubsOptions.withMetricsScope(scope)
      serviceOptions >>> ZWorkflowServiceStubs.make
    }
  }

}

object AnduinZScheduleClient {

  def make: ZLayer[
    ZWorkflowServiceStubs & TemporalConfig,
    Config.Error,
    ZScheduleClient
  ] = {
    val clientOptions = ZLayer.service[TemporalConfig].flatMap { environment =>
      val temporalConfig = environment.get[TemporalConfig]
      ZScheduleClientOptions.make @@
        ZScheduleClientOptions.withNamespace(temporalConfig.namespace) @@
        ZScheduleClientOptions.withDataConverter(AnduinTemporalDataConverter.make())
    }

    clientOptions >>> ZScheduleClient.make
  }

}
