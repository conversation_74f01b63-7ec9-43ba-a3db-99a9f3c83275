// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow

import com.anduin.stargazer.utils.DeploymentUtils
import zio.temporal.worker.ZWorkerOptions

enum ActivityQueue(val workerOptions: ZWorkerOptions = WorkerOptions.default) {

  def entryName: String =
    productPrefix + "$_activity" + DeploymentUtils.tenantSuffix

  override def toString = entryName

  def queueName: String = entryName

  case TemporalLock extends ActivityQueue()

  case BatchUpload extends ActivityQueue()

  case FundSubInvitation extends ActivityQueue()

  case FundSubBillingExport extends ActivityQueue()

  case AuditLogExport extends ActivityQueue()

  case EmailExport extends ActivityQueue()

  case EmailExportForFunds extends ActivityQueue()

  case DataRoomExport extends ActivityQueue()

  case InvestorAccessAnalyticExport extends ActivityQueue()

  case FundSubIAConfigExport extends ActivityQueue()

  case CommentAnalyticExportActivities extends ActivityQueue()

  case FundFormFieldSourceAnalyticsExportActivities extends ActivityQueue()

  case FundSubPermissionAnalyticsExportActivities extends ActivityQueue()

  case FundDataLandingPageAnalyticsExport extends ActivityQueue()

  case CommentExport extends ActivityQueue()

//  case ActionEventExport extends ZActivityQueue()

  case FormSimilarityAnalytic extends ActivityQueue()

  case FormAsaAnalytic extends ActivityQueue()

  case InvestorDataExport extends ActivityQueue()

  case PrefillAnalytic extends ActivityQueue()

  case UserDataExport extends ActivityQueue()

  case FundSubDataImport extends ActivityQueue()

  case FundSubDataExport extends ActivityQueue()

  case FundSubCountersign extends ActivityQueue()

  case FundSubBatchAction extends ActivityQueue()

  case FundSubUpdateSubscriptionDocReview extends ActivityQueue()

  case FundSubFormCommentNotification extends ActivityQueue()

  case InvestorAccessDocumentExpirationNotification extends ActivityQueue()

  case InvestorAccessUpdateProfileTemplate extends ActivityQueue()

  case FundSubSyncDashboardData extends ActivityQueue()

  case SyncSingleFundDashboardData extends ActivityQueue()

  case FundSubSendBaitUploadSupportingDocEvent extends ActivityQueue()

  case FundSubSendBaitSupportingDocReviewReadyEvent extends ActivityQueue()

//  case FundSubSendBaitNewLpInvitedEvent extends ZActivityQueue()

  case SandboxSetupTimeExport extends ActivityQueue()

  case FundSubDailyFundActivitiesEmail extends ActivityQueue()

  case FundSubWeeklyFundActivitiesEmail extends ActivityQueue()

  case FundSubSimulator extends ActivityQueue()

  case FundSubAutoSaveSubscriptionData extends ActivityQueue()

  case FundSubNewInvestorReportEmail extends ActivityQueue()

  case FundSubLpSubmissionExport extends ActivityQueue()

  case FundSubDataExtractRequestNotification extends ActivityQueue()

  case FundSubPrepareDummyDataForDataExtract extends ActivityQueue()

  case FundSubBulkAddEnvironment extends ActivityQueue()

  case FundSubRemoveEnvironment extends ActivityQueue()

  case FundSubInviteFundManager extends ActivityQueue()
  case FundSubMoveFundManager extends ActivityQueue()
  case FundSubRemoveFundManager extends ActivityQueue()
  case FundSubAssignInvestor extends ActivityQueue()
  case FundSubUnassignInvestor extends ActivityQueue()

  // Public APIs
  case GetApiFileDownload extends ActivityQueue()

  case GetRequestStatus extends ActivityQueue()

  case BulkCreateOrders extends ActivityQueue(WorkerOptions.PublicApi.bulkCreateOrders)

  case ActivateOfflineOrders extends ActivityQueue()

  case GetFundInvitationLink extends ActivityQueue()

  case GetOrdersFormData extends ActivityQueue()

  case GetOrderWorkflowData extends ActivityQueue()

  case CreateWebhookEndpoint extends ActivityQueue()

  case GetWebhookEndpoint extends ActivityQueue()

  case GetAllFundWebhooks extends ActivityQueue()

  case RemoveWebhook extends ActivityQueue()

  case UpdateWebhook extends ActivityQueue()

  case GetStandardFormFields extends ActivityQueue()

  case GetStandardFormField extends ActivityQueue()

  case GenericJsonApi extends ActivityQueue()

  case TestApi extends ActivityQueue()

  case FundDataPublicApi extends ActivityQueue()

  // Dms
  case DmsSearchIndex extends ActivityQueue()

  // Rag
  case RagIndexDocument extends ActivityQueue(WorkerOptions.Rag.indexDocumentOptions)
  case RagUpdateIndex extends ActivityQueue(WorkerOptions.Rag.updateIndexOptions)

  // Data protection
  case DataProtectionRequest extends ActivityQueue()

  // Data room
  case DataRoomPostUpload extends ActivityQueue()

  case DataRoomNewUploadNotification extends ActivityQueue()

  case DataRoomSimulator extends ActivityQueue()

  case StorageFileSync extends ActivityQueue(WorkerOptions.medium)

  // Fund Data
  case FundDataImportInvestorsFromSubscription extends ActivityQueue()
  case FundDataImportInvestorsBySpreadsheet extends ActivityQueue()
  case FundDataImportInvestmentEntitiesBySpreadsheet extends ActivityQueue()
  case FundDataExportInvestmentEntitiesToSpreadsheet extends ActivityQueue()
  case FundDataExportContactsToSpreadsheet extends ActivityQueue()
  case FundDataImportContactsBySpreadsheet extends ActivityQueue()
  case FundDataImportRiskAssessmentsBySpreadsheet extends ActivityQueue()
  case FundDataImportDocumentsBySpreadsheet extends ActivityQueue()

  case FundDataImportProfilesFromSpreadsheet extends ActivityQueue()
  case FundDataDocumentExpirationNotification extends ActivityQueue()
  case FundDataAssessmentDueDateNotification extends ActivityQueue()
  case FundDataFundSubProfileConflictSyncNotification extends ActivityQueue()

  case FundDataFundSubSyncAllFirms extends ActivityQueue()
  case FundDataFundSubSyncSingleFirm extends ActivityQueue()

  case FundDataDocumentExpirationWebhook extends ActivityQueue()

  case FundDataBatchDocumentRequest extends ActivityQueue()
  case FundDataMergeInvestors extends ActivityQueue()
  case FundDataAssignInvestorsToClientGroup extends ActivityQueue()
  case FundDataDeleteClientGroup extends ActivityQueue()
  case FundDataInviteMembers extends ActivityQueue()

  case FundDataRemoveGuests extends ActivityQueue()
  case FundDataInviteGuests extends ActivityQueue()
  case FundDataNotifyGuests extends ActivityQueue()
  case FundDataModifyGuestsAccessToOpportunityPages extends ActivityQueue()

  case FundDataCreateOfflineSubscriptions extends ActivityQueue()
  case FundDataCreateOfflineTransactions extends ActivityQueue()
  case FundDataImportOrdersToTransactions extends ActivityQueue()

  case FundDataExtractInvestmentEntityInfoFromFiles extends ActivityQueue()
  case FundDataCreateContacts extends ActivityQueue()

  // Data Extract
  case DataExtractGetUserDocumentsMappingResult extends ActivityQueue()
  case DataExtractGenerateDebugReport extends ActivityQueue()
  case DataExtractGenerateAnalyticsReport extends ActivityQueue()

  // Data Pipeline
  case GreylinCorrection extends ActivityQueue()

  // Ontology
  case InitOntologySpace extends ActivityQueue()

  // Autofill
  case ComputeFormMatching extends ActivityQueue()

  // Annotations / PDF Tool
  case AnalyzePdfAnnotations extends ActivityQueue()

  // Account
  case AccountToken extends ActivityQueue()

  case DataIntegration extends ActivityQueue(WorkerOptions.DataIntegration.workerOptions)

  // Data Room Activity
  case PublicApiReadDataRoomState extends ActivityQueue()
  case PublicApiManageUserGroup extends ActivityQueue()
  case PublicApiInsightsQuery extends ActivityQueue()
  case PublicApiDataRoomAsyncApi extends ActivityQueue()
  case PublicApiManageFileFolder extends ActivityQueue()

  // Async API
  case AsyncApiFast extends ActivityQueue(WorkerOptions.fast)
  case AsyncApiMedium extends ActivityQueue(WorkerOptions.medium)
  case AsyncApiHeavy extends ActivityQueue(WorkerOptions.heavy)
  case AsyncApiCron extends ActivityQueue()
  case AsyncApiCronV2 extends ActivityQueue()

  // Investor Portal
  case InvestorPortal extends ActivityQueue(WorkerOptions.medium)
  case FundDataDocDistribution extends ActivityQueue(WorkerOptions.medium)

  // Email sending
  case EmailSendingUrgentTask extends ActivityQueue(WorkerOptions.fast)
  case EmailSendingScheduler extends ActivityQueue(WorkerOptions.medium)
  case EmailSendingGarbageCollector extends ActivityQueue(WorkerOptions.heavy)
  case EmailSendingProviderAutoRescue extends ActivityQueue(WorkerOptions.heavy)

}
