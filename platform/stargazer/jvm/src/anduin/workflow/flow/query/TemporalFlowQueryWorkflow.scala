// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.flow.query

import scala.reflect.ClassTag

import zio.temporal.workflow.IsWorkflow
import zio.temporal.workflowMethod

import anduin.workflow.WorkflowQueue
import anduin.workflow.flow.TemporalFlowSpec

trait TemporalFlowQueryWorkflow[Spec <: TemporalFlowSpec] extends TemporalFlowSpec { self: Spec =>

  @workflowMethod
  def query(query: BaseQuery): Either[BaseQueryError, BaseQueryResult]

}

trait TemporalFlowQueryWorkflowCompanion[
  Interface <: TemporalFlowQueryWorkflow[? <: TemporalFlowSpec]: {ClassTag, IsWorkflow}
] {

  def workflowQueue: WorkflowQueue

  def maximumAttempts: Int = 1

  given TemporalFlowQueryWorkflowCompanion[Interface] = this

}
