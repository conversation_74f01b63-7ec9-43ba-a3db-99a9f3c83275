// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.flow.query

import anduin.workflow.flow.{FlowEffect, TemporalFlowSpec}

private[query] trait TemporalFlowQueryHandlerSpec[Spec <: TemporalFlowSpec] {
  self: Spec =>

  opaque type QueryHandler[Query <: BaseQuery] = Query => FlowEffect[Either[BaseQueryError, QueryResult[Query]]]

  protected final def handleQuery[Query <: BaseQuery](
    handler: Query => FlowEffect[Either[BaseQueryError, QueryResult[Query]]]
  ): QueryHandler[Query] = handler

  protected given widenQueryHandler
    : [Query <: BaseQuery] => (queryHandler: QueryHandler[Query]) => GenericQueryHandler[Query] = queryHandler

  opaque type GenericQueryHandler[Query] = Query => FlowEffect[Either[BaseQueryError, BaseQueryResult]]

  extension [Query](handler: GenericQueryHandler[Query]) {

    def handle(query: Query): FlowEffect[Either[BaseQueryError, BaseQueryResult]] = {
      handler(query)
    }

  }

  protected final def handleGenericQuery(handler: BaseQuery => FlowEffect[Either[BaseQueryError, BaseQueryResult]])
    : GenericQueryHandler[BaseQuery] =
    handler

}
