// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.flow.query

import scala.reflect.ClassTag

import zio.temporal.workflow.IsWorkflow

import anduin.workflow.WorkflowImpl
import anduin.workflow.flow.{TemporalFlowOperations, TemporalFlowSpec}

trait TemporalFlowQueryWorkflowImpl[Spec <: TemporalFlowSpec]
    extends TemporalFlowQueryWorkflow[Spec]
    with TemporalFlowOperations[Spec]
    with TemporalFlowQueryHandler[Spec] { self: Spec =>

  override final def query(query: BaseQuery): Either[BaseQueryError, BaseQueryResult] = {
    val task = for {
      res <- queryHandler.handle(query)
    } yield res
    task.getOrThrow()
  }

}

object TemporalFlowQueryWorkflowImpl {

  inline def derived[
    Interface <: TemporalFlowQueryWorkflow[? <: TemporalFlowSpec]: {ClassTag, IsWorkflow},
    Implementation <: Interface: ClassTag
  ](
    using companion: TemporalFlowQueryWorkflowCompanion[Interface]
  ): WorkflowImpl[Interface, Implementation] =
    WorkflowImpl.derived[Interface, Implementation](companion.workflowQueue)

}
