// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.flow.query

import shapeless3.deriving.*

import anduin.workflow.flow.TemporalFlowSpec

trait TemporalFlowQueryHandler[Spec <: TemporalFlowSpec] extends TemporalFlowQueryHandlerSpec[Spec] { self: Spec =>

  protected def queryHandler: GenericQueryHandler[BaseQuery]

  protected object GenericQueryHandler {

    inline def derived(
      using inst: K0.CoproductInstances[GenericQueryHandler, BaseQuery]
    ): GenericQueryHandler[BaseQuery] = handleGenericQuery { (query: BaseQuery) =>
      inst.fold(
        query
      )([Query <: BaseQuery] => (st: GenericQueryHandler[Query], query: Query) => st.handle(query))
    }

  }

}
