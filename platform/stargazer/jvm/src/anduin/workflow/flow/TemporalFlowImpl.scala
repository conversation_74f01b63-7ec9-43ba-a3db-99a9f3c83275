// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.flow

import java.time.Duration
import scala.reflect.ClassTag
import zio.temporal.ZRetryOptions
import zio.temporal.activity.ZActivityOptions
import zio.temporal.workflow.{IsWorkflow, ZSaga, ZWorkflow}
import anduin.id.flow.TFlowId
import anduin.workflow.flow.event.TemporalFlowEventHandler
import anduin.workflow.lock.{TemporalLockActivity, TemporalLockInput, TemporalLockSpace}
import anduin.workflow.{ActivityQueue, WorkflowImpl}

trait TemporalFlowImpl[Spec <: TemporalFlowSpec]
    extends TemporalFlow[Spec]
    with TemporalFlowOperations[Spec]
    with TemporalFlowEventHandler[Spec] { self: Spec =>

  private val lockActivity = ZWorkflow.newActivityStub[TemporalLockActivity](
    ZActivityOptions
      .withStartToCloseTimeout(Duration.ofSeconds(60))
      .withTaskQueue(ActivityQueue.TemporalLock.queueName)
      .withRetryOptions(
        ZRetryOptions.default.withMaximumAttempts(1)
      )
  )

  protected def workflowSagaOption: ZSaga.Options = ZSaga.Options.default
  protected def storeActivitySagaOption: ZSaga.Options = ZSaga.Options.default

  protected def lastActivityThreshold: com.google.protobuf.duration.Duration =
    com.google.protobuf.duration.Duration.of(30, 0)

  protected def keepAliveDuration: com.google.protobuf.duration.Duration =
    com.google.protobuf.duration.Duration.of(30, 0)

  private def withUpdateLock[T](flowId: TFlowId, event: BaseEvent)(task: => FlowEffect[T]): FlowEffect[T] = {
    if (shouldLock(event)) {
      FlowEffect.acquireRelease {
        for {
          _ <- FlowEffect.executeActivity(
            lockActivity.lock(
              TemporalLockInput(
                TemporalLockSpace.TemporalFlow,
                flowId,
                lastActivityThreshold,
                keepAliveDuration
              )
            )
          )
          _ <- FlowEffect.attempt(scribe.info(s"Flow ${flowId.idString} lock acquired!"))
        } yield ()
      } { _ =>
        task
      } { _ =>
        for {
          _ <- FlowEffect.executeActivity(
            lockActivity.unlock(
              TemporalLockInput(
                TemporalLockSpace.TemporalFlow,
                flowId,
                lastActivityThreshold,
                keepAliveDuration
              )
            )
          )
          _ <- FlowEffect.attempt(scribe.info(s"Flow ${flowId.idString} lock released!"))
        } yield ()
      }
    } else {
      task
    }
  }

  override final def executeEvent(event: BaseEvent): Either[BaseEventError, BaseEventResult] = {
    val lockTask = withUpdateLock(event.eventId.parent, event) {
      for {
        _ <- FlowEffect.attempt(scribe.info(s"Executing event for temporal flow ${flowEnum}: ${event.eventId.idString}"))
        currentStateOpt <- lastState(event.eventId.parent)
        result <- currentStateOpt.fold {
          val initResult = eventHandler.init(event)
          initResult.fold(
            error => FlowEffect.attempt(Left(error)),
            (newState, initTask) =>
              for {
                resultEither <- initTask(newState)
                result <- resultEither.fold(
                  error => FlowEffect.attempt(Left(error)),
                  res => FlowEffect.attempt(Right(res -> newState))
                )
              } yield result
          )
        } { currentState =>
          val eventHandlerResult = eventHandler.next(event, currentState)
          eventHandlerResult.fold(
            error => FlowEffect.attempt(Left(error)),
            (newState, handlerTask) =>
              for {
                resultEither <- handlerTask(currentState, newState)
                result <- resultEither.fold(
                  error => FlowEffect.attempt(Left(error)),
                  res => FlowEffect.attempt(Right(res -> newState))
                )
              } yield result
          )
        }
        _ <- FlowEffect.attempt(
          scribe.info(
            s"Done executing event for temporal flow ${flowEnum}: ${event.eventId.idString}"
          )
        )
      } yield (currentStateOpt, result)
    }

    val (currentStateOpt, result) = lockTask.getOrThrow(workflowSagaOption)

    currentStateOpt.fold {
      // Do not allow fail init
      result.fold(
        e => recordInitializationFailure(event, e).getOrThrow(storeActivitySagaOption),
        s => recordInitialization(event, s._1, s._2).getOrThrow(storeActivitySagaOption)
      )

    } { currentState =>
      result.fold(
        e => recordTransitionFailure(event, currentState, e).getOrThrow(storeActivitySagaOption),
        r => recordTransition(event, currentState, r._1, r._2).getOrThrow(storeActivitySagaOption)
      )
    }

    result.map(_._1)
  }

}

object TemporalFlowImpl {

  inline def derived[
    Interface <: TemporalFlow[? <: TemporalFlowSpec]: {ClassTag, IsWorkflow},
    Implementation <: Interface: ClassTag
  ](
    using companion: TemporalFlowCompanion[Interface]
  ): WorkflowImpl[Interface, Implementation] =
    WorkflowImpl.derived[Interface, Implementation](companion.workflowQueue)

}
