// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.flow

import scala.reflect.ClassTag

import zio.temporal.workflow.IsWorkflow
import zio.temporal.workflowMethod

import anduin.workflow.WorkflowQueue

// Base trait for all workflows
trait TemporalFlow[Spec <: TemporalFlowSpec] extends TemporalFlowSpec { self: Spec =>

  @workflowMethod
  def executeEvent(event: BaseEvent): Either[BaseEventError, BaseEventResult]

}

trait TemporalFlowCompanion[Interface <: TemporalFlow[? <: TemporalFlowSpec]: {ClassTag, IsWorkflow}] {

  def workflowQueue: WorkflowQueue

  def maximumAttempts: Int = 1

  given TemporalFlowCompanion[Interface] = this

}
