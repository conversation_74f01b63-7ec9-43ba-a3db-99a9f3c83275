// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.flow

import scala.reflect.ClassTag

import zio.temporal.ZRetryOptions
import zio.temporal.workflow.{IsWorkflow, ZChildWorkflowOptions, ZWorkflow}

import anduin.model.id.TemporalWorkflowId
import anduin.utils.ScalaUtils.{cast, downcast, upcast}
import anduin.workflow.WorkflowTask
import anduin.workflow.flow.query.{TemporalFlowQueryWorkflow, TemporalFlowQueryWorkflowCompanion}

trait TemporalFlowChildWorkflow[
  Spec <: TemporalFlowSpec,
  FlowInterface <: TemporalFlow[Spec]: {ClassTag, IsWorkflow},
  QueryInterface <: TemporalFlowQueryWorkflow[Spec]: {ClassTag, IsWorkflow}
](
  using val flowCompanion: TemporalFlowCompanion[FlowInterface],
  val queryCompanion: TemporalFlowQueryWorkflowCompanion[QueryInterface]
) { self: Spec =>

  private inline def newFlowStub(
    key: String
  ) = {
    ZWorkflow.newChildWorkflowStub[FlowInterface](
      ZChildWorkflowOptions
        .withWorkflowId(TemporalWorkflowId.unsafeFromSuffix(key).idString)
        .withTaskQueue(flowCompanion.workflowQueue.queueName)
        .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(flowCompanion.maximumAttempts))
    )
  }

  private inline def newQueryStub(
    key: String
  ) = {
    ZWorkflow.newChildWorkflowStub[QueryInterface](
      ZChildWorkflowOptions
        .withWorkflowId(TemporalWorkflowId.unsafeFromSuffix(key).idString)
        .withTaskQueue(queryCompanion.workflowQueue.queueName)
        .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(queryCompanion.maximumAttempts))
    )
  }

  inline def executeEventEffect(
    event: BaseEvent
  )(
    using ClassTag[Either[BaseEventError, BaseEventResult]]
  ): FlowEffect[Either[BaseEventError, BaseEventResult]] = {
    val childWorkflowStub = newFlowStub(event.eventId.idString)
    for {
      result <- FlowEffect.executeChildWorkflow(
        childWorkflowStub.executeEvent(cast[BaseEvent, childWorkflowStub.BaseEvent](event))
      )
    } yield cast[
      Either[childWorkflowStub.BaseEventError, childWorkflowStub.BaseEventResult],
      Either[BaseEventError, BaseEventResult]
    ](result)
  }

  inline def executeEventTask(
    event: BaseEvent
  )(
    using ClassTag[Either[BaseEventError, BaseEventResult]]
  ): WorkflowTask[Either[BaseEventError, BaseEventResult]] = {
    val childWorkflowStub = newFlowStub(event.eventId.idString)
    for {
      result <- WorkflowTask.executeChildWorkflow(
        childWorkflowStub.executeEvent(cast[BaseEvent, childWorkflowStub.BaseEvent](event))
      )
    } yield cast[
      Either[childWorkflowStub.BaseEventError, childWorkflowStub.BaseEventResult],
      Either[BaseEventError, BaseEventResult]
    ](result)
  }

  inline def queryEffect[Query <: BaseQuery](
    query: Query
  )(
    using ClassTag[Either[BaseQueryError, QueryResult[Query]]]
  ): FlowEffect[Either[BaseQueryError, QueryResult[Query]]] = {
    val childWorkflowStub = newQueryStub(query.queryId.idString)
    for {
      result <- FlowEffect.executeChildWorkflow(
        childWorkflowStub.query(upcast[Query, childWorkflowStub.BaseQuery](query))
      )
      castedResult = cast[
        Either[childWorkflowStub.BaseQueryError, childWorkflowStub.BaseQueryResult],
        Either[BaseQueryError, BaseQueryResult]
      ](result)
    } yield castedResult.map(downcast[BaseQueryResult, QueryResult[Query]])
  }

  inline def queryTask[Query <: BaseQuery](
    query: Query
  )(
    using ClassTag[Either[BaseQueryError, QueryResult[Query]]]
  ): WorkflowTask[Either[BaseQueryError, QueryResult[Query]]] = {
    val childWorkflowStub = newQueryStub(query.queryId.idString)
    for {
      result <- WorkflowTask.executeChildWorkflow(
        childWorkflowStub.query(upcast[Query, childWorkflowStub.BaseQuery](query))
      )
      castedResult = cast[
        Either[childWorkflowStub.BaseQueryError, childWorkflowStub.BaseQueryResult],
        Either[BaseQueryError, BaseQueryResult]
      ](result)
    } yield castedResult.map(downcast[BaseQueryResult, QueryResult[Query]])
  }

}
