// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.flow

import scala.reflect.ClassTag

import zio.{Task, ZIO}
import zio.temporal.ZRetryOptions
import zio.temporal.workflow.{IsWorkflow, ZWorkflowOptions, ZWorkflowStub}

import anduin.model.id.TemporalWorkflowId
import anduin.utils.ScalaUtils
import anduin.utils.ScalaUtils.{cast, downcast, upcast}
import anduin.workflow.TemporalWorkflowService
import anduin.workflow.flow.query.{TemporalFlowQueryWorkflow, TemporalFlowQueryWorkflowCompanion}

trait TemporalFlowExecution[
  Spec <: TemporalFlowSpec,
  FlowInterface <: TemporalFlow[Spec]: {ClassTag, IsWorkflow},
  QueryInterface <: TemporalFlowQueryWorkflow[Spec]: {ClassTag, IsWorkflow}
](
  using val flowCompanion: TemporalFlowCompanion[FlowInterface],
  val queryCompanion: TemporalFlowQueryWorkflowCompanion[QueryInterface]
) { self: Spec =>

  def temporalWorkflowService: TemporalWorkflowService

  private inline def newFlowStub(
    key: String
  ) = {
    temporalWorkflowService.workflowClient.newWorkflowStub[FlowInterface](
      ZWorkflowOptions
        .withWorkflowId(TemporalWorkflowId.unsafeFromSuffix(key).idString)
        .withTaskQueue(flowCompanion.workflowQueue.queueName)
        .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(flowCompanion.maximumAttempts))
    )
  }

  private inline def newQueryStub(
    key: String
  ) = {
    temporalWorkflowService.workflowClient.newWorkflowStub[QueryInterface](
      ZWorkflowOptions
        .withWorkflowId(
          TemporalWorkflowId.unsafeFromSuffix(key).idString
        )
        .withTaskQueue(queryCompanion.workflowQueue.queueName)
        .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(queryCompanion.maximumAttempts))
    )
  }

  inline def executeEvent(
    event: BaseEvent
  )(
    using ClassTag[Either[BaseEventError, BaseEventResult]]
  ): Task[Either[BaseEventError, BaseEventResult]] = {
    for {
      workflowStub <- newFlowStub(event.eventId.idString)
      result <- ZWorkflowStub.execute(
        workflowStub.executeEvent(cast[BaseEvent, workflowStub.BaseEvent](event))
      )
    } yield cast[
      Either[workflowStub.BaseEventError, workflowStub.BaseEventResult],
      Either[BaseEventError, BaseEventResult]
    ](result)
  }

  inline def executeEventAsync(
    event: BaseEvent
  ): Task[Unit] = {
    for {
      workflowStub <- newFlowStub(event.eventId.idString)
      _ <- ZWorkflowStub.start(
        workflowStub.executeEvent(cast[BaseEvent, workflowStub.BaseEvent](event))
      )
    } yield ()
  }

  inline def executeEventOrFail[Result <: BaseEventResult](
    event: BaseEvent
  )(
    using ClassTag[Either[BaseEventError, BaseEventResult]]
  ): Task[Result] = {
    executeEvent(event)
      .flatMap(ZIO.fromEither)
      .map(ScalaUtils.downcast[BaseEventResult, Result])
  }

  inline def query[Query <: BaseQuery](
    query: Query
  )(
    using ClassTag[Either[BaseQueryError, QueryResult[Query]]]
  ): Task[Either[BaseQueryError, QueryResult[Query]]] = {
    for {
      workflowStub <- newQueryStub(query.queryId.idString)
      result <- ZWorkflowStub.execute(
        workflowStub.query(upcast[Query, workflowStub.BaseQuery](query))
      )
      castedResult = cast[
        Either[workflowStub.BaseQueryError, workflowStub.BaseQueryResult],
        Either[BaseQueryError, BaseQueryResult]
      ](result)
    } yield castedResult.map(downcast[BaseQueryResult, QueryResult[Query]])
  }

  inline def queryOrFail[Query <: BaseQuery](
    q: Query
  )(
    using ClassTag[Either[BaseQueryError, QueryResult[Query]]]
  ): Task[QueryResult[Query]] = {
    query(q).flatMap(ZIO.fromEither)
  }

}
