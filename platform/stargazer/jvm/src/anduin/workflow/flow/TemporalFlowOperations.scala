// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.flow

import anduin.id.flow.TFlowId

trait TemporalFlowOperations[Spec <: TemporalFlowSpec] {
  self: Spec =>

  def lastState(key: TFlowId): FlowEffect[Option[BaseState]]

  def recordInitializationFailure(
    event: BaseEvent,
    failure: BaseEventError
  ): FlowEffect[Unit]

  def recordInitialization(
    event: BaseEvent,
    eventResult: BaseEventResult,
    state: BaseState
  ): FlowEffect[Unit]

  def recordTransition(
    event: BaseEvent,
    oldState: BaseState,
    eventResult: BaseEventResult,
    state: BaseState
  ): FlowEffect[Unit]

  def recordTransitionFailure(
    event: BaseEvent,
    oldState: BaseState,
    failure: BaseEventError
  ): FlowEffect[Unit]

}
