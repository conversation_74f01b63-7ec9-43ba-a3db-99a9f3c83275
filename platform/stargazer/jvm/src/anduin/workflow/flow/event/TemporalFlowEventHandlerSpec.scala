// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.flow.event

import anduin.workflow.flow.{FlowEffect, TemporalFlowSpec}
import scala.annotation.nowarn
import scala.reflect.TypeTest

private[event] trait TemporalFlowEventHandlerSpec[Spec <: TemporalFlowSpec] {
  self: Spec =>

  protected final type InitResult = (
    BaseState,
    PartialFunction[BaseState, FlowEffect[Either[BaseEventError, BaseEventResult]]]
  )

  protected final type InitHandlerResult = Either[BaseEventError, InitResult]

  protected final type TransitionResult = (
    BaseState,
    PartialFunction[(BaseState, BaseState), FlowEffect[Either[BaseEventError, BaseEventResult]]]
  )

  protected final type EventHandlerResult = Either[BaseEventError, TransitionResult]

  object EventHandlerResult {

    @nowarn
    inline def init[NextState <: BaseState](
      nextState: => NextState
    )(
      task: NextState => FlowEffect[Either[BaseEventError, BaseEventResult]]
    )(
      using TypeTest[NextState, BaseState]
    ): InitHandlerResult = {
      val taskFn: PartialFunction[BaseState, FlowEffect[Either[BaseEventError, BaseEventResult]]] = {
        case s: NextState =>
          task(s)
      }
      Right[BaseEventError, InitResult](nextState -> taskFn)
    }

    @nowarn
    inline def transit[CurrentState <: BaseState, NextState <: BaseState](
      nextState: => NextState
    )(
      task: (CurrentState, NextState) => FlowEffect[Either[BaseEventError, BaseEventResult]]
    )(
      using TypeTest[NextState, BaseState],
      TypeTest[CurrentState, BaseState]
    ): EventHandlerResult = {
      val taskFn: PartialFunction[(BaseState, BaseState), FlowEffect[Either[BaseEventError, BaseEventResult]]] = {
        case (c: CurrentState, s: NextState) =>
          task(c, s)
      }
      Right[BaseEventError, TransitionResult](nextState -> taskFn)
    }

    def fail[Error <: BaseEventError](error: Error): EventHandlerResult = {
      Left(error)
    }

  }

  protected trait EventHandler {

    protected def initialState: BaseEvent => InitHandlerResult

    protected def nextState: BaseState => BaseEvent => EventHandlerResult

    def init(
      event: BaseEvent
    ): InitHandlerResult = {
      initialState(event)
    }

    def next(
      event: BaseEvent,
      currentState: BaseState
    ): EventHandlerResult = {
      nextState(currentState)(event)
    }

  }

  protected opaque type InitialTransition = PartialFunction[BaseEvent, InitHandlerResult]

  extension (initialTransition: InitialTransition) {
    def lift(event: BaseEvent): Option[InitHandlerResult] = initialTransition.lift(event)
  }

  protected opaque type Transition[SpecificState] = SpecificState => PartialFunction[BaseEvent, EventHandlerResult]

  extension [SpecificState](transition: Transition[SpecificState]) {
    def nextState(state: SpecificState): PartialFunction[BaseEvent, EventHandlerResult] = transition.apply(state)
  }

  protected final def initialTransition(
    initialState: PartialFunction[BaseEvent, InitHandlerResult]
  ): InitialTransition = initialState

  protected final def transition[SpecificState <: BaseState](
    nextState: SpecificState => PartialFunction[BaseEvent, EventHandlerResult]
  ): Transition[SpecificState] = nextState

  protected final def noTransition[SpecificState <: BaseState]: Transition[SpecificState] = { _ =>
    PartialFunction.empty[BaseEvent, EventHandlerResult]
  }

}
