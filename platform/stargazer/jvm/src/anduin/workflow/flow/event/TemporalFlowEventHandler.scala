// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.flow.event

import java.time.Instant

import shapeless3.deriving.*
import zio.temporal.JavaTypeTag
import zio.temporal.workflow.ZWorkflow

import anduin.workflow.data.TemporalData
import anduin.workflow.flow.TemporalFlowSpec

trait TemporalFlowEventHandler[Spec <: TemporalFlowSpec] extends TemporalFlowEventHandlerSpec[Spec] { self: Spec =>

  protected def sideEffect[R: TemporalData](
    f: => R
  )(
    using JavaTypeTag[R]
  ): R =
    ZWorkflow.sideEffect(() => f)

  protected def nowInstant: Instant = ZWorkflow.currentTimeMillis.toInstant

  def eventHandler: EventHandler

  protected object EventHandler {

    inline final def coproductTransition(
      using inst: K0.CoproductInstances[Transition, BaseState]
    ): Transition[BaseState] = transition { (state: BaseState) =>
      inst.fold(
        state
      )([SpecificState <: BaseState] => (st: Transition[SpecificState], state: SpecificState) => st.nextState(state))
    }

    inline final def derived(
      using initialTransition: InitialTransition,
      inst: K0.CoproductInstances[Transition, BaseState]
    ): EventHandler = {
      new EventHandler {

        val initialState: BaseEvent => InitHandlerResult = { event =>
          initialTransition
            .lift(event)
            .getOrElse(Left(initializationError(event)))
        }

        val nextState: BaseState => BaseEvent => EventHandlerResult = { state => event =>
          coproductTransition
            .nextState(state)
            .lift(event)
            .getOrElse(Left(noTransitionError(event, state)))
        }

      }
    }

  }

}
