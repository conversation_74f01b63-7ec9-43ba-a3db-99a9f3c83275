// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow

import scala.reflect.ClassTag

import com.softwaremill.macwire.wire
import zio.ZIO
import zio.temporal.activity.ExtendsActivity
import zio.temporal.worker.ZWorkerFactory

import anduin.workflow.common.{TemporalActivity, TemporalActivityCompanion}

// scalafix:off DisableSyntax.asInstanceOf
abstract class ActivityImpl[Interface <: AnyRef: ClassTag, Implementation <: Interface: ExtendsActivity](
  val queue: ActivityQueue
) {

  def impl: Implementation

  val interfaceClass: Class[Interface] = summon[ClassTag[Interface]].runtimeClass.asInstanceOf[Class[Interface]]

  def register(factory: ZWorkerFactory) = {
    for {
      workerOpt <- factory.getWorker(queue.queueName)
      worker <- ZIO.getOrFailWith(new NoSuchElementException(s"No worker for queue ${queue.queueName}"))(workerOpt)
      _ <- worker.addActivityImplementation[Implementation](impl)
    } yield ()
  }

}

object ActivityImpl {

  def apply[Interface <: AnyRef: ClassTag, Implementation <: Interface: ExtendsActivity](
    queue: ActivityQueue,
    implementation: Implementation
  ): ActivityImpl[Interface, Implementation] = {
    new ActivityImpl(queue) {
      override val impl = implementation
    }
  }

  inline def derived[Interface <: TemporalActivity: ClassTag, Implementation <: Interface: ExtendsActivity](
    using companion: TemporalActivityCompanion[Interface]
  ): ActivityImpl[Interface, Implementation] = {
    val implementation = wire[Implementation]
    new ActivityImpl(companion.activityQueue) {
      override val impl = implementation
    }
  }

}
