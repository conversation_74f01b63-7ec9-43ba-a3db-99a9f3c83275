package anduin.workflow.state

trait WorkflowStateMapSyntax {

  /** Initializes the state with an empty map
    *
    * @tparam K
    *   map key type
    * @tparam V
    *   map value type
    * @return
    *   the state
    */
  def emptyMap[K, V]: WorkflowState.Required[Map[K, V]] =
    WorkflowState.make(Map.empty[K, V])

  extension [K, V](self: WorkflowState[Map[K, V]]) {
    def get(key: K): Option[V] = self.toOption.flatMap(_.get(key))

    def getOrElse(key: K, default: => V): V =
      get(key).getOrElse(default)

    def apply(key: K): V = self.snapshot.apply(key)

    def filterKeysInPlace(p: K => Boolean): WorkflowState[Map[K, V]] =
      filterInPlace((k, _) => p(k))

    def filterInPlace(p: (K, V) => Boolean): WorkflowState[Map[K, V]] =
      self.update(_.view.filter(p.tupled).toMap)

    def update(key: K, value: V): WorkflowState[Map[K, V]] =
      self.update(_.updated(key, value))

    def +=(pair: (K, V)): WorkflowState[Map[K, V]] =
      self.update(_.updated(pair._1, pair._2))

    def ++=(values: Iterable[(K, V)]): WorkflowState[Map[K, V]] =
      self.update(_ ++ values)

    def clear(): WorkflowState[Map[K, V]] =
      self := Map.empty

    def remove(key: K): WorkflowState[Map[K, V]] =
      self.update(_ - key)

    def -=(key: K): WorkflowState[Map[K, V]] =
      self.update(_ - key)

    def --=(keys: Iterable[K]): WorkflowState[Map[K, V]] =
      self.update(_ -- keys)

  }

}
