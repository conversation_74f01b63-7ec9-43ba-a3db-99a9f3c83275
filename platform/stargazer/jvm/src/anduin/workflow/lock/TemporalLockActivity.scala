// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.lock

import com.google.protobuf.empty.Empty
import zio.temporal.{activityInterface, activityMethod}

import anduin.workflow.ActivityQueue
import anduin.workflow.common.{TemporalActivity, TemporalActivityCompanion}

@activityInterface
trait TemporalLockActivity extends TemporalActivity {

  @activityMethod
  def lock(input: TemporalLockInput): Empty

  @activityMethod
  def unlock(input: TemporalLockInput): Empty

}

object TemporalLockActivity extends TemporalActivityCompanion[TemporalLockActivity](ActivityQueue.TemporalLock)
