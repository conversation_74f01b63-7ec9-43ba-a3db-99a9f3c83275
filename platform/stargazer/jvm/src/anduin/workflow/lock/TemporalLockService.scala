// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.lock

import io.temporal.client.{WorkflowExecutionAlreadyStarted, WorkflowFailedException, WorkflowNotFoundException}
import zio.{Task, ZIO}
import zio.temporal.workflow.ZWorkflowStub
import anduin.model.id.TemporalWorkflowId
import anduin.workflow.TemporalWorkflowService

final case class TemporalLockService(
  temporalWorkflowService: TemporalWorkflowService
) {

  private def getWorkflowId(
    input: TemporalLockInput
  ): TemporalWorkflowId = {
    TemporalWorkflowId.unsafeFromSuffix(s"-${input.space}-${input.key.idString}")
  }

  private inline def newWorkflowStub(
    input: TemporalLockInput
  ) = {
    temporalWorkflowService.newWorkflowStub[
      TemporalLockInput,
      TemporalLockOutput,
      TemporalLockWorkflow
    ](presetWorkflowIdOpt = Option(getWorkflowId(input)))
  }

  private inline def getWorkflowStub(
    input: TemporalLockInput
  ) = {
    temporalWorkflowService.getWorkflowStub[TemporalLockWorkflow](getWorkflowId(input), runId = None)
  }

  private inline def startWorkflow(
    input: TemporalLockInput
  ): Task[Unit] = {
    val task = for {
      workflowStub <- newWorkflowStub(input)
      _ <- ZWorkflowStub.start(
        workflowStub.run(input)
      )
    } yield ()
    task.catchSome { case _: WorkflowExecutionAlreadyStarted => ZIO.unit }
  }

  private inline def autoStartWorkflow[A](
    input: TemporalLockInput
  )(
    task: => Task[A]
  ): Task[A] = {
    task.catchSome { case _: WorkflowNotFoundException | _: WorkflowFailedException =>
      startWorkflow(input) *> task
    }
  }

  def lock(
    input: TemporalLockInput
  ): Task[Unit] = {
    autoStartWorkflow(input) {
      for {
        workflowStub <- getWorkflowStub(input)
        _ <- ZWorkflowStub.update(
          workflowStub.lock()
        )
      } yield ()
    }
  }

  def unlock(
    input: TemporalLockInput
  ): Task[Unit] = {
    autoStartWorkflow(input) {
      for {
        workflowStub <- getWorkflowStub(input)
        _ <- ZWorkflowStub.update(
          workflowStub.unlock()
        )
      } yield ()
    }
  }

}
