// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.lock

import com.google.protobuf.empty.Empty
import com.google.protobuf.{BoolValue, Duration}
import io.temporal.workflow.Workflow
import zio.temporal.workflow.ZWorkflow
import anduin.workflow.WorkflowTask
import anduin.workflow.state.WorkflowState
import zio.temporal.ZCurrentTimeMillis

import java.time.temporal.ChronoUnit

class TemporalLockWorkflowImpl extends TemporalLockWorkflow {

  private val workflowLock = Workflow.newWorkflowLock()
  private val lastActivity = WorkflowState.empty[ZCurrentTimeMillis]

  override def lock(): Empty = {
    workflowLock.lock()
    lastActivity.setTo(ZWorkflow.currentTimeMillis)
    Empty()
  }

  override def tryLock(): BoolValue = {
    val isLocked = workflowLock.tryLock()
    lastActivity.setTo(ZWorkflow.currentTimeMillis)
    BoolValue.of(isLocked)
  }

  override def tryLockTimeout(duration: Duration): BoolValue = {
    val isLocked = workflowLock.tryLock(java.time.Duration.ofSeconds(duration.getSeconds))
    lastActivity.setTo(ZWorkflow.currentTimeMillis)
    BoolValue.of(isLocked)
  }

  override def unlock(): Empty = {
    if (workflowLock.isHeld) {
      workflowLock.unlock()
    }
    lastActivity.setTo(ZWorkflow.currentTimeMillis)
    Empty()
  }

  override def runAsync(input: TemporalLockInput): WorkflowTask[TemporalLockOutput] = {
    for {
      _ <- WorkflowTask.succeed(scribe.info(s"Starting temporal lock for space=${input.space}, key=${input.key}"))
      _ <- WorkflowTask.attempt {
        ZWorkflow.awaitUntil(lastActivity.nonEmpty)
      }
      _ <- check(input)
    } yield TemporalLockOutput()
  }

  private def check(input: TemporalLockInput): WorkflowTask[Unit] = {
    for {
      now <- WorkflowTask.attempt {
        ZWorkflow.currentTimeMillis
      }
      activityRecent = lastActivity.toOption.fold(false) { activity =>
        val elapsedSeconds = activity.toInstant.until(now.toInstant, ChronoUnit.SECONDS)
        elapsedSeconds < input.lastActivityThreshold.seconds
      }
      _ <-
        if (activityRecent || !Workflow.isEveryHandlerFinished) {
          WorkflowTask
            .attempt {
              ZWorkflow.sleep(zio.Duration.fromSeconds(input.keepAliveDuration.seconds))
            }
            .flatMap(_ => check(input))
        } else {
          WorkflowTask.succeed(scribe.info(s"Suspended temporal lock for space=${input.space}, key=${input.key}"))
        }
    } yield ()
  }

  override def run(input: TemporalLockInput): TemporalLockOutput = {
    runAsync(input).getOrThrow
  }

}
