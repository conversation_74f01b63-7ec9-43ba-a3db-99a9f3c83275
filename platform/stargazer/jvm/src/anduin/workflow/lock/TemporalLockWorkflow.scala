// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.lock

import com.google.protobuf.empty.Empty
import com.google.protobuf.{BoolValue, Duration}
import zio.temporal.{updateMethod, workflowInterface, workflowMethod}

import anduin.workflow.WorkflowQueue
import anduin.workflow.common.{TemporalWorkflow, TemporalWorkflowCompanion}

@workflowInterface
trait TemporalLockWorkflow extends TemporalWorkflow[TemporalLockInput, TemporalLockOutput] {

  @updateMethod
  def lock(): Empty

  @updateMethod
  def tryLock(): BoolValue

  @updateMethod
  def tryLockTimeout(duration: Duration): BoolValue

  @updateMethod
  def unlock(): Empty

  @workflowMethod
  def run(input: TemporalLockInput): TemporalLockOutput

}

object TemporalLockWorkflow extends TemporalWorkflowCompanion[TemporalLockWorkflow] {

  override val workflowQueue = WorkflowQueue.TemporalLock

}
