// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.lock

import com.google.protobuf.empty.Empty

import anduin.workflow.TemporalWorkflowService
import anduin.workflow.TemporalWorkflowService.runActivity

final case class TemporalLockActivityImpl(
  temporalLockService: TemporalLockService
)(
  using val temporalWorkflowService: TemporalWorkflowService
) extends TemporalLockActivity {

  override def lock(input: TemporalLockInput): Empty = {
    temporalLockService.lock(input).as(Empty()).runActivity
  }

  override def unlock(input: TemporalLockInput): Empty = {
    temporalLockService.unlock(input).as(Empty()).runActivity
  }

}
