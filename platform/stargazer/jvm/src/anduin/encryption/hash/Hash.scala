// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.encryption.hash

trait Hash {

  /** Compute the hash of a plain text.
    *
    * @param plainText
    *   The plain text to hash.
    * @return
    */
  def hash(
    plainText: String
  ): String

  /** Validate a plain text using a hash.
    *
    * @param plainText
    *   The plain text to check.
    * @param hashedText
    *   The hash of the valid text input.
    * @return
    */
  def validate(
    plainText: String,
    hashedText: String
  ): <PERSON><PERSON><PERSON>

}
