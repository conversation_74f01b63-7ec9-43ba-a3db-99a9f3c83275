// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.encryption

import java.security.GeneralSecurityException
import scala.util.Try
import software.amazon.awssdk.auth.credentials.{AwsBasicCredentials, StaticCredentialsProvider}
import anduin.encryption.tink.integration.awskms.AwsKmsClient
import com.google.crypto.tink.streamingaead.StreamingAeadConfig
import scalapb.{GeneratedMessage, GeneratedMessageCompanion}
import zio.{Scope, Task, ZIO}
import anduin.encryption.util.TinkUtils
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.GondorBackendConfig.EncryptionConfig

import java.io.*
import com.google.crypto.tink.*

/** This encryption service uses two keys for encryption and decryption: a Customer Master Key (CMK) and a Data
  * Encryption Key (DEK). The CMK is generated and stored in AWS KMS (it never lease AWS!), while the the DEK is
  * encrypted by CMK and its encrypted version is stored in our configs.
  *
  * Before encrypting and decrypting data, we must ask KSM to decrypt the encrypted DEK (get from the config) so that we
  * can have the plaintext DEK. The data is encrypted and decrypted by the plaintext DEK. This technique is called
  * Envelope Encryption.
  */
final case class StreamingEncryptionService(
  backendConfig: GondorBackendConfig
) {

  private lazy val defaultAssociatedData = Array.emptyByteArray

  private def toTask[A](f: => A): Task[A] = {
    ZIO.attempt(f)
  }

  private lazy val streamingAeadResource: ZIO[Scope, Throwable, StreamingAead] = ZIO.acquireRelease(
    setUp(backendConfig.encryptionConfig)
  ) { _ =>
    ZIO.unit
  }

  // Execute the setup first time encrypt or decrypt is invoked. This function does two things:
  // - Registers Aead. This is a Tink thing, we can't do anything before calling this register.
  // - Decrypts DEK and stores it in keysetHandle.
  def setUp(encryptionConfig: EncryptionConfig): Task[StreamingAead] = {
    toTask {
      scribe.info("Initializing Streaming Encryption...")
      StreamingAeadConfig.register()

      val awsCredProvider = StaticCredentialsProvider.create(
        AwsBasicCredentials.create(encryptionConfig.accessKeyId, encryptionConfig.secretAccessKey)
      )

      val awsKmsClient = {
        new AwsKmsClient().withCredentialsProvider(awsCredProvider)
      }

      // In this step, the DEK will be decrypted by the master key at AWS KMS and stored in the keysetHandle.
      val keysetHandle = KeysetHandle.read(
        BinaryKeysetReader.withBytes(
          TinkUtils.getKeySet(
            encryptionConfig.encryptedDataKeyBlob,
            KeyTemplates.get("AES256_GCM_HKDF_4KB")
          )
        ),
        awsKmsClient.getAead(encryptionConfig.masterKeyKMSArn)
      )

      val streamingAead = keysetHandle.getPrimitive(classOf[StreamingAead])
      scribe.info("Done Streaming Encryption initialization...")

      // Return the Aead so that the subsequent cryptographic operation can use immediately.
      streamingAead
    }.tapErrorCause { cause =>
      ZIO.logErrorCause("Failed to initialize encryption...", cause)
    }
  }

  private def encrypt(aead: StreamingAead, ciphertextDestinationStream: OutputStream): Task[OutputStream] = {
    toTask {
      aead.newEncryptingStream(ciphertextDestinationStream, defaultAssociatedData)
    }
  }

  private def decrypt(aead: StreamingAead, ciphertextSourceStream: InputStream): Task[InputStream] = {
    toTask {
      aead.newDecryptingStream(ciphertextSourceStream, defaultAssociatedData)
    }
  }

  /** Returns a wrapper around `ciphertextDestinationStream`, such that any write-operation via the wrapper results in
    * an encryption of the written data.
    */
  def encrypt(ciphertextDestinationStream: OutputStream): Task[OutputStream] = {
    ZIO.scoped {
      streamingAeadResource.flatMap(encrypt(_, ciphertextDestinationStream))
    }
  }

  /** Returns the encrypted value of the input plaintext.
    */
  def encrypt(plaintext: Array[Byte]): Task[Array[Byte]] = {
    ZIO.scoped {
      for {
        destinationStream <- ZIO.fromAutoCloseable(ZIO.succeed(new ByteArrayOutputStream()))
        result <- encrypt(destinationStream)
          .map { sourceStream =>
            sourceStream.write(plaintext)
            sourceStream.close()
            destinationStream.toByteArray
          }
          .tapSomeError { case e: GeneralSecurityException =>
            ZIO.logErrorCause("Failed to encrypt", e.toCause)
          }
      } yield result
    }

  }

  /** Returns a wrapper around `ciphertextSourceStream`, such that any read-operation via the wrapper results in a
    * decryption of the underlying ciphertext,
    */
  def decrypt(ciphertextSourceStream: InputStream): Task[InputStream] = {
    ZIO.scoped {
      streamingAeadResource.flatMap { streamingAead =>
        decrypt(streamingAead, ciphertextSourceStream)
      }
    }
  }

  /** Returns the decrypted data of the input ciphertext.
    */
  def decrypt(ciphertext: Array[Byte]): Task[Array[Byte]] = {
    ZIO.scoped {
      for {
        sourceStream <- ZIO.fromAutoCloseable(ZIO.succeed(new ByteArrayInputStream(ciphertext)))
        result <- decrypt(sourceStream)
          .map { destinationStream =>
            val decryptedBytes = destinationStream.readAllBytes()
            destinationStream.close()
            decryptedBytes
          }
          .tapSomeError { case e: GeneralSecurityException =>
            ZIO.logErrorCause("Failed to decrypt", e.toCause)
          }
      } yield result
    }
  }

  def deriveCryptoCodec[A <: GeneratedMessage](
    using companion: GeneratedMessageCompanion[A]
  ): CryptorCodec[A] = {
    new CryptorCodec[A] {
      override val encryptor: Encryptor[A] = (a: A) => {
        encrypt(companion.toByteArray(a))
      }

      override val decryptor: Decryptor[A] = (encryptedBytes: Array[Byte]) => {
        for {
          decryptedBytes <- decrypt(encryptedBytes)
          modelOpt <- Try(
            companion.parseFrom(decryptedBytes)
          ).fold(
            ex => {
              scribe.error("Failed to decode proto bytes for decryptor", ex)
              ZIO.succeed(None)
            },
            m => ZIO.succeed(Some(m))
          )
        } yield modelOpt
      }
    }
  }

}
