// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.utils.stream

import zio.ZIO
import zio.stream.{Stream, ZStream}

import java.io.*

object ZStreamIOUtils {

  type ByteStream = Stream[Throwable, Byte]

  def fromInputStream(is: InputStream): ByteStream = {
    ZStream.fromInputStreamZIO(
      ZIO.attempt(is).refineToOrDie[IOException]
    )
  }

  def fromOutputStreamWriterTask(writer: OutputStream => Unit): zio.Task[ByteStream] = {
    ZIO.attempt {
      fromOutputStreamWriter(writer)
    }
  }

  def fromOutputStreamWriter(writer: OutputStream => Unit): ByteStream = {
    ZStream.fromOutputStreamWriter(writer)
  }

  // WARNING: This function stores all bytes from output stream into memory
  def fromOutputStream(use: OutputStream => ZIO[Any, Throwable, Unit]): ZIO[Any, Throwable, ByteStream] = {
    ZIO.scoped {
      for {
        os <- ZIO.fromAutoCloseable(ZIO.attempt(new ByteArrayOutputStream))
        _ <- use(os)
      } yield fromByteArray(os.toByteArray)
    }
  }

  def fromResource(path: String, clazz: Class[?] = getClass): ByteStream = {
    Option(clazz.getResourceAsStream(path)).fold[Stream[Throwable, Byte]] {
      ZStream.fail(new FileNotFoundException(path))
    } { is =>
      fromInputStream(is)
    }
  }

  def fromResourceWithFallback(path: String, fallback: String, clazz: Class[?] = getClass): ByteStream = {
    val isOpt = Option(clazz.getResourceAsStream(path)).orElse(Option(clazz.getResourceAsStream(fallback)))
    isOpt.fold[Stream[Throwable, Byte]] {
      ZStream.fail(new FileNotFoundException(s"path=$path, fallback=$fallback"))
    } { is =>
      fromInputStream(is)
    }
  }

  def fromString(
    string: String
  ): ByteStream = {
    fromInputStream(new ByteArrayInputStream(string.getBytes))
  }

  def toString(
    source: ByteStream
  ): zio.Task[String] = {
    for {
      content <- source.runCollect
    } yield content.asString
  }

  def fromByteArray(
    data: Array[Byte]
  ): ByteStream = {
    fromInputStream(new ByteArrayInputStream(data))
  }

  def copy(
    source: ByteStream
  ): zio.Task[ByteStream] = {
    for {
      bytes <- toByteArray(source)
    } yield fromByteArray(bytes)
  }

  def toByteArray[R](
    source: ZStream[R, Throwable, Byte]
  ): ZIO[R, Throwable, Array[Byte]] = {
    for {
      content <- source.runCollect
    } yield content.toArray
  }

}
