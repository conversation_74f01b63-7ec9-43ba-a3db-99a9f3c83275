// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.execution

import java.util.concurrent.ThreadFactory
import java.util.concurrent.atomic.AtomicInteger

private[execution] final class NamedThreadFactory(name: String, daemon: <PERSON><PERSON><PERSON>) extends ThreadFactory {

  private val parentGroup = Thread.currentThread.getThreadGroup
  private val threadGroup = new ThreadGroup(parentGroup, name)
  private val threadCount = new AtomicInteger(1)

  override def newThread(r: Runnable): Thread = {
    val newThreadNumber = threadCount.getAndIncrement()

    val thread = new Thread(threadGroup, r)
    thread.setName(s"$name-$newThreadNumber")
    thread.setDaemon(daemon)
    thread.setContextClassLoader(ClassLoader.getSystemClassLoader)

    thread
  }

}
