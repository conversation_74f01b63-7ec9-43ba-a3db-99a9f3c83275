// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.execution

import java.util.concurrent.{ForkJoinPool, SynchronousQueue, ThreadPoolExecutor, TimeUnit}

object ZIOExecutor {

  def io(
    name: String,
    daemonic: Boolean = true,
    corePoolSize: Int = 0,
    maxPoolSize: Int = Int.MaxValue,
    keepAliveTime: Long = 60000L,
    timeUnit: TimeUnit = TimeUnit.MILLISECONDS
  ): zio.Executor = {
    zio.Executor.fromThreadPoolExecutor {
      val workQueue = new SynchronousQueue[Runnable]()
      val threadFactory = new NamedThreadFactory(name, daemonic)

      val threadPool = new ThreadPoolExecutor(
        corePoolSize,
        maxPoolSize,
        keepAliveTime,
        timeUnit,
        workQueue,
        threadFactory
      )

      threadPool
    }
  }

  def forkJoin(
    name: String,
    parallelism: Int,
    daemonic: Boolean = true
  ): zio.Executor = {
    zio.Executor.fromJavaExecutor {
      new ForkJoinPool(
        parallelism,
        new StandardWorkerThreadFactory(name, daemonic),
        null, // scalafix:ok DisableSyntax.null
        true
      )
    }
  }

}
