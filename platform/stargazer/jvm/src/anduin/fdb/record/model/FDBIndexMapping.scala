// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record.model

import com.apple.foundationdb.record.metadata.Index

import anduin.fdb.record.FDBRecordEnum

sealed class FDBAggregateIndexMapping[K, M, R, S <: FDBRecordEnum](
  index: Index
)(
  using val recordKey: FDBTupleConverter[K],
  recordModel: FDBRecordModel[M],
  val resultTupleConverter: FDBTupleConverter[R]
) extends FDBIndexMapping[M, S](index)

object FDBAggregateIndexMapping {

  def instance[K: FDBTupleConverter, M: FDBRecordModel, R: FDBTupleConverter, S <: FDBRecordEnum](
    index: Index
  ): FDBAggregateIndexMapping[K, M, R, S] = {
    new FDBAggregateIndexMapping[K, M, R, S](index)
  }

}

sealed class FDBIndexMapping[M, S <: FDBRecordEnum](
  val index: Index
)(
  using val recordModel: FDBRecordModel[M]
)

object FDBIndexMapping {

  def instance[M: FDBRecordModel, S <: FDBRecordEnum](index: Index): FDBIndexMapping[M, S] = {
    new FDBIndexMapping[M, S](index)
  }

}

sealed class FDBKeyValueIndexMapping[K, V, M, S <: FDBRecordEnum](
  index: Index
)(
  using val recordKey: FDBTupleConverter[K],
  recordModel: FDBRecordModel[M],
  val valueTupleConverter: FDBTupleConverter[V]
) extends FDBIndexMapping[M, S](index)

object FDBKeyValueIndexMapping {

  def instance[K: FDBTupleConverter, V: FDBTupleConverter, M: FDBRecordModel, S <: FDBRecordEnum](
    index: Index
  ): FDBKeyValueIndexMapping[K, V, M, S] = {
    new FDBKeyValueIndexMapping[K, V, M, S](index)
  }

}

sealed class FDBLuceneIndexMapping[K, M, S <: FDBRecordEnum](
  index: Index
)(
  using val recordKey: FDBTupleConverter[K],
  recordModel: FDBRecordModel[M]
) extends FDBIndexMapping[M, S](index)

object FDBLuceneIndexMapping {

  def instance[K: FDBTupleConverter, M: FDBRecordModel, S <: FDBRecordEnum](
    index: Index
  ): FDBLuceneIndexMapping[K, M, S] = {
    new FDBLuceneIndexMapping[K, M, S](index)
  }

}
