// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record.model

import zio.{BuildFrom, Cause, Trace, ZIO}

import anduin.fdb.record.model.RecordIO.recordIOExecutor
import com.anduin.stargazer.service.utils.ZIOUtils

// scalafix:off DisableSyntax.contravariant, DisableSyntax.covariant
opaque type RecordReadIO[-R, +E, +A] = ZIO[R, E, A]

object RecordReadIO {

  sealed trait FlatMapIO[R, E, A, F[_, _, _]] {
    def flatMap[R1 <: R, E1 >: E, B](a: => RecordReadIO[R, E, A])(f: A => F[R1, E1, B]): F[R1, E1, B]
  }

  given recordReadIOFlatMap: [R, E, A] => FlatMapIO[R, E, A, RecordReadIO] {

    override def flatMap[R1 <: R, E1 >: E, B](a: => RecordReadIO[R, E, A])(f: A => RecordReadIO[R1, E1, B])
      : RecordReadIO[R1, E1, B] = {
      RecordReadIO.fromZIO {
        a.toZIO.flatMap(f)
      }
    }

  }

  given recordIOFlatMap: [R, E, A] => FlatMapIO[R, E, A, RecordIO] {

    override def flatMap[R1 <: R, E1 >: E, B](a: => RecordReadIO[R, E, A])(f: A => RecordIO[R1, E1, B])
      : RecordIO[R1, E1, B] = {
      a.toRecordIO.flatMap(f)
    }

  }

  extension [R, E, A](underlying: RecordReadIO[R, E, A]) {

    def map[B](f: A => B): RecordReadIO[R, E, B] = {
      RecordReadIO.fromZIO(underlying.map(f))
    }

    def as[B](b: B): RecordReadIO[R, E, B] = {
      map(_ => b)
    }

    def orElseSucceed[A1 >: A](a1: => A1): RecordReadIO[R, E, A1] = {
      underlying.orElseSucceed(a1)
    }

    def flatMap[R1 <: R, E1 >: E, B, F[_, _, _]](
      f: A => F[R1, E1, B]
    )(
      using fm: FlatMapIO[R, E, A, F]
    ): F[R1, E1, B] = {
      fm.flatMap(underlying)(f)
    }

    def unit: RecordReadIO[R, E, Unit] = {
      RecordReadIO.fromZIO(underlying.unit)
    }

    def combine[R1 <: R, E1 >: E, B](r: RecordReadIO[R1, E1, B]): RecordReadIO[R1, E1, (A, B)] = {
      RecordReadIO.fromZIO {
        underlying.zipPar(r)
      }
    }

    def catchAll[R1 <: R, E2, A1 >: A](h: E => RecordReadIO[R1, E2, A1]): RecordReadIO[R1, E2, A1] = {
      RecordReadIO.fromZIO {
        underlying.catchAll(e => h(e))
      }
    }

    def catchSome[R1 <: R, E1 >: E, A1 >: A](pf: PartialFunction[E, RecordReadIO[R1, E1, A1]])
      : RecordReadIO[R1, E1, A1] = {
      RecordReadIO.fromZIO {
        underlying.catchSome(e => pf(e))
      }
    }

    def andThen[R1 <: R, E1 >: E, B, F[_, _, _]](
      that: F[R1, E1, B]
    )(
      using fm: FlatMapIO[R, E, A, F]
    ): F[R1, E1, B] = {
      fm.flatMap(underlying)(_ => that)
    }

    def *>[R1 <: R, E1 >: E, B, F[_, _, _]](
      that: F[R1, E1, B]
    )(
      using fm: FlatMapIO[R, E, A, F]
    ): F[R1, E1, B] = {
      fm.flatMap(underlying)(_ => that)
    }

    def toRecordIO: RecordIO[R, E, A] = RecordIO.fromZIO(underlying)

    protected[fdb] def toZIO: ZIO[R, E, A] = underlying

  }

  private[fdb] def fromZIO[R, E, A](effect: ZIO[R, E, A]): RecordReadIO[R, E, A] = {
    effect.onExecutor(recordIOExecutor)
  }

  given [R, E, A] => Conversion[RecordReadIO[R, E, A], RecordIO[R, E, A]] = (io: RecordReadIO[R, E, A]) => {
    io.toRecordIO
  }

  given [R, E, A, B] => (conversion: Conversion[A, B]) => Conversion[RecordReadIO[R, E, A], RecordIO[R, E, B]] =
    (io: RecordReadIO[R, E, A]) => {
      io.toRecordIO.map(conversion)
    }

  given funcConversion: [X, R, E, A, B] => (conversion: Conversion[A, B])
    => Conversion[X => RecordReadIO[R, E, A], X => RecordReadIO[R, E, B]] = f => {
    f.andThen(_.map(conversion))
  }

  def unit: RecordReadIO[Any, Nothing, Unit] = fromZIO(ZIO.unit)

  def none: RecordReadIO[Any, Nothing, Option[Nothing]] = fromZIO(ZIO.none)

  def succeed[T](t: => T): RecordReadIO[Any, Nothing, T] = fromZIO(ZIO.succeed(t))

  def fail[E](error: => E): RecordReadIO[Any, E, Nothing] = fromZIO(ZIO.fail(error))

  def parTraverseN[R, E, A, B](
    parallelism: => Int
  )(
    in: => Iterable[A]
  )(
    f: A => RecordReadIO[R, E, B]
  ): RecordReadIO[R, E, List[B]] = {
    RecordReadIO.fromZIO {
      ZIO.foreachPar(in)(a => f(a)).withParallelism(parallelism).map(_.toList)
    }
  }

  def collectFirst[R, E, A, B](
    as: => Iterable[A]
  )(
    f: A => RecordReadIO[R, E, Option[B]]
  )(
    using Trace
  ): RecordReadIO[R, E, Option[B]] = {
    RecordReadIO.fromZIO {
      ZIO.collectFirst(as)(a => f(a))
    }
  }

  def traverse[R, E, A, B, M[+X] <: Iterable[X]](
    in: => M[A]
  )(
    f: A => RecordReadIO[R, E, B]
  )(
    using bf: BuildFrom[M[A], B, M[B]]
  ): RecordReadIO[R, E, M[B]] = {
    RecordReadIO.fromZIO {
      ZIO.foreach(in)(r => f(r))
    }
  }

  def fromOption[E, T](opt: => Option[T], error: => E): RecordReadIO[Any, E, T] = {
    fromZIO(ZIO.fromOption(opt).orElseFail(error))
  }

  def traverseOption[R, E, A, B](opt: => Option[A])(task: A => RecordReadIO[R, E, Option[B]])
    : RecordReadIO[R, E, Option[B]] = {
    opt.fold[RecordReadIO[R, E, Option[B]]](RecordReadIO.succeed(None))(task)
  }

  def when[R, E, T](condition: => Boolean)(task: RecordReadIO[R, E, T]): RecordReadIO[R, E, Unit] = {
    if (condition) task.unit else unit
  }

  def unless[R, E, T](condition: => Boolean)(task: RecordReadIO[R, E, T]): RecordReadIO[R, E, Unit] = {
    when(!condition)(task)
  }

  def validate[R, E](predicate: => Boolean)(ex: => E): RecordReadIO[R, E, Unit] = {
    unless(predicate)(fail(ex))
  }

  def validate[R, E, E1 >: E](predicateZIO: RecordReadIO[R, E, Boolean])(ex: => E1): RecordReadIO[R, E1, Unit] = {
    RecordReadIO.fromZIO {
      predicateZIO.toZIO.flatMap(predicate => validate(predicate)(ex))
    }
  }

  def logAnnotate[R, E, A](key: => String, value: => String)(task: RecordReadIO[R, E, A]): RecordReadIO[R, E, A] = {
    RecordReadIO.fromZIO {
      ZIO.logAnnotate(key, value)(task)
    }
  }

  def logSpan[R, E, A](label: => String)(task: RecordReadIO[R, E, A]): RecordReadIO[R, E, A] = {
    RecordReadIO.fromZIO {
      ZIO.logSpan(label)(task)
    }
  }

  def logInfo(
    msg: => String
  )(
    using trace: Trace
  ): RecordReadIO[Any, Nothing, Unit] = {
    RecordReadIO.fromZIO {
      ZIO.logInfo(msg)
    }
  }

  def logInfoCause(
    cause: Cause[Any]
  )(
    using trace: Trace
  ): RecordReadIO[Any, Nothing, Unit] = {
    RecordReadIO.fromZIO {
      ZIO.logInfoCause(cause)
    }
  }

  def logInfoCause(
    msg: => String,
    cause: Cause[Any]
  )(
    using trace: Trace
  ): RecordReadIO[Any, Nothing, Unit] = {
    RecordReadIO.fromZIO {
      ZIO.logInfoCause(msg, cause)
    }
  }

  def logError(
    msg: => String
  )(
    using trace: Trace
  ) = {
    RecordReadIO.fromZIO {
      ZIO.logError(msg)
    }
  }

  def logErrorCause(
    cause: => Cause[Any]
  )(
    using trace: Trace
  ) = {
    RecordReadIO.fromZIO {
      ZIO.logErrorCause(cause)
    }
  }

  def logErrorCause(
    msg: => String,
    cause: => Cause[Any]
  )(
    using trace: Trace
  ) = {
    RecordReadIO.fromZIO {
      ZIO.logErrorCause(msg, cause)
    }
  }

  def logWarning(
    msg: => String
  )(
    using trace: Trace
  ) = {
    RecordReadIO.fromZIO {
      ZIO.logWarning(msg)
    }
  }

  def logWarningCause(
    cause: => Cause[Any]
  )(
    using trace: Trace
  ) = {
    RecordReadIO.fromZIO {
      ZIO.logWarningCause(cause)
    }
  }

  def logWarningCause(
    msg: => String,
    cause: => Cause[Any]
  )(
    using trace: Trace
  ) = {
    RecordReadIO.fromZIO {
      ZIO.logWarningCause(msg, cause)
    }
  }

  def tailRecM[R, E, A, B](a: => A)(f: A => RecordReadIO[R, E, Either[A, B]]): RecordReadIO[R, E, B] = {
    RecordReadIO.fromZIO {
      ZIOUtils.tailRecM(a)(r => f(r))
    }
  }

}
