// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record.model.common

import anduin.id.ModelIdRegistry
import anduin.radix.RadixId
import com.apple.foundationdb.tuple.Tuple

import scala.reflect.TypeTest

import scala.jdk.CollectionConverters.*

object RadixIdTuple {

  sealed trait PartType derives CanEqual

  case object StringPartType extends PartType

  case object StringListPartType extends PartType

  def getIdFromTuple[K <: RadixId](
    tuple: Tuple,
    startAt: Int
  )(
    parts: PartType*
  )(
    using TypeTest[RadixId, K]
  ): Option[K] = {
    val optList = parts.zipWithIndex.map {
      case (StringPartType, index) => Option(tuple.getString(startAt + index)).map(List(_))
      case (StringListPartType, index) =>
        Option(tuple.getNestedList(startAt + index)).map {
          _.asScala.toList.collect { case st: String =>
            st
          }
        }
    }
    if (optList.exists(_.isEmpty)) {
      None
    } else {
      ModelIdRegistry.parser.parseAs[K](optList.flatten.flatten.mkString("."))
    }
  }

}
