// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record.model

import anduin.id.ModelIdRegistry
import anduin.id.user.UserIdParser
import anduin.model.common.user.UserId
import anduin.model.document.DocumentStorageId
import anduin.protobuf.LocalDateMessage
import anduin.radix.RadixId
import com.apple.foundationdb.tuple.Tuple
import magnolia1.*
import scalapb.{GeneratedEnum, GeneratedEnumCompanion}

import java.time.Instant
import java.util.UUID
import scala.reflect.TypeTest

final case class FDBTupleConverter[A](
  size: Int,
  fromTuple: (Tuple, Int) => Option[A],
  toTuple: A => Tuple
) {

  def concat[B](other: FDBTupleConverter[B]): FDBTupleConverter[(A, B)] = {
    FDBTupleConverter(
      size = size + other.size,
      fromTuple = { (tuple, index) =>
        for {
          a <- fromTuple(tuple, index)
          b <- other.fromTuple(tuple, index + size)
        } yield a -> b
      },
      toTuple = { case (a, b) =>
        toTuple(a).addAll(other.toTuple(b))
      }
    )
  }

  def biMap[B](f: A => B)(r: B => A): FDBTupleConverter[B] = {
    FDBTupleConverter(
      size = size,
      fromTuple = (tuple, index) => fromTuple(tuple, index).map(f),
      toTuple = value => toTuple(r(value))
    )
  }

}

object FDBTupleConverter extends ProductDerivation[FDBTupleConverter] {

  override def join[T](ctx: CaseClass[FDBTupleConverter, T]): FDBTupleConverter[T] = {
    val fieldConverters = ctx.params.map(_.typeclass)
    val size = fieldConverters.map(_.size).sum
    val fromTuple: (Tuple, Int) => Option[T] = { (tuple: Tuple, index: Int) =>
      ctx.constructMonadic { param =>
        val offset = fieldConverters.take(param.index).map(_.size).sum
        param.typeclass.fromTuple(tuple, index + offset)
      }
    }
    val toTuple: T => Tuple = value => {
      ctx.params.foldLeft(Tuple.from()) { (tuple, param) =>
        val fieldValue = param.deref(value)
        val fieldTuple = param.typeclass.toTuple(fieldValue)
        tuple.addAll(fieldTuple)
      }
    }
    FDBTupleConverter(size, fromTuple, toTuple)
  }

  given bool: FDBTupleConverter[Boolean] = FDBTupleConverter(
    size = 1,
    fromTuple = (tuple, index) => Option(tuple.getBoolean(index)),
    toTuple = str => Tuple.from(str)
  )

  given string: FDBTupleConverter[String] = FDBTupleConverter(
    size = 1,
    fromTuple = (tuple, index) => Option(tuple.getString(index)),
    toTuple = str => Tuple.from(str)
  )

  given long: FDBTupleConverter[Long] = FDBTupleConverter(
    size = 1,
    fromTuple = (tuple, index) => Option(tuple.getLong(index)),
    toTuple = value => Tuple.from(Long.box(value))
  )

  given int: FDBTupleConverter[Int] = long.biMap(_.toInt)(_.toLong)

  given float: FDBTupleConverter[Float] = FDBTupleConverter(
    size = 1,
    fromTuple = (tuple, idx) => Option(tuple.getFloat(idx)),
    toTuple = value => Tuple.from(Float.box(value))
  )

  given unit: FDBTupleConverter[Unit] = FDBTupleConverter(
    size = 0,
    fromTuple = (_, _) => Some(()),
    toTuple = _ => Tuple.from()
  )

  given uuid: FDBTupleConverter[UUID] = FDBTupleConverter(
    size = 1,
    fromTuple = (tuple, index) => Option(tuple.getUUID(index)),
    toTuple = Tuple.from(_)
  )

  val radixId: FDBTupleConverter[RadixId] = FDBTupleConverter(
    size = 1,
    fromTuple = (tuple, index) => Option(tuple.getString(index)).flatMap(ModelIdRegistry.parser.parse),
    toTuple = value => Tuple.from(value.idString)
  )

  val userId: FDBTupleConverter[UserId] = FDBTupleConverter(
    size = 1,
    fromTuple = (tuple, index) => Option(tuple.getString(index)).flatMap(UserIdParser.parse),
    toTuple = value => Tuple.from(value.idString)
  )

  given generatedEnum: [T <: GeneratedEnum] => GeneratedEnumCompanion[T] => FDBTupleConverter[T] =
    FDBTupleConverter.int.biMap(summon[GeneratedEnumCompanion[T]].fromValue)(_.value)

  given childRadixId: [T <: RadixId] => TypeTest[RadixId, T] => FDBTupleConverter[T] =
    FDBTupleConverter[T](
      size = 1,
      fromTuple = (tuple, index) => {
        Option(tuple.getString(index)).flatMap(ModelIdRegistry.parser.parseAs[T])
      },
      toTuple = id => Tuple.from(id.idString)
    )

  given instant: FDBTupleConverter[Instant] = {
    FDBTupleConverter.long
      .concat(FDBTupleConverter.long)
      .biMap { case (seconds, nanos) =>
        Instant.ofEpochSecond(seconds, nanos)
      } { timestamp =>
        timestamp.getEpochSecond -> timestamp.getNano.toLong
      }
  }

  given localDateMessage: FDBTupleConverter[LocalDateMessage] =
    FDBTupleConverter.int
      .concat(FDBTupleConverter.int)
      .concat(FDBTupleConverter.int)
      .biMap { case (yearAndMonth, dayOfMonth) =>
        LocalDateMessage(
          yearAndMonth._1,
          yearAndMonth._2,
          dayOfMonth
        )
      } { key =>
        ((key.year, key.month), key.dayOfMonth)
      }

  given documentStorageId: FDBTupleConverter[DocumentStorageId] = FDBTupleConverter[DocumentStorageId](
    size = 1,
    fromTuple = (tuple, index) => Option(tuple.getString(index)).map(DocumentStorageId(_)),
    toTuple = storageId => Tuple.from(storageId.id)
  )

}
