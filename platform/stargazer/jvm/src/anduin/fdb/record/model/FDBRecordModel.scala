// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record.model

import scalapb.GeneratedMessage
import com.google.protobuf.{DynamicMessage, Message}
import scala.util.Try

sealed trait FDBRecordModel[M] { self =>
  def toJavaMessage(model: M): Message
  def fromJavaMessage(javaMessage: Message): Try[M]
  def typeNames: List[String]
}

object FDBRecordModel {

  def apply[M](types: List[String])(to: M => Message)(from: Message => Try[M]): FDBRecordModel[M] = {
    new FDBRecordModel[M] {
      def toJavaMessage(model: M): Message = to(model)

      def fromJavaMessage(javaMessage: Message): Try[M] = from(javaMessage)

      def typeNames: List[String] = types
    }
  }

  given protoInstance
    : [M <: GeneratedMessage] => (companion: scalapb.GeneratedMessageCompanion[M]) => FDBRecordModel[M] =
    FDBRecordModel[M](List(companion.scalaDescriptor.name)) { model =>
      DynamicMessage.parseFrom(companion.javaDescriptor, companion.toByteArray(model))
    } { javaMessage =>
      companion.validate(javaMessage.toByteArray)
    }

}
