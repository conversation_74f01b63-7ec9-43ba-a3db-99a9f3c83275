// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record

import com.apple.foundationdb.record.provider.foundationdb.FDBStoreTimer
import scala.jdk.CollectionConverters.*
import scala.util.Random

import zio.{URIO, ZIO}
import zio.telemetry.opentelemetry.tracing.Tracing

import com.anduin.stargazer.service.GondorBackendConfig.FoundationDbConfig
import com.anduin.stargazer.service.utils.ZIOTelemetryUtils

final case class FDBStoreMetric(store: FDBStoreTimer) {

  override def toString: String = {
    store.getKeysAndValues.asScala.iterator
      .map { case (name, count) =>
        s"$name: $count"
      }
      .mkString("\n")
  }

  def getEvents: Seq[FDBStoreMetric.EventMetric] = {
    store.getEvents.asScala.toSeq.map { event =>
      FDBStoreMetric.EventMetric(
        event.name(),
        store.getCount(event),
        store.getTimeNanos(event)
      )
    }
  }

  def traceVerboseEvents(foundationDbConfig: FoundationDbConfig): URIO[Tracing, Unit] = {
    ZIO
      .when(foundationDbConfig.verboseTraceEvent || (Random.nextInt(100) < foundationDbConfig.traceEventSampleRate)) {
        val attributes = store.getEvents.asScala.toList.flatMap { event =>
          List(
            s"fdb_event_count_${event.name().toLowerCase}" -> store.getCount(event).toString,
            s"fdb_event_time_nanos_${event.name().toLowerCase}" -> store.getTimeNanos(event).toString
          )
        }.toMap
        ZIOTelemetryUtils.setAttributes(attributes)
      }
      .unit

  }

}

object FDBStoreMetric {

  final case class EventMetric(
    name: String,
    count: Int,
    timeNanos: Long
  )

}
