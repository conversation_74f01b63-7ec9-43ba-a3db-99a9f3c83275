// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record

import java.util.concurrent.{CompletableFuture, TimeUnit}
import java.util.function
import scala.jdk.CollectionConverters.SeqHasAsJava
import scala.jdk.FunctionConverters.enrichAsJavaBiFunction
import com.apple.foundationdb.record.*
import com.apple.foundationdb.record.provider.foundationdb.{FDBDatabase, FDBRecordContext, FDBStoreTimer}
import com.apple.foundationdb.record.provider.foundationdb.FDBExceptions.FDBStoreTransactionConflictException
import io.opentelemetry.api.trace.SpanKind
import zio.{Chunk, Task, ZEnvironment, ZIO}
import anduin.fdb.record.model.{FDBIndexMapping, FDBRecordMapping, RecordIO, RecordReadTask}
import anduin.telemetry.TelemetryEnvironment
import com.anduin.stargazer.apps.stargazer.StargazerSettings
import com.anduin.stargazer.service.utils.{ZIOTelemetryUtils, ZIOUtils}
import com.apple.foundationdb.record.util.Result
import zio.stream.ZStream

import scala.util.{Failure, Success}

object FDBReadDatabase extends FDBCommonDatabase(FDBCluster.ReadOnly) {

  private val foundationDbConfig = StargazerSettings.gondorConfig.backendConfig.foundationDbReadOnlyConfig

  private val tracingServiceName = "service/fdb-readonly"

  private val slowTime = Some(zio.Duration(1, TimeUnit.SECONDS).toNanos)

  private[fdb] lazy val recordDatabase: FDBDatabase = getDatabase(foundationDbConfig)

  lazy val environment: ZEnvironment[FDBDatabase] = ZEnvironment(recordDatabase)

  private inline def executeCtxT[T](
    stores: FDBRecordStoreProvider[? <: FDBRecordEnum]*
  )(
    contextFn: FDBRecordContext => Task[T]
  ): Task[T] = {
    executeCtx(stores.map(_.subspaceEnum))(contextFn).provideEnvironment(environment)
  }

  private inline def executeCtx[T](
    enums: Seq[FDBRecordEnum]
  )(
    contextFn: FDBRecordContext => Task[T]
  ): Task[T] = {
    val records = enums.map(_.entryName).mkString(",")
    val task = ZIO.scoped {
      for {
        recordDatabase <- ZIO.service[FDBDatabase]
        runner <- ZIO.fromAutoCloseable(
          ZIO.succeed(recordDatabase.newRunner(new FDBStoreTimer(), null)) // scalafix:ok DisableSyntax.null
        )
        result <- ZIO
          .fromCompletableFuture {
            val completableFuture: function.Function[FDBRecordContext, CompletableFuture[? <: (T, FDBStoreTimer)]] = {
              (t: FDBRecordContext) =>
                {
                  ZIOUtils.unsafeRunToCompletableFuture(
                    contextFn(t).map((_, t.getTimer)),
                    RecordIO.recordIORuntime
                  )
                }
            }
            runner.runAsync[(T, FDBStoreTimer)](
              completableFuture,
              ((t: (T, FDBStoreTimer), u: Throwable) => Result.of(t, u)).asJava,
              List[Object]("records", records).asJava
            )
          }
          .tapErrorCause(e => ZIO.logWarningCause(s"Transaction failed on stores: $records, error: ", e))
      } yield result
    }
    ZIOTelemetryUtils
      .traceWithChildSpan(
        spanName = tracingServiceName,
        spanKind = SpanKind.CLIENT,
        attributes = Map(
          "stores" -> records
        ),
        lowSampleRate = true
      )(
        ZIOTelemetryUtils.injectMetrics("gondor_fdb_readonly", Map("stores" -> records), slowTime) {
          task.map(_._1)
        }
      )
      .retryWhile {
        case _: FDBStoreTransactionConflictException => true
        case _                                       => false
      }
      .provideEnvironment(environment ++ TelemetryEnvironment.defaultEnvironment)
      .onExecutor(RecordIO.recordIOExecutor)
  }

  def transact[T, O](
    opProvider: FDBOperationProvider[O]
  )(
    f: O => RecordReadTask[T]
  ): Task[T] = {
    executeCtx(opProvider.subspaceEnums.toSeq) { ctx =>
      for {
        op <- opProvider.storeBuilder(ctx)
        res <- f(op).toZIO
      } yield res
    }
  }

  def transact[T, S1 <: FDBRecordEnum](
    store: FDBRecordStoreProvider[S1]
  )(
    f: FDBRecordStore[S1] => RecordReadTask[T]
  ): Task[T] = {
    executeCtxT(store) { ctx =>
      for {
        s1 <- store.storeBuilder(ctx)
        res <- f(s1).toZIO
      } yield res
    }
  }

  def largeScan[K, M, S <: FDBRecordEnum](
    store: FDBRecordStoreProvider[S],
    mapping: FDBRecordMapping[K, M, S],
    tupleRange: TupleRange,
    fn: (Int /* loop count */, List[(K, M)]) => zio.Task[Unit],
    limit: Int,
    executeProperties: ExecuteProperties =
      ExecuteProperties.newBuilder().setIsolationLevel(IsolationLevel.SNAPSHOT).build()
  ): zio.Task[Unit] = {
    val executePropertiesWithLimit = executeProperties.setReturnedRowLimit(limit)
    val scanProperties = new ScanProperties(executePropertiesWithLimit)
    val task = for {
      _ <- ZIO.logInfo(s"Start largeScan on ${store.subspaceEnum}")
      _ <- ZIOUtils.tailRecM[Any, Throwable, (Int, Option[Array[Byte]]), Unit](
        (1, None)
      ) { case (cntLoop, continuation) =>
        for {
          (newList, newContinuation) <- transact(store) { s =>
            s.scanWithContinue(
              tupleRange,
              scanProperties,
              continuation.orNull
            )
          }
          modelList = newList.flatMap { entry =>
            for {
              key <- mapping.recordKey.fromTuple(entry.getPrimaryKey, 0)
              value <- mapping.recordModel.fromJavaMessage(entry.getRecord).toOption
            } yield key -> value
          }
          _ <- fn(cntLoop, modelList)
          _ <- ZIO.logInfo(s"------------------- [largeScan] done $cntLoop-th loop")
        } yield {
          Either.cond(
            newContinuation.isEmpty,
            (),
            (cntLoop + 1, newContinuation)
          )
        }
      }
      _ <- ZIO.logInfo(s"Done largeScan on ${store.subspaceEnum}")
    } yield ()
    task.tapErrorCause { error =>
      ZIO.logErrorCause(s"Failed to  largeScan on ${store.subspaceEnum}", error)
    }
  }

  def largeScanIndex[M, S <: FDBRecordEnum, R](
    store: FDBRecordStoreProvider[S],
    mapping: FDBIndexMapping[M, S],
    tupleRange: TupleRange,
    indexScanType: IndexScanType,
    fn: List[M] => zio.Task[R],
    transactionLimit: Int,
    executeProperties: ExecuteProperties = ExecuteProperties.SERIAL_EXECUTE,
    isReverse: Boolean = false,
    maximumIterationsOpt: Option[Int] = None
  ): zio.Task[List[R]] = {
    val executePropertiesWithLimit = executeProperties.setReturnedRowLimit(transactionLimit)
    val scanProperties = new ScanProperties(executePropertiesWithLimit, isReverse)
    val task = for {
      _ <- ZIO.logInfo(s"Start largeScanIndex on store ${store.subspaceEnum}, index ${mapping.index}")
      resultList <- ZIOUtils
        .tailRecM[Any, Throwable, (Int, Option[Array[Byte]], List[R]), List[R]](
          (1, None, List.empty[R])
        ) { case (cntLoop, continuation, curList) =>
          for {
            (messageList, newContinuation) <- transact(store) { s =>
              s.scanIndexWithContinue(
                mapping,
                indexScanType,
                tupleRange,
                continuation.orNull,
                scanProperties
              )
            }
            modelList = messageList.flatMap { entry =>
              mapping.recordModel.fromJavaMessage(entry.getRecord).toOption
            }
            result <- fn(modelList)
            newList <- ZIO.succeed(curList.prepended(result))
            _ <- ZIO.logInfo(s"------------------- [largeScanIndex] done $cntLoop-th loop")
          } yield {
            Either.cond(
              newContinuation.isEmpty || maximumIterationsOpt.exists(cntLoop >= _),
              newList.reverse,
              (cntLoop + 1, newContinuation, newList)
            )
          }
        }
      _ <- ZIO.logInfo(s"Done largeScanIndex on store ${store.subspaceEnum}, index ${mapping.index}")
    } yield resultList
    task.tapErrorCause { error =>
      ZIO.logErrorCause(s"Failed to largeScanIndex on ${store.subspaceEnum}, index ${mapping.index} ", error)
    }
  }

  def largeScanStream[K, M, S <: FDBRecordEnum](
    store: FDBRecordStoreProvider[S],
    mapping: FDBRecordMapping[K, M, S],
    tupleRange: TupleRange,
    limit: Int,
    executeProperties: ExecuteProperties = ExecuteProperties.SERIAL_EXECUTE
  ): Task[ZStream[Any, Throwable, (K, M)]] = {
    val executePropertiesWithLimit = executeProperties.setReturnedRowLimit(limit)
    val scanProperties = new ScanProperties(executePropertiesWithLimit)
    val task = for {
      _ <- ZIO.logInfo(s"Start largeScanStream on ${store.subspaceEnum}")
      stream <- ZIO.attempt {
        zio.stream.ZStream.unfoldChunkZIO[Any, Throwable, (K, M), (Int, Option[Array[Byte]])](
          (1, None)
        ) { (loopCnt, continuation) =>
          if (loopCnt > 1 && continuation.isEmpty) {
            ZIO.none <* ZIO.logInfo(s"Done largeScanStream on ${store.subspaceEnum} after ${loopCnt - 1} loop")
          } else {
            transact(store) { s =>
              s.scanChunkWithContinue(
                tupleRange,
                scanProperties,
                continuation.orNull
              )
            }.map { (messageChunk, newContinuation) =>
              val dataChunk = for {
                message <- messageChunk
                key <- mapping.recordKey.fromTuple(message.getPrimaryKey, 0) match {
                  case Some(value) => Chunk.single(value)
                  case None        => Chunk.empty
                }
                value <- mapping.recordModel.fromJavaMessage(message.getRecord) match {
                  case Failure(_)     => Chunk.empty
                  case Success(value) => Chunk.single(value)
                }
              } yield key -> value

              Some(dataChunk -> (loopCnt + 1, newContinuation))
            } <* ZIO.logInfo(s"------------------- [largeScanStream] done $loopCnt-th loop")
          }
        }
      }
    } yield stream
    task.tapErrorCause { error =>
      ZIO.logErrorCause(s"Failed to largeScanStream on ${store.subspaceEnum}", error)
    }
  }

  def largeScanIndexStream[M, S <: FDBRecordEnum](
    store: FDBRecordStoreProvider[S],
    mapping: FDBIndexMapping[M, S],
    tupleRange: TupleRange,
    indexScanType: IndexScanType,
    transactionLimit: Int,
    executeProperties: ExecuteProperties = ExecuteProperties.SERIAL_EXECUTE,
    isReverse: Boolean = false
  ): Task[ZStream[Any, Throwable, M]] = {
    val executePropertiesWithLimit = executeProperties.setReturnedRowLimit(transactionLimit)
    val scanProperties = new ScanProperties(executePropertiesWithLimit, isReverse)

    val task = for {
      _ <- ZIO.logInfo(s"Start largeScanIndexStream on store ${store.subspaceEnum}, index ${mapping.index}")
      stream <- ZIO.attempt {
        zio.stream.ZStream.unfoldChunkZIO[Any, Throwable, M, (Int, Option[Array[Byte]])](
          (1, None)
        ) { (loopCnt, continuation) =>
          if (loopCnt > 1 && continuation.isEmpty) {
            ZIO.none <* ZIO.logInfo(
              s"Done largeScanIndexStream on store ${store.subspaceEnum}, index ${mapping.index} after ${loopCnt - 1} loop"
            )
          } else {
            transact(store) { s =>
              s.scanIndexChunkWithContinue(
                mapping,
                indexScanType,
                tupleRange,
                continuation.orNull,
                scanProperties
              )
            }.map { (messageChunk, newContinuation) =>
              val dataChunk = for {
                message <- messageChunk
                value <- mapping.recordModel.fromJavaMessage(message.getRecord) match {
                  case Failure(_)     => Chunk.empty
                  case Success(value) => Chunk.single(value)
                }
              } yield value

              Some(dataChunk -> (loopCnt + 1, newContinuation))
            } <* ZIO.logInfo(s"------------------- [largeScanIndexStream] done $loopCnt-th loop")
          }
        }
      }
    } yield stream

    task.tapErrorCause { error =>
      ZIO.logErrorCause(s"Failed to largeScanIndexStream on ${store.subspaceEnum}, index ${mapping.index} ", error)
    }
  }

  override def read[T, O](opProvider: FDBOperationProvider[O])(f: O => RecordReadTask[T]): Task[T] = {
    transact(opProvider)(f)
  }

  override def read[T, S <: FDBRecordEnum](
    storeProvider: FDBRecordStoreProvider[S]
  )(
    f: FDBRecordStore[S] => RecordReadTask[T]
  ): Task[T] = {
    transact(storeProvider)(f)
  }

}
