// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record.cache

import anduin.fdb.record.model.{RecordIO, RecordReadIO}
import zio.UIO
import zio.cache.{Cache, Lookup}
import scala.concurrent.duration.FiniteDuration
import scala.jdk.DurationConverters.*

final case class RecordCache[I, E, V] private (private val underlying: Cache[I, E, V]) {

  def get(key: I): RecordReadIO[Any, E, V] = {
    RecordReadIO.fromZIO {
      underlying.get(key)
    }
  }

  def invalidate(key: I): RecordIO[Any, E, Unit] = {
    RecordIO.fromZIO {
      underlying.invalidate(key)
    }
  }

  def invalidateAll: RecordIO[Any, E, Unit] = {
    RecordIO.fromZIO {
      underlying.invalidateAll
    }
  }

}

object RecordCache {

  def make[I, K, E, V](
    capacity: Int,
    timeToLive: FiniteDuration,
    lookup: I => RecordIO[Any, E, V],
    keyBy: I => K
  ): UIO[RecordCache[I, E, V]] = {
    Cache
      .makeWithKey[I, K, Any, E, V](
        capacity,
        Lookup { input =>
          lookup(input).toZIO
        }
      )(
        timeToLive = _ => timeToLive.toJava,
        keyBy = keyBy
      )
      .map(RecordCache(_))
  }

}
