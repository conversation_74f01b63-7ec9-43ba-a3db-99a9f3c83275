// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record

import zio.{Task, ZIO}

import anduin.fdb.utils.FDBUtils
import com.apple.foundationdb.record.{ExecuteProperties, IsolationLevel, RecordCursor}
import zio.stream.ZStream

import scala.jdk.CollectionConverters.*

private[record] object FDBRecordUtils {

  def recordCursorToZStream[T](
    recordCursor: RecordCursor[T]
  ): Task[ZStream[Any, Throwable, T]] = {
    FDBUtils.asyncIteratorToZStream(recordCursor.asIterator())
  }

  def recordCursorToList[T](
    recordCursor: RecordCursor[T]
  ): Task[List[T]] = {
    ZIO.fromCompletableFuture(recordCursor.asList()).map(_.asScala.toList)
  }

  def constructExecutionProperties(
    isolationLevel: IsolationLevel,
    limit: Option[Int]
  ): ExecuteProperties = {
    val withIsolationLevelProperty = ExecuteProperties.newBuilder().setIsolationLevel(isolationLevel).build()
    limit.fold(withIsolationLevelProperty)(withIsolationLevelProperty.setReturnedRowLimit)
  }

}
