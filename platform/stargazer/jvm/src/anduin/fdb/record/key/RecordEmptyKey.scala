// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record.key

import com.apple.foundationdb.record.metadata.expressions.{EmptyKeyExpression, KeyExpression}
import scalapb.GeneratedMessage

trait RecordEmptyKey[M <: GeneratedMessage] extends <PERSON><PERSON><PERSON>[M, EmptyTuple] { self =>

  override def toJava: KeyExpression = EmptyKeyExpression.EMPTY

  def groupBy[K](groupKey: RecordKey[M, K]): RecordGroupKey[M, EmptyTuple, K] = new RecordGroupKey[M, EmptyTuple, K] {

    override def groupedValue = self

    override def groupBy = groupKey
  }

}
