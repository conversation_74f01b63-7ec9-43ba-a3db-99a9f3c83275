// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record.key

import com.apple.foundationdb.record.metadata.expressions.{EmptyKeyExpression, GroupingKeyExpression, KeyExpression}
import scalapb.GeneratedMessage

trait RecordGroupKey[M <: GeneratedMessage, T, K] extends RecordKey[M, K *: T *: EmptyTuple] {

  given CanEqual[KeyExpression, EmptyKeyExpression] = CanEqual.derived
  def groupedValue: RecordKey[M, T]
  def groupBy: RecordKey[M, K]

  def toJava: GroupingKeyExpression = {
    if (groupedValue.toJava == EmptyKeyExpression.EMPTY) {
      new GroupingKeyExpression(groupBy.toJava, 0)
    } else {
      GroupingKeyExpression.of(groupedValue.toJava, groupBy.toJava)
    }
  }

}

object RecordGroupKey {

  def apply[M <: GeneratedMessage, K](
    groupKey: RecordKey[M, K]
  ): RecordGroupKey[M, EmptyTuple, K] = new RecordGroupKey[M, EmptyTuple, K] {
    override def groupedValue: <PERSON>Key[M, EmptyTuple] = new RecordEmptyKey[M] {}
    override def groupBy: RecordKey[M, K] = groupKey
  }

}
