// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record.key

import anduin.fdb.record.RecordFieldMapper
import anduin.fdb.record.key.RecordFieldKey.Macros
import anduin.protobuf.InstantMessage
import com.apple.foundationdb.record.metadata.Key.Evaluated
import com.apple.foundationdb.record.metadata.expressions.KeyExpression
import scalapb.{GeneratedMessage, GeneratedMessageCompanion}

trait RecordKey[M <: GeneratedMessage, T] { self =>

  def toJava: KeyExpression

  override def toString = toJava.toString

}

object RecordKey {

  val nestedInstant = InstantMessage.key(_.seconds) *: InstantMessage.key(_.nanos)

  def empty[M <: GeneratedMessage]: RecordEmptyKey[M] = new RecordEmptyKey[M] {}

  given recordKeyToJava: Conversion[RecordKey[?, ?], KeyExpression] = (key: RecordKey[?, ?]) => key.toJava

  given recordKeyToConcatKey
    : [M <: GeneratedMessage, T] => Conversion[RecordKey[M, T], RecordConcatKey[M, T *: EmptyTuple]] =
    (key: RecordKey[M, T]) => {
      new RecordConcatKey[M, T *: EmptyTuple] {
        def children = List(key)
      }
    }

  private[key] final class KeyPartiallyApplied[M <: GeneratedMessage](val dummy: Boolean = true) extends AnyVal {

    inline def apply[PT, ST](
      inline selector: M => ST
    )(
      using RecordFieldMapper[PT, ST]
    ): RecordFieldKey[M, PT] = {
      apply(selector, KeyExpression.FanType.None, Evaluated.NullStandin.NULL)
    }

    inline def apply[PT, ST](
      inline selector: M => ST,
      inline fanType: KeyExpression.FanType,
      inline nullStandin: Evaluated.NullStandin
    )(
      using RecordFieldMapper[PT, ST]
    ): RecordFieldKey[M, PT] = {
      ${ Macros.fieldImpl[M, PT, ST]('selector, 'fanType, 'nullStandin) }
    }

    inline def scalarNotNull[PT, ST](
      inline selector: M => ST
    )(
      using RecordFieldMapper[PT, ST]
    ): RecordFieldKey[M, PT] = apply(
      selector,
      KeyExpression.FanType.None,
      Evaluated.NullStandin.NOT_NULL
    )

  }

  def key[M <: GeneratedMessage] = new KeyPartiallyApplied[M]

  private[key] final class KeyOptPartiallyApplied[M <: GeneratedMessage](val dummy: Boolean = true) extends AnyVal {

    inline def apply[PT, ST](
      inline selector: M => Option[ST],
      inline fanType: KeyExpression.FanType,
      inline nullStandin: Evaluated.NullStandin
    )(
      using RecordFieldMapper[PT, ST]
    ): RecordFieldKey[M, PT] = {
      ${ Macros.fieldImpl[M, PT, Option[ST]]('selector, 'fanType, 'nullStandin) }
    }

    inline def apply[PT, ST](
      inline selector: M => Option[ST]
    )(
      using RecordFieldMapper[PT, ST]
    ): RecordFieldKey[M, PT] = {
      apply(selector, KeyExpression.FanType.None, Evaluated.NullStandin.NULL)
    }

    inline def scalarNotNull[PT, ST](
      inline selector: M => Option[ST]
    )(
      using RecordFieldMapper[PT, ST]
    ): RecordFieldKey[M, PT] = apply(
      selector,
      KeyExpression.FanType.None,
      Evaluated.NullStandin.NOT_NULL
    )

  }

  def keyOpt[M <: GeneratedMessage] = new KeyOptPartiallyApplied[M]

  extension [M <: GeneratedMessage, T](companion: GeneratedMessageCompanion[M]) {

    inline def key[PT, ST](
      inline selector: M => ST
    )(
      using RecordFieldMapper[PT, ST]
    ): RecordFieldKey[M, PT] = {
      RecordKey.key[M](selector)
    }

    inline def keyOpt[PT, ST](
      inline selector: M => Option[ST]
    )(
      using RecordFieldMapper[PT, ST]
    ): RecordFieldKey[M, PT] = {
      RecordKey.keyOpt[M](selector)
    }

    inline def scalarKeyNotNull[PT, ST](
      inline selector: M => ST
    )(
      using RecordFieldMapper[PT, ST]
    ): RecordFieldKey[M, PT] = {
      RecordKey.key[M].scalarNotNull(selector)
    }

    inline def key[PT, ST](
      inline selector: M => ST,
      inline fanType: KeyExpression.FanType,
      inline nullStandin: Evaluated.NullStandin
    )(
      using RecordFieldMapper[PT, ST]
    ): RecordFieldKey[M, PT] = {
      RecordKey.key[M](selector, fanType, nullStandin)
    }

  }

  extension [M <: GeneratedMessage, T <: GeneratedMessage](field: RecordFieldKey[M, T]) {

    def nest[S](
      other: RecordKey[T, S]
    ): RecordNestKey[M, T, S] = new RecordNestKey[M, T, S] {
      override def parent = field

      override def child = other
    }

  }

}
