// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record

import com.apple.foundationdb.record.metadata.expressions.{FieldKeyExpression, KeyExpression}
import com.apple.foundationdb.record.metadata.{Index, Key}

import anduin.fdb.record.model.*

trait FDBStoreProviderCompanion[S <: FDBRecordEnum] {

  type RecordEnum = S

  type Store = FDBRecordStore[S]

  type Mapping[K, M] = FDBRecordMapping[K, M, S]

  type AggregateMapping[K, M, R] = FDBAggregateIndexMapping[K, M, R, S]

  type IndexMapping[M] = FDBIndexMapping[M, S]

  type LuceneIndexMapping[K, M] = FDBLuceneIndexMapping[K, M, S]

  type KeyValueIndexMapping[K, V, M] = FDBKeyValueIndexMapping[K, V, M, S]

  protected def mappingInstance[K: FDBTupleConverter, M: FDBRecordModel]: Mapping[K, M] = {
    FDBRecordMapping.instance
  }

  protected def mappingIndexInstance[M: FDBRecordModel](
    index: Index
  ): IndexMapping[M] = {
    FDBIndexMapping.instance(index)
  }

  protected def aggregateMappingInstance[K: FDBTupleConverter, M: FDBRecordModel, R: FDBTupleConverter](
    index: Index
  ): AggregateMapping[K, M, R] = {
    FDBAggregateIndexMapping.instance(index)
  }

  protected def luceneMappingInstance[K: FDBTupleConverter, M: FDBRecordModel](
    index: Index
  ): LuceneIndexMapping[K, M] = {
    FDBLuceneIndexMapping.instance(index)
  }

  protected def keyValueIndexMapping[K: FDBTupleConverter, V: FDBTupleConverter, M: FDBRecordModel](
    index: Index
  ): KeyValueIndexMapping[K, V, M] = {
    FDBKeyValueIndexMapping.instance(index)
  }

  protected def scalarFieldNotNull(fieldName: String): FieldKeyExpression = {
    Key.Expressions.field(
      fieldName,
      KeyExpression.FanType.None,
      Key.Evaluated.NullStandin.NOT_NULL
    )
  }

  def apply(keySpace: FDBRecordKeySpace): FDBRecordStoreProvider[S]

  val Production: FDBRecordStoreProvider[S] = apply(FDBRecordKeySpace)

  val Test: FDBRecordStoreProvider[S] = apply(FDBRecordTestKeySpace)
}
