// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record

import scala.util.{Failure, Try}

import com.google.protobuf.Message

import anduin.fdb.record.model.FDBRecordModel

trait MultiModelStoreProviderCompanion[S <: FDBRecordEnum, U] extends FDBStoreProviderCompanion[S] {

  protected def allCases: List[Mapping[?, ? <: U]]

  protected def toJavaMessage: U => Message

  given allStateRecordModel: FDBRecordModel[U] = {
    val modelMap = {
      for {
        mapping <- allCases
        typeName <- mapping.recordModel.typeNames
      } yield typeName -> mapping.recordModel
    }.toMap
    FDBRecordModel[U](allCases.flatMap(_.recordModel.typeNames)) {
      toJavaMessage
    } { javaMsg =>
      val typeName = javaMsg.getDescriptorForType.getName
      modelMap
        .get(typeName)
        .fold[Try[U]] {
          Failure(new RuntimeException(s"Unable to find correct FDBRecordModel for type $typeName"))
        } { recordModel =>
          recordModel.fromJavaMessage(javaMsg)
        }
    }
  }

}
