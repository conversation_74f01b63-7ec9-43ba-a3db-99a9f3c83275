// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record

import com.apple.foundationdb.record.provider.foundationdb
import com.apple.foundationdb.record.provider.foundationdb.keyspace.KeySpacePath
import com.apple.foundationdb.record.provider.foundationdb.{FDBDatabase, FDBRecordContext, OnlineIndexer}
import com.apple.foundationdb.record.{RecordMetaData, RecordMetaDataBuilder}
import scalapb.GeneratedFileObject
import zio.ZIO

import anduin.fdb.record.model.FDBIndexMapping

abstract class FDBRecordStoreProvider[S <: FDBRecordEnum](
  val subspaceEnum: S,
  val protoFileObject: GeneratedFileObject
) {

  protected type IndexMappingWithVersion = (FDBIndexMapping[?, S], Int)

  protected def recordBuilderFn(builder: RecordMetaDataBuilder): Unit

  protected def indexes: Seq[IndexMappingWithVersion]

  protected def removedIndexes: Seq[IndexMappingWithVersion] = Seq.empty

  protected def keySpace: FDBRecordKeySpace

  protected def version: Option[Int] = None

  private def validateIndexes() = {
    val allIndexes = indexes.map(_ -> false) ++ removedIndexes.map(_ -> true)
    val sortedIndexes = allIndexes.sortBy(_._1._2)
    if (sortedIndexes.nonEmpty && (sortedIndexes.map(_._1._2) != Range.inclusive(1, allIndexes.length))) {
      throw new IllegalStateException(s"Invalid indexes in ${subspaceEnum}!")
    }
    sortedIndexes
  }

  private lazy val rmd: RecordMetaData = {
    val builder = RecordMetaData.newBuilder().setRecords(protoFileObject.javaDescriptor)
    builder.setSplitLongRecords(true)
    recordBuilderFn(builder)
    val allIndexes = validateIndexes()
    allIndexes.foreach { case ((mapping, _), isRemoved) =>
      if (isRemoved) {
        builder.removeIndex(mapping.index.getName)
      } else {
        mapping.recordModel.typeNames match {
          case head :: Nil => builder.addIndex(head, mapping.index)
          case _           => builder.addUniversalIndex(mapping.index)
        }
      }
    }
    version.foreach { version =>
      builder.setVersion(version)
    }
    builder.getRecordMetaData
  }

  private lazy val keySpacePath: KeySpacePath =
    keySpace.keySpace.path(FDBRecordKeySpace.rootDirName, subspaceEnum.entryName)

  private def recordStoreBuilder: foundationdb.FDBRecordStore.Builder = {
    foundationdb.FDBRecordStore.newBuilder().setMetaDataProvider(rmd).setKeySpacePath(keySpacePath)
  }

  private[record] def storeBuilder(
    context: FDBRecordContext
  ): zio.Task[FDBRecordStore[S]] = {
    for {
      fdbStore <- ZIO.fromCompletableFuture {
        recordStoreBuilder.setContext(context).createOrOpenAsync()
      }
      _ <- ZIO.fromCompletableFuture {
        fdbStore.setStateCacheabilityAsync(true)
      }
    } yield new FDBRecordStore(fdbStore)
  }

  private[record] def onlineIndexers: ZIO[FDBDatabase, Throwable, Seq[OnlineIndexer]] = {
    for {
      recordDatabase <- ZIO.service[FDBDatabase]
      builder = recordStoreBuilder
      indexers = indexes
        .filterNot { case (indexMapping, _) =>
          removedIndexes.map(_._1).contains(indexMapping)
        }
        .map { case (mapping, _) =>
          OnlineIndexer
            .newBuilder()
            .setDatabase(recordDatabase)
            .setRecordStoreBuilder(builder)
            .setIndex(mapping.index)
            .build()
        }
    } yield indexers
  }

}
