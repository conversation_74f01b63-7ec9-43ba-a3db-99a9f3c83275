// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record

import java.io.{File, PrintWriter}
import java.net.InetAddress

import com.apple.foundationdb.record.provider.common.StoreTimer
import com.apple.foundationdb.record.provider.foundationdb.FDBExceptions.FDBStoreRetriableException
import com.apple.foundationdb.record.provider.foundationdb.storestate.MetaDataVersionStampStoreStateCacheFactory
import com.apple.foundationdb.record.provider.foundationdb.{
  APIVersion,
  FDBDatabase,
  FDBDatabaseFactory,
  FDBDatabaseFactoryImpl,
  TransactionListener
}
import com.apple.foundationdb.Transaction
import zio.Task

import anduin.fdb.record.model.{FDBIndexMapping, FDBRecordMapping, RecordIO, RecordReadTask}
import com.anduin.stargazer.apps.stargazer.StargazerSettings
import com.anduin.stargazer.service.GondorBackendConfig.FoundationDbConfig
import com.apple.foundationdb.record.{ExecuteProperties, IndexScanType, TupleRange}
import zio.stream.ZStream

trait FDBCommonDatabase(cluster: FDBCluster) {

  private def getClusterFile(foundationDbConfig: FoundationDbConfig): File = {
    val file = File.createTempFile(
      foundationDbConfig.localTempFileName,
      foundationDbConfig.localTempFileExtension,
      new File(foundationDbConfig.localTempDirectory)
    )
    file.deleteOnExit()

    new PrintWriter(file) {
      try {
        val addresses = InetAddress.getAllByName(foundationDbConfig.host)
        val joinedAddress = addresses
          .map { addr => s"${addr.getHostAddress}:${foundationDbConfig.port}" }
          .mkString(",")
        val contents = s"${foundationDbConfig.description}@${joinedAddress}"
        write(contents)
      } finally {
        close()
      }
    }

    scribe.info(s"FDBCluster $cluster using host ${foundationDbConfig.host}")

    file
  }

  private val transactionListener = new TransactionListener {
    override def create(database: FDBDatabase, transaction: Transaction): Unit = {
      if (database.getClusterFile.contains("readonly")) {
        transaction.options().setReadLockAware()
      }
    }

    override def commit(database: FDBDatabase, transaction: Transaction, storeTimer: StoreTimer, exception: Throwable)
      : Unit = ()

    override def close(database: FDBDatabase, transaction: Transaction, storeTimer: StoreTimer): Unit = ()
  }

  private lazy val databaseFactory: FDBDatabaseFactoryImpl = {
    // make sure we initialize the instance in FoundationDbClient first.
    val fdbRecordFactory = FDBDatabaseFactory.instance()

    fdbRecordFactory.setStoreStateCacheFactory(MetaDataVersionStampStoreStateCacheFactory.newInstance())
    fdbRecordFactory.setExecutor(RecordIO.recordLayerScheduler)
    fdbRecordFactory.setTransactionTimeoutMillis(
      StargazerSettings.gondorConfig.backendConfig.servicesTimeout.foundationDb.toMillis
    )
    fdbRecordFactory.setAPIVersion(APIVersion.API_VERSION_7_1)
    fdbRecordFactory.setTransactionListener(transactionListener)
    fdbRecordFactory
  }

  private val maxTryCount = 3

  private def testReadTransaction(db: FDBDatabase, tryCount: Int = 0): Unit = {
    try {
      scribe.info("Testing FDB read transaction")
      val ctx = db.openContext()
      ctx.getReadVersion
      ctx.commit()
    } catch {
      case ex: FDBStoreRetriableException =>
        if (tryCount < maxTryCount) {
          testReadTransaction(db, tryCount + 1)
        } else {
          scribe.warn("Retry read transaction limit exceeded: ", ex)
        }
      case ex => scribe.warn("Failed to test FDBDatabase", ex)
    }
  }

  protected def getDatabase(foundationDbConfig: FoundationDbConfig): FDBDatabase = {
    val db = databaseFactory.getDatabase(getClusterFile(foundationDbConfig).getAbsolutePath)
    sys.addShutdownHook {
      db.close()
    }
    testReadTransaction(db)
    db
  }

  def read[T, O](
    opProvider: FDBOperationProvider[O]
  )(
    f: O => RecordReadTask[T]
  ): Task[T]

  def read[T, S <: FDBRecordEnum](
    storeProvider: FDBRecordStoreProvider[S]
  )(
    f: FDBRecordStore[S] => RecordReadTask[T]
  ): Task[T]

  def largeScanStream[K, M, S <: FDBRecordEnum](
    store: FDBRecordStoreProvider[S],
    mapping: FDBRecordMapping[K, M, S],
    tupleRange: TupleRange,
    limit: Int,
    executeProperties: ExecuteProperties = ExecuteProperties.SERIAL_EXECUTE
  ): Task[ZStream[Any, Throwable, (K, M)]]

  def largeScanIndexStream[M, S <: FDBRecordEnum](
    store: FDBRecordStoreProvider[S],
    mapping: FDBIndexMapping[M, S],
    tupleRange: TupleRange,
    indexScanType: IndexScanType,
    transactionLimit: Int,
    executeProperties: ExecuteProperties = ExecuteProperties.SERIAL_EXECUTE,
    isReverse: Boolean = false
  ): Task[ZStream[Any, Throwable, M]]

}

object FDBCommonDatabase {

  def apply(
  )(
    using cluster: FDBCluster
  ): FDBCommonDatabase = {
    cluster match {
      case FDBCluster.Default  => FDBRecordDatabase
      case FDBCluster.ReadOnly => FDBReadDatabase
    }
  }

}
