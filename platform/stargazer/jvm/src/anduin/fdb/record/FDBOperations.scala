// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record

import com.apple.foundationdb.record.provider.foundationdb.FDBRecordContext
import zio.Task

import anduin.fdb.record.FDBOperationProvider.StoreBuilderTaskMap
import anduin.fdb.record.FDBOperations.FDBKeySpaceEnum
import anduin.fdb.record.model.{RecordReadTask, RecordTask}

// scalafix:off DisableSyntax.asInstanceOf
trait FDBOperations[O] {

  given operationCompanion: FDBOperations[O] = this

  def getProvider(keySpace: FDBRecordKeySpace): FDBOperationProvider[O]

  val Production: FDBOperationProvider[O] = getProvider(FDBRecordKeySpace)

  val Test: FDBOperationProvider[O] = getProvider(FDBRecordTestKeySpace)

  def getProviderCached(
    using keySpaceEnum: FDBKeySpaceEnum
  ): FDBOperationProvider[O] = {
    keySpaceEnum match {
      case FDBKeySpaceEnum.Production => Production
      case FDBKeySpaceEnum.Test       => Test
    }
  }

  def transact[A](task: O => RecordTask[A]): Task[A] = {
    FDBRecordDatabase.transact(Production)(task(_))
  }

  def transactC[A](task: O => FDBRecordContext ?=> RecordTask[A]): Task[A] = {
    FDBRecordDatabase.transactC(Production) { case (ctx, ops) =>
      task(ops)(
        using ctx
      )
    }
  }

  def read[A](
    task: O => RecordReadTask[A]
  )(
    using FDBCluster
  ): Task[A] = {
    FDBCommonDatabase().read(Production)(task)
  }

}

object FDBOperations {

  abstract class Single[S <: FDBRecordEnum, O](
    provider: FDBStoreProviderCompanion[S]
  ) extends FDBOperations[O] {

    def apply(store: FDBRecordStore[S]): O

    def getProvider(keySpace: FDBRecordKeySpace): FDBOperationProvider[O] = {

      new FDBOperationProvider[O] {

        private val underlying = provider(keySpace)

        private[record] val subspaceEnums: Set[FDBRecordEnum] = Set(underlying.subspaceEnum)

        private[record] def storeBuilder(
          ctx: FDBRecordContext,
          taskMap: StoreBuilderTaskMap = FDBOperationProvider.getNewMap
        ): Task[O] = {
          for {
            memoizedTask <- taskMap.flatMap(
              _.computeIfAbsent(
                underlying.subspaceEnum,
                _ => underlying.storeBuilder(ctx).map(apply).memoize
              )
            )
            buildTask <- memoizedTask
            op <- buildTask
          } yield op.asInstanceOf[O]
        }
      }
    }

  }

  abstract class Map[I, O](
    using provider: FDBOperations[I]
  ) extends FDBOperations[O] {

    def apply(store: I): O

    def getProvider(keySpace: FDBRecordKeySpace): FDBOperationProvider[O] = {

      new FDBOperationProvider[O] {

        private val underlying = provider.getProvider(keySpace)

        private[record] val subspaceEnums: Set[FDBRecordEnum] = underlying.subspaceEnums

        private[record] def storeBuilder(
          ctx: FDBRecordContext,
          taskMap: StoreBuilderTaskMap = FDBOperationProvider.getNewMap
        ): Task[O] = {
          underlying.storeBuilder(ctx, taskMap).map(apply)
        }
      }
    }

  }

  abstract class Multi[A, B, O](
    using aProvider: FDBOperations[A],
    bProvider: FDBOperations[B]
  ) extends FDBOperations[O] {

    def combine(a: A, b: B): O

    def getProvider(keySpace: FDBRecordKeySpace): FDBOperationProvider[O] = {
      new FDBOperationProvider[O] {

        private val underlyingA = aProvider.getProvider(keySpace)

        private val underlyingB = bProvider.getProvider(keySpace)

        private[record] val subspaceEnums: Set[FDBRecordEnum] = underlyingA.subspaceEnums ++ underlyingB.subspaceEnums

        private[record] def storeBuilder(
          ctx: FDBRecordContext,
          taskMap: StoreBuilderTaskMap = FDBOperationProvider.getNewMap
        ): Task[O] = {
          for {
            a <- underlyingA.storeBuilder(ctx, taskMap)
            b <- underlyingB.storeBuilder(ctx, taskMap)
          } yield combine(a, b)
        }
      }
    }

  }

  given combine: [A, B] => (aProvider: FDBOperations[A], bProvider: FDBOperations[B]) => Multi[A, B, (A, B)] = {
    new Multi[A, B, (A, B)](
      using aProvider,
      bProvider
    ) {
      def combine(a: A, b: B): (A, B) = a -> b
    }
  }

  abstract class Multi3[A, B, C, O](
    using aProvider: FDBOperations[A],
    bProvider: FDBOperations[B],
    cProvider: FDBOperations[C]
  ) extends FDBOperations[O] {

    def combine(a: A, b: B, c: C): O

    def getProvider(keySpace: FDBRecordKeySpace): FDBOperationProvider[O] = {
      new FDBOperationProvider[O] {

        private val underlyingA = aProvider.getProvider(keySpace)

        private val underlyingB = bProvider.getProvider(keySpace)

        private val underlyingC = cProvider.getProvider(keySpace)

        private[record] val subspaceEnums: Set[FDBRecordEnum] =
          underlyingA.subspaceEnums ++ underlyingB.subspaceEnums ++ underlyingC.subspaceEnums

        private[record] def storeBuilder(
          ctx: FDBRecordContext,
          taskMap: StoreBuilderTaskMap = FDBOperationProvider.getNewMap
        ): Task[O] = {
          for {
            a <- underlyingA.storeBuilder(ctx, taskMap)
            b <- underlyingB.storeBuilder(ctx, taskMap)
            c <- underlyingC.storeBuilder(ctx, taskMap)
          } yield combine(
            a,
            b,
            c
          )
        }
      }
    }

  }

  given combine3: [A, B, C] => (aProvider: FDBOperations[A], bProvider: FDBOperations[B], cProvider: FDBOperations[C])
    => Multi3[A, B, C, (A, B, C)] = {
    new Multi3[A, B, C, (A, B, C)](
      using aProvider,
      bProvider,
      cProvider
    ) {
      def combine(a: A, b: B, c: C): (A, B, C) = (a, b, c)
    }
  }

  abstract class Multi4[A, B, C, D, O](
    using aProvider: FDBOperations[A],
    bProvider: FDBOperations[B],
    cProvider: FDBOperations[C],
    dProvider: FDBOperations[D]
  ) extends FDBOperations[O] {

    def combine(a: A, b: B, c: C, d: D): O

    def getProvider(keySpace: FDBRecordKeySpace): FDBOperationProvider[O] = {
      new FDBOperationProvider[O] {

        private val underlyingA = aProvider.getProvider(keySpace)

        private val underlyingB = bProvider.getProvider(keySpace)

        private val underlyingC = cProvider.getProvider(keySpace)

        private val underlyingD = dProvider.getProvider(keySpace)

        private[record] val subspaceEnums: Set[FDBRecordEnum] =
          underlyingA.subspaceEnums ++ underlyingB.subspaceEnums ++ underlyingC.subspaceEnums ++ underlyingD.subspaceEnums

        private[record] def storeBuilder(
          ctx: FDBRecordContext,
          taskMap: StoreBuilderTaskMap = FDBOperationProvider.getNewMap
        ): Task[O] = {
          for {
            a <- underlyingA.storeBuilder(ctx, taskMap)
            b <- underlyingB.storeBuilder(ctx, taskMap)
            c <- underlyingC.storeBuilder(ctx, taskMap)
            d <- underlyingD.storeBuilder(ctx, taskMap)
          } yield combine(
            a,
            b,
            c,
            d
          )
        }
      }
    }

  }

  given combine4: [A, B, C, D] => (
    aProvider: FDBOperations[A],
    bProvider: FDBOperations[B],
    cProvider: FDBOperations[C],
    dProvider: FDBOperations[D]
  ) => Multi4[A, B, C, D, (A, B, C, D)] = {
    new Multi4[A, B, C, D, (A, B, C, D)](
      using aProvider,
      bProvider,
      cProvider,
      dProvider
    ) {
      def combine(a: A, b: B, c: C, d: D): (A, B, C, D) = (a, b, c, d)
    }
  }

  abstract class Multi5[A, B, C, D, E, O](
    using aProvider: FDBOperations[A],
    bProvider: FDBOperations[B],
    cProvider: FDBOperations[C],
    dProvider: FDBOperations[D],
    eProvider: FDBOperations[E]
  ) extends FDBOperations[O] {

    def combine(a: A, b: B, c: C, d: D, e: E): O

    def getProvider(keySpace: FDBRecordKeySpace): FDBOperationProvider[O] = {
      new FDBOperationProvider[O] {

        private val underlyingA = aProvider.getProvider(keySpace)

        private val underlyingB = bProvider.getProvider(keySpace)

        private val underlyingC = cProvider.getProvider(keySpace)

        private val underlyingD = dProvider.getProvider(keySpace)

        private val underlyingE = eProvider.getProvider(keySpace)

        private[record] val subspaceEnums: Set[FDBRecordEnum] =
          underlyingA.subspaceEnums ++ underlyingB.subspaceEnums ++ underlyingC.subspaceEnums ++ underlyingD.subspaceEnums ++ underlyingE.subspaceEnums

        private[record] def storeBuilder(
          ctx: FDBRecordContext,
          taskMap: StoreBuilderTaskMap = FDBOperationProvider.getNewMap
        ): Task[O] = {
          for {
            a <- underlyingA.storeBuilder(ctx, taskMap)
            b <- underlyingB.storeBuilder(ctx, taskMap)
            c <- underlyingC.storeBuilder(ctx, taskMap)
            d <- underlyingD.storeBuilder(ctx, taskMap)
            e <- underlyingE.storeBuilder(ctx, taskMap)
          } yield combine(
            a,
            b,
            c,
            d,
            e
          )
        }
      }
    }

  }

  given combine5: [A, B, C, D, E] => (
    aProvider: FDBOperations[A],
    bProvider: FDBOperations[B],
    cProvider: FDBOperations[C],
    dProvider: FDBOperations[D],
    eProvider: FDBOperations[E]
  ) => Multi5[A, B, C, D, E, (A, B, C, D, E)] = {
    new Multi5[A, B, C, D, E, (A, B, C, D, E)](
      using aProvider,
      bProvider,
      cProvider,
      dProvider,
      eProvider
    ) {
      def combine(a: A, b: B, c: C, d: D, e: E): (A, B, C, D, E) = (a, b, c, d, e)
    }
  }

  abstract class Multi6[A, B, C, D, E, F, O](
    using aProvider: FDBOperations[A],
    bProvider: FDBOperations[B],
    cProvider: FDBOperations[C],
    dProvider: FDBOperations[D],
    eProvider: FDBOperations[E],
    fProvider: FDBOperations[F]
  ) extends FDBOperations[O] {

    def combine(a: A, b: B, c: C, d: D, e: E, f: F): O

    def getProvider(keySpace: FDBRecordKeySpace): FDBOperationProvider[O] = {
      new FDBOperationProvider[O] {

        private val underlyingA = aProvider.getProvider(keySpace)

        private val underlyingB = bProvider.getProvider(keySpace)

        private val underlyingC = cProvider.getProvider(keySpace)

        private val underlyingD = dProvider.getProvider(keySpace)

        private val underlyingE = eProvider.getProvider(keySpace)

        private val underlyingF = fProvider.getProvider(keySpace)

        private[record] val subspaceEnums: Set[FDBRecordEnum] =
          underlyingA.subspaceEnums ++ underlyingB.subspaceEnums ++ underlyingC.subspaceEnums ++ underlyingD.subspaceEnums ++ underlyingE.subspaceEnums ++ underlyingF.subspaceEnums

        private[record] def storeBuilder(
          ctx: FDBRecordContext,
          taskMap: StoreBuilderTaskMap = FDBOperationProvider.getNewMap
        ): Task[O] = {
          for {
            a <- underlyingA.storeBuilder(ctx, taskMap)
            b <- underlyingB.storeBuilder(ctx, taskMap)
            c <- underlyingC.storeBuilder(ctx, taskMap)
            d <- underlyingD.storeBuilder(ctx, taskMap)
            e <- underlyingE.storeBuilder(ctx, taskMap)
            f <- underlyingF.storeBuilder(ctx, taskMap)
          } yield combine(
            a,
            b,
            c,
            d,
            e,
            f
          )
        }
      }
    }

  }

  given combine6: [A, B, C, D, E, F] => (
    aProvider: FDBOperations[A],
    bProvider: FDBOperations[B],
    cProvider: FDBOperations[C],
    dProvider: FDBOperations[D],
    eProvider: FDBOperations[E],
    fProvider: FDBOperations[F]
  ) => Multi6[A, B, C, D, E, F, (A, B, C, D, E, F)] = {
    new Multi6[A, B, C, D, E, F, (A, B, C, D, E, F)](
      using aProvider,
      bProvider,
      cProvider,
      dProvider,
      eProvider,
      fProvider
    ) {
      def combine(a: A, b: B, c: C, d: D, e: E, f: F): (A, B, C, D, E, F) = (a, b, c, d, e, f)
    }
  }

  abstract class Multi7[A, B, C, D, E, F, G, O](
    using aProvider: FDBOperations[A],
    bProvider: FDBOperations[B],
    cProvider: FDBOperations[C],
    dProvider: FDBOperations[D],
    eProvider: FDBOperations[E],
    fProvider: FDBOperations[F],
    gProvider: FDBOperations[G]
  ) extends FDBOperations[O] {

    def combine(a: A, b: B, c: C, d: D, e: E, f: F, g: G): O

    def getProvider(keySpace: FDBRecordKeySpace): FDBOperationProvider[O] = {
      new FDBOperationProvider[O] {

        private val underlyingA = aProvider.getProvider(keySpace)

        private val underlyingB = bProvider.getProvider(keySpace)

        private val underlyingC = cProvider.getProvider(keySpace)

        private val underlyingD = dProvider.getProvider(keySpace)

        private val underlyingE = eProvider.getProvider(keySpace)

        private val underlyingF = fProvider.getProvider(keySpace)

        private val underlyingG = gProvider.getProvider(keySpace)

        private[record] val subspaceEnums: Set[FDBRecordEnum] =
          underlyingA.subspaceEnums ++ underlyingB.subspaceEnums ++ underlyingC.subspaceEnums ++ underlyingD.subspaceEnums ++ underlyingE.subspaceEnums ++ underlyingF.subspaceEnums ++ underlyingG.subspaceEnums

        private[record] def storeBuilder(
          ctx: FDBRecordContext,
          taskMap: StoreBuilderTaskMap = FDBOperationProvider.getNewMap
        ): Task[O] = {
          for {
            a <- underlyingA.storeBuilder(ctx, taskMap)
            b <- underlyingB.storeBuilder(ctx, taskMap)
            c <- underlyingC.storeBuilder(ctx, taskMap)
            d <- underlyingD.storeBuilder(ctx, taskMap)
            e <- underlyingE.storeBuilder(ctx, taskMap)
            f <- underlyingF.storeBuilder(ctx, taskMap)
            g <- underlyingG.storeBuilder(ctx, taskMap)
          } yield combine(
            a,
            b,
            c,
            d,
            e,
            f,
            g
          )
        }
      }
    }

  }

  given combine7: [A, B, C, D, E, F, G] => (
    aProvider: FDBOperations[A],
    bProvider: FDBOperations[B],
    cProvider: FDBOperations[C],
    dProvider: FDBOperations[D],
    eProvider: FDBOperations[E],
    fProvider: FDBOperations[F],
    gProvider: FDBOperations[G]
  ) => Multi7[A, B, C, D, E, F, G, (A, B, C, D, E, F, G)] = {
    new Multi7[A, B, C, D, E, F, G, (A, B, C, D, E, F, G)](
      using aProvider,
      bProvider,
      cProvider,
      dProvider,
      eProvider,
      fProvider,
      gProvider
    ) {
      def combine(a: A, b: B, c: C, d: D, e: E, f: F, g: G): (A, B, C, D, E, F, G) = (a, b, c, d, e, f, g)
    }
  }

  def apply[O](
    using companion: FDBOperations[O]
  ): FDBOperations[O] = companion

  enum FDBKeySpaceEnum {
    case Production
    case Test
  }

}
