// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record

import anduin.utils.StringUtils
import scalapb.{GeneratedMessage, GeneratedMessageCompanion}

import scala.annotation.implicitNotFound

@implicitNotFound(
  "No RecordFieldNameTransformer found for type ${M}."
)
sealed trait RecordFieldNameTransformer[M <: GeneratedMessage] {

  def transform(scalaFieldName: String): String

}

object RecordFieldNameTransformer {

  private def instance[M <: GeneratedMessage](transformer: String => String): RecordFieldNameTransformer[M] =
    new RecordFieldNameTransformer[M] {

      override def transform(scalaFieldName: String): String = transformer(scalaFieldName)
    }

  given fromMessageCompanion: [M <: GeneratedMessage] => GeneratedMessageCompanion[M] => RecordFieldNameTransformer[M] =
    instance((scalaFieldName: String) =>
      summon[GeneratedMessageCompanion[M]].scalaDescriptor.fields.find(_.scalaName == scalaFieldName).get.name
    )

  def camelCaseToSnakeCase[M <: GeneratedMessage]: RecordFieldNameTransformer[M] =
    instance((scalaFieldName: String) => StringUtils.camelCaseToSnakeCase(scalaFieldName))

}
