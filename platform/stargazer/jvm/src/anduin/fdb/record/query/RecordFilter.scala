// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record.query

import com.apple.foundationdb.record.query.expressions.{Query, QueryComponent}
import scala.jdk.CollectionConverters.*

trait RecordFilter[M] { self =>

  def &&(other: RecordFilter[M]): RecordFilter[M] = new RecordFilter[M] {
    override def toJava: QueryComponent = Query.and(self.toJava, other.toJava)
  }

  def ||(other: RecordFilter[M]): RecordFilter[M] = new RecordFilter[M] {
    override def toJava: QueryComponent = Query.or(self.toJava, other.toJava)
  }

  def unary_! : RecordFilter[M] = new RecordFilter[M] {
    override def toJava = Query.not(self.toJava)
  }

  def toJava: QueryComponent
}

object RecordFilter {

  def and[M](first: RecordFilter[M], second: RecordFilter[M], others: RecordFilter[M]*): RecordFilter[M] =
    new RecordFilter[M] {
      override def toJava: QueryComponent = Query.and(first.toJava, second.toJava, others.map(_.toJava)*)
    }

  def and[M](filters: List[RecordFilter[M]]): RecordFilter[M] =
    new RecordFilter[M] {
      override def toJava: QueryComponent = {
        filters match {
          case Nil         => throw new IllegalArgumentException("RecordFilter.and called with empty list")
          case head :: Nil => head.toJava
          case _           => Query.and(filters.map(_.toJava).asJava)
        }
      }
    }

}
