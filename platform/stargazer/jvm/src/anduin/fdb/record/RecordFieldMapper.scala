// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record

import anduin.protobuf.InstantMessage
import scalapb.{GeneratedEnum, GeneratedMessage, TypeMapper}

import java.time.Instant
import scala.annotation.implicitNotFound

@implicitNotFound(
  """No RecordFieldMapper found for conversion between ${MessageType} and ${ScalaType}"""
)
sealed trait RecordFieldMapper[MessageType, ScalaType]

object RecordFieldMapper {

  private def instance[MessageType, ScalaType](): RecordFieldMapper[MessageType, ScalaType] =
    new RecordFieldMapper[MessageType, ScalaType] {}

  given identityMapper: [T <: GeneratedMessage | GeneratedEnum] => RecordFieldMapper[T, T] = instance()

  given fromTypeMapper: [
    MessageType,
    ScalaType
  ]
    => TypeMapper[MessageType, ScalaType]
    => RecordFieldMapper[MessageType, ScalaType] =
    instance()

  given stringMapper: RecordFieldMapper[String, String] = instance()
  given intMapper: RecordFieldMapper[Int, Int] = instance()
  given longMapper: RecordFieldMapper[Long, Long] = instance()
  given booleanMapper: RecordFieldMapper[Boolean, Boolean] = instance()
  given instantMapper: RecordFieldMapper[InstantMessage, Instant] = instance()

  given seqMapper: [MessageType, ScalaType, F[_] <: Iterable[?]] => RecordFieldMapper[MessageType, ScalaType]
    => RecordFieldMapper[F[MessageType], F[ScalaType]] = instance()

}
