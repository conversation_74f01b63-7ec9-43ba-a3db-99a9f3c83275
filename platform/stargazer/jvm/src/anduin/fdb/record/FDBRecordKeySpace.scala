// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.record

import com.apple.foundationdb.record.provider.foundationdb.keyspace.{
  DirectoryLayerDirectory,
  KeySpace,
  KeySpaceDirectory
}

sealed abstract class FDBRecordKeySpace(
  val rootDirName: String,
  val appDirName: String
) {

  val keySpace: KeySpace = {
    val rootDir = new DirectoryLayerDirectory(rootDirName)
    val appDir = new KeySpaceDirectory(
      appDirName,
      KeySpaceDirectory.KeyType.STRING,
      appDirName
    )

    FDBRecordEnum.values.foreach { recordSpace =>
      val recordDir =
        new KeySpaceDirectory(
          recordSpace.entryName,
          KeySpaceDirectory.KeyType.STRING,
          recordSpace.entryName
        )
      appDir.addSubdirectory(recordDir)
    }

    rootDir.addSubdirectory(appDir)

    new KeySpace(rootDir)
  }

}

object FDBRecordKeySpace extends FDBRecordKeySpace("anduin", "app")
object FDBRecordTestKeySpace extends FDBRecordKeySpace("anduin", "testApp")
