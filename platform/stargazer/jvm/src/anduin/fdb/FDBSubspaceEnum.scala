// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb

import anduin.enumeration.StringEnum

enum FDBSubspaceEnum(val value: String) extends StringEnum {

  case SignatureModule extends FDBSubspaceEnum("sig-mod")
  case SignatureRequest extends FDBSubspaceEnum("sig-req")
  case SignatureRequest<PERSON>tem extends FDBSubspaceEnum("sig-req-item")
  case DocusignEnvelope extends FDBSubspaceEnum("docusign-envelope")

  case TokenMetaData extends FDBSubspaceEnum("token-meta-data")

  case TermData extends FDBSubspaceEnum("termData")
  case TermDocType extends FDBSubspaceEnum("documentTypeData")

  case TinyUrl extends FDBSubspaceEnum("tinyurl")

  case CompositeRole extends FDBSubspaceEnum("roles")
  case ReversedCompositeRole extends FDBSubspaceEnum("roles_reversed")
  case RolePermission extends FDBSubspaceEnum("role_permissions")
  case ResourceToRoles extends FDBSubspaceEnum("role_permissions_indexing")

  case NotificationParams extends FDBSubspaceEnum("notification-params")
  case NotificationSpaceIdIndexing extends FDBSubspaceEnum("notification-space-indexing")
  case Notification extends FDBSubspaceEnum("notifications")
  case NotificationTypeIndexing extends FDBSubspaceEnum("notification-type-indexing")
  case NotificationTypeTimeIndexing extends FDBSubspaceEnum("notification-by-type-time-indexing")

  case NotificationTypeTimeNotificationIdIndexing
      extends FDBSubspaceEnum("notification-by-type-time-notification-id-indexing")

  case NotificationSpaceTypeTimeIndexing extends FDBSubspaceEnum("notification-by-space-type-time-indexing")

  // itracker-modules is a legacy name (should be itracker-user-modules actually)
  case IssueTrackerUserModule extends FDBSubspaceEnum("itracker-modules")
  case IssueTrackerIssue extends FDBSubspaceEnum("itracker-issues")
  case IssueTrackerIssueList extends FDBSubspaceEnum("itracker-issue-lists")
  case UserIssueTracker extends FDBSubspaceEnum("user-itracker")
  case IssueTrackerCollaborationChannel extends FDBSubspaceEnum("itracker-collab-channels")
  case CollaboratorGroup extends FDBSubspaceEnum("collab-groups")
  case OfferingResource extends FDBSubspaceEnum("offering-resources")
  case OfferingAccess extends FDBSubspaceEnum("offering-access")
  case IssueTrackerMatterMapping extends FDBSubspaceEnum("itracker-matter-map")
  case IssueTrackerTrxnMapping extends FDBSubspaceEnum("itracker-trxn-map")
  case IssueTrackerReversedTrxnMapping extends FDBSubspaceEnum("itracker-reversed-trxn-map")
  case IssueTrackerChannelMapping extends FDBSubspaceEnum("itracker-channel-map")
  case IssueTrackerReversedChannelMapping extends FDBSubspaceEnum("itracker-reversed-channel-map")

  case Tags extends FDBSubspaceEnum("tags")
  case ResourceToTag extends FDBSubspaceEnum("rs-tag")
  case TagToResource extends FDBSubspaceEnum("tag-rs")

  case ActionLogger extends FDBSubspaceEnum("ActionEventLoggerSubspace")

  case S3LoggingPublishingState extends FDBSubspaceEnum("S3PublishingStateSubspace")
  case S3CassandraLoggingPublishingState extends FDBSubspaceEnum("S3CassandraPublishingStateSubspace")
  case FSAuditLogExportPublishingState extends FDBSubspaceEnum("FSAuditLogExportPublishingStateSubspace")
  case FSEmailExportPublishingState extends FDBSubspaceEnum("FSEmailExportPublishingStateSubspace")

  case CustomDomain extends FDBSubspaceEnum("custom-domain")
  case CustomDomainIndexing extends FDBSubspaceEnum("cdm-index")
  case CustomDomainByTypeIndexing extends FDBSubspaceEnum("cdm-type-index")

  case CustomDomainToOffering extends FDBSubspaceEnum("cdm-off")
  case OfferingToCustomDomain extends FDBSubspaceEnum("off-cdm")
  case OfferingPrimaryCustomDomain extends FDBSubspaceEnum("off-pri-cdm")

  // Email stores
  case EmailThread extends FDBSubspaceEnum("emtr")
  case EmailAttachment extends FDBSubspaceEnum("emat")
  case EmailContent extends FDBSubspaceEnum("emst")
  case EmailContact extends FDBSubspaceEnum("emct")
  case UserEmailThread extends FDBSubspaceEnum("urem")

  // ShortenUrl
  case ShortenUrl extends FDBSubspaceEnum("surl")

  // Fundsub
  case FundSubGlobalOffering extends FDBSubspaceEnum("fundsub-global-offering")
  case FundSubLpForm extends FDBSubspaceEnum("fundsub-lp-form")
  case FundSubStorageIntegration extends FDBSubspaceEnum("fssi")
  case FundSubTaxFormConfig extends FDBSubspaceEnum("fundsub-tax-form-config")
  case FundSubAdminGeneral extends FDBSubspaceEnum("fundsub-admin-general")
  case OldFormStorage extends FDBSubspaceEnum("oldform")
  case OldFormStorageIndex extends FDBSubspaceEnum("oldform-index")
  case OldFormFileStorage extends FDBSubspaceEnum("oldformfile")
  case OldFormFileStorageIndex extends FDBSubspaceEnum("oldformfile-index")

  // Admin user
  case AdminUser extends FDBSubspaceEnum("adu")
  case AdminUserAuditLog extends FDBSubspaceEnum("adulog")

  // Anouncement
  case Announcement extends FDBSubspaceEnum("anm")

  // Server config
  case ServerConfig extends FDBSubspaceEnum("src")

  // Cookie consent audit log
  case CookieConsentAuditLog extends FDBSubspaceEnum("ccl")
  case UserCookieConsentAuditLog extends FDBSubspaceEnum("ucl")

  // Serverless async response
  case ServerlessAsyncResponse extends FDBSubspaceEnum("svl")

  // OpenFga
  case OpenFgaModelVersionMapping extends FDBSubspaceEnum("openfga-version")
  case OpenFgaObjectMapping extends FDBSubspaceEnum("openfga-object")
  case OpenFgaObjectReverseMapping extends FDBSubspaceEnum("openfga-object-reverse")

  case MultiStoreOpenFgaModelVersionMapping extends FDBSubspaceEnum("multi-openfga-version")
  case MultiStoreOpenFgaObjectMapping extends FDBSubspaceEnum("multi-openfga-object")
  case MultiStoreOpenFgaObjectReverseMapping extends FDBSubspaceEnum("multi-openfga-object-reverse")

  case MultiStoreOpenFgaAuthorizationModelMapping extends FDBSubspaceEnum("multi-openfga-authorization-model")

  // Comment
  case CommentDigestEmailSentTime extends FDBSubspaceEnum("comment-digest-email-sent-time")

  // EmailTemplate
  case PortalEmailTemplateMessage extends FDBSubspaceEnum("portal-email-template")
  case PortalEmailTemplateMessageIndex extends FDBSubspaceEnum("portal-email-template-index")

  // Deprecated Subspaces
  @deprecated case DeprecatedAppCustomDomainIndexing extends FDBSubspaceEnum("app-cdm-index")
  @deprecated case DeprecatedEntityCustomDomainIndexing extends FDBSubspaceEnum("entity-cdm-index")
  @deprecated case DeprecatedSystemCustomDomainIndexing extends FDBSubspaceEnum("system-cdm-index")
  @deprecated case DeprecatedAppPrimaryCustomDomain extends FDBSubspaceEnum("app-cdm-primary")
  @deprecated case DeprecatedEntityPrimaryCustomDomain extends FDBSubspaceEnum("entity-cdm-primary")

  @deprecated case DeprecatedAcmeFlow extends FDBSubspaceEnum("acme-flow")
  @deprecated case DeprecatedAcmeChallenge extends FDBSubspaceEnum("acme-challenge")

  @deprecated case DeprecatedThirdPartyAppEntityIndexing extends FDBSubspaceEnum("entity-app-indexing")
  @deprecated case DeprecatedThirdPartyApp extends FDBSubspaceEnum("apps")
  @deprecated case DeprecatedThirdPartyToken extends FDBSubspaceEnum("app-tokens")
  @deprecated case DeprecatedThirdPartyAppTokenIndexing extends FDBSubspaceEnum("app-token-indexing")

  @deprecated case DeprecatedThirdPartyTokenSecretForwardIndexing
      extends FDBSubspaceEnum("token-secret-forward-indexing")

  @deprecated case DeprecatedThirdPartyTokenSecretRevertIndexing extends FDBSubspaceEnum("token-secret-revert-indexing")

  @deprecated case DeprecatedAmplitudeLoggingState extends FDBSubspaceEnum("AmplitudeLoggingStateSubspace")

  @deprecated case DeprecatedCustomEmailDomain extends FDBSubspaceEnum("cedm")
  @deprecated case DeprecatedCustomEmailDomainIndexing extends FDBSubspaceEnum("cedm-index")
  @deprecated case DeprecatedCustomEmailDomainToOffering extends FDBSubspaceEnum("cedm-off")
  @deprecated case DeprecatedOfferingToCustomEmailDomain extends FDBSubspaceEnum("off-cedm")
  @deprecated case DeprecatedOfferingPrimaryCustomEmailDomain extends FDBSubspaceEnum("off-pri-cedm")

  @deprecated case DeprecatedSpiceDbObjectMapping extends FDBSubspaceEnum("spicedb-object")
  @deprecated case DeprecatedSpiceDbObjectReverseMapping extends FDBSubspaceEnum("spicedb-object-reverse")

  case LastSandboxSetupTimeExport extends FDBSubspaceEnum("sst")
}
