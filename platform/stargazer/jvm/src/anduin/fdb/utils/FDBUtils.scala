// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.utils

import scala.collection.immutable.Queue
import com.apple.foundationdb.async.{AsyncIterable, AsyncIterator, MoreAsyncUtil}
import zio.stream.ZStream
import zio.{Chunk, Task, ZIO}
import anduin.fdb.record.model.RecordIO

import scala.jdk.CollectionConverters.*

private[fdb] object FDBUtils {

  def asyncIterableToList[T](
    iterable: AsyncIterable[T]
  ): Task[List[T]] = {
    ZIO.fromCompletableFuture(iterable.asList).map(_.asScala.toList)
  }

  def asyncIterableToZStream[T](
    iterable: AsyncIterable[T]
  ): Task[ZStream[Any, Throwable, T]] = {
    asyncIteratorToZStream(iterable.iterator())
  }

  def asyncIteratorToList[T](
    iterator: AsyncIterator[T]
  ): Task[List[T]] = {
    ZIO
      .fromCompletableFuture {
        MoreAsyncUtil.reduce(
          RecordIO.recordIOScheduler,
          iterator,
          Queue.empty,
          (q: Queue[T], u: T) => {
            q.appended(u)
          }
        )
      }
      .map(_.toList)
  }

  def asyncIteratorToChunk[T](
    iterator: AsyncIterator[T]
  ): Task[Chunk[T]] = {
    ZIO
      .fromCompletableFuture {
        MoreAsyncUtil.reduce(
          RecordIO.recordIOScheduler,
          iterator,
          Chunk.empty,
          (q: Chunk[T], u: T) => {
            q.appended(u)
          }
        )
      }
  }

  def asyncIteratorToZStream[T](
    iterator: AsyncIterator[T]
  ): Task[ZStream[Any, Throwable, T]] = {
    ZIO.fromCompletableFuture {
      MoreAsyncUtil.reduce(
        RecordIO.recordIOScheduler,
        iterator,
        ZStream.empty,
        (t: ZStream[Any, Throwable, T], u: T) => {
          t ++ ZStream.succeed(u)
        }
      )
    }
  }

}
