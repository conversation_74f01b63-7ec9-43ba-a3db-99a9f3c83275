// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.subspace

import scala.annotation.unused

import com.apple.foundationdb
import com.apple.foundationdb.subspace.Subspace
import com.apple.foundationdb.tuple.Tuple
import com.apple.foundationdb.{KeyValue, ReadTransaction}
import zio.ZIO
import zio.stream.ZStream

import anduin.fdb.FDBSubspaceEnum
import anduin.fdb.client.io.{FDBIO, FDBReadIO}
import anduin.fdb.subspace.FDBKey.FDBKeyError
import anduin.fdb.utils.FDBUtils

final case class FDBIndexSubspace[K: FDBKey](
  subspaceEnum: FDBSubspaceEnum
) {
  private val subspace: Subspace = new Subspace(Tuple.from(subspaceEnum.value))

  private def toKey(tupledKey: Tuple): K = {
    implicitly[FDBKey[K]].fromTuple(tupledKey)
  }

  private def toKey(keyValue: KeyValue): K = {
    toKey(subspace.unpack(keyValue.getKey))
  }

  private def toTupledKey(key: K): Tuple = {
    implicitly[FDBKey[K]].toTuple(key)
  }

  private def toSubspaceKey(key: K): Array[Byte] = {
    subspace.pack(toTupledKey(key))
  }

  private def getKeyRange[P, S](
    prefixKey: P
  )(
    using fdbKey: FDBKey[P],
    @unused keyCombine: FDBKeyCombine[P, S, K]
  ): foundationdb.Range = {
    subspace.range(fdbKey.toTuple(prefixKey))
  }

  def exists(key: K): FDBReadIO[Boolean] = {
    FDBReadIO.fromZIO { trxn =>
      ZIO
        .fromCompletableFuture {
          trxn.get(toSubspaceKey(key))
        }
        .map { byteArray =>
          Option(byteArray).nonEmpty
        }
    }
  }

  def set(key: K): FDBIO[Unit] = {
    FDBIO.fromZIO { trxn =>
      ZIO.attempt {
        trxn.set(toSubspaceKey(key), Array.emptyByteArray)
      }
    }
  }

  def toStream: FDBReadIO[ZStream[Any, Throwable, K]] = {
    FDBReadIO.fromZIO { tx =>
      val range = subspace.range()
      val iterable =
        tx.getRange(
          range.begin,
          range.end,
          ReadTransaction.ROW_LIMIT_UNLIMITED,
          false
        )
      FDBUtils
        .asyncIterableToZStream(iterable)
        .map(_.map(toKey))
    }

  }

  def getRangeFromPrefix[P, S](
    prefixKey: P
  )(
    using fdbKey: FDBKey[P],
    keyCombine: FDBKeyCombine[P, S, K]
  ): FDBReadIO[Seq[S]] = {
    FDBReadIO
      .fromZIO { tx =>
        val range = getKeyRange(prefixKey)
        val iterable =
          tx.getRange(
            range.begin,
            range.end,
            ReadTransaction.ROW_LIMIT_UNLIMITED,
            false
          )
        FDBUtils
          .asyncIterableToList(iterable)
          .map(_.map(toKey))
          .map(_.map(keyCombine.suffix))
      }
  }

  private def getRange(range: foundationdb.Range, limit: Int, reverse: Boolean): FDBReadIO[ZStream[Any, Throwable, K]] = {
    FDBReadIO.fromZIO { tx =>
      val iterable = tx.getRange(
        range.begin,
        range.end,
        limit,
        reverse
      )
      FDBUtils
        .asyncIterableToZStream(iterable)
        .map(_.map(toKey))
    }
  }

  def getRange(from: K, to: K, limit: Int, reverse: Boolean): FDBReadIO[ZStream[Any, Throwable, K]] = {
    val packedFromKey = toSubspaceKey(from)
    val packedToKey = toSubspaceKey(to)
    val range = new foundationdb.Range(packedFromKey, packedToKey)
    getRange(
      range,
      limit,
      reverse
    )
  }

  def getRange(from: K, to: K, limit: Int): FDBReadIO[ZStream[Any, Throwable, K]] = {
    getRange(
      from,
      to,
      limit,
      reverse = false
    )
  }

  def getRange(limit: Int): FDBReadIO[ZStream[Any, Throwable, K]] = {
    getRange(limit, reverse = false)
  }

  def getRange(limit: Int, reverse: Boolean): FDBReadIO[ZStream[Any, Throwable, K]] = {
    getRange(
      subspace.range(),
      limit,
      reverse
    )
  }

  def clearKey(key: K): FDBIO[Unit] = {
    FDBIO.fromZIO { trxn =>
      ZIO.attempt {
        trxn.clear(toSubspaceKey(key))
      }
    }
  }

  def clearKeyError(keyError: FDBKeyError): FDBIO[Unit] = {
    FDBIO.fromZIO { trxn =>
      ZIO.attempt {
        trxn.clear(subspace.pack(keyError.tuple))
      }
    }
  }

  def clearRange[P, S](
    prefixKey: P
  )(
    using fdbKey: FDBKey[P],
    keyCombine: FDBKeyCombine[P, S, K]
  ): FDBIO[Unit] = {
    FDBIO.fromZIO { trxn =>
      ZIO.attempt {
        trxn.clear(getKeyRange(prefixKey))
      }
    }
  }

  def clearAll(): FDBIO[Unit] = {
    FDBIO.fromZIO { trxn =>
      ZIO.attempt {
        trxn.clear(subspace.range())
      }
    }
  }

}
