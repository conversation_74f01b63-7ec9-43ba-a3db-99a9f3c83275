// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.subspace

trait FDBKeyCombine[P, S, K] extends Serializable { self =>
  def prefix(key: K): P
  def suffix(key: K): S
  def combine(prefix: P, suffix: S): K
}

object FDBKey<PERSON>ombine {

  given tupleCombine11: [K1, K2] => FDBKeyCombine[K1, K2, (K1, K2)] =
    new FDBKeyCombine[K1, K2, (K1, K2)] {
      override def prefix(key: (K1, K2)): K1 = key._1
      override def suffix(key: (K1, K2)): K2 = key._2
      override def combine(prefix: K1, suffix: K2): (K1, K2) = (prefix, suffix)
    }

  given tupleCombine12: [K1, K2, K3] => FDBKeyCombine[K1, (K2, K3), (K1, K2, K3)] =
    new FDBKeyCombine[K1, (K2, K3), (K1, K2, K3)] {
      override def prefix(key: (K1, K2, K3)): K1 = key._1
      override def suffix(key: (K1, K2, K3)): (K2, K3) = (key._2, key._3)
      override def combine(prefix: K1, suffix: (K2, K3)): (K1, K2, K3) = (prefix, suffix._1, suffix._2)
    }

  given tupleCombine21: [K1, K2, K3] => FDBKeyCombine[(K1, K2), K3, (K1, K2, K3)] =
    new FDBKeyCombine[(K1, K2), K3, (K1, K2, K3)] {
      override def prefix(key: (K1, K2, K3)): (K1, K2) = (key._1, key._2)
      override def suffix(key: (K1, K2, K3)): K3 = key._3
      override def combine(prefix: (K1, K2), suffix: K3): (K1, K2, K3) = (prefix._1, prefix._2, suffix)
    }

  given tupleCombine31: [K1, K2, K3, K4] => FDBKeyCombine[(K1, K2, K3), K4, (K1, K2, K3, K4)] =
    new FDBKeyCombine[(K1, K2, K3), K4, (K1, K2, K3, K4)] {
      override def prefix(key: (K1, K2, K3, K4)): (K1, K2, K3) = (key._1, key._2, key._3)
      override def suffix(key: (K1, K2, K3, K4)): K4 = key._4

      override def combine(prefix: (K1, K2, K3), suffix: K4): (K1, K2, K3, K4) =
        (prefix._1, prefix._2, prefix._3, suffix)

    }

  given tupleCombine13: [K1, K2, K3, K4] => FDBKeyCombine[K1, (K2, K3, K4), (K1, K2, K3, K4)] =
    new FDBKeyCombine[K1, (K2, K3, K4), (K1, K2, K3, K4)] {
      override def prefix(key: (K1, K2, K3, K4)): K1 = key._1
      override def suffix(key: (K1, K2, K3, K4)): (K2, K3, K4) = (key._2, key._3, key._4)

      override def combine(prefix: K1, suffix: (K2, K3, K4)): (K1, K2, K3, K4) =
        (prefix, suffix._1, suffix._2, suffix._3)

    }

}
