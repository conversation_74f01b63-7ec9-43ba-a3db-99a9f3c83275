// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.subspace

import scala.annotation.unused

import com.apple.foundationdb
import com.apple.foundationdb.subspace.Subspace
import com.apple.foundationdb.tuple.Tuple
import com.apple.foundationdb.{KeyValue, ReadTransaction}
import zio.ZIO
import zio.stream.ZStream

import anduin.fdb.FDBSubspaceEnum
import anduin.fdb.client.exception.{Conflict, NotFound}
import anduin.fdb.client.io.{FDBIO, FDBReadIO}
import anduin.fdb.utils.FDBUtils

final case class FDBSubspace[K: FDBKey, M: FDBModel](
  subspaceEnum: FDBSubspaceEnum
) {
  private val subspace: Subspace = new Subspace(Tuple.from(subspaceEnum.value))

  private def toSubspaceKey(key: K): Array[Byte] = {
    subspace.pack(toTupledKey(key))
  }

  private def toRawValue(entity: (K, M)): Array[Byte] = {
    implicitly[FDBModel[M]].toByteArray(entity._2)
  }

  private def toTupledKey(key: K): Tuple = {
    implicitly[FDBKey[K]].toTuple(key)
  }

  private def toKey(tupledKey: Tuple): K = {
    implicitly[FDBKey[K]].fromTuple(tupledKey)
  }

  private def toEntity(key: K, value: Array[Byte]): (K, M) = {
    val model = implicitly[FDBModel[M]].fromByteArray(value)

    (key, model)
  }

  private def toEntity(keyValue: KeyValue): (K, M) = {
    val keyRepr = toKey(subspace.unpack(keyValue.getKey))
    toEntity(keyRepr, keyValue.getValue)
  }

  def set(key: K, model: M): FDBIO[Unit] = {
    FDBIO.fromZIO { tx =>
      ZIO.attempt {
        val packedKey = toSubspaceKey(key)
        val packedValue = toRawValue((key, model))
        tx.set(packedKey, packedValue)
      }
    }
  }

  def get(key: K): FDBReadIO[Option[(K, M)]] = {
    val packedKey = toSubspaceKey(key)
    FDBReadIO.fromZIO { tx =>
      ZIO
        .fromCompletableFuture {
          tx.get(packedKey)
        }
        .map { byteArray =>
          Option(byteArray).map(toEntity(key, _))
        }
    }
  }

  def getOpt(key: K): FDBReadIO[Option[M]] = {
    get(key).map(_.map(_._2))
  }

  // This method will throw NotFound exception if key is not found
  def getValue(key: K): FDBReadIO[M] = {
    getOpt(key).flatMap {
      _.fold[FDBReadIO[M]](FDBReadIO.failed(NotFound(key.toString))) { value =>
        FDBReadIO.pure(value)
      }
    }
  }

  // This method will throw Conflict exception if key has already existed.
  def create(key: K, model: M): FDBIO[Unit] = {
    getOpt(key).toDBIO.flatMap {
      _.fold[FDBIO[Unit]] {
        set(key, model)
      } { _ =>
        FDBIO.failed(Conflict(key.toString))
      }
    }
  }

  // This method will throw NotFound exception if key is not found
  def update(key: K, updateFunc: M => M): FDBIO[Unit] = {
    getOpt(key).toDBIO.flatMap {
      _.fold[FDBIO[Unit]] {
        FDBIO.failed(NotFound(key.toString))
      } { oldModel =>
        set(key, updateFunc(oldModel))
      }
    }
  }

  def updateOrAdd(key: K, defaultModel: M, updateFunc: M => M): FDBIO[Option[M]] = {
    for {
      modelOpt <- getOpt(key).toDBIO
      _ <- modelOpt.fold[FDBIO[Unit]] {
        set(key, defaultModel)
      } { oldModel =>
        set(key, updateFunc(oldModel))
      }
    } yield modelOpt
  }

  private def getKeyRange[P, S](
    prefixKey: P
  )(
    using fdbKey: FDBKey[P],
    @unused keyCombine: FDBKeyCombine[P, S, K]
  ): foundationdb.Range = {
    subspace.range(fdbKey.toTuple(prefixKey))
  }

  def toStream: FDBReadIO[ZStream[Any, Throwable, (K, M)]] = {
    FDBReadIO.fromZIO { tx =>
      val range = subspace.range()
      val iterable = tx.getRange(
        range.begin,
        range.end,
        ReadTransaction.ROW_LIMIT_UNLIMITED,
        false
      )
      FDBUtils
        .asyncIterableToZStream(iterable)
        .map(_.map(toEntity))
    }

  }

  def toList: FDBReadIO[Seq[(K, M)]] = {
    toStream.mapF(_.runCollect)
  }

  def getLast: FDBReadIO[Option[(K, M)]] = {
    toStream.mapF(_.runLast)
  }

  def getRangeFromPrefixAllKey[P, S](
    prefixKey: P
  )(
    using fdbKey: FDBKey[P],
    keyCombine: FDBKeyCombine[P, S, K]
  ): FDBReadIO[Seq[(K, M)]] = {
    FDBReadIO.fromZIO { tx =>
      val range = getKeyRange(prefixKey)
      val iterable = tx.getRange(
        range.begin,
        range.end,
        ReadTransaction.ROW_LIMIT_UNLIMITED,
        false
      )
      FDBUtils
        .asyncIterableToZStream(iterable)
        .map(_.map(toEntity))
        .flatMap(_.runCollect)
    }
  }

  def getRangeFromPrefix[P, S](
    prefixKey: P
  )(
    using fdbKey: FDBKey[P],
    keyCombine: FDBKeyCombine[P, S, K]
  ): FDBReadIO[Seq[(S, M)]] = {
    getRangeFromPrefixAllKey(prefixKey).map { keySeq =>
      keySeq.map(kv => keyCombine.suffix(kv._1) -> kv._2)
    }
  }

  private def getRange(
    range: foundationdb.Range,
    limit: Int,
    reverse: Boolean
  ): FDBReadIO[ZStream[Any, Throwable, (K, M)]] = {
    FDBReadIO.fromZIO { tx =>
      val iterable = tx.getRange(
        range.begin,
        range.end,
        limit,
        reverse
      )
      FDBUtils
        .asyncIterableToZStream(iterable)
        .map(_.map(toEntity))
    }
  }

  def getRange(from: K, to: K, limit: Int, reverse: Boolean): FDBReadIO[ZStream[Any, Throwable, (K, M)]] = {
    val packedFromKey = toSubspaceKey(from)
    val packedToKey = toSubspaceKey(to)
    val range = new foundationdb.Range(packedFromKey, packedToKey)
    getRange(
      range,
      limit,
      reverse
    )
  }

  def getRange(from: K, to: K, limit: Int): FDBReadIO[ZStream[Any, Throwable, (K, M)]] = {
    getRange(
      from,
      to,
      limit,
      reverse = false
    )
  }

  def getRangeAll(from: K, to: K, limit: Int, reverse: Boolean): FDBReadIO[Seq[(K, M)]] = {
    FDBReadIO.fromZIO { tx =>
      val packedFromKey = toSubspaceKey(from)
      val packedToKey = toSubspaceKey(to)
      val range = new foundationdb.Range(packedFromKey, packedToKey)
      val iterable = tx.getRange(
        range.begin,
        range.end,
        limit,
        reverse
      )
      FDBUtils
        .asyncIterableToZStream(iterable)
        .map(_.map(toEntity))
        .flatMap(_.runCollect)
    }
  }

  def getRange(limit: Int): FDBReadIO[ZStream[Any, Throwable, (K, M)]] = {
    getRange(limit, reverse = false)
  }

  def getRange(limit: Int, reverse: Boolean): FDBReadIO[ZStream[Any, Throwable, (K, M)]] = {
    getRange(
      subspace.range(),
      limit,
      reverse
    )
  }

  def getAll: FDBReadIO[ZStream[Any, Throwable, (K, M)]] = {
    val range = subspace.range()
    FDBReadIO.fromZIO { tx =>
      val iterable = tx.getRange(range)
      FDBUtils
        .asyncIterableToZStream(iterable)
        .map(_.map(toEntity))
    }
  }

  def clearKey(key: K): FDBIO[Unit] = {
    FDBIO.fromZIO { trxn =>
      ZIO.attempt {
        trxn.clear(toSubspaceKey(key))
      }
    }
  }

  def clearPrefix[P, S](
    prefixKey: P
  )(
    using fdbKey: FDBKey[P],
    keyCombine: FDBKeyCombine[P, S, K]
  ): FDBIO[Unit] = {
    FDBIO.fromZIO { trxn =>
      ZIO.attempt {
        trxn.clear(getKeyRange(prefixKey))
      }
    }
  }

  def clearAll(): FDBIO[Unit] = {
    FDBIO.fromZIO { trxn =>
      ZIO.attempt {
        trxn.clear(subspace.range())
      }
    }
  }

}
