// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fdb.subspace

import scala.annotation.unused

import com.apple.foundationdb.subspace.Subspace
import com.apple.foundationdb.tuple.Tuple
import com.apple.foundationdb.{KeyValue, Range}
import scalapb.GeneratedMessage
import zio.stream.{ZPipeline, ZSink, ZStream}
import zio.{Chunk, NonEmptyChunk, ZIO}

import anduin.fdb.FDBSubspaceEnum
import anduin.fdb.client.exception.{Conflict, NotFound}
import anduin.fdb.client.io.{FDBIO, FDBReadIO}
import anduin.fdb.utils.FDBUtils

abstract class FDBChunkSubspace[K: FDBKey, M <: GeneratedMessage: FDBChunkModel] {

  def subspaceEnum: FDBSubspaceEnum
  def chunkLimitInBytes: Int
  def chunkSuffix: Tuple
  protected def subspace: Subspace

  private def toRawValue(key: K, value: M): ZStream[Any, Throwable, (Array[Byte], Array[Byte])] = {
    val valueStream = implicitly[FDBChunkModel[M]].toByteStream(value)

    val tupleKey = toTupledKey(key)
    valueStream
      .via(ZPipeline.grouped(chunkLimitInBytes))
      .zipWithIndex
      .map { params =>
        val (chunk, index) = params
        val packedValue = chunk.toArray
        val packedKey = tupleKey.add(index + 1)
        (subspace.pack(packedKey), packedValue)
      }

  }

  /** Resulting Tuple will be packed by the subspace before executing all operations. */
  protected def toTupledKey(key: K): Tuple = {
    implicitly[FDBKey[K]].toTuple(key).addAll(chunkSuffix)
  }

  protected def toKey(tupledKey: Tuple): K = {
    implicitly[FDBKey[K]].fromTuple(tupledKey.popBack()) // remove suffix
  }

  protected def toEntity(byteStream: ZStream[Any, Throwable, Byte]): zio.Task[Option[M]] = {
    implicitly[FDBChunkModel[M]].fromByteStream(byteStream)
  }

  def set(key: K, model: M): FDBIO[Unit] = {

    for {
      _ <- clearKey(key)
      count <- FDBIO.fromZIO { tx =>
        toRawValue(key, model).map { case (k, v) =>
          tx.set(k, v)
        }.runCount
      }
      _ <- FDBIO.when(count <= 0) {
        FDBIO.fromZIO { tx =>
          ZIO.attempt {
            val tupleKey = toTupledKey(key)
            val packedKey = tupleKey.add(1)
            tx.set(subspace.pack(packedKey), Array.emptyByteArray)
          }
        }
      }
    } yield ()

  }

  private def extract(stream: ZStream[Any, Throwable, KeyValue]): ZStream[Any, Throwable, (K, M)] = {
    stream
      .groupAdjacentBy { keyValue =>
        val tuple = subspace.unpack(keyValue.getKey)
        tuple.popBack() // remove the index
      }
      .flatMap { case (tuple: Tuple, chunk: NonEmptyChunk[KeyValue]) =>
        val key = toKey(tuple)
        val byteChunk = chunk.toChunk.flatMap(c => Chunk.fromArray(c.getValue))
        ZStream
          .fromZIO(toEntity(ZStream.fromChunk(byteChunk)))
          .collectSome
          .map(key -> _)
      }
  }

  def get(key: K): FDBReadIO[Option[(K, M)]] = {
    FDBReadIO.fromZIO { tx =>
      val tupleKey = toTupledKey(key)
      for {
        kvStream <- FDBUtils.asyncIterableToZStream(tx.getRange(Range.startsWith(subspace.pack(tupleKey))))
        value <- extract(kvStream).run(ZSink.last)
      } yield value
    }
  }

  def getOpt(key: K): FDBReadIO[Option[M]] = {
    get(key).map(_.map(_._2))
  }

  // This method will throw NotFound exception if key is not found
  def getValue(key: K): FDBReadIO[M] = {
    getOpt(key).flatMap {
      _.fold[FDBReadIO[M]](FDBReadIO.failed(NotFound(key.toString))) { value =>
        FDBReadIO.pure(value)
      }
    }
  }

  // This method will throw Conflict exception if key has already existed.
  def create(key: K, model: M): FDBIO[Unit] = {
    getOpt(key).toDBIO.flatMap {
      _.fold[FDBIO[Unit]] {
        set(key, model)
      } { _ =>
        FDBIO.failed(Conflict(key.toString))
      }
    }
  }

  // This method will throw NotFound exception if key is not found
  def update(key: K, updateFunc: M => M): FDBIO[M] = {
    getOpt(key).toDBIO.flatMap {
      _.fold[FDBIO[M]] {
        FDBIO.failed(NotFound(key.toString))
      } { oldModel =>
        this.set(key, updateFunc(oldModel)).map(_ => oldModel)
      }
    }
  }

  def updateOrAdd(key: K, defaultModel: M, updateFunc: M => M): FDBIO[Option[M]] = {
    getOpt(key).toDBIO.flatMap {
      _.fold[FDBIO[Option[M]]] {
        set(key, defaultModel).map(_ => None)
      } { oldModel =>
        this.set(key, updateFunc(oldModel)).map(_ => Option(oldModel))
      }
    }
  }

  def clear(): FDBIO[Unit] = {
    FDBIO.fromZIO(tx => ZIO.attempt(tx.clear(subspace.range())))
  }

  /** Clear the whole range start with the key
    *
    * @param key
    *   the key
    * @return
    */
  def clearKey(key: K): FDBIO[Unit] = {
    FDBIO.fromZIO { tx =>
      ZIO.attempt(tx.clear(Range.startsWith(subspace.pack(toTupledKey(key)))))
    }
  }

  private def getKeyRange[P, S](
    prefixKey: P
  )(
    using fdbKey: FDBKey[P],
    @unused keyCombine: FDBKeyCombine[P, S, K]
  ): Range = {
    val tupleKey = fdbKey.toTuple(prefixKey)
    // remove the end border of the tuple as this is not a standalone tuple
    // 1 for the tuple
    // 1 for the begin byte \00
    // see https://github.com/apple/foundationdb/blob/master/design/tuple.md
    val beginWithoutStrCode = subspace.range(tupleKey).begin.dropRight(tupleKey.size() + 1)
    Range.startsWith(beginWithoutStrCode)
  }

  def getRange[P, S](
    prefixKey: P
  )(
    using fdbKey: FDBKey[P],
    keyCombine: FDBKeyCombine[P, S, K]
  ): FDBReadIO[Seq[(S, M)]] = {

    FDBReadIO.fromZIO { tx =>
      for {
        kvStream <- FDBUtils.asyncIterableToZStream(tx.getRange(getKeyRange(prefixKey)))
        values <- extract(kvStream).map(kv => keyCombine.suffix(kv._1) -> kv._2).runCollect
      } yield {
        values
      }
    }
  }

  /** WARNING: This api is depended on FDB transaction restriction which is under 5s If your data is large enough to
    * break this restriction, please use `getAllKeys` to split a giant transaction to multiple smaller ones
    */
  def getAll(): FDBReadIO[ZStream[Any, Throwable, (K, M)]] = {
    FDBReadIO.fromZIO { tx =>
      for {
        kvStream <- FDBUtils.asyncIterableToZStream(tx.getRange(subspace.range()))
      } yield {
        extract(kvStream)
      }
    }
  }

}

object FDBChunkSubspace {

  def apply[K: FDBKey, M <: GeneratedMessage: FDBChunkModel](
    subspaceEnum0: FDBSubspaceEnum,
    // https://apple.github.io/foundationdb/blob.html
    chunkLimitInBytes0: Int = defaultChunkLimit,
    chunkSuffix0: Tuple = defaultTupleSeparator
  ): FDBChunkSubspace[K, M] =
    new FDBChunkSubspace[K, M] {
      override val subspaceEnum: FDBSubspaceEnum = subspaceEnum0
      override val chunkLimitInBytes: Int = chunkLimitInBytes0
      override val chunkSuffix: Tuple = chunkSuffix0
      override protected val subspace = new Subspace(Tuple.from(subspaceEnum.value))
    }

  val defaultTupleSeparator: Tuple = {
    new Tuple().add(true)
  }

  val defaultChunkLimit = 10240 // 10 Kbs

  given tupleCatsEq: cats.Eq[Tuple] = (x: Tuple, y: Tuple) => {
    x.compareTo(y) == 0
  }

  final case class FDBChunkError(message: String) extends RuntimeException(message)
}
