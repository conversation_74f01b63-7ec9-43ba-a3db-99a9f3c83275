package anduin.tflow

import io.temporal.workflow.{UpdateMethod, UpdateValidatorMethod}
import zio.temporal.activity.ZActivityStub
import zio.temporal.state.ZWorkflowState
import zio.temporal.workflow.{<PERSON><PERSON><PERSON>, ZSaga, ZWorkflow}
import zio.temporal.workflowMethod

trait TFlow(val w: TFlowSpec) {

  // State
  protected val state: ZWorkflowState[w.State] = ZWorkflowState.empty[w.State]

  protected def storeActivity: ZActivityStub.Of[w.StoreActivity]

  // Workflow methods
  def workflow(constructor: w.WorkflowEvent): ZSaga[Unit]
  def shouldContinue(state: w.State): <PERSON><PERSON><PERSON>
  def act(command: w.Command, previous: w.State, next: w.State): Z<PERSON>aga[w.CommandResponse]
  def query(query: w.Query): ZAsync[w.QueryResponse]

  // State management
  def init(constructor: w.WorkflowEvent): ZAsync[w.State]
  def next(command: w.Command, state: w.State): Either[w.Reject, w.State]

  // Error handler
  def errorHandler(e: Throwable): w.Reject

  @workflowMethod
  def workflowMethod(constructor: w.WorkflowEvent): Unit = {
    val initialState = init(constructor).run.getOrThrow
    storeActivity.writeState(initialState)
    state.setTo(initialState)

    workflow(constructor).runOrThrow()

    ZWorkflow.awaitUntil(shouldContinue(state.snapshot))
  }

  @UpdateMethod
  def handle(command: w.Command): Either[w.Reject, w.CommandResponse] = {
    val executionResult = for {
      currentState <- state.toEither(errorHandler(new IllegalStateException("not initialized")))
      nextState <- next(command, currentState)
      res <- act(command, currentState, nextState).run().left.map(errorHandler)
    } yield {
      storeActivity.writeState(nextState)
      res
    }

    storeActivity.appendHistory(command, executionResult)

    executionResult
  }

  @UpdateValidatorMethod(updateName = "handle")
  def handleValidator(command: w.Command): Unit = {
    val currentState = state.snapshot
    next(command, currentState).getOrElse(throw new IllegalStateException(s"invalid command $command"))
    ()
  }

}

object TFlow {
  val READ_ALL_LIMIT = -1
}
