package anduin.tflow

import scalapb.{GeneratedMessage, GeneratedSealedOneof}

trait TFlowSpec {
  type Command <: GeneratedSealedOneof
  type CommandResponse <: GeneratedSealedOneof

  type Query <: GeneratedSealedOneof
  type QueryResponse <: GeneratedSealedOneof

  type WorkflowEvent <: Command

  type Reject <: GeneratedSealedOneof

  type State <: GeneratedMessage
  type StoreActivity <: TStoreActivity[Command, Reject, CommandResponse, State]
}
