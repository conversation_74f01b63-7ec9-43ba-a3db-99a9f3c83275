// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.kafka

import com.anduin.stargazer.service.GondorConfig
import zio.kafka.consumer.{Consumer, ConsumerSettings}
import zio.kafka.producer.ProducerSettings
import zio.metrics.MetricLabel
import zio.{ZEnvironment, ZLayer}

object KafkaEnvironment {

  final case class Producer(environment: ZEnvironment[zio.kafka.producer.Producer])

  def producerLayer(gondorConfig: GondorConfig): ZLayer[Any, Throwable, zio.kafka.producer.Producer] = {
    val producerSettings = ProducerSettings(List(gondorConfig.backendConfig.kafkaConfig.bootstrapServer))
    ZLayer.succeed(producerSettings) >>> zio.kafka.producer.Producer.live
  }

  def consumerLayer(
    gondorConfig: GondorConfig,
    groupName: String,
    metricLabels: Set[MetricLabel] = Set.empty
  ): ZLayer[Any, Throwable, zio.kafka.consumer.Consumer] = {
    val consumerSettings =
      ConsumerSettings(List(gondorConfig.backendConfig.kafkaConfig.bootstrapServer))
        .withGroupId(groupName)
        .withMetricsLabels(Set(MetricLabel("group_name", groupName)) ++ metricLabels)
    val dianostic = Consumer.NoDiagnostics
    (ZLayer.succeed(consumerSettings) ++ ZLayer.succeed(dianostic)) >>> zio.kafka.consumer.Consumer.live
  }

}
