// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.kafka

import anduin.model.BinaryModel
import anduin.telemetry.TelemetryEnvironment
import com.anduin.stargazer.service.GondorConfig
import scodec.bits.BitVector
import scodec.{Codec, codecs}
import zio.{Task, ZIO, ZLayer}
import zio.kafka.consumer.{Consumer, ConsumerSettings}

import scala.concurrent.duration.FiniteDuration

final case class KafkaService(
  gondorConfig: GondorConfig,
  kafkaProducerEnvironment: KafkaEnvironment.Producer,
  tracingEnvironment: TelemetryEnvironment.Tracing
) {

  def consumerLayer(group: String): ZLayer[Any, Throwable, Consumer] = {
    val consumerSettings =
      ConsumerSettings(List(gondorConfig.backendConfig.kafkaConfig.bootstrapServer)).withGroupId(group)
    val dianostic = Consumer.NoDiagnostics
    (ZLayer.succeed(consumerSettings) ++ ZLayer.succeed(dianostic)) >>> Consumer.live
  }

}

object KafkaService {

  final case class ThrottleOptions(
    units: Long,
    duration: FiniteDuration
  )

  final case class RetryOptions[K, V](
    when: (Throwable, K, V) => Task[RetryACK],
    retryMax: Int
  )

  object RetryOptions {

    def default[K, V]: RetryOptions[K, V] = RetryOptions[K, V](
      when = (_, _, _) => ZIO.succeed(RetryACK.Retry),
      retryMax = 3
    )

  }

  private[kafka] final case class RetryValue[V](value: V, retryCount: Int)

  object RetryValue {

    given binaryModelCodec: [V] => (binaryModel: BinaryModel[V]) => BinaryModel[RetryValue[V]] = {
      new BinaryModel[RetryValue[V]] {
        override def toByteArray(model: RetryValue[V]): Either[BinaryModel.BinaryModelParserException, Array[Byte]] = {

          Codec
            .encodeBoth(codecs.int32, BinaryModel.scodecCodec[V])(model.retryCount, model.value)
            .map(_.toByteArray)
            .toEither
            .left
            .map { err =>
              BinaryModel.BinaryModelParserException(
                err.messageWithContext,
                new IllegalStateException(err.messageWithContext)
              )
            }

        }

        override def fromByteArray(
          array: Array[Byte]
        ): Either[BinaryModel.BinaryModelParserException, RetryValue[V]] = {
          Codec
            .decodeBoth(codecs.int32, BinaryModel.scodecCodec[V])(BitVector(array))
            .toEither
            .left
            .map { err =>
              BinaryModel.BinaryModelParserException(
                err.messageWithContext,
                new IllegalStateException(err.messageWithContext)
              )
            }
            .map { decodeResult =>
              RetryValue(decodeResult.value._2, decodeResult.value._1)
            }
        }
      }
    }

  }

  sealed trait RetryACK derives CanEqual

  object RetryACK {
    case object Stop extends RetryACK
    case object Retry extends RetryACK
  }

}
