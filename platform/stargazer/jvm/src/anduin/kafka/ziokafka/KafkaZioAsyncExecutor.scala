// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.kafka.ziokafka

import zio.kafka.consumer.Consumer
import zio.kafka.producer.Producer
import zio.{Task, UIO, ZIO}

import anduin.kafka.{KafkaService, Topic}
import anduin.model.BinaryModel
import anduin.telemetry.TelemetryEnvironment.AggregatedTracing

final case class KafkaZioAsyncExecutor[K: BinaryModel, V: BinaryModel](
  topic: Topic[K, V],
  consumerGroupName: String,
  handler: Topic.Message[K, V] => Task[Unit],
  errorHandlerOpt: Option[(Topic.Message[K, V], Throwable) => Task[Unit]] = None,
  retryOpt: Option[KafkaZioRetriableFlow.RetryOptions[K, V]] = None,
  parallelism: Int = 1,
  throttle: Option[KafkaService.ThrottleOptions] = None
) {

  private val producer = KafkaZioSimpleProducer[K, V](
    topic = topic
  )

  private val consumer = KafkaZioSimpleConsumer[K, V](
    topic = topic,
    consumerGroupName = consumerGroupName,
    handler = (msg: Topic.Message[K, V]) => handler(msg),
    errorHandlerOpt = errorHandlerOpt,
    retryOpt = retryOpt,
    parallelism = parallelism,
    throttle = throttle
  )

  def send(topicMessage: Topic.Message[K, V]): ZIO[Producer & AggregatedTracing, Throwable, Unit] = {
    producer.send(topicMessage)
  }

  def start(): ZIO[Consumer & AggregatedTracing, Throwable, Unit] = {
    for {
      _ <- ZIO.logInfo(s"Starting async executor for ${topic.name}")
      _ <- consumer.start()
    } yield ()
  }

  def close(): UIO[Unit] = {
    ZIO.logInfo(s"Closing async executor for ${topic.name}")
  }

}
