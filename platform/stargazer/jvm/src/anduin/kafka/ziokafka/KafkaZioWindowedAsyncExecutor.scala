// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.kafka.ziokafka

import java.util.{Properties, UUID}

import org.apache.kafka.common.serialization.Serde
import org.apache.kafka.streams.kstream.{Suppressed, Window, Windows}
import org.apache.kafka.streams.scala.ImplicitConversions.*
import org.apache.kafka.streams.scala.kstream.Materialized
import org.apache.kafka.streams.scala.serialization.Serdes.*
import org.apache.kafka.streams.scala.{ByteArrayWindowStore, StreamsBuilder}
import org.apache.kafka.streams.{KafkaStreams, StreamsConfig}
import zio.kafka.consumer.Consumer
import zio.kafka.producer.Producer
import zio.{Task, ZIO}

import anduin.buildinfo.stargazerJvmBuildInfo
import anduin.kafka.{KafkaService, Topic}
import anduin.model.BinaryModel
import anduin.telemetry.TelemetryEnvironment.AggregatedTracing

final case class KafkaZioWindowedAsyncExecutor[W <: Window, V >: Null: BinaryModel](
  kafkaService: KafkaService,
  inputTopic: Topic[String, V],
  windowedTopic: Topic[String, List[V]],
  consumerGroupName: String,
  handlerFlow: List[V] => Task[Unit],
  errorHandlerOpt: Option[(Topic.Message[String, List[V]], Throwable) => Task[Unit]] = None,
  retryOpt: Option[KafkaZioRetriableFlow.RetryOptions[String, List[V]]] = None,
  window: Windows[W],
  filter: V => Boolean,
  parallelism: Int = 1,
  throttle: Option[KafkaService.ThrottleOptions] = None
) {

  private val producer = KafkaZioSimpleProducer[String, V](
    topic = inputTopic
  )

  private val consumer = KafkaZioSimpleConsumer[String, List[V]](
    topic = windowedTopic,
    consumerGroupName = consumerGroupName,
    handler = (msg: Topic.Message[String, List[V]]) => handlerFlow(msg.value),
    errorHandlerOpt = errorHandlerOpt,
    retryOpt = retryOpt,
    parallelism = parallelism,
    throttle = throttle
  )

  private val kafkaStream = KafkaZioWindowedAsyncExecutor.WindowedStream(
    kafkaService = kafkaService,
    inputTopic = inputTopic,
    windowedTopic = windowedTopic,
    window = window,
    filter = filter
  )

  def send(topicMessage: Topic.Message[String, V]): ZIO[Producer & AggregatedTracing, Throwable, Unit] = {
    producer.send(topicMessage)
  }

  def start(): ZIO[Consumer & AggregatedTracing, Throwable, Unit] = {
    for {
      _ <- ZIO.logInfo(s"Starting windowed async executor ${inputTopic.name}")
      _ <- ZIO.attempt(kafkaStream.build())
      _ <- consumer.start()
    } yield ()
  }

  def close(): Task[Unit] = {
    ZIO.logInfo(s"Closing windowed async executor ${inputTopic.name}") *>
      ZIO.attempt(kafkaStream.close())
  }

}

object KafkaZioWindowedAsyncExecutor {

  private final case class WindowedStream[K >: Null: BinaryModel, V >: Null: BinaryModel, W <: Window](
    kafkaService: KafkaService,
    inputTopic: Topic[String, V],
    windowedTopic: Topic[K, List[V]],
    window: Windows[W],
    filter: V => Boolean
  ) {
    private given stringSerde: Serde[String] = customSerde[String]
    private given kSerde: Serde[K] = customSerde[K]
    private given vSerde: Serde[V] = customSerde[V]
    private given vrSerde: Serde[List[V]] = customSerde[List[V]]

    // To fix multiple instances of Kafka streams running in same state directory when running parallel integ tests
    private val storeTopicName = if (stargazerJvmBuildInfo.buildEnv == "PullRequest") {
      windowedTopic.name + "-" + UUID.randomUUID()
    } else {
      windowedTopic.name
    }

    given materialStore: Materialized[K, List[V], ByteArrayWindowStore] = Materialized
      .as[K, List[V], ByteArrayWindowStore](s"kafka-stream-state-store-$storeTopicName")

    private val config: Properties = {
      val props = new Properties()
      props.put(StreamsConfig.APPLICATION_ID_CONFIG, s"kafka-stream-$storeTopicName")
      props.put(
        StreamsConfig.BOOTSTRAP_SERVERS_CONFIG,
        kafkaService.gondorConfig.backendConfig.kafkaConfig.bootstrapServer
      )
      props
    }

    private val streamsBuilder: StreamsBuilder = {
      val builder = new StreamsBuilder()
      builder
        .stream[K, V](inputTopic.name)
        .groupBy((_, _) => "")
        .windowedBy(window)
        .aggregate(List.empty[V])((_, newValue, aggValue) => aggValue :+ newValue)
        .suppress(Suppressed.untilWindowCloses(Suppressed.BufferConfig.unbounded()))
        .mapValues((_, events) => events.filter(filter))
        .filter((_, events) => events.nonEmpty)
        .toStream((windowedKey, _) => windowedKey.key())
        .to(windowedTopic.name)
      builder
    }

    private val streams: KafkaStreams = new KafkaStreams(streamsBuilder.build(), config)

    def build(): Unit = {
      streams.start()
    }

    def close(): Unit = {
      streams.close()
    }

    private def serializer[T](
      using valueBinaryModel: BinaryModel[T]
    ): T => Array[Byte] = { v =>
      valueBinaryModel
        .toByteArray(v)
        .fold(
          err => {
            scribe.error(err.message)
            Array.empty[Byte]
          },
          identity
        )
    }

    private def deserializer[T](
      using valueBinaryModel: BinaryModel[T]
    ): Array[Byte] => Option[T] = { bytes =>
      valueBinaryModel
        .fromByteArray(bytes)
        .fold(
          err => {
            scribe.error(err.message)
            None
          },
          Some(_)
        )
    }

    private def customSerde[T >: Null](
      using valueBinaryModel: BinaryModel[T]
    ): Serde[T] =
      fromFn[T](serializer[T], deserializer[T])

  }

}
