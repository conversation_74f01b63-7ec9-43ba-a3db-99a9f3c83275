// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.kafka.ziokafka

import zio.kafka.consumer.Consumer
import zio.{Task, UIO, ZIO}

import anduin.kafka.{KafkaService, Topic}
import anduin.model.BinaryModel
import anduin.telemetry.TelemetryEnvironment.AggregatedTracing

final case class KafkaZioSimpleConsumer[K: BinaryModel, V: BinaryModel](
  topic: Topic[K, V],
  consumerGroupName: String,
  handler: Topic.Message[K, V] => Task[Unit],
  errorHandlerOpt: Option[(Topic.Message[K, V], Throwable) => Task[Unit]] = None,
  retryOpt: Option[KafkaZioRetriableFlow.RetryOptions[K, V]] = None,
  parallelism: Int = 1,
  throttle: Option[KafkaService.ThrottleOptions] = None
) {

  private val source = KafkaZioConsumerStream(
    topic = topic
  ).stream

  private val stream = KafkaZioRetriableFlow(
    source = source,
    handler = (msg: Topic.Message[K, V]) => handler(msg),
    errorHandlerOpt = errorHandlerOpt,
    retryOpt = retryOpt,
    parallelism = parallelism,
    throttle = throttle,
    tracingOptions = KafkaZioRetriableFlow.TracingOptions(
      handlerName = consumerGroupName
    )
  ).stream

  def start(): ZIO[Consumer & AggregatedTracing, Throwable, Unit] = {
    for {
      _ <- ZIO.logInfo(s"Starting consumer for ${topic.name} of group $consumerGroupName")
      _ <- stream.runDrain
    } yield ()
  }

  def close(): UIO[Unit] = {
    ZIO.logInfo(s"Closing consumer for ${topic.name} of group $consumerGroupName")
  }

}
