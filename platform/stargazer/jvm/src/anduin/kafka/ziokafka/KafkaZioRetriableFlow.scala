// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.kafka.ziokafka

import java.util.concurrent.TimeUnit

import zio.stream.ZStream
import zio.{Schedule, Task, ZIO}

import anduin.execution.ZIOExecutor
import anduin.kafka.KafkaService.RetryACK
import anduin.kafka.{KafkaService, Topic}
import anduin.telemetry.TelemetryEnvironment.AggregatedTracing
import com.anduin.stargazer.service.utils.*

final case class KafkaZioRetriableFlow[K, V, R, E](
  source: ZStream[E, Throwable, KafkaZioConsumerStream.Record[K, V]],
  handler: Topic.Message[K, V] => Task[R],
  errorHandlerOpt: Option[(Topic.Message[K, V], Throwable) => Task[Unit]] = None,
  retryOpt: Option[KafkaZioRetriableFlow.RetryOptions[K, V]] = None,
  parallelism: Int = 1,
  throttle: Option[KafkaService.ThrottleOptions] = None,
  tracingOptions: KafkaZioRetriableFlow.TracingOptions
) {

  private val throttledSource = throttle.fold(source) { throttle =>
    source.throttleShape(throttle.units, zio.Duration.fromScala(throttle.duration))(_.size.toLong)
  }

  private def handlerFunc(record: KafkaZioConsumerStream.Record[K, V]): ZIO[Any, Throwable, R] = {
    val noRetryEffect =
      handler(record.msg).tapError(error => ZIOUtils.traverseOptionUnit(errorHandlerOpt)(_(record.msg, error)))
    retryOpt.fold(noRetryEffect) { retryOption =>
      if (retryOption.retryMax <= 1) {
        noRetryEffect
      } else {
        val recurSchedule: Schedule[Any, Any, Long] =
          Schedule
            .recurs(retryOption.retryMax - 1)
            .tapOutput { retryTime =>
              ZIO.logWarning(s"Failed to run kafka handler (${retryTime + 1} / ${retryOption.retryMax})")
            }
        val whenSchedule: Schedule[Any, Throwable, Throwable] = Schedule.recurWhileZIO[Any, Throwable] { error =>
          retryOption
            .when(error, record.msg)
            .flatMap {
              case RetryACK.Stop =>
                ZIO.logWarningCause("Will not retry for error ", error.toCause).as(false)
              case RetryACK.Retry =>
                ZIO.logWarningCause("Will retry for error ", error.toCause).as(true)
            }
            .catchAllCause { errorHandlerError =>
              ZIO.logWarningCause("Got error when run kafka handler ", error.toCause) *>
                ZIO.logWarningCause("Kafka retrying handler error: ", errorHandlerError).as(false)
            }
        }
        val schedule: Schedule[Any, Throwable, (Long, Throwable)] = recurSchedule && whenSchedule
        handler(record.msg)
          .retry(schedule)
          .tapError(error => ZIOUtils.traverseOptionUnit(errorHandlerOpt)(_(record.msg, error)))
      }
    }
  }

  private def tracingHandlerFunc(record: KafkaZioConsumerStream.Record[K, V]): ZIO[AggregatedTracing, Throwable, R] = {
    val headers = record.msg.headers
    val annotations = Map("operation" -> tracingOptions.handlerName) ++ headers

    ZIOTelemetryUtils.traceWithRootSpan("app/kafka-handler", annotations)(
      ZIOTelemetryUtils.injectMetrics("kafka", Map("op" -> "app/kafka-handler", "name" -> tracingOptions.handlerName))(
        ZIOLoggingUtils.annotate(
          LoggingKey.Operation -> tracingOptions.handlerName,
          LoggingKey.Type -> "kafka-handler"
        )(
          handlerFunc(record)
            .tapError(error => ZIO.logErrorCause(s"Kafka handler error", error.toCause))
            .tapDefect(cause => ZIO.logErrorCause("Kafka handler defect", cause))
        )
      )
    )
  }

  val stream: ZStream[E & AggregatedTracing, Throwable, R] =
    ZStreamUtils.retryStream(zio.Duration(10, TimeUnit.SECONDS))(
      ZStreamUtils
        .safeMapZIOPar(
          throttledSource.tap { record =>
            record.offset.commit
          },
          parallelism,
          (record: KafkaZioConsumerStream.Record[K, V]) =>
            tracingHandlerFunc(record).onExecutor(KafkaZioRetriableFlow.scheduler)
        )
    )

}

object KafkaZioRetriableFlow {

  private[kafka] final case class RetryOptions[K, V](
    when: (Throwable, Topic.Message[K, V]) => Task[RetryACK],
    retryMax: Int
  )

  private[kafka] final case class TracingOptions(
    handlerName: String
  )

  private[kafka] val scheduler = ZIOExecutor.io("kafka-handler")

}
