// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.kafka.ziokafka

import io.opentelemetry.api.trace.SpanKind
import org.apache.kafka.clients.producer.{ProducerRecord, RecordMetadata}
import zio.ZIO
import zio.kafka.producer.Producer
import zio.stream.ZPipeline
import zio.telemetry.opentelemetry.baggage.Baggage

import anduin.kafka.Topic
import anduin.model.BinaryModel
import anduin.telemetry.TelemetryEnvironment.AggregatedTracing
import com.anduin.stargazer.service.utils.{ZIOTelemetryUtils, ZStreamUtils}

final case class KafkaProducerPipeline[R, K, V](
  topic: Topic[K, V]
)(
  using keyBinaryModel: BinaryModel[K],
  valueBinaryModel: BinaryModel[V],
  headerValueBinaryModel: BinaryModel[String]
) {

  private val serializer = zio.kafka.serde.Serde.byteArray

  private val encodePipeline: ZPipeline[
    R,
    BinaryModel.BinaryModelParserException,
    Topic.Message[K, V],
    KafkaProducerPipeline.BinaryProducerRecord
  ] =
    ZStreamUtils.safePipeline { msg =>
      for {
        key <- ZIO.fromEither(keyBinaryModel.toByteArray(msg.key))
        value <- ZIO.fromEither(valueBinaryModel.toByteArray(msg.value))
        headers <- ZIO.foreach(msg.headers) { case (headerKey, headerValue) =>
          for {
            value <- ZIO.fromEither(headerValueBinaryModel.toByteArray(headerValue))
          } yield (headerKey, value)
        }
        record = new KafkaProducerPipeline.BinaryProducerRecord(
          topic.name,
          key,
          value
        )
        _ = {
          headers.foreach(h => record.headers.add(h._1, h._2))
        }
      } yield record
    }

  private val tracingPipeline: ZPipeline[
    R & AggregatedTracing,
    Throwable,
    Topic.Message[K, V],
    Topic.Message[K, V]
  ] = {
    ZStreamUtils.safePipeline { record =>
      for {
        baggage <- ZIO.service[Baggage]
        attributes <- baggage.getAll
        injected = attributes.foldLeft(record) { (msg, header) =>
          msg.addHeader(header._1, header._2)
        }
      } yield injected
    }
  }

  private val produceRecordPipeline: ZPipeline[
    R & Producer & AggregatedTracing,
    Throwable,
    KafkaProducerPipeline.BinaryProducerRecord,
    RecordMetadata
  ] =
    ZStreamUtils.safePipeline { record =>
      ZIOTelemetryUtils.traceWithChildSpan(
        "service/kafka-producer",
        SpanKind.CLIENT,
        Map("topic" -> record.topic())
      )(
        ZIO.serviceWithZIO[Producer](_.produce(record, serializer, serializer))
      )
    }

  val pipeline: ZPipeline[
    R & Producer & AggregatedTracing,
    Throwable,
    Topic.Message[K, V],
    RecordMetadata
  ] =
    tracingPipeline.andThen(encodePipeline).andThen(produceRecordPipeline)

}

object KafkaProducerPipeline {
  private type BinaryProducerRecord = ProducerRecord[Array[Byte], Array[Byte]]
}
