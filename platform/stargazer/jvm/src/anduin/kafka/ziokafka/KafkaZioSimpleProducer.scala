// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.kafka.ziokafka

import zio.ZIO
import zio.kafka.producer.Producer
import zio.stream.ZStream

import anduin.kafka.Topic
import anduin.model.BinaryModel
import anduin.telemetry.TelemetryEnvironment.AggregatedTracing

final case class KafkaZioSimpleProducer[K: BinaryModel, V: BinaryModel](
  topic: Topic[K, V]
) {

  private val kafkaProducerPipeline = KafkaProducerPipeline(topic).pipeline

  def send(topicMessage: Topic.Message[K, V]): ZIO[Producer & AggregatedTracing, Throwable, Unit] = {
    val stream = ZStream.succeed(topicMessage).via(kafkaProducerPipeline)
    stream.runDrain
  }

}
