// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.kafka.ziokafka

import anduin.kafka.Topic
import anduin.model.BinaryModel
import zio.kafka.consumer.{Consumer, Offset, Subscription}
import zio.kafka.serde.Serde
import zio.stream.ZStream

import scala.jdk.CollectionConverters.*

/** This stream does not auto commit offset
  */
final case class KafkaZioConsumerStream[K, V](
  topic: Topic[K, V]
)(
  using keyBinaryModel: BinaryModel[K],
  valueBinaryModel: BinaryModel[V],
  headerBinaryModel: BinaryModel[String]
) {

  val stream: ZStream[Consumer, Throwable, KafkaZioConsumerStream.Record[K, V]] = {
    ZStream
      .serviceWithStream[Consumer](
        _.plainStream(
          Subscription.topics(topic.name),
          Serde.byteArray.asTry,
          Serde.byteArray.asTry,
          bufferSize = 4
        )
      )
      .map { record =>
        val decodedRecord = for {
          keyBytes <- record.key.toEither
          valueBytes <- record.value.toEither
          key <- keyBinaryModel.fromByteArray(keyBytes)
          value <- valueBinaryModel.fromByteArray(valueBytes)
          offset = record.offset
          headers = record.record.headers.asScala
            .flatMap(h =>
              headerBinaryModel
                .fromByteArray(h.value)
                .fold(
                  error => {
                    scribe.error(s"Cannot decode topic ${topic.name} message header", error)
                    None
                  },
                  value => Some((h.key, value))
                )
            )
            .toMap
          msg = topic.message(
            key,
            value,
            headers
          )
        } yield KafkaZioConsumerStream.Record(msg, offset)
        decodedRecord.left.toOption.foreach { error =>
          scribe.error(s"Cannot decode topic ${topic.name} record", error)
        }
        decodedRecord
      }
      .collectRight
  }

}

object KafkaZioConsumerStream {

  final case class Record[K, V](
    msg: Topic.Message[K, V],
    offset: Offset
  )

}
