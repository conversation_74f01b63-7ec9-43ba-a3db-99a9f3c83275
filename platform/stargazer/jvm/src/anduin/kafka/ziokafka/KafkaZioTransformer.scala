// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.kafka.ziokafka

import zio.kafka.consumer.Consumer
import zio.kafka.producer.Producer
import zio.{Task, UIO, ZIO}

import anduin.kafka.{KafkaService, Topic}
import anduin.model.BinaryModel
import anduin.telemetry.TelemetryEnvironment.AggregatedTracing

final case class KafkaZioTransformer[K1: BinaryModel, V1: BinaryModel, K2: BinaryModel, V2: BinaryModel](
  kafkaService: KafkaService,
  inputTopic: Topic[K1, V1],
  outputTopic: Topic[K2, V2],
  transform: Topic.Message[K1, V1] => Task[Topic.Message[K2, V2]],
  errorHandlerOpt: Option[(Topic.Message[K1, V1], Throwable) => Task[Unit]] = None,
  retryOpt: Option[KafkaZioRetriableFlow.RetryOptions[K1, V1]] = None,
  parallelism: Int = 1,
  throttle: Option[KafkaService.ThrottleOptions] = None
) {

  private val source = KafkaZioConsumerStream(
    topic = inputTopic
  ).stream

  private val kafkaProducerPipeline = KafkaProducerPipeline(outputTopic).pipeline

  private val stream = KafkaZioRetriableFlow(
    source = source,
    handler = (msg: Topic.Message[K1, V1]) => transform(msg),
    errorHandlerOpt = errorHandlerOpt,
    retryOpt = retryOpt,
    parallelism = parallelism,
    throttle = throttle,
    tracingOptions = KafkaZioRetriableFlow.TracingOptions(
      handlerName = inputTopic.name
    )
  ).stream.via(kafkaProducerPipeline)

  def start(): ZIO[Producer & Consumer & AggregatedTracing, Throwable, Unit] = {
    stream.runDrain
  }

  def close(): UIO[Unit] = {
    ZIO.logInfo(s"Closing transformer for ${inputTopic.name}")
  }

}
