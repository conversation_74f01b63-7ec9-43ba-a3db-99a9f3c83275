// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.kafka

import zio.{Task, ZIO}

import anduin.kafka.ziokafka.KafkaZioSimpleProducer
import anduin.model.BinaryModel

final case class KafkaSimpleProducer[K: BinaryModel, V: BinaryModel](
  kafkaService: KafkaService,
  topic: Topic[K, V],
  onSendMessage: Topic.Message[K, V] => Task[Unit] = (_: Topic.Message[K, V]) => ZIO.unit
) {

  private val underlying = KafkaZioSimpleProducer(topic)

  private val environment =
    kafkaService.kafkaProducerEnvironment.environment ++ kafkaService.tracingEnvironment.environment

  def send(topicMessage: Topic.Message[K, V]): Task[Unit] = {
    underlying
      .send(topicMessage)
      .tap { _ => onSendMessage(topicMessage) }
      .provideEnvironment(environment)
  }

}
