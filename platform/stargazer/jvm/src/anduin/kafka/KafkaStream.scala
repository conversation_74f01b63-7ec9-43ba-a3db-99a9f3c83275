// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.kafka

import java.time.Duration
import java.util.Properties
import scala.concurrent.duration.FiniteDuration

import org.apache.kafka.common.serialization.Serde
import org.apache.kafka.streams.scala.kstream.Materialized
import org.apache.kafka.streams.scala.{ByteArrayWindowStore, StreamsBuilder}
import org.apache.kafka.streams.{KafkaStreams, StreamsConfig}

import anduin.model.BinaryModel
import com.anduin.stargazer.apps.stargazer.StargazerSettings

import org.apache.kafka.streams.scala.serialization.Serdes.*

abstract class KafkaStream[K >: Null: BinaryModel, V >: Null: BinaryModel, VR >: Null: BinaryModel](
  outputTopic: Topic[K, VR]
) {
  given kSerde: Serde[K] = customSerde[K]
  given vSerde: Serde[V] = customSerde[V]
  given vrSerde: Serde[VR] = customSerde[VR]

  given materialStore: Materialized[K, VR, ByteArrayWindowStore] = Materialized
    .as[K, VR, ByteArrayWindowStore](s"kafka-stream-state-store-${outputTopic.name}")

  given Conversion[FiniteDuration, Duration] = finiteDuration => Duration.ofMillis(finiteDuration.toMillis)

  private val config: Properties = {
    val props = new Properties()
    props.put(StreamsConfig.APPLICATION_ID_CONFIG, s"kafka-stream-${outputTopic.name}")
    props.put(
      StreamsConfig.BOOTSTRAP_SERVERS_CONFIG,
      StargazerSettings.gondorConfig.backendConfig.kafkaConfig.bootstrapServer
    )
    props
  }

  def streamsBuilder(): StreamsBuilder

  private val streams: KafkaStreams = new KafkaStreams(streamsBuilder().build(), config)

  def build(): Unit = {
    streams.start()
  }

  def close(): Unit = {
    streams.close()
  }

  private def serializer[T](
    using valueBinaryModel: BinaryModel[T]
  ): T => Array[Byte] = { v =>
    valueBinaryModel
      .toByteArray(v)
      .fold(
        err => {
          scribe.error(err.message)
          Array.empty[Byte]
        },
        identity
      )
  }

  private def deserializer[T](
    using valueBinaryModel: BinaryModel[T]
  ): Array[Byte] => Option[T] = { bytes =>
    valueBinaryModel
      .fromByteArray(bytes)
      .fold(
        err => {
          scribe.error(err.message)
          None
        },
        Some(_)
      )
  }

  private def customSerde[T >: Null](
    using valueBinaryModel: BinaryModel[T]
  ): Serde[T] =
    fromFn[T](serializer[T], deserializer[T])

}
