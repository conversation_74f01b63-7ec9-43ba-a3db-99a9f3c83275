// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.kafka

import anduin.kafka.ziokafka.{KafkaZioRetriableFlow, KafkaZioTransformer}
import anduin.model.BinaryModel
import zio.metrics.MetricLabel
import zio.{Scope, Task, UIO}

final case class KafkaTransformer[K1: BinaryModel, V1: BinaryModel, K2: BinaryModel, V2: BinaryModel](
  kafkaService: KafkaService,
  inputTopic: Topic[K1, V1],
  outputTopic: Topic[K2, V2],
  transform: (K1, V1) => Task[(K2, V2)],
  errorHandlerOpt: Option[(K1, V1, Throwable) => Task[Unit]] = None,
  retryOpt: Option[KafkaService.RetryOptions[K1, V1]] = None,
  parallelism: Int = 1,
  throttle: Option[KafkaService.ThrottleOptions] = None,
  overrideConsumerGroupName: Option[String] = None
) {

  private val consumerGroupName = overrideConsumerGroupName.getOrElse(s"gondor-transformer-${inputTopic.name}")

  private def errorHandler(f: (K1, V1, Throwable) => Task[Unit]) = (msg: Topic.Message[K1, V1], e: Throwable) => {
    f(
      msg.key,
      msg.value,
      e
    )
  }

  private val underlying = KafkaZioTransformer(
    kafkaService = kafkaService,
    inputTopic = inputTopic,
    outputTopic = outputTopic,
    transform = (msg: Topic.Message[K1, V1]) => {
      for {
        (k, v) <- transform(msg.key, msg.value)
      } yield outputTopic.message(k, v)
    },
    errorHandlerOpt = errorHandlerOpt.map { f => errorHandler(f) },
    retryOpt = retryOpt.map { retryOptions =>
      KafkaZioRetriableFlow.RetryOptions(
        when = (e: Throwable, msg: Topic.Message[K1, V1]) =>
          retryOptions.when(
            e,
            msg.key,
            msg.value
          ),
        retryMax = retryOptions.retryMax
      )
    },
    parallelism = parallelism,
    throttle = throttle
  )

  def start(): Task[Unit] = {
    for {
      consumerEnv <- KafkaEnvironment
        .consumerLayer(
          kafkaService.gondorConfig,
          consumerGroupName,
          Set(MetricLabel("topic", inputTopic.name))
        )
        .build(Scope.global)
      _ <- underlying
        .start()
        .provideEnvironment(
          kafkaService.kafkaProducerEnvironment.environment ++ consumerEnv ++ kafkaService.tracingEnvironment.environment
        )
    } yield ()
  }

  def close(): UIO[Unit] = {
    underlying.close()
  }

}
