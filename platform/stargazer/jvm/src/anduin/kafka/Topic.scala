// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.kafka

import anduin.kafka.Topic.Message
import anduin.model.BinaryModel
import com.anduin.stargazer.utils.DeploymentUtils

final class Topic[K: BinaryModel, V: BinaryModel](val name: String) {

  def message(key: K, value: V, headers: Map[String, String] = Map()): Topic.Message[K, V] = {
    new Message[K, V](
      key,
      value,
      headers
    )
  }

}

object Topic {

  def apply[K: BinaryModel, V: BinaryModel](
    name: String,
    tenantSuffix: String = DeploymentUtils.tenantSuffix
  ): Topic[K, V] = new Topic(name + tenantSuffix)

  final class Message[K: BinaryModel, V: BinaryModel](
    val key: K,
    val value: V,
    val headers: Map[String, String] = Map()
  ) {

    def addHeader(key: String, value: String): Message[K, V] = {
      new Message(
        this.key,
        this.value,
        headers + (key -> value)
      )
    }

  }

}
