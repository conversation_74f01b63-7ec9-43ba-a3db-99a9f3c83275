// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.kafka

import anduin.kafka.ziokafka.{KafkaZioRetriableFlow, KafkaZioSimpleConsumer}
import anduin.model.BinaryModel
import zio.metrics.MetricLabel
import zio.{Scope, Task, UIO}

final case class KafkaSimpleConsumer[K: BinaryModel, V: BinaryModel](
  kafkaService: KafkaService,
  topic: Topic[K, V],
  consumerGroupName: String,
  handler: (K, V) => Task[Unit],
  errorHandlerOpt: Option[(K, V, Throwable) => Task[Unit]] = None,
  retryOpt: Option[KafkaService.RetryOptions[K, V]] = None,
  parallelism: Int = 1,
  throttle: Option[KafkaService.ThrottleOptions] = None
) {

  private def errorHandler(f: (K, V, Throwable) => Task[Unit]) = (msg: Topic.Message[K, V], e: Throwable) => {
    f(
      msg.key,
      msg.value,
      e
    )
  }

  private val underlying = KafkaZioSimpleConsumer(
    topic = topic,
    consumerGroupName = consumerGroupName,
    handler = (msg: Topic.Message[K, V]) =>
      handler(
        msg.key,
        msg.value
      ),
    errorHandlerOpt = errorHandlerOpt.map { f => errorHandler(f) },
    retryOpt = retryOpt.map { retryOptions =>
      KafkaZioRetriableFlow.RetryOptions(
        when = (e: Throwable, msg: Topic.Message[K, V]) =>
          retryOptions.when(
            e,
            msg.key,
            msg.value
          ),
        retryMax = retryOptions.retryMax
      )
    },
    parallelism = parallelism,
    throttle = throttle
  )

  def start(): Task[Unit] = {
    for {
      consumerEnv <- KafkaEnvironment
        .consumerLayer(
          kafkaService.gondorConfig,
          consumerGroupName,
          Set(MetricLabel("topic", topic.name))
        )
        .build(Scope.global)
      _ <- underlying
        .start()
        .provideEnvironment(consumerEnv ++ kafkaService.tracingEnvironment.environment)
    } yield ()
  }

  def close(): UIO[Unit] = {
    underlying.close()
  }

}
