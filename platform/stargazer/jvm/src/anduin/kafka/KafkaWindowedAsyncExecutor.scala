// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.kafka

import org.apache.kafka.streams.kstream.{Window, Windows}
import zio.metrics.MetricLabel
import zio.{Scope, Task}

import anduin.kafka.KafkaService.RetryACK
import anduin.kafka.ziokafka.{KafkaZioRetriableFlow, KafkaZioWindowedAsyncExecutor}
import anduin.model.BinaryModel

final case class KafkaWindowedAsyncExecutor[W <: Window, V >: Null: BinaryModel](
  kafkaService: KafkaService,
  inputTopic: Topic[String, V],
  windowedTopic: Topic[String, List[V]],
  handlerFlow: List[V] => Task[Unit],
  errorHandlerOpt: Option[(String, List[V], Throwable) => Task[Unit]] = None,
  retryOpt: Option[KafkaService.RetryOptions[String, List[V]]] = None,
  window: Windows[W],
  filter: V => Boolean,
  parallelism: Int = 1,
  overrideConsumerGroupName: Option[String] = None,
  throttle: Option[KafkaService.ThrottleOptions] = None
) {

  private val consumerGroupName = overrideConsumerGroupName.getOrElse(s"gondor-window-executor-${inputTopic.name}")

  private def errorHandler(f: (String, List[V], Throwable) => Task[Unit]) =
    (msg: Topic.Message[String, List[V]], e: Throwable) => {
      f(
        msg.key,
        msg.value,
        e
      )
    }

  private def retryWhen(f: (Throwable, String, List[V]) => Task[RetryACK]) =
    (e: Throwable, msg: Topic.Message[String, List[V]]) => {
      f(
        e,
        msg.key,
        msg.value
      )
    }

  private val underlying = KafkaZioWindowedAsyncExecutor(
    kafkaService = kafkaService,
    inputTopic = inputTopic,
    windowedTopic = windowedTopic,
    consumerGroupName = consumerGroupName,
    handlerFlow = (v: List[V]) => handlerFlow(v),
    errorHandlerOpt = errorHandlerOpt.map { f => errorHandler(f) },
    retryOpt = retryOpt.map { retryOptions =>
      KafkaZioRetriableFlow.RetryOptions(
        when = retryWhen(retryOptions.when),
        retryMax = retryOptions.retryMax
      )
    },
    window = window,
    filter = filter,
    parallelism = parallelism,
    throttle = throttle
  )

  private val producerEnvironment =
    kafkaService.kafkaProducerEnvironment.environment ++ kafkaService.tracingEnvironment.environment

  def send(topicMessage: Topic.Message[String, V]): Task[Unit] = {
    underlying.send(topicMessage).provideEnvironment(producerEnvironment)
  }

  def start(): Task[Unit] = {
    for {
      consumerEnv <- KafkaEnvironment
        .consumerLayer(
          kafkaService.gondorConfig,
          consumerGroupName,
          Set(MetricLabel("topic", inputTopic.name))
        )
        .build(Scope.global)
      _ <- underlying
        .start()
        .provideEnvironment(consumerEnv ++ kafkaService.tracingEnvironment.environment)
    } yield ()
  }

  def close(): Task[Unit] = {
    underlying.close()
  }

}
