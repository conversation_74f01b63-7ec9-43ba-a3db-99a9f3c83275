// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.model.optics.iso

import monocle.Iso
import scalapb.TypeMapper

import anduin.model.common.user.{UserAttributes, UserInfo}
import anduin.optics.iso.utils.IsoUtils.fromTypeMapper
import anduin.service.{
  AuthenticatedRequestContext,
  AuthenticatedRequestContextMessage,
  ServiceActor,
  ServiceActorMessage,
  UserAttributesMessage,
  UserInfoMessage
}

trait RequestContextIso {

  private given userInfoTypeMapper: TypeMapper[UserInfoMessage, UserInfo] = TypeMapper {
    (userInfoMsg: UserInfoMessage) =>
      UserInfo(
        userInfoMsg.emailAddressStr,
        userInfoMsg.firstName,
        userInfoMsg.lastName,
        userInfoMsg.userName,
        userInfoMsg.jobTitle,
        userInfoMsg.phone,
        userInfoMsg.country,
        userInfoMsg.state,
        userInfoMsg.city,
        userInfoMsg.address,
        userInfoMsg.enabled,
        userInfoMsg.createdTimestamp
      )

  } { (userInfo: UserInfo) =>
    UserInfoMessage(
      userInfo.emailAddressStr,
      userInfo.firstName,
      userInfo.lastName,
      userInfo.userName,
      userInfo.jobTitle,
      userInfo.phone,
      userInfo.country,
      userInfo.state,
      userInfo.city,
      userInfo.address,
      userInfo.enabled,
      userInfo.createdTimestamp
    )
  }

  given userInfoIso: Iso[UserInfo, UserInfoMessage] = fromTypeMapper(userInfoTypeMapper)

  private given userAttributesMapper: TypeMapper[UserAttributesMessage, UserAttributes] =
    TypeMapper { (userAttributesMsg: UserAttributesMessage) =>
      UserAttributes(userAttributesMsg.isZombie)
    } { (userAttributes: UserAttributes) =>
      UserAttributesMessage(userAttributes.isZombie)
    }

  given userAttributesIso: Iso[UserAttributes, UserAttributesMessage] = fromTypeMapper(userAttributesMapper)

  private given serviceActorMapper: TypeMapper[ServiceActorMessage, ServiceActor] = TypeMapper {
    (serviceActorMsg: ServiceActorMessage) =>
      ServiceActor(
        serviceActorMsg.userId,
        userInfoIso.reverseGet(serviceActorMsg.userInfoMsg),
        userAttributesIso.reverseGet(serviceActorMsg.userAttributesMsg),
        serviceActorMsg.userSessionIdOpt,
        serviceActorMsg.environmentIdOpt
      )
  } { (serviceActor: ServiceActor) =>
    ServiceActorMessage(
      serviceActor.userId,
      userInfoIso.get(serviceActor.userInfo),
      userAttributesIso.get(serviceActor.userAttributes),
      serviceActor.userSessionIdOpt,
      serviceActor.environmentIdOpt
    )
  }

  given serviceActorIso: Iso[ServiceActor, ServiceActorMessage] = fromTypeMapper(serviceActorMapper)

  private given authenticatedRequestContextTypeMapper
    : TypeMapper[AuthenticatedRequestContextMessage, AuthenticatedRequestContext] = TypeMapper {
    (requestContextMsg: AuthenticatedRequestContextMessage) =>
      AuthenticatedRequestContext(
        serviceActorIso.reverseGet(requestContextMsg.serviceActorMsg),
        requestContextMsg.headers.toSeq,
        requestContextMsg.cookies.toSeq
      )
  } { (requestContext: AuthenticatedRequestContext) =>
    AuthenticatedRequestContextMessage(
      serviceActorIso.get(requestContext.actor),
      requestContext.headers.toMap,
      requestContext.cookies.toMap
    )
  }

  given authenticatedRequestContextIso: Iso[AuthenticatedRequestContext, AuthenticatedRequestContextMessage] =
    fromTypeMapper(authenticatedRequestContextTypeMapper)

  given Conversion[AuthenticatedRequestContext, AuthenticatedRequestContextMessage] = authenticatedRequestContextIso.get

  given Conversion[AuthenticatedRequestContextMessage, AuthenticatedRequestContext] =
    authenticatedRequestContextIso.reverseGet

}

object RequestContextIso extends RequestContextIso
