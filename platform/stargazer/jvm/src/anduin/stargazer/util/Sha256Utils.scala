// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.stargazer.util

import java.math.BigInteger
import java.security.MessageDigest

import zio.Task
import zio.stream.ZStream

import anduin.utils.stream.ZStreamIOUtils.ByteStream

object Sha256Utils {

  def getHash(input: Array[Byte]): String = {
    val digest = MessageDigest.getInstance("SHA-256").digest(input)
    val str = new BigInteger(1, digest).toString(16)
    if (str.length != 64) "0" + str else str
  }

  def getHash(stream: ByteStream): Task[String] = {
    stream
      .rechunk(ZStream.DefaultChunkSize)
      .runFold(
        MessageDigest.getInstance("SHA-256")
      ) { case (digest, chunk) =>
        digest.update(chunk)
        digest
      }
      .map { digest =>
        val str = new BigInteger(1, digest.digest()).toString(16)
        if (str.length != 64) "0" + str else str
      }
  }

}
