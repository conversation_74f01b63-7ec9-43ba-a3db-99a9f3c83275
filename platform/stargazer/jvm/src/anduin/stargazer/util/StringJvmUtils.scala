// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.stargazer.util

import java.text.Normalizer
import java.util.regex.Pattern

object StringJvmUtils {

  /** Attempt to normalize a string, i.e. to make its character Ascii printable e.g. "Árvíztű<PERSON><PERSON> tükörfúrógép" ->
    * "Arvizturo tukorfurogep"
    */
  def normalize(s: String): String = {
    val nfdNormalizedString = Normalizer.normalize(s, Normalizer.Form.NFD)
    val pattern = Pattern.compile("[\\p{InCombiningDiacriticalMarks}\\p{M}\\p{Lm}\\p{Sk}\\p{Pi}\\p{Pf}]+")
    pattern.matcher(nfdNormalizedString).replaceAll("")
  }

}
