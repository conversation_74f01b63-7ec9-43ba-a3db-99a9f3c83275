// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.telemetry

import sttp.model.Header
import sttp.tapir.model.ServerRequest
import sttp.tapir.server.interceptor.{RequestInterceptor, RequestResult}
import zio.{Task, ZIO}

import anduin.utils.RequestIdUtils

object RequestId {

  def fromHeaderTuples(headers: Seq[(String, String)]): Option[String] = {
    headers.find((name, _) => name.toLowerCase == RequestIdUtils.IdHeader.toLowerCase).map(_._2)
  }

  def fromHeaders(headers: Seq[Header]): Option[String] = {
    val tuples = headers.map(h => (h.name, h.value))
    fromHeaderTuples(tuples)
  }

  def getXQuangAddr(id: String): String = {
    s"${RequestIdUtils.XQuangAddr}/${id}"
  }

  val requestInterceptor: RequestInterceptor[Task] = RequestInterceptor.transformServerRequest[Task] { req =>
    req.header(RequestIdUtils.IdHeader) match {
      case Some(_) => ZIO.succeed(req)
      case None =>
        for {
          id <- RequestIdUtils.generate()
          injected = req.withOverride(
            protocolOverride = None,
            connectionInfoOverride = None,
            headersOverride = Some(req.headers.appended(Header(RequestIdUtils.IdHeader, id)))
          )
        } yield injected
    }
  }

  val responseInterceptor: RequestInterceptor[Task] =
    RequestInterceptor.transformResult(new RequestInterceptor.RequestResultTransform[Task] {

      override def apply[B](request: ServerRequest, result: RequestResult[B]): Task[RequestResult[B]] = {
        val id = fromHeaders(request.headers).getOrElse("")
        result match {
          case RequestResult.Response(resp) =>
            val injected = resp.addHeaders(Seq(Header(RequestIdUtils.IdHeader, id)))
            ZIO.succeed(RequestResult.Response(injected))
          case RequestResult.Failure(_) => ZIO.succeed(result)
        }
      }

    })

}
