// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.telemetry

import io.opentelemetry.exporter.otlp.trace.OtlpGrpcSpanExporter
import io.opentelemetry.sdk.resources.Resource
import io.opentelemetry.sdk.trace.`export`.{BatchSpanProcessor, SpanExporter}
import io.opentelemetry.sdk.trace.{SdkTracerProvider, SpanProcessor}
import zio.{<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>}

import com.anduin.stargazer.service.GondorBackendConfig.TracingConfig
import com.anduin.stargazer.utils.DeploymentUtils

final case class TracerProvider(tracingConfig: TracingConfig) {

  lazy val collectorEndpoint: String = {
    s"${tracingConfig.collectorSchema}://${tracingConfig.collectorEndpoint}:${tracingConfig.collectorPort}"
  }

  lazy val otlpGrpcExporter: RIO[Scope, SpanExporter] = {
    ZIO.fromAutoCloseable(
      ZIO.succeed(
        OtlpGrpcSpanExporter
          .builder()
          .setEndpoint(collectorEndpoint)
          .build()
      )
    )
  }

  lazy val defaultProvider: RIO[Scope, SdkTracerProvider] = {
    for {
      exporter <- otlpGrpcExporter
      processor <- TracerProvider.batchProcessor(exporter)
      provider <- ZIO.fromAutoCloseable(
        ZIO.succeed(
          SdkTracerProvider
            .builder()
            .setResource(TracerProvider.spanResource)
            .addSpanProcessor(processor)
            .setSampler(DefaultSampler.instance(tracingConfig))
            .build()
        )
      )
    } yield provider
  }

}

object TracerProvider {

  lazy val spanResource: Resource = {
    Resource
      .builder()
      .put("service.name", DeploymentUtils.deploymentName)
      .put("deployment.environment", DeploymentUtils.deploymentName)
      .put("pod_name", DeploymentUtils.getHostName.getOrElse("unknown"))
      .put("app_name", DeploymentUtils.appName)
      .build()
  }

  def batchProcessor(exporter: SpanExporter): RIO[Scope, SpanProcessor] = {
    ZIO.fromAutoCloseable(
      ZIO.succeed(
        BatchSpanProcessor
          .builder(exporter)
          .build()
      )
    )
  }

}
