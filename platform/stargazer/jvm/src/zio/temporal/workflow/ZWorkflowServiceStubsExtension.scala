// Copyright (C) 2014-2025 Anduin Transactions Inc.

package zio.temporal.workflow

import io.temporal.api.workflowservice.v1.ListNamespacesRequest
import scala.jdk.CollectionConverters.*

import zio.{Task, ZIO}

import anduin.workflow.worker.NamespaceInfo

extension (service: ZWorkflowServiceStubs) {

  def listNamespaces: Task[Set[NamespaceInfo]] = {
    val listNamespaceRequests = ListNamespacesRequest
      .newBuilder()
      .build()

    ZIO.attemptBlocking {
      service.toJava
        .blockingStub()
        .listNamespaces(listNamespaceRequests)
        .getNamespacesList
        .asScala
        .map { d =>
          NamespaceInfo(
            d.getNamespaceInfo.getName,
            d.getNamespaceInfo.getId,
            d
          )
        }
        .toSet
    }
  }

}
