syntax = "proto3";

package kafka;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";
import "inbox/email.proto";
import "email_address.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.kafka"
  single_file: true
  import: "anduin.model.common.emailaddress.EmailAddress"
  import: "anduin.model.id.EmailId"
  import: "anduin.model.email.*"
  import: "anduin.model.kafka.KafkaTopicMessage"
};

message ReceivedFromMailgun {
  option (scalapb.message).companion_extends = "KafkaTopicMessage[String, ReceivedFromMailgun]";
  map<string, string> content = 1;
  google.protobuf.StringValue webhook_type = 2;
}

message FailedEmail {
  option (scalapb.message).companion_extends = "KafkaTopicMessage[String, FailedEmail]";
  oneof content {
    ReceivedFromMailgun raw = 1;
    EmailModel extracted = 2;
    TaggedEmail tagged_email = 4;
  }
  string error_message = 3;
}

message ExtractedEmail {
  option (scalapb.message).companion_extends = "KafkaTopicMessage[EmailId, ExtractedEmail]";
  repeated EmailAddressMessage system_addresses = 1 [(scalapb.field).type = "EmailAddress"];
  EmailModel email = 2;
}

message TaggedEmail {
  option (scalapb.message).companion_extends = "KafkaTopicMessage[EmailId, TaggedEmail]";
  string email_id = 1 [(scalapb.field).type = "EmailId"];
  map<string, EmailTagValues> tag_map = 2 [(scalapb.field).key_type = "EmailTagKey"];
}
