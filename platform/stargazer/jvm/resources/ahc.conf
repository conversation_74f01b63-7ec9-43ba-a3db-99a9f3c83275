threadPoolName="sttp-ahc"
maxConnections=-1
maxConnectionsPerHost=-1
acquireFreeChannelTimeout=30000
connectTimeout=5000
pooledConnectionIdleTimeout=100
connectionPoolCleanerPeriod=1000
readTimeout=60000
requestTimeout=60000
connectionTtl=500
followRedirect=false
maxRedirects=5
compressionEnforced=false
userAgent="AHC/2.1"
enabledProtocols=["TLSv1.2", "TLSv1.1", "TLSv1"]
enabledCipherSuites=[]
filterInsecureCipherSuites=true
useProxySelector=false
useProxyProperties=false
validateResponseHeaders=true
aggregateWebSocketFrameFragments=true
strict302Handling=false
keepAlive=true
maxRequestRetry=5
disableUrlEncodingForBoundRequests=false
useLaxCookieEncoder=false
removeQueryParamOnRedirect=true
useOpenSsl=false
useInsecureTrustManager=false
disableHttpsEndpointIdentificationAlgorithm=false
sslSessionCacheSize=0
sslSessionTimeout=0
tcpNoDelay=true
soReuseAddress=false
soKeepAlive=true
soLinger=-1
soSndBuf=-1
soRcvBuf=-1
httpClientCodecMaxInitialLineLength=4096
httpClientCodecMaxHeaderSize=8192
httpClientCodecMaxChunkSize=8192
httpClientCodecInitialBufferSize=128
disableZeroCopy=false
handshakeTimeout=10000
chunkedFileChunkSize=8192
webSocketMaxBufferSize=128000000
webSocketMaxFrameSize=10240
keepEncodingHeader=false
shutdownQuietPeriod=2000
shutdownTimeout=15000
useNativeTransport=false
ioThreadsCount=0
hashedWheelTimerTickDuration=100
hashedWheelTimerSize=512
expiredCookieEvictionDelay=30000
