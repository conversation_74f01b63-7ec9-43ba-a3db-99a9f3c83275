// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.gondor.server.funddata

import zio.test.*

import anduin.funddata.endpoint.dataroom.{
  FundDataDataRoomEndpoints,
  GetDataRoomsParams,
  LinkDataRoomParams,
  UnlinkDataRoomParams
}
import anduin.funddata.utils.FundDataIntegUtils
import anduin.id.funddata.FundDataFirmId
import anduin.id.role.portal.PortalSectionId
import anduin.model.common.user.UserId
import anduin.model.id.{DataRoomWorkflowIdFactory, TransactionIdFactory}
import anduin.portaluser.PortalUserModel
import anduin.portaluser.PortalUserProtocols.PortalRole
import anduin.testing.*

object FundDataDataRoomEndpointServerInteg extends FundDataBaseInteg with EndpointBaseInteg with TestPortalUser {

  override protected def roles: Seq[PortalRole] = Seq(
    PortalRole(PortalSectionId.FundData, PortalUserModel.Relation.Admin)
  )

  private var testFirmId: FundDataFirmId = scala.compiletime.uninitialized // scalafix:ok
  private var portalUser: UserId = scala.compiletime.uninitialized // scalafix:ok
  private val firmMember = TestUsers.userCM
  private val noRoleMember = TestUsers.userCC
  private val trxnId = TransactionIdFactory.unsafeRandomId
  private val dataRoomWorkflowId = DataRoomWorkflowIdFactory.unsafeRandomId(trxnId)

  override def spec = suite("FundDataDataRoomEndpointServerInteg")(
    test("Setup a FDM") {
      for {
        resp <- FundDataIntegUtils.createFullFDM(createMemberParams =
          List(FundDataIntegUtils.CreateMemberParams(TestUsers.userCMEmail, roleIndex = 1))
        )
      } yield {
        testFirmId = resp.firmId
        portalUser = testUserId
        assertCompletes
      }
    },
    suite("linkDataRoomService")(
      test("success - portal user") {
        validateEndpointSuccess(
          FundDataDataRoomEndpoints.linkDataRoom,
          fundDataDataRoomEndpointServer.linkDataRoomService
        )(
          LinkDataRoomParams(
            testFirmId,
            dataRoomWorkflowId
          ),
          portalUser
        )
      },
      test("failed - firm member") {
        validateEndpointFailure(
          FundDataDataRoomEndpoints.linkDataRoom,
          fundDataDataRoomEndpointServer.linkDataRoomService
        )(
          LinkDataRoomParams(
            testFirmId,
            dataRoomWorkflowId
          ),
          firmMember
        )
      }
    ),
    suite("unlinkDataRoomService")(
      test("success - portal user") {
        validateEndpointSuccess(
          FundDataDataRoomEndpoints.unlinkDataRoom,
          fundDataDataRoomEndpointServer.unlinkDataRoomService
        )(
          UnlinkDataRoomParams(
            testFirmId,
            dataRoomWorkflowId
          ),
          portalUser
        )
      },
      test("failed - firm member") {
        validateEndpointFailure(
          FundDataDataRoomEndpoints.unlinkDataRoom,
          fundDataDataRoomEndpointServer.unlinkDataRoomService
        )(
          UnlinkDataRoomParams(
            testFirmId,
            dataRoomWorkflowId
          ),
          firmMember
        )
      }
    ),
    suite("getDataRoomsService")(
      test("success - has role user") {
        validateEndpointSuccess(
          FundDataDataRoomEndpoints.getDataRooms,
          fundDataDataRoomEndpointServer.getDataRoomsService
        )(
          GetDataRoomsParams(
            testFirmId
          ),
          firmMember
        )
      },
      test("failed - no role user") {
        validateEndpointFailure(
          FundDataDataRoomEndpoints.getDataRooms,
          fundDataDataRoomEndpointServer.getDataRoomsService
        )(
          GetDataRoomsParams(
            testFirmId
          ),
          noRoleMember
        )
      }
    )
  ) @@ TestAspect.sequential

}
