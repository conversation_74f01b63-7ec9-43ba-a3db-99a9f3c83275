// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.investor

import squants.market.{GBP, Money, USD}
import zio.test.*

import anduin.funddata.endpoint.investor.GetInvestorsParams
import anduin.funddata.utils.FundDataIntegUtils
import anduin.funddata.utils.FundDataIntegUtils.*
import anduin.fundsub.endpoint.admin.UpdateLpCommitmentOperator
import anduin.fundsub.endpoint.report.UpdateFundReportPreferenceParams
import anduin.greylin.operation.fundsub.FundSubscriptionSubFundOperations
import anduin.id.funddata.*
import anduin.id.fundsub.{FundSubId, FundSubLpId, InvestmentFundId}
import anduin.protobuf.external.squants.{CurrencyMessage, MoneyMessage}
import anduin.protobuf.fundsub.models.{FundSubReportingModel, InvestmentFundModel}
import anduin.protobuf.fundsub.FundSubAdminRole.FundSubAdmin
import anduin.testing.{FundDataBaseInteg, GondorCoreIntegUtils}
import com.anduin.stargazer.service.fundsub.FundSubIntegUtils

object FundDataInvestorServiceCommitmentInteg extends FundDataBaseInteg with GondorCoreIntegUtils {

  /** Client - IE1 - fund 1 subscription 1 - fundSub 1 lp 1 - (10 USD) - 1 USD -> 10 GBP
    *   - fund 2 subscription - fundSub 2 lp - (40 USD, 50 GBP)
    *   - IE2 - fund 1 subscription 2 - fundSub 1 lp 2 - (20 USD, 30 GBP) - 1 USD -> 10 GBP
    */

  // scalafix:off DisableSyntax.var
  private var testFundSub1Id: FundSubId = scala.compiletime.uninitialized
  private var testFundSub1Lp1Id: FundSubLpId = scala.compiletime.uninitialized
  private var testFundSub1Lp2Id: FundSubLpId = scala.compiletime.uninitialized
  private var testFundSub1SubFund1Id: InvestmentFundId = scala.compiletime.uninitialized
  private var testFundSub1SubFund2Id: InvestmentFundId = scala.compiletime.uninitialized

  private var testFundSub2Id: FundSubId = scala.compiletime.uninitialized
  private var testFundSub2LpId: FundSubLpId = scala.compiletime.uninitialized
  private var testFundSub2SubFund1Id: InvestmentFundId = scala.compiletime.uninitialized
  private var testFundSub2SubFund2Id: InvestmentFundId = scala.compiletime.uninitialized

  private var testFirmId: FundDataFirmId = scala.compiletime.uninitialized
  private var testFund1Id: FundDataFundId = scala.compiletime.uninitialized
  private var testFund2Id: FundDataFundId = scala.compiletime.uninitialized
  private var testFund1Subscription1Id: FundDataFundSubscriptionId = scala.compiletime.uninitialized
  private var testFund1Subscription2Id: FundDataFundSubscriptionId = scala.compiletime.uninitialized
  private var testFund2SubscriptionId: FundDataFundSubscriptionId = scala.compiletime.uninitialized
  private var testClientId: FundDataInvestorId = scala.compiletime.uninitialized
  private var testInvestmentEntity1Id: FundDataInvestmentEntityId = scala.compiletime.uninitialized
  private var testInvestmentEntity2Id: FundDataInvestmentEntityId = scala.compiletime.uninitialized
  // scalafix:on

  override def spec = suite("FundDataInvestorServiceCommitmentInteg")(
    setUpSuite,
    prepareCommitmentSuite,
    testGetClientsSuite,
    testGetClientBreakdownCommitmentSuite
  ) @@ TestAspect.sequential

  private val setUpSuite = suite("Set up")(
    test("Create fund sub 1") {
      for {
        fundSubId <- FundSubIntegUtils.createFundSub(
          fundName = "FundSub for investor service commitment test",
          fundAdmins = Seq(userCCEmail -> FundSubAdmin),
          entityAdmin = userCC,
          investmentFunds = Seq(
            InvestmentFundModel(
              amountFieldId = "amount",
              currency = CurrencyMessage.USD
            ),
            InvestmentFundModel(
              amountFieldId = "amount",
              currency = CurrencyMessage.GBP
            )
          )
        )
        _ = testFundSub1Id = fundSubId
        lpIds <- FundSubIntegUtils.inviteLpToFund(
          testFundSub1Id,
          lpUserIds = Seq(userIC, userSH1),
          fundManagerActorUserId = userCC,
          firmName = "Anduin Invest"
        )
        _ = testFundSub1Lp1Id = lpIds(0)
        _ = testFundSub1Lp2Id = lpIds(1)
        subFunds <- greylinDataService.run(
          FundSubscriptionSubFundOperations.Default.getByFund(testFundSub1Id)
        )
        _ = testFundSub1SubFund1Id = subFunds.find(_.currency == USD).get.id
        _ = testFundSub1SubFund2Id = subFunds.find(_.currency == GBP).get.id
      } yield assertCompletes
    },
    test("Create fund sub 2") {
      for {
        fundSubId <- FundSubIntegUtils.createFundSub(
          fundName = "FundSub for investor service commitment test",
          fundAdmins = Seq(userCCEmail -> FundSubAdmin),
          entityAdmin = userCC,
          investmentFunds = Seq(
            InvestmentFundModel(
              amountFieldId = "amount",
              currency = CurrencyMessage.USD
            ),
            InvestmentFundModel(
              amountFieldId = "amount",
              currency = CurrencyMessage.GBP
            )
          )
        )
        _ = testFundSub2Id = fundSubId
        lpIds <- FundSubIntegUtils.inviteLpToFund(
          testFundSub2Id,
          lpUserIds = Seq(userIC),
          fundManagerActorUserId = userCC,
          firmName = "Anduin Invest"
        )
        _ = testFundSub2LpId = lpIds(0)
        subFunds <- greylinDataService.run(
          FundSubscriptionSubFundOperations.Default.getByFund(testFundSub2Id)
        )
        _ = testFundSub2SubFund1Id = subFunds.find(_.currency == USD).get.id
        _ = testFundSub2SubFund2Id = subFunds.find(_.currency == GBP).get.id
      } yield assertCompletes
    },
    test("Create idm") {
      for {
        resp <- FundDataIntegUtils.createFullFDM(
          createMemberParams = List(CreateMemberParams(userCCEmail)),
          createFundLinkToFSParams =
            List(CreateFundLinkToFSParams(testFundSub1Id), CreateFundLinkToFSParams(testFundSub2Id)),
          createInvestorParams = List(CreateInvestorParams()),
          createIEParams = List(CreateIEParams(), CreateIEParams()),
          createFundSubscriptionLinkToFSOrderParams = List(
            CreateFundSubscriptionLinkToFSOrderParams(testFundSub1Lp1Id),
            CreateFundSubscriptionLinkToFSOrderParams(testFundSub2LpId, fundV2Index = 1),
            CreateFundSubscriptionLinkToFSOrderParams(testFundSub1Lp2Id, linkedInvestmentEntityIndexOpt = Some(1))
          )
        )
      } yield {
        testFirmId = resp.firmId
        testFund1Id = resp.fundV2Ids.head
        testFund2Id = resp.fundV2Ids(1)
        testFund1Subscription1Id = resp.fundSubscriptionIds(0)
        testFund2SubscriptionId = resp.fundSubscriptionIds(1)
        testFund1Subscription2Id = resp.fundSubscriptionIds(2)
        testClientId = resp.investorIds(0)
        testInvestmentEntity1Id = resp.investmentEntityIds(0)
        testInvestmentEntity2Id = resp.investmentEntityIds(1)
        assertCompletes
      }
    }
  )

  private val prepareCommitmentSuite = suite("Prepare commitment")(
    test("Set up main currency for fund 1") {
      for {
        _ <- fundSubReportService.updateFundReportPreferenceUnsafe(
          UpdateFundReportPreferenceParams(
            fundSubId = testFundSub1Id,
            mainCurrencyOpt = Some(CurrencyMessage.GBP),
            exchangeRatesOpt = Some(Seq(FundSubReportingModel.ExchangeRateEntry(CurrencyMessage.USD, 10.0)))
          ),
          userCC
        )
      } yield assertCompletes
    },
    test("Add commitments for fund 1 LP 1") {
      for {
        _ <- fundsubAdminService.updateLpCommitment(
          testFundSub1Lp1Id,
          investmentFundIdOpt = Some(testFundSub1SubFund1Id),
          expectedCommitment = UpdateLpCommitmentOperator.Change(Some(MoneyMessage(CurrencyMessage.USD, "10"))),
          acceptedCommitment = UpdateLpCommitmentOperator.KeepUnchange,
          actor = userCC
        )
      } yield assertCompletes
    },
    test("Add commitments for fund 1 LP 2") {
      for {
        _ <- fundsubAdminService.updateLpCommitment(
          testFundSub1Lp2Id,
          investmentFundIdOpt = Some(testFundSub1SubFund1Id),
          expectedCommitment = UpdateLpCommitmentOperator.Change(Some(MoneyMessage(CurrencyMessage.USD, "20"))),
          acceptedCommitment = UpdateLpCommitmentOperator.KeepUnchange,
          actor = userCC
        )
        _ <- fundsubAdminService.updateLpCommitment(
          testFundSub1Lp2Id,
          investmentFundIdOpt = Some(testFundSub1SubFund2Id),
          expectedCommitment = UpdateLpCommitmentOperator.Change(Some(MoneyMessage(CurrencyMessage.GBP, "30"))),
          acceptedCommitment = UpdateLpCommitmentOperator.KeepUnchange,
          actor = userCC
        )
      } yield assertCompletes
    },
    test("Add commitments for fund 2 LP") {
      for {
        _ <- fundsubAdminService.updateLpCommitment(
          testFundSub2LpId,
          investmentFundIdOpt = Some(testFundSub2SubFund1Id),
          expectedCommitment = UpdateLpCommitmentOperator.Change(Some(MoneyMessage(CurrencyMessage.USD, "40"))),
          acceptedCommitment = UpdateLpCommitmentOperator.KeepUnchange,
          actor = userCC
        )
        _ <- fundsubAdminService.updateLpCommitment(
          testFundSub2LpId,
          investmentFundIdOpt = Some(testFundSub2SubFund2Id),
          expectedCommitment = UpdateLpCommitmentOperator.Change(Some(MoneyMessage(CurrencyMessage.GBP, "50"))),
          acceptedCommitment = UpdateLpCommitmentOperator.KeepUnchange,
          actor = userCC
        )
      } yield assertCompletes
    }
  )

  private val testGetClientsSuite = suite("Test get clients total commitment")(
    test("Get clients commitment") {
      for {
        clientResp <- fundDataInvestorService.getInvestors(
          GetInvestorsParams(testFirmId),
          userCC
        )
      } yield {
        val clientCommitment = clientResp.investors.head.totalCommitment
        assertTrue(
          clientCommitment.contains(Money(40, USD)),
          clientCommitment.contains(Money(380, GBP))
        )
      }
    }
  )

  private val testGetClientBreakdownCommitmentSuite = suite("Test get client breakdown commitment")(
    test("Get client breakdown commitment") {
      for {
        resp <- fundDataInvestorService.getInvestorCommitments(
          testClientId,
          userCC
        )
      } yield {
        val entity1 = resp.investmentEntities.find(_.investmentEntityId == testInvestmentEntity1Id).get
        val entity1Fund1 = entity1.fundsInfo.find(_.fundId == testFund1Id).get
        val entity1Fund2 = entity1.fundsInfo.find(_.fundId == testFund2Id).get
        val entity2 = resp.investmentEntities.find(_.investmentEntityId == testInvestmentEntity2Id).get
        val entity2Fund1 = entity2.fundsInfo.find(_.fundId == testFund1Id).get
        assertTrue(
          resp.investmentEntities.size == 2,
          entity1Fund1.commitments.contains(Money(100, GBP)),
          entity1Fund2.commitments.contains(Money(40, USD)),
          entity1Fund2.commitments.contains(Money(50, GBP)),
          entity2Fund1.commitments.contains(Money(230, GBP))
        )
      }
    }
  )

}
