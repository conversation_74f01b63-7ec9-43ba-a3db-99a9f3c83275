// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.link

import java.time.Instant

import zio.implicits.*
import zio.{Task, ZIO}

import anduin.account.authentication.{CaptchaService, LoginService}
import anduin.account.enterprise.EnterpriseService
import anduin.account.profile.UserProfileService
import anduin.account.profile.UserProfileService.EmailNotFound
import anduin.account.protocol.BifrostCommonProtocol.{CaptchaResponse, LoginAuthenticator}
import anduin.account.signup.SignupVerifyService
import anduin.environment.{
  EnvironmentAuthenticationIntegrationService,
  EnvironmentReauthenticationPolicyService,
  EnvironmentService
}
import anduin.fdb.record.FDBRecordDatabase
import anduin.fdb.record.model.RecordTask
import anduin.id.account.{EnterpriseLoginConfigId, EnterpriseLoginLinkId}
import anduin.id.authwhitelabel.AuthenticationWhitelabelId
import anduin.id.link.ProtectedLinkId
import anduin.id.offering.GlobalOfferingId
import anduin.jwt.{Jwt, JwtService}
import anduin.link
import anduin.link.ProtectedLinkStoreOperations.ProtectedLinkModel
import anduin.link.tokenclaim.PublicPasswordPassedToken
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.UserId
import anduin.service.{AuthenticatedRequestContext, PublicRequestContext, RequestContext}
import anduin.whitelabel.auth.AuthenticationWhitelabelService
import com.anduin.stargazer.service.GondorConfig
import com.anduin.stargazer.service.utils.ZIOUtils

final case class ProtectedLinkServiceImpl(
  userProfileService: UserProfileService,
  captchaService: CaptchaService,
  signupVerifyService: SignupVerifyService,
  jwt: Jwt,
  gondorConfig: GondorConfig,
  enterpriseService: EnterpriseService,
  authenticationWhitelabelService: AuthenticationWhitelabelService,
  environmentService: EnvironmentService,
  environmentAuthenticationIntegrationService: EnvironmentAuthenticationIntegrationService,
  environmentPolicyService: EnvironmentReauthenticationPolicyService,
  loginService: LoginService
) extends ProtectedLinkService {

  private val jwtService = JwtService(
    issuer = "account",
    audience = "",
    subject = ""
  )

  private def execute[A](task: ProtectedLinkStoreOperations => RecordTask[A]) = {
    FDBRecordDatabase.transact(ProtectedLinkStoreOperations.Production) { store =>
      task(store)
    }
  }

  override def verifyCaptchaResponse(captchaResponse: CaptchaResponse): Task[Unit] = {
    captchaService.verifyCaptchaResponse(captchaResponse).flatMapError {
      case ex: CaptchaService.CaptchaException =>
        ZIO
          .logWarningCause(s"Failed to verify Recaptcha for protected link", ex.toCause)
          .as(CheckValidityException.NonHuman)
      case error: Throwable => ZIO.succeed(error)
    }
  }

  private def verifyRecaptcha(params: CheckValidityParams) = {
    ZIOUtils
      .traverseOptionUnit(params.passwordOpt) { password =>
        verifyCaptchaResponse(password.captchaResponse)
      }
  }

  private def createPasswordPassedToken(linkId: ProtectedLinkId) = {
    jwt.encodeTemporaryToken(
      body = PublicPasswordPassedToken(linkId),
      service = jwtService,
      expireIn = gondorConfig.backendConfig.protectedLinkConfig.publicPasswordPassedTokenDuration
    )
  }

  private def createEnterpriseLoginLink(
    protectedLinkId: ProtectedLinkId,
    enterpriseLoginParams: ProtectedLinkService.EnterpriseLoginParams
  ): Task[ProtectedLinkModel] = {
    for {
      enterpriseLoginLink <- enterpriseService.createEnterpriseLoginLink(
        configId = enterpriseLoginParams.configId,
        path = s"#/link/${protectedLinkId.idString}",
        offeringId = enterpriseLoginParams.offeringId,
        creatorId = enterpriseLoginParams.creatorId,
        context = enterpriseLoginParams.context,
        environmentId = enterpriseLoginParams.environmentId,
        engagementId = enterpriseLoginParams.engagementId
      )
      newModel <- execute(
        _.updateEnterpriseLoginLink(protectedLinkId, Some(enterpriseLoginLink.id))
      )
    } yield newModel
  }

  override def create(
    passwordOpt: Option[String],
    expiryDate: Option[Instant],
    whitelistedDomains: Set[String],
    asset: String,
    authenticationWhitelabelId: Option[AuthenticationWhitelabelId],
    isRequiredApproval: Boolean = false,
    enterpriseLoginParamsOpt: Option[ProtectedLinkService.EnterpriseLoginParams] = None
  ): Task[(ProtectedLinkId, ProtectedLinkModel)] = {
    for {
      (protectedLinkId, protectedLinkModel) <- execute(
        _.create(
          passwordOpt,
          expiryDate,
          whitelistedDomains,
          asset,
          authenticationWhitelabelId,
          isRequiredApproval
        )
      )
      _ <- ZIOUtils.traverseOption(enterpriseLoginParamsOpt) { enterpriseLoginParams =>
        createEnterpriseLoginLink(protectedLinkId, enterpriseLoginParams)
      }
    } yield (protectedLinkId, protectedLinkModel)

  }

  override def updateWhiteLabel(
    linkId: ProtectedLinkId,
    newWhiteLabel: ProtectedLinkWhitelabel
  ): Task[ProtectedLinkModel] = {
    execute(_.updateWhitelabel(linkId, newWhiteLabel))
  }

  override def getLinkState(linkId: ProtectedLinkId, ctx: Option[PublicRequestContext] = None)
    : Task[ProtectedLinkState] = {
    val now = Instant.now
    for {
      _ <- ZIO.logInfo(s"[ProtectedLink] User with IP ${ctx.flatMap(_.getClientIP)} get link state Link Id = $linkId")
      state <- execute(_.getLinkState(linkId, now))
    } yield state
  }

  override def getLinkStateWithEnterpriseLogin(
    linkId: ProtectedLinkId,
    globalOfferingId: GlobalOfferingId,
    ctx: Option[PublicRequestContext] = None
  ): Task[ProtectedLinkStateWithEnterpriseLogin] = {
    for {
      linkState <- getLinkState(linkId, ctx)
      linkAssetOpt <- getLinkAssetUnsafe(linkId)
      environmentIdOpt <- ZIOUtils.traverseOption2(linkAssetOpt)(linkAsset =>
        environmentAuthenticationIntegrationService.resolveEnvironmentFromProtectedLink(linkAsset, globalOfferingId)
      )
      isSharableLinkEnabled <- environmentIdOpt.fold(ZIO.succeed(true))(environmentId =>
        environmentAuthenticationIntegrationService.isSharableLinkEnabled(globalOfferingId, environmentId)
      )
      environmentIsSSOEnforced <- environmentIdOpt.fold(ZIO.succeed(false)) { environmentId =>
        environmentPolicyService.hasReauthenticationPolicy(environmentId)
      }
      enterpriseLoginUrlOpt <- ZIOUtils.traverseOption2(linkState.enterpriseLoginLinkId) { enterpriseLoginLinkId =>
        if (environmentIsSSOEnforced) {
          ZIO.succeed(None)
        } else {
          for {
            enterpriseLoginLink <- enterpriseService.getEnterpriseLoginLink(enterpriseLoginLinkId)
            url <- enterpriseService.resolveLinkSourceUrl(enterpriseLoginLink)
          } yield Some(url)
        }
      }
    } yield ProtectedLinkStateWithEnterpriseLogin(
      if (isSharableLinkEnabled) {
        linkState
      } else {
        linkState.copy(isDisabled = true)
      },
      enterpriseLoginUrlOpt
    )
  }

  override def getUserLoginUrlWithEnterpriseLogin(
    configId: EnterpriseLoginConfigId,
    redirectUrl: Option[String],
    email: Option[String],
    triggeredByEnvironment: Boolean,
    linkId: ProtectedLinkId,
    globalOfferingId: GlobalOfferingId,
    ctx: Option[PublicRequestContext]
  ): Task[String] = {
    for {
      _ <- getLinkState(linkId, ctx)
      linkAssetOpt <- getLinkAssetUnsafe(linkId)
      environmentIdOpt <- ZIOUtils.traverseOption2(linkAssetOpt)(linkAsset =>
        environmentAuthenticationIntegrationService.resolveEnvironmentFromProtectedLink(linkAsset, globalOfferingId)
      )
      enforcementEnabled <- environmentIdOpt.fold(ZIO.attempt(false)) { environmentId =>
        environmentPolicyService.hasReauthenticationPolicy(environmentId)
      }
      url <- enterpriseService.getUserLoginUrl(
        enterpriseLoginConfigId = configId,
        redirectUrl = redirectUrl,
        email = email,
        skipLinkAccount = false,
        triggeredByEnvironment = triggeredByEnvironment,
        mustImpersonateConfig = enforcementEnabled
      )
    } yield url
  }

  /** This function doesn't run through verification to get link asset.
    */
  override def getLinkAssetUnsafe(linkId: ProtectedLinkId): Task[Option[String]] = {
    execute(_.getLinkAsset(linkId))
  }

  override def getUserLinkState(linkId: ProtectedLinkId, userId: UserId): Task[UserProtectedLinkState] = {
    for {
      emailAddress <- userProfileService.getEmailAddress(userId)
      now = Instant.now
      emailDomain = emailAddress.domain.value
      userLinkState <- execute(
        _.getUserLinkState(
          linkId,
          userId,
          emailDomain,
          now
        )
      )
    } yield userLinkState
  }

  override def checkValidity(
    params: CheckValidityParams,
    userId: UserId,
    ctx: Option[AuthenticatedRequestContext]
  ): Task[ProtectedLinkEmptyResponse] = {
    for {
      _ <- ZIO.logInfo(
        s"[ProtectedLink] User $userId with IP ${ctx.flatMap(_.getClientIP)} entering password for protected link ${params.linkId}"
      )
      _ <- verifyRecaptcha(params)
      emailAddress <- userProfileService.getEmailAddress(userId)
      now = Instant.now
      emailDomain = emailAddress.domain.value
      _ <- execute(
        _.checkValidity(
          params.linkId,
          userId,
          emailDomain,
          params.passwordOpt.map(_.password),
          now
        )
      )
    } yield ProtectedLinkEmptyResponse()
  }

  override def checkValidityPublic(
    params: CheckValidityParams,
    ctx: Option[PublicRequestContext]
  ): Task[CheckValidityPublicResponse] = {
    val withPasswordText = params.passwordOpt.fold("without password")(_ => "with password")
    for {
      _ <- ZIO.logInfo(
        s"[ProtectedLink] User with IP ${ctx.flatMap(_.getClientIP)} " +
          s"checkValidityPublic for protected link ${params.linkId} $withPasswordText"
      )
      _ <- verifyRecaptcha(params)
      now = Instant.now
      _ <- execute(
        _.checkValidityPublic(
          params.linkId,
          params.passwordOpt.map(_.password),
          now
        )
      )
    } yield CheckValidityPublicResponse(createPasswordPassedToken(params.linkId))
  }

  override def verifyLinkIdFromToken(linkId: ProtectedLinkId, tokenStrOpt: Option[String]): Task[Unit] = {
    tokenStrOpt.fold[Task[Unit]] {
      checkValidityPublic(CheckValidityParams(linkId, None), None).unit
    } { tokenStr =>
      jwt
        .decodeTemporaryToken[PublicPasswordPassedToken](tokenStr, jwtService)
        .fold[Task[Unit]](
          ZIO.fail(_),
          token => ZIOUtils.validate(token.linkId == linkId)(new RuntimeException("Token linkId doesn't match"))
        )
    }
  }

  override def checkSignUpEligibility(linkId: ProtectedLinkId, emailDomain: String): Task[Unit] = {
    execute(_.checkSignUpEligibility(linkId, emailDomain))
  }

  override def markAsPasswordPassed(linkId: ProtectedLinkId, userId: UserId): Task[Unit] = {
    execute(_.markAsPasswordPassed(linkId, userId))
  }

  override def markAsPendingEmailVerification(linkId: ProtectedLinkId, userId: UserId): Task[Unit] = {
    execute(_.markAsPendingEmailVerification(linkId, userId))
  }

  override def getPendingEmailVerification(linkId: ProtectedLinkId, userId: UserId): Task[Boolean] = {
    execute(_.getPendingEmailVerification(linkId, userId))
  }

  /** User must already have passed password before, and the link is still valid. Forever redirecting to the asset.
    */
  override def requestAccess(linkId: ProtectedLinkId, userId: UserId): Task[ProtectedLinkEmptyResponse] = {
    for {
      emailAddress <- userProfileService.getEmailAddress(userId)
      now = Instant.now
      emailDomain = emailAddress.domain.value
      _ <- execute(
        _.grantAccess(
          linkId,
          userId,
          emailDomain,
          now
        )
      )
    } yield ProtectedLinkEmptyResponse()
  }

  override def revokeAccess(linkId: ProtectedLinkId, users: Set[UserId]): Task[Unit] = {
    execute(_.revokeAccess(linkId, users))
  }

  private def deleteEnterpriseLoginLink(
    protectedLinkId: ProtectedLinkId,
    enterpriseLoginLinkId: EnterpriseLoginLinkId
  ): Task[ProtectedLinkModel] = {
    for {
      _ <- enterpriseService.deleteEnterpriseLoginLink(enterpriseLoginLinkId)
      newModel <- execute(
        _.updateEnterpriseLoginLink(protectedLinkId, None)
      )
    } yield newModel
  }

  override def update(
    linkId: ProtectedLinkId,
    passwordChange: Option[Option[String]] = None,
    expiryDateChange: Option[Option[Instant]] = None,
    isDisabledChange: Option[Boolean] = None,
    whitelistDomainsChange: Option[Set[String]] = None,
    isRequireApprovalChange: Option[Boolean] = None,
    enterpriseLoginChange: Option[ProtectedLinkService.EnterpriseLoginChange] = None
  ): Task[ProtectedLinkModel] = {
    for {
      protectedLinkModel <- execute(
        _.update(
          linkId,
          passwordChange,
          expiryDateChange,
          isDisabledChange,
          whitelistDomainsChange,
          isRequireApprovalChange
        )
      )
      newModel <- enterpriseLoginChange.fold(ZIO.attempt(protectedLinkModel)) {
        _.params.fold {
          protectedLinkModel.getEnterpriseLoginLinkId.fold(ZIO.attempt(protectedLinkModel)) {
            enterpriseProtectedLinkId =>
              deleteEnterpriseLoginLink(linkId, enterpriseProtectedLinkId)
          }
        } { params =>
          protectedLinkModel.getEnterpriseLoginLinkId.fold(createEnterpriseLoginLink(linkId, params)) { _ =>
            ZIO.attempt(protectedLinkModel)
          }

        }
      }
    } yield newModel
  }

  override def getWhitelabel(linkId: ProtectedLinkId, context: RequestContext)
    : Task[GetProtectedLinkWhitelabelResponse] = {
    for {
      authenticationWhitelabelIdOpt <- execute(_.getAuthenticationWhitelabelId(linkId))
      environmentDomainOpt <- environmentService.getCurrentEnvironmentWithCustomDomain(context).map(_.map(_._3.domain))
      linkWhitelabelData <- authenticationWhitelabelService.getAuthenticationWhitelabelResponse(
        authenticationWhitelabelIdOpt,
        environmentDomainOpt
      )
    } yield GetProtectedLinkWhitelabelResponse(linkWhitelabelData)
  }

  override def getWhiteLabelForEmail(
    linkId: ProtectedLinkId
  ): Task[Option[AuthenticationWhitelabelService.EmailAuthenticationWhitelabelData]] = {
    for {
      authenticationWhitelabelIdOpt <- execute(_.getAuthenticationWhitelabelId(linkId))
      whiteLabelData <- authenticationWhitelabelService.getEmailAuthenticationWhitelabel(authenticationWhitelabelIdOpt)
    } yield whiteLabelData
  }

  override def verifyAndGetAccountStatus(
    linkId: ProtectedLinkId,
    publicPasswordPassedToken: Option[String],
    email: String,
    globalOfferingId: GlobalOfferingId,
    context: RequestContext
  ): Task[VerifyAndGetAccountStatusResponse] = {
    val task = for {
      _ <- verifyLinkIdFromToken(linkId, publicPasswordPassedToken)
      emailAddress <- ZIOUtils
        .optionToTask(EmailAddress.unapply(email), CheckValidityException.InvalidEmailAddress(email))
      environmentOpt <- environmentService.getCurrentEnvironmentFromDomain(context)
      loginResponse <- loginService.getLoginResponseForEmail(
        email = email,
        environmentIdOpt = environmentOpt.map(_.id),
        offeringIdOpt = Some(globalOfferingId),
        blockBot = false,
        loginFromProtectedLink = Some(linkId)
      )
      response <- loginResponse.authenticator.fold {
        for {
          _ <- checkSignUpEligibility(linkId, emailAddress.domain.value)
            .orElseFail(CheckValidityException.DomainNotWhiteListed(linkId, email))
        } yield VerifyAndGetAccountStatusResponse(AccountStatusForProtectedLink.AccountNotExisted)
      } {
        case LoginAuthenticator.Normal(_) =>
          for {
            _ <- checkSignUpEligibility(linkId, emailAddress.domain.value)
              .orElseFail(CheckValidityException.DomainNotWhiteListed(linkId, email))

            oldUserIdOpt <-
              userProfileService
                .getUserFromEmailAddress(email, useCache = false)
                .map { case (userId, _) =>
                  Some(userId)
                }
                .onErrorHandleWith {
                  case _: EmailNotFound =>
                    ZIO.attempt(None)
                  case error: Throwable => ZIO.fail(error)
                }

            response <- oldUserIdOpt
              .fold(
                ZIO.attempt(VerifyAndGetAccountStatusResponse(AccountStatusForProtectedLink.AccountNotExisted))
              ) { existingUserId =>
                for {
                  noPasswordUser <- signupVerifyService.isNoPasswordUser(existingUserId)
                  isZombie <- userProfileService.isZombieUser(existingUserId)
                  res =
                    if (noPasswordUser) {
                      AccountStatusForProtectedLink.AccountNoPassword
                    } else if (isZombie) {
                      AccountStatusForProtectedLink.AccountZombie
                    } else {
                      AccountStatusForProtectedLink.AccountRegistered
                    }
                } yield VerifyAndGetAccountStatusResponse(res, Some(existingUserId))
              }
          } yield response
        case LoginAuthenticator.SSOEnforced(_, domain, ssoConfig) =>
          ZIO.attempt(
            VerifyAndGetAccountStatusResponse(
              AccountStatusForProtectedLink.AccountSSOEnforced(
                emailAddress.address,
                domain,
                ssoConfig
              ),
              loginResponse.userIdOpt
            )
          )
        case authenticator: LoginAuthenticator.AuthenticateByEnvironment =>
          ZIO.attempt(
            VerifyAndGetAccountStatusResponse(
              AccountStatusForProtectedLink.AccountEnvironmentEnforced(
                authenticator,
                loginResponse.hasPassword,
                loginResponse.enablePrimaryEmailOTP,
                loginResponse.linkedSsoConfigs
              ),
              loginResponse.userIdOpt
            )
          )
      }
    } yield response

    task.onErrorHandleWith {
      case _: link.CheckValidityException.InvalidEmailAddress =>
        ZIO.attempt(VerifyAndGetAccountStatusResponse(AccountStatusForProtectedLink.InvalidEmail))
      case _: CheckValidityException.LinkDisabled =>
        ZIO.attempt(VerifyAndGetAccountStatusResponse(AccountStatusForProtectedLink.LinkDisabled))
      case _: CheckValidityException.LinkExpired =>
        ZIO.attempt(VerifyAndGetAccountStatusResponse(AccountStatusForProtectedLink.LinkExpired))
      case _: CheckValidityException.DomainNotWhiteListed =>
        ZIO.attempt(VerifyAndGetAccountStatusResponse(AccountStatusForProtectedLink.NotWhiteListed))
      case error: Throwable => ZIO.fail(error)
    }
  }

}
