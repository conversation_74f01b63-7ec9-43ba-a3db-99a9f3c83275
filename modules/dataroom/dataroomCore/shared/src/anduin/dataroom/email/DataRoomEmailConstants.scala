// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.email

object DataRoomEmailConstants {

  val replyToSenderTemplateTypes: Seq[DataRoomEmailTemplateType] = Seq(
    DataRoomEmailTemplateType.Invitation,
    DataRoomEmailTemplateType.Reminder,
    DataRoomEmailTemplateType.UserRemovalToUser,
    DataRoomEmailTemplateType.AccessRequestRejection,
    DataRoomEmailTemplateType.RequestAccess
  )

  val customReplyTemplateTypes: Seq[DataRoomEmailTemplateType] = Seq(
    DataRoomEmailTemplateType.Invitation,
    DataRoomEmailTemplateType.Reminder,
    DataRoomEmailTemplateType.InvitationAccepted,
    DataRoomEmailTemplateType.JoinViaLinkInvitation,
    DataRoomEmailTemplateType.UserRemovalToUser,
    DataRoomEmailTemplateType.HomepageMessage,
    DataRoomEmailTemplateType.ManualNotification,
    DataRoomEmailTemplateType.FileUploadDigest,
    DataRoomEmailTemplateType.AccessRequestRejection,
    DataRoomEmailTemplateType.RequestAccess
  )

  val customCcTemplateTypes: Seq[DataRoomEmailTemplateType] = Seq(
    DataRoomEmailTemplateType.Invitation,
    DataRoomEmailTemplateType.Reminder,
    DataRoomEmailTemplateType.InvitationAccepted,
    DataRoomEmailTemplateType.JoinViaLinkInvitation,
    DataRoomEmailTemplateType.UserRemovalToUser,
    DataRoomEmailTemplateType.HomepageMessage,
    DataRoomEmailTemplateType.ManualNotification,
    DataRoomEmailTemplateType.FileUploadDigest,
    DataRoomEmailTemplateType.AccessRequestRejection,
    DataRoomEmailTemplateType.RequestAccess
  )

  val customBccTemplateTypes: Seq[DataRoomEmailTemplateType] = Seq(
    DataRoomEmailTemplateType.Invitation,
    DataRoomEmailTemplateType.Reminder,
    DataRoomEmailTemplateType.InvitationAccepted,
    DataRoomEmailTemplateType.JoinViaLinkInvitation,
    DataRoomEmailTemplateType.UserRemovalToUser,
    DataRoomEmailTemplateType.HomepageMessage,
    DataRoomEmailTemplateType.ManualNotification,
    DataRoomEmailTemplateType.FileUploadDigest,
    DataRoomEmailTemplateType.AccessRequestRejection,
    DataRoomEmailTemplateType.RequestAccess
  )

}
