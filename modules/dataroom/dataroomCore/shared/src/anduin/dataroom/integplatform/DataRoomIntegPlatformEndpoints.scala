// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.integplatform

import anduin.asyncapiv2.execution.AsyncApiWorkflowQueue
import anduin.service.GeneralServiceException
import anduin.tapir.AsyncEndpoint.AsyncAuthenticatedEndpoint
import anduin.tapir.endpoint.EmptyResponse
import anduin.tapir.{AsyncEndpoint, AuthenticatedEndpoints}

import sttp.tapir.*

object DataRoomIntegPlatformEndpoints extends AuthenticatedEndpoints with AsyncEndpoint {
  private val DataRoomIntegPlatformPath = "dataroom-integ-platform"

  val lookupIntegrations: AsyncAuthenticatedEndpoint[
    DataRoomLookupIntegrationsParams,
    GeneralServiceException,
    DataRoomLookupIntegrationsResponse
  ] = asyncEndpoint[
    DataRoomLookupIntegrationsParams,
    GeneralServiceException,
    DataRoomLookupIntegrationsResponse
  ](
    DataRoomIntegPlatformPath / "lookupIntegrations",
    AsyncApiWorkflowQueue.Medium
  )

  val getLinkableInstances: AsyncAuthenticatedEndpoint[
    DataRoomLinkableInstancesParams,
    GeneralServiceException,
    DataRoomLinkableInstancesResponse
  ] = asyncEndpoint[
    DataRoomLinkableInstancesParams,
    GeneralServiceException,
    DataRoomLinkableInstancesResponse
  ](
    DataRoomIntegPlatformPath / "getLinkableInstances",
    AsyncApiWorkflowQueue.Medium
  )

  val linkAppConnector: AsyncAuthenticatedEndpoint[
    DataRoomLinkAppConnectorParams,
    GeneralServiceException,
    EmptyResponse
  ] = asyncEndpoint[
    DataRoomLinkAppConnectorParams,
    GeneralServiceException,
    EmptyResponse
  ](
    DataRoomIntegPlatformPath / "linkAppConnector",
    AsyncApiWorkflowQueue.Medium
  )

  val unlinkAppConnector: AsyncAuthenticatedEndpoint[
    DataRoomUnlinkAppConnectorParams,
    GeneralServiceException,
    EmptyResponse
  ] = asyncEndpoint[
    DataRoomUnlinkAppConnectorParams,
    GeneralServiceException,
    EmptyResponse
  ](
    DataRoomIntegPlatformPath / "unlinkAppConnector",
    AsyncApiWorkflowQueue.Medium
  )

}
