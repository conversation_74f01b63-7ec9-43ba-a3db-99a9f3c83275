// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.exception.graphql

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.model.common.user.UserInfo
import io.circe.Codec

final case class NoAccessAdditionalFields(currentUserInfoOpt: Option[UserInfo])

object NoAccessAdditionalFields {
  given Codec.AsObject[NoAccessAdditionalFields] = deriveCodecWithDefaults
}
