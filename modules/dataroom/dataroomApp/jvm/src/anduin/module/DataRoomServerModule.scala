// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.module

import com.softwaremill.macwire.wire

import anduin.dataroom.server.{DataRoomAppServer, DataRoomSimulatorPublicServer}
import anduin.gondor.server.*

trait DataRoomServerModule extends DataRoomServiceModule {

  given dataRoomServer: DataRoomServer = wire[DataRoomServer]
  given dataRoomUserTrackingServer: DataRoomUserTrackingServer = wire[DataRoomUserTrackingServer]
  given dataRoomSimulatorServer: DataRoomSimulatorServer = wire[DataRoomSimulatorServer]
  given dataRoomSimulatorPublicServer: DataRoomSimulatorPublicServer = wire[DataRoomSimulatorPublicServer]
  given dataRoomIntegrationServer: DataRoomIntegrationServer = wire[DataRoomIntegrationServer]
  given dataRoomPublicServer: DataRoomPublicEndpointServer = wire[DataRoomPublicEndpointServer]
  given fileMoveCopyServer: FileMoveCopyServer = wire[FileMoveCopyServer]
  given dataRoomIntegPlatformServer: DataRoomIntegPlatformServer = wire[DataRoomIntegPlatformServer]
  given dataRoomAppServer: DataRoomAppServer = wire[DataRoomAppServer]
}
