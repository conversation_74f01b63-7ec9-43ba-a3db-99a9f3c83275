// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.server

import sttp.tapir.server.armeria.zio.ArmeriaZioServerInterpreter

import anduin.id.offering.{OfferingId, OfferingIdMapper}
import anduin.standaloneapp.{MainIndexBase, PublicStandaloneAppServer}
import com.anduin.stargazer.service.GondorConfig

final case class DataRoomAppServer(
  gondorConfig: GondorConfig,
  interpreter: ArmeriaZioServerInterpreter[Any]
) extends PublicStandaloneAppServer {

  private val dataRoomMainIndex = DataRoomMainIndex(gondorConfig.frontendConfig, gondorConfig.commonConfig)

  override protected val pathPrefix: String = OfferingIdMapper.getUrlPrefix(OfferingId.DataRoom)

  override protected val mainIndex: MainIndexBase = dataRoomMainIndex

}
