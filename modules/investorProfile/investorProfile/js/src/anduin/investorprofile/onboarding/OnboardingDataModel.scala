// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.onboarding

import anduin.forms.FormData
import anduin.forms.engine.GaiaState
import anduin.id.fundsub.FundSubLpId
import anduin.model.common.user.UserId

private[onboarding] case object OnboardingDataModel {

  sealed trait OnboardingStep derives CanEqual

  case object SelectSuggestedInvestmentEntityName extends OnboardingStep

  case object SelectSubscriptions extends OnboardingStep

  case object SelectPrefillSubscription extends OnboardingStep
  case object PrefillProfile extends OnboardingStep

  case object AddCollaborators extends OnboardingStep

  case object CreateInvestmentEntity extends OnboardingStep

  case class SelectSuggestedInvestmentEntityNameStepData(selectedInvestmentEntityName: String)

  case class SelectSubscriptionsStepData(
    investmentEntityName: String,
    customIdOpt: Option[String],
    selectedSubscriptions: List[FundSubLpId],
    isListExpanded: Boolean
  )

  case class SelectPrefillSubscriptionStepData(
    selectedSubscription: FundSubLpId,
    trackedSubscriptions: List[FundSubLpId],
    subscriptionFormSimilarities: Map[FundSubLpId, Double]
  )

  case class PrefillProfileStepData(
    templateForm: FormData,
    templateComputedState: GaiaState
  )

  case class AddCollaboratorsStepData(collaborators: List[UserId])

  case class OnboardingGlobalData(
    selectSuggestedInvestmentEntityNameStepData: Option[SelectSuggestedInvestmentEntityNameStepData] = None,
    selectSubscriptionsStepData: Option[SelectSubscriptionsStepData] = None,
    selectPrefillSubscriptionStepData: Option[SelectPrefillSubscriptionStepData] = None,
    prefillProfileStepData: Option[PrefillProfileStepData] = None,
    addCollaboratorsStepData: Option[AddCollaboratorsStepData] = None
  )

}
