// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.onboarding.step

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.button.laminar.ButtonL.Color
import design.anduin.components.icon.Icon
import design.anduin.style.tw.*

import anduin.investorprofile.onboarding.OnboardingDataModel.OnboardingStep
import anduin.investorprofile.onboarding.step.WithGeneralStep.{RenderNextButtonInput, RendererInput}

import com.raquo.laminar.api.L.*

private[onboarding] case class WithGeneralStep[P, C](
  previousStepDataSignal: Signal[Option[P]],
  currentStepDataObserver: Observer[Option[C]],
  onBack: Observer[Unit],
  onNext: Observer[OnboardingStep],
  initialState: Option[C] = None,
  useCustomLayout: Boolean = false
) {

  def apply(render: RendererInput[P, C] => HtmlElement): HtmlElement = {
    val backButton = ButtonL(
      style = ButtonL.Style.Ghost(
        color = Color.Gray9,
        icon = Some(Icon.Glyph.ChevronLeft)
      ),
      onClick = Observer { _ =>
        currentStepDataObserver.onNext(None) // Clear Data of the current state
        onBack.onNext(())
      }
    )
    val nextButton = (input: RenderNextButtonInput) =>
      ButtonL(
        style = ButtonL.Style.Full(color = Color.Primary),
        onClick = Observer { _ =>
          onNext.onNext(input.nextStep)
        },
        isDisabled = input.isDisabled
      )

    val skipButton = (step: OnboardingStep) =>
      ButtonL(
        style = ButtonL.Style.Full(color = Color.Gray0),
        onClick = Observer { _ =>
          currentStepDataObserver.onNext(None)
          onNext.onNext(step)
        }
      )

    div(
      tw.hPc100,
      child <-- previousStepDataSignal.map(
        _.fold[HtmlElement](
          div(
            tw.flex.flexCol.justifyCenter.itemsCenter,
            div(
              tw.wPc50,
              div(tw.heading2, "Error happen, please go back to previous step and try again"),
              div(
                tw.mt24,
                backButton()
              )
            )
          )
        ) { previousStepData =>
          div(
            if (useCustomLayout) {
              tw.hPc100
            } else {
              List(
                marginLeft := "auto",
                marginRight := "auto",
                paddingTop := "64px",
                paddingBottom := "64px",
                paddingLeft := "24px",
                paddingRight := "24px",
                maxWidth := "1008px"
              )
            },
            render(
              RendererInput(
                previousStepData,
                currentStepDataObserver.contramap[C](data => Some(data)),
                backButton,
                skipButton,
                nextButton,
                initialState,
                Observer[OnboardingStep] { step =>
                  currentStepDataObserver.onNext(None)
                  onNext.onNext(step)
                }
              )
            )
          )
        }
      )
    )
  }

}

object WithGeneralStep {

  case class RendererInput[P, C](
    previousStepData: P,
    currentStepDataObserver: Observer[C],
    onBackButton: ButtonL,
    onSkipButton: OnboardingStep => ButtonL, // nextStep => ButtonL
    onNextButton: RenderNextButtonInput => ButtonL,
    initialState: Option[C],
    onSkipObserver: Observer[OnboardingStep]
  )

  case class RenderNextButtonInput(
    isDisabled: Signal[Boolean],
    nextStep: OnboardingStep
  )

}
