// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.onboarding.common

import com.raquo.airstream.core.Observer

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.collapse.Collapse
import design.anduin.components.collapse.laminar.CollapseL
import design.anduin.style.tw.*

import com.raquo.laminar.api.L.*

private[onboarding] final case class ViewMoreButton() {

  def apply(showLess: Node, showMore: Node, children: Node*): HtmlElement = {
    CollapseL(
      renderTarget = render => {
        // Show more/less button
        ButtonL(
          style = ButtonL.Style.Text(
            color = ButtonL.Color.Primary
          ),
          onClick = Observer(_ => render.onToggle.onNext(()))
        )(
          div(
            render.currentStatus match {
              case Collapse.Status.Open  => showLess
              case Collapse.Status.Close => showMore
            }
          )
        ).amend(tw.mb12)
      }
    )(children*)
  }

}
