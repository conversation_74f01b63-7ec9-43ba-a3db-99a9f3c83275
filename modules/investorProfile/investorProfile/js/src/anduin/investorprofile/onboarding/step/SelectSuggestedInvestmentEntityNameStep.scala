// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.onboarding.step

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.menu.laminar.VerticalDividerL
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.text.laminar.TruncateL
import design.anduin.style.tw.*

import anduin.investmententity.CreateInvestmentEntityParams
import anduin.investorprofile.common.LearnMorePopOver
import anduin.investorprofile.dashboard.{CreateNewEntityModal, LpLegalDisclaimer}
import anduin.investorprofile.onboarding.common.{RadioWrapper, ViewMoreButton}
import anduin.investorprofile.onboarding.step.WithGeneralStep.RenderNextButtonInput
import anduin.scalajs.pluralize.Pluralize

import com.raquo.laminar.api.L.*
import anduin.investorprofile.onboarding.OnboardingDataModel.*

private[onboarding] case class SelectSuggestedInvestmentEntityNameStep(
  dataObserver: Observer[SelectSuggestedInvestmentEntityNameStepData],
  renderBackButton: ButtonL,
  renderNextButton: RenderNextButtonInput => ButtonL,
  initialState: Option[SelectSuggestedInvestmentEntityNameStepData],
  suggestedInvestmentEntityNames: List[String],
  hasInvestmentEntitiesSignal: Signal[Boolean],
  onCreateInvestmentEntity: Observer[CreateInvestmentEntityParams],
  onLeaveOnboardingObserver: Observer[Unit]
) {

  private val MaxItems = 4

  private val selectedNameVar = Var[Option[SelectSuggestedInvestmentEntityNameStepData]](initialState)

  private def renderRadioButtonItem(ieName: String): HtmlElement = {
    RadioWrapper(
      isChecked = selectedNameVar.signal.map(_.contains(SelectSuggestedInvestmentEntityNameStepData(ieName))),
      onChange = Observer[Unit](_ => selectedNameVar.set(Some(SelectSuggestedInvestmentEntityNameStepData(ieName))))
    )(
      div(
        tw.flex.itemsCenter,
        div(
          tw.bgPrimary1.bgOpacity70,
          tw.flex.justifyCenter.itemsCenter,
          tw.textPrimary3,
          tw.borderAll.borderPrimary3.rounded4.border1,
          width := "28px",
          height := "28px",
          IconL(name = Val(Icon.Glyph.AlignLeft))()
        ),
        TruncateL(
          target = div(tw.flexFill.fontSemiBold.ml12, ieName)
        )()
      )
    ).amend(tw.mb12, maxWidth := "680px")
  }

  def apply(): HtmlElement = {
    div(
      selectedNameVar.signal --> Observer[Option[SelectSuggestedInvestmentEntityNameStepData]] {
        _.foreach(state => dataObserver.onNext(state))
      },
      div(tw.heading2, "Create an investment profile based on past subscriptions"),
      div(
        tw.mt12,
        div("Create an investment profile easily by converting your past subscription data."),
        div("Select a suggested profile name to continue. ", LearnMorePopOver("Learn more")())
      ),
      // Suggested IE List
      div(
        tw.mt24,
        suggestedInvestmentEntityNames.sorted.distinct.take(MaxItems).map(renderRadioButtonItem),
        Option.when(suggestedInvestmentEntityNames.size > MaxItems) {
          ViewMoreButton()(
            div("Show less"),
            div(
              s"Show ${suggestedInvestmentEntityNames.size - MaxItems}" +
                s" more ${Pluralize("suggestion", suggestedInvestmentEntityNames.size - MaxItems)}"
            ),
            suggestedInvestmentEntityNames.sorted.distinct
              .takeRight(suggestedInvestmentEntityNames.size - MaxItems)
              .map(renderRadioButtonItem)*
          )
        }
      ),
      // Terms of service
      LpLegalDisclaimer("Continue with suggestion")().amend(
        if (suggestedInvestmentEntityNames.size > MaxItems) tw.mt20 else tw.mt32
      ),
      // CTA
      div(
        tw.flex.itemsCenter.mt20,
        child.maybe <-- hasInvestmentEntitiesSignal.map(Option.when(_) {
          div(
            tw.flex.itemsCenter.mr4,
            renderBackButton().amend(tw.mr4),
            VerticalDividerL()
          )
        }),
        renderNextButton(RenderNextButtonInput(selectedNameVar.signal.map(_.isEmpty), SelectSubscriptions))(
          "Continue with suggestion"
        ),
        ModalL(
          isClosable = None,
          renderContent = close => {
            CreateNewEntityModal(
              onCancel = close,
              onCreateEntity = Observer[CreateInvestmentEntityParams] { params =>
                onCreateInvestmentEntity.onNext(params)
                onLeaveOnboardingObserver.onNext(())
              }
            )()
          },
          renderTarget = open => {
            ButtonL(style = ButtonL.Style.Text(), onClick = open.contramap(_ => ()))("Or create one manually")
              .amend(tw.ml16)
          },
          size = ModalL.Size(width = ModalL.Width.Px480),
          renderTitle = _ => "Create new investment profile"
        )()
      )
    )
  }

}
