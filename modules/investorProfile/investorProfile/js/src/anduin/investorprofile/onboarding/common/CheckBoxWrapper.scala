// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.onboarding.common

import com.raquo.laminar.api.L.*
import design.anduin.components.checkbox.CheckboxL
import design.anduin.style.tw.*
import org.scalajs.dom

private[onboarding] case class CheckBoxWrapper(
  isChecked: Signal[Boolean],
  onChange: Observer[Boolean],
  onToggle: Observer[Unit]
) {

  def apply(child: Node): HtmlElement = {
    div(
      tw.flex.itemsCenter.cursorPointer,
      tw.wPc100.py12.px16,
      tw.borderAll.rounded4,
      isChecked.not.cls(tw.bgGray0.borderGray3.hover(tw.bgGray1), tw.bgPrimary1.bgOpacity30.borderPrimary3),
      onClick --> Observer[dom.MouseEvent] { _ => onToggle.onNext(()) },
      CheckboxL(isChecked = isChecked, onChange = onChange)(),
      div(
        tw.flexFill.ml12,
        child
      )
    )
  }

}
