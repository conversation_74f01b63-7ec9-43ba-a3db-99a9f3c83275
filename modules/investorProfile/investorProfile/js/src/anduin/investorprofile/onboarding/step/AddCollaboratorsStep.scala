// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.onboarding.step

import design.anduin.components.avatar.InitialAvatar
import design.anduin.components.avatar.laminar.InitialAvatarL
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.menu.laminar.VerticalDividerL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.text.laminar.TruncateL
import design.anduin.style.tw.*
import org.scalajs.dom.MouseEvent

import anduin.fundsub.investmententity.subscription.InvestmentEntityFullSubscriptionInfo
import anduin.investorprofile.common.UserInfoAvatar
import anduin.investorprofile.onboarding.common.CheckBoxWrapper
import anduin.investorprofile.onboarding.step.WithGeneralStep.RenderNextButtonInput
import anduin.model.common.user.{UserId, UserInfo}
import anduin.utils.StringUtils
import com.anduin.stargazer.service.account.AccountUtils

import com.raquo.laminar.api.L.*
import anduin.investorprofile.onboarding.OnboardingDataModel.*

private[onboarding] case class AddCollaboratorsStep(
  previousStepState: SelectSubscriptionsStepData,
  dataObserver: Observer[AddCollaboratorsStepData],
  renderBackButton: ButtonL,
  renderSkipButton: OnboardingStep => ButtonL,
  renderNextButton: RenderNextButtonInput => ButtonL,
  listSubscriptions: List[InvestmentEntityFullSubscriptionInfo]
) {

  private val MaxItems = 1

  private val selectedUserIdsVar: Var[Set[UserId]] = Var(Set.empty[UserId])

  private val listCollaboratorsSignal =
    AccountUtils.currentUserIdObs.map(currentUserOpt =>
      listSubscriptions
        .filter(subscription => previousStepState.selectedSubscriptions.contains(subscription.lpId))
        .flatMap(subscription => subscription.collaborators :+ subscription.mainLp)
        .distinct
        .filterNot(userIdInfo => currentUserOpt.contains(userIdInfo.userId))
    )

  private def renderFundName(fundName: String) = {
    div(
      tw.flex.itemsCenter.mb4,
      InitialAvatarL(
        id = fundName,
        initials = Val(fundName),
        kind = InitialAvatar.Kind.Organization,
        size = InitialAvatar.Size.Px24
      )(),
      TruncateL(
        target = div(tw.fontSemiBold, fundName)
      )().amend(tw.ml8.flexFill)
    )
  }

  private def renderCheckBox(userId: UserId, userInfo: UserInfo) = {
    val listFundNames = listSubscriptions
      .filter(subscription =>
        subscription.mainLp.userId == userId || subscription.collaborators.map(_.userId).contains(userId)
      )
      .map(_.fundName)
      .distinct
      .sorted
    CheckBoxWrapper(
      isChecked = selectedUserIdsVar.signal.map(_.contains(userId)),
      onChange = Observer[Boolean](isChecked => {
        selectedUserIdsVar.update(oldState => if (!isChecked) oldState + userId else oldState - userId)
      }),
      onToggle = Observer[Unit](_ =>
        selectedUserIdsVar.update(oldState => if (oldState.contains(userId)) oldState - userId else oldState + userId)
      )
    )(
      div(
        tw.flex.itemsCenter,
        div(
          tw.flexFill.mr16,
          UserInfoAvatar(
            Some(
              UserInfo(
                emailAddressStr = userInfo.emailAddressStr,
                firstName = userInfo.firstName,
                lastName = userInfo.lastName
              )
            )
          )()
        ),
        div(
          tw.wPc40,
          listFundNames.take(MaxItems).map(renderFundName),
          Option.when(listFundNames.size > MaxItems) {
            PopoverL(
              position = PortalPosition.BottomLeft,
              renderContent = _ => {
                div(
                  tw.mt4,
                  tw.maxWPx256,
                  listFundNames
                    .drop(MaxItems)
                    .map(renderFundName)
                )
              },
              renderTarget = (open, _) => {
                ButtonL(
                  style = ButtonL.Style.Text(isBlock = true),
                  onClick = Observer[MouseEvent] { e =>
                    open.onNext(())
                    e.stopPropagation()
                  }
                )(s"+ ${StringUtils.pluralItem(listFundNames.size - MaxItems, "fund")}")
              }
            )()
          }
        )
      )
    ).amend(tw.mb8)
  }

  def apply(): HtmlElement = {
    div(
      selectedUserIdsVar.signal --> Observer[Set[UserId]] { selectedUserIds =>
        dataObserver.onNext(AddCollaboratorsStepData(selectedUserIds.toList))
      },
      div(tw.heading2, "Add collaborators to your investment profile"),
      div(
        tw.mt12,
        div("These are collaborators you previously worked with. If you need help managing your investment"),
        div("profile or plan on collaborating in future subscriptions, consider granting them access."),
        div("They'll have full access to the profile and its data.")
      ),
      // Check box
      div(
        tw.mt24,
        child <-- listCollaboratorsSignal.map { listCollaborators =>
          div(
            listCollaborators
              .sortBy(_.userInfo.fullNameString)
              .map(userIdInfo => renderCheckBox(userIdInfo.userId, userIdInfo.userInfo))
          )
        }
      ),
      // CTA
      div(
        tw.mt32.flex.itemsCenter,
        renderBackButton().amend(tw.mr4),
        VerticalDividerL(),
        renderSkipButton(CreateInvestmentEntity)("Skip").amend(tw.ml4.mr8),
        renderNextButton(RenderNextButtonInput(selectedUserIdsVar.signal.map(_.isEmpty), CreateInvestmentEntity))(
          "Add collaborators"
        )
      )
    )
  }

}
