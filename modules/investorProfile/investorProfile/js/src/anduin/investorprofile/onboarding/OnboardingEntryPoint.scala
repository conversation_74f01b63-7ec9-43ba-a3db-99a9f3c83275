// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.onboarding

import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*

import anduin.fundsub.investmententity.subscription.InvestmentEntityFullSubscriptionInfo
import anduin.investmententity.{CreateInvestmentEntityParams, InitProfileFromSubscription}
import anduin.investorprofile.onboarding.step.{
  AddCollaboratorsStep,
  PrefillProfileStep,
  SelectPrefillSubscriptionStep,
  SelectSubscriptionsStep,
  SelectSuggestedInvestmentEntityNameStep,
  WithGeneralStep
}

import com.raquo.laminar.api.L.*
import anduin.investorprofile.onboarding.OnboardingDataModel.*

// The subscriptions list is stateless.
private[investorprofile] case class OnboardingEntryPoint(
  notLinkedSubscriptionsInfo: List[InvestmentEntityFullSubscriptionInfo],
  existedInvestmentEntityNames: Seq[String],
  onLeaveOnboardingObserver: Observer[Unit],
  onCreateInvestmentEntity: Observer[CreateInvestmentEntityParams],
  hasInvestmentEntitiesSignal: Signal[Boolean]
) {

  private val currentOnboardingStepVar = Var[OnboardingStep](SelectSuggestedInvestmentEntityName)

  private val onboardingGlobalDataVar = Var[OnboardingGlobalData](OnboardingGlobalData())

  private val selectSuggestedInvestmentEntityNameStepDataObserver =
    Observer[Option[SelectSuggestedInvestmentEntityNameStepData]] { newDataOpt =>
      onboardingGlobalDataVar.update(_.copy(selectSuggestedInvestmentEntityNameStepData = newDataOpt))
    }

  private val selectSuggestedInvestmentEntityNameStepDataSignal =
    onboardingGlobalDataVar.signal.map(_.selectSuggestedInvestmentEntityNameStepData).distinct

  private val selectSubscriptionsStepDataObserver = Observer[Option[SelectSubscriptionsStepData]] { newDataOpt =>
    onboardingGlobalDataVar.update(_.copy(selectSubscriptionsStepData = newDataOpt))
  }

  private val selectSubscriptionsStepDataSignal =
    onboardingGlobalDataVar.signal.map(_.selectSubscriptionsStepData).distinct

  private val selectPrefillSubscriptionStepDataObserver = Observer[Option[SelectPrefillSubscriptionStepData]] {
    newDataOpt =>
      onboardingGlobalDataVar.update(_.copy(selectPrefillSubscriptionStepData = newDataOpt))
  }

  private val selectPrefillSubscriptionStepDataSignal =
    onboardingGlobalDataVar.signal.map(_.selectPrefillSubscriptionStepData).distinct

  private val prefillProfileStepDataObserver = Observer[Option[PrefillProfileStepData]] { newDataOpt =>
    onboardingGlobalDataVar.update(_.copy(prefillProfileStepData = newDataOpt))
  }

  private val addCollaboratorsStepDataObserver = Observer[Option[AddCollaboratorsStepData]] { newDataOpt =>
    onboardingGlobalDataVar.update(_.copy(addCollaboratorsStepData = newDataOpt))
  }

  def apply(): HtmlElement = {
    div(
      selectSubscriptionsStepDataSignal --> Observer.empty,
      selectSuggestedInvestmentEntityNameStepDataSignal --> Observer.empty,
      selectPrefillSubscriptionStepDataSignal --> Observer.empty,
      tw.hPc100,
      child <-- currentOnboardingStepVar.signal.distinct.map {
        case SelectSuggestedInvestmentEntityName =>
          WithGeneralStep[Unit, SelectSuggestedInvestmentEntityNameStepData](
            Val[Option[Unit]](Some(())),
            selectSuggestedInvestmentEntityNameStepDataObserver,
            Observer[Unit](_ => onLeaveOnboardingObserver.onNext(())),
            Observer[OnboardingStep](currentOnboardingStepVar.set),
            onboardingGlobalDataVar.now().selectSuggestedInvestmentEntityNameStepData
          )(rendererInput =>
            SelectSuggestedInvestmentEntityNameStep(
              rendererInput.currentStepDataObserver,
              rendererInput.onBackButton,
              rendererInput.onNextButton,
              rendererInput.initialState,
              notLinkedSubscriptionsInfo
                .map(_.subscriptionName)
                .distinct
                .filter(firmName =>
                  firmName.nonEmpty && !existedInvestmentEntityNames.contains(
                    firmName
                  )
                ),
              hasInvestmentEntitiesSignal,
              onCreateInvestmentEntity,
              onLeaveOnboardingObserver
            )()
          )
        case SelectSubscriptions =>
          WithGeneralStep[SelectSuggestedInvestmentEntityNameStepData, SelectSubscriptionsStepData](
            selectSuggestedInvestmentEntityNameStepDataSignal,
            selectSubscriptionsStepDataObserver,
            Observer[Unit](_ => currentOnboardingStepVar.set(SelectSuggestedInvestmentEntityName)),
            Observer[OnboardingStep](currentOnboardingStepVar.set),
            onboardingGlobalDataVar.now().selectSubscriptionsStepData
          )(rendererInput =>
            SelectSubscriptionsStep(
              rendererInput.previousStepData,
              rendererInput.currentStepDataObserver,
              rendererInput.onBackButton,
              rendererInput.onNextButton,
              rendererInput.initialState,
              notLinkedSubscriptionsInfo
            )()
          )
        case SelectPrefillSubscription =>
          WithGeneralStep[SelectSubscriptionsStepData, SelectPrefillSubscriptionStepData](
            selectSubscriptionsStepDataSignal,
            selectPrefillSubscriptionStepDataObserver,
            Observer[Unit](_ => currentOnboardingStepVar.set(SelectSubscriptions)),
            Observer[OnboardingStep](currentOnboardingStepVar.set),
            onboardingGlobalDataVar.now().selectPrefillSubscriptionStepData
          )(rendererInput =>
            SelectPrefillSubscriptionStep(
              rendererInput.previousStepData,
              rendererInput.currentStepDataObserver,
              rendererInput.onBackButton,
              rendererInput.onSkipButton,
              rendererInput.onNextButton,
              rendererInput.initialState,
              notLinkedSubscriptionsInfo,
              rendererInput.onSkipObserver
            )()
          )
        case PrefillProfile =>
          WithGeneralStep[SelectPrefillSubscriptionStepData, PrefillProfileStepData](
            selectPrefillSubscriptionStepDataSignal,
            prefillProfileStepDataObserver,
            Observer[Unit](_ => currentOnboardingStepVar.set(SelectPrefillSubscription)),
            Observer[OnboardingStep](currentOnboardingStepVar.set),
            onboardingGlobalDataVar.now().prefillProfileStepData,
            useCustomLayout = true
          )(rendererInput =>
            PrefillProfileStep(
              rendererInput.previousStepData,
              rendererInput.currentStepDataObserver,
              rendererInput.onBackButton,
              rendererInput.onSkipButton,
              rendererInput.onNextButton,
              notLinkedSubscriptionsInfo,
              rendererInput.initialState
            )()
          )
        case AddCollaborators =>
          WithGeneralStep[SelectSubscriptionsStepData, AddCollaboratorsStepData](
            selectSubscriptionsStepDataSignal,
            addCollaboratorsStepDataObserver,
            Observer[Unit] { _ =>
              val previousStep = if (onboardingGlobalDataVar.now().prefillProfileStepData.nonEmpty) {
                PrefillProfile
              } else if (onboardingGlobalDataVar.now().selectPrefillSubscriptionStepData.nonEmpty) {
                SelectPrefillSubscription
              } else { SelectSubscriptions }
              currentOnboardingStepVar.set(previousStep)
            },
            Observer[OnboardingStep](currentOnboardingStepVar.set),
            onboardingGlobalDataVar.now().addCollaboratorsStepData
          )(rendererInput =>
            AddCollaboratorsStep(
              rendererInput.previousStepData,
              rendererInput.currentStepDataObserver,
              rendererInput.onBackButton,
              rendererInput.onSkipButton,
              rendererInput.onNextButton,
              notLinkedSubscriptionsInfo
            )()
          )
        case CreateInvestmentEntity =>
          div(
            tw.hPc100.wPc100,
            BlockIndicatorL(isFullHeight = true)(),
            onMountCallback { _ =>
              onboardingGlobalDataVar
                .now()
                .selectSubscriptionsStepData
                .fold(
                  {
                    Toast.error("Can not create new Investment Profile")
                    onLeaveOnboardingObserver.onNext(())
                  }
                ) { selectSubscriptionsStepData =>
                  onCreateInvestmentEntity.onNext(
                    CreateInvestmentEntityParams(
                      selectSubscriptionsStepData.investmentEntityName,
                      selectSubscriptionsStepData.customIdOpt,
                      selectSubscriptionsStepData.selectedSubscriptions,
                      onboardingGlobalDataVar.now().addCollaboratorsStepData.map(_.collaborators).getOrElse(List.empty),
                      onboardingGlobalDataVar.now().prefillProfileStepData.map(_.templateComputedState),
                      onboardingGlobalDataVar
                        .now()
                        .selectPrefillSubscriptionStepData
                        .map(prefillSubscription =>
                          InitProfileFromSubscription(
                            prefillSubscription.selectedSubscription,
                            notLinkedSubscriptionsInfo
                              .find(_.lpId == prefillSubscription.selectedSubscription)
                              .map(_.fundName)
                              .getOrElse("")
                          )
                        )
                    )
                  )
                  onLeaveOnboardingObserver.onNext(())
                }
            }
          )
      }
    )
  }

}
