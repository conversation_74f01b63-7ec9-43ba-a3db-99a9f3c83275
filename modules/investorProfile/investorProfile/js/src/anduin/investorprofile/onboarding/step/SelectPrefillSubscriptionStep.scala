// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.onboarding.step

import design.anduin.components.avatar.InitialAvatar
import design.anduin.components.avatar.laminar.InitialAvatarL
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.button.laminar.ButtonL.Target
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.menu.laminar.VerticalDividerL
import design.anduin.components.progress.laminar.{BlockIndicatorL, RadialIndicatorL}
import design.anduin.components.text.laminar.TruncateL
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import org.scalajs.dom

import anduin.appnavigation.routing.CrossAppRouter
import anduin.frontend.AirStreamUtils
import anduin.fundsub.investmententity.subscription.InvestmentEntityFullSubscriptionInfo
import anduin.id.fundsub.FundSubLpId
import anduin.id.offering.OfferingId
import anduin.investorprofile.client.LpProfileEndpointClient
import anduin.investorprofile.onboarding.common.RadioWrapper
import anduin.investorprofile.onboarding.step.WithGeneralStep.RenderNextButtonInput
import anduin.utils.DateTimeUtils
import stargazer.component.routing.laminar.WithReactRouterL
import stargazer.model.routing.DynamicAuthPage.FundSubLpPageV2
import com.raquo.laminar.api.L.*

import anduin.investorprofile.InvestorProfileCommons.ComputeTemplateProfileSimilarityWithSubscriptionsParams
import anduin.investorprofile.onboarding.OnboardingDataModel.*

private[onboarding] case class SelectPrefillSubscriptionStep(
  previousStepState: SelectSubscriptionsStepData,
  dataObserver: Observer[SelectPrefillSubscriptionStepData],
  renderBackButton: ButtonL,
  renderSkipButton: OnboardingStep => ButtonL,
  renderNextButton: RenderNextButtonInput => ButtonL,
  initialState: Option[SelectPrefillSubscriptionStepData],
  listSubscriptions: List[InvestmentEntityFullSubscriptionInfo],
  onSkip: Observer[OnboardingStep]
) {
  private val selectedFundSubLpIdVar = Var[Option[FundSubLpId]](None)
  private val formSimilaritiesVar = Var[Option[Map[FundSubLpId, Double]]](None)
  private val computeSimilaritiesEventBus = new EventBus[Unit]()

  private val nextStepWhenSkipping =
    if (
      listSubscriptions
        .filter(subscription => previousStepState.selectedSubscriptions.contains(subscription.lpId))
        .flatMap(subscription => subscription.collaborators :+ subscription.mainLp)
        .distinct
        .size > 1
    ) {
      AddCollaborators
    } else {
      CreateInvestmentEntity
    }

  private def onComputeSimilarities() = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        resp <- LpProfileEndpointClient.computeTemplateProfileSimilarityWithSubscriptions(
          ComputeTemplateProfileSimilarityWithSubscriptionsParams(previousStepState.selectedSubscriptions)
        )
      } yield resp.fold(
        _ => Toast.error("Failed to calculate form similarities"),
        resp => {
          // Automatically skip this step when there is no matching with similarity > 0
          if (!resp.similarities.exists(_._2 * 100.toInt > 0)) {
            onSkip.onNext(nextStepWhenSkipping)
          } else {
            formSimilaritiesVar.set(Some(resp.similarities.filter(_._2 * 100.toInt > 0)))
          }
        }
      )
    }
  }

  private def renderRadioButtonItem(
    subscriptionInfo: InvestmentEntityFullSubscriptionInfo,
    similarityOpt: Option[Double]
  ) = {
    val fundName = subscriptionInfo.fundName
    val fundSubLpId = subscriptionInfo.lpId
    RadioWrapper(
      isChecked = selectedFundSubLpIdVar.signal.map(_.contains(subscriptionInfo.lpId)),
      onChange = Observer[Unit](_ => selectedFundSubLpIdVar.set(Some(subscriptionInfo.lpId)))
    )(
      div(
        tw.flex.itemsCenter.wPc100,
        // Left side
        div(
          tw.flex.itemsCenter.wPc40.pr32,
          InitialAvatarL(
            id = fundSubLpId.idString,
            initials = Val(fundName),
            kind = InitialAvatar.Kind.Organization,
            size = InitialAvatar.Size.Px24
          )(),
          TruncateL(
            lineClamp = Some(2),
            target = div(tw.fontSemiBold, fundName)
          )().amend(tw.ml8.flexFill)
        ),
        // Right side
        div(
          tw.flex.itemsCenter.wPc60,
          div(
            tw.flex.flexFill.itemsCenter,
            div(
              tw.flexFill,
              TruncateL(target = div(tw.textGray7.textSmall, "Entity name in form"))(),
              div(
                tw.textBody.fontSemiBold.wPc100,
                TruncateL(
                  target = div(subscriptionInfo.subscriptionName)
                )()
              )
            ),
            subscriptionInfo.lastActivityAt.fold[HtmlElement](div(tw.wPc30)) { time =>
              div(
                tw.wPc30.pl12,
                TruncateL(target = div(tw.textGray7.textSmall, "Last updated"))(),
                TruncateL(
                  target = div(
                    tw.textBody.fontSemiBold,
                    DateTimeUtils.formatInstant(time, DateTimeUtils.MonthDayFormatter)(
                      using DateTimeUtils.defaultTimezone
                    )
                  )
                )()
              )
            },
            similarityOpt.fold(div(tw.wPc30)) { similarity =>
              div(
                tw.wPc30.pl12,
                div(tw.textGray7.textSmall, "Similarity"),
                div(
                  tw.textBody.fontSemiBold,
                  tw.flex.itemsCenter.overflowHidden,
                  if (similarity < 1) {
                    div(
                      tw.textPrimary3.mr8,
                      RadialIndicatorL(percent = similarity)()
                    )
                  } else {
                    div(
                      tw.textSuccess5.mr8,
                      IconL(name = Val(Icon.Glyph.CheckCircle))()
                    )
                  },
                  div(s"${(similarity * 100).toInt}%")
                )
              )
            }
          ),
          WithReactRouterL { router =>
            val crossAppRedirectPage =
              CrossAppRouter.getRedirectPage(OfferingId.FundSub, FundSubLpPageV2(subscriptionInfo.lpId))
            TooltipL(
              renderContent = _.amend("Open subscription"),
              renderTarget = ButtonL(
                ButtonL.Style.Minimal(color = ButtonL.Color.Primary, icon = Some(Icon.Glyph.Eye)),
                tpe = ButtonL.Tpe.Link(router.urlFor(crossAppRedirectPage).value, Target.Blank)
              )().amend(
                selectedFundSubLpIdVar.signal
                  .map(!_.contains(subscriptionInfo.lpId))
                  .cls(tw.invisible.groupHover(tw.visible)),
                onClick --> Observer[dom.MouseEvent](e => {
                  e.stopPropagation()
                })
              )
            )()
          }
        )
      )
    ).amend(tw.group.mb8)
  }

  def apply(): HtmlElement = {
    div(
      computeSimilaritiesEventBus.events.flatMapSwitch(_ => onComputeSimilarities()) --> Observer.empty,
      formSimilaritiesVar.signal.map(_.isEmpty) --> Observer[Boolean] {
        if (_) computeSimilaritiesEventBus.writer.onNext(()) else ()
      },
      formSimilaritiesVar.signal
        .combineWith(selectedFundSubLpIdVar.signal) --> Observer[
        (Option[Map[FundSubLpId, Double]], Option[FundSubLpId])
      ] { case (formSimilaritiesOpt, selectedFundSubLpIdOpt) =>
        selectedFundSubLpIdOpt.foreach(lpId =>
          formSimilaritiesOpt.foreach(formSimilarities =>
            dataObserver.onNext(
              SelectPrefillSubscriptionStepData(
                lpId,
                previousStepState.selectedSubscriptions,
                formSimilarities
              )
            )
          )
        )
      },
      child <-- formSimilaritiesVar.signal.map {
        _.fold(
          BlockIndicatorL(Val(Option("Calculating similarities among subscriptions' forms")), isFullHeight = true)()
        ) { formSimilaritiesMap =>
          val formSimilarities = formSimilaritiesMap.toList
          div(
            // Header
            div(tw.heading2, "Save data in your investment profile"),
            div(
              tw.mt12,
              "Select a past subscription to automatically save its data in your investment profile"
            ),
            // List
            div(
              tw.mt12,
              listSubscriptions
                .filter(subscription => formSimilarities.map(_._1).contains(subscription.lpId))
                .sortBy(_.subscriptionName)
                .sortWith { (a, b) =>
                  formSimilaritiesMap.getOrElse(a.lpId, 0.toDouble) > formSimilaritiesMap.getOrElse(b.lpId, 0.toDouble)
                }
                .map(subscription => renderRadioButtonItem(subscription, formSimilaritiesMap.get(subscription.lpId)))
            ),
            // CTA
            div(
              tw.mt32.flex.itemsCenter,
              renderBackButton().amend(tw.mr4),
              VerticalDividerL(),
              renderSkipButton(nextStepWhenSkipping)("Skip").amend(tw.ml4.mr8),
              renderNextButton(
                RenderNextButtonInput(selectedFundSubLpIdVar.signal.map(_.isEmpty), PrefillProfile)
              )("Review data")
            )
          )
        }
      }
    )
  }

}
