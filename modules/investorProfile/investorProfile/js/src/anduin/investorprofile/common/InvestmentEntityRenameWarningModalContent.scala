// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.common

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL}
import design.anduin.components.progress.Skeleton
import design.anduin.components.progress.laminar.SkeletonL
import design.anduin.components.text.laminar.TruncateL
import design.anduin.style.tw.*
import org.scalajs.dom

import com.raquo.laminar.api.L.*

private[investorprofile] case class InvestmentEntityRenameWarningModalContent(
  investmentEntityNameSignal: Signal[String],
  onCancel: Observer[Unit],
  onUpdate: Observer[Unit]
) {

  private def renderHeader() = {
    div(
      tw.wPc100,
      tw.flex.itemsCenter,
      tw.borderBottom.borderGray3,
      height := "60px",
      div(
        tw.borderRight.borderGray3,
        tw.hPc100,
        width := "60px",
        tw.flex.justifyCenter.itemsCenter,
        ButtonL(
          style = ButtonL.Style.Full(color = ButtonL.Color.Gray0, icon = Option(Icon.Glyph.ViewGrid)),
          isDisabled = Val(true)
        )()
      ),
      SkeletonL(
        height = "32px",
        width = "32px",
        shape = Skeleton.Shape.Rounded
      )().amend(tw.ml16),
      SkeletonL(
        height = "16px",
        width = "60px",
        shape = Skeleton.Shape.Rounded
      )().amend(tw.ml4),
      div(
        tw.ml20.mr6,
        SkeletonL(
          height = "12px",
          width = "60px",
          shape = Skeleton.Shape.Rounded
        )(),
        SkeletonL(
          height = "16px",
          width = "100px",
          shape = Skeleton.Shape.Rounded
        )().amend(tw.mt4)
      ),
      div(
        tw.borderLeft.borderRight.borderGray3,
        tw.flex.itemsCenter.pl12.pr8.hPc100,
        width := "300px",
        div(
          tw.borderAll.borderWarning2.p8.rounded4,
          tw.textWarning4,
          IconL(name = Val(Icon.Glyph.UserInfo))()
        ),
        div(
          tw.flex.flexCol.ml12.mr16.flexFill,
          div(
            tw.textSmall,
            "INVESTMENT PROFILE"
          ),
          child <-- investmentEntityNameSignal.map { investmentEntityName =>
            TruncateL(
              target = div(
                tw.fontSemiBold.textBody,
                investmentEntityName
              )
            )()
          }
        ),
        div(IconL(name = Val(Icon.Glyph.CaretVertical))())
      )
    )
  }

  def apply(): HtmlElement = {
    div(
      tw.wPc100,
      ModalBodyL(
        div(
          div(
            "This investment profile was used in past subscriptions. Renaming it will also update the profile name in the headers of those past subscriptions."
          ),
          div(
            tw.mt20,
            span(tw.fontSemiBold, "Note:"),
            " The content of your subscription forms won't be affected"
          ),
          div(
            tw.wPc100,
            tw.mt24,
            tw.p24,
            tw.bgGray2.rounded6,
            tw.overflowYHidden,
            div(
              tw.bgGray0,
              tw.wPc100,
              tw.rounded4,
              renderHeader(),
              div(
                tw.wPc100,
                paddingTop := "30px",
                paddingLeft := "60px",
                div(
                  SkeletonL(
                    height = "16px",
                    width = "140px",
                    shape = Skeleton.Shape.Rounded
                  )()
                ),
                div(
                  tw.borderAll.borderGray3.rounded4,
                  tw.mt12.py16.px24,
                  tw.flex.itemsEnd,
                  div(
                    tw.flexFill,
                    SkeletonL(
                      height = "12px",
                      width = "80px",
                      shape = Skeleton.Shape.Rounded
                    )(),
                    SkeletonL(
                      height = "16px",
                      width = "120px",
                      shape = Skeleton.Shape.Rounded
                    )().amend(tw.mt4)
                  ),
                  div(
                    tw.ml12,
                    SkeletonL(
                      height = "16px",
                      width = "160px",
                      shape = Skeleton.Shape.Rounded
                    )()
                  )
                )
              )
            )
          )
        )
      ),
      ModalFooterWCancelL(
        cancel = onCancel
      )(
        ButtonL(
          style = ButtonL.Style.Full(color = ButtonL.Color.Warning),
          onClick = Observer[dom.MouseEvent] { _ =>
            onUpdate.onNext(())
          }
        )("Rename profile")
      )
    )
  }

}
