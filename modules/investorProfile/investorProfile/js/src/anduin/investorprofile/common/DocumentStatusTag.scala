package anduin.investorprofile.common

import anduin.scalajs.pluralize.Pluralize
import com.raquo.laminar.api.L.*
import design.anduin.components.tag.Tag
import design.anduin.components.tag.laminar.TagL
import design.anduin.components.tooltip.laminar.TooltipL

import java.time.LocalDate
import java.time.format.DateTimeFormatter

case class DocumentStatusTag(expirationDate: LocalDate) {
  private val dateTimeFormatter = DateTimeFormatter.ofPattern("LLL dd, YYYY")

  def apply(): HtmlElement = {
    val today = LocalDate.now()
    val diff = expirationDate.toEpochDay - today.toEpochDay
    val dateDisplay = expirationDate.format(dateTimeFormatter)
    val target = if (diff <= 0) {
      TagL(
        label = Val("Expired"),
        color = Val(Tag.Light.Danger)
      )()
    } else if (diff <= 14) {
      TagL(
        label = Val(s"Expiring in ${diff} ${Pluralize("day", diff.toInt)}"),
        color = Val(Tag.Light.Warning)
      )()
    } else {
      div(s"Valid until $dateDisplay")
    }
    val tooltipContent = if (diff < 0) {
      s"Expired on: $dateDisplay"
    } else if (diff == 0) {
      s"Expiring today: $dateDisplay"
    } else {
      s"Expiring on: $dateDisplay"
    }
    TooltipL(
      renderTarget = target,
      renderContent = _.amend(tooltipContent)
    )()
  }

}
