// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.common

import design.anduin.components.portal.PortalPosition
import design.anduin.components.tag.laminar.TagL
import design.anduin.components.tag.{Tag, TagColor}
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*

import anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus

import com.raquo.laminar.api.L.*

private[investorprofile] final case class LpStatusRenderer(status: LpStatus) {

  def apply(): HtmlElement = {
    val label = LpStatusRenderer.getStatusName(status)
    val tooltip = LpStatusRenderer.lpStatusTooltip.getOrElse(status, "")
    val tagColor = LpStatusRenderer.lpStatusTagColor.getOrElse(status, Tag.Light.Gray)

    div(
      tw.wFit,
      TooltipL(
        position = PortalPosition.TopCenter,
        renderTarget = TagL(color = Val(tagColor), label = Val(label))(),
        renderContent = _.amend(tooltip)
      )()
    )
  }

}

object LpStatusRenderer {

  private val lpStatusName: Map[LpStatus, String] = Map(
    LpStatus.LPNotStarted -> "Invited",
    LpStatus.LPInProgress -> "Form in progress",
    LpStatus.LPChangeInProgress -> "Changes in progress",
    LpStatus.LPPendingUnsignedReview -> "Pending review",
    LpStatus.LPFilledForm -> "Form filled",
    LpStatus.LPRequestedSignature -> "Pending signature",
    LpStatus.LPSignedForm -> "Form signed",
    LpStatus.LPPendingSubmission -> "Pending submission",
    LpStatus.LPPendingReview -> "Pending approval",
    LpStatus.LPFormReviewed -> "Form reviewed",
    LpStatus.LPSubmitted -> "Submitted",
    LpStatus.LPCountersigned -> "Countersigned",
    LpStatus.LPCompleted -> "Complete",
    LpStatus.LPRemoved -> "Removed"
  )

  def getStatusName(status: LpStatus): String = {
    lpStatusName.getOrElse(status, "")
  }

  private val lpStatusTooltip: Map[LpStatus, String] = Map(
    LpStatus.LPNotStarted -> "Waiting for investor to accept invitation",
    LpStatus.LPInProgress -> "Waiting for investor to complete form",
    LpStatus.LPChangeInProgress -> "Waiting for investor to make changes to form",
    LpStatus.LPPendingUnsignedReview -> "Unsigned subscription documents awaiting review",
    LpStatus.LPFilledForm -> "Waiting for investor to sign form",
    LpStatus.LPRequestedSignature -> "Investor is waiting for signature",
    LpStatus.LPSignedForm -> "Waiting for investor to upload additional documents. You can view the signed documents.",
    LpStatus.LPPendingSubmission -> "Waiting for investor to submit signature package",
    LpStatus.LPPendingReview -> "Signed subscription documents awaiting review",
    LpStatus.LPFormReviewed -> "Waiting for investor to complete the reviewed form",
    LpStatus.LPSubmitted -> "Signature package submitted by investor",
    LpStatus.LPCountersigned -> "Countersigned documents awaiting distribution",
    LpStatus.LPCompleted -> "Countersigned documents shared with investor",
    LpStatus.LPRemoved -> "Investor has been removed"
  )

  private val lpStatusTagColor: Map[LpStatus, TagColor] = Map(
    LpStatus.LPNotStarted -> Tag.Light.Gray,
    LpStatus.LPInProgress -> Tag.Light.Primary,
    LpStatus.LPChangeInProgress -> Tag.Light.Warning,
    LpStatus.LPPendingUnsignedReview -> Tag.Light.Warning,
    LpStatus.LPFilledForm -> Tag.Light.Primary,
    LpStatus.LPRequestedSignature -> Tag.Light.Warning,
    LpStatus.LPSignedForm -> Tag.Light.Primary,
    LpStatus.LPPendingSubmission -> Tag.Light.Warning,
    LpStatus.LPPendingReview -> Tag.Light.Warning,
    LpStatus.LPFormReviewed -> Tag.Light.Primary,
    LpStatus.LPSubmitted -> Tag.Light.Success,
    LpStatus.LPCountersigned -> Tag.Light.Success,
    LpStatus.LPCompleted -> Tag.Bold.Success,
    LpStatus.LPRemoved -> Tag.Bold.Success
  )

}
