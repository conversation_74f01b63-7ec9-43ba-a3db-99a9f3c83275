// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.common

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.laminar.PortalWrapperL
import design.anduin.style.tw.*

import com.raquo.laminar.api.L.*

private[investorprofile] case class LearnMorePopOver(renderText: String) {

  def apply(): Node = {
    PopoverL(
      renderContent = _ =>
        div(
          tw.wPx480.p16,
          div(tw.heading4.textGray7, "Investment Profile"),
          div(
            tw.mt16,
            "The ",
            span(tw.fontSemiBold, "Investment Profile"),
            " app is a central place to manage all investment vehicles (investment profiles) you've created or have access to."
          ),
          div(
            tw.mt24,
            "You can use these profiles to subscribe to new funds and keep track of your subscriptions."
          ),
          div(
            tw.mt24,
            "Each investment profile has a form where you can save your subscription data and re-use it to autofill new subscriptions."
          )
        ),
      renderTarget = (open, _) => {
        ButtonL(style = ButtonL.Style.Text(), onClick = open.contramap(_ => ()))(
          span(tw.textPrimary5.hover(tw.textPrimary2), u(renderText))
        )
      },
      targetWrapper = PortalWrapperL.Inline
    )()
  }

}
