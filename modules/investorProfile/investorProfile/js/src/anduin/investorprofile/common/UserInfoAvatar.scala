// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.common

import design.anduin.components.avatar.laminar.AvatarLabelL
import design.anduin.style.tw.*
import anduin.model.common.user.UserInfo

import com.raquo.laminar.api.L.*

private[investorprofile] case class UserInfoAvatar(userInfoOpt: Option[UserInfo]) {

  def apply(): Node = {
    userInfoOpt.fold[Node](emptyNode)(userInfo =>
      AvatarLabelL(
        emailAddress = userInfo.emailAddressStr,
        fullName = Val(userInfo.fullNameString),
        renderCopyIndicator = _.amend(tw.invisible.groupHover(tw.visible))
      )()
    )
  }

}
