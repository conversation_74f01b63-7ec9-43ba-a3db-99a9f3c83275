// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request.funddatadocument

import anduin.id.taskrequest.TaskRequestItemId
import anduin.investmententity.request.UpdateDocumentRequestItemParams
import anduin.investorprofile.client.LpProfileRequestEndpointClient
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.utils.ZIOUtils
import com.raquo.airstream.core.Observer
import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import zio.ZIO

private[request] case class EditNoteButton(
  taskRequestItemId: TaskRequestItemId,
  currentNote: String,
  renderButton: Observer[Unit] => HtmlElement,
  onDone: Observer[Observer[Unit]]
) {

  private val isSavingVar = Var[Boolean](false)
  private val noteVar = Var[String](currentNote)

  private def onEditNote(closePopover: Observer[Unit]) = {
    val task = for {
      _ <- ZIO.attempt(isSavingVar.set(true))
      _ <- LpProfileRequestEndpointClient
        .updateDocumentRequestItemSubmission(
          UpdateDocumentRequestItemParams(
            requestItemId = taskRequestItemId,
            updatedFileIdOpt = None,
            updatedAdditionalNote = Some(noteVar.now()),
            updatedExpiredAt = None
          )
        )
        .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        .map(
          _.fold[Unit](
            _ => {
              Toast.error("Failed to save note, please try again")
              isSavingVar.set(false)
            },
            _ => {
              Toast.success("Note edited")
              onDone.onNext(closePopover)
            }
          )
        )
    } yield ()

    ZIOUtils.runAsync(task)
  }

  def apply(): Node = {
    PopoverL(
      renderContent = closePopover =>
        div(
          width.px(300),
          tw.m4,
          TextBoxL(
            placeholder = "Maximum 250 characters",
            value = noteVar.signal,
            onChange = Observer[String] { description =>
              noteVar.set(description.take(250))
            },
            tpe = TextBoxL.Tpe.Area(4)
          )(),
          div(
            tw.mt8,
            tw.flex.justifyEnd,
            ButtonL(
              style = ButtonL.Style.Full(height = ButtonL.Height.Fix24),
              onClick = closePopover.contramap(_ => ()),
              isDisabled = isSavingVar.signal
            )("Cancel").amend(tw.mr8),
            ButtonL(
              style = ButtonL.Style.Full(
                color = ButtonL.Color.Primary,
                height = ButtonL.Height.Fix24,
                isBusy = isSavingVar.signal
              ),
              isDisabled = noteVar.signal.map(_ == currentNote),
              onClick = Observer { _ => onEditNote(closePopover) }
            )("Save")
          )
        ),
      renderTarget = (open, _) => renderButton(open),
      position = PortalPosition.BottomCenter
    )()
  }

}
