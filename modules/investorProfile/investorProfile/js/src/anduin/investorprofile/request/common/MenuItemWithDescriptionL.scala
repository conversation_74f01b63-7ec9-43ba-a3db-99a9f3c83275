// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request.common

import com.raquo.laminar.api.L.*
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.style.tw.*

private[request] final case class MenuItemWithDescriptionL(
  iconName: Icon.Name,
  title: String,
  description: String,
  isDisabledSignal: Signal[Boolean],
  onMenuItemClick: Observer[Unit]
) {

  def apply(children: Node*): Node = {

    div(
      child <-- isDisabledSignal.map { isDisabled =>
        val textColorStyle: HtmlMod = if (isDisabled) tw.textGray4 else tw.textGray7
        val cursorStyle: HtmlMod = if (isDisabled) tw.cursorNotAllowed else tw.cursorPointer
        val titleStyle: HtmlMod = if (isDisabled) tw.textGray4 else tw.textGray8
        div(
          tw.flex.p12,
          cursorStyle,
          tw.rounded4.hover(tw.bgGray3),
          onClick.map(_ => ()).filter(_ => !isDisabled) --> onMenuItemClick,
          div(
            tw.hPx20.flex.itemsCenter,
            textColorStyle,
            IconL(name = Val(iconName), size = Icon.Size.Custom(14))()
          ),
          div(
            tw.leading20.ml12,
            div(
              tw.fontSemiBold,
              titleStyle,
              title
            ),
            div(
              textColorStyle,
              description
            )
          ),
          children
        )
      }
    )
  }

}
