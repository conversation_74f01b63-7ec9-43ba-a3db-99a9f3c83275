// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request.collaborator

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.checkbox.CheckboxL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterL}
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import japgolly.scalajs.react.extra.router.RouterCtl
import org.scalajs.dom
import zio.ZIO

import anduin.id.taskrequest.TaskRequestId
import anduin.investmententity.request.RemoveCollaboratorParams
import anduin.investorprofile.client.LpProfileRequestEndpointClient
import anduin.model.common.user.UserId
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.utils.ZIOUtils
import stargazer.component.routing.laminar.WithReactRouterL
import stargazer.model.routing.DynamicAuthPage.LpProfile
import stargazer.model.routing.Page

private[request] case class RemoveCollaboratorModalContent(
  taskRequestId: TaskRequestId,
  userId: UserId,
  userName: String,
  isSelfRemoving: Boolean,
  onCloseModal: Observer[Unit],
  onDone: Observer[Unit]
) {

  private val isRemoving = Var[Boolean](false)
  private val notifyUserVar = Var[Boolean](true)

  private def onRemoveCollaborator(router: RouterCtl[Page]): Unit = {
    val task = for {
      _ <- ZIO.attempt(isRemoving.set(true))
      _ <- LpProfileRequestEndpointClient
        .removeCollaborator(RemoveCollaboratorParams(taskRequestId, userId, notifyUserVar.now()))
        .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        .map(
          _.fold[Unit](
            _ => {
              Toast.error("Failed to remove collaborator, please try again")
              isRemoving.set(false)
            },
            _ => {
              Toast.success("Collaborator removed successfully")
              if (isSelfRemoving) {
                router.set(LpProfile.LpProfileRequestPage()).runNow()
              } else {
                onCloseModal.onNext(())
                onDone.onNext(())
              }
            }
          )
        )
    } yield ()

    ZIOUtils.runAsync(task)
  }

  def apply(): HtmlElement = {
    div(
      ModalBodyL(
        if (isSelfRemoving) {
          div("Are you sure you want to leave? You'll no longer be able to access this document request.")
        } else {
          div(
            tw.mb16,
            "Are you sure you want to remove ",
            span(tw.fontBold, userName),
            "? They'll no longer be able to access this document request."
          )
        },
        Option
          .unless(isSelfRemoving) {
            CheckboxL(
              isChecked = notifyUserVar.signal,
              onChange = notifyUserVar.writer
            )("Notify recipients by email")
          }
          .getOrElse(emptyNode)
      ),
      ModalFooterL(
        WithReactRouterL { router =>
          div(
            tw.flex.justifyEnd,
            ButtonL(onClick = onCloseModal.contramap(_ => ()), isDisabled = isRemoving.signal)("Cancel")
              .amend(tw.mr8),
            ButtonL(
              style = ButtonL.Style.Full(color = ButtonL.Color.Danger, isBusy = isRemoving.signal),
              onClick = Observer[dom.MouseEvent] { _ =>
                onRemoveCollaborator(router)
              }
            )(if (isSelfRemoving) "Leave" else "Remove")
          )
        }
      )
    )
  }

}
