// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request.collaborator

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL}
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import org.scalajs.dom
import zio.ZIO

import anduin.id.taskrequest.TaskRequestId
import anduin.investmententity.request.SendReminderToCollaboratorParams
import anduin.investorprofile.client.LpProfileRequestEndpointClient
import anduin.model.common.user.UserId
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.utils.ZIOUtils

private[request] case class SendReminderModalContent(
  taskRequestId: TaskRequestId,
  userId: UserId,
  userName: String,
  userEmail: String,
  onCloseModal: Observer[Unit]
) {
  private val isSendingReminder = Var[Boolean](false)

  private def onSendReminder(): Unit = {
    val task = for {
      _ <- ZIO.attempt(isSendingReminder.set(true))
      _ <- LpProfileRequestEndpointClient
        .sendReminderToCollaborator(SendReminderToCollaboratorParams(taskRequestId, userId))
        .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        .map(
          _.fold[Unit](
            _ => {
              Toast.error("Failed to send reminder, please try again")
              isSendingReminder.set(false)
            },
            _ => {
              Toast.success("Reminder sent successfully")
              onCloseModal.onNext(())
            }
          )
        )
    } yield ()

    ZIOUtils.runAsync(task)
  }

  def apply(): HtmlElement = {
    div(
      ModalBodyL(
        div(
          tw.mb16,
          "We will send a reminder email to ",
          span(tw.fontBold, userName),
          s" ($userEmail)"
        )
      ),
      ModalFooterWCancelL(cancel = onCloseModal, isCancelDisabled = isSendingReminder.signal)(
        ButtonL(
          style = ButtonL.Style.Full(color = ButtonL.Color.Primary, isBusy = isSendingReminder.signal),
          onClick = Observer[dom.MouseEvent] { _ =>
            onSendReminder()
          }
        )("Send reminder")
      )
    )
  }

}
