// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request.common

import com.raquo.laminar.api.L.*
import design.anduin.components.tag.TagColor
import design.anduin.components.tag.laminar.TagL
import design.anduin.style.tw.*

import anduin.investmententity.request.LpProfileRequestStatus
import anduin.investorprofile.request.common.RequestUtils.instantToStr

private[request] case class LpProfileRequestStatusTag(status: LpProfileRequestStatus, renderFullInfo: Boolean = false) {

  def apply(): HtmlElement = {
    div(
      tw.flex.itemsCenter,
      div(status match {
        case status: LpProfileRequestStatus.RequestViewed =>
          TagL(
            label = Val(status.name),
            color = Val(TagColor.Light.Gray)
          )()
        case status: LpProfileRequestStatus.RequestInProgress =>
          TagL(
            label = Val(if (status.numItems == 0) {
              status.name
            } else { s"${status.numProcessedItems} / ${status.numItems} upload" }),
            color = Val(TagColor.Light.Primary)
          )()
        case status: LpProfileRequestStatus.RequestCanceled =>
          TagL(
            label = Val(status.name),
            color = Val(TagColor.Bold.Gray)
          )()
        case status: LpProfileRequestStatus.RequestCompleted =>
          TagL(
            label = Val(status.name),
            color = Val(TagColor.Light.Success)
          )()
        case _ => div()
      }),
      Option.when(renderFullInfo) {
        div(
          tw.ml8.textSmall.textGray7,
          status match {
            case status: LpProfileRequestStatus.RequestCanceled =>
              s"on ${instantToStr(status.canceledAt)}"
            case status: LpProfileRequestStatus.RequestCompleted =>
              s"on ${instantToStr(status.completedAt)}"
            case _ => ""
          }
        )
      }
    )
  }

}
