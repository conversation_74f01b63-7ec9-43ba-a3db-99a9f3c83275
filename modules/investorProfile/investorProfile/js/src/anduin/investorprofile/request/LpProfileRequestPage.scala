// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request

import com.raquo.laminar.api.L.*
import design.anduin.style.tw.*

private[request] case class LpProfileRequestPage(
  pageParamsSignal: Signal[LpProfileRequestPageParams],
  onSetPageParams: Observer[LpProfileRequestPageParams]
) {

  def apply(): HtmlElement = {
    div(
      tw.wPc100.hPc100,
      div(
        tw.wPc100.hPc100,
        child <-- pageParamsSignal.map(_.selectedRequest).distinct.map {
          _.fold(
            LpProfileRequestDashboardSection(pageParamsSignal, onSetPageParams)()
          ) { selectedRequest =>
            LpProfileRequestDetailSection(
              selectedRequest,
              pageParamsSignal,
              onSetPageParams
            )()
          }
        }
      )
    )
  }

}
