// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request.funddatadocument

import anduin.id.taskrequest.TaskRequestItemId
import anduin.investmententity.request.LpProfileRequestItemSubmittedData.FundDataDocumentRequestItemSubmittedData
import anduin.investmententity.request.RemoveRequestItemParams
import anduin.investorprofile.client.LpProfileRequestEndpointClient
import anduin.investorprofile.request.common.RequestUtils.instantToStr
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.utils.ZIOUtils
import com.raquo.laminar.api.L.*
import design.anduin.components.icon.Icon
import design.anduin.components.progress.CircleIndicator
import design.anduin.components.progress.laminar.CircleIndicatorL
import design.anduin.components.tag.laminar.TagL
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.SingleTooltipL
import design.anduin.style.tw.*
import zio.ZIO

private[request] case class NotApplicableTag(
  taskRequestItemId: TaskRequestItemId,
  submission: FundDataDocumentRequestItemSubmittedData,
  isDisabledSignal: Signal[Boolean],
  onDone: Observer[Unit]
) {

  private val isRemovingTag = Var[Boolean](false)

  private def onRemoveTag(): Unit = {
    val task = for {
      _ <- ZIO.attempt(isRemovingTag.set(true))
      _ <- LpProfileRequestEndpointClient
        .removeRequestItemSubmission(RemoveRequestItemParams(taskRequestItemId))
        .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        .map(
          _.fold[Unit](
            _ => {
              Toast.error("Failed to remove not applicable tag")
              isRemovingTag.set(false)
            },
            _ => {
              Toast.success("Document marked as applicable")
              onDone.onNext(())
            }
          )
        )
    } yield ()

    ZIOUtils.runAsync(task)
  }

  def apply(): HtmlElement = {
    SingleTooltipL(
      renderContent = _.amend(
        s"By ${submission.lastUpdatedBy.userInfo.fullNameString} on ${instantToStr(submission.lastUpdatedAt)}"
      ),
      renderTarget = {
        TagL(
          icon = Option(Icon.Glyph.NoAccess),
          label = Val("Not applicable"),
          isDisabled = isRemovingTag.signal,
          renderButton = button => {
            div(
              child.maybe <-- isRemovingTag.signal.combineWith(isDisabledSignal).map { (isRemovingTag, isDisabled) =>
                Option.when(!isDisabled) {
                  if (isRemovingTag) {
                    div(
                      tw.bgGray3,
                      tw.flexNone.wPx20.hPx20,
                      tw.flex.itemsCenter.justifyCenter,
                      tw.rounded2.roundedL0,
                      CircleIndicatorL(size = CircleIndicator.Size.Custom(14))()
                    )
                  } else {
                    SingleTooltipL(
                      renderContent = _.amend("Unmark as not applicable"),
                      renderTarget = button
                    )()
                  }
                }
              }
            )
          },
          onClose = Some(Observer[Unit] { _ =>
            onRemoveTag()
          })
        )()
      }
    )()
  }

}
