// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request.collaborator

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterL}
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import io.github.arainko.ducktape.*
import org.scalajs.dom
import zio.ZIO

import anduin.id.taskrequest.TaskRequestId
import anduin.investmententity.request.{InviteRequestCollaboratorsParams, RequestCollaboratorInfo}
import anduin.investorprofile.client.LpProfileRequestEndpointClient
import anduin.investorprofile.request.WithRequestDetail
import anduin.investorprofile.request.collaborator.InviteUserMultipleInput.InvitingUserData
import anduin.scalajs.pluralize.Pluralize
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.utils.ZIOUtils

private[request] case class InviteCollaboratorModalContent(
  taskRequestId: TaskRequestId,
  onCancel: Observer[Unit],
  onRefetch: Observer[Boolean],
  onDone: Observer[Unit]
) {

  private val invitingUsersVar =
    Var[List[InvitingUserData]](List(InvitingUserData()))

  private val isInvitingUsersVar = Var[Boolean](false)

  private def handleUpdateInvitingUser(
    id: String,
    updateFunc: InvitingUserData => InvitingUserData
  ): Unit = {
    invitingUsersVar.update(_.map(oldValue => if (oldValue.id == id) updateFunc(oldValue).resetValidation else oldValue))
  }

  private def handleAddInvitingUser(user: InvitingUserData): Unit = {
    invitingUsersVar.update(_ :+ user)
  }

  private def handleRemoveInvitingUser(id: String): Unit = {
    invitingUsersVar.update(_.filterNot(_.id == id))
  }

  private def validateInvitingUser(
    user: InvitingUserData,
    allInvitingUsers: List[InvitingUserData],
    existedEmailAddresses: List[String]
  ): InvitingUserData = {
    user.resetValidation.validateFirstName.validateLastName.validateEmailAddress(
      allInvitingUsers.filterNot(_.id == user.id).map(_.email),
      existedEmailAddresses
    )
  }

  private def inviteCollaborators(allInvitingUsers: List[InvitingUserData], existedEmailAddressed: Seq[String]) = {
    ZIOUtils.runAsync {
      for {
        validatedInvitingUserData <- ZIO.attempt(
          allInvitingUsers.map(
            validateInvitingUser(
              _,
              allInvitingUsers,
              existedEmailAddressed.toList
            )
          )
        )
        isValid = validatedInvitingUserData.forall(_.isValid)
        _ <-
          if (isValid) {
            for {
              _ <- ZIO.attempt(isInvitingUsersVar.set(true))
              _ <- LpProfileRequestEndpointClient
                .inviteCollaborators(
                  InviteRequestCollaboratorsParams(
                    taskRequestId = taskRequestId,
                    requestCollaboratorInfos = allInvitingUsers.map(_.into[RequestCollaboratorInfo].transform())
                  )
                )
                .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
                .map {
                  _.fold(
                    _ => {
                      Toast.error(s"Failed to send invitation")
                      isInvitingUsersVar.set(false)
                    },
                    _ => {
                      Toast.success(s"${Pluralize(
                          "Invitation",
                          allInvitingUsers.size
                        )} sent successfully")
                      onDone.onNext(())
                    }
                  )
                }
            } yield ()
          } else {
            ZIO.attempt(
              invitingUsersVar.set(validatedInvitingUserData)
            )
          }
      } yield ()
    }
  }

  def apply(): HtmlElement = {
    WithRequestDetail(
      taskRequestId,
      render = requestDetailProps =>
        div(
          child <-- requestDetailProps.requestDetailSignal
            .map(_.map(detail => (detail.collaborators ++ detail.recipients).distinct))
            .map {
              _.fold(BlockIndicatorL()().amend(tw.hPx256)) { existedUsers =>
                val existedEmailAddresses = existedUsers.map(_.userInfo.emailAddressStr)
                div(
                  ModalBodyL(
                    div(
                      tw.wPc100,
                      InviteUserMultipleInput(
                        invitingUsersSignal = invitingUsersVar.signal,
                        onUpdateInvitingUser = Observer[InviteUserMultipleInput.UpdateInvitingUserInput](input =>
                          handleUpdateInvitingUser(input.id, input.updateFunc)
                        ),
                        onAddInvitingUser = Observer[InvitingUserData](handleAddInvitingUser),
                        onRemoveInvitingUser = Observer[String](handleRemoveInvitingUser)
                      )()
                    )
                  ),
                  ModalFooterL(
                    div(
                      tw.flex.justifyEnd,
                      div(
                        tw.mr8,
                        ButtonL(
                          isDisabled = isInvitingUsersVar.signal,
                          onClick = Observer[dom.MouseEvent] { _ =>
                            onCancel.onNext(())
                            onRefetch.onNext(false)
                          }
                        )("Cancel")
                      ),
                      ButtonL(
                        style = ButtonL.Style.Full(color = ButtonL.Color.Primary, isBusy = isInvitingUsersVar.signal),
                        onClick = Observer[dom.MouseEvent] { _ =>
                          inviteCollaborators(invitingUsersVar.now(), existedEmailAddresses)
                        },
                        isDisabled = invitingUsersVar.signal.map(_.exists(!_.isAllNonEmpty))
                      )("Invite")
                    )
                  )
                )
              }
            }
        )
    )()
  }

}
