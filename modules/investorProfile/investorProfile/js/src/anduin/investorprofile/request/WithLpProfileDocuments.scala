// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request

import anduin.investorprofile.InvestorProfileDocumentCommons.{GetAllProfileDocumentsParams, ProfileDocumentInfo}
import anduin.investorprofile.client.InvestmentEntityEndpointClient
import anduin.investorprofile.request.WithLpProfileDocuments.DocumentData
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.utils.ZIOUtils
import com.raquo.laminar.api.L.*
import design.anduin.components.toast.Toast
import zio.ZIO
import anduin.id.fundsub.FundSubId

private[request] final case class WithLpProfileDocuments(
  demoFundSubId: Option[FundSubId] = None,
  render: WithLpProfileDocuments.RenderProps => HtmlElement
) {

  private val isGettingVar = Var[Boolean](true)
  private val documentsDataVar = Var[DocumentData](DocumentData())
  private val getDocumentsTriggerGettingSignalEventBus = new EventBus[Boolean]

  def apply(): HtmlElement = {
    render(
      WithLpProfileDocuments.RenderProps(
        isGettingVar.signal.distinct,
        documentsDataVar.signal.distinct,
        getDocumentsTriggerGettingSignalEventBus.writer
      )
    ).amend(
      getDocuments(true) --> Observer.empty,
      getDocumentsTriggerGettingSignalEventBus.events
        .flatMapSwitch { setGetting =>
          getDocuments(setGetting)
        } --> Observer.empty
    )
  }

  private def getDocuments(
    triggerGetting: Boolean
  ): EventStream[Unit] = {
    val task = for {
      _ <- ZIO.when(triggerGetting)(ZIO.attempt(isGettingVar.set(true)))
      _ <- InvestmentEntityEndpointClient
        .getAllProfileDocuments(GetAllProfileDocumentsParams(demoFundSubId))
        .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        .map(
          _.fold(
            _ => {
              Var.set(
                documentsDataVar -> DocumentData(),
                isGettingVar -> false
              )
              Toast.error("Failed to load requests")
            },
            resp => {
              val documentData = DocumentData(resp)
              Var.set(
                documentsDataVar -> documentData,
                isGettingVar -> false
              )
            }
          )
        )
    } yield ()

    ZIOUtils.toEventStreamUnsafeDEPRECATED(task)

  }

}

private[request] object WithLpProfileDocuments {

  final case class RenderProps(
    isGettingSignal: Signal[Boolean],
    documentDataSignal: Signal[DocumentData],
    refetch: Observer[Boolean]
  )

  final case class DocumentData(
    documents: Seq[ProfileDocumentInfo] = Seq.empty
  )

}
