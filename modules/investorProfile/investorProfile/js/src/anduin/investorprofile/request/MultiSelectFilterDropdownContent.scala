// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.divider.laminar.DividerL
import design.anduin.components.icon.Icon
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.style.tw.*

import anduin.investorprofile.request.MultiSelectFilterDropdownContent.{RenderItemProps, SearchItemProps}

private[request] case class MultiSelectFilterDropdownContent[A](
  allAvailableItems: Seq[A],
  previouslySelectedItems: Seq[A],
  renderItemCheckbox: RenderItemProps[A] => HtmlElement,
  searchFuncOpt: Option[SearchItemProps[A] => Boolean],
  onApply: Observer[Seq[A]],
  onCancel: Observer[Unit]
)(
  using SelfEqual[A]
) {

  private val selectedItemsVar = Var[Seq[A]](previouslySelectedItems)
  private val keywordVar = Var[String]("")

  private val searchedAvailableItemsSignal = keywordVar.signal.distinct.map { keyword =>
    if (keyword.isEmpty) {
      allAvailableItems
    } else {
      allAvailableItems.filter(option => searchFuncOpt.forall(searchFunc => searchFunc(SearchItemProps(option, keyword))))
    }
  }

  private def renderFooter() = {
    div(
      tw.flex.justifyBetween.itemsCenter,
      div(
        ButtonL(
          style = ButtonL.Style.Text(),
          onClick = Observer { _ =>
            selectedItemsVar.set(Seq.empty)
          },
          isDisabled = selectedItemsVar.signal.map(_.isEmpty)
        )(tw.textSmall, "Clear all")
      ),
      div(
        tw.flex.spaceX8,
        ButtonL(
          style = ButtonL.Style.Full(height = ButtonL.Height.Fix24),
          onClick = Observer { _ =>
            onCancel.onNext(())
          }
        )("Cancel"),
        ButtonL(
          style = ButtonL.Style.Full(color = ButtonL.Color.Primary, height = ButtonL.Height.Fix24),
          onClick = Observer { _ =>
            onApply.onNext(selectedItemsVar.now())
          },
          isDisabled = selectedItemsVar.signal.map(_ == previouslySelectedItems)
        )("Apply")
      )
    )
  }

  def apply(): HtmlElement = {
    div(
      tw.overflowXAuto,
      Option.when(searchFuncOpt.nonEmpty)(
        TextBoxL(
          icon = Option(Icon.Glyph.Search),
          placeholder = "Search",
          value = keywordVar.signal,
          onChange = keywordVar.writer
        )().amend(tw.mb8)
      ),
      div(
        tw.px12.wPc100.overflowYScroll,
        maxHeight.px(300),
        children <-- searchedAvailableItemsSignal.distinct.map { searchedAvailableItems =>
          searchedAvailableItems.map(option =>
            renderItemCheckbox(
              RenderItemProps(
                option,
                isChecked = selectedItemsVar.signal.distinct.map(_.contains(option)),
                onChange = Observer[Boolean] { isChecked =>
                  if (isChecked) { selectedItemsVar.update(_.appended(option).distinct) }
                  else { selectedItemsVar.update(_.filterNot(_ == option)) }
                }
              )
            )
          )
        }
      ),
      DividerL()().amend(tw.px12),
      div(
        tw.px12,
        renderFooter()
      )
    )
  }

}

private[request] object MultiSelectFilterDropdownContent {
  case class RenderItemProps[A](option: A, isChecked: Signal[Boolean], onChange: Observer[Boolean])

  case class SearchItemProps[A](option: A, searchKey: String)
}
