// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request.collaborator

import com.raquo.laminar.api.L.*
import design.anduin.components.avatar.laminar.AvatarLabelL
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterL, ModalL}
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.style.tw.*
import org.scalajs.dom

import anduin.id.taskrequest.TaskRequestId
import anduin.investmententity.request.RequestCollaboratorState
import anduin.investorprofile.request.WithRequestDetail
import anduin.investorprofile.request.common.RequestUtils.instantToStr
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.common.user.UserInfo.fullNameString
import com.anduin.stargazer.service.account.AccountUtils

private[request] case class ManageCollaboratorModalContent(
  taskRequestId: TaskRequestId,
  onCancel: Observer[Unit],
  onRefetch: Observer[Boolean]
) {

  private def renderSendReminderButton(userId: UserId, collaboratorInfo: UserInfo) = {
    ModalL(
      renderTitle = _ => s"Send reminder",
      size = ModalL.Size(width = ModalL.Width.Px480),
      isClosable = None,
      renderContent = onClose =>
        SendReminderModalContent(
          taskRequestId = taskRequestId,
          userId = userId,
          userName = collaboratorInfo.fullNameString,
          userEmail = collaboratorInfo.emailAddressStr,
          onCloseModal = onClose
        )(),
      renderTarget = open =>
        ButtonL(
          style = ButtonL.Style.Text(color = ButtonL.Color.Primary),
          onClick = open.contramap(_ => ())
        )("Send reminder").amend(tw.text11)
    )()
  }

  private def renderRemoveButton(userId: UserId, collaboratorInfo: UserInfo, isSelfRemoving: Boolean) = {
    ModalL(
      renderTitle = _ => if (isSelfRemoving) "Leave document request" else "Remove collaborator",
      size = ModalL.Size(width = ModalL.Width.Px480),
      isClosable = None,
      renderContent = onClose =>
        RemoveCollaboratorModalContent(
          taskRequestId = taskRequestId,
          userId = userId,
          userName = collaboratorInfo.fullNameString,
          isSelfRemoving = isSelfRemoving,
          onCloseModal = onClose,
          onDone = Observer.combine(onClose, onCancel, onRefetch.contramap(_ => false))
        )(),
      renderTarget = open =>
        ButtonL(
          style = ButtonL.Style.Text(color = ButtonL.Color.Danger),
          onClick = open.contramap(_ => ())
        )(if (isSelfRemoving) "Leave" else "Remove").amend(tw.text11)
    )()

  }

  private def renderCollaborator(collaboratorState: RequestCollaboratorState) = {
    val collaboratorInfo = collaboratorState.collaborator.userInfo
    div(
      tw.py12.borderBottom.border1.borderGray3,
      tw.flex.itemsCenter.justifyBetween,
      AvatarLabelL(
        emailAddress = collaboratorInfo.emailAddressStr,
        fullName = Val(collaboratorInfo.fullNameString),
        renderCopyIndicator = _ => emptyNode
      )(),
      div(
        tw.flex.flexCol.itemsEnd,
        collaboratorState.invitedAt.map { invitedAt =>
          div(
            s"Invited on ${instantToStr(Some(invitedAt), renderShort = true)}",
            span(" by ", span(tw.fontSemiBold, fullNameString(collaboratorState.inviter.userInfo)))
          )
        },
        collaboratorState.joinedAt.map { joinedAt =>
          s"Joined on ${instantToStr(Some(joinedAt), renderShort = true)}"
        },
        div(
          tw.flex.itemsCenter.spaceX12,
          Option.when(collaboratorState.joinedAt.isEmpty) {
            renderSendReminderButton(collaboratorState.collaborator.userId, collaboratorInfo)
          },
          child <-- AccountUtils.currentUserIdObs.map { userIdOpt =>
            val isCurrentUser = userIdOpt.contains(collaboratorState.collaborator.userId)
            renderRemoveButton(
              collaboratorState.collaborator.userId,
              collaboratorInfo,
              isCurrentUser
            )
          }
        )
      )
    )
  }

  def apply(): HtmlElement = {
    div(
      ModalBodyL(
        div(
          tw.wPc100,
          WithRequestDetail(
            taskRequestId,
            render = requestDetailProps =>
              div(
                child <-- requestDetailProps.requestDetailSignal.map(_.map(_.collaboratorsStates)).distinct.map {
                  _.fold(BlockIndicatorL()().amend(tw.hPx256)) { collaboratorsStates =>
                    if (collaboratorsStates.isEmpty) {
                      div(
                        tw.mb16,
                        "You don't have any collaborators to manage. Please click 'Done' and try again."
                      )
                    } else {
                      div(
                        div(tw.mb16, "Collaborators can help you track and manage your requests."),
                        div(
                          tw.mb16,
                          collaboratorsStates.map(renderCollaborator)
                        ),
                        ModalL(
                          renderTitle = _ => s"Invite collaborator",
                          size = ModalL.Size(width = ModalL.Width.Px1160),
                          isClosable = None,
                          renderContent = onClose =>
                            InviteCollaboratorModalContent(
                              taskRequestId = taskRequestId,
                              onCancel = onClose,
                              onRefetch = requestDetailProps.refetch,
                              onDone = Observer.combine(onClose, onCancel, onRefetch.contramap(_ => false))
                            )(),
                          renderTarget = open =>
                            ButtonL(
                              hasChildren = true,
                              style = ButtonL.Style.Full(color = ButtonL.Color.Primary, icon = Some(Icon.Glyph.UserAdd)),
                              onClick = open.contramap(_ => ())
                            )("Invite more").amend(tw.mb24)
                        )()
                      )
                    }
                  }
                }
              )
          )()
        )
      ),
      ModalFooterL(
        div(
          tw.flex.justifyEnd,
          ButtonL(
            onClick = Observer[dom.MouseEvent] { _ =>
              onCancel.onNext(())
              onRefetch.onNext(false)
            }
          )("Done")
        )
      )
    )

  }

}
