// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request

import com.raquo.laminar.api.L.*
import design.anduin.components.nonidealstate.laminar.NonIdealStateL

private[request] case class EmptyState(
  title: Node = emptyNode,
  description: Node = emptyNode,
  action: Node = emptyNode
) {

  def apply(): HtmlElement = {
    NonIdealStateL(
      icon = img(src := "/web/gondor/images/funddata/search_document.svg"),
      title = title,
      description = description,
      action = action
    )()
  }

}
