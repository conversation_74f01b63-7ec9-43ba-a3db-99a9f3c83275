// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request

import scala.math.Ordered.orderingToOrdered

import com.raquo.laminar.api.L.*
import design.anduin.components.badge.Badge
import design.anduin.components.badge.laminar.BadgeL
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.checkbox.CheckboxL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.tab.Tab
import design.anduin.components.tab.laminar.TabL
import design.anduin.components.text.laminar.TruncateL
import design.anduin.style.tw.*
import design.anduin.table.laminar.TableL

import anduin.investmententity.request.{LpProfileRequestInfo, LpProfileRequestStatus}
import anduin.investorprofile.request.common.LpProfileRequestStatusTag
import anduin.investorprofile.request.common.RequestUtils.instantToStr
import anduin.scalajs.pluralize.Pluralize

private[request] case class LpProfileRequestDashboardSection(
  pageParamsSignal: Signal[LpProfileRequestPageParams],
  onSetPageParams: Observer[LpProfileRequestPageParams]
) {

  private val filteredStatusesSignal = pageParamsSignal.map(_.filterByStatus).distinct
  private val filteredFundNamesSignal = pageParamsSignal.map(_.filterByFundName).distinct
  private val pageParamsUpdateEventBus = new EventBus[LpProfileRequestPageParams.ParamsUpdate]

  given SelfEqual[Class[? <: LpProfileRequestStatus]] = CanEqual.derived

  // Filter Bar
  private def filterRequests(requestData: WithLpProfileRequests.RequestData, pageParams: LpProfileRequestPageParams) = {
    val filteredByIsOnGoing = if (pageParams.isOngoing) {
      requestData.onGoingRequests
    } else {
      requestData.historyRequests
    }
    val filteredByFundName = filteredByIsOnGoing.filter(request =>
      if (pageParams.filterByFundName.isEmpty) {
        true
      } else {
        pageParams.filterByFundName.contains(request.fundName)
      }
    )
    val filteredByStatus = filteredByFundName.filter(request =>
      if (pageParams.filterByStatus.isEmpty) {
        true
      } else {
        pageParams.filterByStatus.exists(filteredStatus => filteredStatus.getClass == request.status.getClass)
      }
    )
    filteredByStatus
  }

  private def renderFilterByStatusDropdown(availableStatuses: Seq[LpProfileRequestStatus]) = {
    PopoverL(
      position = PortalPosition.BottomLeft,
      renderContent = onClosePopover => {
        div(
          width.px(240),
          child <-- filteredStatusesSignal.map { filteredStatuses =>
            MultiSelectFilterDropdownContent[LpProfileRequestStatus](
              availableStatuses,
              previouslySelectedItems = filteredStatuses.filter(status => availableStatuses.contains(status)),
              searchFuncOpt = None,
              renderItemCheckbox = props =>
                div(
                  tw.py6,
                  CheckboxL(
                    style = CheckboxL.Style.Minimal,
                    isChecked = props.isChecked,
                    onChange = props.onChange
                  )(LpProfileRequestStatusTag(props.option)())
                ),
              onApply = Observer[Seq[LpProfileRequestStatus]] { selectedStatuses =>
                pageParamsUpdateEventBus.emit(
                  LpProfileRequestPageParams.ParamsUpdate(filterByStatus = Some(selectedStatuses))
                )
                onClosePopover.onNext(())
              },
              onCancel = onClosePopover
            )().amend(width.px(240))
          }
        )
      },
      renderTarget = (open, isOpened) => {
        ButtonL(
          style = ButtonL.Style.Full(
            isSelected = isOpened
          ),
          onClick = open.contramap(_ => ())
        )(
          div(
            tw.flex.itemsCenter.justifyCenter,
            child <-- filteredStatusesSignal.map(filteredStatuses =>
              if (filteredStatuses.isEmpty) {
                "All statuses"
              } else {
                val sortedStatuses =
                  filteredStatuses.sortWith((a, b) => LpProfileRequestStatus.StatusOrdering.compare(a, b) < 0)
                val tailStr =
                  (sortedStatuses.tail.dropRight(1).map(_.name) ++ sortedStatuses.tail.lastOption
                    .map("and " + _.name)
                    .toList)
                    .mkString(", ")
                sortedStatuses.head.name + (if (sortedStatuses.size > 2) ", " else " ") + tailStr
              }
            ),
            div(
              tw.ml4.relative,
              IconL(name = Val(Icon.Glyph.CaretDown))()
            )
          )
        )
      }
    )()
  }

  private def renderFilterByFundNameDropDown(allAvailableFundNames: Seq[String]) = {
    PopoverL(
      position = PortalPosition.BottomLeft,
      renderContent = onClosePopover => {
        div(
          width.px(240),
          child <-- filteredFundNamesSignal.map { filteredFundNames =>
            MultiSelectFilterDropdownContent[String](
              allAvailableFundNames,
              previouslySelectedItems = filteredFundNames.filter(fundName => allAvailableFundNames.contains(fundName)),
              searchFuncOpt = Some(props => props.option.toLowerCase.contains(props.searchKey.toLowerCase)),
              renderItemCheckbox = props =>
                div(
                  tw.py6.wPc100,
                  CheckboxL(
                    style = CheckboxL.Style.Minimal,
                    isChecked = props.isChecked,
                    onChange = props.onChange
                  )(
                    props.option
                  ).amend(tw.wPc100)
                ),
              onApply = Observer[Seq[String]] { selectedFundNames =>
                pageParamsUpdateEventBus.emit(
                  LpProfileRequestPageParams.ParamsUpdate(filterByFundName = Some(selectedFundNames))
                )
                onClosePopover.onNext(())
              },
              onCancel = onClosePopover
            )().amend(width.px(240))
          }
        )
      },
      renderTarget = (open, isOpened) => {
        ButtonL(
          style = ButtonL.Style.Full(
            isSelected = isOpened
          ),
          onClick = open.contramap(_ => ())
        )(
          div(
            tw.flex.itemsCenter.justifyCenter,
            child <-- filteredFundNamesSignal.map(filteredFundNames =>
              if (filteredFundNames.isEmpty) {
                "All funds"
              } else {
                s"${Pluralize("fund", filteredFundNames.size, true)} "
              }
            ),
            div(
              tw.ml4.relative,
              IconL(name = Val(Icon.Glyph.CaretDown))()
            )
          )
        )
      }
    )()

  }

  private def renderFilterBar(
    allAvailableFundNames: Seq[String],
    allAvailableStatuses: Seq[LpProfileRequestStatus]
  ) = {
    div(
      tw.flexNone.mb16.flex.itemsCenter.spaceX8,
      renderFilterByFundNameDropDown(allAvailableFundNames),
      renderFilterByStatusDropdown(allAvailableStatuses)
    )
  }

  // Table
  private val nameColumn = TableL.Column[LpProfileRequestInfo](
    title = "Request name",
    field = "requestname",
    widthGrow = Some(3),
    renderCell = renderProps => {
      val requestName = renderProps.data.requestName
      div(
        TruncateL(
          lineClamp = Some(2),
          target = div(tw.wPc100.whitespaceNormal.breakWords.flexNone, requestName),
          title = Some(requestName)
        )()
      )
    },
    isSortable = true,
    sortWith = Option(sorter => {
      val sortResult = for {
        a <- sorter.a
        b <- sorter.b
      } yield {
        a.requestName.toLowerCase.compareTo(b.requestName.toLowerCase).toDouble
      }
      sortResult.getOrElse(0)
    })
  )

  private val requesterColumn = TableL.Column[LpProfileRequestInfo](
    title = "Requester",
    field = "requester",
    widthGrow = Some(3),
    renderCell = renderProps => {
      TruncateL(
        target = div(
          renderProps.data.createdBy.userInfo.fullNameString
        )
      )()
    },
    isSortable = true,
    sortWith = Option(sorter => {
      val sortResult = for {
        a <- sorter.a
        b <- sorter.b
      } yield {
        val aComparedValue = a.createdBy.userInfo.fullNameString.toLowerCase
        val bComparedValue = b.createdBy.userInfo.fullNameString.toLowerCase
        aComparedValue.compareTo(bComparedValue).toDouble
      }
      sortResult.getOrElse(0)
    })
  )

  private val fundColumn = TableL.Column[LpProfileRequestInfo](
    title = "Fund",
    field = "fund",
    widthGrow = Some(3),
    renderCell = renderProps => {
      TruncateL(
        target = div(
          renderProps.data.fundName
        )
      )()
    },
    isSortable = true,
    sortWith = Option(sorter => {
      val sortResult = for {
        a <- sorter.a
        b <- sorter.b
      } yield {
        a.fundName.toLowerCase.compareTo(b.fundName.toLowerCase).toDouble
      }
      sortResult.getOrElse(0)
    })
  )

  private val statusColumn = TableL.Column[LpProfileRequestInfo](
    title = "Status",
    field = "status",
    widthGrow = Some(2),
    renderCell = renderProps => {
      div(tw.wPc100.overflowHidden, LpProfileRequestStatusTag(renderProps.data.status)())
    },
    isSortable = true,
    sortWith = Option(sorter => {
      val sortResult = for {
        a <- sorter.a
        b <- sorter.b
      } yield {
        LpProfileRequestStatus.StatusOrdering.compare(a.status, b.status)
      }
      sortResult.getOrElse(0)
    })
  )

  private val sentDateColumn = TableL.Column[LpProfileRequestInfo](
    title = "Sent date",
    field = "sentdate",
    widthGrow = Some(2),
    renderCell = renderProps => {
      TruncateL(
        target = div(
          instantToStr(renderProps.data.createdAt, renderShort = true)
        )
      )()
    },
    isSortable = true,
    sortWith = Option(sorter => {
      val sortResult = for {
        a <- sorter.a
        b <- sorter.b
      } yield {
        a.createdAt.compareTo(b.createdAt).toDouble
      }
      sortResult.getOrElse(0)
    })
  )

  private def renderViewRequestButton() = {
    ButtonL(
      hasChildren = true,
      style = ButtonL.Style.Minimal(),
      onClick = Observer.empty
    )(
      div(
        tw.flex.itemsCenter.spaceX8,
        "View",
        IconL(name = Val(Icon.Glyph.ChevronRight), size = Icon.Size.Px16)()
      )
    )
  }

  private def actionColumn = TableL.Column[LpProfileRequestInfo](
    title = "",
    field = "action",
    widthGrow = Some(2),
    horizontalAlignment = Some(TableL.HorizontalAlignment.Right),
    renderCell = renderProps => {
      div(
        tw.invisible.groupHover(tw.visible),
        renderViewRequestButton()
      )
    }
  )

  private def handleRowRendered(rowRenderedData: TableL.RowRenderedData[LpProfileRequestInfo]): Unit = {
    rowRenderedData.rowComponent.getElement().classList.add(tw.group.css)
  }

  private def renderTable(
    requests: Seq[LpProfileRequestInfo],
    requestsCount: Int,
    isOngoing: Boolean
  ) = {
    div(
      tw.flexFill,
      if (requests.isEmpty) {
        div(
          tw.wPc100.hPc100,
          if (requestsCount > 0) {
            EmptyState(title = "No results", description = "Try tweaking your filter")()
          } else {
            if (isOngoing) {
              EmptyState(
                title = "No ongoing requests yet",
                description = div("You'll be notified after a fund sent you a request")
              )()
            } else {
              EmptyState(title = "No history to show", description = "Complete or canceled requests will be shown here")()
            }
          }
        )
      } else {
        TableL[LpProfileRequestInfo](
          dataSignal = Val(requests.toList),
          options = TableL.Options(
            layout = TableL.Layout.FitColumns,
            selectableMode = TableL.SelectableMode.Clickable
          ),
          columns = List(
            nameColumn,
            requesterColumn,
            fundColumn,
            statusColumn,
            sentDateColumn,
            actionColumn
          ),
          initialSortColumns = List(
            TableL.SortColumn(column = sentDateColumn, direction = TableL.ColumnSortDirection.Desc)
          ),
          onRowClick = Observer[TableL.RowClickData[LpProfileRequestInfo]] { rowClickData =>
            val rowDataOpt = rowClickData.getData()
            pageParamsUpdateEventBus.emit(
              LpProfileRequestPageParams.ParamsUpdate(selectedRequest = Some(rowDataOpt.map(rowData => rowData.requestId)))
            )
          },
          onRowRendered = Observer[TableL.RowRenderedData[LpProfileRequestInfo]](handleRowRendered)
        ).amend(tw.hPc100)
      }
    )
  }

  def apply(): HtmlElement = {
    div(
      tw.wPc100.hPc100.flex.flexCol,
      tw.py24,
      paddingLeft.px(160),
      paddingRight.px(160),
      div(tw.heading1, "Document requests"),
      div(
        tw.flexFill,
        WithLpProfileRequests(render =
          renderProps =>
            div(
              tw.hPc100.py16,
              child <-- renderProps.isGettingSignal.map { isGetting =>
                if (isGetting) {
                  BlockIndicatorL(title = Val(Some("Loading requests' information...")), isFullHeight = true)()
                } else {
                  div(
                    tw.hPc100.flex.flexCol,
                    pageParamsUpdateEventBus.events.distinct.withCurrentValueOf(pageParamsSignal) --> Observer[
                      (LpProfileRequestPageParams.ParamsUpdate, LpProfileRequestPageParams)
                    ] { case (update, params) =>
                      val updatedParams = params.applyUpdate(update)
                      onSetPageParams.onNext(updatedParams)
                    },
                    div(
                      tw.flexNone,
                      TabL(
                        activeTabSignal = pageParamsSignal.map(pageParams => Some(if (pageParams.isOngoing) 0 else 1)),
                        style = Tab.Style.Minimal(),
                        panels = List(
                          Tab.Panel(
                            title = div(
                              tw.flex.itemsCenter.spaceX8,
                              div("Ongoing"),
                              BadgeL(
                                Badge.Color.Gray,
                                theme = Badge.Theme.Light,
                                count = renderProps.requestDataSignal
                                  .map(requestData => Some(requestData.onGoingRequests.size))
                              )()
                            )
                          ),
                          Tab.Panel(
                            title = div(
                              tw.flex.itemsCenter.spaceX8,
                              div("History"),
                              BadgeL(
                                Badge.Color.Gray,
                                theme = Badge.Theme.Light,
                                count = renderProps.requestDataSignal
                                  .map(requestData => Some(requestData.historyRequests.size))
                              )()
                            )
                          )
                        ),
                        onClick = Observer[Int] { tabIndex =>
                          val isOngoing = tabIndex == 0
                          pageParamsUpdateEventBus.emit(
                            LpProfileRequestPageParams.ParamsUpdate(
                              isOngoing = Some(isOngoing),
                              filterByFundName = Some(Seq.empty),
                              filterByStatus = Some(Seq.empty),
                              selectedRequest = Some(None)
                            )
                          )
                          renderProps.refetch.onNext(false)
                        }
                      )()
                    ),
                    children <-- renderProps.requestDataSignal.combineWith(pageParamsSignal).map {
                      case (requestData, pageParams) =>
                        val filteredRequests = filterRequests(requestData, pageParams)
                        val numRequests = if (pageParams.isOngoing) {
                          requestData.onGoingRequests.size
                        } else {
                          requestData.historyRequests.size
                        }
                        val requests = if (pageParams.isOngoing) {
                          requestData.onGoingRequests
                        } else {
                          requestData.historyRequests
                        }
                        val children = Seq(
                          renderFilterBar(
                            requests.map(_.fundName).distinct.sorted,
                            LpProfileRequestDashboardSection.availableStatusOptions(pageParams.isOngoing)
                          ).amend(tw.mt16),
                          renderTable(filteredRequests, numRequests, pageParams.isOngoing)
                        )
                        if (requests.isEmpty) children.drop(1) else children
                    }
                  )
                }
              }
            )
        )()
      )
    )

  }

}

private[request] object LpProfileRequestDashboardSection {

  private def availableStatusOptions(isOngoing: Boolean) = if (isOngoing) {
    Seq[LpProfileRequestStatus](
      LpProfileRequestStatus.RequestViewed(),
      LpProfileRequestStatus.RequestInProgress()
    )
  } else {
    Seq[LpProfileRequestStatus](
      LpProfileRequestStatus.RequestCompleted(),
      LpProfileRequestStatus.RequestCanceled()
    )
  }

}
