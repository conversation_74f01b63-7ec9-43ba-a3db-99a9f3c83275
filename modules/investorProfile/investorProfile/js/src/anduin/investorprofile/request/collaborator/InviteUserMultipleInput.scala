// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request.collaborator

import scala.util.Random

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.divider.Divider
import design.anduin.components.divider.laminar.DividerL
import design.anduin.components.icon.Icon
import design.anduin.components.portal.PortalPosition
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import design.anduin.validators.rules.EmailAddressRule
import design.anduin.validators.{ErrorMessage, Result}
import org.scalajs.dom

import anduin.investorprofile.request.collaborator.InviteUserMultipleInput.{InvitingUserData, UpdateInvitingUserInput}

private[request] case class InviteUserMultipleInput(
  invitingUsersSignal: Signal[List[InvitingUserData]],
  onUpdateInvitingUser: Observer[UpdateInvitingUserInput],
  onAddInvitingUser: Observer[InvitingUserData],
  onRemoveInvitingUser: Observer[String]
) {
  private val numInviteUsersSignal = invitingUsersSignal.map(_.size)

  private def renderRemoveButton(removedInvitingUserId: String) = {
    ButtonL(
      isDisabled = numInviteUsersSignal.map(_ <= 1),
      style = ButtonL.Style.Minimal(icon = Option(Icon.Glyph.Cross)),
      onClick = Observer[dom.MouseEvent] { _ =>
        onRemoveInvitingUser.onNext(removedInvitingUserId)
      }
    )()
  }

  private def renderValidation(validation: Result) = {
    if (!validation.valid) {
      div(tw.textDanger4.text11.mt4, validation.errors.head.toString)
    } else {
      emptyNode
    }
  }

  def apply(): HtmlElement = {
    div(
      tw.wPc100,
      div(
        tw.flex.itemsCenter.px12.fontSemiBold.mb8,
        div(
          tw.mr16,
          width.px(600),
          "Contact name",
          span(tw.textDanger4, " *")
        ),
        div(
          tw.mr16,
          width.px(244),
          "Contact email",
          span(tw.textDanger4, " *")
        )
      ),
      children <-- invitingUsersSignal.split(_.id) { case (id, _, inviteUserSignal) =>
        div(
          tw.p8.bgGray1.borderAll.rounded4.borderGray3,
          tw.mb8,
          div(
            tw.flex.itemsCenter,
            div(
              tw.mr8,
              width.px(296),
              TextBoxL(
                placeholder = "First name",
                value = inviteUserSignal.map(_.firstName),
                onChange = Observer[String](firstName =>
                  onUpdateInvitingUser.onNext(UpdateInvitingUserInput(id, _.copy(firstName = firstName)))
                ),
                status = inviteUserSignal
                  .map(_.firstNameValidation)
                  .map(validation => if (validation.valid) TextBoxL.Status.None else TextBoxL.Status.Invalid)
              )()
            ),
            div(
              width.px(296),
              TextBoxL(
                placeholder = "Last name",
                value = inviteUserSignal.map(_.lastName),
                onChange = Observer[String](lastName =>
                  onUpdateInvitingUser.onNext(UpdateInvitingUserInput(id, _.copy(lastName = lastName)))
                ),
                status = inviteUserSignal
                  .map(_.lastNameValidation)
                  .map(validation => if (validation.valid) TextBoxL.Status.None else TextBoxL.Status.Invalid)
              )()
            ),
            DividerL(
              direction = Divider.Direction.Vertical
            )(),
            div(
              tw.mr8,
              tw.flexFill,
              TextBoxL(
                placeholder = "<EMAIL>",
                value = inviteUserSignal.map(_.email),
                onChange = Observer[String](emailAddress =>
                  onUpdateInvitingUser.onNext(UpdateInvitingUserInput(id, _.copy(email = emailAddress.toLowerCase)))
                ),
                status = inviteUserSignal
                  .map(_.emailValidation)
                  .map(validation => if (validation.valid) TextBoxL.Status.None else TextBoxL.Status.Invalid)
              )()
            ),
            TooltipL(
              position = PortalPosition.LeftCenter,
              renderContent = _.amend("Can't remove last row"),
              renderTarget = renderRemoveButton(id),
              isDisabled = numInviteUsersSignal.map(_ > 1)
            )()
          ),
          div(
            tw.flex.itemsCenter,
            div(
              tw.mr8,
              width.px(296),
              child <-- inviteUserSignal.map(_.firstNameValidation).map(renderValidation)
            ),
            div(
              tw.mr16,
              width.px(296),
              child <-- inviteUserSignal.map(_.lastNameValidation).map(renderValidation)
            ),
            div(
              tw.mr8,
              child <-- inviteUserSignal.map(_.emailValidation).map(renderValidation)
            )
          )
        )
      },
      ButtonL(
        style = ButtonL.Style.Minimal(color = ButtonL.Color.Primary, icon = Option(Icon.Glyph.PlusCircle)),
        onClick = Observer[dom.MouseEvent] { _ =>
          onAddInvitingUser.onNext(InvitingUserData())
        }
      )("Add another")
    )
  }

}

private[request] object InviteUserMultipleInput {

  case class InvitingUserData(
    id: String = Random.alphanumeric.take(16).mkString,
    firstName: String = "",
    lastName: String = "",
    email: String = "",
    firstNameValidation: Result = Result.Valid,
    lastNameValidation: Result = Result.Valid,
    emailValidation: Result = Result.Valid
  ) {
    lazy val isValid: Boolean = firstNameValidation.valid && lastNameValidation.valid && emailValidation.valid
    lazy val isAllNonEmpty: Boolean = firstName.trim.nonEmpty && lastName.trim.nonEmpty && email.trim.nonEmpty

    def validateFirstName: InvitingUserData = {
      if (firstName.trim.isEmpty) {
        this.copy(firstNameValidation = Result(valid = false, error = ErrorMessage("First name is required")))
      } else {
        this
      }
    }

    def validateLastName: InvitingUserData = {
      if (lastName.trim.isEmpty) {
        this.copy(lastNameValidation = Result(valid = false, error = ErrorMessage("Last name is required")))
      } else {
        this
      }
    }

    def validateEmailAddress(otherEmailAddresses: List[String], existedEmailAddresses: List[String]): InvitingUserData = {
      if (email.trim.isEmpty) {
        this.copy(emailValidation = Result(valid = false, error = ErrorMessage("Email address is required")))
      } else {
        if (!email.matches(EmailAddressRule.emailRegex)) {
          this.copy(emailValidation = Result(valid = false, error = ErrorMessage("Email is invalid")))
        } else {
          if (otherEmailAddresses.contains(email)) {
            this.copy(emailValidation = Result(valid = false, error = ErrorMessage("Duplicated email")))
          } else {
            if (existedEmailAddresses.contains(email)) {
              this.copy(emailValidation = Result(valid = false, error = ErrorMessage("Existed email")))
            } else {
              this
            }
          }
        }
      }
    }

    def resetValidation: InvitingUserData = {
      this.copy(
        firstNameValidation = Result.Valid,
        lastNameValidation = Result.Valid,
        emailValidation = Result.Valid
      )
    }

  }

  case class UpdateInvitingUserInput(id: String, updateFunc: InvitingUserData => InvitingUserData)
}
