// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request.common

import anduin.utils.DateTimeUtils

import java.time.Instant

private[request] object RequestUtils {

  def instantToStr(instantOpt: Option[Instant], renderShort: Boolean = false) = {
    instantOpt.fold("--") { instant =>
      DateTimeUtils
        .formatInstant(
          instant,
          if (renderShort) DateTimeUtils.ShortLocalDatePattern else DateTimeUtils.MonthDayTimeWithoutYearFormatter
        )(
          using DateTimeUtils.defaultTimezone
        )
    }
  }

}
