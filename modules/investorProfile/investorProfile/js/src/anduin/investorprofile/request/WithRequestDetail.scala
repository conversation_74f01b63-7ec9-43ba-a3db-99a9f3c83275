// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request

import com.raquo.laminar.api.L.*
import zio.ZIO

import anduin.id.taskrequest.TaskRequestId
import anduin.investmententity.request.{GetRequestDetailParams, LpProfileRequestDetail}
import anduin.investorprofile.client.LpProfileRequestEndpointClient
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.utils.ZIOUtils

private[request] case class WithRequestDetail(
  taskRequestId: TaskRequestId,
  render: WithRequestDetail.RenderProps => HtmlElement,
  onError: Observer[Unit] = Observer.empty
) {
  private val getEventBus = new EventBus[(Boolean, Observer[Unit])]

  private val isGettingVar = Var[Boolean](true)
  private val requestDetailVar = Var[Option[LpProfileRequestDetail]](None)

  def apply(): HtmlElement = {
    render(
      WithRequestDetail.RenderProps(
        isGettingSignal = isGettingVar.signal,
        requestDetailSignal = requestDetailVar.signal,
        refetchWithCallback = getEventBus.writer
      )
    ).amend(
      getRequestDetail(setGetting = true, Observer.empty) --> Observer.empty,
      getEventBus.events.flatMapSwitch(params => getRequestDetail(params._1, params._2)) --> Observer.empty
    )
  }

  private def getRequestDetail(setGetting: Boolean, onGetDone: Observer[Unit]): EventStream[Unit] = {
    val task = for {
      _ <- ZIOUtils.when(setGetting)(ZIO.attempt(isGettingVar.set(true)))
      _ <- LpProfileRequestEndpointClient
        .getRequestDetail(GetRequestDetailParams(taskRequestId))
        .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        .map(
          _.fold(
            _ => {
              Var.set(
                isGettingVar -> false,
                requestDetailVar -> None
              )
              onError.onNext(())
            },
            requestDetail => {
              Var.set(
                isGettingVar -> false,
                requestDetailVar -> Some(requestDetail)
              )
              onGetDone.onNext(())
            }
          )
        )
    } yield ()
    ZIOUtils.toEventStreamUnsafeDEPRECATED(task)
  }

}

private[request] object WithRequestDetail {

  final case class RenderProps(
    isGettingSignal: Signal[Boolean],
    requestDetailSignal: Signal[Option[LpProfileRequestDetail]],
    refetchWithCallback: Observer[(Boolean, Observer[Unit])]
  ) {

    val refetch: Observer[Boolean] = Observer[Boolean] { isGetting =>
      refetchWithCallback.onNext((isGetting, Observer.empty))
    }

  }

}
