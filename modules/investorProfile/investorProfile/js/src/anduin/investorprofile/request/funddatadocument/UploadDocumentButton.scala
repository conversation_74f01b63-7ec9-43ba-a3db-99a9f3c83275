// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request.funddatadocument

import anduin.component.laminar.FileUploader
import anduin.id.taskrequest.TaskRequestItemId
import anduin.investmententity.request.{
  CopyFileFromProfileToRequestParams,
  InsertDocumentRequestItemParams,
  UpdateDocumentRequestItemParams
}
import anduin.investorprofile.InvestorProfileDocumentCommons.ProfileDocumentInfo
import anduin.investorprofile.client.LpProfileRequestEndpointClient
import anduin.investorprofile.request.common.MenuItemWithDescriptionL
import anduin.model.id.{FileId, FolderId}
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.services.file.FileJsClient
import com.anduin.stargazer.client.utils.ZIOUtils
import com.raquo.laminar.api.L.*
import com.raquo.laminar.nodes.ReactiveElement
import design.anduin.components.divider.laminar.DividerL
import design.anduin.components.icon.Icon
import design.anduin.components.menu.laminar.MenuL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalL}
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.portal.laminar.PortalWrapperL
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import org.scalajs.dom.{File, HTMLInputElement}
import zio.ZIO

import java.time.Instant

private[request] final case class UploadDocumentButton(
  taskRequestItemId: TaskRequestItemId,
  lpProfileDocumentsSignal: Signal[Seq[ProfileDocumentInfo]],
  onUploadDone: Observer[Observer[Unit]],
  renderButton: UploadDocumentButton.RenderButtonProps => HtmlElement,
  hasOldDocument: Boolean,
  folderId: FolderId
) {
  private val isAddingVar = Var[Boolean](false)

  private def renderUploadRawDocument(onClosePopOver: Observer[Unit]) = {
    FileUploader(
      observer = Observer[Seq[File]] { files =>
        files.headOption.foreach(uploadDocument)
        onClosePopOver.onNext(())
      },
      isMultiple = false,
      renderLayer = (_, _, _, children) => {
        val fileInput = children.lastOption.getOrElse(emptyNode)
        div(
          MenuItemWithDescriptionL(
            iconName = Icon.Glyph.Upload,
            title = "New document",
            isDisabledSignal = isAddingVar.signal,
            description = "Upload a document from your device",
            onMenuItemClick = Observer { _ =>
              fileInput match {
                case element: ReactiveElement[?] =>
                  element.ref match {
                    case ref: HTMLInputElement => ref.click()
                    case _                     => ()
                  }
                case _ => ()
              }
            }
          )(
            div(
              tw.relative,
              child.maybe <-- isAddingVar.signal.map(isAdding => Option.when(!isAdding) { fileInput })
            )
          )
        )
      }
    )()

  }

  private def renderUploadDocumentFromLpProfile() = {
    ModalL(
      renderTitle = _ => s"Upload existing document",
      size = ModalL.Size(ModalL.Width.Px960),
      isClosable = None,
      renderContent = onClose =>
        SelectDocumentFromProfileModalContent(
          lpProfileDocumentsSignal,
          onClose,
          Observer[(FileId, Option[Instant])](uploadDocumentFromProfile)
        )(),
      renderTarget = open =>
        TooltipL(
          isDisabled = lpProfileDocumentsSignal.map(_.nonEmpty),
          renderContent = _.amend("There are no documents in your investment profiles"),
          renderTarget = MenuItemWithDescriptionL(
            iconName = Icon.Glyph.Cloud,
            title = "Existing document",
            isDisabledSignal = lpProfileDocumentsSignal.map(_.isEmpty),
            description = "Upload a document from one of your investment profiles",
            onMenuItemClick = open
          )()
        )()
    )()
  }

  def apply(): Node = {
    div(
      tw.wPc100,
      ModalL(
        renderContent = _ =>
          ModalBodyL(
            BlockIndicatorL(title = Val(Some("Uploading file...")), isFullHeight = true)().amend(height.px(250))
          ),
        isOpened = Some(isAddingVar.signal),
        isClosable = None
      )(),
      PopoverL(
        targetWrapper = PortalWrapperL.Block,
        renderContent = close =>
          MenuL(
            children = Seq(
              renderUploadDocumentFromLpProfile(),
              DividerL()(),
              renderUploadRawDocument(close)
            )
          ),
        renderTarget =
          (open, isOpened) => renderButton(UploadDocumentButton.RenderButtonProps(open, isOpened, isAddingVar.signal)),
        position = PortalPosition.LeftBottom
      )()
    )
  }

  private def upsertDocumentRequestItemTask(fileId: FileId, expiredAt: Option[Instant]) = {
    (if (hasOldDocument) {
       LpProfileRequestEndpointClient
         .updateDocumentRequestItemSubmission(
           UpdateDocumentRequestItemParams(
             requestItemId = taskRequestItemId,
             updatedFileIdOpt = Some(Some(fileId)),
             updatedExpiredAt = Some(expiredAt),
             updatedAdditionalNote = None
           )
         )
     } else {
       LpProfileRequestEndpointClient
         .insertDocumentRequestItemSubmission(
           InsertDocumentRequestItemParams(
             requestItemId = taskRequestItemId,
             fileIdOpt = Some(fileId),
             expiredAt = expiredAt,
             additionalNote = ""
           )
         )
     })
      .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
      .map(
        _.fold(
          _ => Toast.error("Failed to upload documents, try again"),
          _ => {
            Toast.success(s"Document ${if (hasOldDocument) "replaced" else "uploaded"} successfully")
            onUploadDone.onNext(Observer[Unit] { _ => isAddingVar.set(false) })
          }
        )
      )
  }

  private def uploadDocumentFromProfile(
    fileId: FileId,
    expiredAt: Option[Instant]
  ): Unit = {
    val task = for {
      _ <- ZIO.attempt(isAddingVar.set(true))
      fileIdOpt <- LpProfileRequestEndpointClient
        .copyFileFromProfileToRequest(
          CopyFileFromProfileToRequestParams(fileId, taskRequestItemId.parent)
        )
        .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        .map(
          _.fold[Option[FileId]](
            _ => {
              Toast.error("Failed to uploading file from profile")
              None
            },
            resp => Some(resp.fileId)
          )
        )
      _ <- ZIOUtils.traverseOptionUnit(fileIdOpt) { fileId => upsertDocumentRequestItemTask(fileId, expiredAt) }
    } yield ()
    ZIOUtils.runAsync(task)
  }

  private def uploadDocument(
    file: File
  ): Unit = {
    val task = for {
      _ <- ZIO.attempt(isAddingVar.set(true))
      fileIds <- FileJsClient
        .uploadToExistingFolders(
          folderId = folderId,
          files = Seq(file)
        )
        .catchAll { _ =>
          Toast.error("Failed to upload documents, try again")
          ZIO.succeed(Seq.empty[FileId])
        }
      _ <- ZIOUtils.traverseOptionUnit(fileIds.headOption) { fileId =>
        upsertDocumentRequestItemTask(fileId, None)
      }
    } yield ()
    ZIOUtils.runAsync(task)
  }

}

private[request] object UploadDocumentButton {
  final case class RenderButtonProps(open: Observer[Unit], isOpened: Signal[Boolean], isBusy: Signal[Boolean])

}
