// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request

import com.raquo.laminar.api.L.*
import design.anduin.components.wrapper.react.ReactiveWrapperR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.investorprofile.Layout
import anduin.investorprofile.investmententity.HelpMenuPopOver
import stargazer.model.routing.DynamicAuthPage.LpProfile
import stargazer.model.routing.Page

final case class LpProfileRequestEntryPoint(
  router: RouterCtl[Page],
  page: LpProfile.LpProfileRequestPage
) {

  def apply(): VdomElement = {
    LpProfileRequestEntryPoint.component(this)
  }

}

object LpProfileRequestEntryPoint {
  private type Props = LpProfileRequestEntryPoint

  private val reactiveWrapper = (new ReactiveWrapperR[Map[String, String]])()

  private def render(props: Props) = {
    Layout(isInvestmentEntityDetailPage = false, activeItemOpt = Some(Layout.NavigationItem.Requests))(
      reactiveWrapper(
        value = Some(props.page.params),
        containerTag = <.div(tw.flexFill),
        renderNode = paramsOptSignal => {
          div(
            tw.hPc100,
            LpProfileRequestPage(
              pageParamsSignal = paramsOptSignal
                .map(paramsOpt => LpProfileRequestPageParams.fromUrlParams(paramsOpt.getOrElse(Map.empty)))
                .distinct,
              onSetPageParams = Observer[LpProfileRequestPageParams] { params =>
                props.router.set(props.page.copy(params = LpProfileRequestPageParams.toUrlParams(params))).runNow()
              }
            )(),
            HelpMenuPopOver()()
          )
        }
      )()
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
