// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.request

import anduin.id.fundsub.FundSubId
import anduin.id.taskrequest.TaskRequestId
import anduin.investorprofile.request.LpProfileRequestPageParams.{DemoParams, ParamsUpdate}
import anduin.pageparam.{PageParamConverter, PageParams, PageParamsCompanion}
import anduin.investmententity.request.LpProfileRequestStatus

private[investorprofile] final case class LpProfileRequestPageParams(
  isOngoing: Boolean = true,
  filterByFundName: Seq[String] = Seq(),
  filterByStatus: Seq[LpProfileRequestStatus] = Seq(),
  selectedRequest: Option[TaskRequestId] = None,
  demoFundSubId: Option[FundSubId] = None
) extends PageParams {
  lazy val demoParams: DemoParams = DemoParams(demoFundSubId)

  def applyUpdate(update: ParamsUpdate): LpProfileRequestPageParams = copy(
    update.isOngoing.getOrElse(isOngoing),
    update.filterByFundName.getOrElse(filterByFundName),
    update.filterByStatus.getOrElse(filterByStatus),
    update.selectedRequest.getOrElse(selectedRequest),
    demoFundSubId
  )

}

private[investorprofile] object LpProfileRequestPageParams extends PageParamsCompanion[LpProfileRequestPageParams] {

  final case class DemoParams(demoFundSubId: Option[FundSubId] = None)

  final case class ParamsUpdate(
    isOngoing: Option[Boolean] = None,
    filterByFundName: Option[Seq[String]] = None,
    filterByStatus: Option[Seq[LpProfileRequestStatus]] = None,
    selectedRequest: Option[Option[TaskRequestId]] = None
  )

  private val isOngoingKey = "isongoing"
  private val fundKey = "fund"
  private val statusKey = "status"
  private val requestKey = "request"
  private val demoFundSubId = "demo-fundsub"

  override def toUrlParams(p: LpProfileRequestPageParams): Map[String, String] = Map(
    isOngoingKey -> PageParamConverter.boolConverter.toUrlValue(p.isOngoing),
    fundKey -> PageParamConverter.stringConverter.list
      .toUrlValue(p.filterByFundName.toList),
    statusKey -> FilterByStatusConverter.list.toUrlValue(p.filterByStatus.toList),
    requestKey -> PageParamConverter
      .radixIdConverter[TaskRequestId]
      .option
      .toUrlValue(p.selectedRequest),
    demoFundSubId -> PageParamConverter.radixIdConverter[FundSubId].option.toUrlValue(p.demoFundSubId)
  ).filterNot(_._2.isEmpty)

  override def fromUrlParams(urlParams: Map[String, String]): LpProfileRequestPageParams =
    LpProfileRequestPageParams(
      PageParamConverter.boolConverter.fromUrlParams(urlParams, isOngoingKey).getOrElse(true),
      PageParamConverter.stringConverter.list
        .fromUrlParams(urlParams, fundKey)
        .getOrElse(List.empty),
      FilterByStatusConverter.list.fromUrlParams(urlParams, statusKey).getOrElse(List.empty),
      PageParamConverter
        .radixIdConverter[TaskRequestId]
        .option
        .fromUrlParams(urlParams, requestKey)
        .flatten,
      PageParamConverter.radixIdConverter[FundSubId].option.fromUrlParams(urlParams, demoFundSubId).flatten
    )

  private object FilterByStatusConverter extends PageParamConverter[LpProfileRequestStatus] {

    override def toUrlValue(p: LpProfileRequestStatus): String = p match {
      case _: LpProfileRequestStatus.RequestViewed     => "viewed"
      case _: LpProfileRequestStatus.RequestInProgress => "inprogress"
      case _: LpProfileRequestStatus.RequestCanceled   => "canceled"
      case _: LpProfileRequestStatus.RequestCompleted  => "approved"
      case _                                           => "unknown"
    }

    override def fromUrlValue(urlValue: String): Option[LpProfileRequestStatus] = {
      List(
        LpProfileRequestStatus.RequestViewed(),
        LpProfileRequestStatus.RequestInProgress(),
        LpProfileRequestStatus.RequestCanceled(),
        LpProfileRequestStatus.RequestCompleted()
      ).find(toUrlValue(_) == urlValue)
    }

  }

}
