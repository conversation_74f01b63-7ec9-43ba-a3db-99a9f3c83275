// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.auditlog

import design.anduin.components.badge.Badge
import design.anduin.components.badge.laminar.BadgeL
import design.anduin.components.tab.Tab
import design.anduin.components.tab.laminar.TabL
import design.anduin.style.tw.*

import anduin.investmententity.StableInvestmentEntityInfo
import anduin.investorprofile.investmententity.auditlog.document.DocumentAuditLog
import anduin.investorprofile.investmententity.auditlog.entity.EntityAuditLog
import anduin.investorprofile.investmententity.auditlog.profile.ProfileAuditLog
import anduin.investorprofile.models.LogTrackingData

import com.raquo.laminar.api.L.*

private[investmententity] case class AuditLog(
  stableInvestmentEntityInfo: StableInvestmentEntityInfo,
  logTrackingSignal: Signal[Option[LogTrackingData]],
  logTrackingReloadWriter: WriteBus[LogTrackingData]
) {

  private def renderBadgeL(numberOfUpdatesSignal: Signal[Option[Int]]) = {
    numberOfUpdatesSignal
      .map(_.getOrElse(0))
      .map(numberOfUpdates =>
        if (numberOfUpdates == 0) {
          emptyNode
        } else {
          BadgeL(
            Badge.Color.Danger,
            theme = Badge.Theme.Light,
            count = numberOfUpdatesSignal
          )()
        }
      )
  }

  def apply(): HtmlElement = {
    div(
      tw.pt20.px24.flex.flexCol.hPc100,
      // Header
      div(
        tw.mb16.flex.itemsCenter.flexNone,
        div(tw.heading2, "Audit log")
      ),
      // Content
      TabL(
        panels = Seq(
          Tab.Panel(
            title = {
              div(
                tw.flex.itemsCenter,
                div(tw.mr4, "Investment profile"),
                child <-- renderBadgeL(logTrackingSignal.map(_.map(_.investmentEntityLogTracking.countNewUpdate)))
              )
            },
            renderContent = (_: Tab.RenderPanelContent) => {
              EntityAuditLog(
                stableInvestmentEntityInfo,
                logTrackingReloadWriter
              )()
            }
          ),
          Tab.Panel(
            title = {
              div(
                tw.flex.itemsCenter,
                div(tw.mr4, "Profile data"),
                child <-- renderBadgeL(logTrackingSignal.map(_.map(_.masterLpProfileLogTracking.countNewUpdate)))
              )
            },
            renderContent = (_: Tab.RenderPanelContent) => {
              ProfileAuditLog(
                stableInvestmentEntityInfo,
                logTrackingSignal,
                logTrackingReloadWriter
              )()
            }
          ),
          Tab.Panel(
            title = {
              div(
                tw.flex.itemsCenter,
                div(tw.mr4, "Documents"),
                child <-- renderBadgeL(logTrackingSignal.map(_.map(_.profileDocumentLogTracking.countNewUpdate)))
              )
            },
            renderContent = (_: Tab.RenderPanelContent) => {
              DocumentAuditLog(
                stableInvestmentEntityInfo,
                logTrackingReloadWriter
              )()
            }
          )
        ),
        style = Tab.Style.Minimal().copy(isFullHeight = true)
      )().amend(tw.flexFill),
      paddingBottom.px(64)
    )
  }

}
