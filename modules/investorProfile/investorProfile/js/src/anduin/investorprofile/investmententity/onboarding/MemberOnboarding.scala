// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.onboarding

import anduin.id.investmententity.InvestmentEntityId
import anduin.investmententity.UserJoinsParams
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import anduin.investorprofile.client.InvestmentEntityEndpointClient
import com.anduin.stargazer.client.utils.ZIOUtils
import stargazer.component.routing.laminar.WithReactRouterL
import stargazer.model.routing.DynamicAuthPage

import com.raquo.laminar.api.L.*

private[investmententity] case class MemberOnboarding(investmentEntityId: InvestmentEntityId) {

  def apply(): HtmlElement = {
    WithReactRouterL { router =>
      div(
        onMountCallback { _ =>
          // first load
          ZIOUtils.runAsync(
            InvestmentEntityEndpointClient
              .investmentEntityUserJoins(UserJoinsParams(investmentEntityId))
              .map(
                _.fold(
                  ex => {
                    Toast.error("Failed to proceed the onboarding")
                    scribe.error("UserJoins failed", ex)
                  },
                  _ => router.set(DynamicAuthPage.LpProfile.InvestmentEntityProfilesPage(investmentEntityId)).runNow()
                )
              )
          )
        },

        // TODO: need a better onboarding screen
        tw.hPc100,
        "Welcome!"
      )
    }
  }

}
