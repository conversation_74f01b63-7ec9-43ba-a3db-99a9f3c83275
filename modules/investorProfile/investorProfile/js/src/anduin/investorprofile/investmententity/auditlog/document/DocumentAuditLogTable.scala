// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.auditlog.document

import design.anduin.style.CssVar
import design.anduin.style.tw.*
import design.anduin.table.laminar.TableL
import io.circe.Codec
import anduin.circe.generic.semiauto.deriveCodecWithDefaults

import anduin.investmententity.{DocumentLogItemResponse, LogItemResponse}
import anduin.investorprofile.common.UserInfoAvatar
import anduin.investorprofile.investmententity.auditlog.{AuditLogTimeCell, EmptyAuditLogState}
import anduin.investorprofile.models.ParticipantModel
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.FileId

import com.raquo.laminar.api.L.*
import anduin.model.codec.ProtoCodecs.given

private[investmententity] final case class DocumentAuditLogTable(
  auditLogsSignal: Signal[List[DocumentLogItemResponse]],
  isFetchingSignal: Signal[Boolean],
  mapFileIdToFileNameSignal: Signal[Map[FileId, String]],
  seenIndexOptSignal: Signal[Option[Int]],
  userInfoMapSignal: Signal[Map[UserId, UserInfo]]
) {

  given Codec.AsObject[ParticipantModel] = deriveCodecWithDefaults
  given Codec.AsObject[LogItemResponse] = deriveCodecWithDefaults
  given Codec.AsObject[DocumentLogItemResponse] = deriveCodecWithDefaults

  private val seenIndexSignal = seenIndexOptSignal
    .combineWith(auditLogsSignal.map(_.length))
    .mapN((seenIndex, numLogs) => seenIndex.getOrElse(numLogs))

  private val timeColumn = TableL.Column[DocumentLogItemResponse](
    title = "Time",
    field = "date",
    renderCell = renderProps => {
      AuditLogTimeCell(renderProps.data.timeStamp)().amend(
        seenIndexSignal.distinct --> Observer[Int] { seenIndex =>
          if (renderProps.data.eventOrder > seenIndex) {
            renderProps.cellComponent.getRow().getElement().style.backgroundColor =
              CssVar.toColorVar(CssVar.Color.Primary1)
          }
        }
      )
    },
    width = Option(100)
  )

  private val actorColumn = TableL.Column[DocumentLogItemResponse](
    title = "Actor",
    field = "actor",
    renderCell = renderProps => {
      div(
        child <-- userInfoMapSignal.map { userInfoMap =>
          // TODO: @phucnguyen to find a better modal for actor. Now assume actor = None is system
          renderProps.data.actor.fold(div(tw.fontSemiBold, "System")) { userId =>
            UserInfoAvatar(userInfoMap.get(userId))()
          }
        }
      )
    },
    width = Option(240)
  )

  private val ipColumn = TableL.Column[DocumentLogItemResponse](
    title = "IP",
    field = "ipAddress",
    renderCell = renderProps => div(renderProps.data.ipAddress),
    width = Option(100)
  )

  private val actionColumn = TableL.Column[DocumentLogItemResponse](
    title = "Action",
    field = "actionName",
    renderCell = renderProps => DocumentAuditLogActionCell(renderProps.data.logDetail, userInfoMapSignal)()
  )

  private val documentNameColumn = TableL.Column[DocumentLogItemResponse](
    title = "Document",
    field = "document",
    renderCell = renderProps => {
      DocumentAuditLogNameCell(mapFileIdToFileNameSignal.map(_.getOrElse(renderProps.data.documentFileId, "")))()
    },
    width = Option(240)
  )

  private def handleRowRendered(rowRenderedData: TableL.RowRenderedData[DocumentLogItemResponse]) = {
    rowRenderedData.rowComponent.getElement().classList.add(tw.group.css)
  }

  def apply(): HtmlElement = {
    TableL[DocumentLogItemResponse](
      options = TableL.Options(
        layout = TableL.Layout.FitColumns
      ),
      columns = List(
        timeColumn,
        actorColumn,
        documentNameColumn,
        actionColumn,
        ipColumn
      ),
      dataSignal = auditLogsSignal,
      loading = TableL.Loading(
        criteria = isFetchingSignal
      ),
      placeholder = TableL.Placeholder(
        criteria = _.map(_.isEmpty),
        render = _ => EmptyAuditLogState()()
      ),
      onRowRendered = Observer[TableL.RowRenderedData[DocumentLogItemResponse]](handleRowRendered)
    ).amend(maxHeight.px := 500)
  }

}
