// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.documents.modals

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL}
import design.anduin.components.toast.Toast
import org.scalajs.dom.MouseEvent

import anduin.id.lpprofile.LpProfileId
import anduin.investorprofile.InvestorProfileDocumentCommons.{ProfileDocumentInfo, RemoveProfileDocumentsParams}
import anduin.investorprofile.client.LpProfileEndpointClient
import anduin.scalajs.pluralize.Pluralize
import com.anduin.stargazer.client.utils.ZIOUtils

import com.raquo.laminar.api.L.*

private[investmententity] final case class DeleteDocumentModal(
  documents: Seq[ProfileDocumentInfo],
  lpProfileId: LpProfileId,
  onDelete: Observer[Unit],
  onCancel: Observer[Unit]
) {

  private def onDeleteDocument(): Unit = {
    ZIOUtils.runAsync(
      LpProfileEndpointClient
        .removeLpProfileDocuments(
          RemoveProfileDocumentsParams(
            profileId = lpProfileId,
            lpProfileDocumentIds = documents.map(_.lpProfileDocumentId).toSet
          )
        )
        .map(
          _.fold(
            _ => Toast.error("Fail to delete document. Please try again"),
            _ => {
              onDelete.onNext(())
              Toast.success(s"${Pluralize(
                  "document",
                  documents.size,
                  inclusive = true
                )} deleted successfully")
            }
          )
        )
    )
  }

  def apply(): HtmlElement = {
    div(
      ModalBodyL(
        div(s"Are you sure you want to delete ${Pluralize("this", count = documents.size)} ${Pluralize(
            "document",
            count = documents.size,
            inclusive = true
          )}?")
      ),
      ModalFooterWCancelL(
        cancel = onCancel.contramap(_ => ())
      )(
        ButtonL(
          style = ButtonL.Style.Full(
            color = ButtonL.Color.Danger
          ),
          onClick = Observer[MouseEvent] { _ =>
            onDeleteDocument()
            onCancel.onNext(())
          }
        )("Delete")
      )
    )
  }

}
