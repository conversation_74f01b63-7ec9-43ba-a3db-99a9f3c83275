// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.auditlog

import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.style.tw.*

import com.raquo.laminar.api.L.*

private[investmententity] case class EmptyAuditLogState() {

  def apply(): HtmlElement = {
    div(
      tw.wPc100.flex,
      NonIdealStateL(
        icon = {
          svg.svg(
            svg.width := "50",
            svg.height := "48",
            svg.viewBox := "0 0 50 48",
            svg.fill := "none",
            svg.path(
              svg.d :=
                """
                  |M30.1038 4.51953L37.9938 12.5051L31.2912 14.3267C31.2912 14.3267 28.8241 10.0267 28.8738
                  |9.87713C28.9235 9.72753 30.1038 4.51953 30.1038 4.51953Z
                  |""".stripMargin,
              svg.fill := "#48AFF0"
            ),
            svg.path(
              svg.d :=
                """
                  |M36.8421 12H32.1053C31.2329 12 30.5263 11.284 30.5263 10.4V5.6C30.5263 4.716 29.8197 4 28.9474
                  |4H11.5789C10.7066 4 10 4.716 10 5.6V39.2C10 40.084 10.7066 40.8 11.5789 40.8H36.8421C37.7145
                  |40.8 38.4211 40.084 38.4211 39.2V13.6C38.4211 12.716 37.7145 12 36.8421 12Z
                  |""".stripMargin,
              svg.fill := "#DFE9F0"
            ),
            svg.path(
              svg.d := "M17.8945 25.6016H28.9472",
              svg.stroke := "white",
              svg.strokeWidth := "2",
              svg.strokeMiterLimit := "10",
              svg.strokeLineCap := "square"
            ),
            svg.path(
              svg.d := "M23.0264 31.6016H30.1316",
              svg.stroke := "white",
              svg.strokeWidth := "3",
              svg.strokeMiterLimit := "10",
              svg.strokeLineCap := "square"
            ),
            svg.path(
              svg.d := "M17.8945 30.3984H22.138",
              svg.stroke := "white",
              svg.strokeWidth := "2",
              svg.strokeMiterLimit := "10",
              svg.strokeLineCap := "square"
            ),
            svg.path(
              svg.d :=
                """
                  |M28.4211 38.0016C31.764 38.0016 34.474 35.2555 34.474 31.868C34.474 28.4805 31.764 25.7344 28.4211
                  |25.7344C25.0781 25.7344 22.3682 28.4805 22.3682 31.868C22.3682 35.2555 25.0781 38.0016 28.4211 38.0016Z
                  |""".stripMargin,
              svg.stroke := "#137CBD",
              svg.strokeWidth := "3",
              svg.strokeMiterLimit := "10"
            ),
            svg.path(
              svg.d :=
                """
                  |M37.0021 42.8248L32.041 37.7976L34.2736 35.5352L39.2347 40.5624C39.8513 41.1872 39.8513 42.2 39.2347
                  |42.8248C38.6181 43.4496 37.6186 43.4496 37.0021 42.8248Z
                  |""".stripMargin,
              svg.fill := "#137CBD"
            ),
            svg.path(
              svg.d := "M17.8945 16H30.5261",
              svg.stroke := "white",
              svg.strokeWidth := "2",
              svg.strokeMiterLimit := "10",
              svg.strokeLineCap := "square"
            ),
            svg.path(
              svg.d := "M17.8945 20.8008H30.5261",
              svg.stroke := "white",
              svg.strokeWidth := "2",
              svg.strokeMiterLimit := "10",
              svg.strokeLineCap := "square"
            )
          )
        },
        title = "No results found",
        description = "Adjust the filters to try again.",
        action = emptyNode
      )()
    )
  }

}
