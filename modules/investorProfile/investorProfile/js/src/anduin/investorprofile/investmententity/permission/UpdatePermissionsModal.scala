// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.permission

import anduin.investorprofile.investmententity.permission.MemberTable.InvestmentEntityUserWithInfo
import anduin.protobuf.investorprofile.investmententity.InvestmentEntityPermissionLevel
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL}
import design.anduin.style.tw.*
import org.scalajs.dom

import com.raquo.laminar.api.L.*

private[investmententity] case class UpdatePermissionsModal(
  member: InvestmentEntityUserWithInfo,
  onClose: Observer[Unit],
  onUpdatePermission: Observer[InvestmentEntityPermissionLevel]
) {

  private val permissionVar = Var(Option(member.entityInfo.permissionLevel))

  def apply(): HtmlElement = {
    div(
      ModalBodyL(
        div(
          tw.mb16,
          "Update ",
          span(tw.fontBold, member.userInfo.fullNameString),
          "'s permissions"
        ),
        MemberPermissionDropdown(
          permission = permissionVar.signal,
          onChange = Observer[InvestmentEntityPermissionLevel] { p =>
            permissionVar.set(Option(p))
          }
        )()
      ),
      ModalFooterWCancelL(
        cancel = onClose
      )(
        ButtonL(
          isDisabled = permissionVar.signal.map(_.contains(member.entityInfo.permissionLevel)),
          style = ButtonL.Style.Full(color = ButtonL.Color.Primary),
          onClick = Observer[dom.MouseEvent] { _ =>
            onUpdatePermission.onNext(permissionVar.now().getOrElse(member.entityInfo.permissionLevel))
            onClose.onNext(())
          }
        )("Update")
      )
    )
  }

}
