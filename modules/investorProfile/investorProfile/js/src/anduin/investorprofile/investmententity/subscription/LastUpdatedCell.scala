// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.subscription

import java.time.Instant
import java.time.format.DateTimeFormatter

import anduin.utils.DateTimeUtils

import com.raquo.laminar.api.L.*

private[investmententity] case class LastUpdatedCell(lastUpdatedTimeOpt: Option[Instant]) {

  private val timeZone = DateTimeUtils.defaultTimezone

  def apply(): HtmlElement = {
    lastUpdatedTimeOpt.fold[HtmlElement](div())(time =>
      div(
        DateTimeUtils.formatInstant(time, DateTimeFormatter.ofPattern("MMMM dd,yyyy"))(
          using timeZone
        )
      )
    )
  }

}
