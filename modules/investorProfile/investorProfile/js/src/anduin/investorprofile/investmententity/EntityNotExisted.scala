// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.style.tw.*
import japgolly.scalajs.react.extra.router.RouterCtl
import stargazer.model.routing.{Page, StaticAuthPage}

import com.raquo.laminar.api.L.*

final case class EntityNotExisted(
  router: RouterCtl[Page]
) {

  def apply(): HtmlElement = {
    div(
      tw.flexCol.textCenter,
      img(
        src := "/web/gondor/images/illustrations/illus-file-search.svg",
        alt := "No access illustration"
      ),
      div(
        tw.my16,
        div(
          tw.text17.leading28.fontSemiBold,
          "This investment profile no longer exists"
        ),
        div(
          tw.textGray7,
          "Please go back to the dashboard or contact an administrator for help"
        ),
        div(
          tw.flex.itemsCenter.justifyCenter.mt16,
          ButtonL(
            style = ButtonL.Style.Full(
              color = ButtonL.Color.Primary,
              icon = Some(Icon.Glyph.RollBack)
            ),
            onClick = Observer { _ =>
              router.set(StaticAuthPage.CommonEntryPoint).runNow()
            }
          )("Back to dashboard")
        )
      )
    )
  }

}
