// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.documents.tablecells

import java.time.ZoneId
import java.time.format.DateTimeFormatter

import design.anduin.style.tw.*
import design.anduin.table.laminar.TableL

import anduin.investorprofile.InvestorProfileDocumentCommons.ProfileDocumentInfo
import anduin.utils.DateTimeUtils

import com.raquo.laminar.api.L.*

private[investmententity] final case class CreatedAtCell(
  renderProps: TableL.RenderCell[ProfileDocumentInfo]
) {

  def apply(): HtmlElement = {
    val createdAtInstant = renderProps.data.createdAt
    div(
      div(
        tw.textBody.textGray8.mb8,
        createdAtInstant.map(time =>
          DateTimeUtils.formatInstant(time, DateTimeFormatter.ofPattern("MMMM dd, yyyy"))(
            using ZoneId.systemDefault()
          )
        )
      ),
      div(
        tw.textSmall.textGray7,
        createdAtInstant.map(time =>
          DateTimeUtils.formatInstant(time, DateTimeFormatter.ofPattern("HH:mm:ss"))(
            using ZoneId.systemDefault()
          )
        )
      )
    )
  }

}
