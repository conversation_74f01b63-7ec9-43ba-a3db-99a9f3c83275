// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.profile

import anduin.forms.utils.FormValidationUtils
import anduin.forms.utils.FormValidationUtils.{FieldAlert, FieldIDTrait}
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.responsive.ScreenWidth
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import org.scalajs.dom

import design.anduin.components.portal.PortalPosition
import design.anduin.components.tooltip.laminar.TooltipL

import com.raquo.laminar.api.L.*

private[investmententity] final case class MissingFieldBlock(
  fieldAlert: FieldAlert,
  screenWidthSignal: Signal[Option[ScreenWidth]],
  onJumpToField: Observer[FieldIDTrait]
) {

  private def renderBlock(
    icon: Icon.Name,
    iconColor: HtmlMod,
    text: String,
    buttonColor: HtmlMod,
    fontWeight: HtmlMod,
    targetFieldID: FieldIDTrait
  ) = {
    div(
      ComponentUtils.testIdL(MissingFieldBlock, "MissingFieldsSection"),
      tw.flex.itemsCenter,
      div(
        tw.flex.itemsCenter,
        div(
          tw.mr8,
          iconColor,
          TooltipL(
            renderContent = _.amend("There are empty fields"),
            renderTarget = IconL(name = Val(icon))(),
            position = PortalPosition.BottomCenter
          )()
        ),
        div(tw.mr20, text)
      ),
      button(
        tw.flex.itemsCenter,
        buttonColor,
        child <-- screenWidthSignal.map { screenWidth =>
          val label = screenWidth
            .flatMap { screenWidth =>
              if (screenWidth.minWidth <= ScreenWidth.Small.minWidth) {
                Option("View")
              } else {
                None
              }
            }
            .getOrElse("Jump to next empty field")
          div(
            tw.mr8,
            fontWeight,
            label
          )
        },
        IconL(Val(Icon.Glyph.ArrowRight))(),
        onClick --> Observer[dom.MouseEvent] { _ =>
          onJumpToField.onNext(targetFieldID)
        }
      )
    )
  }

  def apply(): Node = {
    fieldAlert match {
      case FormValidationUtils.FieldError(fieldId) =>
        renderBlock(
          Icon.Glyph.Error,
          tw.textDanger4,
          "There are missing required fields",
          tw.textDanger5,
          tw.fontSemiBold,
          fieldId
        )
      case FormValidationUtils.FieldWarning(fieldId) =>
        renderBlock(
          Icon.Glyph.Warning,
          tw.textWarning4,
          "There are empty fields",
          tw.textWarning5,
          tw.fontNormal,
          fieldId
        )
      case FormValidationUtils.NoAlert =>
        emptyNode
    }
  }

}
