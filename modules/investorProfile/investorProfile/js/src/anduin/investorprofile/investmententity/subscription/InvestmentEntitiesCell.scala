// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.subscription

import anduin.investmententity.subscription.LinkedEntityInfo
import anduin.investorprofile.models.SubscriptionModel
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.icon.Icon
import design.anduin.components.text.laminar.TruncateL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*

import com.raquo.laminar.api.L.*

private[investmententity] final case class InvestmentEntitiesCell(subscription: SubscriptionModel) {

  private def renderLinkedEntity(entity: LinkedEntityInfo) = {
    div(
      tw.flex.itemsCenter.mb4,
      TooltipL(
        renderTarget = div(
          tw.textPrimary5.bgPrimary1.hover(tw.textPrimary3.bgPrimary4),
          tw.roundedFull.p4,
          IconL(name = Val(Icon.Glyph.Link), size = Icon.Size.Custom(10))()
        ),
        renderContent = _.amend(
          "Investment profile already",
          br(),
          "linked to this subscription"
        )
      )(),
      div(tw.pl4.flexFill, TruncateL(target = div(entity.investmentEntityName))())
    )
  }

  private def renderEntityFromSubscription(subscription: SubscriptionModel) = {
    if (subscription.entityName.isEmpty) {
      emptyNode
    } else {
      div(
        tw.flex.itemsCenter,
        TooltipL(
          renderTarget = div(
            tw.textGray7.bgGray3.hover(tw.textGray0.bgGray7),
            tw.roundedFull.p4,
            IconL(name = Val(Icon.Glyph.FileText), size = Icon.Size.Custom(10))()
          ),
          renderContent = _.amend(
            "Investment entity used",
            br(),
            "in the subscription form"
          )
        )(),
        div(tw.pl4.flexFill, TruncateL(target = div(subscription.entityName))())
      )
    }
  }

  def apply(): HtmlElement = {
    div(
      subscription.linkedEntities.sortBy(_.investmentEntityName).map(renderLinkedEntity),
      renderEntityFromSubscription(subscription)
    )
  }

}
