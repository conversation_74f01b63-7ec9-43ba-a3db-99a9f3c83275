// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.documents.tablecells

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.wrapper.laminar.WrapperL
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import design.anduin.table.laminar.TableL

import anduin.file.explorer.{FileViewer, UrlLoader}
import anduin.frontend.AirStreamUtils
import anduin.id.lpprofile.LpProfileId
import anduin.investorprofile.InvestorProfileDocumentCommons.{
  GetProfileDocumentViewUrlParams,
  ProfileDocumentInfo,
  UpdateProfileDocumentParams
}
import anduin.investorprofile.client.LpProfileEndpointClient
import anduin.util.FilenameUtils
import com.anduin.stargazer.endpoints.FileInfo

import com.raquo.laminar.api.L.*
import japgolly.scalajs.react.vdom.html_<^.*
import org.scalajs.dom.*

private[investmententity] final case class NameCell(
  renderProps: TableL.RenderCell[ProfileDocumentInfo],
  lpProfileId: LpProfileId,
  isBeingRenameSignal: Signal[Boolean],
  onSelectDocToRename: Observer[Option[ProfileDocumentInfo]],
  reFetchTable: Observer[Unit]
) {

  private val profileDocument = renderProps.data

  private val fileNameExtension = FilenameUtils.getExtension(profileDocument.documentEditableInfo.fileName)
  private val initialName = FilenameUtils.getName(profileDocument.documentEditableInfo.fileName)
  private val newNameVar = Var[String](initialName)

  private val renameFileEventBus = new EventBus[Unit]

  private def renameFile(newName: String) = {
    AirStreamUtils.taskToStreamDEPRECATED(
      for {
        _ <- LpProfileEndpointClient
          .updateLpProfileDocument(
            UpdateProfileDocumentParams(
              profileId = lpProfileId,
              profileDocument.lpProfileDocumentId,
              newDocumentInfo = profileDocument.documentEditableInfo
                .copy(fileName = FilenameUtils.getFilename(newName, fileNameExtension))
            )
          )
          .map(
            _.fold(
              _ => Toast.error("Something went wrong! Please try again"),
              _ => {
                Toast.success("Document renamed")
                reFetchTable.onNext(())
              }
            )
          )
      } yield ()
    )
  }

  private def renderViewMode() = {
    val fileInfo = FileInfo(
      itemId = profileDocument.fileId,
      name = profileDocument.documentEditableInfo.fileName,
      None,
      None,
      None,
      None,
      None,
      None,
      None,
      None,
      None
    )
    WrapperL(
      FileViewer(
        file = Right(fileInfo),
        renderTarget = _ =>
          open => {
            <.span(
              tw.hover(tw.underline),
              ^.onClick --> open,
              <.div(
                tw.inlineFlex,
                TooltipR(
                  renderTarget = <.div(
                    tw.flex.mr12.alignMiddle,
                    <.div(
                      tw.mr12.flex.itemsCenter,
                      IconR(name = Icon.File.ByExtension(profileDocument.documentEditableInfo.fileName))()
                    ),
                    <.div(
                      tw.textLeft.text13.lineClamp2.whitespacePreLine,
                      profileDocument.documentEditableInfo.fileName
                    )
                  ),
                  renderContent = _("Preview document")
                )()
              )
            )
          },
        getViewUrl = LpProfileEndpointClient
          .getLpProfileDocumentViewUrl(
            GetProfileDocumentViewUrlParams(renderProps.data.lpProfileDocumentId)
          )
          .map(
            _.map(res =>
              UrlLoader.UrlData(
                res.url,
                None,
                res.extensionOpt
              )
            )
          ),
        getDownloadUrl = None,
        getShortcutUrl = None
      )()
    )
  }

  private def onRename() = {
    renameFileEventBus.emit(())
    onSelectDocToRename.onNext(None)
  }

  private def onCancelRename() = {
    newNameVar.set(initialName)
    onSelectDocToRename.onNext(None)
  }

  private def renderEditMode() = {
    div(
      tw.flex.itemsCenter,
      div(
        tw.mr4.flexFill,
        TextBoxL(
          value = newNameVar.signal,
          onChange = Observer[String] { newName =>
            val filterNewName = FilenameUtils.updateFileName(newName)
            newNameVar.set(filterNewName)
          },
          isAutoFocus = true,
          onFocus = Observer[FocusEvent] {
            _.target match {
              case ele: HTMLInputElement => ele.select()
              case _                     => ()
            }
          },
          onKeyUp = Observer[KeyboardEvent] { e =>
            e.keyCode match {
              case KeyCode.Enter  => onRename()
              case KeyCode.Escape => onCancelRename()
            }
          }
        )()
      ),
      TooltipL(
        renderTarget = div(
          tw.mr4,
          ButtonL(
            style = ButtonL.Style.Full(
              color = ButtonL.Color.Primary,
              icon = Some(Icon.Glyph.Check)
            ),
            onClick = Observer[MouseEvent] { _ => onRename() },
            isDisabled = newNameVar.signal.map(_.equals(initialName))
          )()
        ),
        renderContent = _.amend("Or press Enter to rename")
      )(),
      TooltipL(
        renderTarget = ButtonL(
          style = ButtonL.Style.Ghost(
            icon = Some(Icon.Glyph.CrossSmall),
            color = ButtonL.Color.Danger
          ),
          onClick = Observer[MouseEvent] { _ => onCancelRename() }
        )(),
        renderContent = _.amend("Or press Esc to cancel")
      )()
    )
  }

  def apply(): HtmlElement = {

    div(
      renameFileEventBus.events.withCurrentValueOf(newNameVar).flatMapSwitch(renameFile) --> Observer.empty,
      onClick --> Observer[MouseEvent] { _.stopPropagation() },
      child <-- isBeingRenameSignal.map {
        if (_) {
          renderEditMode()
        } else {
          renderViewMode()
        }
      }
    )
  }

}
