// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.auditlog.profile

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.divider.laminar.DividerL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.style.tw.*

import anduin.investorprofile.investmententity.auditlog.AuditLogItem
import anduin.investorprofile.models.ProfileFieldModel

import com.raquo.laminar.api.L.*

private[investmententity] case class ProfileFieldFilter(
  allFieldLabelPathsSignal: Signal[List[ProfileFieldModel]],
  selectedFieldsSignal: Signal[List[ProfileFieldModel]],
  onSelectField: Observer[ProfileFieldModel],
  onUnselectField: Observer[ProfileFieldModel],
  onSelectAllFields: Observer[Unit],
  onUnselectAllFields: Observer[Unit]
) {

  private val AllFieldsText = "All fields"
  private val keywordVar = Var("")

  private val filterProfileFieldsSignal = allFieldLabelPathsSignal.signal
    .combineWith(keywordVar.signal)
    .mapN { (allFieldLabelPaths, keyword) =>
      if (keyword.isEmpty) {
        allFieldLabelPaths
      } else {
        allFieldLabelPaths.filter(_.path.exists(_.toLowerCase().contains(keyword.toLowerCase)))
      }
    }

  private val hasProfileFieldsSignal = filterProfileFieldsSignal.map(_.nonEmpty)

  private def groupFilterFieldLabelPaths(paths: List[ProfileFieldModel]) = {
    paths.reverse.foldLeft[List[(String, List[ProfileFieldModel])]](List.empty)((groupedFieldData, field) =>
      groupedFieldData.headOption.fold((field.path.head, List(field)) +: groupedFieldData)(groupedFieldDataHead => {
        if (groupedFieldDataHead._1 == field.path.head) {
          (field.path.head, field +: groupedFieldDataHead._2) +: groupedFieldData.drop(1)
        } else {
          (field.path.head, List(field)) +: groupedFieldData
        }
      })
    )
  }

  private def renderContent(allFieldLabelPaths: List[ProfileFieldModel]) = {
    div(
      tw.py8.px4.overflowXAuto,
      maxWidth := "340px",
      div(
        div(
          tw.mb8,
          TextBoxL(
            icon = Option(Icon.Glyph.Search),
            placeholder = "Search",
            value = keywordVar.signal,
            onChange = keywordVar.writer
          )()
        ),
        div(
          tw.py8,
          AuditLogItem(
            renderContent = () => div(AllFieldsText),
            isSelectedSignal = selectedFieldsSignal.map(_.length == allFieldLabelPaths.length),
            onItemClick = Observer[Boolean] {
              if (_) onUnselectAllFields.onNext(()) else onSelectAllFields.onNext(())
            }
          )()
        ),
        child.maybe <-- hasProfileFieldsSignal.map(Option.when(_)(DividerL()()))
      ),
      div(
        tw.overflowAuto.spaceY4,
        maxHeight := "360px",
        children <-- filterProfileFieldsSignal.map { paths =>
          groupFilterFieldLabelPaths(paths).map { case (title, profileFieldSubPaths) =>
            div(
              div(tw.textGray7, title.toUpperCase()), // Title
              profileFieldSubPaths.map(profileFieldPath =>
                AuditLogItem(
                  isSelectedSignal = selectedFieldsSignal.map(_.contains(profileFieldPath)),
                  renderContent = () => div(profileFieldPath.path.drop(1).filter(_.nonEmpty).mkString(" > ")),
                  onItemClick = Observer[Boolean] {
                    if (_) {
                      onUnselectField.onNext(profileFieldPath)
                    } else {
                      onSelectField.onNext(profileFieldPath)
                    }
                  }
                )()
              ),
              DividerL()()
            )
          }
        }
      )
    )
  }

  def apply(): HtmlElement = {
    div(
      child <-- allFieldLabelPathsSignal.signal.map { allFieldLabelPaths =>
        PopoverL(
          position = PortalPosition.BottomLeft,
          renderContent = _ => {
            renderContent(allFieldLabelPaths)
          },
          renderTarget = (open, isOpened) => {
            ButtonL(
              style = ButtonL.Style.Minimal(
                height = ButtonL.Height.Fix24,
                isSelected = isOpened
              ),
              onClick = open.contramap(_ => ())
            )(
              div(
                tw.flex.itemsCenter.justifyCenter,
                child <-- selectedFieldsSignal.map { selectedFields =>
                  val selectedFieldsCount = selectedFields.length
                  if (selectedFields.isEmpty || selectedFieldsCount == allFieldLabelPaths.length) {
                    AllFieldsText
                  } else {
                    s"$selectedFieldsCount selected"
                  }
                },
                div(
                  tw.ml4.relative,
                  IconL(name = Val(Icon.Glyph.CaretDown))()
                )
              )
            )
          }
        )()
      }
    )
  }

}
