// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.auditlog.document

import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.text.laminar.TruncateL
import design.anduin.style.tw.*

import com.raquo.laminar.api.L.*

private[investmententity] final case class DocumentAuditLogNameCell(
  docName: Signal[String]
) {

  def apply(): HtmlElement = {
    div(
      tw.flex.itemsCenter,
      children <-- docName.map { docName =>
        Seq(
          div(
            tw.mr12.flex.itemsCenter,
            IconL(Val(Icon.File.ByExtension(docName)))()
          ),
          TruncateL(
            lineClamp = Some(3),
            target = div(
              tw.fontSemiBold.whitespacePreWrap,
              docName
            )
          )()
        )
      }
    )
  }

}
