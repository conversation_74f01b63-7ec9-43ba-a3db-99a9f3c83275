// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.auditlog.document

import java.time.Instant
import java.time.temporal.ChronoUnit

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.dropdown.laminar.DropdownL
import design.anduin.style.tw.*
import org.scalajs.dom.MouseEvent

import anduin.investmententity.auditlog.{
  ActorFilter,
  DocumentAuditLogDocumentFilter,
  DocumentAuditLogEventTypeFilter,
  TimestampFilter
}
import anduin.investmententity.{GetDocumentAuditLogDataParams, InvestmentEntityGetAllUsersResponse}
import anduin.investorprofile.InvestorProfileDocumentCommons.ProfileDocumentInfo
import anduin.investorprofile.investmententity.auditlog.{AuditLogActivityFilter, AuditLogActorFilter}
import anduin.investorprofile.models.{ActorModel, TimeFilterModel}
import anduin.protobuf.investorprofile.auditlog.DocumentAuditLogEventType

import com.raquo.laminar.api.L.*

private[document] final case class DocumentAuditLogFilterBar(
  documentsSignal: Signal[List[ProfileDocumentInfo]],
  updateFilter: (GetDocumentAuditLogDataParams => GetDocumentAuditLogDataParams) => Unit,
  userInfoMappingSignal: Signal[InvestmentEntityGetAllUsersResponse],
  onChangeActor: Observer[Unit]
) {

  private val allEventType = DocumentAuditLogEventType.values.toList
  private val allActorsVar = Var[List[ActorModel]](List.empty)
  private val allDocumentsVar = Var[List[ProfileDocumentInfo]](List.empty)

  private val selectedEventTypesVar = Var[List[DocumentAuditLogEventType]](allEventType)
  private val selectedDocumentVar = Var[List[ProfileDocumentInfo]](List.empty)
  private val selectedActorVar = Var[List[ActorModel]](List.empty)
  private val dateRangeFilterVar = Var(Some(TimeFilterModel.defaultTimeFilterValue))
  private val isActorChangedVar = Var(false)
  private val isDocumentChangedVar = Var(false)

  private def makeFilters() = {
    val actors = selectedActorVar.now().map(_.userId)
    val actorsFilter = ActorFilter(actors)
    val eventTypeFilter = DocumentAuditLogEventTypeFilter(selectedEventTypesVar.now())
    val documentFilter = DocumentAuditLogDocumentFilter(selectedDocumentVar.now().map(_.fileId))

    List(actorsFilter) ++ List(eventTypeFilter) ++ List(documentFilter)
  }

  private def handleClearFilters() = {
    selectedActorVar.set(allActorsVar.now())
    selectedDocumentVar.set(allDocumentsVar.now())
    selectedEventTypesVar.set(allEventType)
    dateRangeFilterVar.set(Some(TimeFilterModel.defaultTimeFilterValue))
  }

  private def getActivityText(eventType: DocumentAuditLogEventType) = {
    eventType match {
      case DocumentAuditLogEventType.UPLOAD_DOCUMENT          => "Upload document"
      case DocumentAuditLogEventType.ADD_NOTE                 => "Add note to document"
      case DocumentAuditLogEventType.SET_EXPIRATION_DATE      => "Set document's expiration date"
      case DocumentAuditLogEventType.DELETE_DOCUMENT          => "Delete document"
      case DocumentAuditLogEventType.EDIT_DOCUMENT_NAME       => "Rename document"
      case DocumentAuditLogEventType.EDIT_DOCUMENT_NOTE       => "Edit document's note"
      case DocumentAuditLogEventType.EDIT_DOCUMENT_EXPIRATION => "Change document's expiration"
      case DocumentAuditLogEventType.DOWNLOAD_DOCUMENT        => "Download document"
      case DocumentAuditLogEventType.SAVE_FROM_FUND           => "Save document from fund"
      case DocumentAuditLogEventType.Unrecognized(_)          => ""
    }
  }

  private def renderActivity(): HtmlElement = {
    div(
      tw.mr8.flex.itemsCenter,
      div("Activity:"),
      div(
        AuditLogActivityFilter[DocumentAuditLogEventType](
          activities = allEventType,
          selectedActivitiesSignal = selectedEventTypesVar.signal,
          onSelectActivity = Observer[DocumentAuditLogEventType] { event =>
            selectedEventTypesVar.update(_.appended(event).distinct)
          },
          onUnselectActivity = Observer[DocumentAuditLogEventType] { event =>
            selectedEventTypesVar.update(_.filter(!_.equals(event)))
          },
          onSelectAllActivity = Observer[Unit] { _ =>
            selectedEventTypesVar.set(allEventType)
          },
          onUnselectAllActivity = Observer[Unit] { _ =>
            selectedEventTypesVar.set(List.empty)
          },
          getActivityText = getActivityText
        )()
      )
    )
  }

  private def renderDocumentFilter(): HtmlElement = {
    div(
      tw.mr8.flex.itemsCenter,
      div("Documents:"),
      FilterByDocument(
        documentsSignal = allDocumentsVar.signal,
        selectedDocumentSignal = selectedDocumentVar.signal,
        onSelectDocument = Observer[ProfileDocumentInfo] { doc =>
          selectedDocumentVar.update(_.appended(doc).distinct)
          isDocumentChangedVar.update(!_)
        },
        onUnselectDocument = Observer[ProfileDocumentInfo] { doc =>
          selectedDocumentVar.update(_.filter(!_.equals(doc)))
          isDocumentChangedVar.update(!_)
        },
        onSelectAllDocument = Observer[Unit] { _ =>
          selectedDocumentVar.set(allDocumentsVar.now())
          isDocumentChangedVar.update(!_)
        },
        onUnselectAllDocument = Observer[Unit] { _ =>
          selectedDocumentVar.set(List.empty)
          isDocumentChangedVar.update(!_)
        }
      )()
    )
  }

  private def renderActor(): HtmlElement = {
    div(
      tw.mr8.flex.itemsCenter,
      div("Actor:"),
      AuditLogActorFilter(
        allActorsSignal = allActorsVar.signal,
        selectedActorsSignal = selectedActorVar.signal,
        onSelectActor = Observer[ActorModel] { selectedActor =>
          selectedActorVar.update(_.appended(selectedActor).distinct)
          isActorChangedVar.update(!_)
        },
        onUnselectActor = Observer[ActorModel] { unSelectedActor =>
          selectedActorVar.update(_.filter(!_.equals(unSelectedActor)))
          isActorChangedVar.update(!_)
        },
        onSelectAllActors = Observer[Unit] { _ =>
          selectedActorVar.set(allActorsVar.now())
          isActorChangedVar.update(!_)
        },
        onUnselectAllActors = Observer[Unit] { _ =>
          selectedActorVar.set(List.empty)
          isActorChangedVar.update(!_)
        }
      )()
    )
  }

  private def renderDateRange(): HtmlElement = {
    div(
      DropdownL[TimeFilterModel](
        items = TimeFilterModel.TimeFilters.map(item => DropdownL.Item(value = item)),
        target = DropdownL.Target(
          appearance = DropdownL.Appearance.Minimal(height = ButtonL.Height.Fix24)
        ),
        value = dateRangeFilterVar.signal,
        valueToString = _.text,
        onChange = Observer[TimeFilterModel] { timeFilter => dateRangeFilterVar.set(Some(timeFilter)) }
      )()
    )
  }

  def apply(): HtmlElement = {
    div(
      tw.flex.itemsCenter.textSmall.textGray7,
      userInfoMappingSignal.distinct --> Observer[InvestmentEntityGetAllUsersResponse] { userInfoMapping =>
        val allActors = AuditLogActorFilter.getAllActorModel(userInfoMapping)
        allActorsVar.set(allActors)
        selectedActorVar.set(allActors)
      },
      documentsSignal.distinct --> Observer[List[ProfileDocumentInfo]] { documents =>
        allDocumentsVar.set(documents)
        selectedDocumentVar.set(documents)
      },
      renderDocumentFilter(),
      renderActivity(),
      renderActor(),
      renderDateRange(),
      child.maybe <-- selectedActorVar.signal
        .combineWith(
          selectedEventTypesVar.signal,
          dateRangeFilterVar.signal,
          selectedDocumentVar.signal
        )
        .distinct
        .map { case (actorFilter, eventTypeFilter, timeFilter, documentFilter) =>
          val isDefaultActorFilter =
            actorFilter.isEmpty || actorFilter.length == allActorsVar.now().length
          val isDefaultEventTypeFilter = eventTypeFilter.length == allEventType.length
          val isDefaultDateRangeFilter = timeFilter.contains(TimeFilterModel.defaultTimeFilterValue)
          val isDefaultDocumentFilter = documentFilter.length == allDocumentsVar.now().length
          Option.unless(
            isDefaultActorFilter && isDefaultEventTypeFilter && isDefaultDateRangeFilter && isDefaultDocumentFilter
          ) {
            ButtonL(
              style = ButtonL.Style.Minimal(color = ButtonL.Color.Primary, height = ButtonL.Height.Fix24),
              onClick = Observer[MouseEvent] { _ =>
                handleClearFilters()
              }
            )("Clear filters")
          }
        },
      isActorChangedVar.signal.changes --> Observer[Boolean] { _ =>
        onChangeActor.onNext(())
        updateFilter(_.copy(offset = 0, filters = makeFilters()))
      },
      isDocumentChangedVar.signal.changes --> Observer[Boolean] { _ =>
        updateFilter(_.copy(filters = makeFilters()))
      },
      selectedEventTypesVar.signal.changes --> Observer[List[DocumentAuditLogEventType]] { _ =>
        updateFilter(_.copy(filters = makeFilters()))
      },
      dateRangeFilterVar.signal.changes --> Observer[Option[TimeFilterModel]] { timeFilterOpt =>
        updateFilter(
          _.copy(
            timestampFilterOpt = timeFilterOpt.map { time =>
              TimestampFilter(from = Instant.now().minus(time.numDays.toLong, ChronoUnit.DAYS), to = Instant.now())
            }
          )
        )
      }
    )
  }

}
