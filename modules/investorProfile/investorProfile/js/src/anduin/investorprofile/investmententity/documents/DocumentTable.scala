// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.documents

import java.time.{Instant, LocalDate}

import design.anduin.table.laminar.{SelectionColumnL, TableL}
import io.circe.Codec
import anduin.circe.generic.semiauto.deriveCodecWithDefaults

import anduin.id.lpprofile.LpProfileId
import anduin.investorprofile.InvestorProfileDocumentCommons.ProfileDocumentInfo

import com.raquo.laminar.api.L.*
import anduin.investorprofile.investmententity.documents.tablecells.*
import anduin.model.codec.ProtoCodecs.given

private[investmententity] final case class DocumentTable(
  isFetchingSignal: Signal[Boolean],
  lpProfileId: LpProfileId,
  profileDocumentsSignal: Signal[List[ProfileDocumentInfo]],
  selectedDocumentSignal: Signal[List[ProfileDocumentInfo]],
  beingRenamedDocumentSignal: Signal[Option[ProfileDocumentInfo]],
  renderEmptyState: () => HtmlElement,
  onRowsSelected: Observer[List[ProfileDocumentInfo]],
  onSelectDocToRename: Observer[Option[ProfileDocumentInfo]],
  onUnselectDocument: Observer[ProfileDocumentInfo],
  reFetchTable: Observer[Unit]
) {

  private given Codec.AsObject[ProfileDocumentInfo] = deriveCodecWithDefaults

  private def sortByExpirationDate(sorter: TableL.ColumnSorter[ProfileDocumentInfo]): Double = {
    val firstDate = sorter.a.flatMap(_.documentEditableInfo.expirationDateOpt).getOrElse(LocalDate.MIN)
    val secondDate = sorter.b.flatMap(_.documentEditableInfo.expirationDateOpt).getOrElse(LocalDate.MIN)
    firstDate.compareTo(secondDate).toDouble
  }

  private def sortByName(sorter: TableL.ColumnSorter[ProfileDocumentInfo]): Double = {
    val firstName = sorter.a.map(_.documentEditableInfo.fileName).getOrElse("")
    val secondName = sorter.b.map(_.documentEditableInfo.fileName).getOrElse("")
    firstName.compareTo(secondName).toDouble
  }

  private def sortByNote(sorter: TableL.ColumnSorter[ProfileDocumentInfo]): Double = {
    val firstNote = sorter.a.map(_.documentEditableInfo.note).getOrElse("")
    val secondNote = sorter.b.map(_.documentEditableInfo.note).getOrElse("")
    firstNote.compareTo(secondNote).toDouble
  }

  private def sortByCreatedAt(sorter: TableL.ColumnSorter[ProfileDocumentInfo]): Double = {
    val firstDate = sorter.a.flatMap(_.createdAt).getOrElse(Instant.MIN)
    val secondDate = sorter.b.flatMap(_.createdAt).getOrElse(Instant.MIN)
    firstDate.compareTo(secondDate).toDouble
  }

  private def renderNameCell(renderProps: TableL.RenderCell[ProfileDocumentInfo]) = {
    NameCell(
      renderProps,
      lpProfileId,
      beingRenamedDocumentSignal.map(_.fold(false)(_.fileId.equals(renderProps.data.fileId))),
      onSelectDocToRename,
      reFetchTable
    )()
  }

  private def renderNoteCell(renderProps: TableL.RenderCell[ProfileDocumentInfo]) = {
    NoteCell(renderProps)()
  }

  private def renderExpirationCell(renderProps: TableL.RenderCell[ProfileDocumentInfo]) = {
    ExpirationCell(renderProps)()
  }

  private def renderActionCell(renderProps: TableL.RenderCell[ProfileDocumentInfo]) = {
    ActionCell(
      renderProps,
      lpProfileId,
      reFetchTable,
      onUnselectDocument,
      onSelectDocToRename
    )()
  }

  private def renderCreatedAtCell(renderProps: TableL.RenderCell[ProfileDocumentInfo]) = {
    CreatedAtCell(renderProps)()
  }

  private val selectColumn = SelectionColumnL[ProfileDocumentInfo](
    dataSignal = profileDocumentsSignal.map(_.map { row =>
      SelectionColumnL.Row(data = row)
    }),
    selectedRowsSignal = selectedDocumentSignal,
    onRowsSelected = onRowsSelected
  )()

  private val nameColumn = TableL.Column[ProfileDocumentInfo](
    title = "Name",
    field = "name",
    renderCell = renderNameCell,
    isSortable = true,
    sortWith = Some(sortByName)
  )

  private val noteColumn = TableL.Column[ProfileDocumentInfo](
    title = "Note",
    field = "note",
    renderCell = renderNoteCell,
    isSortable = true,
    sortWith = Some(sortByNote)
  )

  private val expirationColumn = TableL.Column[ProfileDocumentInfo](
    title = "Expiration Date",
    field = "expirationDate",
    renderCell = renderExpirationCell,
    isSortable = true,
    sortWith = Some(sortByExpirationDate),
    width = Option(180)
  )

  private val actionColumn = TableL.Column[ProfileDocumentInfo](
    title = "",
    field = "",
    renderCell = renderActionCell,
    width = Option(60)
  )

  private val createdAtColumn = TableL.Column[ProfileDocumentInfo](
    title = "Uploaded",
    field = "uploaded",
    renderCell = renderCreatedAtCell,
    isSortable = true,
    sortWith = Some(sortByCreatedAt),
    width = Option(180)
  )

  def apply(): HtmlElement = {
    TableL[ProfileDocumentInfo](
      options = TableL.Options(
        indexColumn = Option("lpProfileDocumentId"),
        layout = TableL.Layout.FitColumns
      ),
      columns = List(
        selectColumn,
        nameColumn,
        noteColumn,
        createdAtColumn,
        expirationColumn,
        actionColumn
      ),
      initialSortColumns = List(
        TableL.SortColumn(column = createdAtColumn, direction = TableL.ColumnSortDirection.Desc)
      ),
      dataSignal = profileDocumentsSignal,
      loading = TableL.Loading(
        criteria = isFetchingSignal
      ),
      placeholder = TableL.Placeholder(
        criteria = _.map(_.isEmpty),
        render = _ => renderEmptyState()
      ),
      onRowClick = Observer(_.toggleSelectRow())
    ).amend(maxHeight.px := 500)
  }

}
