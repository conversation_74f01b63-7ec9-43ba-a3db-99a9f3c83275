// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.auditlog.profile

import java.time.Instant
import java.time.temporal.ChronoUnit

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.dropdown.laminar.DropdownL
import design.anduin.style.tw.*
import org.scalajs.dom

import anduin.id.investmententity.InvestmentEntityId
import anduin.id.lpprofile.LpProfileId
import anduin.investmententity.{GetLpProfileAuditLogDataParams, InvestmentEntityGetAllUsersResponse}
import anduin.investorprofile.InvestorProfileCommons.GetAllProfileFieldLabelPathsResponse
import anduin.investorprofile.investmententity.auditlog.{AuditLogActivityFilter, AuditLogActorFilter}
import anduin.investorprofile.models.{ActorModel, ProfileFieldModel, TimeFilterModel}
import anduin.protobuf.investorprofile.auditlog.LpProfileAuditLogEventType

import com.raquo.laminar.api.L.*

import anduin.investmententity.auditlog.*

private[investmententity] case class ProfileAuditLogFilterBar(
  fieldLabelPathsSignal: Signal[GetAllProfileFieldLabelPathsResponse],
  investmentEntityId: InvestmentEntityId,
  lpProfileId: LpProfileId,
  updateFilter: (GetLpProfileAuditLogDataParams => GetLpProfileAuditLogDataParams) => Unit,
  userInfoMappingSignal: Signal[InvestmentEntityGetAllUsersResponse],
  onChangeActor: Observer[Unit]
) {

  private val AllEventType = LpProfileAuditLogEventType.values.toList
  private val allActorsVar = Var[List[ActorModel]](List.empty)
  private val allFieldAliasVar = Var[List[ProfileFieldModel]](List.empty)

  private val selectedEventTypesVar = Var[List[LpProfileAuditLogEventType]](AllEventType)
  private val selectedProfileFieldsVar = Var[List[ProfileFieldModel]](List.empty)
  private val selectedActorVar = Var[List[ActorModel]](List.empty)
  private val dateRangeFilterVar = Var(Some(TimeFilterModel.defaultTimeFilterValue))

  private val isActorChangedVar = Var(false)
  private val isFieldLabelPathChangedVar = Var(false)

  private def handleClearFilters() = {
    selectedEventTypesVar.set(AllEventType)
    selectedProfileFieldsVar.set(allFieldAliasVar.now())
    selectedActorVar.set(allActorsVar.now())
    dateRangeFilterVar.set(Some(TimeFilterModel.defaultTimeFilterValue))
  }

  private def makeFilters() = {
    val actors = selectedActorVar.now().map(_.userId)
    val actorsFilter = ActorFilter(actors)
    val eventTypeFilter = LpProfileAuditLogEventTypeFilter(selectedEventTypesVar.now())
    val profileFieldFilter = LpProfileFieldFilter(selectedProfileFieldsVar.now().map(_.fieldAlias))
    List(actorsFilter) ++ List(eventTypeFilter) ++ List(profileFieldFilter)
  }

  private def renderActor(): HtmlElement = {
    div(
      tw.mr8.flex.itemsCenter,
      div("Actor:"),
      AuditLogActorFilter(
        allActorsSignal = allActorsVar.signal,
        selectedActorsSignal = selectedActorVar.signal,
        onSelectActor = Observer[ActorModel] { selectedActor =>
          selectedActorVar.update(_.appended(selectedActor).distinct)
          isActorChangedVar.update(!_)
        },
        onUnselectActor = Observer[ActorModel] { unSelectedActor =>
          selectedActorVar.update(_.filter(!_.equals(unSelectedActor)))
          isActorChangedVar.update(!_)
        },
        onSelectAllActors = Observer[Unit] { _ =>
          selectedActorVar.set(allActorsVar.now())
          isActorChangedVar.update(!_)
        },
        onUnselectAllActors = Observer[Unit] { _ =>
          selectedActorVar.set(List.empty)
          isActorChangedVar.update(!_)
        }
      )()
    )
  }

  private def getActivityText(eventType: LpProfileAuditLogEventType) = {
    eventType match {
      case LpProfileAuditLogEventType.PROFILE_CLEAR     => "Profile cleared"
      case LpProfileAuditLogEventType.FIELD_UPDATE      => "Field updated"
      case LpProfileAuditLogEventType.FIELD_CLEAR       => "Field cleared"
      case LpProfileAuditLogEventType.PERMISSION_EDIT   => "Permission edited"
      case LpProfileAuditLogEventType.PERMISSION_GRANT  => "Permission granted"
      case LpProfileAuditLogEventType.PERMISSION_REVOKE => "Permission revoked"
      case LpProfileAuditLogEventType.Unrecognized(_)   => ""
    }
  }

  def renderActivity(): HtmlElement = {
    div(
      tw.mr8.flex.itemsCenter,
      div("Activity:"),
      div(
        AuditLogActivityFilter[LpProfileAuditLogEventType](
          activities = AllEventType,
          selectedActivitiesSignal = selectedEventTypesVar.signal,
          onSelectActivity = Observer[LpProfileAuditLogEventType] { eventType =>
            selectedEventTypesVar.update(_.appended(eventType).distinct)
          },
          onUnselectActivity = Observer[LpProfileAuditLogEventType] { eventType =>
            selectedEventTypesVar.update(_.filter(!_.equals(eventType)))
          },
          onSelectAllActivity = Observer[Unit] { _ =>
            selectedEventTypesVar.set(AllEventType)
          },
          onUnselectAllActivity = Observer[Unit] { _ =>
            selectedEventTypesVar.set(List.empty)
          },
          getActivityText = getActivityText
        )()
      )
    )
  }

  def renderField(): HtmlElement = {
    div(
      tw.mr8.flex.itemsCenter,
      div("Profile field:"),
      ProfileFieldFilter(
        allFieldLabelPathsSignal = allFieldAliasVar.signal,
        selectedFieldsSignal = selectedProfileFieldsVar.signal,
        onSelectField = Observer[ProfileFieldModel] { selectedField =>
          selectedProfileFieldsVar.update(_.appended(selectedField).distinct)
          isFieldLabelPathChangedVar.update(!_)
        },
        onUnselectField = Observer[ProfileFieldModel] { unselectedField =>
          selectedProfileFieldsVar.update(_.filter(!_.equals(unselectedField)))
          isFieldLabelPathChangedVar.update(!_)
        },
        onSelectAllFields = Observer[Unit] { _ =>
          selectedProfileFieldsVar.set(allFieldAliasVar.now())
          isFieldLabelPathChangedVar.update(!_)
        },
        onUnselectAllFields = Observer[Unit] { _ =>
          selectedProfileFieldsVar.set(List.empty)
          isFieldLabelPathChangedVar.update(!_)
        }
      )()
    )
  }

  def renderDateRange(): HtmlElement = {
    div(
      DropdownL[TimeFilterModel](
        items = TimeFilterModel.TimeFilters.map(item => DropdownL.Item(value = item)),
        target = DropdownL.Target(
          appearance = DropdownL.Appearance.Minimal(height = ButtonL.Height.Fix24)
        ),
        value = dateRangeFilterVar.signal,
        valueToString = _.text,
        onChange = Observer[TimeFilterModel] { timeFilter => dateRangeFilterVar.set(Some(timeFilter)) }
      )()
    )
  }

  def apply(): HtmlElement = {
    div(
      userInfoMappingSignal.distinct --> Observer[InvestmentEntityGetAllUsersResponse] { userInfoMapping =>
        val allActors = AuditLogActorFilter.getAllActorModel(userInfoMapping)
        allActorsVar.set(allActors)
        selectedActorVar.set(allActors)
      },
      fieldLabelPathsSignal.distinct --> Observer[GetAllProfileFieldLabelPathsResponse] { resp =>
        val allFieldAlias = resp.paths.map { case (field, paths) =>
          ProfileFieldModel(
            field.name,
            paths
          )
        }.toList
        allFieldAliasVar.set(allFieldAlias)
        selectedProfileFieldsVar.set(allFieldAlias)
      },
      tw.flex.itemsCenter.textSmall.textGray7,
      renderActor(),
      renderActivity(),
      renderField(),
      renderDateRange(),
      child.maybe <-- selectedActorVar.signal
        .combineWith(
          selectedEventTypesVar.signal,
          dateRangeFilterVar.signal,
          selectedProfileFieldsVar.signal
        )
        .distinct
        .mapN { case (actorsFilter, eventTypeFilter, timeFilter, profileFieldFilter) =>
          val isDefaultEventTypes = eventTypeFilter.length == AllEventType.length
          val isDefaultActor = actorsFilter.length == allActorsVar.now().length
          val isDefaultDateRange = timeFilter
            .contains(TimeFilterModel.defaultTimeFilterValue)
          val isDefaultField = profileFieldFilter.length == allFieldAliasVar.now().length
          Option.unless(isDefaultEventTypes && isDefaultField && isDefaultDateRange && isDefaultActor) {
            ButtonL(
              style = ButtonL.Style.Minimal(color = ButtonL.Color.Primary, height = ButtonL.Height.Fix24),
              onClick = Observer[dom.MouseEvent] { _ =>
                handleClearFilters()
              }
            )("Clear filters")
          }
        },
      // Update filter
      isActorChangedVar.signal.changes --> Observer[Boolean] { _ =>
        onChangeActor.onNext(())
        updateFilter(_.copy(offset = 0, filters = makeFilters()))
      },
      isFieldLabelPathChangedVar.signal.changes --> Observer[Boolean] { _ =>
        updateFilter(_.copy(filters = makeFilters()))
      },
      selectedEventTypesVar.signal.changes --> Observer[List[LpProfileAuditLogEventType]] { _ =>
        updateFilter(_.copy(filters = makeFilters()))
      },
      dateRangeFilterVar.signal.changes --> Observer[Option[TimeFilterModel]] { timeFilterOpt =>
        updateFilter(
          _.copy(
            timestampFilterOpt = timeFilterOpt.map { time =>
              TimestampFilter(from = Instant.now().minus(time.numDays.toLong, ChronoUnit.DAYS), to = Instant.now())
            }
          )
        )
      }
    )
  }

}
