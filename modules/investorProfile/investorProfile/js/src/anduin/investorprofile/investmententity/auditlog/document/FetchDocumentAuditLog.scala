// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.auditlog.document

import design.anduin.components.toast.Toast

import anduin.frontend.AirStreamUtils
import anduin.investorprofile.client.InvestmentEntityEndpointClient
import anduin.investorprofile.investmententity.auditlog.AuditLogPageSizeDropDown
import anduin.investorprofile.models.LogTrackingData

import com.raquo.laminar.api.L.*

import anduin.investmententity.*

private[document] case class FetchDocumentAuditLog(
  stableInvestmentEntityInfo: StableInvestmentEntityInfo,
  renderChildren: FetchDocumentAuditLog.RenderChildren => HtmlElement,
  onLogTracking: Observer[LogTrackingData]
) {

  private val filterDataEventBus = new EventBus[GetDocumentAuditLogDataParams]

  private val DefaultData = FetchDocumentAuditLog.Data(
    isFetching = true,
    response = GetAuditLogTableResponse(
      auditLogData = None,
      userInfoMapping = InvestmentEntityGetAllUsersResponse(
        userDataMap = Map.empty
      )
    )
  )

  private val filterVar = Var(
    GetDocumentAuditLogDataParams(
      lpProfileId = stableInvestmentEntityInfo.masterProfileId,
      investmentEntityId = stableInvestmentEntityInfo.investmentEntityId,
      offset = 0,
      limitOpt = Option(AuditLogPageSizeDropDown.defaultPageSize)
    )
  )

  private val resultVar = Var(DefaultData)
  private val forceUpdateVar = Var(false)

  private def updateFilter(updater: GetDocumentAuditLogDataParams => GetDocumentAuditLogDataParams) = {
    resultVar.update(_.copy(isFetching = true))
    forceUpdateVar.update(!_)
    filterVar.update(updater)
  }

  private def fetchAuditLogs(filterParams: GetDocumentAuditLogDataParams) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        getLogTracking <- LogTrackingData.fetchLogTrackingData(stableInvestmentEntityInfo)
        getLogResp <- InvestmentEntityEndpointClient.getDocumentAuditLog(filterParams)
      } yield {
        getLogTracking.fold(
          _ => Toast.error("Failed to load document log tracking"),
          resp => onLogTracking.onNext(resp)
        )
        getLogResp.fold(
          _ => {
            Toast.error("Failed to load document audit log")
            DefaultData.copy(isFetching = false)
          },
          resp => {
            FetchDocumentAuditLog.Data(isFetching = false, response = resp)
          }
        )
      }
    }
  }

  def apply(): HtmlElement = {
    renderChildren(
      FetchDocumentAuditLog.RenderChildren(
        currentFilterSignal = filterVar.signal,
        dataSignal = resultVar.signal,
        updateFilter = updateFilter
      )
    ).amend(
      filterDataEventBus.events.flatMapSwitch(fetchAuditLogs) --> resultVar.writer,
      filterVar.signal --> filterDataEventBus.writer
    )
  }

}

private[document] object FetchDocumentAuditLog {

  case class Data(
    isFetching: Boolean,
    response: GetAuditLogTableResponse
  )

  case class RenderChildren(
    currentFilterSignal: Signal[GetDocumentAuditLogDataParams],
    dataSignal: Signal[Data],
    updateFilter: (GetDocumentAuditLogDataParams => GetDocumentAuditLogDataParams) => Unit
  )

}
