// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.auditlog.profile

import design.anduin.components.toast.Toast

import anduin.frontend.AirStreamUtils
import anduin.investorprofile.InvestorProfileCommons.{
  GetAllProfileFieldLabelPathsResponse,
  GetAllProfileFieldLabelPathsParams
}
import anduin.investorprofile.client.{InvestmentEntityEndpointClient, LpProfileEndpointClient}
import anduin.investorprofile.investmententity.auditlog.AuditLogPageSizeDropDown
import anduin.investorprofile.models.LogTrackingData

import com.raquo.laminar.api.L.*

import anduin.investmententity.*

private[profile] case class FetchProfileAuditLog(
  stableInvestmentEntityInfo: StableInvestmentEntityInfo,
  renderChildren: FetchProfileAuditLog.RenderChildren => HtmlElement,
  onLogTracking: Observer[LogTrackingData]
) {

  private val filterDataEventBus = new EventBus[GetLpProfileAuditLogDataParams]

  private val DefaultData = FetchProfileAuditLog.Data(
    isFetching = true,
    response = GetAuditLogTableResponse(
      auditLogData = None,
      userInfoMapping = InvestmentEntityGetAllUsersResponse(
        userDataMap = Map.empty
      )
    ),
    fieldLabelPaths = GetAllProfileFieldLabelPathsResponse(
      paths = Seq.empty
    )
  )

  private val filterVar = Var(
    GetLpProfileAuditLogDataParams(
      lpProfileId = stableInvestmentEntityInfo.masterProfileId,
      investmentEntityId = stableInvestmentEntityInfo.investmentEntityId,
      offset = 0,
      limitOpt = Option(AuditLogPageSizeDropDown.defaultPageSize)
    )
  )

  private val resultVar = Var(DefaultData)
  private val forceUpdateVar = Var(false)

  private def updateFilter(updater: GetLpProfileAuditLogDataParams => GetLpProfileAuditLogDataParams) = {
    resultVar.update(_.copy(isFetching = true))
    forceUpdateVar.update(!_)
    filterVar.update(updater)
  }

  private def fetchAuditLogs(filterParams: GetLpProfileAuditLogDataParams) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        getLogTracking <- LogTrackingData.fetchLogTrackingData(stableInvestmentEntityInfo)
        getLogResp <- InvestmentEntityEndpointClient.getLpProfileAuditLog(filterParams)
        getProfilePathResp <- LpProfileEndpointClient.getProfileFieldLabelPaths(
          GetAllProfileFieldLabelPathsParams(stableInvestmentEntityInfo.masterProfileId)
        )
      } yield {
        getLogTracking.fold(
          _ => Toast.error("Failed to load profile log tracking"),
          resp => onLogTracking.onNext(resp)
        )
        getLogResp.fold(
          _ => {
            Toast.error("Failed to load profile audit log")
            DefaultData.copy(isFetching = false)
          },
          resp => {
            FetchProfileAuditLog.Data(
              isFetching = false,
              response = resp,
              fieldLabelPaths = getProfilePathResp.toOption.getOrElse(
                GetAllProfileFieldLabelPathsResponse(paths = Seq.empty)
              )
            )
          }
        )
      }
    }
  }

  def apply(): HtmlElement = {
    renderChildren(
      FetchProfileAuditLog.RenderChildren(
        currentFilterSignal = filterVar.signal,
        dataSignal = resultVar.signal,
        updateFilter = updateFilter
      )
    ).amend(
      filterDataEventBus.events.flatMapSwitch(fetchAuditLogs) --> resultVar.writer,
      filterVar.signal --> Observer[GetLpProfileAuditLogDataParams] { filter =>
        filterDataEventBus.emit(filter)
      }
    )
  }

}

private[profile] object FetchProfileAuditLog {

  case class Data(
    isFetching: Boolean,
    response: GetAuditLogTableResponse,
    fieldLabelPaths: GetAllProfileFieldLabelPathsResponse = GetAllProfileFieldLabelPathsResponse(paths = Seq.empty)
  )

  case class RenderChildren(
    currentFilterSignal: Signal[GetLpProfileAuditLogDataParams],
    dataSignal: Signal[Data],
    updateFilter: (GetLpProfileAuditLogDataParams => GetLpProfileAuditLogDataParams) => Unit
  )

}
