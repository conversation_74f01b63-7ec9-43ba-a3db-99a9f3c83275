// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.permission

import anduin.facades.editor.quill.mod.default as QuillEditor
import anduin.investmententity.{
  GetRemindUserEmailTemplateParams,
  RemindUserEmailTemplate,
  RemindUserParams,
  StableInvestmentEntityInfo
}
import anduin.investorprofile.client.InvestmentEntityEndpointClient
import anduin.investorprofile.investmententity.permission.MemberTable.InvestmentEntityUserWithInfo
import anduin.model.common.user.UserId
import anduin.utils.DateTimeUtils
import com.anduin.stargazer.client.utils.ZIOUtils
import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.editor.laminar.*
import design.anduin.components.editor.{EditorRenderer, OnChangeData}
import design.anduin.components.field.laminar.FieldL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL}
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import org.scalajs.dom

private[investmententity] case class SendReminderModal(
  stableInvestmentEntityInfo: StableInvestmentEntityInfo,
  toRemindMember: InvestmentEntityUserWithInfo,
  onReminded: Observer[UserId],
  onCancel: Observer[Unit]
) {

  private val subjectVar = Var("")

  private val ctaVar = Var("")

  private val messageVar = Var("")

  private val isLoadingVar = Var(true)

  private val investmentEntityId = stableInvestmentEntityInfo.investmentEntityId

  private def handleChangeMessage(data: OnChangeData): Unit = {
    messageVar.set(data.value)
  }

  private def getEmailTemplate(): Unit = {
    ZIOUtils.runAsync {
      InvestmentEntityEndpointClient
        .getRemindUserEmailTemplate(
          GetRemindUserEmailTemplateParams(
            toRemindMember.entityInfo.userId,
            investmentEntityId,
            DateTimeUtils.defaultTimezone
          )
        )
        .map(
          _.fold(
            _ => Toast.error("Failed to load email template"),
            resp => {
              ctaVar.set(resp.cta)
              messageVar.set(resp.body)
              subjectVar.set(resp.subject)
              isLoadingVar.set(false)
            }
          )
        )
    }
  }

  private def handleSendEmail(): Unit = {
    val subject = subjectVar.now()
    val message = messageVar.now()
    val cta = ctaVar.now()
    ZIOUtils.runAsync {
      InvestmentEntityEndpointClient
        .remindUserToJoin(
          RemindUserParams(
            toRemindMember.entityInfo.userId,
            investmentEntityId,
            emailTemplate = RemindUserEmailTemplate(
              subject = subject,
              body = message,
              cta = cta
            )
          )
        )
        .map(
          _.fold(
            _ => Toast.error("Failed to remind member"),
            _ => {
              Toast.success("Reminder sent successfully")
              onReminded.onNext(toRemindMember.entityInfo.userId)
              onCancel.onNext(())
            }
          )
        )
    }
  }

  private def minimalToolbarL(
    readOnly: Boolean,
    editorInstance: QuillEditor
  ) = {
    ToolbarContainerL()().amend(
      div(
        tw.flex.itemsCenter.flexWrap.p4,
        TextFormatButton(
          readOnly = readOnly,
          editorInstance = editorInstance,
          formatType = TextFormatButton.FormatType.Bold
        )(),
        TextFormatButton(
          readOnly = readOnly,
          editorInstance = editorInstance,
          formatType = TextFormatButton.FormatType.Italic
        )(),
        TextFormatButton(
          readOnly = readOnly,
          editorInstance = editorInstance,
          formatType = TextFormatButton.FormatType.Underline
        )(),
        TextFormatButton(
          readOnly = readOnly,
          editorInstance = editorInstance,
          formatType = TextFormatButton.FormatType.StrikeThrough
        )(),
        BulletListButton(
          readOnly = readOnly,
          editorInstance = editorInstance
        )(),
        NumberListButton(
          readOnly = readOnly,
          editorInstance = editorInstance
        )()
      )
    )
  }

  private def renderEditor(editorRenderer: EditorRenderer) = {
    div(
      tw.borderAll.borderGray3,
      // Toolbar
      minimalToolbarL(
        readOnly = editorRenderer.readOnly,
        editorInstance = editorRenderer.editorInstance
      ).amend(tw.borderBottom.border1.borderGray3),
      // Editor content
      editorRenderer.editorNode
    )
  }

  def apply(): HtmlElement = {
    div(
      onMountCallback(_ => getEmailTemplate()),
      child <-- isLoadingVar.signal.map {
        if (_) {
          BlockIndicatorL()()
        } else {
          div(
            ModalBodyL(
              div(
                tw.mb16,
                FieldL(
                  label = Option("Subject")
                )(
                  TextBoxL(
                    value = subjectVar.signal,
                    onChange = subjectVar.writer
                  )()
                )
              ),
              div(
                tw.mb16,
                FieldL(
                  label = Option("Message")
                )(
                  RichEditorL(
                    initialValue = messageVar.now(),
                    onChange = Observer[OnChangeData](handleChangeMessage),
                    render = renderEditor
                  )()
                )
              ),
              div(
                FieldL(
                  label = Option("Email call-to-action")
                )(
                  TextBoxL(
                    value = ctaVar.signal,
                    onChange = ctaVar.writer
                  )()
                )
              )
            ),
            ModalFooterWCancelL(
              cancel = onCancel
            )(
              ButtonL(
                style = ButtonL.Style.Full(color = ButtonL.Color.Primary),
                onClick = Observer[dom.MouseEvent] { _ =>
                  handleSendEmail()
                  ()
                }
              )("Send")
            )
          )
        }
      }
    )
  }

}
