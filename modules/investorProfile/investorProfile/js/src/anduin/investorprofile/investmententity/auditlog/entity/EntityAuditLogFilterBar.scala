// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.auditlog.entity

import java.time.Instant
import java.time.temporal.ChronoUnit

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.dropdown.laminar.DropdownL
import design.anduin.style.tw.*
import org.scalajs.dom

import anduin.id.investmententity.InvestmentEntityId
import anduin.investmententity.auditlog.{ActorFilter, InvestmentEntityAuditLogEventTypeFilter, TimestampFilter}
import anduin.investmententity.{GetInvestmentEntityAuditLogDataParams, InvestmentEntityGetAllUsersResponse}
import anduin.investorprofile.investmententity.auditlog.{AuditLogActivityFilter, AuditLogActorFilter}
import anduin.investorprofile.models.{ActorModel, TimeFilterModel}
import anduin.protobuf.investorprofile.auditlog.InvestmentEntityAuditLogEventType

import com.raquo.laminar.api.L.*

private[entity] case class EntityAuditLogFilterBar(
  updateFilter: (GetInvestmentEntityAuditLogDataParams => GetInvestmentEntityAuditLogDataParams) => Unit,
  investmentEntityId: InvestmentEntityId,
  userInfoMappingSignal: Signal[InvestmentEntityGetAllUsersResponse],
  onChangeActor: Observer[Unit]
) {

  private val AllEventType: List[InvestmentEntityAuditLogEventType] =
    InvestmentEntityAuditLogEventType.values.toList

  private val allActorsVar = Var[List[ActorModel]](List.empty)
  private val selectedEventTypesVar = Var[List[InvestmentEntityAuditLogEventType]](AllEventType)
  private val selectedActorVar = Var[List[ActorModel]](List.empty)
  private val isActorChangedVar = Var(false)
  private val dateRangeFilterVar = Var(Some(TimeFilterModel.defaultTimeFilterValue))

  private def handleClearFilters() = {
    selectedEventTypesVar.set(AllEventType)
    selectedActorVar.set(allActorsVar.now())
    dateRangeFilterVar.set(Some(TimeFilterModel.defaultTimeFilterValue))
  }

  private def getActivityText(eventType: InvestmentEntityAuditLogEventType) = {
    eventType match {
      case InvestmentEntityAuditLogEventType.INVESTMENT_ENTITY_CREATE           => "Investment profile created"
      case InvestmentEntityAuditLogEventType.INVESTMENT_ENTITY_RENAME           => "Investment profile renamed"
      case InvestmentEntityAuditLogEventType.INVESTMENT_ENTITY_CHANGE_CUSTOM_ID => "Custom ID changed"
      case InvestmentEntityAuditLogEventType.ROLE_GRANT                         => "Role granted"
      case InvestmentEntityAuditLogEventType.ROLE_EDIT                          => "Role edited"
      case InvestmentEntityAuditLogEventType.ROLE_REVOKE                        => "Role revoked"
      case InvestmentEntityAuditLogEventType.SUBSCRIPTION_LINKED                => "Subscription linked"
      case InvestmentEntityAuditLogEventType.SUBSCRIPTION_REMOVED               => "Subscription unlinked"
      case InvestmentEntityAuditLogEventType.AUTO_SAVE_ENABLED                  => "Autosave enabled"
      case InvestmentEntityAuditLogEventType.AUTO_SAVE_DISABLE                  => "Autosave disabled"
      case InvestmentEntityAuditLogEventType.Unrecognized(_)                    => ""
    }
  }

  private def makeFilters() = {
    val actors = selectedActorVar.now().map(_.userId)
    val actorsFilter = ActorFilter(actors)
    val eventTypeFilter = InvestmentEntityAuditLogEventTypeFilter(selectedEventTypesVar.now())

    List(actorsFilter) ++ List(eventTypeFilter)
  }

  private def renderActivity(): HtmlElement = {
    div(
      tw.mr8.flex.itemsCenter,
      div("Activity:"),
      div(
        AuditLogActivityFilter[InvestmentEntityAuditLogEventType](
          activities = AllEventType,
          selectedActivitiesSignal = selectedEventTypesVar.signal,
          onSelectActivity = Observer[InvestmentEntityAuditLogEventType] { eventType =>
            selectedEventTypesVar.update(_.appended(eventType).distinct)
          },
          onUnselectActivity = Observer[InvestmentEntityAuditLogEventType] { eventType =>
            selectedEventTypesVar.update(_.filter(!_.equals(eventType)))
          },
          onSelectAllActivity = Observer[Unit] { _ => selectedEventTypesVar.set(AllEventType) },
          onUnselectAllActivity = Observer[Unit] { _ => selectedEventTypesVar.set(List.empty) },
          getActivityText = getActivityText
        )()
      )
    )
  }

  private def renderActor(): HtmlElement = {
    div(
      tw.mr8.flex.itemsCenter,
      div("Actor:"),
      AuditLogActorFilter(
        allActorsSignal = allActorsVar.signal,
        selectedActorsSignal = selectedActorVar.signal,
        onSelectActor = Observer[ActorModel] { selectedActor =>
          selectedActorVar.update(_.appended(selectedActor).distinct)
          isActorChangedVar.update(!_)
        },
        onUnselectActor = Observer[ActorModel] { unSelectActor =>
          selectedActorVar.update(_.filter(!_.equals(unSelectActor)))
          isActorChangedVar.update(!_)
        },
        onSelectAllActors = Observer[Unit] { _ =>
          selectedActorVar.set(allActorsVar.now())
          isActorChangedVar.update(!_)
        },
        onUnselectAllActors = Observer[Unit] { _ =>
          selectedActorVar.set(List.empty)
          isActorChangedVar.update(!_)
        }
      )()
    )
  }

  private def renderDateRange(): HtmlElement = {
    div(
      DropdownL[TimeFilterModel](
        items = TimeFilterModel.TimeFilters.map(item => DropdownL.Item(value = item)),
        target = DropdownL.Target(
          appearance = DropdownL.Appearance.Minimal(height = ButtonL.Height.Fix24)
        ),
        value = dateRangeFilterVar.signal,
        valueToString = _.text,
        onChange = Observer[TimeFilterModel] { timeFilter => dateRangeFilterVar.set(Some(timeFilter)) }
      )()
    )
  }

  def apply(): HtmlElement = {
    div(
      tw.flex.itemsCenter.textSmall.textGray7,
      userInfoMappingSignal.distinct --> Observer[InvestmentEntityGetAllUsersResponse] { userInfoMapping =>
        val allActors = AuditLogActorFilter.getAllActorModel(userInfoMapping)
        allActorsVar.set(allActors)
        selectedActorVar.set(allActors)
      },
      renderActor(),
      renderActivity(),
      renderDateRange(),
      child.maybe <-- selectedActorVar.signal
        .combineWith(selectedEventTypesVar.signal, dateRangeFilterVar.signal)
        .mapN { case (actorsFilter, eventTypeFilter, timeFilter) =>
          val isDefaultEventType = eventTypeFilter.length == AllEventType.length
          val isDefaultActor = actorsFilter.isEmpty || actorsFilter.length == allActorsVar.now().length
          val isDefaultDateRange = timeFilter.contains(TimeFilterModel.defaultTimeFilterValue)
          Option.unless(isDefaultActor && isDefaultEventType && isDefaultDateRange) {
            ButtonL(
              style = ButtonL.Style.Minimal(color = ButtonL.Color.Primary, height = ButtonL.Height.Fix24),
              onClick = Observer[dom.MouseEvent] { _ =>
                handleClearFilters()
              }
            )("Clear filters")
          }
        },
      // Update filter
      isActorChangedVar.signal.changes --> Observer[Boolean] { _ =>
        onChangeActor.onNext(())
        updateFilter(_.copy(offset = 0, filters = makeFilters()))
      },
      selectedEventTypesVar.signal.changes --> Observer[List[InvestmentEntityAuditLogEventType]] { _ =>
        updateFilter(_.copy(filters = makeFilters()))
      },
      dateRangeFilterVar.signal.changes --> Observer[Option[TimeFilterModel]] { timeFilterOpt =>
        updateFilter(
          _.copy(
            timestampFilterOpt = timeFilterOpt.map { time =>
              TimestampFilter(from = Instant.now().minus(time.numDays.toLong, ChronoUnit.DAYS), to = Instant.now())
            }
          )
        )
      }
    )
  }

}
