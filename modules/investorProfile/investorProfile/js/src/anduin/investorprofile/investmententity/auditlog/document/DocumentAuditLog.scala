// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.auditlog.document

import design.anduin.components.pagination.laminar.PaginationL
import design.anduin.style.tw.*

import anduin.investorprofile.investmententity.auditlog.AuditLogPageSizeDropDown
import anduin.investorprofile.models.LogTrackingData
import anduin.scalajs.pluralize.Pluralize

import com.raquo.laminar.api.L.*

import anduin.investmententity.*

private[investmententity] case class DocumentAuditLog(
  stableInvestmentEntityInfo: StableInvestmentEntityInfo,
  logTrackingWriter: WriteBus[LogTrackingData]
) {

  private val pageIndexVar = Var[Int](0)
  private val pageSizeVar = Var(AuditLogPageSizeDropDown.defaultPageSize)
  private val minSeenIndexOptVar = Var[Option[Int]](None)

  def apply(): HtmlElement = {
    val lpProfileId = stableInvestmentEntityInfo.masterProfileId

    FetchDocument(
      lpProfileId = lpProfileId,
      renderChildren = renderDocumentsProps => {
        FetchDocumentAuditLog(
          stableInvestmentEntityInfo = stableInvestmentEntityInfo,
          renderChildren = renderProps => {
            val auditLogDataSignal = renderProps.dataSignal.map(_.response.auditLogData)
            val auditLogsSignal = auditLogDataSignal.map(_.map(_.items.flatMap[DocumentLogItemResponse] {
              case resp: DocumentLogItemResponse => Some(resp)
              case _                             => None
            }).getOrElse(List.empty))
            val allLogCountSignal = auditLogDataSignal.map(_.map(_.allLogCount).getOrElse(0))
            val totalPageSignal = allLogCountSignal
              .combineWith(pageSizeVar.signal)
              .mapN((allLogCount, pageSize) => {
                (allLogCount + pageSize - 1) / pageSize
              })

            val isFetchingSignal = renderProps.dataSignal.map(_.isFetching)
            val allInvestmentEntityUserInfoMapSignal = renderProps.dataSignal.map(_.response.userInfoMapping)
            val documentsSignal = renderDocumentsProps.dataSignal.map(_.documents)
            val mapFileIdToFileNameSignal =
              documentsSignal.map(_.map(doc => doc.fileId -> doc.documentEditableInfo.fileName).toMap)
            val auditLogUserInfoMappingSignal = auditLogDataSignal.map(
              _.map(_.userIdAndUserInfos).getOrElse(Seq.empty).map(info => info.userId -> info.userInfo).toMap
            )

            div(
              tw.py20.flex.flexCol.hPc100,
              DocumentAuditLogFilterBar(
                documentsSignal = documentsSignal,
                updateFilter = renderProps.updateFilter,
                userInfoMappingSignal = allInvestmentEntityUserInfoMapSignal,
                onChangeActor = Observer[Unit] { _ =>
                  pageIndexVar.set(0)
                }
              )().amend(tw.mb24),
              DocumentAuditLogTable(
                auditLogsSignal = auditLogsSignal,
                isFetchingSignal = isFetchingSignal,
                mapFileIdToFileNameSignal = mapFileIdToFileNameSignal,
                seenIndexOptSignal = minSeenIndexOptVar.signal,
                userInfoMapSignal = auditLogUserInfoMappingSignal
              )().amend(tw.flexFill),
              div(
                tw.m4.mt20.flex.itemsCenter.textGray7,
                div(
                  tw.mrAuto,
                  allLogCountSignal.map(_ == 0).cls(tw.hidden),
                  child <-- allLogCountSignal
                    .combineWith(pageIndexVar.signal, pageSizeVar.signal)
                    .mapN((allLogCount, pageIndex, pageSize) => {
                      val fromLogNumber = pageIndex * pageSize + 1
                      val toLogNumber = allLogCount min (pageIndex + 1) * pageSize
                      s"$fromLogNumber - $toLogNumber of $allLogCount ${Pluralize("event", allLogCount)}"
                    })
                ),
                div(
                  tw.flex.justifyCenter,
                  allLogCountSignal.map(_ == 0).cls(tw.hidden),
                  PaginationL(
                    totalPage = totalPageSignal,
                    currentPage = pageIndexVar.signal.map(_ + 1),
                    onJumpToPage = Observer { page =>
                      pageIndexVar.set(page - 1)
                      renderProps.updateFilter(
                        _.copy(
                          offset = (page - 1) * pageSizeVar.now()
                        )
                      )
                    }
                  )()
                ),
                div(
                  tw.flex.itemsCenter.mlAuto,
                  div(tw.mr8, "Events per page:"),
                  AuditLogPageSizeDropDown(
                    pageSizeSignal = pageSizeVar.signal.map(Some(_)),
                    onChange = Observer[Int] { pageSize =>
                      pageIndexVar.set(0)
                      pageSizeVar.set(pageSize)
                      renderProps.updateFilter(
                        _.copy(
                          offset = 0,
                          limitOpt = Option(pageSize)
                        )
                      )
                    }
                  )()
                )
              )
            )
          },
          onLogTracking = Observer[LogTrackingData] { trackingData =>
            val newSeenIndex = trackingData.profileDocumentLogTracking.seenIndex
            minSeenIndexOptVar.update { minSeenIndexOpt =>
              Some(minSeenIndexOpt.map(_.min(newSeenIndex)).getOrElse(newSeenIndex))
            }
            logTrackingWriter.onNext(trackingData)
          }
        )()
      }
    )()
  }

}
