// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.auditlog.entity

import design.anduin.style.CssVar
import design.anduin.style.tw.*
import design.anduin.table.laminar.TableL
import io.circe.Codec
import anduin.circe.generic.semiauto.deriveCodecWithDefaults

import anduin.investmententity.{InvestmentEntityLogItemResponse, LogItemResponse}
import anduin.investorprofile.common.UserInfoAvatar
import anduin.investorprofile.investmententity.auditlog.{AuditLogTimeCell, EmptyAuditLogState}
import anduin.investorprofile.models.ParticipantModel
import anduin.model.common.user.{UserId, UserInfo}

import com.raquo.laminar.api.L.*
import anduin.model.codec.ProtoCodecs.given

private[entity] case class EntityAuditLogTable(
  auditLogsSignal: Signal[List[InvestmentEntityLogItemResponse]],
  isFetchingSignal: Signal[Boolean],
  seenIndexOptSignal: Signal[Option[Int]],
  userInfoMapSignal: Signal[Map[UserId, UserInfo]]
) {

  given Codec.AsObject[ParticipantModel] = deriveCodecWithDefaults
  given Codec.AsObject[LogItemResponse] = deriveCodecWithDefaults
  given Codec.AsObject[InvestmentEntityLogItemResponse] = deriveCodecWithDefaults

  private val seenIndexSignal = seenIndexOptSignal
    .combineWith(auditLogsSignal.map(_.length))
    .mapN((seenIndex, numLogs) => seenIndex.getOrElse(numLogs))

  private val timeColumn = TableL.Column[InvestmentEntityLogItemResponse](
    title = "Time",
    field = "date",
    renderCell = renderProps => {
      AuditLogTimeCell(renderProps.data.timeStamp)()
        .amend(
          seenIndexSignal.distinct --> Observer[Int] { seenIndex =>
            if (renderProps.data.eventOrder > seenIndex) {
              renderProps.cellComponent.getRow().getElement().style.backgroundColor =
                CssVar.toColorVar(CssVar.Color.Primary1)
            }
          }
        )
    },
    width = Option(160)
  )

  private val actorColumn = TableL.Column[InvestmentEntityLogItemResponse](
    title = "Actor",
    field = "actor",
    renderCell = renderProps => {
      div(
        child <-- userInfoMapSignal.map { userDataMap =>
          UserInfoAvatar(renderProps.data.actor.flatMap(userId => userDataMap.get(userId)))()
        }
      )
    }
  )

  private val actionColumn = TableL.Column[InvestmentEntityLogItemResponse](
    title = "Action",
    field = "actionName",
    renderCell = renderProps => EntityAuditLogActionCell(renderProps.data.logDetail, userInfoMapSignal)()
  )

  private val ipColumn = TableL.Column[InvestmentEntityLogItemResponse](
    title = "IP",
    field = "ipAddress",
    renderCell = renderProps => div(tw.mx4, renderProps.data.ipAddress),
    width = Option(160)
  )

  private def handleRowRendered(rowRenderedData: TableL.RowRenderedData[InvestmentEntityLogItemResponse]) = {
    rowRenderedData.rowComponent.getElement().classList.add(tw.group.css)
  }

  def apply(): HtmlElement = {
    TableL[InvestmentEntityLogItemResponse](
      options = TableL.Options(
        layout = TableL.Layout.FitColumns
      ),
      columns = List(
        timeColumn,
        actorColumn,
        actionColumn,
        ipColumn
      ),
      dataSignal = auditLogsSignal,
      loading = TableL.Loading(
        criteria = isFetchingSignal
      ),
      placeholder = TableL.Placeholder(
        criteria = _.map(_.isEmpty),
        render = _ => EmptyAuditLogState()()
      ),
      onRowRendered = Observer[TableL.RowRenderedData[InvestmentEntityLogItemResponse]](handleRowRendered)
    ).amend(maxHeight.px := 500)
  }

}
