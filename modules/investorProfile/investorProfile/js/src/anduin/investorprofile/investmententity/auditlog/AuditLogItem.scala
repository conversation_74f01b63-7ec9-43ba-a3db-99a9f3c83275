// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.auditlog

import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.style.tw.*
import org.scalajs.dom.MouseEvent

import com.raquo.laminar.api.L.*

private[investmententity] case class AuditLogItem(
  isSelectedSignal: Signal[Boolean],
  renderContent: () => HtmlElement,
  onItemClick: Observer[Boolean]
) {

  def apply(): HtmlElement = {
    div(
      child <-- isSelectedSignal.map { isSelected =>
        button(
          tw.flex.itemsCenter,
          tw.p8.leading16,
          tw.wPc100.textLeft.rounded4,
          tw.hover(tw.bgGray2),
          onClick --> Observer[MouseEvent] { _ => onItemClick.onNext(isSelected) },
          div(
            tw.mr20,
            IconL(
              isSelectedSignal.map {
                if (_) Icon.Glyph.Check else Icon.Glyph.Blank
              },
              Icon.Size.Custom(12)
            )()
          ),
          span(renderContent())
        )
      }
    )
  }

}
