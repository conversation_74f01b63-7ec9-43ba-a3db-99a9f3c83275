// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.permission

import anduin.frontend.AirStreamUtils
import anduin.graphql.component.laminar.QueryComponentL
import anduin.graphql.component.{FetchStrategy, GraphqlOptions}
import anduin.id.investmententity.InvestmentEntityId
import anduin.investorprofile.investmententity.permission.MemberTable.InvestmentEntityUserWithInfo
import anduin.model.common.user.UserId
import anduin.protobuf.investorprofile.investmententity.InvestmentEntityPermissionLevel
import anduin.rohan.operation.AnduinQuery
import com.anduin.stargazer.service.account.AccountUtils
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import io.circe.Codec
import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import japgolly.scalajs.react.extra.router.RouterCtl
import stargazer.model.routing.{Page, StaticAuthPage}

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration
import anduin.investorprofile.client.InvestmentEntityEndpointClient
import stargazer.component.routing.laminar.WithReactRouterL

import anduin.investmententity.*
import com.raquo.laminar.api.L.*

private[investmententity] case class Permissions(
  stableInvestmentEntityInfo: StableInvestmentEntityInfo
) {
  private val updatePermissionEventBus = new EventBus[(UserId, InvestmentEntityPermissionLevel)]
  private val removeMemberEventBus = new EventBus[RemoveMemberModal.RemoveData]
  private val memberLeaveEventBus = new EventBus[UserId]
  private val inviteMembersEventBus = new EventBus[Seq[NewInvestmentEntityUserParams]]
  private val isViewOnlyMode = stableInvestmentEntityInfo.permission.isViewer
  private val investmentEntityId = stableInvestmentEntityInfo.investmentEntityId

  private def onInviteMembers(
    newMembers: Seq[NewInvestmentEntityUserParams],
    forceRefetch: Observer[Unit]
  ) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        resp <- InvestmentEntityEndpointClient.inviteInvestmentEntityUsers(
          InviteUsersParams(newMembers, investmentEntityId)
        )
      } yield resp.fold(
        _ => Toast.error("Failed to invite new members"),
        _ => {
          Toast.success("Invitation sent successfully")
          forceRefetch.onNext(())
        }
      )
    }
  }

  private def onUpdatePermission(
    toUpdateUser: UserId,
    newPermission: InvestmentEntityPermissionLevel,
    forceRefetch: Observer[Unit]
  ) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        resp <- InvestmentEntityEndpointClient.updatePermission(
          UpdateUserPermissionParams(
            toUpdateUser,
            investmentEntityId,
            newPermission
          )
        )
      } yield resp.fold(
        _ => Toast.error("Failed to update member's permission"),
        _ => {
          Toast.success("Permission updated successfully")
          forceRefetch.onNext(())
        }
      )
    }
  }

  private def onRemoveMember(
    removeData: RemoveMemberModal.RemoveData,
    forceRefetch: Observer[Unit]
  ) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        resp <- InvestmentEntityEndpointClient.removeUser(
          RemoveUserParams(
            removeData.userId,
            investmentEntityId,
            removeData.notifyEmail
          )
        )
      } yield resp.fold(
        _ => Toast.error("Failed to remove member"),
        _ => {
          Toast.success("Member removed successfully")
          forceRefetch.onNext(())
        }
      )
    }
  }

  private def onMemberLeaves(
    router: RouterCtl[Page]
  ) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        resp <- InvestmentEntityEndpointClient.userLeaves(UserLeavesParams(investmentEntityId))
      } yield resp.fold(
        _ => Toast.error("Failed to process the request"),
        _ => {
          Toast.success("Left the profile successfully")
          // Redirect to dashboard
          router.set(StaticAuthPage.CommonEntryPoint).runNow()
        }
      )
    }
  }

  private def renderCta(
    members: List[InvestmentEntityUserWithInfo]
  ) = {
    if (isViewOnlyMode) {
      emptyNode
    } else {
      div(
        tw.mb24,
        ModalL(
          isClosable = None,
          renderContent = close => {
            // Handle duplicated users
            AddMemberModal(
              onCancel = close,
              allCurrentMembers = members,
              onAddMembers = inviteMembersEventBus.writer
            )()
          },
          renderTarget = open => {
            ButtonL(
              style = ButtonL.Style.Full(color = ButtonL.Color.Primary, icon = Option(Icon.Glyph.UserAdd)),
              onClick = open.contramap(_ => ())
            )("Add member")
          },
          size = ModalL.Size(width = ModalL.Width.Px1160),
          renderTitle = _ => "Add member"
        )()
      )
    }
  }

  def renderContent(
    data: GetAllInvestmentEntityUsersResponse,
    currentUserIdOpt: Option[UserId],
    forceRefetch: Observer[Unit]
  ): HtmlElement = {
    val members = data.users.toList.map { ieUserInfo =>
      InvestmentEntityUserWithInfo(
        ieUserInfo,
        ieUserInfo.userIdInfo.userInfo,
        ieUserInfo.inviterIdInfo.userInfo
      )
    }

    WithReactRouterL { router =>
      div(
        inviteMembersEventBus.events.map(onInviteMembers(_, forceRefetch)) --> Observer.empty,
        updatePermissionEventBus.events.map(e =>
          onUpdatePermission(
            e._1,
            e._2,
            forceRefetch
          )
        ) --> Observer.empty,
        removeMemberEventBus.events.map(onRemoveMember(_, forceRefetch)) --> Observer.empty,
        memberLeaveEventBus.events.map(_ => onMemberLeaves(router)) --> Observer.empty,
        tw.pt20.px24.flex.flexCol.hPc100,
        paddingBottom.px(84),
        // Header
        div(tw.mb16.heading2, "Members"),
        // CTA
        renderCta(members),
        renderMemberTable(
          members,
          currentUserIdOpt,
          forceRefetch
        )
      )
    }.amend(tw.hPc100)
  }

  private def renderMemberTable(
    members: List[InvestmentEntityUserWithInfo],
    currentUserIdOpt: Option[UserId],
    forceRefetch: Observer[Unit]
  ) = {
    currentUserIdOpt.fold[Node](emptyNode)(
      MemberTable(
        _,
        stableInvestmentEntityInfo = stableInvestmentEntityInfo,
        members = members,
        onUpdatePermission = updatePermissionEventBus.writer,
        onRemoveMember = removeMemberEventBus.writer,
        onLeaveMember = memberLeaveEventBus.writer,
        onRemindMember = forceRefetch.contramap[UserId](_ => ())
      )()
    )
  }

  private val query = AnduinQuery.InvestmentEntityUsers

  private val graphqlOptions = GraphqlOptions(
    pollInterval = FiniteDuration(3, TimeUnit.MINUTES),
    requestTimeout = FiniteDuration(60, TimeUnit.SECONDS)
  )

  def apply(): HtmlElement = {
    QueryComponentL[Permissions.Variables, Permissions.ChildProps](
      query,
      Val(Permissions.Variables(investmentEntityId)),
      graphqlOptions,
      initialFetchStrategy = FetchStrategy.ForcedFetch
    ).apply { queryData =>
      div(
        tw.hPc100,
        child <-- queryData.data.combineWith(AccountUtils.currentUserIdObs).map { case (data, currentUserIdOpt) =>
          data.fold[Node](
            div(
              tw.py20.px24.hPc100,
              BlockIndicatorL(
                title = Val(Some("Loading members...")),
                isFullHeight = true
              )()
            )
          ) { childProps =>
            renderContent(
              childProps.investmentEntityUsers,
              currentUserIdOpt,
              queryData.forceFetch
            )
          }
        }
      )

    }.amend(tw.hPc100)
  }

}

private[investorprofile] object Permissions {

  final case class Variables(
    investmentEntityId: InvestmentEntityId
  )

  object Variables {
    given Codec.AsObject[Variables] = deriveCodecWithDefaults
  }

  final case class ChildProps(
    investmentEntityUsers: GetAllInvestmentEntityUsersResponse
  )

  object ChildProps {
    given Codec.AsObject[ChildProps] = deriveCodecWithDefaults
  }

}
