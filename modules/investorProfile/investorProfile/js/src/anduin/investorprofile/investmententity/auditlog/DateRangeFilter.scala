// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.auditlog

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.dropdown.laminar.DropdownL

import anduin.investorprofile.models.TimeFilterModel

import com.raquo.laminar.api.L.*

private[investmententity] case class DateRangeFilter(
  dateRangeFilterSignal: Signal[Option[TimeFilterModel]],
  onChange: Observer[TimeFilterModel]
) {

  def apply(): HtmlElement = {
    div(
      DropdownL[TimeFilterModel](
        items = TimeFilterModel.TimeFilters.map(item => DropdownL.Item(value = item)),
        target = DropdownL.Target(
          appearance = DropdownL.Appearance.Minimal(height = ButtonL.Height.Fix24)
        ),
        value = dateRangeFilterSignal,
        valueToString = _.text,
        onChange = onChange
      )()
    )
  }

}
