// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.profile

import java.time.Instant

import com.raquo.airstream.timing.ThrottleStream
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.menu.laminar.MenuL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.progress.laminar.RadialIndicatorL
import design.anduin.components.responsive.ScreenWidth
import design.anduin.components.responsive.laminar.WithScreenWidthL
import design.anduin.components.toast.Toast
import design.anduin.components.tracker.laminar.VisibilityTrackerL
import design.anduin.style.CssVar
import design.anduin.style.tw.*
import zio.ZIO

import anduin.forms.engine.GaiaEngine.{EngineConfiguration, EngineContext}
import anduin.forms.engine.{GaiaEngine, GaiaState}
import anduin.forms.renderer.TableOfContent
import anduin.forms.utils.FormValidationUtils.{FieldAlert, FieldID, FieldIDTrait, NoAlert}
import anduin.forms.utils.{FormDataUtils, FormValidationUtils}
import anduin.forms.{FlexibleFormRenderer, Form, FormRenderer, FormRendererSkeleton}
import anduin.frontend.AirStreamUtils
import anduin.id.lpprofile.LpProfileId
import anduin.investorprofile.InvestorProfileCommons.{LpProfileInfo, SaveProfileDataParams}
import anduin.investorprofile.client.LpProfileEndpointClient
import anduin.protobuf.investorprofile.investmententity.InvestmentEntityPermissionLevel
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.utils.ZIOUtils

import cats.syntax.either.*
import com.raquo.laminar.api.L.*

private[investmententity] final case class ProfileDetailedView(
  userPermission: InvestmentEntityPermissionLevel,
  lpProfileInfoOptSignal: Signal[Option[LpProfileInfo]],
  targetedFieldAliasSignal: Signal[Option[String]],
  onRefresh: Observer[Unit]
) {

  private val formStateVar = Var[GaiaState](GaiaState.empty)
  private val formStateSignal = formStateVar.signal.distinct
  private val goToFieldEventBus = new EventBus[Either[String, FieldIDTrait]]
  private val formPercentagesVar = Var(0f)
  private val isEditingVar = Var(false)
  private val hideTableOfContentsVar = Var(false)

  private val fieldAlertVar: Var[FieldAlert] = Var(NoAlert)
  private val showWarningAndErrorMessageVar = Var(false)
  private val headerVisibleVar = Var(false)
  private val warningVisibleVar = Var(false)
  private val changeSaveVar: Var[Option[Instant]] = Var(None)
  private val headerNode = div()

  private val closeTableOfContentsEventBus = new EventBus[Boolean]
  private val closeTableOfContentsSignal = closeTableOfContentsEventBus.events.toSignal(false)

  private val gaiaEngineOptSignal = lpProfileInfoOptSignal.map(
    _.flatMap { lpProfileInfo =>
      GaiaEngine
        .make(
          lpProfileInfo.templateForm.form,
          EngineConfiguration.default,
          EngineContext.default
        )
        .toOption
    }
  )

  private val shouldShowWarningAndErrorMessageSignal = showWarningAndErrorMessageVar.signal
    .combineWith(isEditingVar.signal)
    .map { case (showMessage, isEditing) => showMessage && isEditing }

  private val screenWidthVar = Var[Option[ScreenWidth]](None)

  private val saveFormEventBus = new EventBus[GaiaState]

  private val saveFormThrottleStream = new ThrottleStream(
    saveFormEventBus.events,
    100,
    leading = true
  )

  private val forceSaveFormEventBus = new EventBus[Observer[Unit]]

  private def renderTableOfContentItem(
    render: TableOfContent.RenderTableOfContentItem
  ) = {
    Seq(
      div(
        tw.flexFill,
        // Section label
        render.label
      )
    )
  }

  private def renderTableOfContents(toc: Node, screenWidthSignal: Signal[ScreenWidth]) = {
    div(
      tw.bgGray0.overflowAuto,
      tw.fixed.left0.top0.bottom0.p16.z1,
      tw.lg(tw.relative.zAuto),
      hideTableOfContentsVar.signal.cls(tw.hidden),
      screenWidthSignal
        .map { screenWidth =>
          ScreenWidth.Small.minWidth <= screenWidth.minWidth && screenWidth.minWidth <= ScreenWidth.Medium.minWidth
        }
        .cls(tw.shadow4),
      // The close button
      div(
        tw.absolute.top0.right0.mt16.mr16,
        tw.lg(tw.hidden),
        ButtonL(
          style = ButtonL.Style.Minimal(icon = Option(Icon.Glyph.Cross)),
          onClick = Observer { _ =>
            hideTableOfContentsVar.set(true)
          }
        )()
      ),
      // The table of contents
      toc
    )
  }

  private def renderProfileForm(layout: FormRenderer.Layout) = {
    div(
      tw.flex.wPc100,
      // The main form
      layout
        .renderContainer()
        .amend(
          tw.flexFill.mr20,
          layout.renderMainForm()
        ),
      // The table of contents are stick on the right side
      div(
        styleProp(CssVar.Width.CustomWidth1) := "300px",
        tw.wCustom1,
        layout.renderTableOfContents()
      )
    )
  }

  private def renderFormSkeleton(layout: FormRendererSkeleton.Layout) = {
    div(
      tw.flex.wPc100,
      // The main form
      layout.renderContainer.amend(
        tw.flexFill.mr20,
        layout.renderMainForm
      ),
      // The table of contents are stick on the right side
      div(
        styleProp(CssVar.Width.CustomWidth1) := "300px",
        tw.wCustom1,
        layout.renderTableOfContents
      )
    )
  }

  private def renderProfileContent(screenWidthSignal: Signal[ScreenWidth]) = {
    lpProfileInfoOptSignal
      .combineWith(targetedFieldAliasSignal, gaiaEngineOptSignal)
      .map { case (lpProfileInfoOpt, targetedFieldAlias, gaiaEngineOpt) =>
        lpProfileInfoOpt
          .fold[Node](
            FormRendererSkeleton(
              layout = Option(renderFormSkeleton)
            )()
          ) { lpProfileInfo =>
            val formData = lpProfileInfo.templateForm

            formPercentagesVar.set(lpProfileInfo.dataFillPercentage)
            formStateVar.set(lpProfileInfo.dataState)

            div(
              closeTableOfContentsSignal.combineWith(screenWidthSignal) --> Observer[(Boolean, ScreenWidth)] {
                case (isTocClosed, screenWidth) =>
                  if (screenWidth.minWidth >= ScreenWidth.Large.minWidth) {
                    // Always show the table of contents in the large screen
                    hideTableOfContentsVar.set(false)
                  } else {
                    hideTableOfContentsVar.set(isTocClosed)
                  }
              },
              child <-- isEditingVar.signal.map { isEditing =>
                gaiaEngineOpt.fold[Node](emptyNode) { engine =>
                  FlexibleFormRenderer(
                    formData = formData,
                    dataObserver = if (isEditing) {
                      Observer[GaiaState] { formState =>
                        formStateVar.set(formState)
                        changeSaveVar.set(None)
                        saveFormEventBus.emit(formState)
                      }
                    } else {
                      Observer.empty[GaiaState]
                    },
                    engine = engine,
                    initialData = if (formStateVar.now().isEmpty) lpProfileInfo.dataState else formStateVar.now(),
                    initialKeyOpt = None,
                    submitButtonOpt = None,
                    isReadOnly = !isEditing,
                    showErrorSignal = Var(false).signal,
                    showWarningSignal = shouldShowWarningAndErrorMessageSignal,
                    dataToolConfigOpt = None,
                    renderTableOfContent = renderTableOfContents(_, screenWidthSignal),
                    onCloseTableOfContents = Observer[Unit] { _ =>
                      closeTableOfContentsEventBus.emit(true)
                    },
                    onFieldAlert = Observer[FieldAlert](fieldAlertVar.set),
                    goToFieldEventStream = goToFieldEventBus.events.map {
                      case Left(id)     => FieldID(id)
                      case Right(value) => value
                    },
                    formFillingPercentageSignal = formPercentagesVar.signal,
                    renderTableOfContentItem = Option(renderTableOfContentItem),
                    layout = Option(renderProfileForm)
                  )()
                }
              },
              saveFormThrottleStream --> Observer[GaiaState] { gaiaState =>
                saveProfileData(
                  lpProfileInfo.id,
                  formData.form,
                  gaiaState
                )
                ()
              },
              forceSaveFormEventBus.events.flatMapSwitch(onSuccess =>
                saveProfileData(
                  lpProfileInfo.id,
                  formData.form,
                  formStateVar.now(),
                  onSuccess
                )
              ) --> Observer.empty,
              onMountCallback { _ =>
                if (targetedFieldAlias.isDefined) {
                  targetedFieldAlias.foreach { fieldAlias =>
                    goToFieldEventBus.emit(fieldAlias.asLeft)
                  }
                }
              }
            )
          }
      }
  }

  private def saveProfileData(
    profileId: LpProfileId,
    form: Form,
    gaiaState: GaiaState,
    onSuccess: Observer[Unit] = Observer.empty
  ) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      ZIOUtils.when(gaiaState.nonEmpty)(
        LpProfileEndpointClient
          .saveProfileData(
            SaveProfileDataParams(
              profileId = profileId,
              newStateData = gaiaState.withoutPatchEffects
            )
          )
          .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
          .map { resp =>
            resp.fold(
              _ => Toast.error("Failed to save, please refresh and try again"),
              _ => {
                changeSaveVar.set(Some(Instant.now()))
                formPercentagesVar.set(
                  FormDataUtils.calculateProgressPerFields(form, gaiaState.defaultStateMap)
                )
                onSuccess.onNext(())
              }
            )
          }
      )
    }
  }

  private def renderAlertVisibilityTracker(
    backgroundColor: HtmlMod,
    fieldAlert: FieldAlert
  ) = {
    VisibilityTrackerL(
      onVisibilityChanged = warningVisibleVar.writer,
      node = div(
        tw.mb16.mx16.p12.rounded4,
        tw.lg(tw.mx0),
        backgroundColor,
        MissingFieldBlock(
          fieldAlert = fieldAlert,
          screenWidthSignal = screenWidthVar.signal,
          onJumpToField = goToFieldEventBus.writer.contramap(_.asRight)
        )()
      )
    )()
  }

  private def renderActionsEditingMode(changeSaveOpt: Option[Instant]) = {
    div(
      tw.flex.itemsCenter.textGray7,
      changeSaveOpt.map(_ =>
        div(
          tw.flex.itemsCenter.mr16,
          div(
            tw.textSuccess4.mr8,
            IconL(
              name = Val(Icon.Glyph.Check)
            )()
          ),
          "Changes saved"
        )
      ),
      ButtonL(
        style = ButtonL.Style.Full(),
        onClick = Observer { _ =>
          forceSaveFormEventBus.emit(onRefresh)
          isEditingVar.set(false)
          changeSaveVar.set(None)
        }
      )("Exit edit mode")
    )
  }

  private def renderActionsNormalMode(lpProfileInfo: LpProfileInfo) = {
    val isEmptyStateSignal = formStateSignal.map(_.events.isEmpty)

    div(
      tw.flex.itemsCenter,
      div(
        tw.mr8,
        PopoverL(
          position = PortalPosition.BottomRight,
          renderContent = _ => {
            MenuL(
              Seq(
                ClearFormConfirmationModal(
                  lpProfileInfo.id,
                  lpProfileInfo.templateForm.form,
                  disableMenuItemSignalOpt = Option(isEmptyStateSignal),
                  onDone = Observer { _ =>
                    onRefresh.onNext(())
                    formStateVar.set(GaiaState.empty)
                  }
                )()
              )
            )
          },
          renderTarget = (open, isOpened) => {
            ButtonL(
              style = ButtonL.Style.Minimal(
                icon = Option(Icon.Glyph.EllipsisHorizontal),
                isSelected = isOpened
              ),
              onClick = open.contramap(_ => ())
            )()
          }
        )()
      ),
      ButtonL(
        style = ButtonL.Style.Full(
          color = ButtonL.Color.Primary,
          icon = Some(Icon.Glyph.EditField)
        ),
        onClick = Observer { _ =>
          isEditingVar.set(true)
          changeSaveVar.set(None)
        },
        isDisabled = formStateSignal.map(_ != lpProfileInfo.dataState)
      )("Edit profile")
    )
  }

  private def renderHeader() = {
    headerNode.amend(
      child.maybe <-- lpProfileInfoOptSignal
        .combineWith(
          isEditingVar.signal,
          changeSaveVar.signal,
          formPercentagesVar.signal
        )
        .distinct
        .map { case (lpProfileInfoOpt, isEditing, changeSaveOpt, formPercentage) =>
          lpProfileInfoOpt.map { lpProfileInfo =>
            val roundedProgress = (formPercentage * 100).toInt
            div(
              tw.flex.itemsCenter.py20.px24,
              tw.borderBottom.border1.borderGray3,
              div(
                div(
                  tw.text20.leading32.fontSemiBold,
                  "Investment profile"
                ),
                div(
                  tw.flex.itemsCenter,
                  "Profile data:",
                  span(
                    tw.textPrimary3.mx8,
                    RadialIndicatorL(formPercentage.toDouble)()
                  ),
                  s"$roundedProgress%"
                )
              ),
              Option.when(userPermission.isOwner || userPermission.isEditor)(
                div(
                  tw.mlAuto,
                  if (isEditing) {
                    renderActionsEditingMode(changeSaveOpt)
                  } else {
                    renderActionsNormalMode(lpProfileInfo)
                  }
                )
              )
            )
          }
        }
    )
  }

  private def renderBody(screenWidthSignal: Signal[ScreenWidth]) = {
    div(
      tw.flexFill.overflowAuto.pt20.px24,
      paddingBottom.px(84),
      VisibilityTrackerL(
        onVisibilityChanged = headerVisibleVar.writer,
        node = div(tw.wPc100.hPx1)
      )(),
      // Warn if there's an incomplete field
      child.maybe <-- fieldAlertVar.signal
        .combineWith(shouldShowWarningAndErrorMessageSignal)
        .map { case (fieldAlert, showMessage) =>
          Option.when(showMessage) {
            fieldAlert match {
              case FormValidationUtils.FieldError(_) =>
                renderAlertVisibilityTracker(
                  tw.bgDanger1,
                  fieldAlert
                )
              case FormValidationUtils.FieldWarning(_) =>
                renderAlertVisibilityTracker(
                  tw.bgWarning1,
                  fieldAlert
                )
              case FormValidationUtils.NoAlert => emptyNode
            }
          }
        },
      child <-- renderProfileContent(screenWidthSignal)
    )
  }

  def apply(): HtmlElement = {
    WithScreenWidthL(
      render = screenWidthSignal => {
        div(
          tw.flex.itemsCenter.wPc100.hPc100,
          div(
            tw.hPc100.flex.flexCol.overflowHidden.flexFill,
            renderHeader(),
            renderBody(screenWidthSignal)
          )
        )
      }
    )()
  }

}
