// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.documents.tablecells

import design.anduin.components.text.laminar.TruncateL
import design.anduin.style.tw.*
import design.anduin.table.laminar.TableL

import anduin.investorprofile.InvestorProfileDocumentCommons.ProfileDocumentInfo

import com.raquo.laminar.api.L.*

private[investmententity] final case class NoteCell(
  renderProps: TableL.RenderCell[ProfileDocumentInfo]
) {

  def apply(): HtmlElement = {
    val profileDocument = renderProps.data
    val noteContent =
      if (profileDocument.documentEditableInfo.note.isEmpty) "--" else profileDocument.documentEditableInfo.note

    TruncateL(
      target = div(
        tw.whitespacePreWrap,
        noteContent
      ),
      title = Some(noteContent),
      lineClamp = Some(3)
    )()
  }

}
