// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.auditlog.profile

import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.style.CssVar
import design.anduin.style.tw.*
import design.anduin.table.laminar.TableL
import io.circe.Codec
import anduin.circe.generic.semiauto.deriveCodecWithDefaults

import anduin.investmententity.{LogItemResponse, LpProfileLogItemResponse}
import anduin.investorprofile.common.UserInfoAvatar
import anduin.investorprofile.investmententity.auditlog.{AuditLogTimeCell, EmptyAuditLogState}
import anduin.investorprofile.models.ParticipantModel
import anduin.model.common.user.{UserId, UserInfo}

import com.raquo.laminar.api.L.*
import anduin.model.codec.ProtoCodecs.given
import anduin.protobuf.investorprofile.auditlog.lpprofile.*

private[investmententity] case class ProfileAuditLogTable(
  auditLogsSignal: Signal[List[LpProfileLogItemResponse]],
  isFetchingSignal: Signal[Boolean],
  seenIndexOptSignal: Signal[Option[Int]],
  userInfoMapSignal: Signal[Map[UserId, UserInfo]]
) {

  given Codec.AsObject[ParticipantModel] = deriveCodecWithDefaults
  given Codec.AsObject[LogItemResponse] = deriveCodecWithDefaults
  given Codec.AsObject[LpProfileLogItemResponse] = deriveCodecWithDefaults

  private val seenIndexSignal = seenIndexOptSignal
    .combineWith(auditLogsSignal.map(_.length))
    .mapN((seenIndex, numLogs) => seenIndex.getOrElse(numLogs))

  private val timeColumn = TableL.Column[LpProfileLogItemResponse](
    title = "Time",
    field = "date",
    renderCell = renderProps => {
      AuditLogTimeCell(renderProps.data.timeStamp)().amend(
        seenIndexSignal.distinct --> Observer[Int] { seenIndex =>
          if (renderProps.data.eventOrder > seenIndex) {
            val color = if (renderProps.data.isWireInstruction) CssVar.Color.Warning1 else CssVar.Color.Primary1
            renderProps.cellComponent.getRow().getElement().style.backgroundColor = CssVar.toColorVar(color)
          }
        }
      )
    },
    width = Option(160)
  )

  private val actorColumn = TableL.Column[LpProfileLogItemResponse](
    title = "Actor",
    field = "actor",
    renderCell = renderProps => {
      div(
        child <-- userInfoMapSignal.map { userDataMap =>
          // TODO: @phucnguyen to find a better modal for actor. Now assume actor = None is system
          renderProps.data.actor.fold(div(tw.fontSemiBold, "System")) { userId =>
            UserInfoAvatar(userDataMap.get(userId))()
          }
        }
      )
    }
  )

  private val actionColumn = TableL.Column[LpProfileLogItemResponse](
    title = "Action",
    field = "actionName",
    renderCell = renderProps => ProfileActionCell(renderProps.data.logDetail)()
  )

  private val summaryColumn = TableL.Column[LpProfileLogItemResponse](
    title = "Summary",
    field = "summary",
    renderCell = renderProps => ProfileActionSummaryCell(renderProps.data.logDetail, userInfoMapSignal)(),
    widthGrow = Some(2)
  )

  private val sourceColumn = TableL.Column[LpProfileLogItemResponse](
    title = "Source",
    field = "source",
    renderCell = renderProps => {
      val cell = renderProps.data.source match {
        case _: ManualEdit => div("Manual Edit")
        case _: Api        => div("Api")
        case srcFundSub: ImportFromFundSub =>
          div("Transaction data ", span(tw.fontSemiBold, srcFundSub.fundName))
        case srcFile: ImportFromFile => div("Import data ", span(tw.fontSemiBold, srcFile.fileName))
        case srcAutoSave: AutoSaveFromFundSub =>
          div("Autosaved from ", span(tw.fontSemiBold, srcAutoSave.fundName))
        case _ => div("")
      }
      cell.amend(tw.whitespacePreWrap)
    },
    width = Option(160)
  )

  private val warningColumn = TableL.Column[LpProfileLogItemResponse](
    title = "",
    field = "warning",
    renderCell = renderProps =>
      if (renderProps.data.isWireInstruction) {
        div(
          tw.textWarning5,
          IconL(name = Val(Icon.Glyph.Warning))()
        )
      } else {
        div("")
      },
    width = Option(50)
  )

  private def handleRowRendered(rowRenderedData: TableL.RowRenderedData[LpProfileLogItemResponse]) = {
    rowRenderedData.rowComponent.getElement().classList.add(tw.group.css)
  }

  def apply(): HtmlElement = {
    TableL[LpProfileLogItemResponse](
      options = TableL.Options(
        layout = TableL.Layout.FitColumns
      ),
      columns = List(
        timeColumn,
        actorColumn,
        actionColumn,
        summaryColumn,
        sourceColumn,
        warningColumn
      ),
      dataSignal = auditLogsSignal,
      loading = TableL.Loading(
        criteria = isFetchingSignal
      ),
      placeholder = TableL.Placeholder(
        criteria = _.map(_.isEmpty),
        render = _ => EmptyAuditLogState()()
      ),
      onRowRendered = Observer[TableL.RowRenderedData[LpProfileLogItemResponse]](handleRowRendered)
    ).amend(maxHeight.px := 500)
  }

}
