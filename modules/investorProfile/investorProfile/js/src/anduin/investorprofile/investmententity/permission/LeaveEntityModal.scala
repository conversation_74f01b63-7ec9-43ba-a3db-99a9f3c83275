// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.permission

import anduin.investorprofile.investmententity.permission.MemberTable.InvestmentEntityUserWithInfo
import anduin.model.common.user.UserId
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL}
import design.anduin.style.tw.*
import org.scalajs.dom

import com.raquo.laminar.api.L.*

private[investmententity] case class LeaveEntityModal(
  member: InvestmentEntityUserWithInfo,
  entityName: String,
  onCancel: Observer[Unit],
  onLeave: Observer[UserId]
) {

  def apply(): HtmlElement = {
    div(
      ModalBodyL(
        div(
          tw.mb16,
          "Are you sure you want to leave ",
          span(tw.fontBold, s"$entityName?"),
          "You'll no longer be able to access this profile."
        )
      ),
      ModalFooterWCancelL(
        cancel = onCancel
      )(
        ButtonL(
          style = ButtonL.Style.Full(color = ButtonL.Color.Danger),
          onClick = Observer[dom.MouseEvent] { _ =>
            onCancel.onNext(())
            onLeave.onNext(member.entityInfo.userId)
          }
        )("Leave")
      )
    )
  }

}
