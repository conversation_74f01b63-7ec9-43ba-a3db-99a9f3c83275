// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.investmententity.auditlog

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.divider.laminar.DividerL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.style.tw.*

import com.raquo.laminar.api.L.*

private[investmententity] case class AuditLogActivityFilter[T](
  activities: List[T],
  selectedActivitiesSignal: Signal[List[T]],
  onSelectActivity: Observer[T],
  onUnselectActivity: Observer[T],
  onSelectAllActivity: Observer[Unit],
  onUnselectAllActivity: Observer[Unit],
  getActivityText: T => String
) {

  private val AllActionsText = "All actions"
  private val keywordVar = Var("")

  private val filterActivitiesSignal = keywordVar.signal.map { keyword =>
    if (keyword.isEmpty) {
      activities
    } else {
      activities.filter(eventType => getActivityText(eventType).toLowerCase().contains(keyword.toLowerCase().trim))
    }
  }

  private val hasActivitiesSignal = filterActivitiesSignal.map(_.nonEmpty)

  private def renderContent() = {
    div(
      tw.py8.px4.overflowXAuto,
      maxWidth := "340px",
      div(
        div(
          tw.mb8,
          TextBoxL(
            icon = Option(Icon.Glyph.Search),
            placeholder = "Search",
            value = keywordVar.signal,
            onChange = keywordVar.writer
          )()
        ),
        div(
          tw.py8,
          AuditLogItem(
            renderContent = () => div(AllActionsText),
            isSelectedSignal = selectedActivitiesSignal.map(_.length == activities.length),
            onItemClick = Observer[Boolean] {
              if (_) onUnselectAllActivity.onNext(()) else onSelectAllActivity.onNext(())
            }
          )()
        ),
        child.maybe <-- hasActivitiesSignal.map(Option.when(_)(DividerL()()))
      ),
      div(
        tw.overflowAuto.spaceY4,
        maxHeight := "360px",
        children <-- filterActivitiesSignal.map {
          _.map { activity =>
            AuditLogItem(
              isSelectedSignal = selectedActivitiesSignal.map(_.contains(activity)),
              renderContent = () => div(getActivityText(activity)),
              onItemClick = Observer[Boolean] {
                if (_) {
                  onUnselectActivity.onNext(activity)
                } else {
                  onSelectActivity.onNext(activity)
                }
              }
            )()
          }
        }
      )
    )
  }

  def apply(): HtmlElement = {
    div(
      PopoverL(
        position = PortalPosition.BottomLeft,
        renderContent = _ => renderContent(),
        renderTarget = (open, isOpened) => {
          ButtonL(
            style = ButtonL.Style.Minimal(
              height = ButtonL.Height.Fix24,
              isSelected = isOpened
            ),
            onClick = open.contramap(_ => ())
          )(
            div(
              tw.flex.itemsCenter.justifyCenter,
              child <-- selectedActivitiesSignal.map { selectedElements =>
                if (selectedElements.length == activities.length) {
                  AllActionsText
                } else {
                  s"${selectedElements.length} selected"
                }
              },
              div(
                tw.ml4.relative,
                IconL(name = Val(Icon.Glyph.CaretDown))()
              )
            )
          )
        }
      )()
    )
  }

}
