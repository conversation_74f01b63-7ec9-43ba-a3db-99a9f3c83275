// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.dashboard

import anduin.investmententity.CreateInvestmentEntityParams
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.field.laminar.FieldL
import design.anduin.components.field.laminar.FieldL.Helper
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL}
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.style.tw.*
import org.scalajs.dom

import com.raquo.laminar.api.L.*

private[investorprofile] case class CreateNewEntityModal(
  onCancel: Observer[Unit],
  onCreateEntity: Observer[CreateInvestmentEntityParams]
) {

  private val nameVar = Var("")
  private val customIdVar = Var("")

  def apply(): HtmlElement = {
    div(
      ModalBodyL(
        div(
          tw.mb16,
          FieldL(
            label = Option("Name"),
            requirement = FieldL.Requirement.Required
          )(
            TextBoxL(
              isAutoFocus = true,
              placeholder = "Profile name",
              value = nameVar.signal,
              onChange = nameVar.writer
            )()
          )
        ),
        FieldL(
          label = Option("Tracking ID"),
          helper = Helper.InTooltip(() =>
            div(maxWidth := "202px", "Tracking ID is a custom number used to monitor and reference an profile")
          ),
          requirement = FieldL.Requirement.Optional
        )(
          div(
            tw.wPc50,
            TextBoxL(
              placeholder = "Profile tracking ID",
              value = customIdVar.signal,
              onChange = customIdVar.writer
            )()
          )
        )
      ),
      div(
        tw.mb20,
        paddingLeft := "28px",
        paddingRight := "28px",
        LpLegalDisclaimer("Create")()
      ),
      ModalFooterWCancelL(
        cancel = onCancel
      )(
        ButtonL(
          style = ButtonL.Style.Full(color = ButtonL.Color.Primary),
          isDisabled = nameVar.signal.map(_.trim.isEmpty),
          onClick = Observer[dom.MouseEvent] { _ =>
            val customId = customIdVar.now().trim
            val entityParams = CreateInvestmentEntityParams(
              nameVar.now().trim,
              if (customId.isEmpty) None else Some(customId)
            )
            onCancel.onNext(())
            onCreateEntity.onNext(entityParams)
          }
        )("Create")
      )
    )
  }

}
