// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.dashboard

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterL, ModalFooterWCancelL}
import design.anduin.components.portal.laminar.PortalWrapperL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import org.scalajs.dom

import anduin.id.investmententity.InvestmentEntityId
import anduin.investmententity.InvestmentEntityInfo
import anduin.scalajs.pluralize.Pluralize
import stargazer.component.routing.laminar.WithReactRouterL
import stargazer.model.routing.DynamicAuthPage.LpProfile

private[investorprofile] case class DeleteEntityModal(
  entity: InvestmentEntityInfo,
  onCancel: Observer[Unit],
  onDelete: Observer[InvestmentEntityId]
) {

  def apply(): HtmlElement = {
    if (entity.numSubscriptions > 0) {
      WithReactRouterL { router =>
        div(
          ModalBodyL(
            div(
              tw.mb16,
              span(tw.fontBold, entity.entityName),
              s" is linked to ${Pluralize("subscription", entity.numSubscriptions, true)} and deleting it will also remove the ${Pluralize("link", entity.numSubscriptions)} between them ",
              TooltipL(
                renderTarget = IconL(name = Val(Icon.Glyph.Question), size = Icon.Size.Custom(12))()
                  .amend(tw.inlineBlock.textGray7),
                renderContent = _.amend(
                  "Links are used to associate a profile with a subscription for tracking purposes. They have no impact on your subscription’s content."
                ),
                targetWrapper = PortalWrapperL.Inline
              )()
            ),
            div(
              tw.mb16,
              span(tw.fontBold, "Note: "),
              "Subscriptions and their content won't be affected"
            )
          ),
          ModalFooterL(
            div(
              tw.flex.justifyBetween.itemsCenter,
              ButtonL(
                style = ButtonL.Style.Text(color = ButtonL.Color.Primary),
                onClick = Observer[dom.MouseEvent] { _ =>
                  router.set(LpProfile.InvestmentEntitySubscriptionsPage(entity.investmentEntityId)).runNow()
                }
              )("View linked subscriptions"),
              div(
                tw.flex.itemsCenter.spaceX8,
                ButtonL(onClick = Observer[dom.MouseEvent] { _ =>
                  onCancel.onNext(())
                })("Cancel"),
                ButtonL(
                  style = ButtonL.Style.Full(color = ButtonL.Color.Danger),
                  onClick = Observer[dom.MouseEvent] { _ =>
                    onCancel.onNext(())
                    onDelete.onNext(entity.investmentEntityId)
                  }
                )("Delete profile")
              )
            )
          )
        )
      }
    } else {
      div(
        ModalBodyL(
          div(
            tw.mb16,
            "Are you sure you want to delete ",
            span(tw.fontBold, entity.entityName),
            "? All of its data will be removed."
          )
        ),
        ModalFooterWCancelL(
          cancel = onCancel
        )(
          ButtonL(
            style = ButtonL.Style.Full(color = ButtonL.Color.Danger),
            onClick = Observer[dom.MouseEvent] { _ =>
              onCancel.onNext(())
              onDelete.onNext(entity.investmentEntityId)
            }
          )("Delete profile")
        )
      )
    }
  }

}
