// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.dashboard

import java.time.format.DateTimeFormatter

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.button.laminar.ButtonL.Color
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.progress.laminar.RadialIndicatorL
import design.anduin.components.text.laminar.TruncateL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import org.scalajs.dom

import anduin.id.investmententity.InvestmentEntityId
import anduin.investmententity.InvestmentEntityInfo
import anduin.investorprofile.dashboard.ListEntities.SortByDropdownItem
import anduin.scalajs.pluralize.Pluralize
import anduin.utils.{DateTimeUtils, StringUtils}
import com.anduin.stargazer.client.modules.routing.RoutingUtils.Router
import stargazer.component.routing.laminar.WithReactRouterL
import stargazer.model.routing.DynamicAuthPage.LpProfile.{
  InvestmentEntityAuditLogPage,
  InvestmentEntityOnboardingPage,
  InvestmentEntityProfilesPage
}

import com.raquo.laminar.api.L.*

private[investorprofile] case class EntityCardView(
  entities: Seq[InvestmentEntityInfo],
  onDeleteEntity: Observer[InvestmentEntityId],
  onUpdateEntity: Observer[EditEntityModal.EntityEditInfo],
  sortBySignal: Signal[ListEntities.SortByDropdownItem],
  listSuggestedInvestmentEntityNamesSize: Int,
  isOpenOnboardingFlowObserver: Observer[Boolean]
) {

  private def renderEntityCard(router: Router, entity: InvestmentEntityInfo) = {
    val roundedProgress = (entity.profileDataProgress * 100).toInt
    a(
      tw.noUnderline.hover(tw.noUnderline),
      href := router
        .urlFor(
          if (entity.userState.isInvited) {
            InvestmentEntityOnboardingPage(entity.investmentEntityId)
          } else {
            InvestmentEntityProfilesPage(entity.investmentEntityId)
          }
        )
        .value,
      div(
        tw.borderAll.borderGray3.rounded8.hover(tw.borderPrimary3),
        tw.textGray8.hover(tw.bgOpacity20.bgPrimary1),
        height := "184px",
        tw.flex.flexCol,
        // Header
        div(
          tw.px12.flex.itemsCenter,
          if (entity.numUpdates > 0) tw.justifyBetween else tw.justifyEnd,
          height := "44px",
          background := "url(/web/gondor/images/lp/entity-card-header.png) no-repeat top center",
          borderTopLeftRadius := "4px",
          borderTopRightRadius := "4px",
          backgroundSize.cover,
          Option.when(entity.numUpdates > 0) {
            TooltipL(
              renderContent = _.amend("Click to view updates"),
              renderTarget = {
                a(
                  tw.noUnderline.hover(tw.noUnderline),
                  href := router
                    .urlFor(InvestmentEntityAuditLogPage(entity.investmentEntityId))
                    .value,
                  div(
                    tw.rounded2.bgDanger1.textDanger5.textSmall.fontSemiBold.hover(tw.bgWarning2),
                    tw.py2.px6,
                    Pluralize(
                      "update",
                      entity.numUpdates,
                      true
                    )
                  )
                )
              }
            )()
          },
          EntityActionMenu(
            entity,
            onDeleteEntity,
            onUpdateEntity,
            renderTarget = (open, isOpened) => {
              button(
                tw.hPx32.wPx32.flex.itemsCenter.justifyCenter,
                tw.textGray0.rounded3.hover(tw.bgGray7),
                isOpened.cls(tw.bgGray7),
                onClick --> Observer[dom.MouseEvent] { e =>
                  e.preventDefault()
                  e.stopPropagation()
                  open.onNext(())
                },
                IconL(
                  name = Val(Icon.Glyph.EllipsisHorizontal)
                )()
              )
            }
          )()
        ),
        // Content
        div(
          tw.pt12.pb24.px20,
          tw.flexFill.flex.flexCol.justifyBetween,
          div(
            tw.flexFill,
            renderName(entity),
            div(
              tw.flex.itemsCenter.mt6.textSmall,
              TruncateL(
                target = div(entity.customId.fold(nbsp)(customId => s"Tracking ID: $customId"))
              )(),
              Option.when(entity.isWireInstructionUpdated) {
                div(
                  tw.flexNone.flex.itemsCenter,
                  entity.customId.map(_ => div(tw.bgGray3.py8.wPx1.hPc100.mx8)),
                  div(
                    tw.flex.itemsCenter.textWarning5,
                    IconL(name = Val(Icon.Glyph.Warning))(),
                    div(tw.ml4, "Wire instruction updated")
                  )
                )
              }
            )
          ),
          div(
            tw.grid.gridCols3.gapX16,
            div(
              TruncateL(
                target = div(tw.textGray7.mb8, "Profile data")
              )(),
              div(
                tw.flex.itemsCenter.overflowHidden,
                if (entity.profileDataProgress < 1.0) {
                  div(
                    tw.textPrimary3.mr8,
                    RadialIndicatorL(percent = entity.profileDataProgress.toDouble)()
                  )
                } else {
                  div(
                    tw.textSuccess5.mr8,
                    IconL(name = Val(Icon.Glyph.CheckCircle))()
                  )
                },
                div(tw.textBodyLarge.fontSemiBold, s"$roundedProgress%")
              )
            ),
            div(
              TruncateL(
                target = div(tw.textGray7.mb8, "Profile used in")
              )(),
              TruncateL(
                target = div(
                  tw.textBodyLarge.fontSemiBold,
                  StringUtils.pluralItem(entity.numSubscriptions, "subscription")
                )
              )()
            ),
            div(
              TruncateL(
                target = div(tw.textGray7.mb8, "Last updated")
              )(),
              TruncateL(
                target = div(
                  tw.textBodyLarge.fontSemiBold,
                  s" ${DateTimeUtils.formatInstant(entity.lastUpdated, DateTimeFormatter.ofPattern("MMM dd, yyyy"))(
                      using DateTimeUtils.defaultTimezone
                    )}"
                )
              )()
            )
          )
        )
      )
    )
  }

  private def renderName(
    entityInfo: InvestmentEntityInfo
  ) = {
    TruncateL(
      target = div(tw.heading4, entityInfo.entityName),
      title = Some(entityInfo.entityName)
    )()
  }

  private def renderSuggestedInvestmentEntity(numIeNames: Int) = {
    div(
      height := "184px",
      background := "linear-gradient(90deg, rgba(43,149,214,0.95) 0%, rgba(7,92,146,0.95) 100%), url(/web/gondor/images/lp/entity-card.png) no-repeat top left",
      backgroundSize.cover,
      tw.rounded4,
      tw.py16.px20.flex.flexCol,
      div(
        tw.heading2.textGray0,
        s"$numIeNames suggested ${Pluralize("profile", numIeNames)}"
      ),
      div(
        tw.textGray0.flexFill.overflowYAuto,
        "You have past subscriptions with forms based on investment profiles " +
          "that you haven't created yet. Use our profile suggestions to easily keep " +
          "track of them."
      ),
      div(
        tw.flex.itemsCenter.justifyEnd,
        ButtonL(
          style = ButtonL.Style.Full(color = Color.Gray0),
          onClick = Observer { _ =>
            isOpenOnboardingFlowObserver.onNext(true)
          }
        )(
          div(
            tw.flex,
            div(tw.textGray8, "See suggested profiles"),
            IconL(Val(Icon.Glyph.ChevronRight))().amend(tw.textGray7)
          )
        )
      )
    )
  }

  def apply(): HtmlElement = {
    WithReactRouterL { router =>
      div(
        marginBottom.px(84),
        child <-- sortBySignal.map { sortByType =>
          val sortedEntities = sortByType match {
            case SortByDropdownItem.LastUpdated => entities.sortWith((a, b) => a.lastUpdated.isAfter(b.lastUpdated))
            case SortByDropdownItem.EntityName  => entities.sortWith((a, b) => a.entityName < b.entityName)
          }
          div(
            tw.grid.gridCols2.gapX16.gapY20,
            tw.lg(tw.gridCols3),
            tw.xl(tw.gridCols3),
            sortedEntities.map(renderEntityCard(router, _)),
            Option.when(listSuggestedInvestmentEntityNamesSize > 0) {
              renderSuggestedInvestmentEntity(listSuggestedInvestmentEntityNamesSize)
            }
          )
        }
      )
    }
  }

}
