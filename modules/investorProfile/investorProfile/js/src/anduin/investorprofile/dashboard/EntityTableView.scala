// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.dashboard

import anduin.id.investmententity.InvestmentEntityId
import anduin.investmententity.InvestmentEntityInfo
import anduin.investorprofile.investmententity.permission.PermissionCopyUtils
import anduin.scalajs.pluralize.Pluralize
import anduin.utils.DateTimeUtils
import com.anduin.stargazer.client.modules.routing.RoutingUtils.Router
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.progress.laminar.RadialIndicatorL
import design.anduin.components.tag.laminar.TagL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import design.anduin.table.laminar.TableL
import io.circe.Codec
import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import org.scalajs.dom
import org.scalajs.dom.{MouseEvent, window}
import stargazer.model.routing.DynamicAuthPage.LpProfile.{
  InvestmentEntityAuditLogPage,
  InvestmentEntityOnboardingPage,
  InvestmentEntityProfilesPage
}
import java.time.Instant
import java.time.format.DateTimeFormatter

import design.anduin.components.button.laminar.ButtonL.Color
import design.anduin.components.text.laminar.TruncateL
import stargazer.component.routing.laminar.WithReactRouterL

import com.raquo.laminar.api.L.*
import anduin.model.codec.ProtoCodecs.given

private[investorprofile] case class EntityTableView(
  entities: Seq[InvestmentEntityInfo],
  onDeleteEntity: Observer[InvestmentEntityId],
  onUpdateEntity: Observer[EditEntityModal.EntityEditInfo],
  listSuggestedInvestmentEntityNamesSize: Int,
  isOpenOnboardingFlowObserver: Observer[Boolean]
) {

  private def sortByLastUpdated(sorter: TableL.ColumnSorter[InvestmentEntityInfo]): Double = {
    val firstDate = sorter.a.map(_.lastUpdated).getOrElse(Instant.MIN)
    val secondDate = sorter.b.map(_.lastUpdated).getOrElse(Instant.MIN)
    firstDate.compareTo(secondDate).toDouble
  }

  private def renderEntityCell(entity: InvestmentEntityInfo) = {
    div(
      div(tw.textBody.fontSemiBold.mb4, TruncateL(target = div(entity.entityName))()),
      div(
        tw.flex.itemsCenter.mt6.textSmall,
        entity.customId.fold(nbsp)(customId => s"Tracking ID: $customId"),
        Option.when(entity.isWireInstructionUpdated) {
          div(
            tw.flex.itemsCenter,
            div(tw.bgGray3.py8.wPx1.hPc100.ml8),
            div(
              tw.flex.itemsCenter.textWarning5.ml8,
              IconL(name = Val(Icon.Glyph.Warning))(),
              div(tw.ml4, "Wire instruction updated")
            )
          )
        }
      )
    )
  }

  private def renderProfileDataCell(entity: InvestmentEntityInfo) = {
    val roundedProgress = (entity.profileDataProgress * 100).toInt
    div(
      tw.flex.itemsCenter,
      if (entity.profileDataProgress < 1.0) {
        div(
          tw.textPrimary3.mr8,
          RadialIndicatorL(percent = entity.profileDataProgress.toDouble)()
        )
      } else {
        div(
          tw.textSuccess5.mr8,
          IconL(name = Val(Icon.Glyph.CheckCircle))()
        )
      },
      div(tw.textBody.fontSemiBold, s"$roundedProgress%")
    )
  }

  private def renderSubscriptionsCell(entity: InvestmentEntityInfo) = {
    div(
      Pluralize(
        "subscription",
        entity.numSubscriptions,
        true
      )
    )
  }

  private def renderPermissionCell(entity: InvestmentEntityInfo) = {
    val (color, label) = PermissionCopyUtils.permissionTagInfo(entity.permission)
    TagL(label = Val(label), color = Val(color))()
  }

  private def renderLastUpdatedCell(entity: InvestmentEntityInfo) = {
    div(
      s"${DateTimeUtils.formatInstant(entity.lastUpdated, DateTimeFormatter.ofPattern("MMMM dd, yyyy"))(
          using DateTimeUtils.defaultTimezone
        )}"
    )
  }

  private def handleRowRendered(router: Router, rowRenderedData: TableL.RowRenderedData[InvestmentEntityInfo]) = {
    rowRenderedData.getData().foreach { entity =>
      val url = router
        .urlFor(
          if (entity.userState.isInvited) {
            InvestmentEntityOnboardingPage(entity.investmentEntityId)
          } else {
            InvestmentEntityProfilesPage(entity.investmentEntityId)
          }
        )
        .value
      rowRenderedData.rowComponent.getElement().onclick = (e: MouseEvent) => {
        e.preventDefault()
        e.stopPropagation()
        window.location.href = url
      }
    }
  }

  private def renderNewUpdatesCell(entity: InvestmentEntityInfo) = {
    WithReactRouterL { router =>
      div(
        tw.flex.itemsCenter,
        Option.when(entity.numUpdates > 0) {
          TooltipL(
            renderContent = _.amend("Click to view updates"),
            renderTarget = {
              a(
                tw.noUnderline.hover(tw.noUnderline),
                href := router
                  .urlFor(InvestmentEntityAuditLogPage(entity.investmentEntityId))
                  .value,
                div(
                  tw.rounded2.bgDanger1.textDanger5.textSmall.fontSemiBold.hover(tw.bgWarning2),
                  tw.py2.px6,
                  Pluralize(
                    "update",
                    entity.numUpdates,
                    true
                  )
                )
              )
            }
          )()
        }
      )
    }
  }

  private def renderActionCell(entity: InvestmentEntityInfo) = {
    EntityActionMenu(
      entity,
      onDeleteEntity,
      onUpdateEntity,
      renderTarget = (open, isOpened) => {
        ButtonL(
          style = ButtonL.Style.Minimal(
            icon = Option(Icon.Glyph.EllipsisHorizontal),
            isSelected = isOpened
          ),
          title = "More actions",
          onClick = Observer[dom.MouseEvent] { e =>
            e.preventDefault()
            e.stopPropagation()
            open.onNext(())
          }
        )()
      }
    )()
  }

  // Setup columns
  private val entityColumn = TableL.Column[InvestmentEntityInfo](
    isSortable = true,
    title = "Profile",
    field = "entityName",
    renderCell = renderProps => renderEntityCell(renderProps.data),
    widthGrow = Option(3)
  )

  private val profileDataColumn = TableL.Column[InvestmentEntityInfo](
    isSortable = true,
    title = "Profile data",
    field = "profileDataProgress",
    renderCell = renderProps => renderProfileDataCell(renderProps.data)
  )

  private val subscriptionsColumn = TableL.Column[InvestmentEntityInfo](
    isSortable = true,
    title = "Profile used in",
    field = "numSubscriptions",
    renderCell = renderProps => renderSubscriptionsCell(renderProps.data)
  )

  private val permissionColumn = TableL.Column[InvestmentEntityInfo](
    isSortable = true,
    title = "Permissions",
    field = "permission.value",
    renderCell = renderProps => renderPermissionCell(renderProps.data)
  )

  private val lastUpdatedColumn = TableL.Column[InvestmentEntityInfo](
    isSortable = true,
    title = "Last updated",
    field = "lastUpdated",
    sortWith = Option(sortByLastUpdated),
    renderCell = renderProps => renderLastUpdatedCell(renderProps.data)
  )

  private val newUpdateColumn = TableL.Column[InvestmentEntityInfo](
    isSortable = true,
    title = "New updates",
    field = "numUpdates",
    renderCell = renderProps => renderNewUpdatesCell(renderProps.data)
  )

  private val actionColumn = TableL.Column[InvestmentEntityInfo](
    title = "",
    field = "",
    renderCell = renderProps => renderActionCell(renderProps.data),
    width = Option(56)
  )

  private given Codec.AsObject[InvestmentEntityInfo] = deriveCodecWithDefaults

  private def renderSuggestedInvestmentEntity(numIeNames: Int) = {
    div(
      tw.rounded4,
      background := "linear-gradient(90deg, rgba(43,149,214,0.95) 0%, rgba(7,92,146,0.95) 100%), url(/web/gondor/images/lp/entity-card.png) no-repeat top left",
      backgroundSize.cover,
      height := "100px",
      tw.py12.px20.flex,
      div(
        tw.flexFill,
        div(
          tw.heading2.textGray0,
          s"$numIeNames suggested ${Pluralize("profile", numIeNames)}"
        ),
        div(
          tw.textGray0.flexFill.overflowYAuto,
          div(
            "You have past subscriptions with forms based on investment profiles " +
              "that you haven't created yet."
          ),
          div("Use our profile suggestions to easily keep track of them.")
        )
      ),
      div(
        tw.flex.itemsCenter,
        ButtonL(
          style = ButtonL.Style.Full(color = Color.Gray0),
          onClick = Observer { _ =>
            isOpenOnboardingFlowObserver.onNext(true)
          }
        )(
          div(
            tw.flex,
            div(tw.textGray8, "See suggested profiles"),
            IconL(Val(Icon.Glyph.ChevronRight))().amend(tw.textGray7)
          )
        )
      )
    )
  }

  def apply(): HtmlElement = {
    WithReactRouterL { router =>
      div(
        tw.flex.flexCol.hPc100,
        Option.when(entities.nonEmpty) {
          TableL[InvestmentEntityInfo](
            options = TableL.Options(
              layout = TableL.Layout.FitColumns
            ),
            columns = List(
              entityColumn,
              profileDataColumn,
              subscriptionsColumn,
              lastUpdatedColumn,
              newUpdateColumn,
              permissionColumn,
              actionColumn
            ),
            initialSortColumns = List(
              TableL.SortColumn(column = lastUpdatedColumn, direction = TableL.ColumnSortDirection.Desc)
            ),
            dataSignal = Val(entities.toList),
            onRowRendered = Observer[TableL.RowRenderedData[InvestmentEntityInfo]](handleRowRendered(router, _))
          ).amend(
            tw.flexFill,
            tw.mb12
          )
        },
        Option.when(listSuggestedInvestmentEntityNamesSize > 0) {
          renderSuggestedInvestmentEntity(listSuggestedInvestmentEntityNamesSize)
        }
      )
    }.amend(tw.hPc100, paddingBottom.px(60))
  }

}
