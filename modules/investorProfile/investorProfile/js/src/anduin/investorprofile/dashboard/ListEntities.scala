// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.dashboard

import anduin.id.investmententity.InvestmentEntityId
import anduin.investmententity.{CreateInvestmentEntityParams, InvestmentEntityInfo}
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.dropdown.laminar.DropdownL
import design.anduin.components.icon.Icon
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.style.tw.*
import org.scalajs.dom

import anduin.investorprofile.dashboard.ListEntities.SortByDropdownItem

import com.raquo.laminar.api.L.*

private[investorprofile] case class ListEntities(
  entities: Seq[InvestmentEntityInfo],
  canCreateEntity: Signal[Boolean],
  viewTypeSignal: Signal[Dashboard.ViewType],
  onSwitchView: Observer[Dashboard.ViewType],
  onCreateEntity: Observer[CreateInvestmentEntityParams],
  onDeleteEntity: Observer[InvestmentEntityId],
  onUpdateEntity: Observer[EditEntityModal.EntityEditInfo],
  listSuggestedInvestmentEntityNamesSize: Int,
  isOpenOnboardingFlowObserver: Observer[Boolean]
) {

  private val keywordVar = Var("")
  private val filterEntitiesVar = Var(entities)
  private val sortByTypeVar = Var[SortByDropdownItem](SortByDropdownItem.LastUpdated)

  private def handleSearch(keyword: String) = {
    keywordVar.set(keyword)
    if (keyword.isEmpty) {
      filterEntitiesVar.set(entities)
    } else {
      filterEntitiesVar.set(entities.filter(_.entityName.toLowerCase.contains(keyword.toLowerCase)))
    }
  }

  def apply(): HtmlElement = {
    div(
      tw.flex.flexCol.hPc100,
      // Header
      div(
        tw.mb20.flexNone.flex.justifyBetween.itemsCenter,
        div(
          tw.heading1.textGray8,
          "Investment profiles"
        ),
        div(
          tw.ml16,
          width := "300px",
          TextBoxL(
            icon = Option(Icon.Glyph.Search),
            placeholder = "Search...",
            value = keywordVar.signal,
            onChange = Observer[String](handleSearch)
          )()
        )
      ),
      // Action bar
      div(
        tw.mb20.flexNone.flex.itemsCenter,
        child.maybe <-- viewTypeSignal.map(viewType =>
          Option.when(viewType == Dashboard.ViewType.Card) {
            div(
              tw.flex.itemsCenter,
              div(
                tw.textGray7.mr8,
                "Sort:"
              ),
              div(
                tw.mr24,
                DropdownL[SortByDropdownItem](
                  items = Seq(
                    DropdownL.Item(value = SortByDropdownItem.EntityName),
                    DropdownL.Item(value = SortByDropdownItem.LastUpdated)
                  ),
                  minWidth = 180,
                  value = sortByTypeVar.signal.map(Option(_)),
                  valueToString = _.label,
                  target = DropdownL.Target(
                    appearance = DropdownL.Appearance.Minimal(isFullWidth = true)
                  ),
                  onChange = sortByTypeVar.writer
                )()
              )
            )
          }
        ),
        div(
          tw.textGray7.mr8,
          "Layout:"
        ),
        div(
          tw.mr8,
          ButtonL(
            style = ButtonL.Style.Minimal(
              icon = Option(Icon.Glyph.ViewGrid),
              isSelected = viewTypeSignal.map(_ == Dashboard.ViewType.Card),
              height = ButtonL.Height.Fix24
            ),
            onClick = Observer[dom.MouseEvent] { _ =>
              onSwitchView.onNext(Dashboard.ViewType.Card)
            }
          )()
        ),
        div(
          tw.mr8,
          ButtonL(
            style = ButtonL.Style.Minimal(
              icon = Option(Icon.Glyph.ViewList),
              isSelected = viewTypeSignal.map(_ == Dashboard.ViewType.Table),
              height = ButtonL.Height.Fix24
            ),
            onClick = Observer[dom.MouseEvent] { _ =>
              onSwitchView.onNext(Dashboard.ViewType.Table)
            }
          )()
        ),
        div(
          tw.mlAuto,
          CreateNewEntityButton(
            canCreateEntity = canCreateEntity,
            onCreateEntity = onCreateEntity
          )()
        )
      ),
      // List entities
      child <-- filterEntitiesVar.signal.combineWith(viewTypeSignal).mapN { case (filterEntities, viewType) =>
        if (filterEntities.isEmpty && listSuggestedInvestmentEntityNamesSize <= 0) {
          div(
            tw.hPc100,
            EntityEmptyView()()
          )
        } else {
          viewType match {
            case Dashboard.ViewType.Card =>
              EntityCardView(
                entities = filterEntities,
                onDeleteEntity = onDeleteEntity,
                onUpdateEntity = onUpdateEntity,
                sortBySignal = sortByTypeVar.signal,
                listSuggestedInvestmentEntityNamesSize = listSuggestedInvestmentEntityNamesSize,
                isOpenOnboardingFlowObserver = isOpenOnboardingFlowObserver
              )()
            case Dashboard.ViewType.Table =>
              EntityTableView(
                entities = filterEntities,
                onDeleteEntity = onDeleteEntity,
                onUpdateEntity = onUpdateEntity,
                listSuggestedInvestmentEntityNamesSize = listSuggestedInvestmentEntityNamesSize,
                isOpenOnboardingFlowObserver = isOpenOnboardingFlowObserver
              )()
          }
        }
      }
    )
  }

}

object ListEntities {

  sealed trait SortByDropdownItem derives CanEqual {
    def label: String
  }

  object SortByDropdownItem {

    case object EntityName extends SortByDropdownItem {
      override def label: String = "Profile name"
    }

    case object LastUpdated extends SortByDropdownItem {
      override def label: String = "Last updated"
    }

  }

}
