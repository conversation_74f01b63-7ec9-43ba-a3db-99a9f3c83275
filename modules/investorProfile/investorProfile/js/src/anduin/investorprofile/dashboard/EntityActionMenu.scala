// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.dashboard

import anduin.id.investmententity.InvestmentEntityId
import anduin.investmententity.InvestmentEntityInfo
import design.anduin.components.menu.laminar.{MenuDividerL, MenuItemL, MenuL}
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import japgolly.scalajs.react.extra.router.RouterCtl
import stargazer.model.routing.DynamicAuthPage.LpProfile.{InvestmentEntityOnboardingPage, InvestmentEntityProfilesPage}
import stargazer.model.routing.Page

import stargazer.component.routing.laminar.WithReactRouterL

import com.raquo.laminar.api.L.*

final case class EntityActionMenu(
  entity: InvestmentEntityInfo,
  onDeleteEntity: Observer[InvestmentEntityId],
  onUpdateEntity: Observer[EditEntityModal.EntityEditInfo],
  renderTarget: (Observer[Unit], Signal[Boolean]) => Node
) {

  private def openEntityMenuItem(
    router: RouterCtl[Page]
  ) = {
    MenuItemL(
      url = router
        .urlFor(
          if (entity.userState.isInvited) {
            InvestmentEntityOnboardingPage(entity.investmentEntityId)
          } else {
            InvestmentEntityProfilesPage(entity.investmentEntityId)
          }
        )
        .value
    )("Open investment profile")
  }

  private def editEntityMenuItem(closePopover: Observer[Unit]) = {
    ModalL(
      isClosable = None,
      renderTitle = _ => "Edit name and tracking ID",
      renderContent = close => {
        EditEntityModal(
          entity = EditEntityModal.EntityInfo(
            entity.investmentEntityId,
            entity.entityName,
            entity.customId,
            entity.numAssociatedSubscriptions
          ),
          onClose = Observer.combine(close, closePopover),
          onUpdate = onUpdateEntity
        )()
      },
      renderTarget = open => {
        MenuItemL(
          onClick = open
        )("Edit name and tracking ID")
      }
    )()
  }

  private def deleteEntityMenuItem(closePopover: Observer[Unit]) = {
    ModalL(
      isClosable = None,
      renderTitle = _ => "Delete investment profile",
      renderContent = close => {
        DeleteEntityModal(
          entity = entity,
          onCancel = Observer.combine(close, closePopover),
          onDelete = onDeleteEntity
        )()
      },
      renderTarget = open => {
        MenuItemL(
          color = MenuItemL.ColorDanger,
          onClick = open
        )("Delete investment profile")
      }
    )()
  }

  def apply(): HtmlElement = {
    div(
      PopoverL(
        position = PortalPosition.BottomRight,
        renderContent = closePopover => {
          WithReactRouterL { router =>
            MenuL(
              if (entity.permission.isOwner) {
                Seq(
                  openEntityMenuItem(router),
                  editEntityMenuItem(closePopover),
                  MenuDividerL(),
                  deleteEntityMenuItem(closePopover)
                )
              } else {
                Seq(
                  openEntityMenuItem(router)
                )
              }
            )
          }
        },
        renderTarget = renderTarget
      )()
    )
  }

}
