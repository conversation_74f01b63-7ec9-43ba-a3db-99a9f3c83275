// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.dashboard

import design.anduin.style.tw.*

import com.raquo.laminar.api.L.*

final case class LpLegalDisclaimer(
  text: String
) {
  def apply(): HtmlElement = LpLegalDisclaimer.render(this)
}

object LpLegalDisclaimer {
  private type Props = LpLegalDisclaimer

  private def render(props: Props): HtmlElement = {
    div(
      tw.text11.textGray7.itemsCenter,
      "By clicking ",
      span(
        tw.textGray8.fontSemiBold,
        props.text
      ),
      ", I consent to Anduin's ",
      a(
        tw.textPrimary5.underline,
        href := "https://anduintransact.com/terms/",
        target := "_blank",
        "Terms of Service"
      ),
      " and ",
      a(
        tw.textPrimary5.underline,
        href := "https://anduintransact.com/privacy/",
        target := "_blank",
        "Privacy Policy"
      )
    )
  }

}
