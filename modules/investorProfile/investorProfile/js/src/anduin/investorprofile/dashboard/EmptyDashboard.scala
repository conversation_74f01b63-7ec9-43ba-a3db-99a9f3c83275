// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.dashboard

import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.ModalL

import anduin.investmententity.CreateInvestmentEntityParams
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.style.tw.*

import anduin.investorprofile.common.LearnMorePopOver

import com.raquo.laminar.api.L.*

private[investorprofile] case class EmptyDashboard(
  canCreateEntity: Signal[Boolean],
  onCreateEntity: Observer[CreateInvestmentEntityParams]
) {

  private val icon = div(
    height := "52px",
    width := "52px",
    tw.roundedFull,
    tw.bgWarning1.flex.itemsCenter.justifyCenter.bgOpacity30.textWarning3,
    IconL(name = Val(Icon.Glyph.UserInfo), size = Icon.Size.Px24)()
  )

  def apply(): HtmlElement = {
    NonIdealStateL(
      icon = icon,
      title = "Create an investment profile",
      description = div(
        tw.maxWPx480.textCenter,
        "Create an investment profile that can be used to subscribe to funds, save subscription data, and pre-fill new subscriptions. ",
        LearnMorePopOver("Learn more")()
      ),
      action = div(
        tw.flex.itemsCenter,
        ModalL(
          isClosable = None,
          renderContent = close => {
            CreateNewEntityModal(
              onCancel = close,
              onCreateEntity = onCreateEntity
            )()
          },
          renderTarget = open => {
            ButtonL(
              style = ButtonL.Style.Ghost(color = ButtonL.Color.Primary, isFullWidth = true),
              onClick = open.contramap(_ => ()),
              isDisabled = canCreateEntity.map(!_)
            )("Create profile").amend(tw.wPx480, height := "52px")
          },
          size = ModalL.Size(width = ModalL.Width.Px480),
          renderTitle = _ => "Create new investment profile"
        )()
      )
    )()
  }

}
