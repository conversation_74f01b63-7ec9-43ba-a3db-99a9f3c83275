// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.dashboard

import anduin.actionlogger.ActionEventLoggerJs
import anduin.frontend.AirStreamUtils
import anduin.graphql.component.laminar.QueryComponentL
import anduin.graphql.component.{FetchStrategy, GraphqlOptions}
import anduin.id.investmententity.InvestmentEntityId
import anduin.investmententity.{
  CreateInvestmentEntityParams,
  DeleteInvestmentEntityParams,
  EditInvestmentEntityParams,
  InvestmentEntityDashboardResponse,
  InvestmentEntityDashboardWithSubscriptionsResponse
}
import anduin.rohan.operation.AnduinQuery
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import io.circe.Codec
import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration

import anduin.investorprofile.client.InvestmentEntityEndpointClient
import anduin.investorprofile.dashboard.Dashboard.ChildProps
import anduin.investorprofile.investmententity.HelpMenuPopOver
import anduin.investorprofile.onboarding.OnboardingEntryPoint

import com.raquo.laminar.api.L.*

private[investorprofile] case class Dashboard() {

  private val viewTypeVar = Var[Dashboard.ViewType](Dashboard.ViewType.Card)
  private val createEntityEventBus = new EventBus[CreateInvestmentEntityParams]
  private val editEntityEventBus = new EventBus[EditEntityModal.EntityEditInfo]
  private val deleteEntityEventBus = new EventBus[InvestmentEntityId]

  private val dashboardDataVar = Var[ChildProps](
    ChildProps(
      InvestmentEntityDashboardWithSubscriptionsResponse(
        InvestmentEntityDashboardResponse(
          hasPermissionToCreateNewEntity = false,
          hasDeletedInvestmentEntities = false,
          Seq.empty
        ),
        Seq.empty
      )
    )
  )

  private val isLoadingVar = Var(true)

  private val listSuggestedInvestmentEntityNamesSizeSignal =
    dashboardDataVar.signal.map { data =>
      val entities = data.investmentEntityDashboardWithSubscriptions.dashboardData.entities
      val subscriptions = data.investmentEntityDashboardWithSubscriptions.notLinkedSubscriptions
      subscriptions
        .map(_.subscriptionName)
        .distinct
        .count(firmName =>
          firmName.nonEmpty &&
            !entities.map(_.entityName).contains(firmName)
        )
    }.distinct

  private val isUserHasInvestmentEntitiesSignal =
    dashboardDataVar.signal.map(_.investmentEntityDashboardWithSubscriptions.dashboardData.entities.nonEmpty).distinct

  private val isUserHasDeletedEntitiesSignal =
    dashboardDataVar.signal
      .map(_.investmentEntityDashboardWithSubscriptions.dashboardData.hasDeletedInvestmentEntities)
      .distinct

  private val ieDashboardDataSignal =
    dashboardDataVar.signal.map(_.investmentEntityDashboardWithSubscriptions.dashboardData).distinct

  private val isOpenOnboardingFlowVar = Var(false)

  private def onCreateEntity(
    entityParams: CreateInvestmentEntityParams,
    forceRefetch: Observer[Unit]
  ) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        resp <- InvestmentEntityEndpointClient.createInvestmentEntity(entityParams)
      } yield resp.fold(
        _ => Toast.error("Failed to create investment profile"),
        _ => {
          Toast.success("Investment Profile created")
          ActionEventLoggerJs.logPageViewCb(Some("ActionEventIaAppCreateEntity")).runNow()
          forceRefetch.onNext(())
        }
      )
    }
  }

  private def onDeleteEntity(
    entityId: InvestmentEntityId,
    forceRefetch: Observer[Unit]
  ) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        resp <- InvestmentEntityEndpointClient.deleteInvestmentEntity(
          DeleteInvestmentEntityParams(entityId)
        )
      } yield resp.fold(
        _ => Toast.error("Failed to deleted investment profile"),
        _ => {
          Toast.success("Investment profile deleted")
          forceRefetch.onNext(())
        }
      )
    }
  }

  private def onEditEntity(
    data: EditEntityModal.EntityEditInfo,
    forceRefetch: Observer[Unit]
  ) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        resp <- InvestmentEntityEndpointClient.editInvestmentEntity(
          EditInvestmentEntityParams(
            data.investmentEntityId,
            data.entityName,
            data.customId
          )
        )
      } yield resp.fold(
        _ => Toast.error("Failed to edit investment profile"),
        _ => {
          Toast.success("Investment profile updated")
          forceRefetch.onNext(())
        }
      )
    }
  }

  private def renderDashboard() = {
    div(
      tw.hPc100.wPc100,
      child <-- ieDashboardDataSignal.combineWith(listSuggestedInvestmentEntityNamesSizeSignal).map {
        case (dashboardData, listSuggestedInvestmentEntityNamesSize) =>
          val entities = dashboardData.entities
          val canCreateEntity =
            Val(dashboardData.hasPermissionToCreateNewEntity)
          if (entities.isEmpty && listSuggestedInvestmentEntityNamesSize == 0) {
            EmptyDashboard(
              canCreateEntity = canCreateEntity,
              onCreateEntity = createEntityEventBus.writer
            )().amend(
              tw.hPc100.wPc100,
              tw.flex.flexCol.itemsCenter.justifyCenter
            )
          } else {
            ListEntities(
              entities = entities,
              canCreateEntity = canCreateEntity,
              viewTypeSignal = viewTypeVar.signal,
              onSwitchView = viewTypeVar.writer,
              onCreateEntity = createEntityEventBus.writer,
              onDeleteEntity = deleteEntityEventBus.writer,
              onUpdateEntity = editEntityEventBus.writer,
              listSuggestedInvestmentEntityNamesSize,
              isOpenOnboardingFlowVar.writer
            )().amend(
              tw.p24
            )
          }
      }
    )
  }

  private def renderContent(
    forceRefetch: Observer[Unit]
  ) = {
    div(
      tw.hPc100.wPc100,
      createEntityEventBus.events.map(onCreateEntity(_, forceRefetch)) --> Observer.empty,
      editEntityEventBus.events.map(onEditEntity(_, forceRefetch)) --> Observer.empty,
      deleteEntityEventBus.events.map(onDeleteEntity(_, forceRefetch)) --> Observer.empty,
      listSuggestedInvestmentEntityNamesSizeSignal
        .combineWith(isUserHasInvestmentEntitiesSignal, isUserHasDeletedEntitiesSignal)
        .distinct --> Observer[
        (Int, Boolean, Boolean)
      ] { case (listSuggestedInvestmentEntityNamesSize, isUserHasInvestmentEntities, isUserHasDeletedEntities) =>
        if (listSuggestedInvestmentEntityNamesSize > 0 && !isUserHasInvestmentEntities && !isUserHasDeletedEntities) {
          isOpenOnboardingFlowVar.set(true)
        }
      },
      child <-- isOpenOnboardingFlowVar.signal.map {
        if (_) {
          OnboardingEntryPoint(
            dashboardDataVar.now().investmentEntityDashboardWithSubscriptions.notLinkedSubscriptions.toList,
            dashboardDataVar.now().investmentEntityDashboardWithSubscriptions.dashboardData.entities.map(_.entityName),
            Observer[Unit] { _ =>
              isOpenOnboardingFlowVar.set(false)
              isLoadingVar.set(true)
            },
            createEntityEventBus.writer,
            isUserHasInvestmentEntitiesSignal
          )()
        } else {
          renderDashboard()
        }
      }
    )
  }

  private val query = AnduinQuery.InvestmentEntityDashboardWithSubscriptions

  private val graphqlOptions = GraphqlOptions(
    pollInterval = FiniteDuration(5, TimeUnit.MINUTES),
    requestTimeout = FiniteDuration(3, TimeUnit.MINUTES)
  )

  def apply(): HtmlElement = {
    QueryComponentL[Unit, Dashboard.ChildProps](
      query,
      Signal.fromValue(()),
      graphqlOptions,
      initialFetchStrategy = FetchStrategy.ForcedFetch
    ).apply { queryData =>
      div(
        tw.hPc100.wPc100,
        queryData.data --> Observer[Option[ChildProps]] { dataOpt =>
          dataOpt.fold(
            isLoadingVar.set(true)
          ) { data =>
            Var.set(
              dashboardDataVar -> data,
              isLoadingVar -> false
            )
          }
        },
        child <-- isLoadingVar.signal.map(isLoading =>
          if (isLoading) {
            BlockIndicatorL(
              title = Val(Some("Loading investment profiles...")),
              isFullHeight = true
            )()
          } else {
            renderContent(
              queryData.forceFetch
            )
          }
        ),
        HelpMenuPopOver()()
      )
    }
  }

}

private[investorprofile] object Dashboard {

  sealed trait ViewType derives CanEqual

  object ViewType {
    case object Card extends ViewType
    case object Table extends ViewType
  }

  final case class ChildProps(
    investmentEntityDashboardWithSubscriptions: InvestmentEntityDashboardWithSubscriptionsResponse
  )

  object ChildProps {
    given Codec.AsObject[ChildProps] = deriveCodecWithDefaults
  }

}
