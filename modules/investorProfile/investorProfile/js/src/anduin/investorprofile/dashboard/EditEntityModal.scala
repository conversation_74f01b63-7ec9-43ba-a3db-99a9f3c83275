// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.dashboard

import anduin.id.investmententity.InvestmentEntityId
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.field.laminar.FieldL
import design.anduin.components.field.laminar.FieldL.Helper
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL, ModalL}
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.style.tw.*
import org.scalajs.dom

import anduin.investorprofile.common.InvestmentEntityRenameWarningModalContent

import com.raquo.laminar.api.L.*

private[investorprofile] case class EditEntityModal(
  entity: EditEntityModal.EntityInfo,
  onClose: Observer[Unit],
  onUpdate: Observer[EditEntityModal.EntityEditInfo]
) {

  private val nameVar = Var(entity.entityName)
  private val customIdVar = Var(entity.customId.getOrElse(""))
  private val isOpenRenameWarningModalVar = Var(false)

  private def isValid(name: String, customId: String) = {
    name.trim.nonEmpty &&
    ((entity.entityName != name.trim) || (entity.customId.getOrElse("") != customId.trim))
  }

  def apply(): HtmlElement = {
    div(
      ModalL(
        isClosable = None,
        isOpened = Some(isOpenRenameWarningModalVar.signal),
        renderTitle = _ => "Rename investment profile",
        renderContent = _ => {
          InvestmentEntityRenameWarningModalContent(
            investmentEntityNameSignal = nameVar.signal,
            onCancel = Observer[Unit] { _ => isOpenRenameWarningModalVar.set(false) },
            onUpdate = Observer[Unit] { _ =>
              val trimmedCustomId = customIdVar.now().trim
              isOpenRenameWarningModalVar.set(false)
              onClose.onNext(())
              onUpdate.onNext(
                EditEntityModal.EntityEditInfo(
                  entity.investmentEntityId,
                  entityName = nameVar.now().trim,
                  customId = if (trimmedCustomId.isEmpty) None else Some(trimmedCustomId)
                )
              )
            }
          )()
        },
        size = ModalL.Size(width = ModalL.Width.Px720)
      )(),
      ModalBodyL(
        div(
          tw.mb16,
          FieldL(
            label = Option("Name"),
            requirement = FieldL.Requirement.Required
          )(
            TextBoxL(
              isAutoFocus = true,
              placeholder = "Profile name",
              value = nameVar.signal,
              onChange = nameVar.writer
            )()
          )
        ),
        FieldL(
          label = Option("Tracking ID"),
          helper = Helper.InTooltip(() =>
            div(maxWidth := "202px", "Tracking ID is a custom number used to monitor and reference a profile")
          ),
          requirement = FieldL.Requirement.Optional
        )(
          div(
            tw.wPc50,
            TextBoxL(
              placeholder = "Profile tracking ID",
              value = customIdVar.signal,
              onChange = customIdVar.writer
            )()
          )
        )
      ),
      ModalFooterWCancelL(
        cancel = onClose
      )(
        ButtonL(
          style = ButtonL.Style.Full(color = ButtonL.Color.Primary),
          isDisabled = nameVar.signal.combineWith(customIdVar.signal).mapN { case (name, customId) =>
            !isValid(name, customId)
          },
          onClick = Observer[dom.MouseEvent] { _ =>
            if (entity.numAssociatedSubscriptions > 0 && nameVar.now().trim != entity.entityName) {
              isOpenRenameWarningModalVar.set(true)
            } else {
              val trimmedCustomId = customIdVar.now().trim
              onClose.onNext(())
              onUpdate.onNext(
                EditEntityModal.EntityEditInfo(
                  entity.investmentEntityId,
                  entityName = nameVar.now().trim,
                  customId = if (trimmedCustomId.isEmpty) None else Some(trimmedCustomId)
                )
              )
            }
          }
        )("Save")
      )
    )
  }

}

private[investorprofile] object EditEntityModal {

  final case class EntityEditInfo(
    investmentEntityId: InvestmentEntityId,
    entityName: String,
    customId: Option[String]
  )

  final case class EntityInfo(
    investmentEntityId: InvestmentEntityId,
    entityName: String,
    customId: Option[String],
    numAssociatedSubscriptions: Int
  )

}
