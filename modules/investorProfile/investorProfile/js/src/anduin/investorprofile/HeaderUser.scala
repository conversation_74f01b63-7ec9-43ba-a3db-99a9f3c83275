// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile

import design.anduin.components.icon.Icon
import design.anduin.components.menu.react.{MenuDividerR, MenuItemR, MenuR}
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.PortalPosition
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.account.component.ProfileSettingModal
import anduin.user.{CurrentUserInitialAvatar, CurrentUserNameDisplay}
import com.anduin.stargazer.client.components.support.SupportingContactsModal
import com.anduin.stargazer.client.modules.routing.RoutingUtils.Router
import stargazer.component.routing.react.WithReactRouterR
import stargazer.model.routing.StaticAuthPage

private[investorprofile] final case class HeaderUser() derives CanEqual {
  def apply(): VdomElement = HeaderUser.component(this)
}

private[investorprofile] object HeaderUser {

  private type Props = HeaderUser

  private def renderTarget(toggle: Callback) = {
    <.div(
      ComponentUtils.testId(HeaderUser, "Target"),
      <.button(
        ^.onClick --> toggle,
        tw.cursorPointer,
        CurrentUserInitialAvatar()()
      )
    )
  }

  private def renderLogOut(router: Router) = {
    MenuItemR(
      color = MenuItemR.ColorDanger,
      url = router.urlFor(StaticAuthPage.Logout).value,
      icon = Some(Icon.Glyph.Logout)
    )("Log out")
  }

  private def renderProfileSettings() = {
    ProfileSettingModal(
      renderTarget = onClick =>
        MenuItemR(
          onClick = onClick,
          icon = Some(Icon.Glyph.Cog)
        )("Profile settings")
    )()
  }

  private def renderUserName(email: String, name: String) = {
    React.Fragment(
      <.p(tw.fontBold, name),
      <.p(tw.text11.textGray7.wPc100.truncate, email)
    )
  }

  private def renderUser: VdomElement = {
    <.div(
      tw.flex.itemsCenter.p8,
      <.div(CurrentUserInitialAvatar()()),
      <.div(
        tw.ml8.leading16.flexFill,
        CurrentUserNameDisplay(renderUserName)()
      )
    )
  }

  private def renderContent(router: Router) = {
    MenuR()(
      <.div(
        ^.width := "280px",
        ComponentUtils.testId(HeaderUser, "Content"),
        renderUser,
        renderProfileSettings(),
        MenuDividerR()(),
        SupportingContactsModal(
          renderTarget = openModal =>
            MenuItemR(
              color = MenuItemR.ColorGray,
              icon = Some(Icon.Glyph.Envelope),
              onClick = openModal
            )("Contact support")
        )(),
        MenuDividerR()(),
        renderLogOut(router)
      )
    )
  }

  private def render: VdomElement = {
    WithReactRouterR { router =>
      PopoverR(
        position = PortalPosition.BottomRight,
        renderTarget = (toggle, _) => renderTarget(toggle),
        renderContent = _ => renderContent(router)
      )()
    }
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .render_(render)
    .build

}
