// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.models

private[investorprofile] sealed trait ActivityType derives CanEqual {
  def name: String
}

private[investorprofile] object ActivityType {

  case object CreateInvestment extends ActivityType {
    override def name: String = "Create investment profile"
  }

  case object RenameInvestment extends ActivityType {
    override def name: String = "Rename investment profile"
  }

  case object EditCustomId extends ActivityType {
    override def name: String = "Edit custom ID"
  }

  case object GivePermission extends ActivityType {
    override def name: String = "Give permission x to y"
  }

  case object EditPermission extends ActivityType {
    override def name: String = "Edit permission of y to x"
  }

  case object RemoveAccess extends ActivityType {
    override def name: String = "Remove access from user y"
  }

  case object AddLink extends ActivityType {
    override def name: String = "Add link between investment profile and transaction"
  }

  case object RemoveLink extends ActivityType {
    override def name: String = "Remove link between investment profile and transaction"
  }

  case object CreateProfile extends ActivityType {
    override def name: String = "Create a new profile"
  }

  case object UpdateProfile extends ActivityType {
    override def name: String = "Update a profile"
  }

  case object RemoveProfile extends ActivityType {
    override def name: String = "Remove a profile"
  }

}
