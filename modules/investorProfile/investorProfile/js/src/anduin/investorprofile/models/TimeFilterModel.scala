// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.models

private[investorprofile] case class TimeFilterModel(
  text: String,
  numDays: Int
)

private[investorprofile] object TimeFilterModel {
  val defaultTimeFilterValue = TimeFilterModel("Last 30 days", 30)

  val TimeFilters = List[TimeFilterModel](
    defaultTimeFilterValue,
    TimeFilterModel("Last 60 days", 60),
    TimeFilterModel("Last 90 days", 90)
  )

}
