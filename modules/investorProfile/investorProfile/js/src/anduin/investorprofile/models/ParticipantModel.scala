// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.models

import io.circe.Codec

import anduin.model.common.user.UserId
import anduin.circe.generic.semiauto.deriveCodecWithDefaults

private[investorprofile] case class ParticipantModel(
  name: String,
  emailAddress: String,
  userId: UserId
)

private[investorprofile] object ParticipantModel {
  given Codec.AsObject[ParticipantModel] = deriveCodecWithDefaults
}
