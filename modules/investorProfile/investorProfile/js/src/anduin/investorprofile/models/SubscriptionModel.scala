// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.models

import java.time.Instant

import anduin.id.fundsub.FundSubLpId
import anduin.investmententity.subscription.LinkedEntityInfo
import anduin.protobuf.external.squants.MoneyMessage
import anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus

private[investorprofile] case class SubscriptionModel(
  lpId: FundSubLpId,
  fundName: String,
  entityName: String,
  userAccessible: Boolean,
  lpStatus: Option[LpStatus],
  participants: List[ParticipantModel],
  lastUpdated: Option[Instant],
  linkedEntities: Seq[LinkedEntityInfo],
  commitmentAmount: Option[MoneyMessage]
) derives CanEqual
