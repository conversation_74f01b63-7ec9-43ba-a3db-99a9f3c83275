// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.investorprofile.models

import anduin.investmententity.{GetAllAuditLogTrackingParams, StableInvestmentEntityInfo}
import anduin.service.GeneralServiceException
import zio.Task
import anduin.investorprofile.client.InvestmentEntityEndpointClient

private[investorprofile] sealed trait LogTrackingTrait derives CanEqual {
  def countNewUpdate: Int
}

private[investorprofile] case class InvestmentEntityLogTracking(maxLogIndex: Int, seenIndex: Int)
    extends LogTrackingTrait {
  override def countNewUpdate: Int = maxLogIndex - seenIndex
}

private[investorprofile] case class LpProfileLogTracking(
  maxLogIndex: Int,
  maxImportantLogIndex: Option[Int],
  seenIndex: Int
) extends LogTrackingTrait {
  override def countNewUpdate: Int = maxLogIndex - seenIndex
}

private[investorprofile] case class DocumentLogTracking(
  maxLogIndex: Int,
  seenIndex: Int
) extends LogTrackingTrait {
  override def countNewUpdate: Int = maxLogIndex - seenIndex
}

case class LogTrackingData(
  investmentEntityLogTracking: InvestmentEntityLogTracking,
  masterLpProfileLogTracking: LpProfileLogTracking,
  profileDocumentLogTracking: DocumentLogTracking
)

object LogTrackingData {

  def fetchLogTrackingData(
    stableInvestmentEntityInfo: StableInvestmentEntityInfo
  ): Task[Either[GeneralServiceException, LogTrackingData]] = {
    for {
      logTrackingEither <- InvestmentEntityEndpointClient
        .getAllAuditLogTracking(
          GetAllAuditLogTrackingParams(
            stableInvestmentEntityInfo.investmentEntityId,
            stableInvestmentEntityInfo.masterProfileId
          )
        )
    } yield {
      logTrackingEither.map(logTracking =>
        LogTrackingData(
          InvestmentEntityLogTracking(
            logTracking.investmentEntityLogTracking.maxIndex,
            logTracking.investmentEntityLogTracking.seenIndex
          ),
          LpProfileLogTracking(
            logTracking.masterLpProfileLogTracking.maxIndex,
            logTracking.masterLpProfileLogTracking.maxImportantEventIndex,
            logTracking.masterLpProfileLogTracking.seenIndex
          ),
          DocumentLogTracking(
            logTracking.documentLogTracking.fold(0)(_.maxIndex),
            logTracking.documentLogTracking.fold(0)(_.seenIndex)
          )
        )
      )
    }
  }

}
