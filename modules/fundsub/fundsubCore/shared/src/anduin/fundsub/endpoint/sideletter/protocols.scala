// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.endpoint.sideletter

import java.time.Instant

import io.circe.Codec

import anduin.circe.generic.semiauto.{deriveCodecWithDefaults, deriveEnumCodec}
import anduin.fundsub.endpoint.common.CreateRequestSignerData
import anduin.fundsub.endpoint.signature.DocusignESignatureOptionParams
import anduin.fundsub.model.FundSubSharedClientModel.ParticipantInfo
import anduin.id.fundsub.FundSubLpId
import anduin.id.signature.SignatureRequestId
import anduin.model.codec.MapCodecs.given
import anduin.model.codec.ProtoCodecs.given
import anduin.model.common.user.UserId
import anduin.model.id.FileId
import anduin.protobuf.signature.DocumentSignatureMessage
import scala.annotation.tailrec

import anduin.dashboard.data.SideLetterWorkflowStatus

final case class SideLetterWorkflow(
  lpId: FundSubLpId,
  status: SideLetterWorkflowStatus = SideLetterWorkflowStatus.NotStarted,
  markedAsAgreed: Boolean = false,
  markedAsAgreedBy: Option[UserId] = None,
  markedAsAgreedAt: Option[Instant] = None,
  markedAsCompleted: Boolean = false,
  markedAsCompletedBy: Option[UserId] = None,
  markedAsCompletedAt: Option[Instant] = None
) {
  lazy val userIds: Seq[UserId] = (markedAsAgreedBy.toSeq ++ markedAsCompletedBy.toSeq).distinct
}

object SideLetterWorkflow {
  given Codec[SideLetterWorkflow] = deriveCodecWithDefaults
}

enum SideLetterFileTag {
  case SIGNED_OFFLINE, FULLY_EXECUTED
}

object SideLetterFileTag {
  given Codec[SideLetterFileTag] = deriveEnumCodec
}

final case class SignedFile(
  signedFileId: FileId,
  originalFileId: FileId,
  signers: Seq[UserId],
  certificate: Option[FileId]
)

object SignedFile {
  given Codec[SignedFile] = deriveCodecWithDefaults
}

final case class PendingSignatureRequest(
  requestId: SignatureRequestId,
  originalFileIds: Seq[FileId]
)

object PendingSignatureRequest {
  given Codec[PendingSignatureRequest] = deriveCodecWithDefaults
}

final case class SignatureEvent(
  fileId: FileId,
  certificateOpt: Option[FileId],
  signers: Seq[UserId]
)

final case class SideLetterVersion(
  lpId: FundSubLpId,
  versionIndex: Int,
  fileIds: Seq[FileId],
  pendingSignatureRequests: Seq[PendingSignatureRequest],
  completedSignatureRequestIds: Seq[SignatureRequestId],
  fileTagMap: Map[FileId, SideLetterFileTag],
  mainFileIds: Seq[FileId],
  signedFiles: Seq[SignedFile],
  createdByOpt: Option[UserId],
  createdAt: Option[Instant],
  updatedAt: Option[Instant]
) {

  lazy val hasESignedFile: Boolean = fileIds.exists(signedFiles.map(_.signedFileId).contains)

  lazy val containSignedDoc: Boolean = hasESignedFile || fileIds.flatMap { fileId =>
    fileTagMap.get(fileId)
  }.nonEmpty

  lazy val userIds: Seq[UserId] = (createdByOpt.toSeq ++ signedFiles.flatMap(_.signers)).distinct

  lazy val signedFileToCleanFileMap: Map[FileId, FileId] = signedFiles.map { signedFile =>
    signedFile.signedFileId -> signedFile.originalFileId
  }.toMap

  def pendingSignatureRequestFileIds: Seq[FileId] = pendingSignatureRequests
    .flatMap(_.originalFileIds)
    .distinct
    .filter(fileIds.contains)

  def filterOutFilesWithSignedVersion: Seq[FileId] = fileIds.filterNot {
    signedFiles.map(_.originalFileId).contains
  }

  def allFilesAndCertificates: Seq[FileId] = fileIds ++ signedFiles
    .filter { signedFile =>
      fileIds.contains(signedFile.signedFileId) && fileIds.contains(signedFile.originalFileId)
    }
    .flatMap(_.certificate)

  /*
    Example 1:
    User request signature on doc.pdf to produce doc-signed.pdf.
    Later on, doc-signed.pdf is used in another signature request so we will eventually have doc-signed-signed.pdf.

    This function will find the original file (i.e. doc.pdf) from a signed file (i.e. doc-signed-signed.pdf).
   */
  @tailrec
  def getCleanFileIdOpt(signedFileId: FileId, tempOpt: Option[FileId] = None): Option[FileId] = {
    val previousFileOpt = tempOpt
      .orElse(signedFileToCleanFileMap.get(signedFileId))
      .fold[Option[FileId]](
        None
      ) { fileId =>
        signedFileToCleanFileMap
          .get(fileId)
          .orElse(Option(fileId))
      }
    if (previousFileOpt == tempOpt) {
      tempOpt
    } else {
      getCleanFileIdOpt(signedFileId, previousFileOpt)
    }
  }

  /*
    Refer to Example 1, doc-signed-signed.pdf have 2 related certificates
      - doc-signed-certificate.pdf
      - doc-signed-signed-certificate.pdf
   */
  @tailrec
  def getRelatedCertificates(signedFileId: FileId, result: Seq[FileId] = Seq.empty): Seq[FileId] = {
    val certificateOpt = signedFiles
      .find(_.signedFileId == signedFileId)
      .flatMap(_.certificate)
    val previousCleanFileIdOpt = signedFileToCleanFileMap.get(signedFileId)
    if (previousCleanFileIdOpt.isEmpty) {
      result
    } else {
      val previousCleanFileId = previousCleanFileIdOpt.get // avoid fold for tailrec
      getRelatedCertificates(
        previousCleanFileId,
        result ++ certificateOpt.toSeq
      )
    }
  }

  /*
  Refer to Example 1, doc-signed-signed.pdf have 2 related clean files
    - doc-signed.pdf
    - doc.pdf
   */
  @tailrec
  def getRelatedCleanFiles(signedFileId: FileId, result: Seq[FileId] = Seq.empty): Seq[FileId] = {
    val previousCleanFileIdOpt = signedFileToCleanFileMap.get(signedFileId)

    if (previousCleanFileIdOpt.isEmpty) {
      result
    } else {
      val previousCleanFileId = previousCleanFileIdOpt.get // avoid fold for tailrec
      getRelatedCleanFiles(previousCleanFileId, result :+ previousCleanFileId)
    }
  }

  @tailrec
  def getSignatureHistory(signedFileId: FileId, result: Seq[SignatureEvent]): Seq[SignatureEvent] = {
    val previousCleanFileIdOpt = signedFileToCleanFileMap.get(signedFileId)
    val signers = signedFiles
      .find(_.signedFileId == signedFileId)
      .map(_.signers)
      .getOrElse(Seq.empty)

    val certificateOpt = signedFiles
      .find(_.signedFileId == signedFileId)
      .flatMap(_.certificate)

    if (previousCleanFileIdOpt.isEmpty) {
      result :+ SignatureEvent(
        fileId = signedFileId,
        certificateOpt = certificateOpt,
        signers = signers
      )
    } else {
      val previousCleanFileId = previousCleanFileIdOpt.get // avoid fold for tailrec
      getSignatureHistory(
        previousCleanFileId,
        result :+ SignatureEvent(
          fileId = signedFileId,
          certificateOpt = certificateOpt,
          signers = signers
        )
      )
    }
  }

  def removeFile(fileId: FileId): SideLetterVersion = {
    // file being removed can be the signed file, if that so, we need to remove the original file as well
    // file can be signed multiple time so we need to remove all related files
    val allFilesToRemove = getRelatedCleanFiles(fileId) :+ fileId
    copy(
      fileIds = fileIds.filterNot(id => allFilesToRemove.contains(id)),
      fileTagMap = fileTagMap.filterNot { case (id, _) =>
        allFilesToRemove.contains(id)
      },
      mainFileIds = mainFileIds.filterNot { id =>
        allFilesToRemove.contains(id)
      },
      signedFiles = signedFiles.filterNot { signedFile =>
        allFilesToRemove.exists { id =>
          signedFile.signedFileId == id || signedFile.originalFileId == id
        }
      }
    )
  }

}

object SideLetterVersion {
  given Codec[SideLetterVersion] = deriveCodecWithDefaults
}

final case class SideLetterPackage(
  sideLetterWorkflow: SideLetterWorkflow,
  latestVersionOpt: Option[SideLetterVersion]
) {
  lazy val latestVersionIndex: Int = latestVersionOpt.map(_.versionIndex).getOrElse(-1)

  lazy val userIds: Seq[UserId] =
    (sideLetterWorkflow.userIds ++ latestVersionOpt.map(_.userIds).getOrElse(Seq.empty)).distinct

}

object SideLetterPackage {
  given Codec[SideLetterPackage] = deriveCodecWithDefaults
}

final case class SideLetterPackageWithParticipantsInfo(
  sideLetterPackage: SideLetterPackage,
  participants: Map[UserId, ParticipantInfo]
) {
  lazy val sideLetterWorkflow: SideLetterWorkflow = sideLetterPackage.sideLetterWorkflow
  lazy val latestVersionOpt: Option[SideLetterVersion] = sideLetterPackage.latestVersionOpt
  lazy val latestVersionIndex: Int = latestVersionOpt.map(_.versionIndex).getOrElse(0)

}

object SideLetterPackageWithParticipantsInfo {
  given Codec[SideLetterPackageWithParticipantsInfo] = deriveCodecWithDefaults
}

final case class StartSideLetterWorkflowParams(
  lpId: FundSubLpId,
  fileIds: Seq[FileId],
  mainFileIds: Seq[FileId],
  fileTagMap: Map[FileId, SideLetterFileTag],
  status: SideLetterWorkflowStatus
) {

  def updateFileIds(oldFileIdToNewFileIdMap: Map[FileId, FileId]): StartSideLetterWorkflowParams = {
    val newFileIds = fileIds.map(oldFileIdToNewFileIdMap)
    val newMainFileIds = mainFileIds.map(oldFileIdToNewFileIdMap)
    val newFileTagMap = fileTagMap.map { case (oldFileId, tag) =>
      oldFileIdToNewFileIdMap.get(oldFileId).fold(oldFileId -> tag) { newFileId =>
        newFileId -> tag
      }
    }
    copy(
      fileIds = newFileIds,
      mainFileIds = newMainFileIds,
      fileTagMap = newFileTagMap
    )
  }

}

object StartSideLetterWorkflowParams {
  given Codec[StartSideLetterWorkflowParams] = deriveCodecWithDefaults
}

final case class CreateNewVersionParams(
  lpId: FundSubLpId,
  fileIds: Seq[FileId],
  mainFileIds: Seq[FileId],
  fileTagMap: Map[FileId, SideLetterFileTag],
  status: SideLetterWorkflowStatus,
  isLpView: Boolean
) {

  def updateFileIds(oldFileIdToNewFileIdMap: Map[FileId, FileId]): CreateNewVersionParams = {
    val newFileIds = fileIds.map(oldFileIdToNewFileIdMap)
    val newMainFileIds = mainFileIds.map(oldFileIdToNewFileIdMap)
    val newFileTagMap = fileTagMap.map { case (oldFileId, tag) =>
      oldFileIdToNewFileIdMap.get(oldFileId).fold(oldFileId -> tag) { newFileId =>
        newFileId -> tag
      }
    }
    copy(
      fileIds = newFileIds,
      mainFileIds = newMainFileIds,
      fileTagMap = newFileTagMap
    )
  }

}

object CreateNewVersionParams {
  given Codec[CreateNewVersionParams] = deriveCodecWithDefaults
}

final case class MarkAsAgreedParams(
  lpId: FundSubLpId,
  versionIndex: Int,
  skipEmail: Boolean = false
)

object MarkAsAgreedParams {
  given Codec[MarkAsAgreedParams] = deriveCodecWithDefaults
}

final case class MarkAsCompleteParams(
  lpId: FundSubLpId,
  versionIndex: Int,
  skipEmail: Boolean = false
)

object MarkAsCompleteParams {
  given Codec[MarkAsCompleteParams] = deriveCodecWithDefaults
}

final case class AddFilesParams(
  lpId: FundSubLpId,
  versionIndex: Int,
  fileIds: Seq[FileId],
  mainFileIds: Seq[FileId],
  fileTagMap: Map[FileId, SideLetterFileTag],
  isLpView: Boolean
) {

  def updateFileIds(oldFileIdToNewFileIdMap: Map[FileId, FileId]): AddFilesParams = {
    val newFileIds = fileIds.map(oldFileIdToNewFileIdMap)
    val newMainFileIds = mainFileIds.map(oldFileIdToNewFileIdMap)
    val newFileTagMap = fileTagMap.map { case (oldFileId, tag) =>
      oldFileIdToNewFileIdMap.get(oldFileId).fold(oldFileId -> tag) { newFileId =>
        newFileId -> tag
      }
    }
    copy(
      fileIds = newFileIds,
      mainFileIds = newMainFileIds,
      fileTagMap = newFileTagMap
    )
  }

}

object AddFilesParams {
  given Codec[AddFilesParams] = deriveCodecWithDefaults
}

final case class RemoveFilesParams(
  lpId: FundSubLpId,
  versionIndex: Int,
  fileId: FileId
)

object RemoveFilesParams {
  given Codec[RemoveFilesParams] = deriveCodecWithDefaults
}

final case class AddFileTagsParams(
  lpId: FundSubLpId,
  versionIndex: Int,
  fileId: FileId,
  tag: SideLetterFileTag
)

object AddFileTagsParams {
  given Codec[AddFileTagsParams] = deriveCodecWithDefaults
}

final case class RemoveFileTagsParams(
  lpId: FundSubLpId,
  versionIndex: Int,
  fileId: FileId
)

object RemoveFileTagsParams {
  given Codec[RemoveFileTagsParams] = deriveCodecWithDefaults
}

final case class MarkFileAsMainAgreementParams(
  lpId: FundSubLpId,
  versionIndex: Int,
  fileId: FileId
)

object MarkFileAsMainAgreementParams {
  given Codec[MarkFileAsMainAgreementParams] = deriveCodecWithDefaults
}

final case class UnmarkFileAsMainAgreementParams(
  lpId: FundSubLpId,
  versionIndex: Int,
  fileId: FileId
)

object UnmarkFileAsMainAgreementParams {
  given Codec[UnmarkFileAsMainAgreementParams] = deriveCodecWithDefaults
}

final case class RequestSignatureParams(
  lpId: FundSubLpId,
  cleanFileIds: Seq[FileId],
  signers: Seq[CreateRequestSignerData],
  message: String,
  refDocs: Seq[FileId],
  docusignESignatureOptionParamsOpt: Option[DocusignESignatureOptionParams] = None
)

object RequestSignatureParams {
  given Codec.AsObject[RequestSignatureParams] = deriveCodecWithDefaults
}

final case class RequestSignatureResponse(
  requestId: SignatureRequestId,
  sideLetterPackageWithParticipantsInfo: SideLetterPackageWithParticipantsInfo
)

object RequestSignatureResponse {
  given Codec.AsObject[RequestSignatureResponse] = deriveCodecWithDefaults
}

final case class CancelSignatureRequestParams(
  lpId: FundSubLpId,
  signatureRequestId: SignatureRequestId
)

object CancelSignatureRequestParams {
  given Codec.AsObject[CancelSignatureRequestParams] = deriveCodecWithDefaults
}

final case class SignSignatureRequestParams(
  lpId: FundSubLpId,
  signatureRequestId: SignatureRequestId,
  signatures: Map[FileId, DocumentSignatureMessage]
)

object SignSignatureRequestParams {
  given Codec.AsObject[SignSignatureRequestParams] = deriveCodecWithDefaults
}

final case class UploadSignedDocumentParams(
  lpId: FundSubLpId,
  versionIndex: Int,
  fileIds: Seq[FileId]
)

object UploadSignedDocumentParams {
  given Codec.AsObject[UploadSignedDocumentParams] = deriveCodecWithDefaults
}
