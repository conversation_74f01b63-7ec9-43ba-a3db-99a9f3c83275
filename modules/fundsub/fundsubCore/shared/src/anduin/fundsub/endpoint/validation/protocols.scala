// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.endpoint.validation

import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.tapir.endpoint.AuthenticatedEndpointValidationParams

trait FundSubPermissionCheckParams extends AuthenticatedEndpointValidationParams {
  def fundSubId: FundSubId
}

trait FundSubLpPermissionCheckParams extends AuthenticatedEndpointValidationParams {
  def lpId: FundSubLpId
}

trait FundSubPortalPermissionCheckParams extends AuthenticatedEndpointValidationParams {
  def fundSubId: FundSubId
}
