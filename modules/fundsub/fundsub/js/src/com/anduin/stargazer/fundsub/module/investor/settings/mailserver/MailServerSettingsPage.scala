// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.mailserver

import design.anduin.components.button.Button
import design.anduin.components.progress.Skeleton
import design.anduin.components.progress.react.SkeletonR
import design.anduin.components.switcher.react.SwitcherR
import design.anduin.components.toast.Toast
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.email.CustomSmtpServerConfig
import anduin.id.fundsub.FundSubId
import anduin.switch.SwitchConfig
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

private[settings] final case class MailServerSettingsPage(fundSubId: FundSubId) {
  def apply(): VdomElement = MailServerSettingsPage.component(this)
}

private[settings] object MailServerSettingsPage {
  private type Props = MailServerSettingsPage

  private final case class State(
    configOpt: Option[SwitchConfig[CustomSmtpServerConfig]]
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State) = {
      <.div(
        ComponentUtils.testId(MailServerSettingsPage, "Container"),
        <.div(
          state.configOpt.fold[VdomElement] {
            SkeletonR(
              effect = Skeleton.Effect.Wave,
              height = "20px",
              width = "200px",
              shape = Skeleton.Shape.Rounded
            )()
          } { config =>
            if (config.enabled) {
              renderEnabled(props.fundSubId, config)
            } else {
              renderDisabled(
                props.fundSubId,
                config
              )
            }
          }
        )
      )
    }

    private def renderEnabled(fundSubId: FundSubId, config: SwitchConfig[CustomSmtpServerConfig]) = {
      <.div(
        <.div(
          tw.flex.itemsCenter,
          DisableSmtpModalButton(
            fundSubId,
            config,
            onSave = config => scope.modState(_.copy(configOpt = Some(config)))
          )(),
          <.div(
            tw.fontSemiBold,
            "Enable custom SMTP server"
          )
        ),
        <.div(
          tw.ml32.pl4.mt16,
          SmtpConfigRenderer(
            config.config.getOrElse(CustomSmtpServerConfig.Default)
          )()
        ),
        <.div(
          tw.flex.itemsCenter.gapX8.mt16,
          EnableSmtpModalButton(
            fundSubId = fundSubId,
            config = config.config.getOrElse(CustomSmtpServerConfig.Default),
            renderTarget = onClick =>
              Button(
                style = Button.Style.Full(),
                onClick = onClick
              )("Edit"),
            onSave = config => scope.modState(_.copy(configOpt = Some(config)))
          )(),
          SendTestEmailModalButton(
            fundSubId
          )()
        )
      )
    }

    private def renderDisabled(fundSubId: FundSubId, config: SwitchConfig[CustomSmtpServerConfig]) = {
      <.div(
        <.div(
          tw.flex.itemsCenter.spaceX4,
          EnableSmtpModalButton(
            fundSubId = fundSubId,
            config = config.config.getOrElse(CustomSmtpServerConfig.Default),
            renderTarget = onClick =>
              SwitcherR(
                isChecked = false,
                onChange = _ => onClick
              )(),
            onSave = config => scope.modState(_.copy(configOpt = Some(config)))
          )(),
          <.div(
            tw.fontSemiBold,
            "Enable custom SMTP server"
          )
        )
      )
    }

    def fetchConfig: Callback = for {
      props <- scope.props
      _ <- ZIOUtils.toReactCallback(
        FundSubEndpointClient
          .getSmtpConfig(props.fundSubId)
          .map(
            _.fold(
              ex => Toast.errorCallback("Failed to load mail server config: " + ex.getMessage),
              config =>
                scope.modState(
                  _.copy(configOpt = Some(config))
                )
            )
          )
      )
    } yield ()

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State(None))
    .renderBackend[Backend]
    .componentDidMount(_.backend.fetchConfig)
    .build

}
