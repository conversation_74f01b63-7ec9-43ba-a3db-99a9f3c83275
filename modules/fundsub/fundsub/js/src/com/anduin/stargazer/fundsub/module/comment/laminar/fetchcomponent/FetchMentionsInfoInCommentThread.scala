// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent

import com.raquo.laminar.api.L.*
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.id.issuetracker.IssueId
import anduin.stargazer.service.formcomment.FormCommentCommons.{GetMentionsInfoInCommentThreadParam, MentionInfo}
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

private[laminar] case class FetchMentionsInfoInCommentThread(
  fundSubId: FundSubId,
  issueIdSignal: Signal[IssueId],
  commentIdSignal: Signal[String],
  lpIdSignal: Signal[FundSubLpId],
  hasMentionSignal: Signal[Boolean],
  renderChildren: FetchMentionsInfoInCommentThread.RenderChildren => HtmlElement
) {

  private val fetchMentionsInfoInCommentThreadEventBus = new EventBus[Unit]

  private val resultVar = Var(
    FetchMentionsInfoInCommentThread.Data(
      isFetching = true,
      mentionsInfo = List.empty
    )
  )

  private def fetchMentionsInfoInCommentThread(
    issueId: IssueId,
    lpId: FundSubLpId,
    commentId: String,
    hasMention: Boolean
  ) = {
    AirStreamUtils.taskToStream {
      if (!hasMention) {
        ZIO.attempt {
          FetchMentionsInfoInCommentThread.Data(
            isFetching = false,
            mentionsInfo = List.empty
          )
        }
      } else {
        FundSubEndpointClient
          .getMentionsInfoInComment(
            GetMentionsInfoInCommentThreadParam(
              fundSubLpId = lpId,
              targetedIssueId = issueId,
              commentId = commentId
            )
          )
          .map {
            _.fold(
              _ => {
                scribe.info(s"Failed to fetch mentioned users info in comment thread ${issueId} of LP ${lpId}")
                FetchMentionsInfoInCommentThread.Data(
                  isFetching = false,
                  mentionsInfo = List.empty
                )
              },
              response => {
                FetchMentionsInfoInCommentThread.Data(
                  isFetching = false,
                  mentionsInfo = response.mentionsInfo
                )
              }
            )
          }
      }
    }
  }

  def apply(): HtmlElement = {
    renderChildren(
      FetchMentionsInfoInCommentThread.RenderChildren(
        dataSignal = resultVar.signal
      )
    ).amend(
      fetchMentionsInfoInCommentThreadEventBus.events
        .withCurrentValueOf(issueIdSignal, lpIdSignal, commentIdSignal, hasMentionSignal)
        .flatMapSwitch(fetchMentionsInfoInCommentThread) --> resultVar.writer,
      hasMentionSignal.distinct.map(_ => fetchMentionsInfoInCommentThreadEventBus.emit(())) --> Observer.empty
    )
  }

}

private[laminar] object FetchMentionsInfoInCommentThread {

  case class Data(
    isFetching: Boolean,
    mentionsInfo: Seq[MentionInfo]
  )

  case class RenderChildren(dataSignal: Signal[Data])
}
