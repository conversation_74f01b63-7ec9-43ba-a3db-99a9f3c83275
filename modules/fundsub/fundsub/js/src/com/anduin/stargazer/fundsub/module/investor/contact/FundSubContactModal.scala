// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.contact

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.modal.Modal
import design.anduin.components.portal.PortalUtils
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.constants.Terms
import anduin.fundsub.endpoint.contact.{FundSubContactInfo, GetContactAndGroupParams}
import anduin.fundsub.endpoint.reviewpackage.FundSubReviewPackageData
import anduin.id.contact.ContactId
import anduin.id.entity.EntityId
import anduin.id.fundsub.{FundSubContactGroupId, FundSubId}
import anduin.protobuf.fundsub.LpFlowType
import anduin.scalajs.pluralize.Pluralize
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.investor.contact.modal.{ExplanationModal, OnboardingModal}
import com.anduin.stargazer.fundsub.module.investor.dashboard.lp.closes.FundSubCloseDataProvider.FundSubCloseDataClient
import stargazer.model.routing.Page

final case class FundSubContactModal(
  fundName: String,
  fundSubId: FundSubId,
  entityId: EntityId,
  enabledCustomId: Boolean,
  router: RouterCtl[Page],
  closeData: Option[FundSubCloseDataClient],
  reviewPackageData: Option[FundSubReviewPackageData],
  lpFlowType: LpFlowType,
  onClose: Callback
) {

  def apply(): VdomElement = FundSubContactModal.component(this)
}

object FundSubContactModal {

  private type Props = FundSubContactModal

  private final case class State(
    viewedOnboarding: Option[Boolean] = None,
    contactInfo: Option[FundSubContactInfo] = None,
    // Selected contacts that doesn't belong to any group
    selectedContacts: Seq[ContactId] = Seq.empty,
    // Selected contacts that belong to some group
    selectedGroupContacts: Seq[(FundSubContactGroupId, ContactId)] = Seq.empty,
    // Selected group doesn't contain any contact
    selectedEmptyGroups: Seq[FundSubContactGroupId] = Seq.empty,
    searchValue: String = ""
  )

  private class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      <.div(
        tw.flex.flexCol.hPc100,
        renderHeader(props),
        <.div(
          tw.overflowAuto.hPc100,
          state.contactInfo.fold(
            BlockIndicatorR()()
          ) { contactInfo =>
            <.div(
              ComponentUtils.testId(FundSubContactModal, "Container"),
              tw.py16.px20,
              state.viewedOnboarding.map(viewedOnboarding =>
                Modal(
                  renderContent = close => OnboardingModal(close >> scope.modState(_.copy(viewedOnboarding = Some(true))))(),
                  isOpened = Some(!viewedOnboarding),
                  isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false))
                )()
              ),
              FundSubContactHeader(
                fundSubId = props.fundSubId,
                enabledCustomId = props.enabledCustomId,
                entityId = props.entityId,
                contactInfo = contactInfo,
                selectedContacts = state.selectedContacts,
                selectedGroupContacts = state.selectedGroupContacts,
                selectedEmptyGroups = state.selectedEmptyGroups,
                onChangeSearchValue = searchValue => scope.modState(_.copy(searchValue = searchValue)),
                reFetch = fetchData,
                router = props.router,
                closeData = props.closeData,
                reviewPackageData = props.reviewPackageData,
                lpFlowType = props.lpFlowType
              )(),
              <.div(
                tw.mt16,
                FundSubContactTable(
                  fundSubId = props.fundSubId,
                  enabledCustomId = props.enabledCustomId,
                  contactInfo = contactInfo,
                  selectedContacts = state.selectedContacts,
                  selectedGroupContacts = state.selectedGroupContacts,
                  selectedEmptyGroups = state.selectedEmptyGroups,
                  onToggleContact = onToggleContact,
                  onToggleGroup = onToggleGroup,
                  onToggleEmptyGroup = onToggleEmptyGroup,
                  searchValue = state.searchValue,
                  reFetch = fetchData
                )()
              )
            )
          }
        )
      )
    }

    private def renderHeader(props: Props) = {
      <.div(
        tw.hPx64.flex.itemsCenter.borderBottom.borderGray3.px16,
        <.div(
          tw.mr12,
          Button(
            style = Button.Style.Minimal(icon = Some(Icon.Glyph.Cross)),
            onClick = props.onClose
          )()
        ),
        <.div(tw.bgGray3.ml4.hPx32.wPx1),
        <.div(
          tw.ml16.flexFill,
          <.div(
            tw.fontSemiBold.text15,
            s"Contacts in ${props.fundName}"
          ),
          <.div(
            tw.inlineBlock,
            renderLearnMoreButton
          )
        )
      )
    }

    private def renderLearnMoreButton = {
      Modal(
        renderTarget = open =>
          TooltipR(
            renderTarget = Button(
              style = Button.Style.Text(),
              onClick = open
            )(<.span(tw.textGray7, "Learn more")),
            renderContent = _(s"Learn more about contacts and ${Pluralize(Terms.InvesteeEntityLabelLowerCase, 2)}")
          )(),
        renderContent = _ => ExplanationModal(),
        title = s"Learn more about contacts and ${Pluralize(Terms.InvesteeEntityLabelLowerCase, 2)}",
        size = Modal.Size(width = Modal.Width.Px720)
      )()
    }

    private def onToggleContact(contactIds: Seq[ContactId], selected: Boolean): Callback = {
      if (selected) {
        scope.modState(state => state.copy(selectedContacts = state.selectedContacts ++ contactIds))
      } else {
        scope.modState(state => state.copy(selectedContacts = state.selectedContacts.filterNot(contactIds.contains)))
      }
    }

    private def onToggleGroup(contacts: Seq[(FundSubContactGroupId, ContactId)], selected: Boolean): Callback = {
      if (selected) {
        scope.modState(state => state.copy(selectedGroupContacts = state.selectedGroupContacts ++ contacts))
      } else {
        scope.modState(state =>
          state.copy(selectedGroupContacts = state.selectedGroupContacts.filterNot(contacts.contains))
        )
      }
    }

    private def onToggleEmptyGroup(groupIds: Seq[FundSubContactGroupId], selected: Boolean): Callback = {
      if (selected) {
        scope.modState(state => state.copy(selectedEmptyGroups = state.selectedEmptyGroups ++ groupIds))
      } else {
        scope.modState(state => state.copy(selectedEmptyGroups = state.selectedEmptyGroups.filterNot(groupIds.contains)))
      }
    }

    // Refine all selected data after contactInfo is updated
    private def onContactInfoUpdated(contactInfo: FundSubContactInfo, viewedOnboarding: Boolean) = {
      val contactsWithoutGroup = contactInfo.contactWithoutGroup.map(_.contactId)
      val contactsWithGroup = contactInfo.groups.flatMap { case (group, contacts) =>
        contacts.map(group.id -> _.contactId)
      }.toSeq
      val emptyGroups = contactInfo.groups.filter(_._2.isEmpty).map(_._1.id).toSeq
      for {
        state <- scope.state
        _ <- scope.modState(
          _.copy(
            viewedOnboarding = Some(viewedOnboarding),
            contactInfo = Some(contactInfo),
            selectedContacts = state.selectedContacts.intersect(contactsWithoutGroup),
            selectedGroupContacts = state.selectedGroupContacts.intersect(contactsWithGroup),
            selectedEmptyGroups = state.selectedEmptyGroups.intersect(emptyGroups)
          )
        )
      } yield ()
    }

    def fetchData: Callback = {
      for {
        props <- scope.props
        _ <- ZIOUtils.toReactCallback(
          FundSubEndpointClient
            .getContactAndGroup(GetContactAndGroupParams(props.fundSubId))
            .map(
              _.fold(
                _ => Toast.errorCallback("Unable to get contact, try again"),
                res => onContactInfoUpdated(res.contactInfo, res.viewedOnboarding)
              )
            )
        )
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .componentDidMount(_.backend.fetchData)
    .build

}
