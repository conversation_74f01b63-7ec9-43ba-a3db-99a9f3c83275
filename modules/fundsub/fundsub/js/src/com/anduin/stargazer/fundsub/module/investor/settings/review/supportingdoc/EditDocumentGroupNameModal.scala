// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.review.supportingdoc

import design.anduin.components.button.Button
import design.anduin.components.field.Field
import design.anduin.components.modal.{ModalBody, ModalFooterWCancel}
import design.anduin.components.textbox.TextBox
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import com.anduin.stargazer.fundsub.module.investor.settings.review.supportingdoc.SupportingDocGroupReviewSettingModal.UnassignedSupportingDocGroupName

final case class EditDocumentGroupNameModal(
  docGroups: List[String],
  initialGroupNameOpt: Option[String],
  onClose: Callback,
  onSave: String => Callback
) {
  def apply(): VdomElement = EditDocumentGroupNameModal.component(this)
}

object EditDocumentGroupNameModal {

  private type Props = EditDocumentGroupNameModal

  private final case class State(
    currentGroupName: String
  )

  private class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      val isDuplicated = state.currentGroupName.trim == UnassignedSupportingDocGroupName || props.docGroups
        .filterNot { groupName =>
          props.initialGroupNameOpt.exists(_.trim == groupName)
        }
        .exists(_.trim == state.currentGroupName.trim)
      val isCreatingNewGroup = props.initialGroupNameOpt.isEmpty
      React.Fragment(
        ModalBody()(
          props.initialGroupNameOpt.fold {
            <.div(
              tw.mb24,
              "Enter a name for the new document group"
            )
          } { initialGroupName =>
            <.div(
              tw.mb24,
              "Enter a new name for ",
              <.span(
                tw.fontSemiBold,
                initialGroupName
              )
            )
          },
          Field(
            validation = if (isDuplicated) {
              Field.Validation.Invalid("This name already exists. Please try a different one.")
            } else {
              Field.Validation.None
            }
          )(
            TextBox(
              testId = "Name",
              value = state.currentGroupName,
              isAutoFocus = true,
              placeholder = "Enter document group name",
              onChange = value => scope.modState(_.copy(currentGroupName = value)),
              status = if (isDuplicated) {
                TextBox.Status.Invalid
              } else {
                TextBox.Status.None
              }
            )()
          )
        ),
        ModalFooterWCancel(props.onClose)(
          Button(
            testId = "Confirm",
            style = Button.Style.Full(color = Button.Color.Primary),
            onClick = props.onSave(state.currentGroupName.trim),
            isDisabled = props.initialGroupNameOpt.exists(_.trim == state.currentGroupName.trim)
              || state.currentGroupName.trim.isEmpty || isDuplicated
          )(
            if (isCreatingNewGroup) {
              "Create document group"
            } else {
              "Save"
            }
          )
        )
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialStateFromProps(props => State(currentGroupName = props.initialGroupNameOpt.getOrElse("")))
    .renderBackend[Backend]
    .build

}
