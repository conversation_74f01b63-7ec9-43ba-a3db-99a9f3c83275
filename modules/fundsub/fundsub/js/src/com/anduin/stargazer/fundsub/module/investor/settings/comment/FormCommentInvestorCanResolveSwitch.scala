// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.comment

import design.anduin.components.toast.Toast
import design.anduin.table.TaskUtils
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.endpoint.lp.UpdateFormCommentInvestorCanResolveSwitchParams
import anduin.id.fundsub.FundSubId
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.investor.settings.FundSubChildSettingSwitch

private[comment] final case class FormCommentInvestorCanResolveSwitch(
  fundId: FundSubId,
  investorCanResolve: Boolean
) {
  def apply(): VdomElement = FormCommentInvestorCanResolveSwitch.component(this)
}

private[comment] object FormCommentInvestorCanResolveSwitch {
  private type Props = FormCommentInvestorCanResolveSwitch

  private final case class State(
    investorCanResolveOptimisticUpdate: Option[Boolean] = None
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      val isChecked = state.investorCanResolveOptimisticUpdate.getOrElse(props.investorCanResolve)
      val isWaitingAcknowledge =
        state.investorCanResolveOptimisticUpdate.nonEmpty && !state.investorCanResolveOptimisticUpdate
          .contains(props.investorCanResolve)
      FundSubChildSettingSwitch(
        title = "Allow investors to mark comment threads as resolved",
        enabled = isChecked,
        isDisabled = isWaitingAcknowledge,
        onChange = onUpdate,
        description = () =>
          <.div(
            "When turned on, investors can mark any comment threads on their documents as resolved. When turned off, they can still reopen resolved threads."
          )
      )()
    }

    private def onUpdate(investorCanResolve: Boolean) = {
      for {
        props <- scope.props
        _ <- scope.modState(
          _.copy(
            investorCanResolveOptimisticUpdate = Option(investorCanResolve)
          ),
          TaskUtils.toReactCallback(
            FundSubEndpointClient
              .updateFormCommentInvestorCanResolveSwitch(
                UpdateFormCommentInvestorCanResolveSwitchParams(
                  props.fundId,
                  investorCanResolve = investorCanResolve
                )
              )
              .map(
                _.fold(
                  _ =>
                    scope.modState(
                      _.copy(investorCanResolveOptimisticUpdate = None),
                      Toast.errorCallback("Failed to update settings")
                    ),
                  _ => Callback.empty
                )
              )
          )
        )
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
