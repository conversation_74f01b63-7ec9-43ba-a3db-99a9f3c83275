// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration

import com.raquo.laminar.api.L.*
import io.circe.Codec

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.fundsub.endpoint.graphql.FundAdminsInfoForDashboardSchema
import anduin.graphql.component.laminar.QueryComponentL
import anduin.graphql.component.{FetchStrategy, GraphqlOptions}
import anduin.id.fundsub.FundSubId
import anduin.rohan.operation.AnduinQuery

private[laminar] case class FetchAdminsInfoDashboardSchemaL(
  fundSubId: FundSubId,
  renderChildren: Signal[Option[FundAdminsInfoForDashboardSchema]] => HtmlElement
) {

  def apply(): HtmlElement = {
    QueryComponentL[FetchAdminsInfoDashboardSchemaL.Variables, FetchAdminsInfoDashboardSchemaL.ChildProps](
      query = AnduinQuery.FundAdminsInfoForDashboardSchema,
      variableSignal = Val(
        FetchAdminsInfoDashboardSchemaL.Variables(fundSubId = fundSubId)
      ),
      options = GraphqlOptions(pollInterval = FiniteDuration(30, TimeUnit.SECONDS)),
      initialFetchStrategy = FetchStrategy.ForcedFetch
    ).apply { queryData =>
      val adminsSignal = queryData.data.map(_.flatMap(_.fundAdminsInfoForDashboardSchema))
      renderChildren(adminsSignal)
    }
  }

}

private[laminar] object FetchAdminsInfoDashboardSchemaL {

  final case class Variables(fundSubId: FundSubId) derives CanEqual

  object Variables {
    given Codec.AsObject[Variables] = deriveCodecWithDefaults
  }

  final case class ChildProps(
    fundAdminsInfoForDashboardSchema: Option[FundAdminsInfoForDashboardSchema]
  ) derives CanEqual

  object ChildProps {
    given Codec.AsObject[ChildProps] = deriveCodecWithDefaults
  }

}
