// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.homepage

import design.anduin.components.button.Button
import design.anduin.components.divider.react.DividerR
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.menu.react.{MenuDividerR, MenuItemR, MenuR}
import design.anduin.components.modal.Modal
import design.anduin.components.nonidealstate.react.NonIdealStateR
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.{PortalPosition, PortalWrapper}
import design.anduin.components.progress.react.RadialIndicatorR
import design.anduin.components.text.react.TruncateR
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.actionlogger.ActionEventLoggerJs
import anduin.ad.integplatform.IntegPlatformAdBanner
import anduin.ad.workflow.WorkflowsAdBanner
import anduin.appnavigation.routing.CrossAppRouter
import anduin.fundsub.model.FundSubSharedClientModel.ParticipantInfo
import anduin.id.offering.OfferingId
import anduin.investmententity.InvestmentEntityInfo
import anduin.scalajs.pluralize.Pluralize
import anduin.utils.{CurrencyJsUtils, DateTimeUtils}
import anduin.whitelabel.dashboard.DashboardWhitelabelValue
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils
import com.anduin.stargazer.fundsub.module.investor.AdminDashboardTab
import com.anduin.stargazer.fundsub.module.investor.dashboard.lp.table.StopClickEventWrapper
import com.anduin.stargazer.fundsub.module.lp.dashboard.inactivesubscription.ActivateSubscriptionModal
import stargazer.component.routing.react.WithReactRouterR
import stargazer.model.routing.DynamicAuthPage.FundSubAdminPage
import stargazer.model.routing.DynamicAuthPage.LpProfile.InvestmentEntityProfilesPage
import stargazer.model.routing.{Page, StaticAuthPage}

private[homepage] final case class FundSubHomePageFundList(
  currentTab: FundSubHomePage.Tab,
  fundSubsJoinedAsAdmin: List[FundSubJoinedAsAdmin],
  fundSubsJoinedAsLp: List[FundSubJoinedAsLp],
  investmentEntityList: List[InvestmentEntityInfo],
  environmentWhitelabelValues: Map[DashboardWhitelabelValue, String],
  sortAndFilterOptions: FundSubHomePage.FundAndInvestmentSortAndFilterOptions,
  onChangeSortAndFilterOptions: FundSubHomePage.OnChangeSortAndFilterOptionsParams => Callback, // (optionsUpdateFn, onDone) => Callback
  hideLpProfile: Boolean
) {
  def apply(): VdomElement = FundSubHomePageFundList.component(this)
}

private[homepage] object FundSubHomePageFundList {

  private type Props = FundSubHomePageFundList

  private val Epsilon = 1e-3

  private val TitleStyle = TagMod(
    tw.text13.textGray7.leading20.mb2
  )

  private val ContentStyle = TagMod(
    tw.text13.textGray8.leading20
  )

  private def renderNeedCurrencySetup(
    router: RouterCtl[Page],
    fundSubJoinedAsAdmin: FundSubJoinedAsAdmin
  ): VdomElement = {
    <.div(
      tw.flex.itemsCenter,
      <.div("--"),
      <.div(
        tw.ml4,
        ^.onClick ==> (_.stopPropagationCB),
        PopoverR(
          position = PortalPosition.BottomLeft,
          renderTarget = (open, isOpen) =>
            Button(
              style = Button.Style.Minimal(
                icon = Some(Icon.Glyph.Question),
                isSelected = isOpen
              ),
              onClick = open
            )(),
          renderContent = _ =>
            <.div(
              tw.px12.py8,
              ^.width := 300.px,
              <.div(tw.heading4, "No data"),
              <.div(
                tw.mt8,
                "This fund has more than one currency. " +
                  "Please select the fund's main currency and set up conversion rate in the ",
                Button(
                  style = Button.Style.Text(),
                  tpe = Button.Tpe.Link(
                    href = router
                      .urlFor(
                        FundSubAdminPage(
                          entityId = fundSubJoinedAsAdmin.investorEntity,
                          fundSubId = fundSubJoinedAsAdmin.fundSubId,
                          params = Map(FundSubAdminPage.TabKey -> AdminDashboardTab.Reporting.value)
                        )
                      )
                      .value
                  )
                )("Reporting tab"),
                " to display this."
              )
            )
        )()
      )
    )
  }

  private def renderEmptyContent = {
    <.div(tw.text13.textGray5.leading20, "-")
  }

  private def renderEmptyResult: VdomElement = {
    <.div(
      ^.paddingTop := "144px",
      NonIdealStateR(
        icon = <.img(
          ^.width := "50px",
          ^.height := "48px",
          ^.src := "/web/gondor/images/fundsub/no_match_funds.svg",
          ^.alt := "No results found"
        ),
        title = <.p(tw.heading3.textGray8, "No results found"),
        description = <.p(tw.textGray7.textBody, "Try again with a different term")
      )()
    )
  }

  private def getFilteredFundSubsJoinedAsAdmin(props: Props) = {
    val filteredList = props.fundSubsJoinedAsAdmin
      .filter(fund =>
        fund.fundName.toLowerCase.contains(
          props.sortAndFilterOptions.fundSortAndFilterOption.searchKey.toLowerCase
        ) && (!fund.isFundClosed || props.sortAndFilterOptions.fundSortAndFilterOption.showFundClosed)
      )
    props.sortAndFilterOptions.fundSortAndFilterOption.sortBy match {
      case FundSubHomePage.SortOption.Alphabetically =>
        filteredList.sortBy(_.fundName.toLowerCase)
      case FundSubHomePage.SortOption.MostRecentActivity =>
        filteredList.sortBy(_.lastActivityAt).reverse
      case FundSubHomePage.SortOption.CreatedDate =>
        filteredList.sortBy(_.fundCreatedAt).reverse
    }
  }

  private def getFilteredFundSubsJoinedAsLp(props: Props) = {
    val filteredList = props.fundSubsJoinedAsLp
      .filter { fund =>
        (fund.fundName.toLowerCase.contains(
          props.sortAndFilterOptions.investmentSortAndFilterOption.searchKey.toLowerCase
        ) ||
          fund.lpFirmName.toLowerCase.contains(
            props.sortAndFilterOptions.investmentSortAndFilterOption.searchKey.toLowerCase
          )) && (!fund.isFundClosed || props.sortAndFilterOptions.investmentSortAndFilterOption.showFundClosed)
      }
    props.sortAndFilterOptions.investmentSortAndFilterOption.sortBy match {
      case FundSubHomePage.SortOption.Alphabetically =>
        filteredList.sortBy(lp => (lp.fundName.toLowerCase, lp.lpFirmName.toLowerCase))
      case FundSubHomePage.SortOption.MostRecentActivity =>
        filteredList.sortBy(_.lastActivityAt).reverse
      case FundSubHomePage.SortOption.CreatedDate =>
        filteredList.sortBy(_.fundCreatedAt).reverse
    }
  }

  private def render(props: Props): VdomElement = {
    <.div(
      renderTitle(props),
      renderFundList(
        props,
        getFilteredFundSubsJoinedAsLp(props),
        getFilteredFundSubsJoinedAsAdmin(props)
      )
    )
  }

  private def renderTitle(props: Props) = {
    <.div(
      tw.leading32.text23.fontSemiBold.mt48,
      ^.color := DashboardWhitelabelValue.TextOnBannerColor.extractThis(props.environmentWhitelabelValues),
      props.currentTab.title
    )
  }

  private def renderSearchAndSort(props: Props, currentTab: FundSubHomePage.Tab, searchResultCnt: Int) = {
    <.div(
      tw.flex.justifyBetween,
      currentTab match {
        case FundSubHomePage.Tab.Funds =>
          TagMod(
            renderSearchBox(
              key = "fund",
              placeholder = "Search fund names",
              searchKey = props.sortAndFilterOptions.fundSortAndFilterOption.searchKey,
              onSearch = key =>
                props.onChangeSortAndFilterOptions(
                  FundSubHomePage.OnChangeSortAndFilterOptionsParams(
                    currentOptions =>
                      currentOptions.copy(fundSortAndFilterOption =
                        currentOptions.fundSortAndFilterOption.copy(searchKey = key)
                      ),
                    Callback.empty
                  )
                ),
              searchResultCnt = searchResultCnt
            ),
            TagMod.when(searchResultCnt > 0 || props.sortAndFilterOptions.fundSortAndFilterOption.searchKey.isEmpty) {
              renderSortAndFilterButton(
                option = props.sortAndFilterOptions.fundSortAndFilterOption,
                onChange = (option, onDone) =>
                  props.onChangeSortAndFilterOptions(
                    FundSubHomePage.OnChangeSortAndFilterOptionsParams(
                      currentOptions => currentOptions.copy(fundSortAndFilterOption = option),
                      onDone
                    )
                  )
              )
            }
          )
        case FundSubHomePage.Tab.Investments =>
          TagMod(
            renderSearchBox(
              key = "investment",
              placeholder = "Search fund and investment entity",
              searchKey = props.sortAndFilterOptions.investmentSortAndFilterOption.searchKey,
              onSearch = key =>
                props.onChangeSortAndFilterOptions(
                  FundSubHomePage.OnChangeSortAndFilterOptionsParams(
                    currentOptions =>
                      currentOptions.copy(investmentSortAndFilterOption =
                        currentOptions.investmentSortAndFilterOption.copy(searchKey = key)
                      ),
                    Callback.empty
                  )
                ),
              searchResultCnt = searchResultCnt
            ),
            TagMod.when(
              searchResultCnt > 0 || props.sortAndFilterOptions.investmentSortAndFilterOption.searchKey.isEmpty
            ) {
              renderSortAndFilterButton(
                option = props.sortAndFilterOptions.investmentSortAndFilterOption,
                onChange = (option, onDone) =>
                  props.onChangeSortAndFilterOptions(
                    FundSubHomePage.OnChangeSortAndFilterOptionsParams(
                      currentOptions => currentOptions.copy(investmentSortAndFilterOption = option),
                      onDone
                    )
                  )
              )
            }
          )
      }
    )
  }

  private def renderSearchBox(
    key: String,
    placeholder: String,
    searchKey: String,
    onSearch: String => Callback,
    searchResultCnt: Int
  ): VdomElement = {
    <.div(
      ^.key := key,
      tw.flexFill,
      <.div(
        tw.wPc100,
        tw.sm(tw.hidden),
        FundSubHomePageSearchBox(
          placeholder = placeholder,
          onSearch = searchedText =>
            ActionEventLoggerJs.logPageViewCb(subPage = Some("All Funds Dashboard - Search Bar"))
              >> onSearch(searchedText),
          initialValue = searchKey
        )()
      ),
      <.div(
        tw.hidden,
        tw.sm(tw.block),
        ^.width := "280px",
        FundSubHomePageSearchBox(
          placeholder = placeholder,
          onSearch = searchedText =>
            ActionEventLoggerJs.logPageViewCb(subPage = Some("All Funds Dashboard - Search Bar"))
              >> onSearch(searchedText),
          initialValue = searchKey
        )()
      ),
      TagMod.when(searchKey.nonEmpty && searchResultCnt > 0) {
        <.div(
          tw.leading20.text13.textGray7.mt8,
          s"${Pluralize(
              "result",
              searchResultCnt,
              true
            )} found"
        )
      }
    )
  }

  private def renderSortAndFilterButton(
    option: FundSubHomePage.SortAndFilterOption,
    onChange: (FundSubHomePage.SortAndFilterOption, Callback) => Callback // (sortAndFilterOption, onDone) => Callback
  ) = {
    PopoverR(
      position = PortalPosition.BottomLeft,
      renderTarget = (open, isOpened) =>
        <.div(
          <.div(
            tw.hidden,
            tw.sm(tw.block),
            Button(
              style = Button.Style.Ghost(icon = Some(Icon.Glyph.SortDes), isSelected = isOpened),
              onClick = open
            )(
              <.div(
                tw.flex.itemsCenter,
                <.div(option.sortBy.optionName),
                <.div(tw.ml8, IconR(name = Icon.Glyph.CaretDown, size = Icon.Size.Px16)())
              )
            )
          ),
          <.div(
            tw.ml16,
            tw.sm(tw.hidden),
            Button(
              style = Button.Style.Ghost(icon = Some(Icon.Glyph.SortDes), isSelected = isOpened),
              onClick = open
            )()
          )
        ),
      renderContent = close =>
        MenuR()(
          FundSubHomePage.SortOptions.toVdomArray(
            using { sortOption =>
              <.div(
                ^.key := sortOption.optionName,
                MenuItemR(
                  isSelected = option.sortBy == sortOption,
                  onClick = onChange(option.copy(sortBy = sortOption), close),
                  icon = Some(Icon.Glyph.Blank)
                )(sortOption.optionName)
              )
            }
          ),
          MenuDividerR()(),
          MenuItemR(
            isSelected = option.showFundClosed,
            onClick = onChange(option.copy(showFundClosed = !option.showFundClosed), close),
            icon = Some(Icon.Glyph.Blank)
          )("Show closed funds")
        )
    )()
  }

  private def renderFundSubsJoinedAsAdmin(
    props: Props,
    filteredFundSubsJoinedAsAdmin: List[FundSubJoinedAsAdmin],
    router: RouterCtl[Page]
  ) = {
    <.div(
      <.div(
        ^.marginTop := "36px",
        <.div(
          tw.flex.spaceX20,
          WorkflowsAdBanner(),
          IntegPlatformAdBanner()
        ),
        <.div(DividerR()(), tw.my20),
        renderSearchAndSort(
          props,
          FundSubHomePage.Tab.Funds,
          filteredFundSubsJoinedAsAdmin.length
        )
      ),
      if (filteredFundSubsJoinedAsAdmin.isEmpty && props.sortAndFilterOptions.fundSortAndFilterOption.searchKey.nonEmpty) {
        renderEmptyResult
      } else {
        filteredFundSubsJoinedAsAdmin.toVdomArray(
          using { fund =>
            <.div(
              ComponentUtils.testId(FundSubHomePageFundList, "AdminCard"),
              ^.key := fund.fundSubId.idString,
              tw.mt16,
              renderFundSubAsAdminCardContent(
                fund,
                router,
                props.sortAndFilterOptions.fundSortAndFilterOption.searchKey.nonEmpty
              )
            )
          }
        )
      }
    )
  }

  private def renderFundSubsJoinedAsLp(
    filteredFundSubsJoinedAsLp: List[FundSubJoinedAsLp],
    props: Props,
    router: RouterCtl[Page]
  ) = {
    val shouldShowLpProfile = props.investmentEntityList.nonEmpty && !props.hideLpProfile
    <.div(
      // TODO: remove the TagMod condition when Investor Access launches
      TagMod.when(shouldShowLpProfile)(
        <.div(
          renderInvestmentEntityList(props, router),
          <.div(
            tw.text15.leading24.fontSemiBold.textGray8.mb8,
            tw.sm(tw.text17.leading28),
            "Your investments"
          )
        )
      ),
      <.div(
        TagMod.when(!shouldShowLpProfile) { ^.marginTop := "36px" },
        renderSearchAndSort(
          props,
          FundSubHomePage.Tab.Investments,
          filteredFundSubsJoinedAsLp.length
        )
      ),
      if (
        filteredFundSubsJoinedAsLp.isEmpty && props.sortAndFilterOptions.investmentSortAndFilterOption.searchKey.nonEmpty
      ) {
        renderEmptyResult
      } else {
        val activeOrders = filteredFundSubsJoinedAsLp.filterNot(_.orderType.isOfflineOrder)
        val inactiveOrders = filteredFundSubsJoinedAsLp.filter(_.orderType.isOfflineOrder)
        <.div(
          <.div(
            if (inactiveOrders.nonEmpty) tw.mb48 else TagMod.empty,
            tw.mt16,
            renderActiveSubscriptionSection(
              activeOrders,
              router,
              props.sortAndFilterOptions.investmentSortAndFilterOption.searchKey.nonEmpty
            )
          ),
          renderInactiveSubscriptionSection(
            inactiveOrders,
            props.sortAndFilterOptions.investmentSortAndFilterOption.searchKey.nonEmpty
          )
        )
      }
    )
  }

  private def renderFundList(
    props: Props,
    filteredFundSubsJoinedAsLp: List[FundSubJoinedAsLp],
    filteredFundSubsJoinedAsAdmin: List[FundSubJoinedAsAdmin]
  ): VdomElement = {
    WithReactRouterR { router =>
      props.currentTab match {
        case FundSubHomePage.Tab.Funds =>
          renderFundSubsJoinedAsAdmin(
            props = props,
            filteredFundSubsJoinedAsAdmin = filteredFundSubsJoinedAsAdmin,
            router = router
          )
        case FundSubHomePage.Tab.Investments =>
          renderFundSubsJoinedAsLp(
            filteredFundSubsJoinedAsLp = filteredFundSubsJoinedAsLp,
            props = props,
            router = router
          )
      }
    }
  }

  private def renderActiveSubscriptionSection(
    activeSubscriptions: List[FundSubJoinedAsLp],
    router: RouterCtl[Page],
    isSearchResult: Boolean
  ) = {
    TagMod.when(activeSubscriptions.nonEmpty)(
      TagMod(
        activeSubscriptions.toVdomArray(
          using { fund =>
            <.div(
              ComponentUtils.testId(FundSubHomePageFundList, "LpCard"),
              ^.key := fund.fundSubLpId.idString,
              renderActiveOrder(
                fund,
                router,
                isSearchResult
              )
            )
          }
        )
      )
    )
  }

  private def startSubscriptionBtn(
    subscription: FundSubJoinedAsLp,
    isSearchResult: Boolean
  ): VdomElement = {
    Modal(
      renderTarget = openModal =>
        Button(
          style = Button.Style.Ghost(),
          onClick = Callback.when(isSearchResult)(
            ActionEventLoggerJs.logPageViewCb(
              subPage = Some("All Funds Dashboard - Search Result: Fund Joined As LP - Inactive Order")
            )
          ) >> openModal
        )(
          <.div(
            tw.flex.itemsCenter,
            <.div(s"Begin ${FundSubCopyUtils.getFlowTerm.standAloneTerm}"),
            <.div(tw.ml8, IconR(name = Icon.Glyph.ChevronRight, size = Icon.Size.Px16)())
          )
        ),
      renderContent = onClose =>
        ActivateSubscriptionModal(
          lpId = subscription.fundSubLpId,
          firmName = subscription.lpFirmName,
          onClose = onClose
        )(),
      title = s"Begin ${FundSubCopyUtils.getFlowTerm.standAloneTerm}"
    )()
  }

  private def renderInactiveOrder(
    fundSubJoinedAsLp: FundSubJoinedAsLp,
    isSearchResult: Boolean
  ): VdomElement = {
    FundSubCard(
      fundName = fundSubJoinedAsLp.fundName,
      logoUrl = fundSubJoinedAsLp.logoUrl,
      logoBgColor = fundSubJoinedAsLp.logoBgColor,
      lpStatusOpt = Option(fundSubJoinedAsLp.lpStatus),
      onClick = Callback.empty,
      isFundClosed = fundSubJoinedAsLp.isFundClosed,
      clickableAreaOpt = Option.unless(fundSubJoinedAsLp.isFundClosed) {
        FundSubCard.ClickableArea.TopRight(renderTarget = _ => startSubscriptionBtn(fundSubJoinedAsLp, isSearchResult))
      }
    )(
      renderInfoForLp(fundSubJoinedAsLp)
    )
  }

  private def renderInactiveSubscriptionSection(
    inactiveSubscriptions: List[FundSubJoinedAsLp],
    isSearchResult: Boolean
  ) = {
    TagMod.when(inactiveSubscriptions.nonEmpty)(
      <.div(
        <.div(
          tw.mb16,
          <.div(
            tw.leading24.fontSemiBold.text15.textGray8.mb8,
            tw.sm(tw.text17.leading28),
            "Subscriptions prepared for you"
          ),
          <.div(
            tw.textGray7.text13.leading20,
            "Subscription forms that have been prepared for you by fund managers. " +
              s"Click on a card to start a ${FundSubCopyUtils.getFlowTerm.standAloneTerm} for that entity."
          )
        ),
        inactiveSubscriptions.toVdomArray(
          using { fund =>
            <.div(
              ComponentUtils.testId(FundSubHomePageFundList, "LpCard"),
              ^.key := fund.fundSubLpId.idString,
              renderInactiveOrder(
                fund,
                isSearchResult
              )
            )
          }
        )
      )
    )
  }

  private def renderFundSubAsAdminCardContent(
    fundSubJoinedAsAdmin: FundSubJoinedAsAdmin,
    router: RouterCtl[Page],
    isSearchResult: Boolean
  ): VdomElement = {
    val numberOfLpNode = if (fundSubJoinedAsAdmin.numberOfLp == 0) {
      renderEmptyContent
    } else {
      <.div(ContentStyle, fundSubJoinedAsAdmin.numberOfLp)
    }

    val lastActivityNode = fundSubJoinedAsAdmin.lastActivityAt.fold(
      renderEmptyContent
    ) { lastActivityAt =>
      <.div(
        ContentStyle,
        DateTimeUtils
          .formatDefaultWithSameYearHidden(lastActivityAt, DateTimeUtils.defaultTimezone)()
      )
    }

    val closingDateNode = fundSubJoinedAsAdmin.targetClosingDate.fold(
      renderEmptyContent
    ) { closingDate =>
      <.div(
        ContentStyle,
        DateTimeUtils
          .formatDefaultWithSameYearHidden(closingDate, DateTimeUtils.defaultTimezone)()
      )
    }

    FundSubCard(
      fundName = fundSubJoinedAsAdmin.fundName,
      fundDescriptionOpt = fundSubJoinedAsAdmin.customFundId,
      logoUrl = fundSubJoinedAsAdmin.logoUrl,
      logoBgColor = fundSubJoinedAsAdmin.logoBgColor,
      onClick = Callback.when(isSearchResult)(
        ActionEventLoggerJs.logPageViewCb(subPage = Some("All Funds Dashboard - Search Result: Fund Joined As Admin"))
      ) >> router.set(FundSubAdminPage(fundSubJoinedAsAdmin.investorEntity, fundSubJoinedAsAdmin.fundSubId)),
      isFundClosed = fundSubJoinedAsAdmin.isFundClosed
    )(
      <.div(
        tw.sm(tw.flex.itemsCenter),
        TagMod.when(fundSubJoinedAsAdmin.isFundClosed) { tw.opacity40 },
        <.div(
          tw.flex,
          <.div(
            ComponentUtils.testId(FundSubHomePageFundList, "LpNum"),
            ^.width := "72px",
            tw.flexFill,
            tw.sm(tw.flexNone),
            <.div(TitleStyle, "Investors"),
            numberOfLpNode
          ),
          <.div(
            ComponentUtils.testId(FundSubHomePageFundList, "LastActivity"),
            ^.width := "100px",
            tw.flexFill,
            tw.sm(tw.flexNone),
            tw.ml16,
            <.div(TitleStyle, "Last activity"),
            lastActivityNode
          )
        ),
        <.div(
          tw.wPx1.bgGray3.mx16.hidden,
          ^.height := "36px",
          tw.sm(tw.block)
        ),
        <.div(
          tw.wPc100.hPx1.bgGray3.my16,
          tw.sm(tw.hidden)
        ),
        <.div(
          tw.flex,
          <.div(
            ComponentUtils.testId(FundSubHomePageFundList, "Amount"),
            ^.width := "100px",
            tw.flexFill,
            tw.sm(tw.flexNone),
            <.div(TitleStyle, "Under review"),
            if (fundSubJoinedAsAdmin.needCurrencySetup) {
              renderNeedCurrencySetup(router, fundSubJoinedAsAdmin)
            } else if (fundSubJoinedAsAdmin.underReviewAmount < Epsilon) {
              renderEmptyContent
            } else {
              <.div(
                ContentStyle,
                CurrencyJsUtils.convertToMoneyString(
                  fundSubJoinedAsAdmin.underReviewAmount,
                  fundSubJoinedAsAdmin.currency,
                  CurrencyJsUtils.MoneyFormat2a
                )
              )
            }
          ),
          <.div(
            ComponentUtils.testId(FundSubHomePageFundList, "Amount"),
            ^.width := "100px",
            tw.ml16.flexFill,
            tw.sm(tw.flexNone),
            <.div(TitleStyle, "Accepted"),
            if (fundSubJoinedAsAdmin.needCurrencySetup) {
              renderNeedCurrencySetup(router, fundSubJoinedAsAdmin)
            } else if (fundSubJoinedAsAdmin.approvedAmount < Epsilon) {
              renderEmptyContent
            } else {
              <.div(
                ContentStyle,
                CurrencyJsUtils.convertToMoneyString(
                  fundSubJoinedAsAdmin.approvedAmount,
                  fundSubJoinedAsAdmin.currency,
                  CurrencyJsUtils.MoneyFormat2a
                )
              )
            }
          )
        ),
        <.div(
          tw.hidden,
          tw.sm(tw.flex),
          <.div(
            ComponentUtils.testId(FundSubHomePageFundList, "Target"),
            ^.width := "100px",
            tw.ml16,
            <.div(TitleStyle, "Target"),
            if (fundSubJoinedAsAdmin.needCurrencySetup) {
              renderNeedCurrencySetup(router, fundSubJoinedAsAdmin)
            } else {
              fundSubJoinedAsAdmin.targetAmount.fold(renderEmptyContent) { targetAmount =>
                <.div(
                  ContentStyle,
                  CurrencyJsUtils.convertToMoneyString(targetAmount, CurrencyJsUtils.MoneyFormat2a)
                )
              }
            }
          ),
          <.div(
            ComponentUtils.testId(FundSubHomePageFundList, "ClosingDate"),
            ^.width := "116px",
            tw.ml16.hidden,
            tw.md(tw.block),
            <.div(TitleStyle, "Target closing date"),
            closingDateNode
          )
        )
      )
    )
  }

  private def renderCollaboratorsPopover(infos: List[ParticipantInfo]) = {
    StopClickEventWrapper(
      PopoverR(
        renderTarget = (open, _) =>
          <.span(
            "and ",
            <.span(
              tw.textPrimary5.cursorPointer.hover(tw.underline),
              ^.onClick --> open,
              Pluralize(
                "other",
                infos.length,
                true
              )
            )
          ),
        position = PortalPosition.BottomLeft,
        renderContent = _ =>
          <.div(
            tw.px8.py4.text13.spaceY12,
            infos.toVdomArray(
              using { info =>
                <.div(
                  ^.key := info.email,
                  <.div(tw.mb2, info.displayName),
                  <.div(tw.textGray7, info.email)
                )
              }
            )
          )
      )()
    )
  }

  private def renderParticipants(users: List[ParticipantInfo]) = {
    val (others, userToShow) = if (users.size >= 2) {
      users.drop(1) -> users.headOption
    } else {
      List.empty -> users.headOption
    }

    userToShow.fold(renderEmptyContent) { user =>
      <.div(
        tw.hPx20.flex.wPc100,
        ^.key := user.userId.idString,
        <.div(
          ^.flexShrink := "1",
          tw.overflowHidden.whitespaceNowrap,
          TooltipR(
            renderContent = _(user.email),
            targetWrapper = PortalWrapper.Inline,
            renderTarget = <.div(
              tw.truncate.inlineBlock.text13.leading20.mr4.wPc100,
              user.fullName
            )
          )()
        ),
        TagMod.when(others.nonEmpty) {
          <.div(
            ^.flexShrink := "0",
            renderCollaboratorsPopover(others)
          )
        }
      )
    }
  }

  private def renderInfoForLp(fundSubJoinedAsLp: FundSubJoinedAsLp): VdomElement = {
    val users = List(fundSubJoinedAsLp.lpUser) ++ fundSubJoinedAsLp.collaborators

    val lpFirmNameNode = if (fundSubJoinedAsLp.lpFirmName.isEmpty) {
      renderEmptyContent
    } else {
      <.div(
        tw.truncate.maxWPc100,
        ContentStyle,
        fundSubJoinedAsLp.lpFirmName
      )
    }

    val lastActivityNode = fundSubJoinedAsLp.lastActivityAt.fold(
      renderEmptyContent
    ) { lastActivityAt =>
      if (fundSubJoinedAsLp.orderType.isOfflineOrder) {
        renderEmptyContent
      } else {
        <.div(
          ContentStyle,
          DateTimeUtils.formatDefaultWithSameYearHidden(lastActivityAt, DateTimeUtils.defaultTimezone)()
        )
      }
    }

    val commitmentNode = fundSubJoinedAsLp.commitmentAmount.fold(
      renderEmptyContent
    ) { commitmentAmount =>
      if (fundSubJoinedAsLp.orderType.isOfflineOrder) {
        renderEmptyContent
      } else {
        <.div(
          ContentStyle,
          CurrencyJsUtils.convertToMoneyString(commitmentAmount, CurrencyJsUtils.MoneyFormat2a)
        )
      }
    }

    <.div(
      tw.sm(tw.flex.itemsCenter),
      TagMod.when(fundSubJoinedAsLp.isFundClosed) { tw.opacity40 },
      <.div(
        tw.hidden,
        tw.md(tw.block.flex),
        <.div(
          ComponentUtils.testId(FundSubHomePageFundList, "LpNum"),
          ^.width := "180px",
          <.div(
            TitleStyle,
            tw.truncate,
            fundSubJoinedAsLp.firmLabel
          ),
          lpFirmNameNode
        ),
        <.div(
          ComponentUtils.testId(FundSubHomePageFundList, "Participants"),
          ^.width := "228px",
          tw.ml16,
          <.div(TitleStyle, "Participants"),
          renderParticipants(users)
        )
      ),
      <.div(
        tw.flex.flexFill,
        tw.md(tw.hidden),
        <.div(
          ComponentUtils.testId(FundSubHomePageFundList, "LpNum"),
          tw.flexFill,
          <.div(
            TitleStyle,
            tw.truncate,
            fundSubJoinedAsLp.firmLabel
          ),
          lpFirmNameNode
        ),
        <.div(
          ComponentUtils.testId(FundSubHomePageFundList, "Participants"),
          tw.flexFill.ml16,
          <.div(TitleStyle, "Participants"),
          renderParticipants(users)
        )
      ),
      <.div(
        tw.hidden,
        tw.sm(tw.flex),
        TagMod.when(fundSubJoinedAsLp.orderType.isOfflineOrder) {
          tw.invisible
        },
        <.div(tw.wPx1.bgGray3.mx16, ^.height := "36px"),
        <.div(
          tw.flex,
          <.div(
            ComponentUtils.testId(FundSubHomePageFundList, "LastActivity"),
            ^.width := "100px",
            <.div(TitleStyle, "Last activity"),
            lastActivityNode
          ),
          <.div(
            ComponentUtils.testId(FundSubHomePageFundList, "Commitment"),
            ^.width := "100px",
            tw.ml16,
            <.div(TitleStyle, "Commitment"),
            commitmentNode
          )
        )
      ),
      <.div(
        tw.sm(tw.hidden),
        TagMod.when(fundSubJoinedAsLp.orderType.isOfflineOrder) { tw.hidden },
        <.div(tw.wPc100.hPx1.bgGray3.my16),
        <.div(
          tw.flex,
          <.div(
            ComponentUtils.testId(FundSubHomePageFundList, "LastActivity"),
            tw.flexFill,
            <.div(TitleStyle, "Last activity"),
            lastActivityNode
          ),
          <.div(
            ComponentUtils.testId(FundSubHomePageFundList, "Commitment"),
            tw.flexFill,
            tw.ml16,
            <.div(TitleStyle, "Commitment"),
            commitmentNode
          )
        )
      )
    )
  }

  private def renderActiveOrder(
    fundSubJoinedAsLp: FundSubJoinedAsLp,
    router: RouterCtl[Page],
    isSearchResult: Boolean
  ): VdomElement = {
    FundSubCard(
      fundName = fundSubJoinedAsLp.fundName,
      logoUrl = fundSubJoinedAsLp.logoUrl,
      logoBgColor = fundSubJoinedAsLp.logoBgColor,
      lpStatusOpt = Option(fundSubJoinedAsLp.lpStatus),
      onClick = Callback.when(isSearchResult)(
        ActionEventLoggerJs.logPageViewCb(
          subPage = Some("All Funds Dashboard - Search Result: Fund Joined As LP - Active Order")
        )
      ) >> ActivateSubscriptionModal.routeToLpPage(
        fundSubJoinedAsLp.fundSubLpId,
        fundSubJoinedAsLp.flowType,
        router
      ),
      isFundClosed = fundSubJoinedAsLp.isFundClosed
    )(
      renderInfoForLp(fundSubJoinedAsLp)
    )
  }

  private def renderInvestmentEntityCard(router: RouterCtl[Page], investmentEntityInfo: InvestmentEntityInfo) = {
    val crossAppRedirectPage = CrossAppRouter.getRedirectPage(
      OfferingId.LpProfile,
      InvestmentEntityProfilesPage(investmentEntityInfo.investmentEntityId)
    )
    <.a(
      ^.key := investmentEntityInfo.investmentEntityId.idString,
      ^.href := router.urlFor(crossAppRedirectPage).value,
      ^.target.blank,
      ^.rel := "noopener noreferrer",
      tw.noUnderline.textGray8.hover(tw.noUnderline),
      tw.hPc100,
      tw.flex.flexCol.justifyBetween,
      tw.px20.py16,
      tw.bgGray0.rounded8.borderGray3.border1.borderAll,
      tw.cursorPointer.hover(tw.bgGray1.borderGray4),
      TruncateR(
        target = <.div(
          tw.heading4.fontSemiBold,
          investmentEntityInfo.entityName
        )
      )(),
      <.div(
        tw.flex.itemsCenter.spaceX16,
        <.div(
          tw.flexFill,
          TruncateR(
            target = <.div(tw.textGray6.mb2.text13.leading20, "Profile data")
          )(),
          <.div(
            tw.flex.itemsCenter.overflowHidden,
            if (investmentEntityInfo.profileDataProgress < 1.0) {
              <.div(
                tw.textPrimary3.mr8,
                RadialIndicatorR(percent = investmentEntityInfo.profileDataProgress.toDouble)()
              )
            } else {
              <.div(
                tw.textSuccess5.bgSuccess1.roundedFull.mr8.hPx20.wPx20,
                <.div(tw.m4, IconR(name = Icon.Glyph.Check, size = Icon.Size.Custom(12))())
              )
            },
            <.div(
              tw.text13.leading20.textGray8,
              s"${(investmentEntityInfo.profileDataProgress * 100).toInt}%"
            )
          )
        ),
        <.div(
          tw.flexFill,
          TruncateR(
            target = <.div(tw.textGray6.mb2.text13.leading20, "Profile used in")
          )(),
          TruncateR(
            target = <.div(
              tw.text13.leading20.textGray8,
              Pluralize(
                s"${FundSubCopyUtils.getFlowTerm.standAloneTerm}",
                investmentEntityInfo.numSubscriptions,
                true
              )
            )
          )()
        )
      )
    )
  }

  private def renderShowMoreInvestmentEntityCard(href: String, numMoreInvestmentEntities: Int) = {
    <.a(
      ^.href := href,
      ^.target.blank,
      ^.rel := "noopener noreferrer",
      tw.noUnderline.textGray7.hover(tw.noUnderline),
      tw.hPc100,
      ^.width := "56px",
      tw.flex.flexCol.itemsCenter.justifyCenter,
      tw.bgGray0.rounded8.borderGray3.border1.borderAll,
      tw.cursorPointer.hover(tw.bgGray1.borderGray4),
      s"+$numMoreInvestmentEntities"
    )
  }

  private def renderGoToInvestmentEntityProfileBtn(lpProfileUrl: String) = {
    <.div(
      <.div(
        tw.hidden,
        tw.sm(tw.block),
        Button(
          style = Button.Style.Minimal(),
          tpe = Button.Tpe.Link(href = lpProfileUrl, target = Button.Target.Blank)
        )(
          <.div(
            tw.flex.itemsCenter,
            <.div(tw.mr8, "Open Investor Access app"),
            <.div(IconR(name = Icon.Glyph.OpenNewWindow, size = Icon.Size.Px16)())
          )
        )
      ),
      <.div(
        tw.sm(tw.hidden),
        Button(
          style = Button.Style.Minimal(icon = Some(Icon.Glyph.OpenNewWindow)),
          tpe = Button.Tpe.Link(href = lpProfileUrl)
        )()
      )
    )
  }

  private def renderInvestmentEntityList(props: Props, router: RouterCtl[Page]) = {
    val crossAppRedirectPage = CrossAppRouter.getRedirectPage(
      OfferingId.LpProfile,
      StaticAuthPage.CommonEntryPoint
    )

    val lpProfileUrl = router.urlFor(crossAppRedirectPage).value

    val sortedInvestmentEntityList = props.investmentEntityList
      .sortWith((a, b) => a.lastUpdated.compareTo(b.lastUpdated) > 0)
    <.div(
      tw.mb48,
      ^.marginTop := "54px",
      <.div(
        tw.flex.itemsCenter.justifyBetween,
        <.div(
          tw.text15.leading24.fontSemiBold.textGray8,
          tw.sm(tw.text17.leading28),
          "Investment profile management"
        ),
        Option.when(props.investmentEntityList.nonEmpty) {
          renderGoToInvestmentEntityProfileBtn(lpProfileUrl)
        }
      ),
      Option.when(props.investmentEntityList.isEmpty) {
        <.div(
          tw.text13.leading20.textGray7.mt8.flex.itemsCenter,
          <.div(tw.mr4, "You don't have any saved investment entities. Create one by going to"),
          Button(style = Button.Style.Text(isBlock = true), tpe = Button.Tpe.Link(href = lpProfileUrl))(
            <.div(
              tw.flex.itemsCenter,
              <.div(tw.mr4, "Investor Access app"),
              <.div(IconR(name = Icon.Glyph.OpenNewWindow)())
            )
          )
        )
      },
      Option.when(props.investmentEntityList.nonEmpty) {
        React.Fragment(
          <.div(
            tw.mt20.hidden,
            tw.sm(tw.block.flex),
            ^.height := "112px",
            tw.spaceX16,
            sortedInvestmentEntityList
              .take(2)
              .map(entity =>
                <.div(
                  ^.key := entity.masterProfileId.idString,
                  ^.maxWidth := "50%",
                  tw.flexFill,
                  renderInvestmentEntityCard(router, entity)
                )
              )
              .toVdomArray,
            Option.when(props.investmentEntityList.size > 2) {
              renderShowMoreInvestmentEntityCard(
                href = lpProfileUrl,
                props.investmentEntityList.size - 2
              )
            }
          ),
          <.div(
            tw.mt20,
            tw.sm(tw.hidden),
            ^.height := "112px",
            tw.flex.spaceX16,
            sortedInvestmentEntityList
              .take(1)
              .map(entity =>
                <.div(
                  ^.key := entity.masterProfileId.idString,
                  tw.flexFill,
                  renderInvestmentEntityCard(router, entity)
                )
              )
              .toVdomArray,
            Option.when(props.investmentEntityList.size > 1) {
              renderShowMoreInvestmentEntityCard(
                href = lpProfileUrl,
                props.investmentEntityList.size - 1
              )
            }
          )
        )
      }
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
