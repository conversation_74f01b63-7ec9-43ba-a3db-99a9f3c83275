// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.inbox

import com.raquo.airstream.core.Signal
import com.raquo.laminar.api.L.*
import design.anduin.components.text.laminar.TruncateL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*

import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.stargazer.service.formcomment.FormCommentCommons.DualCommentThread
import com.anduin.stargazer.fundsub.module.comment.FundSubCommentUtils
import com.anduin.stargazer.fundsub.module.comment.laminar.inbox.InboxDualThreadSummaryItemL.LpInfoForThreadSummary
import com.anduin.stargazer.fundsub.module.comment.laminar.share.{
  DualThreadAssignmentInfo,
  DualThreadCommentsCountSummary,
  InactiveReminderText
}

// Show anchorpoint and latest comment of each thread (public, internal)
private[inbox] case class InboxDualThreadSummaryItemL(
  dualCommentThreadSignal: Signal[DualCommentThread],
  hasNewSharedThreadNotiSignal: Signal[Boolean],
  hasNewInternalThreadNotiSignal: Signal[Boolean],
  isHiddenSignal: Signal[Boolean],
  isCommentAssignmentEnabledSignal: Signal[Boolean],
  isSelectedSignal: Signal[Boolean],
  inactiveThresholdSignal: Signal[Option[Int]],
  onViewAnchorPoint: Observer[(DualCommentThread, Boolean)],
  fundSubId: FundSubId,
  refetchAssigneeInfoEventStream: EventStream[Unit],
  isResolvedTabSignal: Signal[Boolean],
  lpInfoSignal: Signal[LpInfoForThreadSummary]
) {

  private val internalCommentThreadOptSignal = dualCommentThreadSignal.map(_.internalCommentThreadOpt)

  private val sharedCommentThreadOptSignal = dualCommentThreadSignal.map(_.sharedCommentThreadOpt)

  private val shouldRenderCommentSummariesSignal =
    internalCommentThreadOptSignal.map(_.nonEmpty) || sharedCommentThreadOptSignal.map(_.nonEmpty)

  private val shouldRenderInActiveSharedCommentNotificationSignal: Signal[Boolean] =
    inactiveThresholdSignal.combineWith(sharedCommentThreadOptSignal).map {
      case (inactiveThresholdOpt, sharedCommentThread) =>
        sharedCommentThread.exists { thread =>
          thread.isInactive(inactiveThresholdOpt)
        }
    }

  private val clickViewCommentEventBus = new EventBus[Boolean] // isInternal

  private def renderInvestorInfo = {
    val displayNameSignal = lpInfoSignal.map { dualCommentThread =>
      if (dualCommentThread.investmentEntity.nonEmpty) {
        dualCommentThread.investmentEntity
      } else {
        dualCommentThread.mainInvestorName
      }
    }
    div(
      tw.fontMedium.mr16.flexFill,
      text <-- displayNameSignal
    )
  }

  private def renderInvestorNameAndTimestamp = {
    div(
      ComponentUtils.testIdL(InboxDualThreadSummaryItemL, "Avatar"),
      tw.mb8.flex.itemsCenter.spaceX0,
      TruncateL(
        lineClamp = Some(1),
        target = renderInvestorInfo
      )(),
      child <-- dualCommentThreadSignal
        .map(_.getLatestCommentTime)
        .splitOption(
          project = (_, latestCommentTimeSignal) =>
            div(
              tw.textSmall.textGray6,
              text <-- latestCommentTimeSignal.map(
                FundSubCommentUtils.getCommentLastUpdateDisplayString
              )
            ),
          ifEmpty = emptyNode
        )
    )
  }

  private def renderAssignmentSection = {
    val shouldRenderAssignmentSectionSignal =
      isCommentAssignmentEnabledSignal && !dualCommentThreadSignal.map(_.isUnAssigned)
    div(
      child <-- shouldRenderAssignmentSectionSignal.splitBoolean(
        whenTrue = _ =>
          DualThreadAssignmentInfo(
            fundSubId,
            lpIdSignal = dualCommentThreadSignal.map(_.lpId),
            topicDataSignal = dualCommentThreadSignal.map(_.topicData),
            refetchAssigneeInfoEventStream = refetchAssigneeInfoEventStream
          )(),
        whenFalse = _ => emptyNode
      )
    )
  }

  def apply(): HtmlElement = {
    div(
      ComponentUtils.testIdL(InboxDualThreadSummaryItemL),
      tw.px16.pt12,
      isSelectedSignal.cls(tw.bgPrimary1.bgOpacity40),
      tw.hover(tw.bgGray1),
      div(
        ComponentUtils.testIdL(InboxDualThreadSummaryItemL),
        tw.pb16.borderBottom.borderGray3.border1,
        tw.cursorPointer,
        onClickOnThreadSummary,
        renderInvestorNameAndTimestamp,
        InboxCommentAnchorPointDescriptionL(
          topicDataSignal = dualCommentThreadSignal.map(_.topicData),
          isHiddenSignal = isHiddenSignal
        )(),
        // last comment of each thread
        renderCommentSummaries,
        renderAssignmentSection
      ),
      clickViewCommentEventBus.events.withCurrentValueOf(dualCommentThreadSignal).map {
        (isInternalComment, dualCommentThread) =>
          dualCommentThread -> isInternalComment
      } --> onViewAnchorPoint
    )
  }

  private def onClickOnThreadSummary = {
    onClick.compose(
      _.mapToUnit
        .withCurrentValueOf(dualCommentThreadSignal)
        .map { dualCommentThread =>
          dualCommentThread -> dualCommentThread.internalCommentThreadOpt.nonEmpty
        }
    ) --> onViewAnchorPoint
  }

  private def renderCommentSummaries = {
    child <-- shouldRenderCommentSummariesSignal.splitBoolean(
      whenTrue = _ =>
        div(
          tw.flex.itemsCenter.mt8,
          renderInternalCommentSummary,
          renderSharedCommentSummary
        ),
      whenFalse = _ => emptyNode
    )
  }

  private def renderInternalCommentSummary = {
    child <-- internalCommentThreadOptSignal
      .splitOption(
        project = (_, internalCommentSignal) =>
          div(
            width.px := 60,
            DualThreadCommentsCountSummary(
              isPublicSignal = internalCommentSignal.map(_.isPublic),
              isNewCommentSignal = hasNewInternalThreadNotiSignal,
              totalNumberOfCommentsInThreadSignal = internalCommentSignal.map(_.numOfReply + 1),
              onClickObs = clickViewCommentEventBus.writer.contramap(_ => true),
              shouldBlur = !isResolvedTabSignal && internalCommentSignal.map(_.isResolved)
            )()
          ),
        ifEmpty = emptyNode
      )
  }

  private def renderSharedCommentSummary = {
    child <-- sharedCommentThreadOptSignal
      .splitOption(
        project = (_, sharedCommentSignal) =>
          div(
            tw.flex.itemsCenter.spaceX8,
            DualThreadCommentsCountSummary(
              isPublicSignal = sharedCommentSignal.map(_.isPublic),
              isNewCommentSignal = hasNewSharedThreadNotiSignal,
              totalNumberOfCommentsInThreadSignal = sharedCommentSignal.map(_.numOfReply + 1),
              onClickObs = clickViewCommentEventBus.writer.contramap(_ => false),
              shouldBlur = !isResolvedTabSignal && sharedCommentSignal.map(_.isResolved)
            )(),
            child <-- shouldRenderInActiveSharedCommentNotificationSignal.splitBoolean(
              whenTrue = _ => InactiveReminderText(sharedCommentSignal.map(_.daysFromLastComment()))(),
              whenFalse = _ => emptyNode
            )
          ),
        ifEmpty = emptyNode
      )
  }

}

private[inbox] object InboxDualThreadSummaryItemL {

  case class LpInfoForThreadSummary(
    lpId: FundSubLpId,
    mainInvestorName: String,
    investmentEntity: String
  )

}
