// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.lp.v2.fill.review

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.button.laminar.ButtonL.Height
import design.anduin.components.editor.laminar.EditorPreviewL
import design.anduin.components.icon.Icon
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterL, ModalL}
import design.anduin.components.progress.CircleIndicator
import design.anduin.components.progress.laminar.{BlockIndicatorL, CircleIndicatorL}
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.subscriptiondoc.{
  StartEditingSubscriptionFormParams,
  SubscriptionVersionBasicInfo,
  SubscriptionVersionReviewStatus
}
import anduin.fundsub.model.FundSubSharedClientModel.{FundSubLpClientModel, SubscriptionRequestChangeType}
import anduin.id.form.FormId
import anduin.id.fundsub.FundSubLpId
import anduin.id.issuetracker.IssueId
import anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus
import anduin.service.GeneralServiceException
import anduin.stargazer.service.formcomment.FormCommentCommons.CommentThread
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils
import com.anduin.stargazer.fundsub.module.lp.v2.fill.FillSubscriptionDocumentsStep.OpenFillFormModalData
import com.anduin.stargazer.fundsub.module.lp.v2.fill.form.{EditSubscriptionButton, EditSubscriptionConfirmModal}
import com.anduin.stargazer.fundsub.module.lp.v2.sign.EditSignatureButton
import com.anduin.stargazer.fundsub.module.lp.v2.sign.signbutton.AddESignatureButton
import com.anduin.stargazer.fundsub.module.model.FundSubClientModel.FundSubPublicClientModel

private[v2] final case class ViewMessageButton(
  lpId: FundSubLpId,
  formIdOptSignal: Signal[Option[FormId]],
  lpStatusSignal: Signal[LpStatus],
  fundSubModelSignal: Signal[FundSubPublicClientModel],
  fundSubLpModelSignal: Signal[FundSubLpClientModel],
  openFillFormModalVar: Var[OpenFillFormModalData],
  versionInfoSignal: Signal[SubscriptionVersionBasicInfo],
  refreshLatestVersion: Observer[Unit],
  requestChange: SubscriptionVersionReviewStatus.RequestChanged,
  onCancelSignatureRequest: Observer[Unit]
) {

  private val isCallingEditSubscription = Var(false)
  private val editingSubscriptionEventBus = new EventBus[Unit]
  private val selectedCommentAliasVar = Var(Option.empty[String])
  private val openEditSubscriptionWarningVar = Var(false)
  private val isDisabledSignal = fundSubModelSignal.map(_.closingConfig.exists(_.isClosed))

  private val editSubscriptionConfirmReasonOptSignal: Signal[Option[EditSubscriptionConfirmModal.ConfirmReason]] =
    versionInfoSignal.map { versionInfo =>
      EditSubscriptionConfirmModal.shouldShowConfirm(versionInfo)
    }

  private val clickFormCommentingAliasEventBus = new EventBus[String]

  def apply(): HtmlElement = {
    div(
      Option.when(requestChange.requestChangeType == SubscriptionRequestChangeType.ChangeSubscriptionForm)(
        div(
          editingSubscriptionEventBus.events
            .withCurrentValueOf(editSubscriptionConfirmReasonOptSignal)
            .flatMapSwitch(editSubscription) --> Observer.empty,
          child.maybe <-- isCallingEditSubscription.signal.map {
            Option.when(_) {
              div(
                tw.fixed.inset0.overflowYAuto,
                tw.flex.justifyCenter.itemsCenter,
                tw.z9999,
                backgroundColor := "rgba(48, 64, 77, 0.9)",
                div(
                  tw.flex.flexCol.itemsCenter.textGray0,
                  CircleIndicatorL(size = CircleIndicator.Size.Custom(42))(),
                  div(tw.text11.leading16.mt8, "Loading form...")
                )
              )
            }
          }
        )
      ),
      ModalL(
        renderTitle = _ => "Change request message",
        size = ModalL.Size(width = ModalL.Width.Px600),
        renderTarget = openModal =>
          div(
            tw.mt12.flex,
            TooltipL(
              renderTarget = ButtonL(
                testId = "ViewMessage",
                style = ButtonL.Style.Full(height = Height.Fix24, icon = Option(Icon.Glyph.Envelope)),
                onClick = openModal.contramap(_ => ()),
                isDisabled = isDisabledSignal
              )("View Message"),
              renderContent = _.amend("View message from the fund")
            )()
          ),
        renderContent = renderModalBody
      )()
    )
  }

  private def renderModalBody(closeModal: Observer[Unit]) = {
    div(
      Option.when(requestChange.requestChangeType == SubscriptionRequestChangeType.ChangeSubscriptionForm)(
        ModalL(
          renderTitle = _ => "Edit form",
          renderContent = _ =>
            div(
              child <-- versionInfoSignal.map { versionInfo =>
                EditSubscriptionConfirmModal(
                  lpId = lpId,
                  onConfirm = Observer { _ =>
                    editingSubscriptionEventBus.emit(())
                    openEditSubscriptionWarningVar.set(false)
                  },
                  onCancel = Observer { _ =>
                    openEditSubscriptionWarningVar.set(false)
                  },
                  fundSubModelSignal = fundSubModelSignal,
                  versionInfo = versionInfo
                )()
              }
            ),
          isOpened = Some(openEditSubscriptionWarningVar.signal),
          afterUserClose = Observer { _ =>
            openEditSubscriptionWarningVar.set(false)
          }
        )()
      ),
      ModalBodyL(
        div(
          "The fund requested you to make changes to your ",
          requestChange.requestChangeType match {
            case SubscriptionRequestChangeType.ChangeSubscriptionForm =>
              s"${FundSubCopyUtils.getFlowTerm.standAloneTerm}."
            case SubscriptionRequestChangeType.ChangeSignedDoc => "signature."
          }
        ),
        div(
          tw.mt16.fontSemiBold,
          "Message from the fund:"
        ),
        div(
          tw.mt8.py16.px20.bgGray1,
          tw.borderAll.rounded4.border1.borderGray3,
          requestChange.messageOpt.map { message =>
            EditorPreviewL(Val(message))()
          },
          Option.when(requestChange.requestChangeType == SubscriptionRequestChangeType.ChangeSubscriptionForm)(
            div(
              child.maybe <-- formIdOptSignal.map {
                _.map { lpFormId =>
                  renderCommentThreads(lpFormId)
                }
              }
            )
          )
        )
      ),
      ModalFooterL(
        div(
          tw.flex.justifyEnd,
          ButtonL(onClick = closeModal.contramap(_ => ()))("Done"),
          div(
            tw.ml8,
            requestChange.requestChangeType match {
              case SubscriptionRequestChangeType.ChangeSubscriptionForm =>
                EditSubscriptionButton(
                  lpId = lpId,
                  lpStatusSignal = lpStatusSignal,
                  openFillFormModalVar = openFillFormModalVar,
                  fundSubModelSignal = fundSubModelSignal,
                  versionInfoSignal = versionInfoSignal,
                  onCancelSignatureRequest = onCancelSignatureRequest
                )()
              case SubscriptionRequestChangeType.ChangeSignedDoc =>
                child.maybe <-- EditSignatureButton
                  .showEditSignatureSignal(
                    Val(requestChange),
                    AddESignatureButton.shouldBlockSignatureByReviewSignal(
                      fundSubModelSignal,
                      fundSubLpModelSignal
                    )
                  )
                  .map(
                    Option.when(_)(
                      EditSignatureButton(
                        lpId,
                        Val(requestChange),
                        refreshLatestVersion
                      )()
                    )
                  )
            }
          )
        )
      )
    )
  }

  private def renderCommentThreads(lpFormId: FormId) = {
    FetchLpRequestChangesAttachedComments(
      fundSubLpId = lpId,
      lpFormId = lpFormId,
      commentIds = requestChange.attachedComments,
      renderChildren = renderProps => {
        val sortedCommentThreads =
          renderProps.commentThreadsSignal.map(_.sortBy(-_.createdAtOpt.map(_.getEpochSecond).getOrElse(0L)))
        div(
          child <-- renderProps.isLoadingSignal.splitBoolean(
            whenTrue = _ => BlockIndicatorL()(),
            whenFalse = _ =>
              div(
                children <-- sortedCommentThreads.split(_.id) { case (threadId, _, threadSignal) =>
                  renderThread(threadId, threadSignal)
                }
              )
          )
        )
      }
    )()
  }

  private def renderThread(threadId: IssueId, threadSignal: Signal[CommentThread]) = {
    div(
      tw.mt16,
      child <-- threadSignal
        .map { thread =>
          val tocSection = thread.formQuestionData.map(_.tocSection).getOrElse("")
          val fieldDescription = thread.formQuestionData.map(_.fieldDescription).getOrElse("")
          val fieldAlias = thread.formQuestionData.map(_.fieldAlias).getOrElse("")
          val lastComment = thread.lastComment
          val numReplies = thread.replies.size
          (tocSection, fieldDescription, fieldAlias, lastComment, numReplies)
        }
        .distinct
        .map { case (tocSection, fieldDescription, fieldAlias, lastComment, numReplies) =>
          AttachedCommentItemLpView(
            fundSubLpId = lpId,
            tocSection = tocSection,
            fieldDescription = fieldDescription,
            content = lastComment.content,
            fieldAlias = fieldAlias,
            creatorIdOpt = lastComment.creatorOpt,
            creatorInfoOpt = lastComment.creatorInfoOpt,
            createdAtOpt = lastComment.createdAt,
            editedAtOpt = lastComment.lastUpdatedAt,
            numReplies = numReplies,
            handleOnClick = clickFormCommentingAliasEventBus.writer,
            threadId = threadId,
            commentId = lastComment.commentId,
            hasMentions = lastComment.mentions.nonEmpty
          )()
        },
      clickFormCommentingAliasEventBus.events.withCurrentValueOf(editSubscriptionConfirmReasonOptSignal).map {
        case (fieldAlias, confirmReasonOpt) =>
          selectedCommentAliasVar.set(Some(fieldAlias))
          if (confirmReasonOpt.nonEmpty) {
            openEditSubscriptionWarningVar.set(true)
          } else {
            editingSubscriptionEventBus.emit(())
          }
      } --> Observer.empty
    )
  }

  private def editSubscription(confirmReasonOpt: Option[EditSubscriptionConfirmModal.ConfirmReason])
    : EventStream[Unit] = {
    AirStreamUtils.taskToStream(
      for {
        _ <- ZIO.attempt(isCallingEditSubscription.set(true))
        resp <- FundSubEndpointClient
          .startEditingSubscriptionForm(StartEditingSubscriptionFormParams(lpId))
          .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
        _ <- ZIO.attempt(isCallingEditSubscription.set(false))
      } yield resp.fold(
        _ => Toast.error("Failed to start editing, try again"),
        res => {
          if (confirmReasonOpt.contains(EditSubscriptionConfirmModal.ConfirmReason.CancelPendingSignatureRequest)) {
            onCancelSignatureRequest.onNext(())
          }
          openFillFormModalVar.set(
            OpenFillFormModalData(
              isOpened = Option(true),
              res.versionIndex,
              isOpenToEdit = true,
              targetedFieldAlias = selectedCommentAliasVar.now()
            )
          )
        }
      )
    )
  }

}
