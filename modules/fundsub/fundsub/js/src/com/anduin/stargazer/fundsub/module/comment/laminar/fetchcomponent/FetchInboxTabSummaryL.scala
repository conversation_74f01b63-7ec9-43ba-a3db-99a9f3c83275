// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent

import com.raquo.laminar.api.L.*

import anduin.frontend.AirStreamUtils
import anduin.id.fundsub.FundSubId
import anduin.model.notichannel.FundSubNotificationChannels
import anduin.stargazer.service.formcomment.FormCommentCommons.{GetInboxTabSummaryParams, GetInboxTabSummaryResponse}
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.notiCenter.NotificationCenter

private[laminar] case class FetchInboxTabSummaryL(
  fundSubId: FundSubId,
  renderChildren: FetchInboxTabSummaryL.RenderChildren => HtmlElement
) {

  private val forceUpdateVar = Var(0)
  private val fetchSummaryEventBus = new EventBus[Unit]
  private val resultVar: Var[Option[GetInboxTabSummaryResponse]] = Var(None)

  private val dataTrackingSignal = NotificationCenter
    .eventStream(FundSubNotificationChannels.fundSubFormComment(fundSubId))
    .throttle(ms = 3000)
    .toSignal(NotificationCenter.Watch.Initial)

  private def fetchSummary() = {
    AirStreamUtils.taskToStreamDEPRECATED {
      FundSubEndpointClient
        .getInboxTabSummary(
          GetInboxTabSummaryParams(
            fundId = fundSubId
          )
        )
        .map {
          _.fold(
            _ => None,
            response => Option(response)
          )
        }
    }
  }

  def apply(): HtmlElement = {
    renderChildren(
      FetchInboxTabSummaryL.RenderChildren(
        tabsSummaryDataSignal = resultVar.signal
      )
    ).amend(
      fetchSummaryEventBus.events
        .flatMapSwitch(_ => fetchSummary()) --> resultVar.writer,
      forceUpdateVar.signal.distinct.combineWith(dataTrackingSignal) --> Observer { _ =>
        fetchSummaryEventBus.emit(())
      }
    )
  }

}

private[laminar] object FetchInboxTabSummaryL {

  case class RenderChildren(
    tabsSummaryDataSignal: Signal[Option[GetInboxTabSummaryResponse]]
  )

}
