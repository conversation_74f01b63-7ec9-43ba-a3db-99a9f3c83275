// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar

import com.raquo.laminar.api.L.*
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*

import anduin.forms.engine.GaiaState
import anduin.forms.model.Schema
import anduin.forms.renderer.TableOfContent.Section
import anduin.forms.utils.FormDataUtils
import anduin.forms.{Form, FormStates}
import anduin.fundsub.comment.CommentBubbleL
import anduin.id.issuetracker.IssueId
import anduin.utils.StringUtils
import com.anduin.stargazer.fundsub.module.comment.laminar.SectionCommentCounterL.{CommentThreadInfo, Counter}
import com.anduin.stargazer.fundsub.module.comment.models.CommentsData

final case class SectionCommentCounterL(
  commentsDataSignal: Signal[CommentsData],
  form: Form,
  formStates: FormStates,
  section: Section,
  showInternalComment: <PERSON><PERSON><PERSON>,
  onJumpToTarget: Observer[String]
) {

  private val allContainers = FormDataUtils.findAllContainers(form)

  private val newCommentsSignal = commentsDataSignal.map(_.issueWithNewCommentIds.keys.toSet)

  private val allPagesInForm: Seq[(String, Schema.obj)] = FormDataUtils.findAllPage(form)

  private val formCommentsGroupedByPageSignal: Signal[Map[String, Seq[CommentThreadInfo]]] =
    commentsDataSignal.map { commentsData =>
      commentsData.threads
        .filter { thread =>
          thread.isFormComment && (showInternalComment || thread.isPublic)
        }
        .map { thread =>
          val alias = thread.topicData.asMessage.sealedValue.formQuestionData.map(_.fieldAlias).getOrElse("")
          (alias, thread.id)
        }
        .flatMap { case (fieldAlias, issueId) =>
          allPagesInForm
            .collectFirst {
              case (pageAlias, obj) if FormDataUtils.getContainedSections(obj).contains(fieldAlias) => pageAlias
            }
            .map { pageAlias =>
              SectionCommentCounterL.CommentThreadInfo(
                pageAlias = pageAlias,
                fieldAlias = fieldAlias,
                issueId = issueId
              )
            }
        }
        .groupBy(_.pageAlias)
    }

  private val firstFieldHasCommentSignal: Signal[Option[String]] = formCommentsGroupedByPageSignal.map {
    commentsByPage =>
      for {
        firstPageHasComment <- section.pages.find { pageAlias =>
          commentsByPage.get(pageAlias).exists(_.nonEmpty)
        }
        allFieldsInPage <- allPagesInForm
          .collectFirst {
            case (key, obj) if key == firstPageHasComment => FormDataUtils.getContainedSections(obj)
          }
        fieldWithComment <- allFieldsInPage.find { fieldAlias =>
          commentsByPage.getOrElse(firstPageHasComment, Seq.empty).exists(_.fieldAlias == fieldAlias)
        }
      } yield fieldWithComment
  }

  private def countCommentsOfPageSection(
    pageAlias: String,
    formCommentsByPage: Map[String, Seq[CommentThreadInfo]],
    gaiaState: GaiaState,
    newComments: Set[IssueId]
  ): Counter = {
    formCommentsByPage
      .get(pageAlias)
      .map { commentInfoList =>
        val hiddenCommentsCheckResults =
          commentInfoList.map { commentInfo =>
            gaiaState
              .get(commentInfo.fieldAlias)
              .exists(_.isHidden)
          }

        val numCommentNotifications = commentInfoList.count(data => newComments.contains(data.issueId))
        SectionCommentCounterL.Counter(
          numOpenComments = hiddenCommentsCheckResults.count(_ == false),
          numHiddenComments = hiddenCommentsCheckResults.count(_ == true),
          numCommentNotifications = numCommentNotifications
        )
      }
      .getOrElse(
        Counter(
          numOpenComments = 0,
          numHiddenComments = 0,
          numCommentNotifications = 0
        )
      )

  }

  private def countCommentsOfNonPageSection(
    sectionId: String,
    formCommentsByPage: Map[String, Seq[CommentThreadInfo]],
    gaiaState: GaiaState,
    newComments: Set[IssueId]
  ): Counter = {
    val childrenPages =
      formCommentsByPage
        .filter { case (pageId, _) =>
          allContainers.exists { case (key, obj) =>
            key == sectionId && FormDataUtils.getContainedSections(obj).contains(pageId)
          }
        }
        .keys
        .toList
    val commentCountForPages =
      childrenPages.map(countCommentsOfPageSection(_, formCommentsByPage, gaiaState, newComments))
    SectionCommentCounterL.Counter(
      numOpenComments = commentCountForPages.map(_.numOpenComments).sum,
      numHiddenComments = commentCountForPages.map(_.numHiddenComments).sum,
      numCommentNotifications = commentCountForPages.map(_.numCommentNotifications).sum
    )
  }

  def apply(): HtmlElement = {
    val commentCountSignal =
      formCommentsGroupedByPageSignal.combineWith(formStates.stateSignal, newCommentsSignal).map {
        case (formCommentsByPage, gaiaState, newComments) =>
          if (section.isPage) {
            countCommentsOfPageSection(section.key, formCommentsByPage, gaiaState, newComments)
          } else {
            countCommentsOfNonPageSection(section.key, formCommentsByPage, gaiaState, newComments)
          }
      }

    div(
      child.maybe <-- commentCountSignal.map { counter =>
        val commentStr = if (counter.numCommentNotifications > 0) {
          "new comment"
        } else {
          "comment"
        }
        Option.when(counter.numOpenComments + counter.numHiddenComments > 0) {
          TooltipL(
            renderTarget = {
              div(
                tw.mx8.cursorPointer,
                CommentBubbleL(
                  hasComment = true,
                  hasNewComment = counter.numCommentNotifications > 0,
                  isResolved = false,
                  theme = CommentBubbleL.Theme.Minimal,
                  totalComments = 0
                )().amend(
                  // Use `stopPropagation` to prevent users from clicking the containing section
                  onClick.stopPropagation.mapToUnit
                    .compose(
                      _.withCurrentValueOf(firstFieldHasCommentSignal)
                        .map { fieldAliasOpt =>
                          fieldAliasOpt.foreach(onJumpToTarget.onNext)
                        }
                    )
                    --> Observer.empty
                )
              )
            },
            renderContent = _.amend(
              Option.when(counter.numOpenComments + counter.numHiddenComments > 0) {
                val totalComment = counter.numOpenComments + counter.numHiddenComments
                // TODO @tuananhtd: update copy for hidden comment
                div(s"View ${StringUtils.pluralItem(totalComment, commentStr)}")
              }
            )
          )()
        }
      }
    )
  }

}

object SectionCommentCounterL {

  case class Counter(
    numOpenComments: Int,
    numHiddenComments: Int,
    numCommentNotifications: Int
  )

  case class CommentThreadInfo(
    pageAlias: String,
    fieldAlias: String,
    issueId: IssueId
  )

}
