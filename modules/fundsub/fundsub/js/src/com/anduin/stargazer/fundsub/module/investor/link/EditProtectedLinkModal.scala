// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.link

import design.anduin.components.button.Button
import design.anduin.components.modal.{Modal, ModalBody, ModalFooterWCancel}
import design.anduin.components.portal.PortalUtils
import design.anduin.components.toast.Toast
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.endpoint.dashboard.EditProtectedLinkParams
import anduin.id.fundsub.FundSubId
import anduin.link.ProtectedLinkState
import anduin.utils.StateSnapshotWithModFn
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.investor.link.LinkSettings.PasswordSetting

private[investor] final case class EditProtectedLinkModal(
  fundSubId: FundSubId,
  linkState: ProtectedLinkState,
  renderTarget: Callback => VdomNode,
  linkDisableChange: Option[Boolean],
  refreshLinkState: Callback,
  enterpriseLoginEnabledForFundsub: Boolean
) {

  def apply(): VdomElement = EditProtectedLinkModal.component(this)

  private val toLinkSettings = LinkSettings(
    password = PasswordSetting(
      password = if (linkState.hasPassword) {
        PasswordSetting.UseExisting
      } else {
        PasswordSetting.Change("")
      },
      isEnabled = linkState.hasPassword,
      initialIsEnabled = linkState.hasPassword
    ),
    expiryDate = LinkSettings.ExpiryDateSetting(
      expiryDate = linkState.expiryDate.getOrElse(LinkSettings.ExpiryDateSetting.default),
      isEnabled = linkState.expiryDate.isDefined
    ),
    isDisabled = linkState.isDisabled,
    whitelistedDomains = LinkSettings.WhitelistedDomainSetting(
      domains = linkState.whitelistedDomains.toSeq.sorted,
      isEnabled = linkState.whitelistedDomains.nonEmpty
    ),
    isEnterpriseLoginEnabled = linkState.isEnterpriseLoginEnabled
  )

}

private[investor] object EditProtectedLinkModal {

  private type Props = EditProtectedLinkModal

  private final case class State(
    linkSettings: LinkSettings,
    isBusy: Boolean
  )

  private class Backend(scope: BackendScope[Props, State]) {

    private val linkSettingsStateSnapshot = {
      StateSnapshotWithModFn.withReuse
        .zoom[State, LinkSettings](_.linkSettings)(linkSettings => _.copy(linkSettings = linkSettings))
        .prepareVia(scope)
    }

    private def editLinkCb(onCloseModal: Callback): Callback = {
      for {
        props <- scope.props
        _ <- scope.modState(_.copy(isBusy = true))
        state <- scope.state
        _ <- ZIOUtils.toReactCallback {
          val link = state.linkSettings
          val editLinkParams = EditProtectedLinkParams(
            fundSubId = props.fundSubId,
            isDisabledChange = props.linkDisableChange,
            passwordChange = if (link.password.isEnabled) {
              link.password.password match {
                case PasswordSetting.Change(password) =>
                  Some(EditProtectedLinkParams.PasswordChange(Some(password)))
                case PasswordSetting.UseExisting =>
                  None
              }
            } else {
              Some(EditProtectedLinkParams.PasswordChange(None))
            },
            expiryDateChange = if (props.toLinkSettings.expiryDate == link.expiryDate) {
              None
            } else {
              Some(
                EditProtectedLinkParams.ExpiryChange(Option.when(link.expiryDate.isEnabled)(link.expiryDate.expiryDate))
              )
            },
            whitelistedDomainsChange = if (link.whitelistedDomains.isEnabled) {
              if (link.whitelistedDomains.domains.toSet == props.linkState.whitelistedDomains) {
                None
              } else {
                Some(link.whitelistedDomains.domains.toSet)
              }
            } else {
              Some(Set.empty)
            },
            enableEnterpriseLoginChange =
              if (props.toLinkSettings.isEnterpriseLoginEnabled == link.isEnterpriseLoginEnabled) {
                None
              } else {
                Some(link.isEnterpriseLoginEnabled)
              }
          )

          FundSubEndpointClient
            .editProtectedLink(editLinkParams)
            .map {
              _.fold(
                _ => Toast.errorCallback("Failed to create the link, please try again later"),
                _ =>
                  for {
                    _ <- props.refreshLinkState
                    _ <- scope.modState(
                      _.copy(
                        // Reset password setting after the setting is done.
                        linkSettings = state.linkSettings.copy(
                          password = PasswordSetting(
                            password = PasswordSetting.UseExisting,
                            isEnabled = link.password.isEnabled,
                            initialIsEnabled = link.password.initialIsEnabled
                          ),
                          whitelistedDomains = link.whitelistedDomains.copy(
                            // Auto standardize domains: remove duplicates, sort
                            domains = link.whitelistedDomains.domains.distinct.sorted
                          )
                        )
                      ),
                      Toast.successCallback("Shareable link enabled successfully")
                    )
                    _ <- onCloseModal
                  } yield ()
              )
            }
        }
        _ <- scope.modState(_.copy(isBusy = false))
      } yield ()
    }

    private def resetState: Callback = {
      for {
        props <- scope.props
        _ <- scope.modState(_.copy(linkSettings = props.toLinkSettings))
      } yield ()
    }

    def renderModalContent(props: Props, state: State)(onClose: Callback): VdomElement = {
      React.Fragment(
        ModalBody()(
          FundSubProtectedLinkSettings(
            props.fundSubId,
            linkSettingsStateSnapshot(state),
            props.enterpriseLoginEnabledForFundsub
          )()
        ),
        ModalFooterWCancel(resetState >> onClose)(
          Button(
            testId = "Save",
            style = Button.Style.Full(color = Button.Color.Primary),
            onClick = editLinkCb(onClose),
            isDisabled = !LinkSettings.isValid(state.linkSettings) ||
              (props.linkDisableChange.isEmpty && (state.linkSettings == props.toLinkSettings))
          )(props.linkDisableChange.fold("Save")(isDisable => if (isDisable) "Disable" else "Enable"))
        )
      )
    }

    def render(props: Props, state: State): VdomElement = {
      Modal(
        testId = "ShareableLink",
        title = "Shareable link",
        size = Modal.Size(Modal.Width.Px600),
        renderTarget = props.renderTarget,
        renderContent = renderModalContent(props, state),
        isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false))
      )()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps { props =>
      State(props.toLinkSettings, isBusy = false)
    }
    .renderBackend[Backend]
    .build

}
