// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.ria

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.ria.{CreateRiaFundGroupParams, InviteAdvisor}
import anduin.id.fundsub.FundSubId
import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.divider.laminar.DividerL
import design.anduin.components.field.laminar.FieldL
import design.anduin.components.icon.Icon
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL, ModalL}
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.style.tw.*
import design.anduin.components.toast.Toast
import zio.ZIO

import anduin.model.common.emailaddress.EmailAddress
import com.anduin.stargazer.fundsub.client.FundSubRiaEndpointClient

private[ria] case class AddAdvisorGroupModal(
  fundSubId: FundSubId,
  existingGroupNameSignal: Signal[Seq[String]],
  renderTarget: Observer[Unit] => HtmlElement,
  onDoneAddAdvisorGroup: Observer[Unit]
) {

  private val groupNameVar = Var("")
  private val groupNameSignal = groupNameVar.signal.distinct

  private val membersVar = Var(Seq(InviteAdvisor()))
  private val membersSignal = membersVar.signal.distinct
  private val addGroupEvent = new EventBus[Unit]
  private val clickedAddButtonVar = Var(false)
  private val clickedAddButtonSignal = clickedAddButtonVar.signal.distinct

  private val isCreatingAdvisorGroupVar = Var(false)
  private val isCreatingAdvisorGroupSignal = isCreatingAdvisorGroupVar.signal.distinct

  private val isValidNameSignal =
    groupNameSignal.combineWith(existingGroupNameSignal).map { (groupName, existingGroupNames) =>
      groupName.trim.nonEmpty && !existingGroupNames.contains(groupName)
    }

  private val areAllEmailHasSameDomainSignal = membersSignal.map { members =>
    members
      .flatMap { member =>
        EmailAddress
          .unapply(
            name = Option(member.firstName),
            address = member.email
          )
          .map(_.domain.value)
      }
      .distinct
      .length == 1
  }

  private val areValidMembersSignal = areAllEmailHasSameDomainSignal &&
    membersSignal.map { members =>
      members.nonEmpty &&
      members.map(_.email.trim).distinct.length == members.length &&
      members.forall { item =>
        item.firstName.trim.nonEmpty &&
        item.lastName.trim.nonEmpty &&
        item.email.trim.nonEmpty &&
        EmailAddress.isValid(item.email.trim)
      }
    }

  private val isValidSignal = isValidNameSignal && areValidMembersSignal

  private def renderMembers(): HtmlElement = {
    div(
      child.maybe <-- membersSignal.map(_.isEmpty).map {
        Option.unless(_) {
          div(
            tw.mb4.text13.leading20,
            tw.flex.itemsCenter.gapX8,
            div(
              width.px(175),
              FieldL(
                label = Option("Advisor first name"),
                requirement = FieldL.Requirement.Required
              )()
            ),
            div(
              width.px(175),
              FieldL(
                label = Option("Advisor last name"),
                requirement = FieldL.Requirement.Required
              )()
            ),
            div(
              tw.flexFill,
              FieldL(
                label = Option("Advisor email"),
                requirement = FieldL.Requirement.Required
              )()
            )
          )
        }
      },
      children <-- membersSignal.splitByIndex { (index, _, memberSignal) =>
        div(
          tw.mb8,
          tw.flex.gapX8,
          div(
            width.px(175),
            FieldL(
              label = None,
              requirement = FieldL.Requirement.Required,
              validation = memberSignal.map(_.firstName.trim.isEmpty).combineWith(clickedAddButtonSignal).mapN {
                (isEmpty, clickedAddButton) =>
                  if (isEmpty && clickedAddButton) {
                    FieldL.Validation.Invalid("Advisor first name is required")
                  } else {
                    FieldL.Validation.None
                  }
              }
            )(
              TextBoxL(
                placeholder = "e.g. John",
                value = memberSignal.map(_.firstName),
                onChange = Observer[String] { firstName =>
                  membersVar.update { members =>
                    members.lift(index).fold(members) { member =>
                      members.updated(index, member.copy(firstName = firstName))
                    }
                  }
                }
              )()
            )
          ),
          div(
            width.px(175),
            FieldL(
              label = None,
              requirement = FieldL.Requirement.Required,
              validation = memberSignal.map(_.lastName.trim.isEmpty).combineWith(clickedAddButtonSignal).mapN {
                (isEmpty, clickedAddButton) =>
                  if (isEmpty && clickedAddButton) {
                    FieldL.Validation.Invalid("Advisor last name is required")
                  } else {
                    FieldL.Validation.None
                  }
              }
            )(
              TextBoxL(
                placeholder = "e.g. Smith",
                value = memberSignal.map(_.lastName),
                onChange = Observer[String] { lastName =>
                  membersVar.update { members =>
                    members.lift(index).fold(members) { member =>
                      members.updated(index, member.copy(lastName = lastName))
                    }
                  }
                }
              )()
            )
          ),
          div(
            tw.flexFill,
            FieldL(
              label = None,
              requirement = FieldL.Requirement.Required,
              validation = memberSignal
                .map(_.email)
                .combineWith(membersSignal, areAllEmailHasSameDomainSignal, clickedAddButtonSignal)
                .map { (email, members, areAllEmailHasSameDomain, clickedAddButton) =>
                  if (clickedAddButton && email.trim.isEmpty) {
                    FieldL.Validation.Invalid("Email is required")
                  } else if (clickedAddButton && !EmailAddress.isValid(email.trim)) {
                    FieldL.Validation.Invalid("Invalid email address")
                  } else if (clickedAddButton && members.count(_.email.trim == email.trim) > 1) {
                    FieldL.Validation.Invalid("This email is already used by another advisor")
                  } else if (clickedAddButton && !areAllEmailHasSameDomain) {
                    FieldL.Validation.Invalid("All invited emails must have the same domain")
                  } else {
                    FieldL.Validation.None
                  }
                }
            )(
              TextBoxL(
                placeholder = "e.g. <EMAIL>",
                value = memberSignal.map(_.email),
                onChange = Observer[String] { email =>
                  membersVar.update { members =>
                    members.lift(index).fold(members) { member =>
                      members.updated(index, member.copy(email = email))
                    }
                  }
                }
              )()
            )
          ),
          child(
            ButtonL(
              style = ButtonL.Style.Minimal(icon = Option(Icon.Glyph.Cross)),
              onClick = Observer { _ =>
                membersVar.update(_.patch(index, Nil, 1))
              }
            )()
          ) <-- membersVar.signal.map(_.size > 1)
        )
      }
    )
  }

  private def cleanUpState(): Unit = {
    Var.set(
      groupNameVar -> "",
      membersVar -> Seq(InviteAdvisor()),
      clickedAddButtonVar -> false,
      isCreatingAdvisorGroupVar -> false
    )
  }

  def apply(): HtmlElement = {
    ModalL(
      size = ModalL.Size(width = ModalL.Width.Px720),
      renderTitle = _ => "Add advisor entity",
      renderTarget = renderTarget,
      renderContent = onClose => {
        div(
          onUnmountCallback { _ =>
            cleanUpState()
          },
          ModalBodyL(
            div(
              tw.flex.flexCol,
              // Field
              div(
                tw.mb16,
                FieldL(
                  label = Option("Advisor entity"),
                  requirement = FieldL.Requirement.Required,
                  validation = groupNameSignal.combineWith(existingGroupNameSignal, clickedAddButtonVar).map {
                    case (groupName, existingGroupName, clickedAddButton) =>
                      if (clickedAddButton && groupName.trim.isEmpty) {
                        FieldL.Validation.Invalid("Advisor entity name is required")
                      } else if (existingGroupName.contains(groupName)) {
                        FieldL.Validation.Invalid("An advisor entity with this name already exists")
                      } else {
                        FieldL.Validation.None
                      }
                  }
                )(
                  TextBoxL(
                    isAutoFocus = true,
                    value = groupNameSignal,
                    onChange = groupNameVar.writer,
                    placeholder = "Advisor entity name"
                  )()
                )
              ),
              // Divider
              div(
                tw.mb16,
                DividerL()()
              ),
              // List of members
              renderMembers(),
              div(
                tw.py8,
                ButtonL(
                  style = ButtonL.Style.Text(
                    color = ButtonL.Color.Primary,
                    icon = Option(Icon.Glyph.Plus)
                  ),
                  onClick = Observer { _ =>
                    membersVar.update(_ :+ InviteAdvisor())
                  }
                )("Add advisor")
              )
            )
          ),
          ModalFooterWCancelL(
            cancel = onClose
          )(
            ButtonL(
              isDisabled = areValidMembersSignal.combineWith(isValidNameSignal, clickedAddButtonSignal).map {
                case (areValidMembers, isValidName, clickedAddButton) =>
                  !isValidName || (clickedAddButton && !areValidMembers)
              },
              style = ButtonL.Style.Full(
                color = ButtonL.Color.Primary,
                isBusy = isCreatingAdvisorGroupSignal
              ),
              onClick = Observer { _ =>
                clickedAddButtonVar.set(true)
                addGroupEvent.emit(())
              }
            )("Invite advisor entity")
          ),
          addGroupEvent.events
            .sample(groupNameSignal, membersSignal, isValidSignal)
            .filter(_._3)
            .flatMapSwitch { (groupName, members, _) =>
              handleAddAdvisorGroup(
                fundSubId = fundSubId,
                name = groupName,
                invitedAdvisors = members,
                onClose = onClose
              )
            } --> Observer.empty
        )
      }
    )().amend(
      onUnmountCallback { _ =>
        Var.set(
          clickedAddButtonVar -> false
        )
      }
    )
  }

  private def handleAddAdvisorGroup(
    fundSubId: FundSubId,
    name: String,
    invitedAdvisors: Seq[InviteAdvisor],
    onClose: Observer[Unit]
  ) = {
    AirStreamUtils.taskToStream(
      for {
        _ <- ZIO.succeed(isCreatingAdvisorGroupVar.set(true))
        _ <- FundSubRiaEndpointClient
          .createRiaFundGroup(
            CreateRiaFundGroupParams(
              fundSubId = fundSubId,
              name = name,
              inviteAdvisors = invitedAdvisors
            )
          )
          .map(
            _.fold(
              _ => {
                isCreatingAdvisorGroupVar.set(false)
                Toast.error("Failed to create advisor entity")
              },
              _ => {
                isCreatingAdvisorGroupVar.set(false)
                onClose.onNext(())
                onDoneAddAdvisorGroup.onNext(())
                Toast.success("Advisor entity created")
              }
            )
          )
      } yield ()
    )
  }

}
