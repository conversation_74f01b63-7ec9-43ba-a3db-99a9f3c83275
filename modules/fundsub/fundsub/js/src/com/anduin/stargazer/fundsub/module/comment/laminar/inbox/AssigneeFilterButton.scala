// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.inbox

import com.raquo.airstream.core.Signal
import com.raquo.laminar.api.L.*
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.components.portal.PortalPosition
import anduin.id.fundsub.FundSubId
import anduin.model.common.user.UserId
import anduin.model.id.TeamId
import anduin.stargazer.service.formcomment.FormCommentCommons.MentionInfo.{MentionedGroupInfo, MentionedUserInfo}
import anduin.stargazer.service.formcomment.FormCommentCommons.MentionInfo
import design.anduin.components.avatar.InitialAvatar
import design.anduin.components.avatar.laminar.InitialAvatarL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.menu.laminar.{MenuDividerL, MenuItemL, MenuL}
import com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent.FetchAssigneeListL

private[inbox] case class AssigneeFilterButton(
  fundId: FundSubId,
  assigneeSignal: Signal[Option[Either[UserId, TeamId]]],
  skipActorGroupSignal: Signal[Boolean],
  onAssigneeUpdate: Observer[Option[Either[UserId, TeamId]]]
) {

  private val AllAssigneeItem = MentionedGroupInfo(
    teamIdOpt = None,
    numberOfMembers = None,
    members = None,
    groupName = ""
  )

  private val selectedItemIdSignal = assigneeSignal.distinct
    .map { assigneeOpt =>
      assigneeOpt.map(_.fold(_.idString, _.idString)).getOrElse("")
    }

  private def renderAvatar(participant: MentionInfo) = {
    participant match {
      case MentionedUserInfo(_, _, _, _) =>
        InitialAvatarL(
          id = participant.toIdString,
          initials = Val(
            participant.getDisplayName
              .split(" ")
              .take(2)
              .map(_.take(1))
              .mkString
          ),
          size = InitialAvatar.Size.Px24
        )()
      case MentionedGroupInfo(_, _, _, _) =>
        div(
          tw.wPx24.hPx24.flex.itemsCenter.justifyCenter,
          IconL(
            name = Val(Icon.Glyph.UserGroupLarge),
            size = Icon.Size.Px16
          )()
        )
    }
  }

  private def renderMenuItem(
    isSelected: Boolean,
    participant: MentionInfo,
    onClick: Observer[Unit]
  ) = {
    val menuIcon = Option.when(!isSelected)(Icon.Glyph.Blank)

    MenuItemL(
      isSelected = isSelected,
      onClick = onClick,
      icon = menuIcon
    )(
      div(
        tw.flex.itemsCenter,
        renderAvatar(participant),
        div(
          tw.pl8,
          if (participant.toIdString.equals("")) {
            "All assignees"
          } else {
            participant.getDisplayName
          }
        )
      )
    )
  }

  private def renderPopoverContent(
    participantsSignal: Signal[Seq[MentionInfo]],
    onClose: Observer[Unit]
  ) = {
    div(
      child <-- participantsSignal
        .combineWith(selectedItemIdSignal)
        .distinct
        .map { case (participants, selectedAssigneeId) =>
          MenuL(
            participants
              .flatMap { participant =>
                val shouldAddDividerOnTop = participant.toIdString != "" && (participant match {
                  case _: MentionedGroupInfo => true
                  case _                     => false
                })
                val isSelected = selectedAssigneeId.equals(participant.toIdString)
                val menuItem = renderMenuItem(
                  isSelected = isSelected,
                  participant = participant,
                  onClick = Observer[Unit] { _ =>
                    if (participant.toIdString.equals("")) { // first item (un-assign)
                      onAssigneeUpdate.onNext(None)
                    } else {
                      participant match {
                        case MentionInfo.MentionedGroupInfo(teamIdOpt, _, _, _) =>
                          teamIdOpt.foreach { teamId =>
                            onAssigneeUpdate.onNext(Some(Right(teamId)))
                          }
                        case MentionInfo.MentionedUserInfo(userId, _, _, _) =>
                          onAssigneeUpdate.onNext(Some(Left(userId)))
                      }
                    }
                    onClose.onNext(())
                  }
                )

                (if (shouldAddDividerOnTop) Seq(MenuDividerL()) else Seq.empty) ++
                  Seq(menuItem)
              }
          )
        }
    )
  }

  private def renderPopoverTarget(
    isOpenSignal: Signal[Boolean],
    onOpen: Observer[Unit]
  ) = {
    div(
      ComponentUtils.testIdL("AssigneeFilterButton"),
      child <-- selectedItemIdSignal.map { selectedAssigneeId =>
        val isUsingFilter = selectedAssigneeId != ""
        ButtonL(
          style = ButtonL.Style.Ghost(
            icon = Option(Icon.Glyph.UserGroup),
            color = if (isUsingFilter) ButtonL.Color.Primary else ButtonL.Color.Gray9,
            isSelected = isOpenSignal
          ),
          onClick = onOpen.contramap(_ => ())
        )().amend(
          isUsingFilter.cls(tw.bgPrimary2)
        )
      }
    )
  }

  def apply(): HtmlElement = {
    FetchAssigneeListL(
      fundSubId = fundId,
      skipActorGroupSignal = skipActorGroupSignal,
      renderChildren = renderProps => {
        val assigneeListSignal = renderProps.dataSignal.map(_.assigneeList)
        val participantsSignal = assigneeListSignal
          .map(
            _.flatMap(groupInfo =>
              (if (groupInfo.isGPGroup) Seq(groupInfo) else Seq.empty)
                ++ groupInfo.members.getOrElse(Seq.empty)
            )
          )
          .map(pList => Seq(AllAssigneeItem) ++ pList)
        div(
          child.maybe <-- assigneeListSignal.map(_.nonEmpty).distinct.map { shouldRender =>
            // corner case with accessibility, there can be no other groups.
            Option.when(shouldRender) {
              PopoverL(
                position = PortalPosition.BottomRight,
                renderContent = onClose =>
                  renderPopoverContent(
                    participantsSignal = participantsSignal,
                    onClose = onClose
                  ),
                renderTarget = (onOpen, isOpenSignal) => {
                  TooltipL(
                    renderContent = _.amend("Filter by assignee"),
                    renderTarget = renderPopoverTarget(
                      isOpenSignal,
                      onOpen
                    )
                  )()
                }
              )()
            }
          }
        )
      }
    )()
  }

}
