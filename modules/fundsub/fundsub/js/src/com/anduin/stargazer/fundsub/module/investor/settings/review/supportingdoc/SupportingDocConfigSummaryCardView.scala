// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.review.supportingdoc

import com.raquo.laminar.api.L.*
import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.PortalPosition
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.components.well.Well
import design.anduin.components.well.react.WellR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import fundsub.review.supportingdoc.SupportingDocReviewConfigMode.*
import fundsub.review.supportingdoc.{InvestorGroupSupportingDocReviewConfig, SupportingDocGroupReviewConfig}
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.actionlogger.ActionEventLoggerJs
import anduin.fundsub.endpoint.investorgroup.InvestorGroupDetail
import anduin.fundsub.endpoint.review.FundSubSupportingDocReviewConfig
import anduin.fundsub.model.FundSubSharedClientModel.ParticipantInfo
import anduin.id.fundsub.group.FundSubInvestorGroupId
import anduin.model.common.user.UserId
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils
import com.anduin.stargazer.fundsub.module.investor.settings.review.model.AdminAndInvestorGroupInfo
import com.anduin.stargazer.fundsub.module.investor.settings.review.supportingdoc.SupportingDocGroupReviewSettingModal.UnassignedSupportingDocGroupName
import com.anduin.stargazer.fundsub.module.investor.settings.review.widget.InvestorGroupName
import com.anduin.stargazer.service.actionlogger.ActionEventEditAmlKycReviewSettingsButtonClickParams

private[settings] final case class SupportingDocConfigSummaryCardView(
  config: FundSubSupportingDocReviewConfig,
  adminAndInvestorGroupInfo: AdminAndInvestorGroupInfo,
  onEdit: Callback,
  reviewerUserInfos: Map[UserId, ParticipantInfo] = Map.empty
) {
  def apply(): VdomElement = SupportingDocConfigSummaryCardView.component(this)
}

private[settings] object SupportingDocConfigSummaryCardView {
  private type Props = SupportingDocConfigSummaryCardView

  private def render(props: Props) = {
    props.config.mode match {
      case SUPPORTING_DOC_REVIEW_CONFIG_MODE_ENABLED_WITH_SINGLE_CONFIG =>
        renderCardAndEditBtn(props, "Review workflow for all investors")(
          renderInvestorGroupConfig(
            props,
            props.config.allInvestorGroupConfigOpt,
            Some(props.adminAndInvestorGroupInfo.allInvestorGroupId)
          )
        )
      case SUPPORTING_DOC_REVIEW_CONFIG_MODE_ENABLED_WITH_MULTI_GROUP_CONFIG =>
        renderCardAndEditBtn(props, "Review workflow for investor groups")(
          renderMultipleModeConfig(props)
        )
      case _ => EmptyVdom
    }
  }

  private def renderMultipleModeConfig(props: Props) = {
    <.div(
      props.adminAndInvestorGroupInfo.investorGroups
        .sortBy(_.name)
        .collect {
          case investorGroupDetail: InvestorGroupDetail
              if investorGroupDetail.id != props.adminAndInvestorGroupInfo.allInvestorGroupId =>
            investorGroupDetail.id
        }
        .toVdomArray(
          using { investorGroupId =>
            val configOpt = props.config.investorGroupConfigMap.get(investorGroupId)
            val groupOpt = props.adminAndInvestorGroupInfo.investorGroups
              .find(_.id == investorGroupId)
            <.div(
              ^.key := investorGroupId.toString,
              tw.mt16.pb16.borderBottom.borderGray3,
              InvestorGroupName(
                groupOpt.map(_.id),
                groupOpt.map(_.name).getOrElse(""),
                groupOpt.map(_.membersCount).getOrElse(0)
              )(),
              <.div(
                renderInvestorGroupConfig(
                  props,
                  configOpt,
                  Some(investorGroupId)
                )
              )
            )
          }
        ),
      <.div(
        tw.mt12,
        InvestorGroupName(
          None,
          "Unassigned investors",
          props.adminAndInvestorGroupInfo.unassignedInvestorsCount
        )(),
        renderInvestorGroupConfig(
          props,
          props.config.unassignedInvestorGroupConfigOpt,
          None
        )
      )
    )
  }

  private def renderInvestorGroupConfig(
    props: Props,
    investorGroupConfigOpt: Option[InvestorGroupSupportingDocReviewConfig],
    investorGroupIdOpt: Option[FundSubInvestorGroupId]
  ) = {
    <.div(
      tw.spaceY16.mt16,
      props.config.supportingDocGroups.zipWithIndex.toVdomArray(
        using { case (docGroupInfo, index) =>
          renderDocGroupConfig(
            props = props,
            docGroupIndex = index + 1,
            docGroupName = docGroupInfo.groupName,
            docGroupConfigOpt = investorGroupConfigOpt.flatMap(
              _.docGroupConfigs.find(_.groupName == docGroupInfo.groupName)
            ),
            investorGroupIdOpt = investorGroupIdOpt
          )

        }
      ),
      renderDocGroupConfig(
        props = props,
        docGroupIndex = 0,
        docGroupName = UnassignedSupportingDocGroupName,
        docGroupConfigOpt = investorGroupConfigOpt.flatMap(_.unassignedDocGroupConfigOpt),
        investorGroupIdOpt = investorGroupIdOpt
      )
    )
  }

  private def renderDocGroupConfig(
    props: Props,
    docGroupIndex: Int,
    docGroupName: String,
    docGroupConfigOpt: Option[SupportingDocGroupReviewConfig],
    investorGroupIdOpt: Option[FundSubInvestorGroupId]
  ) = {
    val reviewers = docGroupConfigOpt.map(_.reviewers).getOrElse(Seq.empty)
    val reviewNote = docGroupConfigOpt.map(_.reviewNote).getOrElse("")
    <.div(
      ComponentUtils.testId("ConfigRow"),
      tw.flex,
      ^.key := docGroupIndex,
      if (props.config.supportingDocGroups.nonEmpty) {
        <.div(
          ComponentUtils.testId("DocGroup"),
          tw.flex.flexNone.wPc50.pr24,
          if (reviewNote.trim.isEmpty) {
            tw.pl24
          } else {
            PopoverR(
              renderTarget = (open, isOpen) =>
                <.div(
                  tw.mr4,
                  TooltipR(
                    renderTarget = Button(
                      testId = "ViewNote",
                      style = Button.Style.Full(
                        icon = Option(Icon.Glyph.FileText),
                        height = Button.Height.Fix20,
                        isSelected = isOpen
                      ),
                      onClick = open
                    )(),
                    renderContent = _("View notes")
                  )()
                ),
              renderContent = close =>
                <.div(
                  tw.p12,
                  ^.width := "224px",
                  <.div(
                    tw.flex.itemsCenter.mb8,
                    <.div(
                      tw.fontSemiBold.mrAuto,
                      "Note"
                    ),
                    Button(
                      testId = "Close",
                      style = Button.Style.Minimal(
                        icon = Some(Icon.Glyph.Cross),
                        height = Button.Height.Fix24
                      ),
                      onClick = close
                    )()
                  ),
                  <.div(
                    reviewNote.trim
                  )
                ),
              position = PortalPosition.RightTop
            )()
          },
          TooltipR(
            renderTarget = TagMod(
              tw.fontSemiBold.textGray7.truncate,
              docGroupName
            ),
            renderContent = _(docGroupName)
          )(),
          <.div(
            tw.fontSemiBold.textGray7,
            ":"
          )
        )
      } else {
        <.div(
          ComponentUtils.testId("GeneralConfig"),
          tw.mr12.fontSemiBold.textGray7,
          if (props.config.mode.isSupportingDocReviewConfigModeEnabledWithMultiGroupConfig) tw.ml24 else TagMod.empty,
          "Reviewers:"
        )
      },
      if (reviewers.isEmpty) {
        MissingReviewerAssignmentTag()
      } else {
        <.div(
          <.div(
            tw.flexFill,
            reviewers.zipWithIndex.toVdomArray(
              using { case (reviewer, index) =>
                val hasAccess = props.adminAndInvestorGroupInfo.isValidReviewerOfInvestorGroup(
                  reviewer,
                  investorGroupIdOpt,
                  isSubscriptionDocReview = false
                )
                val isLastReviewer = index + 1 == reviewers.size
                React.Fragment(
                  renderReviewerName(
                    props,
                    reviewer,
                    hasAccess = hasAccess
                  ),
                  if (isLastReviewer) {
                    EmptyVdom
                  } else {
                    <.div(tw.inlineBlock.mr4, ",")
                  }
                )
              }
            )
          )
        )
      }
    )
  }

  private def renderWarning(props: Props) = {
    if (props.adminAndInvestorGroupInfo.isLoading) {
      <.div(
        tw.my8.spaceY8,
        if (props.adminAndInvestorGroupInfo.isMissingSupportingDocReviewer(props.config)) {
          WellR(
            style = Well.Style.Danger()
          )(s"There are ${FundSubCopyUtils.getFlowTerm.standAloneTerm}s missing reviewer assignments. Edit the workflow to assign reviewers.")
        } else {
          EmptyVdom
        },
        if (props.adminAndInvestorGroupInfo.hasSupportingDocReviewerLosingAccess(props.config)) {
          WellR(
            style = Well.Style.Danger()
          )(
            s"""
               |There are ${FundSubCopyUtils.getFlowTerm.standAloneTerm}s without a reviewer because some users no longer have access to investor data.
               |Edit the workflow and reassign reviewers.
               |""".stripMargin
          )
        } else {
          EmptyVdom
        }
      )
    } else {
      EmptyVdom
    }
  }

  private def renderCardAndEditBtn(props: Props, title: String)(child: VdomElement) = {
    <.div(
      ComponentUtils.testId(SupportingDocConfigSummaryCardView),
      tw.py12.px16.borderAll.borderGray3.rounded6,
      <.div(
        tw.flex.itemsCenter,
        <.div(
          tw.textGray7.fontSemiBold,
          title
        ),
        <.div(
          tw.mlAuto,
          Button(
            testId = "EditBtn",
            style = Button.Style.Minimal(icon = Option(Icon.Glyph.Edit)),
            onClick = props.onEdit >> ActionEventLoggerJs.logEventCb(
              ActionEventEditAmlKycReviewSettingsButtonClickParams(
                props.adminAndInvestorGroupInfo.fundId
              )
            )
          )("Edit")
        )
      ),
      renderWarning(props),
      child
    )
  }

  private def renderReviewerName(props: Props, reviewer: UserId, hasAccess: Boolean) = {
    <.div(
      ^.key := reviewer.toString,
      tw.inlineBlock,
      if (hasAccess || props.adminAndInvestorGroupInfo.isLoading) {
        props.reviewerUserInfos
          .get(reviewer)
          .whenDefined(
            using { reviewerInfo =>
              <.div(ComponentUtils.testId("Reviewer"), reviewerInfo.userInfo.getDisplayName)
            }
          )
      } else {
        <.div(
          ComponentUtils.testId("ReviewerWithError"),
          tw.flex.itemsCenter.textDanger4,
          props.reviewerUserInfos
            .get(reviewer)
            .whenDefined(
              using { reviewerInfo =>
                <.div(reviewerInfo.userInfo.getDisplayName)
              }
            ),
          <.div(
            tw.ml4.cursorPointer,
            TooltipR(
              renderTarget = IconR(name = Icon.Glyph.Error)(),
              renderContent = _("Member no longer has access to investor data")
            )()
          )
        )
      }
    )
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
