// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.ria

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL, ModalL}
import design.anduin.components.switcher.laminar.SwitcherL
import design.anduin.components.toast.Toast
import design.anduin.components.well.Well
import design.anduin.components.well.laminar.WellL
import design.anduin.style.tw.*
import design.anduin.components.icon.Icon
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.ria.{ManageAbilityToCreateOrderParams, RiaFundGroup}
import anduin.id.fundsub.ria.FundSubRiaGroupId
import com.anduin.stargazer.fundsub.client.FundSubRiaEndpointClient
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils

private[ria] case class AllowCreateSubscriptionsModal(
  group: RiaFundGroup,
  renderTarget: Observer[Unit] => HtmlElement,
  onClose: Observer[Unit],
  onDoneDisableCreateSubscriptions: Observer[Unit]
) {

  private val updateEvent = new EventBus[Unit]
  private val isUpdatingVar = Var(false)
  private val isUpdatingSignal = isUpdatingVar.signal.distinct

  private val isAllowVar = Var(group.canCreateOrder)

  def apply(): HtmlElement = {
    ModalL(
      size = ModalL.Size(width = ModalL.Width.Px480),
      afterUserClose = onClose,
      renderContent = onClose => {
        div(
          ModalBodyL(
            div(
              tw.spaceY24,
              SwitcherL(
                isChecked = isAllowVar.signal,
                onChange = isAllowVar.writer
              )(
                span(
                  span("Enable "),
                  span(tw.fontSemiBold, group.name),
                  span(s" to create new ${FundSubCopyUtils.getFlowTerm.standAloneTerm}s in your fund.")
                )
              ),
              WellL(
                style = Well.Style.Gray(
                  icon = Option(Icon.Glyph.Info)
                )
              )(
                s"When disable, advisors from ${group.name} won't be able to create new subscriptions in your fund. Created subscription won't be affected."
              )
            )
          ),
          ModalFooterWCancelL(
            cancel = onClose
          )(
            ButtonL(
              style = ButtonL.Style.Full(
                color = ButtonL.Color.Primary,
                isBusy = isUpdatingSignal
              ),
              onClick = Observer { _ =>
                updateEvent.emit(())
              },
              isDisabled = isAllowVar.signal.map(_ == group.canCreateOrder)
            )("Save")
          ),
          updateEvent.events.sample(isAllowVar.signal).flatMapSwitch { isAllow =>
            handleDisableGroup(
              fundSubRiaGroupId = group.id,
              onClose = onClose,
              isAllow = isAllow
            )
          } --> Observer.empty
        )
      },
      renderTitle = _ => "Allow creating new subscriptions",
      renderTarget = renderTarget
    )()
  }

  private def handleDisableGroup(
    fundSubRiaGroupId: FundSubRiaGroupId,
    isAllow: Boolean,
    onClose: Observer[Unit]
  ) = {
    AirStreamUtils.taskToStream(
      for {
        _ <- ZIO.succeed(isUpdatingVar.set(true))
        _ <- FundSubRiaEndpointClient
          .manageAbilityToCreateOrder(
            ManageAbilityToCreateOrderParams(
              fundSubRiaGroupId = fundSubRiaGroupId,
              isAllow = isAllow
            )
          )
          .map(
            _.fold(
              _ => {
                isUpdatingVar.set(false)
                Toast.error("Failed to disable ability to create new subscription")
              },
              _ => {
                isUpdatingVar.set(false)
                onClose.onNext(())
                onDoneDisableCreateSubscriptions.onNext(())
                Toast.success(s"New subscription creation ${if (isAllow) "enabled" else "disabled"}")
              }
            )
          )
      } yield ()
    )
  }

}
