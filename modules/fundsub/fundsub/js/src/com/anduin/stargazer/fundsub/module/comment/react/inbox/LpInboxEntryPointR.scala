// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.react.inbox

import com.raquo.laminar.api.L.*
import design.anduin.components.wrapper.react.WrapperR
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.id.fundsub.{FundSubId, FundSubLpId}
import com.anduin.stargazer.fundsub.module.comment.laminar.inbox.LpInboxEntryPointL

private[fundsub] case class LpInboxEntryPointR(
  fundSubId: FundSubId,
  lpId: FundSubLpId,
  onClose: Callback
) {

  def apply(): VdomElement = LpInboxEntryPointR.component(this)
}

private[fundsub] object LpInboxEntryPointR {

  private type Props = LpInboxEntryPointR

  private def render(props: Props) = {
    WrapperR(
      LpInboxEntryPointL(
        fundSubId = props.fundSubId,
        lpId = props.lpId,
        onClose = Observer[Unit] { _ =>
          props.onClose.runNow()
        }
      )()
    )()
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
