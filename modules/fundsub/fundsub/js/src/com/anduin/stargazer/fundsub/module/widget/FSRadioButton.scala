// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.widget

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import design.anduin.components.radio.Radio
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

final case class FSRadioButton(
  isSelected: Boolean,
  onClick: Callback
) {
  def apply(children: VdomNode*): VdomElement = FSRadioButton.component(this)(children*)
}

object FSRadioButton {
  private type Props = FSRadioButton

  private def render(props: Props, children: PropsChildren) = {
    <.div(
      tw.borderAll.rounded8.border2,
      tw.flex.itemsCenter,
      if (props.isSelected) {
        tw.borderPrimary4
      } else {
        tw.borderGray3
      },
      tw.cursorPointer.p16,
      ^.onClick --> props.onClick,
      Radio(
        isChecked = props.isSelected,
        onChange = props.onClick
      )(),
      <.div(
        tw.ml12,
        children
      )
    )
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_PC(render)
    .build

}
