// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.review.supportingdoc

import design.anduin.components.icon.Icon
import design.anduin.components.tag.Tag
import design.anduin.components.tag.react.TagR
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[review] object MissingReviewerAssignmentTag {
  def apply(): VdomElement = component.apply()

  private def render = {
    TagR(
      label = "Missing reviewer assignment",
      color = Tag.Light.Danger,
      icon = Some(Icon.Glyph.Error)
    )()
  }

  private val component = ScalaComponent
    .builder[Unit](this.getClass.getSimpleName)
    .stateless
    .render_(render)
    .build

}
