// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.notify

import com.raquo.laminar.api.L.*

import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.id.issuetracker.IssueId
import anduin.stargazer.service.formcomment.FormCommentCommons.CommentThreadWithMetaData
import com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent.FetchCommentSettingL
import com.anduin.stargazer.fundsub.module.comment.models.CommentNotificationSetting

private[laminar] case class NotifyWizard(
  commentNotificationSettingSignal: Signal[Option[CommentNotificationSetting]],
  fundSubId: FundSubId,
  excludeChooseInvestorStep: Boolean,
  initialStep: NotifyWizard.Step,
  onClose: Observer[Unit]
) {

  private val stepVar: Var[NotifyWizard.Step] = Var(initialStep)
  private val entitySignal = commentNotificationSettingSignal.map(_.map(_.investorEntity))

  def apply(): HtmlElement = {
    FetchCommentSettingL(
      fundSubId = fundSubId,
      renderChildren = settingSignal => {
        val isCommentMentioningEnabledSignal = settingSignal.map(_.exists(_.isCommentMentioningEnabled)).distinct

        div(
          child <-- stepVar.signal.map {
            case NotifyWizard.Step.SelectInvestor =>
              SelectInvestorStepL(
                commentNotificationSettingSignal = commentNotificationSettingSignal,
                fundSubId = fundSubId,
                onClose = onClose,
                onGoToNextStep = Observer[FundSubLpId] { lpId =>
                  stepVar.set(
                    NotifyWizard.Step.SelectComment(
                      fundSubLpId = lpId,
                      selectedThreadIds = Set.empty
                    )
                  )
                }
              )()

            case s: NotifyWizard.Step.SelectComment =>
              SelectCommentsStepL(
                commentNotificationSettingSignal = commentNotificationSettingSignal,
                excludeChooseInvestorStep = excludeChooseInvestorStep,
                fundSubLpId = s.fundSubLpId,
                selectedThreadIds = s.selectedThreadIds,
                isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
                onClose = onClose,
                onGoToNextStep = Observer[SelectCommentsStepL.OnNextData] { data =>
                  stepVar.set(
                    NotifyWizard.Step.EditEmail(
                      fundSubLpId = s.fundSubLpId,
                      selectedThreadIds = data.selectedThreadIds,
                      selectedThreads = data.selectedThreads
                    )
                  )
                },
                onGoToPrevStep = Observer[Unit] { _ =>
                  stepVar.set(NotifyWizard.Step.SelectInvestor)
                }
              )()

            case s: NotifyWizard.Step.EditEmail =>
              ReviewStepL(
                entitySignal = entitySignal,
                fundSubLpId = s.fundSubLpId,
                selectedThreads = s.selectedThreads,
                onClose = onClose,
                onGoToPreviousStep = Observer[Unit] { _ =>
                  stepVar.set(
                    NotifyWizard.Step.SelectComment(
                      fundSubLpId = s.fundSubLpId,
                      selectedThreadIds = s.selectedThreadIds
                    )
                  )
                }
              )()
          }
        )
      }
    )()
  }

}

private[laminar] object NotifyWizard {

  sealed trait Step derives CanEqual

  object Step {
    object SelectInvestor extends Step

    case class SelectComment(
      fundSubLpId: FundSubLpId,
      selectedThreadIds: Set[IssueId] = Set.empty
    ) extends Step

    case class EditEmail(
      fundSubLpId: FundSubLpId,
      selectedThreadIds: Set[IssueId],
      selectedThreads: List[CommentThreadWithMetaData]
    ) extends Step

  }

}
