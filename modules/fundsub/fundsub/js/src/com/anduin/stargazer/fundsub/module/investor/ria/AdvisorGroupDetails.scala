// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.ria

import anduin.fundsub.endpoint.ria.{RiaFundGroup, RiaFundGroupState}
import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.divider.Divider
import design.anduin.components.divider.laminar.DividerL
import design.anduin.components.icon.Icon
import design.anduin.components.menu.laminar.{MenuDividerL, MenuItemL, MenuL}
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.tag.Tag
import design.anduin.components.tag.laminar.TagL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*

import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils

case class AdvisorGroupDetails(
  group: RiaFundGroup,
  groupsSignal: Signal[Seq[RiaFundGroup]],
  onRefetchGroup: Observer[Unit],
  onRefetchAllGroup: Observer[Unit]
) {

  private val isDisabled = !group.canCreateOrder

  private val linkedRiaEntityOpt = group.state match {
    case _: RiaFundGroupState.RiaEntityNotLinked               => None
    case RiaFundGroupState.RiaEntityLinked(linkedRiaEntity, _) => Option(linkedRiaEntity)
  }

  private val shouldShowEmptyState = group.state match {
    case RiaFundGroupState.RiaEntityNotLinked(invitedAdvisors) => invitedAdvisors.isEmpty
    case _: RiaFundGroupState.RiaEntityLinked                  => false
  }

  private val isDisableAbilityToRemove = group.numberOfMembers > 0 || group.numberOfOrders > 0

  def apply(): HtmlElement = {
    div(
      // Header
      div(
        tw.flex.itemsCenter.mb4,
        TooltipL(
          renderTarget = div(
            tw.flex.itemsCenter,
            h2(
              tw.heading2.mr8.textGray8,
              group.name
            ),
            when(isDisabled) {
              div(
                tw.mr8,
                TagL(
                  label = Val("Disabled"),
                  color = Val(Tag.Light.Warning)
                )()
              )
            }
          ),
          renderContent = _.amend("This advisor entity is unable to create new subscriptions"),
          isDisabled = Val(!isDisabled)
        )(),
        renderActionButton()
      ),
      renderGroupInfo(),
      if (shouldShowEmptyState) {
        renderEmptyState()
      } else {
        renderAdvisorTable()
      }
    )
  }

  private def renderAdvisorTable() = {
    div(
      div(
        tw.mb16.flex.spaceX8,
        InviteAdvisorMembersModal(
          group = group,
          renderTarget = onOpen => {
            ButtonL(
              style = ButtonL.Style.Full(icon = Option(Icon.Glyph.UserAdd)),
              onClick = onOpen.contramap(_ => ()),
              isDisabled = Val(group.isLinked)
            )("Invite advisors")
          },
          onDoneAddAdvisorMembers = onRefetchGroup,
          existingGroupEmailAddresses = group.state match {
            case RiaFundGroupState.RiaEntityNotLinked(invitedAdvisors) =>
              invitedAdvisors.map(_.info.userInfo.emailAddressStr)
            case _: RiaFundGroupState.RiaEntityLinked => Seq.empty
          }
        )(),
        Option.when(group.isLinked) {
          PopoverL(
            position = PortalPosition.BottomLeft,
            renderTarget = (onOpen, _) => {
              TooltipL(
                renderTarget = ButtonL(
                  style = ButtonL.Style.Full(
                    icon = Option(Icon.Glyph.Question),
                    height = ButtonL.Height.Fix32
                  ),
                  onClick = onOpen.contramap(_ => ())
                )(),
                renderContent = _.amend("Why can't I invite more advisors?")
              )()
            },
            renderContent = _ =>
              div(
                width.px := 400,
                tw.spaceY8.p8,
                div(
                  tw.fontSemiBold,
                  "Invitation are now accessible to the advisor entity only"
                ),
                div(
                  "All invitations to this investor entity are now managed by the advisor entity admin."
                ),
                div(
                  "Please contact the advisor entity admin to invite new investors to this advisor entity."
                )
              )
          )()
        }
      ),
      AdvisorMembersTable(
        group = group,
        onRefetch = onRefetchGroup
      )()
    )
  }

  private def renderEmptyState() = {
    EmptyAdvisorMembers(
      group = group,
      onDoneAddAdvisorMembers = onRefetchGroup
    )()
  }

  private def renderGroupInfo() = {
    div(
      tw.mb16,
      tw.flex.itemsCenter.gapX20,
      div(
        tw.spaceY4,
        div(
          tw.textGray7,
          "Owned by"
        ),
        div(
          linkedRiaEntityOpt.fold("--")(_.name)
        )
      ),
      DividerL(direction = Divider.Direction.Vertical)(),
      div(
        tw.spaceY4,
        div(
          tw.textGray7,
          "Advisors"
        ),
        div(
          linkedRiaEntityOpt.fold("--")(_.advisors.size.toString)
        )
      ),
      DividerL(Divider.Direction.Vertical)(),
      div(
        tw.spaceY4,
        div(
          tw.textGray7,
          "Subscriptions"
        ),
        div(
          linkedRiaEntityOpt.fold("--")(_.orders.size.toString)
        )
      )
    )
  }

  private def renderActionButton() = {
    div(
      tw.mlAuto,
      PopoverL(
        position = PortalPosition.BottomRight,
        renderContent = onClosePopover =>
          div(
            MenuL(
              Seq(
                RenameAdvisorGroupModal(
                  group = group,
                  existingGroupNameSignal = groupsSignal.map(_.map(_.name)),
                  renderTarget = onOpen => {
                    MenuItemL(
                      icon = Option(Icon.Glyph.Edit),
                      onClick = onOpen.contramap(_ => ())
                    )("Edit advisor entity name")
                  },
                  onClose = onClosePopover,
                  onDoneRenameGroup = onRefetchGroup
                )(),
                MenuDividerL(),
                AllowCreateSubscriptionsModal(
                  group = group,
                  renderTarget = onOpen => {
                    MenuItemL(
                      icon = Option(Icon.Glyph.Cog),
                      onClick = onOpen
                    )(s"Enable new ${FundSubCopyUtils.getFlowTerm.standAloneTerm} creation")
                  },
                  onClose = onClosePopover,
                  onDoneDisableCreateSubscriptions = onRefetchGroup
                )(),
                RemoveAdvisorGroupConfirmModal(
                  group = group,
                  renderTarget = onOpen => {
                    TooltipL(
                      isDisabled = Val(!isDisableAbilityToRemove),
                      position = PortalPosition.RightCenter,
                      renderTarget = MenuItemL(
                        color = MenuItemL.ColorDanger,
                        icon = Option(Icon.Glyph.Cross),
                        onClick = onOpen,
                        isDisabled = isDisableAbilityToRemove
                      )(s"Remove advisor entity"),
                      renderContent = _.amend("You can only remove advisor entities with no advisors and orders")
                    )()
                  },
                  onClose = onClosePopover,
                  onDoneRemoveAdvisorGroup = onRefetchAllGroup
                )()
              )
            )
          ),
        renderTarget = (onOpen, isOpen) => {
          ButtonL(
            style = ButtonL.Style.Full(icon = Option(Icon.Glyph.CaretDown), isSelected = isOpen),
            onClick = onOpen.contramap(_ => ())
          )("Actions")
        }
      )()
    )
  }

}
