// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.models

private[fundsub] sealed trait CommentTypeFilter derives CanEqual {
  val displayName: String
}

private[fundsub] object CommentTypeFilter {

  case object AllComments extends CommentTypeFilter {
    override val displayName = "Show all"
  }

  case object InternalOnly extends CommentTypeFilter {
    override val displayName = "Only internal comments"
  }

  case object SharedOnly extends CommentTypeFilter {
    override val displayName = "Only shared comments"
  }

}
