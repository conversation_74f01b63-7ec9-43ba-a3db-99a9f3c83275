// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.inbox

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.modal.laminar.ModalFooterWCancelL
import design.anduin.components.switcher.laminar.SwitcherL
import design.anduin.style.tw.*
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.lp.{
  GetFormCommentDigestEmailExceptionInfoParams,
  LpSimpleInfo,
  UpdateFormCommentDigestEmailExceptionLpsParams
}
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.scalajs.pluralize.Pluralize
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils

private[inbox] case class ManageDigestEmailExceptionModalL(
  fundSubId: FundSubId,
  suppressSendingFormComment: Boolean,
  closeModal: Observer[Unit]
) {
  private val exceptionLpsVar: Var[Seq[LpSimpleInfo]] = Var(Seq.empty)
  private val updatedLpsVar: Var[Set[FundSubLpId]] = Var(Set.empty)
  private val isUpdatingVar: Var[Boolean] = Var(false)

  private val fetchExceptionLpsInfoEventBus = new EventBus[Unit]

  private def fetchExceptionLpsInfo() = {
    AirStreamUtils.taskToStreamDEPRECATED {
      FundSubEndpointClient
        .getFormCommentDigestEmailExceptionInfo(
          GetFormCommentDigestEmailExceptionInfoParams(fundSubId)
        )
        .map(
          _.fold(
            _ => Seq[LpSimpleInfo](),
            resp => resp.exceptionLps
          )
        )
    }
  }

  private def updateFormCommentDigestEmailException(updatedLps: Set[FundSubLpId]) = {
    if (updatedLps.nonEmpty) {
      ZIOUtils.runAsync {
        for {
          _ <- ZIO.succeed(isUpdatingVar.set(true))
          _ <- FundSubEndpointClient
            .updateFormCommentDigestEmailExceptionLps(
              UpdateFormCommentDigestEmailExceptionLpsParams(
                fundSubId = fundSubId,
                removedLps = updatedLps.toSeq
              )
            )
          _ <- ZIO.succeed(closeModal.onNext(()))
        } yield ()
      }
    } else {
      closeModal.onNext(())
    }
  }

  def apply(): HtmlElement = {
    div(
      fetchExceptionLpsInfoEventBus.events.flatMapSwitch(_ => fetchExceptionLpsInfo()) --> exceptionLpsVar.writer,
      div(
        tw.pt20.px20,
        div(
          tw.px8,
          div(
            tw.pb24,
            renderDescription(suppressSendingFormComment = suppressSendingFormComment),
            renderExceptionTable()
          ),
          ModalFooterWCancelL(
            cancel = Observer { _ => closeModal.onNext(()) },
            isCancelDisabled = isUpdatingVar.signal
          )(
            div(
              child <-- updatedLpsVar.signal.map { updatedLps =>
                ButtonL(
                  style = ButtonL.Style.Full(
                    color = ButtonL.Color.Primary,
                    isBusy = isUpdatingVar.signal
                  ),
                  onClick = Observer { _ => updateFormCommentDigestEmailException(updatedLps) }
                )("Update")
              }
            )
          )
        )
      ),
      onMountCallback(_ => fetchExceptionLpsInfoEventBus.emit(()))
    )
  }

  private def renderDescription(suppressSendingFormComment: Boolean) = {
    child <-- exceptionLpsVar.signal
      .map(_.size)
      .distinct
      .map(countExceptions =>
        div(
          tw.mb20,
          "Automatic notifications are turned ",
          strong(if (suppressSendingFormComment) "ON" else "OFF"),
          " for " + Pluralize("this", count = countExceptions) + " " + Pluralize(
            "investor",
            count = countExceptions
          ) + ". You can turn them back ",
          strong(if (suppressSendingFormComment) "OFF" else "ON"),
          s" below or via their ${FundSubCopyUtils.getFlowTerm.termInFormContext}s."
        )
      )
  }

  private def renderRow(lpInfoSignal: Signal[LpSimpleInfo]) = {
    div(
      child <-- lpInfoSignal.map(lpInfo =>
        div(
          tw.flex.py8,
          tw.borderBottom.border1.borderGray3,
          div(tw.flex1.py2.textGray8, lpInfo.displayName),
          div(
            tw.flex1,
            child <-- updatedLpsVar.signal.map { updatedLps =>
              SwitcherL(
                isChecked = Val(if (suppressSendingFormComment) {
                  !updatedLps.contains(lpInfo.fundSubLpId)
                } else {
                  updatedLps.contains(lpInfo.fundSubLpId)
                }),
                onChange = Observer[Boolean] { status =>
                  val newUpdatedLps = if (suppressSendingFormComment != status) {
                    updatedLps + lpInfo.fundSubLpId
                  } else {
                    updatedLps - lpInfo.fundSubLpId
                  }
                  updatedLpsVar.set(newUpdatedLps)
                }
              )()
            }
          )
        )
      )
    )
  }

  private def renderExceptionTable() = {
    div(
      div(
        tw.flex.py8.textGray7,
        tw.borderBottom.border1.borderGray3,
        div(tw.flex1.py2, "Investors"),
        div(tw.flex1, "Notifications")
      ),
      children <-- exceptionLpsVar.signal.split(_.fundSubLpId) { (_, _, lpInfoSignal) =>
        renderRow(lpInfoSignal)
      }
    )
  }

}
