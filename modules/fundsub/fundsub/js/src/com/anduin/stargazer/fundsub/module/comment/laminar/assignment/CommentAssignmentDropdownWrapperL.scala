// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.assignment

import com.raquo.laminar.api.L.*

import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.protobuf.fundsub.comment.TopicData
import com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent.{
  FetchAssignableListL,
  FetchCommentAnchorPointAssigneeL
}
import design.anduin.style.tw.*

private[comment] case class CommentAssignmentDropdownWrapperL(
  fundSubId: FundSubId,
  lpIdSignal: Signal[FundSubLpId],
  topicDataSignal: Signal[TopicData],
  onAssign: Observer[Unit]
) {

  def apply(): HtmlElement = {
    FetchCommentAnchorPointAssigneeL(
      fundSubId = fundSubId,
      lpIdSignal = lpIdSignal,
      topicDataSignal = topicDataSignal,
      renderChildren = commentAssigneeRenderProps =>
        div(
          child <-- lpIdSignal.map(_.parent).distinct.map { fundSubId =>
            FetchAssignableListL(
              lpIdSignal = lpIdSignal,
              fundSubId = fundSubId,
              renderChildren = commentAssignableListRenderProps => {
                val isFetchingSignal = commentAssigneeRenderProps.dataSignal
                  .map(_.isFetching) || commentAssignableListRenderProps.dataSignal.map(_.isFetching)
                val assigneeOptSignal = commentAssigneeRenderProps.dataSignal.map {
                  _.data.flatMap { assignee =>
                    assignee.assigneeIdOpt.map { assigneeId =>
                      CommentAssignmentDropdownL.AssigneeInfo(
                        displayName = assignee.assigneeName,
                        assigneeId = assigneeId
                      )
                    }
                  }
                }
                div(
                  child <-- isFetchingSignal.splitBoolean(
                    whenTrue = _ => CommentAssignmentDropdownSkeletonL()(),
                    whenFalse = _ =>
                      div(
                        child <-- assigneeOptSignal.splitOne(_.map(_.assigneeId)) { case (_, initialAssigneeOpt, _) =>
                          CommentAssignmentDropdownL(
                            lpIdSignal = lpIdSignal,
                            topicDataSignal = topicDataSignal,
                            fundGroupsSignal = commentAssignableListRenderProps.dataSignal.map { data =>
                              convertToFundGroups(data)
                            },
                            initialAssigneeOpt = initialAssigneeOpt,
                            onAssign = onAssign
                          )()
                        }
                      )
                  )
                )
              }
            )()
          }
        )
    )()
  }

  private def convertToFundGroups(assignableListData: FetchAssignableListL.Data) = {
    assignableListData.assignableList.flatMap { assignableItem =>
      assignableItem.teamIdOpt.map { groupId =>
        CommentAssignmentDropdownL.FundGroupsWithMembersToAssign(
          groupId = groupId,
          groupName = assignableItem.groupName,
          members = assignableItem.members.getOrElse(Seq.empty).map { member =>
            CommentAssignmentDropdownL.FundMembersToAssign(
              userId = member.userId,
              userFullName = member.fullName,
              emailAddress = member.emailAddress
            )
          }
        )
      }
    }
  }

}
