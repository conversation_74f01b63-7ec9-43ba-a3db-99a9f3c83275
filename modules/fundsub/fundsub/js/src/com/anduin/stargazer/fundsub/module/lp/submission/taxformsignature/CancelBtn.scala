// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.lp.submission.taxformsignature

import design.anduin.components.button.Button
import design.anduin.components.modal.{Modal, ModalBody, ModalFooterWCancel}
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.endpoint.supportingdoc.CancelSignatureOnTaxFormParams
import anduin.id.fundsub.FundSubSupportingDocId
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

private[taxformsignature] final case class CancelBtn(
  docId: FundSubSupportingDocId,
  onCancelled: Callback
) {
  def apply(): VdomElement = CancelBtn.component(this)
}

private[taxformsignature] object CancelBtn {
  private type Props = CancelBtn

  private final case class State(
    isCanceling: Boolean = false
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    private def onCancelRequest(props: Props)(onClose: Callback) = {
      val cb = ZIOUtils.toReactCallback {
        FundSubEndpointClient
          .cancelSignatureRequestOnTaxForm(
            CancelSignatureOnTaxFormParams(
              props.docId
            )
          )
          .map(
            _.fold(
              _ =>
                scope.modState(
                  _.copy(
                    isCanceling = false
                  ),
                  Toast.errorCallback("Failed to cancel signature request")
                ),
              _ =>
                scope.modState(
                  _.copy(
                    isCanceling = false
                  ),
                  onClose >> props.onCancelled >> Toast.successCallback("Signature request cancelled")
                )
            )
          )
      }
      scope.modState(
        _.copy(isCanceling = true),
        cb
      )
    }

    def render(props: Props, state: State): VdomElement = {
      Modal(
        testId = "CancelRequest",
        title = "Cancel request",
        renderTarget = openModal =>
          Button(
            style = Button.Style.Text(color = Button.Color.Danger),
            onClick = openModal,
            testId = "CancelRequest"
          )(<.span(tw.text11.leading16, "Cancel this request")),
        renderContent = closeModal =>
          React.Fragment(
            ModalBody()("This will cancel the signature request. Are you sure you want to continue?"),
            ModalFooterWCancel(cancel = closeModal)(
              Button(
                style = Button.Style.Full(color = Button.Color.Danger, isBusy = state.isCanceling),
                onClick = onCancelRequest(props)(closeModal)
              )("Cancel request")
            )
          )
      )()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
