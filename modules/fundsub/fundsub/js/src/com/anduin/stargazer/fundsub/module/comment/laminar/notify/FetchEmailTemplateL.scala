// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.notify

import com.raquo.laminar.api.L.*

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.lp.{GetEmailTemplateParams, UserEmailTemplate}
import anduin.id.fundsub.FundSubLpId
import anduin.protobuf.fundsub.FundSubEvent
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

private[notify] case class FetchEmailTemplateL(
  event: FundSubEvent,
  fundSubLpId: FundSubLpId,
  renderChildren: Option[UserEmailTemplate] => HtmlElement,
  onDidFetch: Observer[UserEmailTemplate]
) {

  private val fetchEmailTemplateEventBus = new EventBus[Option[FundSubLpId]]
  private val emailTemplateVar = Var(Option.empty[UserEmailTemplate])

  private def fetchRecipients(targetLpIdOpt: Option[FundSubLpId]) = {
    targetLpIdOpt
      .map { targetLpId =>
        AirStreamUtils.taskToStreamDEPRECATED {
          FundSubEndpointClient
            .getEmailTemplates(
              GetEmailTemplateParams(
                fundSubId = targetLpId.parent,
                fundSubEvents = Seq(event),
                lpIdOpt = Option(targetLpId)
              )
            )
            .map {
              _.fold(
                _ => Option.empty[UserEmailTemplate],
                _.templates.find(_.fundSubEvent == event)
              )
            }
        }
      }
      .getOrElse(EventStream.empty)
  }

  def apply(): HtmlElement = {
    div(
      fetchEmailTemplateEventBus.events.flatMapSwitch(fetchRecipients) --> Observer[Option[UserEmailTemplate]] { data =>
        onDidFetch.onNext(data.getOrElse(UserEmailTemplate(event)))
        emailTemplateVar.set(data)
      },
      child <-- emailTemplateVar.signal.map(renderChildren),
      onMountCallback { _ =>
        fetchEmailTemplateEventBus.emit(Option(fundSubLpId))
      }
    )
  }

}
