// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.link

import design.anduin.components.button.Button
import design.anduin.components.modal.{Modal, ModalBody, ModalFooterWCancel}
import design.anduin.components.portal.PortalUtils
import design.anduin.components.toast.Toast
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.endpoint.dashboard.CreateProtectedLinkParams
import anduin.id.fundsub.FundSubId
import anduin.utils.StateSnapshotWithModFn
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

private[investor] final case class CreateProtectedLinkModal(
  fundSubId: FundSubId,
  renderTarget: Callback => VdomNode,
  refreshLinkState: Callback,
  enterpriseLoginEnabledForFundsub: Boolean
) {

  def apply(): VdomElement = CreateProtectedLinkModal.component(this)
}

private[investor] object CreateProtectedLinkModal {

  private type Props = CreateProtectedLinkModal

  private final case class State(
    linkSettings: LinkSettings,
    isBusy: Boolean
  )

  private val defaultLinkSettings = {
    LinkSettings(
      password = LinkSettings.PasswordSetting(
        password = LinkSettings.PasswordSetting.Change(""),
        isEnabled = false,
        initialIsEnabled = false
      ),
      expiryDate = LinkSettings.ExpiryDateSetting(
        expiryDate = LinkSettings.ExpiryDateSetting.default,
        isEnabled = false
      ),
      isDisabled = false,
      whitelistedDomains = LinkSettings.WhitelistedDomainSetting(
        domains = Seq(),
        isEnabled = false
      ),
      isEnterpriseLoginEnabled = false
    )
  }

  private class Backend(scope: BackendScope[Props, State]) {

    private val linkSettingsStateSnapshot = {
      StateSnapshotWithModFn.withReuse
        .zoom[State, LinkSettings](_.linkSettings)(linkSettings => _.copy(linkSettings = linkSettings))
        .prepareVia(scope)
    }

    private def createLinkCb(onCloseModal: Callback): Callback = {
      for {
        props <- scope.props
        _ <- scope.modState(_.copy(isBusy = true))
        state <- scope.state
        _ <- ZIOUtils.toReactCallback {
          val link = state.linkSettings
          FundSubEndpointClient
            .createProtectedLink(
              CreateProtectedLinkParams(
                fundSubId = props.fundSubId,
                passwordOpt = link.password.password match {
                  case LinkSettings.PasswordSetting.Change(password) if link.password.isEnabled =>
                    Some(password)
                  case _ =>
                    None
                },
                expiryDate = Option.when(link.expiryDate.isEnabled)(link.expiryDate.expiryDate),
                whitelistedDomains = link.whitelistedDomains.domains.toSet,
                enableEnterpriseLogin = link.isEnterpriseLoginEnabled
              )
            )
            .map {
              _.fold(
                _ => Toast.errorCallback("Failed to create the link, please try again later"),
                _ => props.refreshLinkState >> onCloseModal
              )
            }
        }
        _ <- scope.modState(_.copy(isBusy = false))
      } yield ()
    }

    def renderModalContent(props: Props, state: State)(onClose: Callback): VdomElement = {
      React.Fragment(
        ModalBody()(
          FundSubProtectedLinkSettings(
            props.fundSubId,
            linkSettingsStateSnapshot(state),
            props.enterpriseLoginEnabledForFundsub
          )()
        ),
        ModalFooterWCancel(onClose)(
          Button(
            testId = "CreateBtn",
            style = Button.Style.Full(color = Button.Color.Primary),
            onClick = createLinkCb(onClose),
            isDisabled = !LinkSettings.isValid(state.linkSettings)
          )("Create")
        )
      )
    }

    def render(props: Props, state: State): VdomElement = {
      Modal(
        testId = "ShareableLink",
        title = "Shareable link",
        size = Modal.Size(Modal.Width.Px600),
        renderTarget = props.renderTarget,
        renderContent = renderModalContent(props, state),
        isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false))
      )()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State(defaultLinkSettings, isBusy = false))
    .renderBackend[Backend]
    .build

}
