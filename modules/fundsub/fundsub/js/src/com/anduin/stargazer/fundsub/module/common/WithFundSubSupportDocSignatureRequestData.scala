// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.common

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration

import io.circe.Codec
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.graphql.component.{FetchStrategy, GraphqlOptions, QueryComponent, QueryProps}
import anduin.id.fundsub.FundSubLpId
import anduin.rohan.operation.AnduinQuery
import com.anduin.stargazer.fundsub.module.model.FundSubClientModel.FundSubSupportingDocSignatureRequestData

private[module] final case class WithFundSubSupportDocSignatureRequestData(
  lpId: FundSubLpId
) {

  def apply(renderFn: WithFundSubSupportDocSignatureRequestData.RenderFn): VdomNode = {
    WithFundSubSupportDocSignatureRequestData.graphqlComponent(
      WithFundSubSupportDocSignatureRequestData.OuterProps(renderFn),
      WithFundSubSupportDocSignatureRequestData.Variables(lpId)
    )
  }

}

private[module] object WithFundSubSupportDocSignatureRequestData {

  final case class Variables(fundSubLpId: FundSubLpId) derives CanEqual

  object Variables {
    given Codec.AsObject[Variables] = deriveCodecWithDefaults
  }

  final case class Result(fundSubSupportingDocSignatureRequestData: Option[FundSubSupportingDocSignatureRequestData])
      derives CanEqual

  object Result {
    given Codec.AsObject[Result] = deriveCodecWithDefaults
  }

  final case class OuterProps(renderFn: RenderFn) derives CanEqual

  private type RenderFn = (Option[FundSubSupportingDocSignatureRequestData], Callback) => VdomNode

  private type Props = QueryProps[OuterProps, Variables, Result]

  private def render(props: Props) = {
    props.outerProps.renderFn(
      props.data.fundSubSupportingDocSignatureRequestData,
      props.refetch
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

  private val graphqlComponent = QueryComponent(
    component,
    AnduinQuery.FundSubSupportingDocSignatureRequestData,
    GraphqlOptions(FiniteDuration(1, TimeUnit.MINUTES)),
    initialFetchStrategy = FetchStrategy.ForcedFetch
  )

}
