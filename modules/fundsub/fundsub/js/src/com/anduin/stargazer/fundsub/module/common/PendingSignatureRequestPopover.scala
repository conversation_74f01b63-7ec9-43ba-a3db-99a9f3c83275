// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.common

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.modal.Modal
import design.anduin.components.portal.PortalUtils
import design.anduin.components.progress.react.{BlockIndicatorR, CircleIndicatorR}
import design.anduin.components.tag.TagColor
import design.anduin.components.tag.react.TagR
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.component.util.JsDateFormatterUtils
import anduin.component.util.JsDateFormatterUtils.JsDateFormat
import anduin.endpoint.signature.SignatureSharedModels.SignatureRecipientStatus
import anduin.fundsub.endpoint.dashboard.{SignatureRequestSigner, SubDocSignatureRequestInfo}
import anduin.fundsub.endpoint.subscriptiondoc.RemindSubscriptionDocSignatureRequestParams
import anduin.id.fundsub.FundSubLpId
import anduin.id.signature.SignatureRequestId
import anduin.model.common.user.UserId
import anduin.protobuf.fundsub.LpFlowType
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.signature.{MarkRequestCompleteModal, MarkRequestCompleteModalRestrictedFlow}

private[module] final case class PendingSignatureRequestPopover(
  lpId: FundSubLpId,
  signatureInfoOpt: Option[SubDocSignatureRequestInfo],
  closePopover: Callback,
  lpFlowType: LpFlowType
) {
  def apply(): VdomElement = PendingSignatureRequestPopover.component(this)
}

private[module] object PendingSignatureRequestPopover {

  private type Props = PendingSignatureRequestPopover

  private final case class State(
    reminding: Set[UserId] = Set.empty,
    isShowingMarkAsCompleteModal: Boolean = false
  )

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

  private class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      <.div(
        tw.p12,
        ^.width := "368px",
        props.signatureInfoOpt.fold(
          <.div(
            tw.hPx128.flex.itemsCenter.justifyCenter,
            BlockIndicatorR()()
          )
        ) { signatureInfo =>
          <.div(
            <.div(
              tw.fontSemiBold.mr12.breakAll,
              s"${signatureInfo.requesterName} sent a signature request to:"
            ),
            signatureInfo.signers.toVdomArray(
              using { signer =>
                <.div(
                  ^.key := signer.email,
                  tw.flex.flexCol.py12.borderBottom.borderGray3,
                  <.div(
                    <.div(signer.name),
                    <.div(
                      tw.textGray7.text11.leading16.breakAll,
                      signer.email
                    )
                  ),
                  signer.status match {
                    case SignatureRecipientStatus.Sent =>
                      // can only send reminder for flexible flow
                      signatureInfo.requestIdOpt.map { requestId =>
                        <.div(
                          tw.mt6,
                          remindButton(
                            state,
                            requestId,
                            signer
                          )
                        )
                      }

                    case SignatureRecipientStatus.Viewed =>
                      <.div(
                        tw.mt6.flex.itemsCenter,
                        TagR(
                          label = "Viewed",
                          icon = Some(Icon.Glyph.FileEye)
                        )(),
                        signatureInfo.requestIdOpt.map { requestId =>
                          <.div(
                            tw.ml8.pl8.borderLeft.borderGray3,
                            remindButton(
                              state,
                              requestId,
                              signer
                            )
                          )
                        }
                      )

                    case SignatureRecipientStatus.Signed =>
                      <.div(
                        tw.mt6,
                        TagR(
                          color = TagColor.Light.Success,
                          label = "Signed",
                          icon = Some(Icon.Glyph.Check)
                        )()
                      )
                  }
                )
              }
            ),
            signatureInfo.sentAt.map { sentAt =>
              <.div(
                tw.mt12.textGray7.text11.leading16,
                "Request sent on ",
                JsDateFormatterUtils.format(sentAt, JsDateFormat.MonthDateYearTime3)
              )
            },
            TagMod.when(signatureInfo.signers.size > 1) {
              <.div(
                tw.mt2.text11.leading16.flex,
                markAsCompleteButton(props, signatureInfo)
              )
            }
          )
        }
      )
    }

    private def remindButton(
      state: State,
      requestId: SignatureRequestId,
      signer: SignatureRequestSigner
    ): VdomElement = {
      if (state.reminding.contains(signer.userId)) {
        <.div(tw.textGray6, CircleIndicatorR()())
      } else {
        <.div(
          tw.flex.itemsCenter,
          <.div(tw.textPrimary4, IconR(Icon.Glyph.Envelope)()),
          <.div(
            tw.ml6.leading16.text11,
            Button(
              testId = "SendReminder",
              style = Button.Style.Text(),
              onClick = remind(requestId, signer.userId)
            )("Send reminder")
          )
        )
      }
    }

    private def remind(requestId: SignatureRequestId, userId: UserId): Callback = {
      for {
        _ <- scope.modState(state => state.copy(reminding = state.reminding + userId))
        props <- scope.props
        _ <- ZIOUtils.toReactCallback {
          for {
            either <- FundSubEndpointClient
              .remindSubscriptionDocSignatureRequest(
                RemindSubscriptionDocSignatureRequestParams(
                  lpId = props.lpId,
                  requestId = requestId,
                  signer = userId
                )
              )
          } yield {
            scope.modState(
              state => state.copy(reminding = state.reminding - userId),
              either.fold(
                _ => Toast.errorCallback("Unable to send reminder, please try again"),
                _ => Toast.successCallback("Reminder sent")
              )
            )
          }
        }
      } yield ()
    }

    private def markAsCompleteButton(
      props: Props,
      signatureInfo: SubDocSignatureRequestInfo
    ) = {
      val canMarkComplete = signatureInfo.signers.exists(_.status == SignatureRecipientStatus.Signed)
      Modal(
        testId = "MarkComplete",
        title = "Mark request complete",
        isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false)),
        renderContent = closeModal =>
          if (props.lpFlowType.isRestricted) {
            MarkRequestCompleteModalRestrictedFlow(
              lpId = props.lpId,
              onDoneMarkAsComplete = closeModal,
              close = closeModal
            )()
          } else {
            props.signatureInfoOpt
              .flatMap(_.requestIdOpt)
              .fold(EmptyVdom) { requestId =>
                MarkRequestCompleteModal(
                  lpId = props.lpId,
                  requestId = requestId,
                  onDoneMarkAsComplete =
                    props.closePopover, // closeModal will not work here due to the live update of signatureInfoOpt
                  close = closeModal
                )()
              }
          },
        afterUserClose = props.closePopover,
        renderTarget = onOpen =>
          TooltipR(
            renderTarget = Button(
              testId = "MarkComplete",
              style = Button.Style.Text(),
              onClick = onOpen,
              isDisabled = !canMarkComplete
            )("Mark as complete"),
            renderContent = _("At least one signer has to sign"),
            isDisabled = canMarkComplete
          )()
      )()
    }

  }

}
