// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.contact.modal

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.suggest.{MultiSuggest, Suggest}
import design.anduin.components.textbox.TextBox
import design.anduin.components.toast.Toast
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.contact.endpoint.ContactModel
import anduin.contact.metadata.FundSubContactMetaData
import anduin.fundsub.constants.Terms
import anduin.fundsub.endpoint.contact.{AddContactParams, ContactData, EditContactParams, FundSubContactInfo}
import anduin.id.contact.ContactId
import anduin.id.fundsub.FundSubId
import anduin.protobuf.fundsub.contact.FundSubContactGroup
import anduin.scalajs.pluralize.Pluralize
import anduin.utils.StringUtils
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

final case class AddOrEditContactModal(
  fundSubId: FundSubId,
  // None = add new contact, or edit existing contact otherwise.
  contactModelOpt: Option[ContactModel],
  allContactsInfo: FundSubContactInfo,
  onClose: Callback,
  reFetch: Callback
) {

  private lazy val isAddNew = contactModelOpt.isEmpty

  def apply(): VdomElement = AddOrEditContactModal.component(this)
}

object AddOrEditContactModal {

  private type Props = AddOrEditContactModal

  private final case class State(
    firstName: String = "",
    lastName: String = "",
    email: String = "",
    groups: Seq[FundSubContactGroup] = Seq.empty,
    busy: Boolean = false
  )

  private class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      <.div(
        tw.p20.pt16,
        <.div(
          tw.flex.mb16,
          <.div(
            ComponentUtils.testId(AddOrEditContactModal, "FirstName"),
            tw.flexFill.mr8,
            TextBox(
              value = state.firstName,
              onChange = firstName => scope.modState(_.copy(firstName = firstName)),
              placeholder = "First name",
              icon = Some(Icon.Glyph.UserSingle)
            )()
          ),
          <.div(
            ComponentUtils.testId(AddOrEditContactModal, "LastName"),
            tw.flexFill,
            TextBox(
              value = state.lastName,
              onChange = lastName => scope.modState(_.copy(lastName = lastName)),
              placeholder = "Last name"
            )()
          )
        ),
        <.div(
          ComponentUtils.testId(AddOrEditContactModal, "Email"),
          tw.flexFill,
          TextBox(
            value = state.email,
            onChange = email => scope.modState(_.copy(email = email)),
            placeholder = "Email",
            icon = Some(Icon.Glyph.Envelope)
          )()
        ),
        <.div(
          tw.flex.mt32.mb4,
          <.div(tw.fontSemiBold, "Add contact to investor entities"),
          <.div(tw.ml4.textGray6, "(optional)")
        ),
        <.div(ComponentUtils.testId(AddOrEditContactModal, "InvestorList"), renderInvestorInput(props, state)),
        <.div(tw.mt20, renderButtons(props, state))
      )
    }

    private def renderButtons(props: Props, state: State): VdomNode = {
      <.div(
        tw.flex.itemsCenter,
        <.div(tw.mlAuto.mr8, Button(onClick = props.onClose)("Cancel")),
        Button(
          style = Button.Style.Full(color = Button.Color.Primary, isBusy = state.busy),
          onClick = props.contactModelOpt.fold[Callback](
            onAddContact(props, state)
          ) { contactModel =>
            onEditContact(
              props,
              state,
              contactModel.contactId
            )
          },
          isDisabled = StringUtils.isEmptyOrWhitespace(state.firstName)
        )(if (props.isAddNew) "Add" else "Save")
      )
    }

    private def onEditContact(props: Props, state: State, contactId: ContactId): Callback = {
      scope.modState(
        _.copy(busy = true),
        ZIOUtils.toReactCallback(
          FundSubEndpointClient
            .editContact(
              EditContactParams(
                props.fundSubId,
                contactId,
                ContactData(
                  state.firstName,
                  state.lastName,
                  state.email,
                  state.groups.map(_.id)
                )
              )
            )
            .map(
              _.fold(
                _ => scope.modState(_.copy(busy = false), Toast.errorCallback("Unable to save contact, try again")),
                _ => Toast.successCallback("Contact saved") >> props.reFetch >> props.onClose
              )
            )
        )
      )
    }

    private def onAddContact(props: Props, state: State): Callback = {
      scope.modState(
        _.copy(busy = true),
        ZIOUtils.toReactCallback(
          FundSubEndpointClient
            .addContact(
              AddContactParams(
                props.fundSubId,
                Seq(
                  ContactData(
                    state.firstName,
                    state.lastName,
                    state.email,
                    state.groups.map(_.id)
                  )
                )
              )
            )
            .map(
              _.fold(
                _ => scope.modState(_.copy(busy = false), Toast.errorCallback("Unable to add contact, try again")),
                _ => Toast.successCallback("Contact added") >> props.reFetch >> props.onClose
              )
            )
        )
      )
    }

    private val InvestorSuggest = (new MultiSuggest[FundSubContactGroup])()

    private def renderInvestorInput(props: Props, state: State): VdomNode = {
      InvestorSuggest(
        value = state.groups.map(_.name),
        onChange = groupNames => {
          val validGroups = groupNames.flatMap(name => props.allContactsInfo.allGroups.find(_.name == name))
          scope.modState(_.copy(groups = validGroups))
        },
        options = Suggest.Options[FundSubContactGroup](
          items = props.allContactsInfo.allGroups.filterNot(state.groups.contains).map(Suggest.Option(_)),
          valueToString = _.name,
          renderItemBody = Some(group => {
            val count = props.allContactsInfo.groupMembership.getOrElse(group.value.id, 0)
            val metaData = if (count == 0) {
              "Empty"
            } else {
              Pluralize(
                "contact",
                count,
                inclusive = true
              )
            }
            <.div(
              tw.flex.itemsCenter,
              IconR(Icon.Glyph.UserGroupLarge)(),
              <.div(tw.ml8.mr4.fontSemiBold, group.value.name),
              <.div(s"($metaData)")
            )
          })
        ),
        textBox = MultiSuggest.TextBoxProps(s"Enter ${Terms.InvesteeEntityLabelLowerCase}")
      )()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps(props => {
      props.contactModelOpt.fold[State](State()) { contactModel =>
        val info = contactModel.contactInfo
        val groupIds = info.metaData.collect { case FundSubContactMetaData(id, _) =>
          id
        }.distinct
        val groups = groupIds.flatMap(id => props.allContactsInfo.allGroups.find(_.id == id))
        State(
          info.firstName,
          info.lastName,
          info.email,
          groups
        )
      }
    })
    .renderBackend[Backend]
    .build

}
