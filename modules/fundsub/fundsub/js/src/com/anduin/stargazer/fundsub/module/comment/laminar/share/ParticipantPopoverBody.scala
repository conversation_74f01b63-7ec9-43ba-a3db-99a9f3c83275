// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.share

import com.raquo.laminar.api.L.*
import design.anduin.components.avatar.InitialAvatar
import design.anduin.components.avatar.laminar.InitialAvatarL
import design.anduin.components.clipboard.laminar.CopyL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import anduin.stargazer.service.formcomment.FormCommentCommons.MentionInfo
import anduin.stargazer.service.formcomment.FormCommentCommons.MentionInfo.{MentionedGroupInfo, MentionedUserInfo}

private[share] case class ParticipantPopoverBody(
  participant: MentionInfo
) {

  def renderSingleParticipantPopoverBody(participantData: MentionedUserInfo) = {
    div(
      ComponentUtils.testIdL("MentionedUser"),
      tw.flex,
      div(
        tw.mt8,
        InitialAvatarL(
          id = participantData.emailAddress,
          initials = Val(participantData.getDisplayName.split(" ").take(2).mkString("")),
          size = InitialAvatar.Size.Px24
        )()
      ),
      div(
        tw.ml8,
        width.px(220),
        div(
          tw.fontBold.textGray8.text13.breakAll,
          participantData.getDisplayName
        ),
        div(
          tw.textGray7.text11.breakAll,
          participantData.role
        ),
        div(
          tw.textGray7.text11.breakAll,
          CopyL(
            content = Val(participantData.emailAddress),
            renderChildren = renderProps => {
              TooltipL(
                renderContent = _.amend("Click to copy"),
                renderTarget = {
                  span(
                    tw.cursorPointer,
                    onClick --> renderProps.copy.contramap(_ => ()),
                    participantData.emailAddress
                  )
                }
              )()
            }
          )()
        )
      )
    )
  }

  def renderGPGroupPopoverBody(participant: MentionedGroupInfo) = {
    val members = participant.members.getOrElse(Seq.empty)
    div(
      ComponentUtils.testIdL("MentionedGPGroup"),
      width.px(280),
      maxHeight.px(280),
      tw.pr8.overflowAuto,
      marginRight.px(-8),
      members.zipWithIndex.map { case (participantData, index) =>
        div(
          tw.flex.itemsCenter,
          (index > 0).cls(tw.mt8),
          InitialAvatarL(
            id = participantData.emailAddress,
            initials = Val(participantData.getDisplayName.split(" ").take(2).mkString("")),
            size = InitialAvatar.Size.Px20
          )().amend(tw.inlineBlock),
          div(
            tw.ml8.textGray7.overflowHidden.whitespaceNowrap.flex1,
            textOverflow.ellipsis,
            span(tw.pr4.fontMedium.textGray8, participantData.getDisplayName),
            span(tw.textGray7, "(" + participantData.emailAddress + ")")
          )
        )
      }
    )
  }

  def apply(): HtmlElement = {
    participant match {
      case mentionedGroup: MentionedGroupInfo => renderGPGroupPopoverBody(mentionedGroup)
      case mentionedUser: MentionedUserInfo   => renderSingleParticipantPopoverBody(mentionedUser)
    }
  }

}
