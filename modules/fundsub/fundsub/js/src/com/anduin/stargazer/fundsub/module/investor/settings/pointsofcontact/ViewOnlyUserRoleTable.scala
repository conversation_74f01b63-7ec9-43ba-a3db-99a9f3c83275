// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.pointsofcontact

import design.anduin.components.avatar.InitialAvatar
import design.anduin.components.avatar.react.InitialAvatarR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import design.anduin.table.react.TableR
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.id.fundsub.FundSubId
import com.anduin.stargazer.fundsub.module.investor.permission.shared.GroupLabel
import com.anduin.stargazer.fundsub.module.investor.settings.pointsofcontact.PointOfContactSetting.RowData

private[pointsofcontact] case class ViewOnlyUserRoleTable(
  fundSubId: FundSubId,
  rowsWithModifyCb: Seq[
    (PointOfContactSetting.RowData, PointOfContactSetting.RowData => Callback, Callback)
  ], // data, modRowData, deleteRow
  shouldScrollToLastRow: Boolean = false,
  onScrollToLastRow: Callback = Callback.empty
) {

  def apply(): VdomElement = {
    ViewOnlyUserRoleTable.component(this)
  }

}

private[pointsofcontact] object ViewOnlyUserRoleTable {

  private type Props = ViewOnlyUserRoleTable

  private object ColumnTitle {
    val Name = "Name"
    val Group = "Group"
    val EmailAddress = "Email address"
    val PhoneNumber = "Phone number"
  }

  private type RowData = PointOfContactSetting.RowData

  private val emptyCell = <.div(tw.textGray5.leading24, "-")

  private case class Backend(scope: BackendScope[Props, Unit]) {

    private val TableOfUsers = (new TableR[RowData])()

    private def renderName(rowData: RowData) = {
      rowData.userIdOpt.fold {
        <.div(
          tw.flex.flexRow.itemsCenter,
          <.div(
            tw.wFit,
            InitialAvatarR(
              id = rowData.name,
              initials = "",
              kind = InitialAvatar.Kind.User,
              size = InitialAvatar.Size.Px24
            )()
          ),
          <.div(
            tw.leading20.text13.textGray8.fontSemiBold.ml8,
            rowData.name
          )
        )
      } { _ =>
        <.div(
          tw.flex.flexRow.itemsCenter,
          <.div(
            tw.wFit,
            InitialAvatarR(
              id = rowData.name,
              initials = rowData.name,
              kind = InitialAvatar.Kind.User,
              size = InitialAvatar.Size.Px24
            )()
          ),
          <.div(
            tw.leading20.text13.textGray8.fontSemiBold.ml8,
            rowData.name
          )
        )
      }
    }

    private def renderGroup(rowData: RowData) = {
      rowData.userIdOpt.fold {
        <.div(tw.leading24, rowData.groupName)
      } { _ =>
        <.div(
          tw.flex.flexRow.itemsCenter.hPx24,
          GroupLabel(rowData.groupName, isCircleIcon = false)()
        )
      }
    }

    private def renderEmail(rowData: RowData) = {
      rowData.userIdOpt.fold {
        if (rowData.email.isEmpty) {
          emptyCell
        } else {
          <.div(tw.leading24.truncate, rowData.email)
        }
      } { _ =>
        <.div(
          tw.leading24.truncate,
          rowData.email
        )
      }
    }

    private def renderPhone(rowData: RowData) = {
      if (rowData.phone.isEmpty) {
        emptyCell
      } else {
        <.div(tw.leading24.truncate, rowData.phone)
      }
    }

    def render(props: Props): VdomElement = {
      val userColumn = TableR.Column[RowData](
        field = "name",
        title = ColumnTitle.Name,
        renderCell = renderProps => renderName(renderProps.data)
      )
      val roleColumn = TableR.Column[RowData](
        field = "groupName",
        title = ColumnTitle.Group,
        renderCell = renderProps => renderGroup(renderProps.data)
      )
      val emailColumn = TableR.Column[RowData](
        field = "email",
        title = ColumnTitle.EmailAddress,
        renderCell = renderProps => renderEmail(renderProps.data)
      )
      val phoneColumn = TableR.Column[RowData](
        field = "phone",
        title = ColumnTitle.PhoneNumber,
        renderCell = renderProps => renderPhone(renderProps.data)
      )

      val columns = List(
        Option(userColumn),
        Option(roleColumn),
        Option(emailColumn),
        Option(phoneColumn)
      ).flatten

      <.div(
        ^.maxHeight := 500.px,
        TableOfUsers(
          columns = columns,
          data = props.rowsWithModifyCb.map(_._1).toList,
          initialSortColumns = List(
            TableR.SortColumn[RowData](
              column = userColumn,
              direction = TableR.ColumnSortDirection.Asc
            )
          ),
          options = TableR.Options(
            layout = TableR.Layout.FitColumns,
            rowHeight = Option(48)
          )
        )()
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .renderBackend[Backend]
    .build

}
