// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.models

import anduin.id.fundsub.FundSubLpId
import anduin.stargazer.service.formcomment.FormCommentCommons.{CommentVisibilityType, CommentThread, DualCommentThread}
import anduin.protobuf.fundsub.comment.{AmlKycDocData, FormQuestionData, TopicData}

private[fundsub] sealed trait CommentView derives CanEqual

private[fundsub] object CommentView {

  case object NoThread extends CommentView

  case class Doctype(
    doctype: String,
    fundSubLpId: FundSubLpId,
    targetIsHidden: Boolean,
    isPublic: Boolean = true,
    comment: String = "",
    defaultOpenedCommentIdOpt: Option[String]
  ) extends CommentView

  case class Field(
    formQuestionData: FormQuestionData,
    fundSubLpId: FundSubLpId,
    targetIsHidden: Boolean,
    isPublic: Boolean = true,
    comment: String = "",
    defaultOpenedCommentIdOpt: Option[String]
  ) extends CommentView

  final case class CommentViewParams(
    topicData: TopicData,
    fundSubLpId: FundSubLpId,
    targetIsHidden: Boolean,
    isPublic: Boolean = true,
    comment: String = "",
    defaultOpenedCommentIdOpt: Option[String]
  )

  final case class SwitchThreadVisibilityParams(
    newVisibilityType: CommentVisibilityType,
    initialComment: String
  )

  def threadToCommentView(targetIsHidden: Boolean, thread: CommentThread): CommentView = {
    val lpId = thread.fundSubLpId
    thread.topicData match {
      case amlKycDocData: AmlKycDocData =>
        CommentView.Doctype(
          amlKycDocData.doctype,
          fundSubLpId = lpId,
          targetIsHidden = targetIsHidden,
          isPublic = thread.isPublic,
          defaultOpenedCommentIdOpt = None
        )
      case formQuestionData: FormQuestionData =>
        CommentView.Field(
          formQuestionData,
          fundSubLpId = lpId,
          targetIsHidden = targetIsHidden,
          isPublic = thread.isPublic,
          defaultOpenedCommentIdOpt = None
        )
      case _ => CommentView.NoThread
    }
  }

  def anchorPointToCommentViews(anchorPoint: DualCommentThread): List[CommentView] = {
    val targetIsHidden = anchorPoint.targetIsHidden

    List(
      anchorPoint.internalCommentThreadOpt.map(thread => threadToCommentView(targetIsHidden, thread)),
      anchorPoint.sharedCommentThreadOpt.map(thread => threadToCommentView(targetIsHidden, thread))
    ).flatten
  }

}
