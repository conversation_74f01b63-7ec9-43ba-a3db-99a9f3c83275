// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings

import design.anduin.components.dropdown.Dropdown
import design.anduin.components.switcher.react.SwitcherR
import design.anduin.components.toast.Toast
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import anduin.fundsub.endpoint.lp.UpdateInactiveLpSettingParams
import anduin.id.fundsub.FundSubId
import anduin.protobuf.fundsub.models.InactiveNotificationSetting
import anduin.utils.StringUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import com.anduin.stargazer.client.utils.ZIOUtils

final case class ManageInactiveLpSetting(fundSubId: FundSubId, initialSetting: InactiveNotificationSetting) {

  def apply(): VdomElement = ManageInactiveLpSetting.component(this)

}

object ManageInactiveLpSetting {

  private type Props = ManageInactiveLpSetting

  private final case class State(
    setting: InactiveNotificationSetting,
    pendingUpdateSetting: Option[InactiveNotificationSetting] = None
  )

  private val DaysDropdown = (new Dropdown[Int])()

  private final case class Backend(scope: BackendScope[Props, State]) {

    private def onUpdateSetting(props: Props, setting: InactiveNotificationSetting) = {
      val cb = ZIOUtils.toReactCallback(
        FundSubEndpointClient
          .updateInactiveLpSetting(
            UpdateInactiveLpSettingParams(
              fundSubId = props.fundSubId,
              enabled = setting.enabled,
              days = setting.days
            )
          )
          .map(
            _.fold(
              _ =>
                scope.modState(
                  _.copy(
                    pendingUpdateSetting = None
                  ),
                  Toast.errorCallback("Failed to update setting")
                ),
              _ =>
                scope.modState(state =>
                  state.copy(
                    setting = setting,
                    pendingUpdateSetting = None
                  )
                )
            )
          )
      )
      scope.modState(
        _.copy(
          pendingUpdateSetting = Option(setting)
        ),
        cb
      )
    }

    private def renderDaysDropdown(props: Props, state: State) = {
      val isLoading = state.pendingUpdateSetting.nonEmpty
      val setting = state.pendingUpdateSetting.getOrElse(state.setting)

      <.div(
        tw.wPx128,
        DaysDropdown(
          value = Option(setting.days),
          valueToString = days => {
            StringUtils.pluralItem(days, "day")
          },
          onChange = days =>
            Callback.unless(isLoading) {
              onUpdateSetting(props, setting.copy(days = days))
            },
          items = Seq(
            1,
            3,
            5
          ).map(Dropdown.Item(_)),
          button = Dropdown.Button(
            appearance = Dropdown.Appearance.Full(isFullWidth = true)
          )
        )()
      )
    }

    def render(props: Props, state: State): VdomElement = {
      val isLoading = state.pendingUpdateSetting.nonEmpty
      val setting = state.pendingUpdateSetting.getOrElse(state.setting)

      <.div(
        ComponentUtils.testId(ManageInactiveLpSetting, "Container"),
        SettingSwitchTemplate(
          label = "Show investor inactivity notification",
          description = () =>
            Option(
              <.div(
                if (setting.enabled) {
                  "Show dashboard notifications for investors who've been inactive for:"
                } else {
                  "See dashboard notifications for investors who have been inactive."
                }
              )
            ),
          renderSwitch = () =>
            SwitcherR(
              isChecked = setting.enabled,
              onChange = enabled =>
                Callback.unless(isLoading) {
                  onUpdateSetting(
                    props,
                    InactiveNotificationSetting(
                      enabled = enabled,
                      days = if (enabled) 3 else setting.days
                    )
                  )
                }
            )(),
          renderContent = () =>
            Option.when(setting.enabled) {
              renderDaysDropdown(props, state)
            }
        )()
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getName)
    .initialStateFromProps { props =>
      State(
        setting = props.initialSetting
      )
    }
    .renderBackend[Backend]
    .build

}
