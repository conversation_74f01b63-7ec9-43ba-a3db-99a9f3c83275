// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.contact

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.menu.react.{MenuItemR, MenuR}
import design.anduin.components.modal.Modal
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.PortalPosition
import design.anduin.components.portal.PortalUtils.IsClosable
import design.anduin.components.textbox.TextBox
import design.anduin.components.toast.Toast
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.vdom.html_<^.*
import org.scalajs.dom.KeyCode
import anduin.fundsub.constants.Terms
import anduin.fundsub.endpoint.contact.{AddContactParams, ContactData, FundSubContactInfo}
import anduin.fundsub.endpoint.reviewpackage.FundSubReviewPackageData
import anduin.id.contact.ContactId
import anduin.id.entity.EntityId
import anduin.id.fundsub.{FundSubContactGroupId, FundSubId}
import anduin.protobuf.fundsub.LpFlowType
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.investor.contact.modal.*
import com.anduin.stargazer.fundsub.module.investor.contact.widget.FundSubContactWidget
import com.anduin.stargazer.fundsub.module.investor.contact.widget.FundSubContactWidget.ContactSelection
import com.anduin.stargazer.fundsub.module.investor.dashboard.lp.closes.FundSubCloseDataProvider.FundSubCloseDataClient
import stargazer.model.routing.Page

final case class FundSubContactHeader(
  fundSubId: FundSubId,
  enabledCustomId: Boolean,
  entityId: EntityId,
  contactInfo: FundSubContactInfo,
  selectedContacts: Seq[ContactId],
  selectedGroupContacts: Seq[(FundSubContactGroupId, ContactId)],
  selectedEmptyGroups: Seq[FundSubContactGroupId],
  onChangeSearchValue: String => Callback,
  reFetch: Callback,
  router: RouterCtl[Page],
  closeData: Option[FundSubCloseDataClient],
  reviewPackageData: Option[FundSubReviewPackageData],
  lpFlowType: LpFlowType
) {

  def apply(): VdomElement = FundSubContactHeader.component(this)

  lazy val isSelecting: Boolean =
    selectedContacts.nonEmpty || selectedGroupContacts.nonEmpty || selectedEmptyGroups.nonEmpty

}

object FundSubContactHeader {

  private type Props = FundSubContactHeader

  private final case class State(
    searchValue: String = "",
    importing: Boolean = false
  )

  private class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      val hasData = props.contactInfo.contactWithoutGroup.nonEmpty || props.contactInfo.groups.nonEmpty
      <.div(
        ComponentUtils.testId(FundSubContactHeader, "Container"),
        tw.flex,
        <.div(
          tw.flex,
          if (props.isSelecting) {
            <.div(
              tw.mr8,
              renderBatchDeleteButton(props)
            )
          } else {
            TagMod(
              renderImportContactButton(
                props,
                state,
                !hasData
              ),
              <.div(tw.mx8.borderGray4.borderLeft),
              renderAddContactButton(props),
              <.div(tw.mx8, renderAddInvestorButton(props))
            )
          }
        ),
        <.div(
          ComponentUtils.testId(FundSubContactHeader, "SearchBox"),
          tw.mlAuto,
          renderSearchBox(props, state)
        )
      )
    }

    private def renderBatchDeleteButton(props: Props): VdomNode = {
      val (selectedContacts, selectedGroups) = getSelectedContactAndGroupToDelete(props)
      Modal(
        renderTarget = open =>
          Button(
            style = Button.Style.Full(color = Button.Color.Gray0, icon = Some(Icon.Glyph.UserRemove)),
            onClick = open
          )(<.span(ComponentUtils.testId(FundSubContactHeader, "BatchDeleteButton"), "Remove")),
        renderContent = closeModal =>
          BatchDeleteContactAndGroupModal(
            fundSubId = props.fundSubId,
            selectedContacts = selectedContacts,
            selectedGroups = selectedGroups,
            reFetch = props.reFetch,
            closeModal = closeModal
          )(),
        title = getDeleteModalTitle(selectedContacts, selectedGroups),
        isClosable = Some(IsClosable(onEsc = true, onOutsideClick = false))
      )()
    }

    private def getDeleteModalTitle(
      selectedContacts: Seq[(ContactId, Seq[FundSubContactGroupId])],
      selectedGroups: Seq[FundSubContactGroupId]
    ) = {
      if (selectedContacts.isEmpty) {
        "Delete entities?"
      } else if (selectedGroups.isEmpty) {
        "Delete contacts?"
      } else {
        "Delete entities & contacts?"
      }
    }

    private def renderSearchBox(props: Props, state: State): VdomNode = {
      <.div(
        ^.width := "240px",
        TextBox(
          value = state.searchValue,
          onChange = value => scope.modState(_.copy(searchValue = value)),
          placeholder = "Search contacts",
          icon = Some(Icon.Glyph.Search),
          onKeyDown =
            e => Callback.when(e.keyCode == KeyCode.Enter)(props.onChangeSearchValue(state.searchValue.toLowerCase))
        )()
      )

    }

    private def renderImportContactButton(props: Props, state: State, isPrimary: Boolean): VdomNode = {
      PopoverR(
        renderTarget = (open, isOpened) =>
          Button(
            style = Button.Style
              .Full(
                color = if (isPrimary) Button.Color.Primary else Button.Color.Gray0,
                isSelected = isOpened,
                isBusy = state.importing
              ),
            onClick = open
          )(
            <.div(
              ComponentUtils.testId(FundSubContactHeader, "ImportButton"),
              tw.flex.itemsCenter,
              IconR(Icon.Glyph.Plus)(),
              <.span(tw.mx8, "Import contacts"),
              IconR(Icon.Glyph.CaretDown)()
            )
          ),
        renderContent = closePopover =>
          MenuR()(
            renderImportUserContact(props, closePopover)
          ),
        position = PortalPosition.BottomLeft
      )()
    }

    private def renderImportUserContact(props: Props, closePopover: Callback): VdomNode = {
      Modal(
        renderTarget = open => MenuItemR(onClick = open, icon = Some(Icon.Glyph.UserInfo))("From existing funds"),
        renderContent = close =>
          FundSubContactWidget(
            fundSubId = props.fundSubId,
            addContacts = onImportUserContact(props),
            close = close >> closePopover
          )(),
        size = Modal.Size(Modal.Width.Full, Modal.Height.Full)
      )()
    }

    private def onImportUserContact(props: Props)(selection: ContactSelection): Callback = {
      scope.modState(
        _.copy(importing = true),
        ZIOUtils.toReactCallback(
          FundSubEndpointClient
            .addContact(
              AddContactParams(
                props.fundSubId,
                selection.allContacts.map(model =>
                  ContactData(
                    model.firstName,
                    model.lastName,
                    model.email,
                    Seq.empty
                  )
                )
              )
            )
            .map(
              _.fold(
                _ =>
                  scope.modState(
                    _.copy(importing = false),
                    Toast.errorCallback("Unable to import existing contacts, try again")
                  ),
                _ =>
                  scope.modState(
                    _.copy(importing = false),
                    Toast.successCallback("Contact imported") >> props.reFetch
                  )
              )
            )
        )
      )
    }

    private def renderAddContactButton(props: Props): VdomNode = {
      Modal(
        renderTarget = open =>
          Button(
            style = Button.Style.Full(icon = Some(Icon.Glyph.UserSingle)),
            onClick = open
          )(<.span(ComponentUtils.testId(FundSubContactHeader, "AddContactButton"), "Add a contact")),
        renderContent = close =>
          AddOrEditContactModal(
            fundSubId = props.fundSubId,
            contactModelOpt = None,
            allContactsInfo = props.contactInfo,
            onClose = close,
            reFetch = props.reFetch
          )(),
        title = "Add a contact"
      )()
    }

    private def renderAddInvestorButton(props: Props): VdomNode = {
      Modal(
        renderTarget = open =>
          Button(
            style = Button.Style.Full(icon = Some(Icon.Glyph.UserGroupLarge)),
            onClick = open
          )(
            <.span(
              ComponentUtils.testId(FundSubContactHeader, "AddInvestorButton"),
              s"Add an ${Terms.InvesteeEntityLabelLowerCase}"
            )
          ),
        renderContent = close =>
          AddOrEditGroupModal(
            fundSubId = props.fundSubId,
            groupOpt = None,
            enabledCustomId = props.enabledCustomId,
            allContactsInfo = props.contactInfo,
            onClose = close,
            reFetch = props.reFetch
          )(),
        title = s"Add ${Terms.InvesteeEntityLabelLowerCase}",
        size = Modal.Size(width = Modal.Width.Px720)
      )()
    }

    private def getSelectedContactAndGroupToDelete(props: Props) = {
      // List of selected contact with corresponding groups
      val selectedContacts = props.selectedGroupContacts.groupBy(_._2).view.mapValues(_.map(_._1).toSeq).toSeq ++
        props.selectedContacts.map(id => id -> Seq.empty[FundSubContactGroupId])
      // List of selected group, either because all contacts inside are selected, or is empty selected group
      val selectedGroups = props.contactInfo.groups
        .filter { case (group, contacts) =>
          if (contacts.isEmpty) {
            props.selectedEmptyGroups.contains(group.id)
          } else {
            contacts.forall(contact => props.selectedGroupContacts.contains(group.id -> contact.contactId))
          }
        }
        .map(_._1.id)
        .toSeq
      (selectedContacts, selectedGroups)
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
