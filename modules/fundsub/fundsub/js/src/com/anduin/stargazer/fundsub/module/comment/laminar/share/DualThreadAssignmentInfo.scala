package com.anduin.stargazer.fundsub.module.comment.laminar.share

import com.raquo.laminar.api.L.*
import design.anduin.components.progress.Skeleton
import design.anduin.components.progress.laminar.SkeletonL
import design.anduin.style.tw.*

import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.protobuf.fundsub.comment.TopicData
import com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent.FetchCommentAnchorPointAssigneeL
import com.anduin.stargazer.service.account.AccountUtils

private[laminar] case class DualThreadAssignmentInfo(
  fundSubId: FundSubId,
  lpIdSignal: Signal[FundSubLpId],
  topicDataSignal: Signal[TopicData],
  refetchAssigneeInfoEventStream: EventStream[Unit]
) {

  def apply(): HtmlElement = {
    FetchCommentAnchorPointAssigneeL(
      fundSubId = fundSubId,
      lpIdSignal = lpIdSignal,
      topicDataSignal = topicDataSignal,
      renderChildren = renderProps => {
        val isFetchingSignal = renderProps.dataSignal.map(_.isFetching)
        val assigneeNameOptSignal = computeAssigneeNameString(renderProps)
        div(
          child <-- isFetchingSignal.splitBoolean(
            whenTrue = _ => renderLoadingSkeleton,
            whenFalse = _ => emptyNode
          ),
          child <-- assigneeNameOptSignal.splitOption(
            ifEmpty = emptyNode,
            project = (_, assigneeNameSignal) =>
              div(
                tw.textGray7.text13.leading20.textSmall.mt8,
                "Assigned to ",
                text <-- assigneeNameSignal
              )
          ),
          refetchAssigneeInfoEventStream.mapTo(false) --> renderProps.refetch
        )
      }
    )()
  }

  private def computeAssigneeNameString(renderProps: FetchCommentAnchorPointAssigneeL.RenderChildren) = {
    renderProps.dataSignal.map(_.data).combineWith(AccountUtils.currentUserIdObs).map {
      case (assigneeOpt, currentUserIdOpt) =>
        assigneeOpt
          .filter(_.assigneeName.nonEmpty)
          .map { assignee =>
            val currentUserIsAssignee = currentUserIdOpt.exists { currentUserId =>
              assignee.assigneeIdOpt
                .exists(_.fold(_ == currentUserId, _ => false))
            }
            if (currentUserIsAssignee) {
              "me"
            } else {
              assignee.assigneeName
            }
          }
    }
  }

  private def renderLoadingSkeleton =
    div(
      tw.mt8,
      SkeletonL(
        height = "16px",
        width = "180px",
        shape = Skeleton.Shape.Rounded
      )()
    )

}
