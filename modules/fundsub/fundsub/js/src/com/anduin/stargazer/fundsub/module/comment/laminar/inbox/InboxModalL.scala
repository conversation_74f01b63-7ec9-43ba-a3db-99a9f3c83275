// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.inbox

import anduin.frontend.AirStreamUtils
import com.raquo.laminar.api.L.*
import design.anduin.style.tw.*
import anduin.fundsub.endpoint.graphql.FundAdminsInfoForDashboardSchema
import anduin.id.fundsub.FundSubId
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.comment.models.{CommentNotificationSetting, InboxLocationParams}
import design.anduin.components.util.ComponentUtils
import anduin.fundsub.comment.models.CommentNotification

private[inbox] case class InboxModalL(
  fundId: FundSubId,
  // TODO(comment) @tuananhtd: verify this preset location logic
  presetLocationOptSignal: Signal[Option[InboxLocationParams]],
  notificationsSignal: Signal[List[CommentNotification]],
  commentNotificationSettingSignal: Signal[Option[CommentNotificationSetting]],
  fundSubAdminsInfoSchema: Signal[Option[FundAdminsInfoForDashboardSchema]],
  isAmlKycCommentEnabledSignal: Signal[Boolean],
  isCommentMentioningEnabledSignal: Signal[Boolean],
  isCommentAssignmentEnabledSignal: Signal[Boolean],
  inactiveThresholdSignal: Signal[Option[Int]],
  onClose: Observer[Unit]
) {

  private val fundNameSignal = fundSubAdminsInfoSchema.map(_.map(_.fundName).getOrElse(""))
  private val entitySignal = commentNotificationSettingSignal.map(_.map(_.investorEntity))
  private val canMakeShareCommentVar = Var(false)

  def apply(): HtmlElement = {
    div(
      ComponentUtils.testIdL(InboxModalL),
      EventStream.unit(emitOnce = true).flatMapSwitch(_ => checkIfUserCanMakeShareComment) --> Observer.empty,
      tw.wPc100.hVh100.flex.flexCol,
      // Header
      InboxHeaderL(
        commentNotificationSettingSignal = commentNotificationSettingSignal,
        fundNameSignal = fundNameSignal,
        canMakeShareCommentSignal = canMakeShareCommentVar.signal,
        fundSubId = fundId,
        onClose = onClose
      )(),
      // Body
      InboxBodyL(
        presetLocationOptSignal = presetLocationOptSignal,
        commentNotificationSettingSignal = commentNotificationSettingSignal,
        entitySignal = entitySignal,
        fundId = fundId,
        isAmlKycCommentEnabledSignal = isAmlKycCommentEnabledSignal,
        isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
        isCommentAssignmentEnabledSignal = isCommentAssignmentEnabledSignal,
        inactiveThresholdSignal = inactiveThresholdSignal,
        notificationsSignal = notificationsSignal,
        canMakePublicCommentSignal = canMakeShareCommentVar.signal
      )()
    )
  }

  private def checkIfUserCanMakeShareComment = {
    AirStreamUtils.taskToStreamDEPRECATED {
      FundSubEndpointClient
        .checkIfUserCanMakePublicCommentInFund(
          fundId
        )
        .map(
          _.fold(
            _ => (),
            response => canMakeShareCommentVar.set(response)
          )
        )
    }
  }

}
