// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.review.widget

import design.anduin.components.avatar.InitialAvatar
import design.anduin.components.avatar.react.InitialAvatarR
import design.anduin.components.menu.react.MenuDividerR
import design.anduin.components.suggest.{MultiSuggest, Suggest}
import design.anduin.components.tag.Tag
import design.anduin.components.textbox.TextBox
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.id.fundsub.group.FundSubInvestorGroupId
import anduin.model.common.user.{UserId, UserInfo}
import com.anduin.stargazer.fundsub.module.investor.settings.review.model.AdminAndInvestorGroupInfo

private[settings] final case class ReviewerAssignmentEditor(
  userIds: Seq[UserId],
  adminAndInvestorGroupInfo: AdminAndInvestorGroupInfo,
  investorGroupIdOpt: Option[FundSubInvestorGroupId],
  isSubscriptionDocReview: Boolean = false,
  onChange: Seq[UserId] => Callback
) {
  def apply(): VdomElement = ReviewerAssignmentEditor.component(this)
}

private[settings] object ReviewerAssignmentEditor {
  private type Props = ReviewerAssignmentEditor

  private val ReviewerSuggest = (new MultiSuggest[UserInfo])()

  private def getGroupNameAndFullName(props: Props)(reviewer: UserInfo) = {
    val reviewerUserIdOpt = props.adminAndInvestorGroupInfo.emailToUserId
      .get(reviewer.emailAddressStr)

    val adminGroupIdOpt = reviewerUserIdOpt
      .flatMap { userId =>
        props.adminAndInvestorGroupInfo.userIdToAdminGroupId.get(userId)
      }

    val adminGroupOpt = props.adminAndInvestorGroupInfo.adminGroups
      .find { group =>
        adminGroupIdOpt.contains(group.groupId)
      }

    val groupMembers = adminGroupOpt
      .map(_.members.map(_.userId))
      .map(_.filterNot { userId =>
        props.userIds.contains(userId)
      })
      .getOrElse(Seq.empty)
      .sortBy { adminId =>
        props.adminAndInvestorGroupInfo.adminUserInfos
          .get(adminId)
          .map(_.fullNameString)
          .getOrElse("")
      }

    val groupName = adminGroupOpt.map(_.name).getOrElse("")
    val indexInGroup = groupMembers.indexWhere(member => reviewerUserIdOpt.contains(member))
    groupName -> indexInGroup
  }

  private def renderSuggest(props: Props) = {
    val validUserIds = props.adminAndInvestorGroupInfo.adminGroups.flatMap(_.members.map(_.userId))
    val allUserInfos = props.adminAndInvestorGroupInfo.adminUserInfos
    val reviewerEmails = props.userIds.flatMap { userId =>
      allUserInfos.get(userId).map(_.emailAddressStr)
    }
    ReviewerSuggest(
      value = reviewerEmails,
      valueToTag = email => {
        val hasAccess = props.adminAndInvestorGroupInfo.isValidReviewerOfInvestorGroup(
          email,
          props.investorGroupIdOpt,
          props.isSubscriptionDocReview
        )
        MultiSuggest.TagProps(
          label = allUserInfos
            .find(_._2.emailAddressStr == email)
            .map(_._2.fullNameString)
            .getOrElse(""),
          color = {
            if (hasAccess) Tag.Light.Gray else Tag.Light.Danger
          },
          tooltip = Some(() => if (hasAccess) email else ("Member no longer has access to investor data"))
        )
      },
      textBox = MultiSuggest.TextBoxProps(
        placeholder = "Type or search",
        size = TextBox.Size.Px24
      ),
      options = Suggest.Options[UserInfo](
        items = props.adminAndInvestorGroupInfo.adminUserInfos.toList
          .filterNot { case (userId, _) => props.userIds.contains(userId) }
          .filter(adminUserInfo => validUserIds.contains(adminUserInfo._1))
          .sortBy { case (_, userInfo) =>
            getGroupNameAndFullName(props)(userInfo)
          }
          .map(_._2)
          .map(Suggest.Option(_)),
        valueToString = _.emailAddressStr,
        renderItem = Some { renderProps =>
          val reviewer = renderProps.item.value
          val (groupName, indexInGroup) = getGroupNameAndFullName(props)(reviewer)

          <.div(
            TagMod.when(indexInGroup == 0) {
              TagMod(
                // Don't show the top border for the first item
                TagMod.unless(renderProps.index == 0)(
                  MenuDividerR()()
                ),
                <.div(
                  ComponentUtils.testId("Group"),
                  tw.text13.textGray7.fontSemiBold.ml20.py4,
                  groupName
                )
              )
            },
            renderProps.defaultItemRender
          )
        },
        renderItemBody = Some { c =>
          <.div(
            ComponentUtils.testId("NameEmail"),
            tw.flex.itemsCenter,
            <.div(
              tw.mr8,
              InitialAvatarR(
                id = c.value.emailAddressStr,
                initials = c.value.fullNameString.split("\\s+").take(2).map(_.take(1)).mkString("").toUpperCase(),
                size = InitialAvatar.Size.Px20
              )()
            ),
            s"${c.value.fullNameString} (${c.value.emailAddressStr})"
          )
        },
        optionFilter = Some(c => s"${c.value.fullNameString} (${c.value.emailAddressStr})")
      ),
      onChange = emails => {
        val selectedUserIds = emails.flatMap { email =>
          allUserInfos
            .find(_._2.emailAddressStr == email)
            .map(_._1)
        }
        props.onChange(selectedUserIds)
      }
    )()
  }

  private def render(props: Props) = {
    renderSuggest(props)
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
