// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.exporttemplate

import design.anduin.components.modal.{ModalBody, ModalFooterWCancel}
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import design.anduin.components.button.Button
import design.anduin.components.toast.Toast

import anduin.id.fundsub.FundSubSelfServiceExportTemplateId
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubDataExportEndpointClient

private[exporttemplate] case class DeleteTemplateConfirmationModal(
  templateId: FundSubSelfServiceExportTemplateId,
  templateName: String,
  onCloseModal: Callback,
  refetchTemplates: Callback
) {
  def apply(): VdomElement = DeleteTemplateConfirmationModal.component(this)
}

private[exporttemplate] object DeleteTemplateConfirmationModal {
  private type Props = DeleteTemplateConfirmationModal

  private case class State(
    isDeleting: Boolean = false
  )

  private case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      React.Fragment(
        ModalBody()(
          <.div(
            tw.spaceY8,
            <.div(
              "You are about to delete ",
              <.span(
                tw.fontSemiBold,
                props.templateName
              ),
              "."
            ),
            <.div(
              "Once deleted, exporting data using this template will no longer be possible."
            ),
            <.div(
              "Are you sure you want to continue?"
            )
          )
        ),
        ModalFooterWCancel(
          cancel = props.onCloseModal
        )(
          Button(
            style = Button.Style.Full(color = Button.Color.Danger, isBusy = state.isDeleting),
            onClick = onClickDeleteTemplate
          )("Delete template")
        )
      )
    }

    private def onClickDeleteTemplate = {
      for {
        props <- scope.props
        _ <- scope.state
        _ <- scope.modState(
          _.copy(isDeleting = true),
          ZIOUtils.toReactCallback(
            FundSubDataExportEndpointClient
              .deleteSelfServiceExportTemplate(
                props.templateId
              )
              .map(
                _.fold(
                  _ =>
                    scope.modState(
                      _.copy(isDeleting = false),
                      Toast.errorCallback("Failed to delete export template, please try again ")
                    ),
                  _ =>
                    scope.modState(
                      _.copy(
                        isDeleting = false
                      ),
                      Toast
                        .successCallback("Export template deleted") >> props.onCloseModal >> props.refetchTemplates
                    )
                )
              )
          )
        )
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
