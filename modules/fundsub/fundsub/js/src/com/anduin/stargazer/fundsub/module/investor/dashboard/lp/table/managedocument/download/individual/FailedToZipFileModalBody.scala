// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.dashboard.lp.table.managedocument.download.individual

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.modal.{ModalBody, ModalFooterWCancel, ModalHeader}
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[download] final case class FailedToZipFileModalBody(
  onRetry: Callback,
  onClose: Callback
) {
  def apply(): VdomElement = FailedToZipFileModalBody.component(this)
}

private[download] object FailedToZipFileModalBody {
  private type Props = FailedToZipFileModalBody

  private def renderContent = {
    <.div(
      tw.flex.itemsCenter,
      <.div(
        tw.textWarning4,
        IconR(
          name = Icon.Glyph.Error
        )()
      ),
      <.div(
        tw.ml8,
        "Failed to zip documents!"
      )
    )
  }

  private def render(props: Props) = {
    <.div(
      ModalHeader(
        title = "Zipping documents",
        close = Callback.empty,
        isClosable = false
      )(),
      ModalBody()(
        renderContent
      ),
      ModalFooterWCancel(props.onClose)(
        Button(
          style = Button.Style.Full(
            color = Button.Color.Primary
          ),
          onClick = props.onRetry
        )("Retry")
      )
    )
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
