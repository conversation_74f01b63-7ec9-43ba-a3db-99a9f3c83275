// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.share

import java.time.Instant

import com.raquo.laminar.api.L.*
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.util.{ComponentUtils, NodeListSeq}
import design.anduin.style.tw.*
import org.scalajs.dom.{HTMLParagraphElement, HTMLSpanElement}

import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.id.issuetracker.IssueId
import anduin.scalajs.dompurify.{DomPurify, SanitizeOptions}
import anduin.stargazer.service.formcomment.FormCommentCommons.MentionInfo
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.portal.PortalPosition
import org.scalajs.dom

import anduin.fundsub.comment.utils.CommentUtils
import com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent.FetchMentionsInfoInCommentThread

// Need a dedicate component to render comment text causes we have special logic to render mention and edited tag
private[fundsub] case class CommentContent(
  content: String,
  fundSubId: FundSubId,
  threadId: IssueId,
  commentId: String,
  lpIdSignal: Signal[FundSubLpId],
  createdAtOpt: Option[Instant],
  editedAtOpt: Option[Instant],
  isFlaggedSignal: Signal[Boolean] = Val(false),
  hasMentionSignal: Signal[Boolean]
) {

  private val isEdited = editedAtOpt.isDefined

  def apply(): HtmlElement = {
    div(
      ComponentUtils.testIdL(CommentContent, "container"),
      FetchMentionsInfoInCommentThread(
        fundSubId = fundSubId,
        lpIdSignal = lpIdSignal,
        commentIdSignal = Val(commentId),
        issueIdSignal = Val(threadId),
        hasMentionSignal = hasMentionSignal,
        renderChildren = mentionInfoInfoInCommentThreadsRenderProps => {
          val mentionsSignal = mentionInfoInfoInCommentThreadsRenderProps.dataSignal.map(_.mentionsInfo)
          div(
            className := "ql-editor acl-editor-l acl-editor-l-readonly",
            onMountCallback { thisNode =>
              val node = thisNode.thisNode.ref
              if (isEdited && node.children.nonEmpty) {
                val editedTextNode = dom.document.createElement("span")

                node.lastChild.appendChild(editedTextNode)

                render(
                  editedTextNode,
                  TooltipL(
                    renderContent = _.amend(CommentUtils.getTimestampDisplayString(createdAtOpt)),
                    position = PortalPosition.TopCenter,
                    renderTarget = {
                      span(
                        tw.text11.textGray6.cursorPointer,
                        "(edited)"
                      )
                    }
                  )().amend(tw.inlineBlock.pl4)
                )
                ()
              }
            }
          ).amendThis { thisNode =>
            val node = thisNode.ref
            val sanitizedCommentContent = DomPurify.sanitize(
              content,
              SanitizeOptions(
                allowedTags = Option(List("span", "p")),
                allowedAttributes = Option(List("class", "data-id"))
              )
            )
            node.innerHTML = sanitizedCommentContent
            NodeListSeq(node.querySelectorAll("p")).foreach {
              case ele: HTMLParagraphElement =>
                ele.style.margin = "0px"
              case _ =>
                ()
            }
            div(
              isFlaggedSignal.distinct --> Observer[Boolean] { isFlagged =>
                if (isFlagged) {
                  node.style.opacity = "60%"
                }
              },
              mentionsSignal.distinct --> Observer[Seq[MentionInfo]] { mentionsInfo =>
                NodeListSeq(node.querySelectorAll(".acl-editor-mention")).foreach {
                  case ele: HTMLSpanElement =>
                    val id = ele.getAttribute("data-id")
                    mentionsInfo.find(_.toIdString == id).foreach { mentionInfo =>
                      ele.innerHTML = ""
                      render(
                        ele,
                        span(
                          tw.inlineBlock,
                          if (mentionInfo.nonEmpty) {
                            PopoverL(
                              isAutoFocus = false,
                              renderContent = _ => {
                                div(
                                  ParticipantPopoverBody(mentionInfo)()
                                )
                              },
                              renderTarget = (open, _) => {
                                span(
                                  tw.cursorPointer,
                                  onClick.mapToUnit --> open,
                                  "@",
                                  mentionInfo.getDisplayName
                                )
                              }
                            )()
                          } else {
                            span(
                              tw.cursorPointer,
                              "@",
                              mentionInfo.getDisplayName
                            )
                          }
                        )
                      )
                    }
                  case _ =>
                    ()
                }
              }
            )
          }
        }
      )()
    )
  }

}
