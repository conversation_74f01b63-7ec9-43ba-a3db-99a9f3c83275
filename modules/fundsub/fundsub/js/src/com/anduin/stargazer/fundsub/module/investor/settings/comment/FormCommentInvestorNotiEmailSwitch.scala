// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.comment

import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import design.anduin.table.TaskUtils
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.VdomElement
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.endpoint.lp.UpdateFormCommentInvestorDigestEmailSwitchParams
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.scalajs.pluralize.Pluralize
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.investor.settings.FundSubChildSettingSwitch

private[comment] final case class FormCommentInvestorNotiEmailSwitch(
  fundId: FundSubId,
  enableEmail: Boolean,
  exceptionalLpIds: List[FundSubLpId]
) {
  def apply(): VdomElement = FormCommentInvestorNotiEmailSwitch.component(this)
}

private[comment] object FormCommentInvestorNotiEmailSwitch {
  private type Props = FormCommentInvestorNotiEmailSwitch

  private final case class State(
    enableEmailOptimisticUpdate: Option[Boolean] = None
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    private def renderDescription(fundId: FundSubId, isEnabled: Boolean, exceptionalLpIds: List[FundSubLpId]) = {
      val (statusWording, iconName) = if (!isEnabled) {
        ("ON", Icon.Glyph.Bell)
      } else {
        ("OFF", Icon.Glyph.BellOff)
      }
      <.div(
        <.div("Email digest is sent out every 10 minutes"),
        TagMod.when(exceptionalLpIds.nonEmpty) {
          <.div(
            tw.flex.itemsCenter.mt8.textGray8,
            <.span(tw.textGray6.mr8, IconR(name = iconName)()),
            <.span(
              s"Automatic notifications are turned ",
              <.span(
                tw.fontBold,
                statusWording
              ),
              " for ",
              CommentEmailExceptionalLpIdsModalBtn(
                fundId = fundId,
                buttonLabel = s"${exceptionalLpIds.size} ${Pluralize("investor", exceptionalLpIds.size)}",
                enableEmail = isEnabled,
                exceptionalLpIds = exceptionalLpIds
              )()
            )
          )

        }
      )
    }

    def render(props: Props, state: State): VdomElement = {
      val isChecked = state.enableEmailOptimisticUpdate.getOrElse(props.enableEmail)
      val isWaitingAcknowledge =
        state.enableEmailOptimisticUpdate.nonEmpty && state.enableEmailOptimisticUpdate != Option(props.enableEmail)

      FundSubChildSettingSwitch(
        title = "Automatically notify investors when there are new comments from the fund on their documents",
        enabled = isChecked,
        isDisabled = isWaitingAcknowledge,
        onChange = onUpdateSetting,
        description = () =>
          renderDescription(
            fundId = props.fundId,
            isEnabled = isChecked,
            exceptionalLpIds = props.exceptionalLpIds
          )
      )()
    }

    private def onUpdateSetting(enableEmail: Boolean) = {
      for {
        props <- scope.props
        _ <- scope.modState(
          _.copy(
            enableEmailOptimisticUpdate = Option(enableEmail)
          ),
          TaskUtils.toReactCallback(
            FundSubEndpointClient
              .updateFormCommentInvestorDigestEmailSwitch(
                UpdateFormCommentInvestorDigestEmailSwitchParams(
                  props.fundId,
                  enableEmail = enableEmail
                )
              )
              .map(
                _.fold(
                  _ =>
                    scope.modState(
                      _.copy(enableEmailOptimisticUpdate = None),
                      Toast.errorCallback("Failed to update settings")
                    ),
                  _ => Callback.empty
                )
              )
          )
        )
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
