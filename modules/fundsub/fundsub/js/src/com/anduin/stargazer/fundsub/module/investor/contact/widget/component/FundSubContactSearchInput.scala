// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.contact.widget.component

import anduin.contact.endpoint.{ContactGroupModel, ContactModel}
import anduin.contact.widget.{ContactWidgetModel, ContactWidgetUtils}
import anduin.fundsub.endpoint.contact.{QueryContactsByEmailParams, QueryContactsByNameParams, QueryGroupByNameParams}
import anduin.id.contact.ContactId
import anduin.id.fundsub.FundSubId
import anduin.scalajs.pluralize.Pluralize
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.raquo.airstream.core.Observer
import com.raquo.airstream.eventbus.{EventBus, WriteBus}
import com.raquo.airstream.ownership.{OneTimeOwner, Subscription}
import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.popover.react.PopoverContentR
import design.anduin.components.portal.{PortalPosition, PortalUtils}
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.components.textbox.TextBox
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import org.scalajs.dom.HTMLElement
import scala.concurrent.duration.*
import zio.{Task, ZIO}

private[widget] final case class FundSubContactSearchInput(
  fundSubId: FundSubId,
  onSelectContact: (ContactWidgetModel, Map[ContactId, ContactModel]) => Callback,
  onSelectGroup: (String, Seq[ContactGroupModel], ContactGroupModel) => Callback,
  onViewAllContactsResult: (String, Seq[ContactWidgetModel], Map[ContactId, ContactModel]) => Callback,
  onViewAllGroupsResult: (String, Seq[ContactGroupModel]) => Callback
) {
  def apply(): VdomElement = FundSubContactSearchInput.component(this)
}

private[widget] object FundSubContactSearchInput {

  private type Props = FundSubContactSearchInput

  private final case class State(
    inputValue: String,
    contacts: Seq[ContactWidgetModel],
    groups: Seq[ContactGroupModel],
    showResultPopover: Boolean,
    isBusy: Boolean,
    contactMap: Map[ContactId, ContactModel] = Map.empty
  )

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(
      State(
        "",
        Seq.empty,
        Seq.empty,
        showResultPopover = false,
        isBusy = false
      )
    )
    .renderBackend[Backend]
    .componentWillUnmount { scope =>
      scope.backend.stopProcessEditEvent()
    }
    .componentDidMount { scope =>
      scope.backend.startProcessEditEvent(scope.props.fundSubId)
    }
    .build

  private class Backend(scope: BackendScope[Props, State]) {

    private val targetRef: Ref.Simple[HTMLElement] = Ref[HTMLElement]

    private val DisplayResultCount: Int = 5

    val editEventBus: EventBus[String] = new EventBus[String]

    def render(props: Props, state: State): VdomElement = {
      <.div(
        tw.wPc100.mb16,
        <.div.withRef(targetRef)(
          tw.pb4,
          TextBox(
            icon = Some(Icon.Glyph.Search),
            size = TextBox.Size.Px40,
            value = state.inputValue,
            onChange = onInputChange,
            placeholder = "Search for a contact, organization, group...",
            onFocus = Callback.when(state.inputValue.nonEmpty) {
              scope.modState(_.copy(showResultPopover = true))
            }
          )()
        ),
        TagMod.when(state.showResultPopover) {
          resultPopover(props, state)
        }
      )
    }

    private def resultPopover(props: Props, state: State): VdomElement = {
      PopoverContentR(
        isAutoFocus = false,
        isClosable = PortalUtils.defaultIsClosable,
        position = PortalPosition.BottomCenter,
        targetRef = targetRef,
        renderContent = renderProps => {
          renderProps.renderContent(
            <.div(
              tw.p20,
              ^.width := "calc(100vw - 80px)",
              if (state.isBusy) {
                BlockIndicatorR(title = Some("Finding contacts..."))()
              } else if (state.contacts.isEmpty && state.groups.isEmpty) {
                <.div(
                  tw.hPx256.flex.flexCol.itemsCenter.justifyCenter,
                  <.div(
                    tw.hPx64.wPx64.bgGray1.textGray6.roundedFull,
                    tw.flex.itemsCenter.justifyCenter,
                    IconR(Icon.Glyph.Search, Icon.Size.Px24)()
                  ),
                  <.div(tw.text17.leading28.fontBold.mt12, "No results found"),
                  <.div(tw.textGray7.mt4, "We couldn't find any contacts matching your search")
                )
              } else {
                <.div(
                  <.div(
                    tw.textGray6,
                    "Results in:"
                  ),
                  <.div(
                    tw.flex.mt16,
                    TagMod.when(state.contacts.nonEmpty)(contactSearchResults(props, state)),
                    TagMod.when(state.groups.nonEmpty)(groupSearchResults(props, state))
                  )
                )
              }
            )
          )
        },
        onOverlayClick = Some(scope.modState(_.copy(showResultPopover = false)))
      )()
    }

    private def contactSearchResults(props: Props, state: State): VdomElement = {
      <.div(
        tw.flex.flexCol.wPc40.mr20,
        <.div(tw.text15.fontMedium, "Names & emails"),
        <.div(
          tw.wPx32.bgPrimary3.mb12,
          ^.height := "2px",
          ^.marginTop := "6px"
        ),
        state.contacts
          .take(DisplayResultCount)
          .toTagMod(
            using { contact =>
              <.div(
                tw.flex.itemsCenter.cursorPointer.px12.py8.hover(tw.bgGray1),
                ^.onClick -->
                  scope.modState(
                    _.copy(
                      inputValue = "",
                      contacts = Seq.empty,
                      groups = Seq.empty,
                      showResultPopover = false
                    ),
                    props.onSelectContact(contact, state.contactMap)
                  ),
                <.div(tw.fontSemiBold, contact.fullName),
                <.div(tw.ml8.text11.leading16.textGray7, s"(${contact.email})")
              )
            }
          ),
        TagMod.when(state.contacts.size > DisplayResultCount) {
          <.div(
            tw.mt12.flex.itemsCenter.textPrimary4,
            IconR(Icon.Glyph.ArrowRight)(),
            <.div(
              tw.ml8,
              Button(
                style = Button.Style.Text(),
                onClick = scope.modState(
                  _.copy(showResultPopover = false, inputValue = ""),
                  props.onViewAllContactsResult(
                    state.inputValue,
                    state.contacts,
                    state.contactMap
                  )
                )
              )("View all results")
            )
          )
        }
      )
    }

    private def groupSearchResults(props: Props, state: State): VdomElement = {
      <.div(
        tw.flex.flexCol.wPc40.mr20,
        <.div(tw.text15.fontMedium, "Groups"),
        <.div(
          tw.wPx32.bgPrimary3.mb12,
          ^.height := "2px",
          ^.marginTop := "6px"
        ),
        state.groups
          .take(DisplayResultCount)
          .toTagMod(
            using { group =>
              <.div(
                tw.flex.itemsCenter.cursorPointer.px12.py8.hover(tw.bgGray1),
                ^.onClick -->
                  scope.modState(
                    _.copy(
                      inputValue = "",
                      contacts = Seq.empty,
                      groups = Seq.empty,
                      showResultPopover = false
                    ),
                    props.onSelectGroup(
                      state.inputValue,
                      state.groups,
                      group
                    )
                  ),
                <.div(tw.fontSemiBold, group.groupInfo.groupName),
                <.div(
                  tw.ml8.text11.leading16.textGray7,
                  s"(${Pluralize(
                      "contact",
                      group.contacts.size,
                      inclusive = true
                    )})"
                )
              )
            }
          ),
        TagMod.when(state.groups.size > DisplayResultCount) {
          <.div(
            tw.mt12.flex.itemsCenter.textPrimary4,
            IconR(Icon.Glyph.ArrowRight)(),
            <.div(
              tw.ml8,
              Button(
                style = Button.Style.Text(),
                onClick = scope.modState(
                  _.copy(showResultPopover = false, inputValue = ""),
                  props.onViewAllGroupsResult(state.inputValue, state.groups)
                )
              )("View all results")
            )
          )
        }
      )
    }

    private def onInputChange(value: String): Callback = {
      for {
        _ <- scope.modState(_.copy(inputValue = value))
        _ <- ZIOUtils.toReactCallback {
          feedEditEvent(editEventBus.writer, value).map(_ => Callback.empty)
        }
      } yield ()
    }

    private def feedEditEvent(writeBus: WriteBus[String], item: String) = {
      ZIO.attempt(writeBus.onNext(item))
    }

    private def searchForContacts(fundSubId: FundSubId, queryStr: String): Task[Unit] = {
      for {
        emailEither <- FundSubEndpointClient.queryContactsByEmail(QueryContactsByEmailParams(fundSubId, queryStr))
        nameEither <- FundSubEndpointClient.queryContactsByName(QueryContactsByNameParams(fundSubId, queryStr))
        _ <- ZIOUtils.fromReactCallback {
          val emailContacts = emailEither.fold(
            _ => Seq.empty[ContactModel],
            resp => resp.contacts
          )
          val nameContacts = nameEither.fold(
            _ => Seq.empty[ContactModel],
            resp => resp.contacts
          )
          val allContacts = (emailContacts ++ nameContacts).distinct
          scope.modState(
            _.copy(
              contacts = ContactWidgetUtils.constructContactWidgetModels(allContacts),
              contactMap = allContacts.map(c => c.contactId -> c).toMap
            )
          )
        }
      } yield ()
    }

    private def searchForGroups(fundSubId: FundSubId, queryStr: String): Task[Unit] = {
      for {
        groupsEither <- FundSubEndpointClient.queryGroupByName(QueryGroupByNameParams(fundSubId, queryStr))
        _ <- ZIOUtils.fromReactCallback {
          val groups = groupsEither.fold(
            _ => Seq.empty[ContactGroupModel],
            resp => resp.groups
          )
          scope.modState(_.copy(groups = groups))
        }
      } yield ()
    }

    private def throttledEvents(fundSubId: FundSubId) = editEventBus.events
      .throttle(FiniteDuration(1, SECONDS).toMillis.toInt)
      .flatMapSwitch { value =>
        val task = if (value.isEmpty) {
          ZIOUtils.fromReactCallback(
            scope.modState {
              _.copy(
                contacts = Seq.empty,
                groups = Seq.empty,
                showResultPopover = false,
                isBusy = false
              )
            }
          )
        } else {
          for {
            _ <- ZIOUtils.fromReactCallback(scope.modState(_.copy(showResultPopover = true, isBusy = true)))
            _ <- searchForContacts(fundSubId, value)
            _ <- searchForGroups(fundSubId, value)
            _ <- ZIOUtils.fromReactCallback(scope.modState(_.copy(isBusy = false)))
          } yield ()
        }

        ZIOUtils.toEventStreamUnsafeDEPRECATED(task)
      }

    private var subscription: Option[Subscription] = None // scalafix:ok DisableSyntax.var

    def startProcessEditEvent(fundSubId: FundSubId): Callback = {
      Callback {
        subscription.foreach(_.kill())
        val owner = new OneTimeOwner(() => scribe.error("SearchInput owner accessed after killed"))
        subscription = Option(
          throttledEvents(fundSubId).addObserver(Observer.empty)(
            using owner
          )
        )
      }
    }

    def stopProcessEditEvent(): Callback = {
      Callback {
        subscription.foreach(_.kill())
        subscription = None
      }
    }

  }

}
