// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.emaillog

import com.anduin.stargazer.fundsub.module.investor.settings.emaillog.data.RecipientInfo
import design.anduin.components.avatar.react.InitialAvatarR
import design.anduin.components.dropdown.Dropdown
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[emaillog] sealed trait RecipientOption derives CanEqual {
  def title: String
  def email: String = ""
}

private[emaillog] object AllRecipients extends RecipientOption {
  override def title: String = "All recipients"
}

private[emaillog] final case class SingleRecipientOption(
  info: RecipientInfo
) extends RecipientOption {
  override def title: String = s"${info.fullName} (${info.email})"

  override def email: String = info.email
}

private[emaillog] final case class RecipientDropdown(
  option: RecipientOption,
  recipients: List[RecipientInfo],
  onChange: RecipientOption => Callback
) {
  def apply(): VdomElement = RecipientDropdown.component(this)
}

private[emaillog] object RecipientDropdown {
  private val EmailDropdown = (new Dropdown[RecipientOption])()

  private type Props = RecipientDropdown

  private def renderItem(recipientOption: RecipientOption) = {
    recipientOption match {
      case AllRecipients => <.div("All recipients")
      case SingleRecipientOption(recipient) =>
        <.div(
          tw.flex.itemsCenter,
          InitialAvatarR(
            id = recipient.displayName,
            initials = recipient.initials
          )(),
          <.div(
            tw.ml8.fontSemiBold.textGray8,
            recipient.fullName
          ),
          <.div(
            ^.wordBreak.breakAll,
            tw.ml8.textGray8,
            s"(${recipient.email})"
          )
        )
    }
  }

  private def render(props: Props) = {
    val allOptions = List[RecipientOption](
      AllRecipients
    ) ++ props.recipients
      .sortBy(_.fullName)
      .map { recipient =>
        SingleRecipientOption(
          recipient
        )
      }
    EmailDropdown(
      value = Option(props.option),
      valueToString = _.title,
      items = allOptions.map(Dropdown.Item(_)),
      menu = Dropdown.Menu[RecipientOption](
        renderItemBody = Option(item => renderItem(item.value))
      ),
      button = Dropdown.Button(
        appearance = Dropdown.Appearance.Full(isFullWidth = true)
      ),
      onChange = value => props.onChange(value)
    )()
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
