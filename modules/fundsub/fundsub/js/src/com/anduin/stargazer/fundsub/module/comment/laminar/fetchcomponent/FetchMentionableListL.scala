// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent

import com.raquo.laminar.api.L.*
import design.anduin.components.toast.Toast

import anduin.frontend.AirStreamUtils
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.model.notichannel.FundSubNotificationChannels
import anduin.stargazer.service.formcomment.FormCommentCommons.GetMentionableListParams
import anduin.stargazer.service.formcomment.FormCommentCommons.MentionInfo.MentionedGroupInfo
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.notiCenter.NotificationCenter

private[laminar] case class FetchMentionableListL(
  lpIdSignal: Signal[FundSubLpId],
  fundSubId: FundSubId,
  isPublicSignal: Signal[Boolean],
  renderChildren: FetchMentionableListL.RenderChildren => HtmlElement
) {

  case class EventBusData(
    lpId: FundSubLpId,
    isPublicThread: Boolean
  )

  private val fetchMentionableListEventBus = new EventBus[EventBusData]

  private val resultVar = Var(
    FetchMentionableListL.Data(
      isFetching = true,
      mentionableList = Seq.empty
    )
  )

  private val trackingSignal = NotificationCenter.eventSignal(FundSubNotificationChannels.fundSubFormComment(fundSubId))

  private def fetchComments(data: EventBusData) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      FundSubEndpointClient
        .getMentionableList(
          GetMentionableListParams(
            fundSubLpId = data.lpId,
            isPublicThread = data.isPublicThread
          )
        )
        .map {
          _.fold(
            _ => {
              Toast.error("Failed to fetch mentionable list")
              FetchMentionableListL.Data(
                isFetching = false,
                mentionableList = Seq.empty
              )
            },
            response => {
              FetchMentionableListL.Data(
                isFetching = false,
                mentionableList = response.mentionableList
              )
            }
          )
        }
    }
  }

  def apply(): HtmlElement = {
    renderChildren(
      FetchMentionableListL.RenderChildren(
        dataSignal = resultVar.signal
      )
    ).amend(
      onMountCallback { _ =>
        NotificationCenter.subscribeSync(Seq(FundSubNotificationChannels.fundSubFormComment(fundSubId)))
      },
      fetchMentionableListEventBus.events.flatMapSwitch(fetchComments) --> resultVar.writer,
      trackingSignal
        .combineWith(lpIdSignal, isPublicSignal) --> Observer[(NotificationCenter.Watch, FundSubLpId, Boolean)] {
        case ((_, lpId, isPublicThread)) =>
          fetchMentionableListEventBus.emit(
            EventBusData(
              lpId = lpId,
              isPublicThread = isPublicThread
            )
          )
      }
    )
  }

}

private[laminar] object FetchMentionableListL {

  case class Data(
    isFetching: Boolean,
    mentionableList: Seq[MentionedGroupInfo]
  )

  case class RenderChildren(dataSignal: Signal[Data])
}
