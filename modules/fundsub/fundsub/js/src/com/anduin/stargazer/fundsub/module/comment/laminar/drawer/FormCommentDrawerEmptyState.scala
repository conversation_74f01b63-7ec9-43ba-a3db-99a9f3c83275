package com.anduin.stargazer.fundsub.module.comment.laminar.drawer

import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import com.raquo.laminar.api.L.*

import design.anduin.style.tw.*

private[drawer] case class FormCommentDrawerEmptyState() {

  def apply(): HtmlElement = {
    NonIdealStateL(
      icon = img(src := "/web/gondor/images/fundsub/formcomment/formcomment_onboarding.svg"),
      description = div(
        tw.mt8.textGray6,
        div(
          tw.textCenter,
          "Leave a comment by hovering on"
        ),
        div(
          tw.textCenter,
          "a form question"
        )
      )
    )()
  }

}
