// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.preview

import com.raquo.laminar.api.L.*
import design.anduin.style.tw.*

import anduin.id.fundsub.FundSubLpId
import anduin.stargazer.service.formcomment.FormCommentCommons.CommentThread
import com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent.FetchCommentSettingL
import com.anduin.stargazer.fundsub.module.comment.models.{CommentNotificationSetting, CommentTarget, CommentsData}

case class FormCommentProviderL(
  lpId: FundSubLpId,
  renderChildren: FormCommentProviderL.Comments => HtmlElement
) {

  private def buildCommentData(
    target: CommentTarget,
    unseenNotifications: List[FetchCommentNotificationsL.Notification],
    threadsOpt: Option[List[CommentThread]],
    canMakePublicComment: Boolean
  ) = {
    val interestedThreadIds = threadsOpt
      .map(_.map(_.id))
      .getOrElse(List.empty)
      .toSet

    val newIssueIds = unseenNotifications
      .filter {
        _.model.notificationAction.isFormCommentAddComment
      }
      .flatMap { item =>
        val sealedValue = item.model.notificationData.asMessage.sealedValue
        target match {
          case CommentTarget.AmlKycDocument =>
            sealedValue.amlKycCommentNotificationData.map { amlKyc =>
              item.id -> amlKyc.issueId
            }
          case CommentTarget.Form =>
            sealedValue.formCommentNotificationData.map { formComment =>
              item.id -> formComment.issueId
            }
        }
      }
      .map { case (notificationId, issueId) =>
        issueId -> notificationId
      }
      .groupBy(_._1)
      .toList
      .filter { case (id, _) =>
        interestedThreadIds.contains(id)
      }
      .map { case (issueId, group) =>
        issueId -> group.map(_._2)
      }
      .toMap

    val newCommentNotifications = unseenNotifications
      .filter {
        _.model.notificationAction.isFormCommentAddReply
      }
      .flatMap { item =>
        val sealedValue = item.model.notificationData.asMessage.sealedValue
        target match {
          case CommentTarget.AmlKycDocument =>
            sealedValue.amlKycCommentNotificationData.map { amlKyc =>
              item.id -> (amlKyc.issueId, amlKyc.commentIdOpt)
            }
          case CommentTarget.Form =>
            sealedValue.formCommentNotificationData.map { formComment =>
              item.id -> (formComment.issueId, formComment.commentIdOpt)
            }
        }
      }
      .filter { case (_, value) =>
        interestedThreadIds.contains(value._1)
      }

    val newCommentIds = newCommentNotifications
      .flatMap { case (notificationId, value) =>
        value._2.filter(_.nonEmpty).map(_ -> notificationId)
      }
      .groupBy(_._1)
      .toList
      .map { case (commentId, group) =>
        commentId -> group.map(_._2)
      }
      .toMap

    val issueWithNewCommentIds = newCommentNotifications
      .map { case (notificationId, value) =>
        value._1 -> notificationId
      }
      .groupBy(_._1)
      .toList
      .filter { case (id, _) =>
        interestedThreadIds.contains(id)
      }
      .map { case (issueId, group) =>
        issueId -> group.map(_._2)
      }
      .toMap ++ newIssueIds

    CommentsData(
      isLoading = threadsOpt.isEmpty,
      threads = threadsOpt.getOrElse(List.empty),
      newCommentIds = newCommentIds,
      newThreadIds = newIssueIds,
      issueWithNewCommentIds = issueWithNewCommentIds,
      canMakePublicComment = canMakePublicComment
    )
  }

  def apply(): HtmlElement = {
    FetchCommentSettingL(
      fundSubId = lpId.parent,
      renderChildren = settingSignal => {
        val isFormCommentEnabledSignal = settingSignal.map(_.exists(_.isFormCommentEnabled)).distinct
        val notificationSetting = settingSignal
          .map(_.map { setting =>
            CommentNotificationSetting(
              formCommentDigestEmailExceptionLps = setting.formCommentDigestEmailExceptionLps,
              investorEntity = setting.investorEntity,
              suppressSendingFormCommentDigestEmail = setting.suppressSendingFormCommentDigestEmail
            )
          })
          .distinct
        val inactiveThresholdSignal = settingSignal.map(_.flatMap(_.inactiveDaysThreshold))

        div(
          tw.hPc100.wPc100,
          FetchCommentNotificationsL(
            lpId = lpId,
            renderChildren = notificationsOpt => {
              FetchFormCommentsL(
                lpId = lpId,
                renderChildren = fetchResultSignal => {
                  val notificationsSignal = notificationsOpt.map(_.getOrElse(List.empty))
                  val commentsSignal =
                    notificationsSignal.combineWith(isFormCommentEnabledSignal, fetchResultSignal).distinct.mapN {
                      case (notifications, isFormCommentEnabled, fetchResultOpt) =>
                        if (isFormCommentEnabled) {
                          buildCommentData(
                            CommentTarget.Form,
                            notifications,
                            fetchResultOpt.map(_.threads),
                            canMakePublicComment = fetchResultOpt.exists(_.canMakePublicComment)
                          )
                        } else {
                          CommentsData()
                        }
                    }
                  renderChildren(
                    FormCommentProviderL.Comments(
                      isFormCommentEnabledSignal = isFormCommentEnabledSignal,
                      comments = commentsSignal,
                      notificationSetting = notificationSetting,
                      inactiveThresholdSignal = inactiveThresholdSignal
                    )
                  )
                }
              )()
            }
          )()
        )
      }
    )()
  }

}

object FormCommentProviderL {

  case class Comments(
    isFormCommentEnabledSignal: Signal[Boolean],
    comments: Signal[CommentsData],
    notificationSetting: Signal[Option[CommentNotificationSetting]],
    inactiveThresholdSignal: Signal[Option[Int]]
  )

}
