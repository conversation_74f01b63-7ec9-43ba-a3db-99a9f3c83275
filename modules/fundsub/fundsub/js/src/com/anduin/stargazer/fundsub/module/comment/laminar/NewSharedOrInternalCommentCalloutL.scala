package com.anduin.stargazer.fundsub.module.comment.laminar

import com.raquo.laminar.api.L.*
import design.anduin.style.tw.*

private[laminar] case class NewSharedOrInternalCommentCalloutL(isPublicSignal: Signal[Boolean]) {

  def apply(): HtmlElement = {
    div(
      child <-- isPublicSignal.splitBoolean(
        whenTrue = _ =>
          div(
            tw.bgWarning1.textWarning5.px20.py8,
            "Shared comments are visible to the investor"
          ),
        whenFalse = _ =>
          div(
            tw.bgGray3.px16.py8,
            "Internal comments are only visible to fund team members"
          )
      )
    )
  }

}
