// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.dashboard.lp.table

import com.raquo.airstream.core.Observer
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.PortalPosition
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import anduin.actionlogger.ActionEventLoggerJs
import anduin.fundsub.status.LpStatusSharedUtils
import anduin.id.fundsub.FundSubLpId
import anduin.model.common.user.UserId
import anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import design.anduin.components.wrapper.react.WrapperR
import design.anduin.components.wrapper.laminar.WrapperL
import com.raquo.laminar.api.L.*

import anduin.fundsub.endpoint.subscriptiondoc.SubscriptionVersionIndex
import anduin.fundsub.endpoint.dashboard.LpDashboardItem
import anduin.fundsub.endpoint.group.FundSubGroupRoleType
import com.anduin.stargazer.fundsub.module.lp.v2.shared.FetchSubscriptionVersionInfo
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils

private[dashboard] final case class LpFillingProgress(
  lpId: FundSubLpId,
  status: LpStatus,
  firmNameOrLpName: String,
  progress: Float,
  currentUserId: UserId,
  userGroupRole: FundSubGroupRoleType,
  customRenderTarget: Option[(Callback, Boolean) => VdomNode] = None,
  isTrackingEvent: Boolean = false,
  lpItemOpt: Option[LpDashboardItem]
) {
  def apply(): VdomElement = LpFillingProgress.component(this)
}

private[dashboard] object LpFillingProgress {

  private type Props = LpFillingProgress

  private final case class State()

  private case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props): VdomElement = {

      StopClickEventWrapper(
        PopoverR(
          position = PortalPosition.RightTop,
          renderTarget = props.customRenderTarget.getOrElse((open, _) =>
            <.div(
              tw.flex.itemsCenter.cursorPointer,
              ^.onClick --> (
                Callback.when(props.isTrackingEvent)(
                  ActionEventLoggerJs.logPageViewCb(subPage =
                    Some(
                      s"${FundSubCopyUtils.getFlowTerm.termInDocumentContext.split(' ').map(_.capitalize).mkString(" ")} Column - View Form In Progress Status"
                    )
                  )
                ) >> open
              ),
              <.div(
                tw.borderAll.border1.borderPrimary1.rounded3,
                tw.hPx20.flex.itemsCenter.relative,
                <.div(
                  tw.absolute.hPx20.overflowHidden.bgPrimary1.rounded3,
                  ^.width := s"${(props.progress * 100).toInt}%"
                ),
                <.div(
                  tw.fontSemiBold.text11.textPrimary5.px4.truncate,
                  ^.zIndex := "1",
                  LpStatusSharedUtils.getStatusName(props.status)
                )
              ),
              <.div(
                tw.ml4.text11.fontSemiBold.leading16.textPrimary5,
                s"${(props.progress * 100).toInt}%"
              )
            )
          ),
          renderContent = close =>
            React.Fragment(
              WrapperR(
                node = {
                  FetchSubscriptionVersionInfo(
                    lpId = props.lpId,
                    versionIndex = SubscriptionVersionIndex.Latest,
                    onVersionFetched = Observer.empty,
                    render = fetchLatestVersionData => {
                      val formVersionIndexSignal = fetchLatestVersionData.versionInfoOptSignal.map(_.map(_.versionIndex))
                      div(
                        child <-- formVersionIndexSignal
                          .map { formVersionIndexOpt =>
                            WrapperL(
                              LpFillingProgressDetail(
                                lpId = props.lpId,
                                status = props.status,
                                firmNameOrLpName = props.firmNameOrLpName,
                                progress = props.progress,
                                currentUserId = props.currentUserId,
                                userGroupRole = props.userGroupRole,
                                onDone = close,
                                lpItemOpt = props.lpItemOpt,
                                lastVersionIndexOpt = formVersionIndexOpt
                              )()
                            )
                          }
                      )
                    }
                  )()
                }
              )()
            )
        )()
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
