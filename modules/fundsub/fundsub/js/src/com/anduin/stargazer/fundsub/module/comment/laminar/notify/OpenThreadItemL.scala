// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.notify

import com.raquo.laminar.api.L.*
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.style.tw.*

import anduin.protobuf.fundsub.comment.{AmlKycDocData, FormQuestionData}
import anduin.stargazer.service.formcomment.FormCommentCommons.CommentThread

private[notify] case class OpenThreadItemL(
  thread: CommentThread
) {

  private def renderTargetDocument(docData: Option[AmlKycDocData]) = {
    val docType = docData.map(_.doctype).getOrElse("Untitled document")
    div(
      tw.flex.itemsStart,
      div(
        tw.mr8.textGray6,
        IconL(name = Val(Icon.Glyph.FileGeneric))()
      ),
      div(
        tw.flexFill.textSmall.textGray7,
        docType
      )
    )
  }

  private def renderTargetField(formQuestionData: Option[FormQuestionData]) = {
    val fieldDescription = formQuestionData.map(_.fieldDescription).getOrElse("")
    val tocSection = formQuestionData.map(_.tocSection).getOrElse("")
    div(
      tw.flex.itemsStart,
      div(
        tw.mr8.textGray6,
        IconL(name = Val(Icon.Glyph.Questionnaire))()
      ),
      div(
        tw.flexFill.textSmall.textGray7,
        Option.when(tocSection.nonEmpty) {
          div(
            tw.lineClamp1.fontSemiBold,
            tocSection
          )
        },
        Option.when(fieldDescription.nonEmpty) {
          div(
            tw.lineClamp1,
            fieldDescription
          )
        }
      )
    )
  }

  def apply(): HtmlElement = {
    val sealedValue = thread.topicData.asMessage.sealedValue
    SingleThreadItemL(
      thread = thread
    )(
      div(
        tw.mb8.p12.borderAll.border1.borderGray3.rounded4,
        if (sealedValue.isAmlKycDocData) {
          renderTargetDocument(sealedValue.amlKycDocData)
        } else {
          renderTargetField(sealedValue.formQuestionData)
        }
      )
    )
  }

}
