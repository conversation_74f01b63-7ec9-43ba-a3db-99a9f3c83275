// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.review.subdoc

import design.anduin.components.button.Button
import design.anduin.components.modal.{Modal, ModalBody, ModalFooterWCancel}
import design.anduin.components.toast.Toast
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.endpoint.subscriptiondoc.review.*
import anduin.id.entity.EntityId
import anduin.id.fundsub.FundSubId
import anduin.utils.StringUtils
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubSubscriptionDocReviewEndpointClient
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils

private[settings] final case class DisableSubscriptionDocReviewFlowConfirmationModal(
  fundId: FundSubId,
  onClose: Callback,
  onDisable: FundSubSubscriptionDocReviewConfig => Callback,
  reviewType: FundSubSubscriptionDocReviewType,
  entityId: EntityId,
  shouldSubmitForReviewBeforeSign: Boolean
) {
  def apply(): VdomElement = DisableSubscriptionDocReviewFlowConfirmationModal.component(this)
}

private[settings] object DisableSubscriptionDocReviewFlowConfirmationModal {
  private type Props = DisableSubscriptionDocReviewFlowConfirmationModal

  private final case class State(
    isUpdating: Boolean = false,
    isGettingPendingSubscriptionInReview: Boolean = false,
    pendingSubscriptionsInReview: Seq[InvestorPendingReviewInfo] = Seq.empty
  )

  private final case class Backend(scope: BackendScope[Props, State]) {
    val disableText = "Disable review workflow"

    def render(props: Props, state: State): VdomElement = {
      React.Fragment(
        ModalBody()(
          if (props.reviewType == FundSubSubscriptionDocReviewType.SignedSubscription) {
            <.div(
              s"Are you sure you want to disable the signed ${FundSubCopyUtils.getFlowTerm.standAloneTerm} review? This'll remove the \"Pending approval\" step from ${FundSubCopyUtils.getFlowTerm.standAloneTerm}s."
            )
          } else {
            <.div(
              s"Are you sure you want to disable the unsigned ${FundSubCopyUtils.getFlowTerm.standAloneTerm} review? This'll remove the \"Pending review\" step from ${FundSubCopyUtils.getFlowTerm.standAloneTerm}s",
              TagMod.when(props.shouldSubmitForReviewBeforeSign) {
                <.span(", and investors won't be required to submit documents before they're able to sign.")
              }
            )
          }
        ),
        ModalFooterWCancel(props.onClose)(
          if (state.isGettingPendingSubscriptionInReview) {
            Button(
              style = Button.Style.Full(
                color = Button.Color.Danger,
                isBusy = true
              )
            )(disableText)
          } else {
            if (state.pendingSubscriptionsInReview.isEmpty) {
              Button(
                style = Button.Style.Full(
                  color = Button.Color.Danger,
                  isBusy = state.isUpdating
                ),
                onClick = disableReviewFlow
              )(disableText)
            } else {
              renderDisplaySubscriptionDocInReviewModal(props, state)
            }
          }
        )
      )
    }

    private def renderDisplaySubscriptionDocInReviewModal(props: Props, state: State) = {
      Modal(
        title = s"There ${
            if (state.pendingSubscriptionsInReview.length > 1) { "are" }
            else { "is" }
          } ${StringUtils
            .pluralItem(state.pendingSubscriptionsInReview.length, s"${FundSubCopyUtils.getFlowTerm.standAloneTerm}")} in review",
        renderTarget = onClick =>
          Button(
            style = Button.Style.Full(
              color = Button.Color.Danger,
              isBusy = state.isUpdating
            ),
            onClick = onClick
          )(disableText),
        renderContent = onClose =>
          DisplaySubscriptionDocInReviewModal(
            fundId = props.fundId,
            entityId = props.entityId,
            onClose = onClose,
            reviewType = props.reviewType
          )()
      )()
    }

    def getSubscriptionsInReview: Callback = {
      val cb = for {
        props <- scope.props
        _ <- ZIOUtils.toReactCallback {
          FundSubSubscriptionDocReviewEndpointClient
            .getInvestorsPendingReview(
              GetInvestorsPendingReviewParams(
                fundSubId = props.fundId,
                reviewType = props.reviewType
              )
            )
            .map(
              _.fold(
                ex =>
                  scope.modState(
                    _.copy(isGettingPendingSubscriptionInReview = false),
                    Toast.errorCallback(s"Failed to get pending review flow: ${ex.message}")
                  ),
                response =>
                  scope.modState(
                    _.copy(
                      isGettingPendingSubscriptionInReview = false,
                      pendingSubscriptionsInReview = response
                    )
                  )
              )
            )
        }
      } yield ()

      scope.modState(
        _.copy(isGettingPendingSubscriptionInReview = true),
        cb
      )
    }

    private def disableReviewFlow = {
      val cb = for {
        props <- scope.props
        _ <- ZIOUtils.toReactCallback {
          FundSubSubscriptionDocReviewEndpointClient
            .saveFundSubSubscriptionDocReviewConfig(
              SaveSubscriptionDocReviewConfigParams(
                fundId = props.fundId,
                mode = FundSubSubscriptionDocReviewConfigMode.Disabled,
                reviewType = props.reviewType
              )
            )
            .map(
              _.fold(
                ex =>
                  scope.modState(
                    _.copy(isUpdating = false),
                    Toast.errorCallback(s"Failed to disable review flow: ${ex.message}")
                  ),
                response =>
                  scope.modState(
                    _.copy(isUpdating = false),
                    Toast.successCallback(props.reviewType match {
                      case FundSubSubscriptionDocReviewType.UnsignedSubscription =>
                        s"Unsigned ${FundSubCopyUtils.getFlowTerm.standAloneTerm} review disabled"
                      case FundSubSubscriptionDocReviewType.SignedSubscription =>
                        s"Signed ${FundSubCopyUtils.getFlowTerm.standAloneTerm} review disabled"
                    }) >> props.onDisable(response)
                  )
              )
            )
        }
      } yield ()

      scope.modState(
        _.copy(isUpdating = true),
        cb
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .componentDidMount(scope => scope.backend.getSubscriptionsInReview)
    .build

}
