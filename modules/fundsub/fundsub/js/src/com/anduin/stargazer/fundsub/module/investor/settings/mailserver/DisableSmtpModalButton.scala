// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.mailserver

import design.anduin.components.button.Button
import design.anduin.components.modal.{Modal, ModalBody, ModalFooterWCancel}
import design.anduin.components.switcher.react.SwitcherR
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.email.CustomSmtpServerConfig
import anduin.fundsub.endpoint.admin.UpdateSmtpConfigParams
import anduin.id.fundsub.FundSubId
import anduin.switch.SwitchConfig
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

private[mailserver] final case class DisableSmtpModalButton(
  fundSubId: FundSubId,
  config: SwitchConfig[CustomSmtpServerConfig],
  onSave: SwitchConfig[CustomSmtpServerConfig] => Callback
) {
  def apply(): VdomElement = DisableSmtpModalButton.component(this)
}

private[mailserver] object DisableSmtpModalButton {
  private type Props = DisableSmtpModalButton

  private final case class State(
    isSaving: Boolean = false
  )

  private final class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      Modal(
        renderTarget = onClick =>
          SwitcherR(
            isChecked = true,
            onChange = _ => onClick
          )(),
        title = "Disable Custom SMTP Server?",
        renderContent = onClose =>
          <.div(
            ModalBody()(
              <.div(
                <.div(
                  "When you disable the custom SMTP server, newly sent out email will come from default email server."
                ),
                <.div(
                  tw.mt32,
                  "Do you want to continue?"
                )
              )
            ),
            ModalFooterWCancel(onClose)(
              Button(
                style = Button.Style.Full(
                  color = Button.Color.Primary,
                  isBusy = state.isSaving
                ),
                onClick = onDisable
              )("Confirm")
            )
          )
      )()
    }

    private def onDisable = {
      val cb = for {
        props <- scope.props
        config = props.config.copy(enabled = false)
        _ <- ZIOUtils.toReactCallback(
          FundSubEndpointClient
            .updateSmtpConfig(
              UpdateSmtpConfigParams(
                fundSubId = props.fundSubId,
                smptConfig = config
              )
            )
            .map(
              _.fold(
                ex => scope.modState(_.copy(isSaving = false), Toast.errorCallback(s"Failed to disable SMTP server: $ex")),
                _ =>
                  scope.modState(
                    _.copy(isSaving = false),
                    props.onSave(config) >> Toast.successCallback(s"Disabled SMTP server successfully")
                  )
              )
            )
        )
      } yield ()
      scope.modState(
        _.copy(isSaving = true),
        cb
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
