// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.review.supportingdoc

import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.supportingdoc.v2.UpdatePreventInvestorUploadSupportingDocAfterCountersignedParams
import anduin.id.fundsub.FundSubId
import anduin.protobuf.fundsub.FeatureSwitch
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.SupportingDocEndpointClient
import com.anduin.stargazer.fundsub.module.investor.settings.FundSubSettingSwitch
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils

private[settings] final case class PreventLpUploadSupportingDocAfterCountersignedSwitcher(
  fundSubId: FundSubId,
  featureSwitch: FeatureSwitch
) {
  def apply(): VdomElement = PreventLpUploadSupportingDocAfterCountersignedSwitcher.component(this)
}

private[settings] object PreventLpUploadSupportingDocAfterCountersignedSwitcher {
  private type Props = PreventLpUploadSupportingDocAfterCountersignedSwitcher

  private final case class State(
    isUpdating: Boolean = false,
    shouldPreventOpt: Option[Boolean] = None
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      val shouldPrevent =
        state.shouldPreventOpt.getOrElse(props.featureSwitch.preventLpUploadSupportingDocAfterCountersigned)
      FundSubSettingSwitch(
        enabled = shouldPrevent,
        onChange = checked => Callback.unless(state.isUpdating)(update(props, checked)),
        isDisabled = state.isUpdating,
        title = "Prevent investors from updating AML/KYC and other documents",
        description = () =>
          Option(
            <.div(
              s"Prevents investors from editing, uploading, and removing any documents after the ${FundSubCopyUtils.getFlowTerm.standAloneTerm} has been ",
              <.span(tw.fontSemiBold, "countersigned"),
              " or ",
              <.span(tw.fontSemiBold, "distributed"),
              "."
            )
          )
      )()
    }

    def update(props: Props, isChecked: Boolean): Callback = {
      val cb = for {
        _ <- ZIOUtils.toReactCallback {
          SupportingDocEndpointClient
            .updatePreventInvestorUploadSupportingDocAfterCountersigned(
              UpdatePreventInvestorUploadSupportingDocAfterCountersignedParams(
                fundSubId = props.fundSubId,
                shouldPrevent = isChecked
              )
            )
            .map(
              _.fold(
                _ => Toast.errorCallback(s"Failed to update config"),
                _ => Toast.successCallback(s"Fund setting update")
              )
            )
        }
        _ <- scope.modState(_.copy(isUpdating = false))
      } yield ()

      scope.modState(_.copy(isUpdating = true, shouldPreventOpt = Option(isChecked)), cb)
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
