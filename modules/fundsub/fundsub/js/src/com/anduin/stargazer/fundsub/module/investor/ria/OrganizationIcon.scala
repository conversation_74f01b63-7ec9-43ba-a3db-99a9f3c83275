// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.ria

import com.raquo.laminar.api.L.*

private[ria] case class OrganizationIcon() {

  def apply(): SvgElement = {
    svg.svg(
      svg.width := "48",
      svg.height := "39",
      svg.viewBox := "0 0 48 39",
      svg.fill := "none",
      svg.path(
        svg.d := "M38.4407 18.7114C41.642 18.7114 44.2373 16.1162 44.2373 12.9148C44.2373 9.71339 41.642 7.11816 38.4407 7.11816C35.2393 7.11816 32.644 9.71339 32.644 12.9148C32.644 16.1162 35.2393 18.7114 38.4407 18.7114Z",
        svg.fill := "#0478ED"
      ),
      svg.path(
        svg.d := "M48 28.5765C48 27.9663 48 28.2714 48 27.2544C48 26.2375 47.7966 25.3222 47.2881 24.5087C45.661 21.8646 42.3051 20.8477 38.4407 20.8477C34.5763 20.8477 31.1186 21.8646 29.5932 24.5087C29.0847 25.3222 28.8813 26.3392 28.8813 27.2544C28.8813 28.5765 28.8813 34.068 28.8813 36.7121L48 28.5765Z",
        svg.fill := "#0478ED"
      ),
      svg.path(
        svg.d := "M9.55931 18.7114C12.7607 18.7114 15.3559 16.1162 15.3559 12.9148C15.3559 9.71339 12.7607 7.11816 9.55931 7.11816C6.35793 7.11816 3.7627 9.71339 3.7627 12.9148C3.7627 16.1162 6.35793 18.7114 9.55931 18.7114Z",
        svg.fill := "#D9DCE1"
      ),
      svg.path(
        svg.d := "M9.55932 20.8477C5.59322 20.8477 2.23729 21.8646 0.711865 24.5087C0.203391 25.3222 0 26.3392 0 27.2544C0 28.2714 0 27.9663 0 28.5765L19.2203 36.7121C19.2203 34.068 19.2203 28.5765 19.2203 27.2544C19.2203 26.2375 19.017 25.3222 18.5085 24.5087C16.8814 21.8646 13.5254 20.8477 9.55932 20.8477Z",
        svg.fill := "#D9DCE1"
      ),
      svg.path(
        svg.d := "M24 15.0509C28.1562 15.0509 31.5255 11.6816 31.5255 7.52543C31.5255 3.36925 28.1562 0 24 0C19.8439 0 16.4746 3.36925 16.4746 7.52543C16.4746 11.6816 19.8439 15.0509 24 15.0509Z",
        svg.fill := "#56AAFF"
      ),
      svg.path(
        svg.d := "M24.0001 38.746L36.6102 33.4579C36.6102 31.1189 36.6102 27.8647 36.6102 26.3393C36.6102 24.9155 36.2035 23.4918 35.3899 22.2715C33.2543 19.1189 28.9831 17.7969 24.0001 17.7969C19.017 17.7969 14.7458 19.1189 12.6102 22.2715C11.7967 23.4918 11.3899 24.9155 11.3899 26.3393C11.3899 27.8647 11.3899 31.0172 11.3899 33.3562L24.0001 38.746Z",
        svg.fill := "#56AAFF"
      )
    )
  }

}
