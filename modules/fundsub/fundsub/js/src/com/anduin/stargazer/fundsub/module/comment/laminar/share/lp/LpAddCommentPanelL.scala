// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.share.lp

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.divider.Divider
import design.anduin.components.divider.laminar.DividerL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*

import anduin.id.fundsub.FundSubLpId
import com.anduin.stargazer.fundsub.module.comment.laminar.share.NewCommentBoxL

private[laminar] case class LpAddCommentPanelL(
  lpIdSignal: Signal[FundSubLpId],
  isCommentMentioningEnabledSignal: Signal[Boolean],
  initialComment: String = "",
  onAddComment: Observer[NewCommentBoxL.AddCommentData],
  onSwitchAllComments: Observer[Unit],
  onClose: Observer[Unit]
) {

  private def renderAddNewComment =
    div(
      tw.bgGray0,
      ComponentUtils.testIdL("CommentBoxContainer"),
      tw.p16,
      NewCommentBoxL(
        lpIdSignal = lpIdSignal,
        initialComment = initialComment,
        isPublicSignal = Val(true),
        isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
        newCommentBoxView = NewCommentBoxL.View.InvestorSideSharedCommentView,
        onAddComment = onAddComment,
        enableLpAutoNotificationOptSignal = Val(None),
        isDrawerViewSignal = Val(true)
      )()
    )

  private def renderAddFirstCommentMessage =
    div(
      ComponentUtils.testIdL("AddFirstCommentMessage"),
      tw.flexFill.overflowAuto,
      div(
        tw.hPx256,
        tw.flex.itemsCenter.justifyCenter.textCenter.px48,
        NonIdealStateL(
          icon = {
            div(
              tw.textGray4,
              IconL(name = Val(Icon.Glyph.CommentLine), size = Icon.Size.Custom(px = 48))()
            )
          },
          title = "No comments yet",
          description = "Leave a comment for the fund and your " +
            "collaborators if " +
            "any"
        )()
      )
    )

  def apply(): HtmlElement = {
    div(
      ComponentUtils.testIdL("LPCommentPanel"),
      tw.hPc100.flex.flexCol,
      renderHeaderRow,
      div(
        tw.flex1.overflowAuto,
        renderAddFirstCommentMessage
      ),
      renderAddNewComment
    )
  }

  private def renderHeaderRow = {
    div(
      tw.flex.itemsCenter,
      tw.borderGray3.border1.borderBottom.p16,
      renderBackToAllComments,
      renderCloseButtonForMobile
    )
  }

  private def renderBackToAllComments = {
    div(
      TooltipL(
        renderContent = _.amend("Back to all comments"),
        renderTarget = {
          ButtonL(
            testId = "BackToAllCommentsButton",
            style = ButtonL.Style.Minimal(icon = Option(Icon.Glyph.ChevronLeft)),
            onClick = onSwitchAllComments.contramap(_ => ())
          )()
        }
      )()
    )
  }

  private def renderCloseButtonForMobile = {
    // Close button that is only shown in small screens
    div(
      tw.block,
      tw.sm(tw.hidden),
      div(
        tw.flex.itemsCenter,
        DividerL(direction = Divider.Direction.Vertical)(),
        ButtonL(
          testId = "CloseCommentsThreadButton",
          style = ButtonL.Style.Full(
            height = ButtonL.Height.Fix32,
            icon = Option(Icon.Glyph.Cross)
          ),
          onClick = onClose.contramap(_ => ())
        )()
      )
    )
  }

}
