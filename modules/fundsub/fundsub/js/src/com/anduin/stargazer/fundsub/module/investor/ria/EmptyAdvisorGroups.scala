// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.ria

import anduin.id.fundsub.FundSubId
import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.nonidealstate.laminar.NonIdealStateL

private[ria] case class EmptyAdvisorGroups(
  fundSubId: FundSubId,
  onDoneAddAdvisorGroup: Observer[Unit]
) {

  def apply(): HtmlElement = {
    div(
      paddingBottom.px(150),
      paddingTop.px(150),
      NonIdealStateL(
        icon = OrganizationIcon()(),
        title = "No advisor entity to show",
        description = "Click below to start inviting an advisor entity",
        action = {
          AddAdvisorGroupModal(
            fundSubId = fundSubId,
            existingGroupNameSignal = Val(Seq()),
            renderTarget = onOpen => {
              ButtonL(
                style = ButtonL.Style.Full(color = ButtonL.Color.Primary, icon = Option(Icon.Glyph.Plus)),
                onClick = onOpen.contramap(_ => ())
              )("Invite advisor entity")
            },
            onDoneAddAdvisorGroup = onDoneAddAdvisorGroup
          )()
        }
      )()
    )
  }

}
