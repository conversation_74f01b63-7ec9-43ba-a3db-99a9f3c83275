// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent

import com.raquo.laminar.api.L.*
import design.anduin.components.toast.Toast
import design.anduin.components.util.ComponentUtils
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.formcomment.{GetCommentAnchorPointAssigneeParams, GetCommentAnchorPointAssigneeResponse}
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.protobuf.fundsub.comment.TopicData
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

private[laminar] case class FetchCommentAnchorPointAssigneeL(
  fundSubId: FundSubId,
  lpIdSignal: Signal[FundSubLpId],
  topicDataSignal: Signal[TopicData],
  renderChildren: FetchCommentAnchorPointAssigneeL.RenderChildren => HtmlElement
) {

  private val fetchCommentsEventBus = new EventBus[Boolean] // should set isFetching

  private val getCommentAssigneeRespOptVar: Var[Option[GetCommentAnchorPointAssigneeResponse]] = Var(None)

  private val isFetchingVar: Var[Boolean] = Var(true)

  private val mountEventBus = new EventBus[Unit]

  private def fetchAssigneeInfo(setIsFetching: Boolean, lpId: FundSubLpId, topicData: TopicData) = {
    AirStreamUtils.taskToStream {
      for {
        _ <- ZIO.succeed(isFetchingVar.set(setIsFetching))
        response <- FundSubEndpointClient
          .getCommentAnchorPointAssignee(
            GetCommentAnchorPointAssigneeParams(
              lpId = lpId,
              topicData = topicData
            )
          )
          .map {
            _.fold(
              _ => {
                Toast.error("Failed to fetch comment anchor point assignee")
                Var.set(
                  isFetchingVar -> false,
                  getCommentAssigneeRespOptVar -> None
                )
              },
              response => {
                Var.set(
                  isFetchingVar -> false,
                  getCommentAssigneeRespOptVar -> Some(response)
                )
              }
            )
          }
      } yield response
    }
  }

  def apply(): HtmlElement = {
    renderChildren(
      FetchCommentAnchorPointAssigneeL.RenderChildren(
        dataSignal = isFetchingVar.signal
          .combineWith(getCommentAssigneeRespOptVar.signal)
          .map { case (isFetching, respOpt) =>
            FetchCommentAnchorPointAssigneeL.Data(
              isFetching,
              respOpt
            )
          },
        refetch = fetchCommentsEventBus.writer
      )
    ).amend(
      ComponentUtils.testIdL(FetchCommentAnchorPointAssigneeL),
      fetchCommentsEventBus.events
        .withCurrentValueOf(lpIdSignal, topicDataSignal)
        .flatMapSwitch(fetchAssigneeInfo) --> Observer.empty,
      EventStream
        .merge(
          lpIdSignal.changes.mapToUnit,
          topicDataSignal.changes.mapToUnit,
          mountEventBus.events
        )
        .throttle(100)
        .mapTo(true)
        --> fetchCommentsEventBus,
      onMountCallback(_ => {
        mountEventBus.emit(())
      })
    )
  }

}

private[laminar] object FetchCommentAnchorPointAssigneeL {

  case class Data(
    isFetching: Boolean,
    data: Option[GetCommentAnchorPointAssigneeResponse]
  )

  object Data {

    val empty: Data = Data(
      isFetching = false,
      data = None
    )

  }

  case class RenderChildren(dataSignal: Signal[Data], refetch: Observer[Boolean])
}
