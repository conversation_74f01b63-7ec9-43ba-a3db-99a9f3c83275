// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.review.supportingdoc

import com.raquo.laminar.api.L
import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.{ComponentUtils, NodeListSeq}
import design.anduin.components.wrapper.react.MountWrapperR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import org.scalajs.dom.HTMLElement

import anduin.facades.core.sortablejs.mod
import anduin.facades.core.sortablejs.mod.{GroupOptions, Options, Sortable}

private[settings] final case class ManageSupportingDocumentGroup(
  items: Seq[(String, Int)] = Seq.empty,
  onChange: List[Int] => Callback,
  onRemoveOpt: Option[Int => Callback],
  onEnd: Callback = Callback.empty,
  onStart: Callback,
  shouldShowInstruction: <PERSON>olean,
  shouldHide: Boolean = false
) {
  def apply(): VdomElement = ManageSupportingDocumentGroup.component(this)
}

private[settings] object ManageSupportingDocumentGroup {
  private type Props = ManageSupportingDocumentGroup

  // The CSS class of placeholder element
  private val GhostCssClass = "sortable-ghost"

  private case class Backend(scope: BackendScope[Props, Unit]) {

    private val sortableVar = L.Var[Option[Sortable]](None)

    def render(props: Props): VdomNode = {
      MountWrapperR(
        onMount = handleMount(props, _),
        onUnmount = _ => handleUnmount(),
        renderChildren = renderProps => {
          <.div.withOptionalRef(Option(renderProps.ref))(
            tw.hPc100.spaceY8,
            Option.when(props.shouldShowInstruction) {
              <.div(
                ComponentUtils.testId("DropTarget"),
                tw.flex.justifyCenter.borderAll.borderPrimary3.borderDashed.textPrimary4.rounded4,
                ^.padding := "10px",
                "Drop document type here"
              )
            },
            props.items.toVdomArray(
              using { case (item, key) =>
                <.div(
                  // The `key` property makes sure the items order are kept after users drag and drop them
                  ^.key := key,
                  ComponentUtils.testId(ManageSupportingDocumentGroup, "Row"),
                  VdomAttr("document") := key,
                  if (props.shouldHide) tw.hidden else TagMod.empty,
                  tw.flex.itemsCenter.justifyCenter.cursorPointer,
                  tw.borderAll.borderGray3.bgGray1.rounded4,
                  tw.p8.spaceX8,
                  <.div(
                    tw.textGray7,
                    IconR(name = Icon.Glyph.Drag)()
                  ),
                  <.div(
                    ComponentUtils.testId(ManageSupportingDocumentGroup, "Name"),
                    tw.flexFill,
                    item
                  ),
                  if (props.onRemoveOpt.nonEmpty) {
                    TooltipR(
                      renderTarget = Button(
                        testId = "Remove",
                        style = Button.Style.Minimal(
                          icon = Some(Icon.Glyph.Cross),
                          height = Button.Height.Fix20
                        ),
                        onClick = props.onRemoveOpt.fold(Callback.empty) { onRemove =>
                          onRemove(key)
                        }
                      )(),
                      renderContent = _("Unassign")
                    )()
                  } else {
                    EmptyVdom
                  }
                )
              }
            )
          )
        }
      )()
    }

    private def handleChange(
      props: Props,
      additionalAction: Callback = Callback.empty,
      container: HTMLElement
    ): Unit = {
      val keys = NodeListSeq(container.querySelectorAll("[document]"))
        .map(_.asInstanceOf[HTMLElement].getAttribute("document")) // scalafix:ok DisableSyntax.asInstanceOf
        .flatMap(_.toIntOption)
        .toList
      additionalAction.runNow()
      props.onChange(keys).runNow()
    }

    private def handleMount(props: Props, container: HTMLElement) = {
      Callback {
        val options = Options()
          .setGhostClass(GhostCssClass)
          .setGroup(GroupOptions("shared"))
          .setDragClass("sortable-drag")
          .setAnimation(100)
          .setInvertSwap(true)
          .setSwapThreshold(0.4)
          .setOnAdd(event =>
            handleChange(
              props = props,
              container = event.to
            )
          )
          .setOnRemove(event =>
            handleChange(
              props = props,
              additionalAction = Callback(container.append(event.item)),
              container = event.from
            )
          )
          .setOnStart(_ => props.onStart.runNow())
          .setOnEnd(_ => props.onEnd.runNow())
          .setOnChange(_ => props.onEnd.runNow())
        val sortable = design.anduin.facades.sortablejs.SortableJsFacades.create(container, options)
        sortableVar.set(Option(sortable))
      }
    }

    private def handleUnmount() = {
      Callback {
        sortableVar.now().foreach(_.destroy())
      }
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .renderBackend[Backend]
    .build

}
