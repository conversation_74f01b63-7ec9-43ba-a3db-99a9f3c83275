// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.review.supportingdoc

import design.anduin.components.toast.Toast
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.id.fundsub.FundSubId
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubSubscriptionDocReviewEndpointClient
import com.anduin.stargazer.fundsub.module.investor.settings.review.model.AdminAndInvestorGroupInfo
import com.anduin.stargazer.fundsub.module.investor.settings.review.supportingdoc.AdminGroupDetailProvider.Renderer

private[settings] final case class AdminGroupDetailProvider(
  fundId: FundSubId,
  renderer: Renderer
) {
  def apply(): VdomElement = AdminGroupDetailProvider.component(this)
}

private[settings] object AdminGroupDetailProvider {
  private type Props = AdminGroupDetailProvider
  private type Renderer = AdminAndInvestorGroupInfo => VdomElement

  final case class State(
    adminAndInvestorGroupInfo: Option[AdminAndInvestorGroupInfo] = None
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      state.adminAndInvestorGroupInfo.fold(
        props.renderer(
          AdminAndInvestorGroupInfo(
            isLoading = true,
            fundId = props.fundId
          )
        )
      ) { adminAndInvestorGroupInfo =>
        props.renderer(adminAndInvestorGroupInfo)
      }
    }

    def fetchData: Callback = {
      for {
        props <- scope.props
        _ <- ZIOUtils.toReactCallback {
          FundSubSubscriptionDocReviewEndpointClient
            .getReviewConfigRelatedInfo(props.fundId)
            .map(
              _.fold(
                ex => Toast.errorCallback(s"Failed to load review config related info: ${ex.message}"),
                reviewRelatedInfo =>
                  scope.modState(
                    _.copy(
                      adminAndInvestorGroupInfo = Option(
                        AdminAndInvestorGroupInfo(
                          fundId = props.fundId,
                          adminGroups = reviewRelatedInfo.adminGroups,
                          investorGroups = reviewRelatedInfo.investorGroups,
                          adminUserInfos = reviewRelatedInfo.adminUserInfos
                        )
                      )
                    )
                  )
              )
            )
        }
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .componentDidMount(_.backend.fetchData)
    .build

}
