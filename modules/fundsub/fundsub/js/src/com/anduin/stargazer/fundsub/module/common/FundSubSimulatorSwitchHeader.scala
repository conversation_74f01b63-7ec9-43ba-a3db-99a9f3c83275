// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.common

import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.menu.react.{MenuItemR, MenuR}
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.PortalPosition
import design.anduin.components.responsive.ScreenWidth
import design.anduin.components.responsive.react.WithScreenWidthR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.appnavigation.routing.CrossAppRouter
import anduin.id.entity.EntityId
import anduin.id.fundsub.FundSubLpId
import anduin.id.offering.OfferingId
import anduin.protobuf.fundsub.DemoInfo
import com.anduin.stargazer.fundsub.module.investor.WithFundSubPublicModel
import com.anduin.stargazer.service.account.WithCurrentUserInfo
import stargazer.component.routing.react.WithReactRouterR
import stargazer.model.routing.{DynamicAuthPage, StaticAuthPage}

case class FundSubSimulatorSwitchHeader(
  currLpIdOpt: Option[FundSubLpId],
  demoFundAdminEntityId: EntityId,
  demoInfo: DemoInfo,
  shouldHideSwitchHeaderInSmallScreen: Boolean = false,
  shouldShowAllRoles: Boolean = false // For Navigator in FS Dashboard
) {
  def apply(): VdomElement = FundSubSimulatorSwitchHeader.component(this)

  private lazy val isInMainFundSub = currLpIdOpt.forall(_.parent == demoInfo.mainDemoFundSubId)

}

object FundSubSimulatorSwitchHeader {
  private type Props = FundSubSimulatorSwitchHeader

  enum Role(val name: String) {
    case FundManager extends Role("a fund manager")
    case MainInvestor extends Role("an investor")
    case MainInvestorWithEmptyForm extends Role("an investor with empty form")
    case IdmInvestor extends Role("an investor with prefilled form")
    case IaInvestor extends Role("an investor with profile")
    case Empty extends Role("")
  }

  case class RoleAndLink(role: Role, url: DynamicAuthPage.FundSubPage)

  private def isAnduinEmail(email: String) = email.trim.endsWith("@anduintransact.com")

  private def render(props: Props) = {
    if (!props.isInMainFundSub) {
      WithFundSubPublicModel(props.demoInfo.mainDemoFundSubId, loadingElement = <.div()) { (fundSubModel, _) =>
        <.div(fundSubModel.demoInfo.map { mainDemoInfo =>
          renderNavigator(
            currLpIdOpt = props.currLpIdOpt,
            demoFundAdminEntityId = props.demoFundAdminEntityId,
            mainDemoInfo = mainDemoInfo,
            additionalDemoInfo = Some(props.demoInfo),
            shouldShowAllRoles = props.shouldShowAllRoles,
            shouldHideSwitchHeaderInSmallScreen = props.shouldHideSwitchHeaderInSmallScreen
          )
        })
      }
    } else {
      props.demoInfo.additionalDemoFundSubIds.headOption.fold(
        renderNavigator(
          currLpIdOpt = props.currLpIdOpt,
          demoFundAdminEntityId = props.demoFundAdminEntityId,
          mainDemoInfo = props.demoInfo,
          additionalDemoInfo = None,
          shouldShowAllRoles = props.shouldShowAllRoles,
          shouldHideSwitchHeaderInSmallScreen = props.shouldHideSwitchHeaderInSmallScreen
        )
      ) { additionalDemoFundSubId =>
        WithFundSubPublicModel(additionalDemoFundSubId, loadingElement = <.div()) { (fundSubModel, _) =>
          renderNavigator(
            currLpIdOpt = props.currLpIdOpt,
            demoFundAdminEntityId = props.demoFundAdminEntityId,
            mainDemoInfo = props.demoInfo,
            additionalDemoInfo = fundSubModel.demoInfo,
            shouldShowAllRoles = props.shouldShowAllRoles,
            shouldHideSwitchHeaderInSmallScreen = props.shouldHideSwitchHeaderInSmallScreen
          )
        }
      }

    }

  }

  private def renderNavigator(
    currLpIdOpt: Option[FundSubLpId],
    demoFundAdminEntityId: EntityId,
    mainDemoInfo: DemoInfo,
    additionalDemoInfo: Option[DemoInfo],
    shouldShowAllRoles: Boolean,
    shouldHideSwitchHeaderInSmallScreen: Boolean
  ) = {
    val fundManagerRoleAndLink = RoleAndLink(
      Role.FundManager,
      DynamicAuthPage.FundSubAdminPage(demoFundAdminEntityId, mainDemoInfo.mainDemoFundSubId)
    )
    val mainInvestorRoleAndLink = RoleAndLink(
      Role.MainInvestor,
      DynamicAuthPage.FundSubLpPage(mainDemoInfo.demoLpId)
    )
    val mainInvestorWithEmptyFormRoleAndLink = RoleAndLink(
      Role.MainInvestorWithEmptyForm,
      DynamicAuthPage.FundSubLpPage(mainDemoInfo.demoLpId)
    )
    val idmInvestorRoleAndLinkOpt = mainDemoInfo.additionalDemoLpIds.headOption.map {
      otherLpId => // Hard-coded the head of additional demo Lp for now
        RoleAndLink(
          Role.IdmInvestor,
          DynamicAuthPage.FundSubLpPage(otherLpId)
        )
    }
    val iaInvestorRoleAndLinkOpt = additionalDemoInfo.map { demoInfo => // Hard-coded
      RoleAndLink(
        Role.IaInvestor,
        DynamicAuthPage.FundSubLpPage(demoInfo.demoLpId)
      )
    }

    val allRoleAndLinks = List(
      Some(fundManagerRoleAndLink),
      if (additionalDemoInfo.isEmpty) Some(mainInvestorRoleAndLink) else Some(mainInvestorWithEmptyFormRoleAndLink),
      idmInvestorRoleAndLinkOpt,
      iaInvestorRoleAndLinkOpt
    ).flatten

    val currRole =
      if (shouldShowAllRoles) { Role.Empty }
      else if (currLpIdOpt.isEmpty) { Role.FundManager }
      else if (currLpIdOpt.contains(mainDemoInfo.demoLpId)) {
        if (additionalDemoInfo.isEmpty) Role.MainInvestor else Role.MainInvestorWithEmptyForm
      } else if (currLpIdOpt.exists(lpId => mainDemoInfo.additionalDemoLpIds.contains(lpId))) { Role.IdmInvestor }
      else if (currLpIdOpt.zip(additionalDemoInfo).exists((lpId, demoInfo) => lpId == demoInfo.demoLpId)) {
        Role.IaInvestor
      } else {
        Role.Empty
      } // Hard code the role base on demo-info, first. Can enhance to make it more generic when have more demo roles.

    val counterRoleAndLinks = allRoleAndLinks.filterNot(_.role == currRole)

    WithScreenWidthR(
      render = screenWidth =>
        <.div(
          tw.py8.bgGray7.textGray0.px16,
          tw.flex.flexCol.justifyCenter.itemsCenter,
          tw.md(tw.flexRow),
          if ((shouldHideSwitchHeaderInSmallScreen && screenWidth < ScreenWidth.Large)) tw.hidden else TagMod.empty,
          Option.when(currRole != Role.Empty) {
            <.div(
              tw.md(tw.mr16),
              s"You're currently viewing the product as ${currRole.name}"
            )
          },
          renderAction(
            mainDemoInfo,
            counterRoleAndLinks,
            screenWidth
          )
        )
    )()
  }

  private def renderAction(
    demoInfo: DemoInfo,
    counterRoleAndLinks: List[RoleAndLink],
    screenWidth: ScreenWidth
  ) = {
    val isExtraSmallScreen = screenWidth < ScreenWidth.Small
    val fundDataFirmPageOpt = demoInfo.demoFundDataFirmIdOpt.map { firmId =>
      CrossAppRouter.getRedirectPage(OfferingId.IDM, DynamicAuthPage.FundData.FundDataFund2Page(firmId))
    }
    val marketingDataRoomPageOpt = demoInfo.demoMarketingDataRoomWorkflowIdOpt.map { dataRoomWorkflowId =>
      CrossAppRouter.getRedirectPage(OfferingId.DataRoom, DynamicAuthPage.DataRoomDetailHomePage(dataRoomWorkflowId))
    }
    val riaFundGroupPageOpt = demoInfo.demoRiaFundGroupIdOpt.map { riaFundGroupId =>
      CrossAppRouter.getRedirectPage(OfferingId.Ria, DynamicAuthPage.Ria.RiaEntityFundPage(riaFundGroupId))
    }

    WithReactRouterR { router =>
      <.div(
        tw.flex.itemsCenter.mt8,
        tw.md(tw.mt0),
        if (counterRoleAndLinks.size <= 1) {
          <.div(
            counterRoleAndLinks.headOption.map { roleAndLink =>
              renderButton(
                title = s"View as ${roleAndLink.role.name}",
                onClick = router.set(roleAndLink.url),
                isExtraSmallScreen = isExtraSmallScreen,
                isDropdown = false
              )
            }
          )
        } else {
          PopoverR(
            renderTarget = (toggle, _) =>
              renderButton(
                title = "View as",
                onClick = toggle,
                isExtraSmallScreen = isExtraSmallScreen,
                isDropdown = true
              ),
            renderContent = { close =>
              MenuR()(
                counterRoleAndLinks.toReactFragment(
                  using { roleAndLink =>
                    MenuItemR(
                      onClick = close,
                      url = router.urlFor(roleAndLink.url).value,
                      openIn = MenuItemR.OpenInThisTab
                    )(roleAndLink.role.name)
                  }
                )
              )
            },
            position = PortalPosition.BottomLeft
          )()
        },
        <.div(
          WithCurrentUserInfo { userInfo =>
            val menuItems =
              List(
                Option.when(isAnduinEmail(userInfo.emailAddressStr)) { (close: Callback) =>
                  MenuItemR(
                    onClick = close,
                    url = router.urlFor(StaticAuthPage.FundSubSimulatorDashboardPage).value,
                    openIn = MenuItemR.OpenInThisTab
                  )("Dashboard")
                },
                fundDataFirmPageOpt.map { fundDataFirmPage => (close: Callback) =>
                  MenuItemR(
                    onClick = close,
                    url = router.urlFor(fundDataFirmPage).value,
                    openIn = MenuItemR.OpenInNewTab
                  )("Investor Data Management")
                },
                marketingDataRoomPageOpt.map { marketingDataRoomPage => (close: Callback) =>
                  MenuItemR(
                    onClick = close,
                    url = router.urlFor(marketingDataRoomPage).value,
                    openIn = MenuItemR.OpenInNewTab
                  )("Marketing Data Room")
                },
                riaFundGroupPageOpt.map { riaFundGroupPage => (close: Callback) =>
                  MenuItemR(
                    onClick = close,
                    url = router.urlFor(riaFundGroupPage).value,
                    openIn = MenuItemR.OpenInNewTab
                  )("Advisor Advantage")
                }
              ).flatten
            <.div(Option.when(menuItems.nonEmpty) {
              <.div(
                tw.flex.itemsCenter,
                <.span(
                  tw.mx16,
                  "or"
                ),
                PopoverR(
                  renderTarget = (toggle, _) =>
                    renderButton(
                      title = "Go to",
                      onClick = toggle,
                      isExtraSmallScreen = isExtraSmallScreen,
                      isDropdown = true
                    ),
                  renderContent = { close =>
                    MenuR()(
                      menuItems.toReactFragment(
                        using _.apply(close)
                      )
                    )
                  },
                  position = PortalPosition.BottomLeft
                )()
              )
            })
          }()
        )
      )
    }
  }

  private def renderButton(
    title: String,
    onClick: Callback,
    isExtraSmallScreen: Boolean,
    isDropdown: Boolean
  ) = {
    <.button(
      tw.flex.itemsCenter.px12.py6.textGray0.hover(tw.bgGray6),
      tw.borderAll.borderGray5.border1.rounded4.hover(tw.borderGray3),
      if (isExtraSmallScreen) tw.leading16 else TagMod.empty,
      ^.onClick --> onClick,
      <.div(
        tw.fontMedium,
        title
      ),
      Option.unless(isExtraSmallScreen) {
        <.div(
          tw.ml8,
          IconR(if (isDropdown) Icon.Glyph.CaretDown else Icon.Glyph.ArrowRight)()
        )
      }
    )
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
