// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.auditlog

import java.time.ZoneId
import scala.scalajs.js.Date

import design.anduin.components.button.Button
import design.anduin.components.dropdown.Dropdown
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.pagination.react.PaginationR
import design.anduin.components.progress.Skeleton
import design.anduin.components.progress.react.SkeletonR
import design.anduin.components.toast.Toast
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import anduin.model.id.TeamId
import anduin.actionlogger.ActionEventLoggerJs
import anduin.copy.Bundle.I18N
import anduin.fundsub.auditlog.AuditLogFilter
import anduin.fundsub.auditlog.AuditLogFilter.TimestampFilter
import anduin.fundsub.endpoint.admin.LpBasicInfo
import anduin.fundsub.endpoint.auditlog.{AuditLogData, ExportAuditLogDataParams, GetAuditLogDataParams}
import anduin.fundsub.endpoint.lp.NameEmailInfo
import anduin.id.fundsub.auditlog.FundSubAuditLogId
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.model.common.user.UserId
import anduin.utils.DateTimeUtils
import com.anduin.stargazer.client.services.file.FileJsClient
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.copy.WithFundSubCopyConfigR

private[settings] final case class AuditLogTable(
  fundSubId: FundSubId
) {

  def apply(): VdomElement = {
    <.div(
      WithFundSubCopyConfigR(
        renderer = copyConfig =>
          AuditLogTable.component(
            AuditLogTable.Props(
              fundSubId,
              copyConfig
            )
          )
      )()
    )
  }

}

private[settings] object AuditLogTable {

  case class Column(title: String, width: String, minWidth: String)

  val CollapseColumn: Column = Column(
    "",
    "36px",
    "36px"
  )

  val TimeColumn: Column = Column(
    "Time",
    "130px",
    "130px"
  )

  val ActorColumn: Column = Column(
    "Actor",
    "244px",
    "244px"
  )

  val ActionColumn: Column = Column(
    "Action",
    "280px",
    "280px"
  )

  val SummaryColumn: Column = Column(
    "Summary",
    "",
    "288px"
  )

  private final case class Props(
    fundSubId: FundSubId,
    copyConfig: I18N
  )

  private final case class State(
    auditLogEvents: Seq[AuditLogData] = Seq.empty,
    auditLogId: Option[FundSubAuditLogId] = None,
    totalPage: Option[Int] = None,
    currentPage: Int = 1,
    eventPerPage: Int = 20,
    totalEvent: Option[Int] = None,
    timezone: ZoneId = DateTimeUtils.defaultTimezone,
    userInfoMap: Map[UserId, NameEmailInfo] = Map.empty,
    gpTeamNameMap: Map[TeamId, String] = Map.empty,
    lpInfoMap: Map[FundSubLpId, LpBasicInfo] = Map.empty,
    filters: Seq[AuditLogFilter] = Seq.empty,
    timestampFilter: Option[TimestampFilter] = None,
    isExporting: Boolean = false,
    isFetchingEvent: Boolean = false
  )

  val EVENT_PER_PAGE_OPTS: Seq[Int] = Seq(
    10,
    20,
    50,
    100
  )

  private val EventPerPageDropdown = (new Dropdown[Int])()

  private final case class Backend(scope: BackendScope[Props, State]) {

    private def renderHeader() = {
      val columns = List(
        CollapseColumn,
        TimeColumn,
        ActorColumn,
        ActionColumn,
        SummaryColumn
      )
      <.div(
        tw.flex,
        columns.toVdomArray(
          using { column =>
            <.div(
              ComponentUtils.testId("Header-" + column.title),
              ^.key := s"AuditLogColumnHeader-${column.title}",
              ^.minWidth := column.minWidth,
              ^.width := column.width,
              <.div(
                tw.textGray7.fontSemiBold.m8.textLeft,
                column.title
              )
            )
          }
        )
      )
    }

    def render(props: Props, state: State): VdomElement = {
      <.div(
        ComponentUtils.testId(AuditLogTable),
        tw.spaceY16,
        <.div(
          tw.flex.itemsCenter.justifyBetween,
          AuditLogFilterSection(
            fundSubId = props.fundSubId,
            copyConfig = props.copyConfig,
            onApplyFilter = filters => {
              scope.modState(
                _.copy(
                  filters = filters._1,
                  timestampFilter = filters._2
                ),
                fetchAuditLogData(
                  page = 1,
                  eventPerPage = state.eventPerPage,
                  filters = filters._1,
                  timestampFilter = filters._2
                )
              )
            }
          )(),
          <.div(
            Button(
              testId = "Export",
              style = Button.Style.Full(icon = Some(Icon.Glyph.FileExport), isBusy = state.isExporting),
              onClick = exportAuditLogData(props, state)
            )("Export")
          )
        ),
        if (!state.isFetchingEvent && state.auditLogEvents.isEmpty) {
          renderEmptyEvent()
        } else {
          React.Fragment(
            renderTable(props, state),
            renderPagination(state)
          )
        }
      )
    }

    private def renderTableSkeletonRow() = {
      <.div(
        tw.flex,
        <.div(
          ^.height := "56px",
          tw.p8.flex.itemsCenter,
          <.div()
        ),
        <.div(
          ^.height := "56px",
          tw.p8.flex.itemsCenter,
          SkeletonR(
            height = "20px",
            width = "100px",
            shape = Skeleton.Shape.Rounded
          )()
        ),
        <.div(
          ^.height := "56px",
          tw.p8.flex.itemsCenter,
          SkeletonR(
            height = "20px",
            width = "232px",
            shape = Skeleton.Shape.Rounded
          )()
        ),
        <.div(
          ^.height := "56px",
          tw.p8.flex.itemsCenter,
          SkeletonR(
            height = "20px",
            width = "280px",
            shape = Skeleton.Shape.Rounded
          )()
        ),
        <.div(
          ^.height := "56px",
          tw.p8.flex.itemsCenter,
          SkeletonR(
            height = "20px",
            width = "288px",
            shape = Skeleton.Shape.Rounded
          )()
        )
      )
    }

    private def renderEmptyEvent() = {
      <.div(
        ^.marginTop := "154px",
        tw.wPc100.flex.justifyCenter,
        <.div(
          tw.flex.justifyCenter.spaceY20.flexCol.itemsCenter,
          <.div(
            tw.textGray4,
            IconR(name = Icon.Glyph.Search, size = Icon.Size.Custom(54))()
          ),
          <.div(
            tw.textGray7,
            "There are no actions found"
          )
        )
      )
    }

    private def renderTable(props: Props, state: State) = {
      <.div(
        tw.wPc100.borderAll.border1.borderGray3.bgGray0.rounded4,
        renderHeader(),
        <.div(
          if (state.isFetchingEvent) {
            Seq
              .fill(state.eventPerPage)(0)
              .toVdomArray(
                using { _ => renderTableSkeletonRow() }
              )
          } else {
            state.auditLogEvents.zipWithIndex.toVdomArray(
              using { case (event, index) =>
                AuditLogTableRow(
                  event = event,
                  index = index,
                  userInfoMap = state.userInfoMap,
                  gpTeamNameMap = state.gpTeamNameMap,
                  lpInfoMap = state.lpInfoMap,
                  copyConfig = props.copyConfig,
                  auditLogId = state.auditLogId,
                  timezone = state.timezone
                )()
              }
            )
          }
        )
      )
    }

    private def updateAndFetchAuditLogPage(page: Int) = {
      for {
        state <- scope.state
        _ <- scope.modState(
          _.copy(currentPage = page),
          fetchAuditLogData(
            page = page,
            eventPerPage = state.eventPerPage,
            timestampFilter = state.timestampFilter,
            filters = state.filters
          )
        )
      } yield ()
    }

    private def renderPagination(state: State) = {
      val render = for {
        totalPage <- state.totalPage
        totalEvent <- state.totalEvent
      } yield <.div(
        tw.flex.itemsCenter.justifyBetween.textGray7,
        s"${(state.currentPage - 1) * state.eventPerPage + 1} - ${Math
            .min(state.currentPage * state.eventPerPage, totalEvent)} of $totalEvent actions",
        PaginationR(
          totalPage = totalPage,
          currentPage = state.currentPage,
          onJumpToPage = page => {
            updateAndFetchAuditLogPage(page)
          }
        )(),
        <.div(
          tw.flex.itemsCenter,
          <.div(tw.mr8, "Events per page:"),
          EventPerPageDropdown(
            value = Some(state.eventPerPage),
            valueToString = _.toString,
            items = EVENT_PER_PAGE_OPTS.map(Dropdown.Item(_)),
            onChange = value =>
              scope.modState(
                _.copy(
                  eventPerPage = value,
                  currentPage = 1
                ),
                fetchAuditLogData(
                  page = 1,
                  eventPerPage = value,
                  timestampFilter = state.timestampFilter,
                  filters = state.filters
                )
              )
          )()
        )
      )
      render.getOrElse(<.div())
    }

    def fetchAuditLogData(
      page: Int,
      eventPerPage: Int,
      filters: Seq[AuditLogFilter] = Seq.empty,
      timestampFilter: Option[TimestampFilter] = Filter.defaultRangeFilterOption.filter
    ): Callback = {
      for {
        _ <- scope.modState(_.copy(isFetchingEvent = true))
        props <- scope.props
        _ <- ZIOUtils.toReactCallback {
          FundSubEndpointClient
            .getAuditLogData(
              GetAuditLogDataParams(
                fundSubId = props.fundSubId,
                offset = (page - 1) * eventPerPage,
                limitOpt = Option(eventPerPage),
                filters = filters,
                timestampFilterOpt = timestampFilter
              )
            )
            .map(
              _.fold(
                _ => Toast.errorCallback("Failed to get audit log data"),
                res =>
                  scope.modState(
                    _.copy(
                      auditLogEvents = Seq.empty,
                      auditLogId = Option(res.auditLogId),
                      currentPage = page,
                      totalPage = Option((res.totalEvent - 1) / eventPerPage + 1),
                      totalEvent = Option(res.totalEvent),
                      userInfoMap = res.userInfoMap,
                      gpTeamNameMap = res.gpTeamNameMap,
                      lpInfoMap = res.lpInfoMap,
                      timezone = res.timezone.getOrElse(DateTimeUtils.defaultTimezone)
                    ),
                    scope.modState(
                      _.copy(
                        auditLogEvents = res.events,
                        isFetchingEvent = false
                      )
                    )
                  )
              )
            )
        }
      } yield ()
    }

    private def exportAuditLogData(
      props: Props,
      state: State
    ) = {
      for {
        _ <- scope.modState(
          _.copy(isExporting = true),
          Toast.loadingCallback("Exporting...")
        )
        _ <- ActionEventLoggerJs.logPageViewCb(subPage = Some("Export audit log button"))
        _ <- ZIOUtils.toReactCallback {
          for {
            res <- FundSubEndpointClient.exportAuditLogData(
              ExportAuditLogDataParams(
                fundSubId = props.fundSubId,
                filters = state.filters,
                timestampFilterOpt = state.timestampFilter,
                timezoneOffsetInMinutes = -new Date().getTimezoneOffset().toInt // Offset returns UTC zone - locale zone
              )
            )
          } yield {
            scope.modState(
              _.copy(isExporting = false),
              Toast.clearCallback() >> res.fold(
                _ => Toast.errorCallback("Failed to export audit log, try again."),
                res => Toast.successCallback("Audit log exported") >> FileJsClient.downloadCallback(res.fileId)
              )
            )
          }
        }
      } yield ()

    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .componentDidMount { scope =>
      scope.backend.fetchAuditLogData(scope.state.currentPage, scope.state.eventPerPage)
    }
    .build

}
