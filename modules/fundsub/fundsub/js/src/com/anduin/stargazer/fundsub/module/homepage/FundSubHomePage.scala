// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.homepage

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration
import scala.util.Random

import design.anduin.components.accessibility.react.TitleR
import design.anduin.components.navigation.react.NavigationBarR
import design.anduin.components.nonidealstate.react.NonIdealStateR
import design.anduin.components.progress.Skeleton
import design.anduin.components.progress.react.SkeletonR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import io.circe.{Codec, Decoder, Encoder}
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.fundsub.module.common.EnvironmentCustomDomainRedirector
import anduin.graphql.component.FetchStrategy.ForcedFetch
import anduin.graphql.component.{GraphqlOptions, QueryComponent, QueryProps}
import anduin.rohan.operation.AnduinQuery
import anduin.whitelabel.dashboard.DashboardWhitelabelValue
import com.anduin.stargazer.client.localstorage.{AsyncLocalStorage, LocalStorageKey}
import com.anduin.stargazer.fundsub.module.common.{BaseLayout, CommonFooterWithMultiRegionButton}

final case class FundSubHomePage(
  showOnlyModeOpt: Option[FundSubHomePage.ShowOnlyMode] = None,
  currentTabOpt: Option[FundSubHomePage.Tab] = None,
  onChangeTab: FundSubHomePage.Tab => Callback = _ => Callback.empty,
  sortAndFilterOptions: FundSubHomePage.FundAndInvestmentSortAndFilterOptions,
  onChangeSortAndFilterOptions: FundSubHomePage.OnChangeSortAndFilterOptionsParams => Callback // (options, onDone) => Callback
) derives CanEqual {

  def apply(): VdomElement = FundSubHomePage.graphqlComponent(
    this,
    ()
  )

}

object FundSubHomePage {

  sealed trait ShowOnlyMode derives CanEqual

  object ShowOnlyMode {
    case object ShowOnlyAdmin extends ShowOnlyMode
    case object ShowOnlyLp extends ShowOnlyMode
  }

  sealed trait Tab derives CanEqual {
    def title: String
  }

  object Tab {

    case object Funds extends Tab {
      override val title = "Funds"
    }

    case object Investments extends Tab {
      override val title = "Investments"
    }

  }

  sealed trait SortOption derives CanEqual {
    def optionName: String
  }

  object SortOption {

    case object Alphabetically extends SortOption {
      override val optionName = "Alphabetically"
    }

    case object MostRecentActivity extends SortOption {
      override val optionName = "Most recent activity"
    }

    case object CreatedDate extends SortOption {
      override val optionName = "Created date"
    }

    given decoder: Decoder[SortOption] = Decoder[String].emap {
      case "alphabetically" => Right(Alphabetically)
      case "recent"         => Right(MostRecentActivity)
      case "createdDate"    => Right(CreatedDate)
      case _                => Right(Alphabetically)
    }

    given encoder: Encoder[SortOption] = Encoder[String].contramap {
      case Alphabetically     => "alphabetically"
      case MostRecentActivity => "recent"
      case CreatedDate        => "createdDate"
    }

  }

  val SortOptions: Seq[SortOption] = Seq(
    SortOption.Alphabetically,
    SortOption.MostRecentActivity,
    SortOption.CreatedDate
  )

  final case class SortAndFilterOption(
    searchKey: String = "",
    sortBy: SortOption = SortOption.Alphabetically,
    showFundClosed: Boolean = true
  )

  object SortAndFilterOption {
    given Codec.AsObject[SortAndFilterOption] = deriveCodecWithDefaults
  }

  final case class FundAndInvestmentSortAndFilterOptions(
    fundSortAndFilterOption: SortAndFilterOption = SortAndFilterOption(),
    investmentSortAndFilterOption: SortAndFilterOption = SortAndFilterOption()
  )

  object FundAndInvestmentSortAndFilterOptions {
    given Codec.AsObject[FundAndInvestmentSortAndFilterOptions] = deriveCodecWithDefaults
  }

  type SortAndFilterOptionsUpdateFn =
    FundAndInvestmentSortAndFilterOptions => FundAndInvestmentSortAndFilterOptions // current options => new options

  final case class OnChangeSortAndFilterOptionsParams(
    sortAndFilterOptionsUpdateFn: SortAndFilterOptionsUpdateFn,
    onDone: Callback
  )

  final case class ChildProps(
    fundSubHomePageData: Option[FundSubHomePageData]
  ) derives CanEqual

  object ChildProps {
    given Codec.AsObject[ChildProps] = deriveCodecWithDefaults
  }

  private type Props = QueryProps[FundSubHomePage, Unit, ChildProps]

  val Tabs = Seq(
    FundSubHomePage.Tab.Funds,
    FundSubHomePage.Tab.Investments
  )

  private def renderSkeleton() = {
    React.Fragment(
      // Header
      <.div(
        tw.flex.itemsCenter.p16.mb32,
        <.div(
          tw.mr16,
          SkeletonR(
            effect = Skeleton.Effect.Wave,
            height = "32px",
            width = "32px",
            shape = Skeleton.Shape.Rounded
          )()
        ),
        <.div(
          tw.flexFill,
          SkeletonR(
            effect = Skeleton.Effect.Wave,
            height = "32px",
            width = "200px",
            shape = Skeleton.Shape.Rounded
          )()
        ),
        SkeletonR(
          effect = Skeleton.Effect.Wave,
          height = "32px",
          width = "32px",
          shape = Skeleton.Shape.Circle
        )()
      ),
      <.div(
        tw.mxAuto.px24,
        tw.lg(tw.px32.maxWPx1024),
        // Search box
        <.div(
          tw.pt16.flex.itemsEnd.flexCol.mb32,
          SkeletonR(
            effect = Skeleton.Effect.Wave,
            height = "32px",
            width = "280px",
            shape = Skeleton.Shape.Rectangle
          )()
        ),
        // Investment profile management
        <.div(
          tw.mb40,
          <.div(
            tw.mb20,
            SkeletonR(
              effect = Skeleton.Effect.Wave,
              height = "auto",
              width = "300px",
              shape = Skeleton.Shape.Text(fontSize = 23)
            )()
          ),
          <.div(
            tw.flex.spaceX16,
            ^.height := "112px",
            <.div(
              tw.flexFill,
              SkeletonR(
                effect = Skeleton.Effect.Wave,
                height = "100%",
                width = "100%",
                shape = Skeleton.Shape.Rounded
              )()
            ),
            <.div(
              tw.flexFill,
              SkeletonR(
                effect = Skeleton.Effect.Wave,
                height = "100%",
                width = "100%",
                shape = Skeleton.Shape.Rounded
              )()
            ),
            SkeletonR(
              effect = Skeleton.Effect.Wave,
              height = "100%",
              width = "64px",
              shape = Skeleton.Shape.Rounded
            )()
          )
        ),
        // Funds you're managing
        <.div(
          tw.mb40,
          <.div(
            tw.mb20,
            SkeletonR(
              effect = Skeleton.Effect.Wave,
              height = "auto",
              width = "300px",
              shape = Skeleton.Shape.Text(fontSize = 23)
            )()
          ),
          0.to(2)
            .toVdomArray(
              using { index =>
                <.div(
                  ^.key := index,
                  tw.mb16.borderAll.borderGray3.rounded6.bgGray0.p24,
                  tw.flex.itemsCenter,
                  <.div(
                    tw.mr16,
                    SkeletonR(
                      effect = Skeleton.Effect.Wave,
                      height = "64px",
                      width = "64px",
                      shape = Skeleton.Shape.Rounded
                    )()
                  ),
                  <.div(
                    tw.flexFill,
                    SkeletonR(
                      effect = Skeleton.Effect.Wave,
                      height = "24px",
                      width = s"${Random.between(4, 6) * 10}%",
                      shape = Skeleton.Shape.Rounded
                    )()
                  )
                )
              }
            )
        ),
        // Funds you're participating in
        <.div(
          tw.mb40,
          <.div(
            tw.mb20,
            SkeletonR(
              effect = Skeleton.Effect.Wave,
              height = "auto",
              width = "300px",
              shape = Skeleton.Shape.Text(fontSize = 23)
            )()
          ),
          0.to(2)
            .toVdomArray(
              using { index =>
                <.div(
                  ^.key := index,
                  tw.mb16.borderAll.borderGray3.rounded6.bgGray0.p24,
                  tw.flex.itemsCenter,
                  <.div(
                    tw.mr16,
                    SkeletonR(
                      effect = Skeleton.Effect.Wave,
                      height = "64px",
                      width = "64px",
                      shape = Skeleton.Shape.Rounded
                    )()
                  ),
                  <.div(
                    tw.flexFill.spaceY16,
                    SkeletonR(
                      effect = Skeleton.Effect.Wave,
                      height = "24px",
                      width = s"${Random.between(4, 6) * 10}%",
                      shape = Skeleton.Shape.Rounded
                    )(),
                    SkeletonR(
                      effect = Skeleton.Effect.Wave,
                      height = "10px",
                      width = "100px",
                      shape = Skeleton.Shape.Rounded
                    )()
                  )
                )
              }
            )
        )
      )
    )
  }

  private def renderTabs(
    currentTab: FundSubHomePage.Tab,
    onChangeTab: FundSubHomePage.Tab => Callback,
    theme: DashboardWhitelabelValue.PredefinedValue
  ) = {
    val tabTheme = theme match {
      case DashboardWhitelabelValue.PredefinedValue.TabNavigationDark =>
        NavigationBarR.Theme.Light
      case _ =>
        NavigationBarR.Theme.Dark
    }
    NavigationBarR(
      activeItem = Option(Tabs.indexOf(currentTab)),
      items = Tabs.map { tab =>
        NavigationBarR.Item(
          renderContent = () => tab.title,
          onClick = onChangeTab(tab),
          renderItem = Option(_.renderItem(if (tabTheme.equals(NavigationBarR.Theme.Dark)) {
            ^.opacity := "80%"
          } else {
            ^.opacity := "auto"
          }))
        )
      },
      theme = tabTheme
    )()
  }

  private def renderHeaderAndTabs(
    props: Props,
    bannerBackgroundColor: String,
    shouldShowTabs: Boolean,
    currentTab: Tab,
    tabNavigationTheme: DashboardWhitelabelValue.PredefinedValue
  ) = {
    React.Fragment(
      <.div(
        tw.absolute.top0.left0.right0,
        tw.lg(tw.hidden),
        ^.height := "188px",
        ^.background := bannerBackgroundColor,
        FundSubHomePageHeader(
          currentEnvironmentOpt = props.data.fundSubHomePageData.flatMap(_.currentEnvironmentOpt)
        )()
      ),
      <.div(
        tw.absolute.top0.left0.right0.hidden,
        tw.lg(tw.block),
        ^.height := "164px",
        ^.background := bannerBackgroundColor,
        FundSubHomePageHeader(
          currentEnvironmentOpt = props.data.fundSubHomePageData.flatMap(_.currentEnvironmentOpt),
          renderCenteredContent = () =>
            <.div(
              TagMod.unless(shouldShowTabs) { tw.hidden },
              renderTabs(
                currentTab,
                props.outerProps.onChangeTab,
                tabNavigationTheme
              )
            )
        )()
      ),
      <.div(
        tw.absolute.left0.right0.flex.justifyCenter.z1,
        tw.lg(tw.hidden),
        TagMod.unless(shouldShowTabs) { tw.hidden },
        ^.top := "78px",
        renderTabs(
          currentTab,
          props.outerProps.onChangeTab,
          tabNavigationTheme
        )
      )
    )
  }

  private def renderBody(props: Props) = {
    val fundSubHomePageDataOpt = props.data.fundSubHomePageData
    val environmentWhitelabelValues = fundSubHomePageDataOpt
      .flatMap(_.currentEnvironmentOpt.map(_.environmentWhitelabelData.customValues))
      .getOrElse(Map.empty)
    val hideLpProfile =
      fundSubHomePageDataOpt
        .flatMap(_.currentEnvironmentOpt)
        .map(_.environmentWhitelabelData.hideLpProfile)
        .exists(identity)
    val bannerBackgroundColor =
      DashboardWhitelabelValue.BannerBackgroundColor.extractThis(environmentWhitelabelValues)
    val tabNavigationTheme =
      DashboardWhitelabelValue.TabNavigation.extractOrDefault(environmentWhitelabelValues)
    val shouldShowTabs =
      fundSubHomePageDataOpt.exists(data =>
        data.fundSubsJoinedAsAdmin.nonEmpty && data.fundSubsJoinedAsLp.nonEmpty
      ) && props.outerProps.showOnlyModeOpt.isEmpty
    val currentTab = props.outerProps.showOnlyModeOpt.fold(
      props.outerProps.currentTabOpt.getOrElse(
        fundSubHomePageDataOpt
          .map { data =>
            if (data.fundSubsJoinedAsAdmin.isEmpty) {
              Tab.Investments
            } else {
              Tab.Funds
            }
          }
          .getOrElse(Tab.Funds)
      )
    ) {
      case ShowOnlyMode.ShowOnlyAdmin => Tab.Funds
      case ShowOnlyMode.ShowOnlyLp    => Tab.Investments
    }

    if (props.loading) {
      renderSkeleton()
    } else {
      <.div(
        tw.bgGray0.minHVh100,
        <.div(
          tw.flex.flexCol.minHVh100,
          tw.relative,
          ^.paddingTop := "64px",
          renderHeaderAndTabs(
            props,
            bannerBackgroundColor,
            shouldShowTabs,
            currentTab,
            tabNavigationTheme
          ),
          <.div(
            tw.relative.flexGrow,
            tw.mxAuto.px16,
            tw.lg(tw.px32.maxWPx1024.mb32),
            fundSubHomePageDataOpt.fold[TagMod](
              <.div(
                ^.paddingTop := "170px",
                NonIdealStateR(
                  icon = <.img(^.src := "/web/gondor/images/404/illustration-404.svg"),
                  title = "Server is busy. Please try again later!"
                )()
              )
            ) { fundSubHomePageData =>
              if (fundSubHomePageData.fundSubsJoinedAsAdmin.isEmpty && fundSubHomePageData.fundSubsJoinedAsLp.isEmpty) {
                TagMod(
                  ComponentUtils.testId(FundSubHomePage, "Empty"),
                  FundSubHomePageGetStarted()()
                )
              } else {
                TagMod(
                  ComponentUtils.testId(FundSubHomePage, "Content"),
                  tw.pt24.pb4,
                  tw.lg(tw.pt0),
                  FundSubHomePageFundList(
                    currentTab = currentTab,
                    fundSubsJoinedAsAdmin = fundSubHomePageData.fundSubsJoinedAsAdmin
                      .filterNot(_ => currentTab != FundSubHomePage.Tab.Funds),
                    fundSubsJoinedAsLp = fundSubHomePageData.fundSubsJoinedAsLp
                      .filterNot(_ => currentTab != FundSubHomePage.Tab.Investments),
                    investmentEntityList = fundSubHomePageData.investmentEntityList,
                    environmentWhitelabelValues = environmentWhitelabelValues,
                    sortAndFilterOptions = props.outerProps.sortAndFilterOptions,
                    onChangeSortAndFilterOptions = { case OnChangeSortAndFilterOptionsParams(updateFn, onDone) =>
                      props.outerProps.onChangeSortAndFilterOptions(
                        OnChangeSortAndFilterOptionsParams(
                          updateFn,
                          saveOptions(updateFn(props.outerProps.sortAndFilterOptions)) >> onDone
                        )
                      )
                    },
                    hideLpProfile = hideLpProfile
                  )()
                )
              }
            }
          ),
          <.div(
            tw.lg(tw.pb48) // Styling for large screen
          ),
          <.div(
            tw.borderTop.borderGray3.bgGray0.p16.wPc100.hidden,
            tw.lg(tw.block),
            CommonFooterWithMultiRegionButton()()
          )
        )
      )
    }
  }

  private def saveOptions(options: FundAndInvestmentSortAndFilterOptions) = {
    AsyncLocalStorage.set(LocalStorageKey.FundSubHomePageSortAndFilterOptions, options)
  }

  private def render(props: Props): VdomNode = {
    EnvironmentCustomDomainRedirector(fundSubIdOpt = None)(
      TitleR("Fund Subscription")(
        BaseLayout()(
          renderBody(props)
        )
      )
    )
  }

  private def handleDidFetch(props: Props) = {
    val tabOpt = props.data.fundSubHomePageData
      .flatMap { data =>
        props.outerProps.currentTabOpt.fold(
          Option.when[Tab](data.fundSubsJoinedAsAdmin.nonEmpty || data.fundSubsJoinedAsLp.nonEmpty) {
            if (data.fundSubsJoinedAsAdmin.isEmpty) {
              Tab.Investments
            } else {
              Tab.Funds
            }
          }
        ) { currentTab =>
          Option.when[Tab](data.fundSubsJoinedAsAdmin.nonEmpty || data.fundSubsJoinedAsLp.nonEmpty) {
            if (data.fundSubsJoinedAsAdmin.isEmpty && currentTab == Tab.Funds) {
              Tab.Investments
            } else if (data.fundSubsJoinedAsLp.isEmpty && currentTab == Tab.Investments) {
              Tab.Funds
            } else {
              currentTab
            }
          }
        }
      }

    for {
      _ <- Callback.traverseOption(tabOpt) {
        props.outerProps.onChangeTab(_)
      }
      optionsOpt <- AsyncLocalStorage
        .get[FundAndInvestmentSortAndFilterOptions](LocalStorageKey.FundSubHomePageSortAndFilterOptions)
      _ <- props.outerProps.onChangeSortAndFilterOptions(
        OnChangeSortAndFilterOptionsParams(
          _ => {
            val options = optionsOpt.getOrElse(FundAndInvestmentSortAndFilterOptions())
            options.copy(
              fundSortAndFilterOption = options.fundSortAndFilterOption
                .copy(searchKey = props.outerProps.sortAndFilterOptions.fundSortAndFilterOption.searchKey),
              investmentSortAndFilterOption = options.investmentSortAndFilterOption
                .copy(searchKey = props.outerProps.sortAndFilterOptions.investmentSortAndFilterOption.searchKey)
            )
          },
          Callback.empty
        )
      )
    } yield ()
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .componentDidUpdate(scope =>
      Callback.when(!scope.currentProps.loading && scope.prevProps.loading) {
        handleDidFetch(scope.currentProps)
      }
    )
    .build

  private val graphqlComponent = QueryComponent(
    component,
    AnduinQuery.FundSubHomePageData,
    GraphqlOptions(
      pollInterval = FiniteDuration(30, TimeUnit.SECONDS),
      requestTimeout = FiniteDuration(20, TimeUnit.SECONDS)
    ),
    initialFetchStrategy = ForcedFetch
  )

}
