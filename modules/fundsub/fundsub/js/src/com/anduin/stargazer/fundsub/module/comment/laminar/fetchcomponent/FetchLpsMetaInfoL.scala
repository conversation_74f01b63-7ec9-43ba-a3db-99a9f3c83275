// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent

import com.raquo.laminar.api.L.*

import anduin.frontend.AirStreamUtils
import anduin.id.fundsub.{FundSubId, FundSubLpId, IssueLpCompositeId}
import anduin.model.notichannel.FundSubNotificationChannels
import anduin.stargazer.service.formcomment.FormCommentCommons.{FundSubLpMetaInfo, GetLpsMetaInfoParams}
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.notiCenter.NotificationCenter
import com.anduin.stargazer.notiCenter.NotificationCenter.TypedMultiWatch

// TODO(comment) @tuananhtd: check this component performance
private[laminar] case class FetchLpsMetaInfoL(
  fundSubId: FundSubId,
  getLpsWithCommentOnly: Boolean,
  getLpsUnsubscribedToDigestEmailOnly: Boolean,
  renderChildren: FetchLpsMetaInfoL.RenderChildren => HtmlElement
) {

  private val fetchLpsMetaInfoEventBus = new EventBus[List[FundSubLpId]]

  private val lpsMetaInfoVar: Var[List[FundSubLpMetaInfo]] = Var(List.empty)

  private def fetchLpsMetaInfo(lpIds: List[FundSubLpId]): EventStream[Unit] = {
    AirStreamUtils.taskToStream {
      FundSubEndpointClient
        .getLpsMetaInfo(
          GetLpsMetaInfoParams(
            fundId = fundSubId,
            targetedLpIds = lpIds,
            getLpsWithCommentOnly = getLpsWithCommentOnly,
            getLpsUnsubscribedToDigestEmailOnly = getLpsUnsubscribedToDigestEmailOnly
          )
        )
        .map {
          _.fold(
            _ => (),
            response => {
              val newLpIds = response.allLpsMetaInfo.map(_.fundSubLpId).toSet
              lpsMetaInfoVar.update { oldList =>
                oldList.filterNot(lp => newLpIds.contains(lp.fundSubLpId)) ++ response.allLpsMetaInfo
              }
            }
          )
        }
    }
  }

  def apply(): HtmlElement = {
    renderChildren(
      FetchLpsMetaInfoL.RenderChildren(
        allLpsMetaInfoSignal = lpsMetaInfoVar.signal,
        refetch = fetchLpsMetaInfoEventBus.writer
      )
    ).amend(
      NotificationCenter
        .eventSignalMulti[IssueLpCompositeId](FundSubNotificationChannels.fundSubFormComment(fundSubId))
        .map {
          case TypedMultiWatch.Initial() => ()
          case TypedMultiWatch.Updated(ids, _) =>
            val updatedLpIds =
              ids.map(_.child).distinct
            fetchLpsMetaInfoEventBus.emit(updatedLpIds.toList)
        } --> Observer.empty,
      fetchLpsMetaInfoEventBus.events.flatMapSwitch(fetchLpsMetaInfo) --> Observer.empty,
      onMountCallback { _ =>
        NotificationCenter.subscribeSync(Seq(FundSubNotificationChannels.fundSubFormComment(fundSubId)))
        fetchLpsMetaInfoEventBus.emit(List.empty)
      }
    )
  }

}

private[laminar] object FetchLpsMetaInfoL {

  case class RenderChildren(
    allLpsMetaInfoSignal: Signal[List[FundSubLpMetaInfo]],
    // TODO(comment) @tuananhtd: should detect when to re-fetch info
    refetch: Observer[List[FundSubLpId]]
  )

}
