// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.emaillog

import com.anduin.stargazer.fundsub.module.investor.settings.auditlog.{AuditLogFilterSection, Filter}
import com.anduin.stargazer.fundsub.module.investor.settings.emaillog.data.EmailLogFilterParams
import design.anduin.components.button.Button
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[emaillog] final case class FilterDescription(
  params: EmailLogFilterParams,
  onReset: Callback
) {
  def apply(): VdomElement = FilterDescription.component(this)
}

private[emaillog] object FilterDescription {
  private type Props = FilterDescription

  private def render(props: Props) = {
    val dateRangeDescription = props.params.dateRangeFilterItemOpt
      .filter(_ != Filter.defaultRangeFilterOption)
      .flatMap { filterItem =>
        props.params.dateRangeFilterItemOpt.flatMap(_.filter).map { filter =>
          if (filter.isCustom) {
            AuditLogFilterSection.timeRangeToText(
              filter.from,
              filter.to
            )
          } else {
            filterItem.textLabel
          }
        }
      }
      .getOrElse("")

    <.div(
      tw.flex.itemsCenter.textGray7,
      <.div(
        tw.fontSemiBold,
        "Filtered By"
      ),
      <.div(
        tw.ml4,
        TagMod.when(dateRangeDescription.nonEmpty) {
          s"Date range ($dateRangeDescription)"
        },
        TagMod.when(dateRangeDescription.nonEmpty && props.params.recipientEmail.nonEmpty) {
          ", "
        },
        TagMod.when(props.params.recipientEmail.nonEmpty) {
          s"Recipient (${props.params.recipientEmail})"
        }
      ),
      TagMod.when(
        !props.params.dateRangeFilterItemOpt.contains(Filter.defaultRangeFilterOption) ||
          props.params.recipientEmail.nonEmpty
      ) {
        <.div(
          tw.ml4,
          Button(
            style = Button.Style.Text(),
            onClick = props.onReset
          )("Reset all")
        )
      }
    )
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
