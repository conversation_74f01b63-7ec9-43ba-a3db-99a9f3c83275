// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.emaillog

import anduin.fundsub.auditlog.AuditLogFilter.TimestampFilter
import com.anduin.stargazer.fundsub.module.investor.settings.auditlog.{
  AuditLogFilterSection,
  CustomDateRangeFilterModal,
  Filter
}
import com.anduin.stargazer.fundsub.module.investor.settings.auditlog.Filter.TextFilterItem
import design.anduin.components.button.Button
import design.anduin.components.dropdown.Dropdown
import design.anduin.components.modal.Modal
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[emaillog] final case class DateRangeFilter(
  dateRangeFilterItemOpt: Option[TextFilterItem[TimestampFilter]],
  onChange: Option[TextFilterItem[TimestampFilter]] => Callback
) {
  def apply(): VdomElement = DateRangeFilter.component(this)
}

private[emaillog] object DateRangeFilter {
  private type Props = DateRangeFilter

  private final case class State(
    isOpenCustomDateRangeModal: Boolean = false
  )

  private val DateRangeDropdown = (new Dropdown[TextFilterItem[TimestampFilter]])()

  private final case class Backend(scope: BackendScope[Props, State]) {

    private def renderCustomDateRangeModal(props: Props, state: State) = {
      val fromOpt = props.dateRangeFilterItemOpt.flatMap(_.filter).flatMap { timestampFilter =>
        Option.when(timestampFilter.isCustom)(timestampFilter.from)
      }

      val toOpt = props.dateRangeFilterItemOpt.flatMap(_.filter).flatMap { timestampFilter =>
        Option.when(timestampFilter.isCustom)(timestampFilter.to)
      }

      Modal(
        title = "Customize date range",
        renderContent = _ =>
          CustomDateRangeFilterModal(
            onApply = (from, to) => {
              val newDateRangeFilter = TextFilterItem(
                "Custom Range",
                Option(
                  TimestampFilter(
                    from,
                    to,
                    isCustom = true
                  )
                )
              )
              props.onChange(Option(newDateRangeFilter))
            },
            onClose = scope.modState(_.copy(isOpenCustomDateRangeModal = false)),
            from = fromOpt,
            to = toOpt
          )(),
        isOpened = Option(state.isOpenCustomDateRangeModal),
        afterUserClose = scope.modState(_.copy(isOpenCustomDateRangeModal = false))
      )()
    }

    def render(props: Props, state: State): VdomElement = {
      <.div(
        renderCustomDateRangeModal(props, state),
        <.div(
          DateRangeDropdown(
            value = props.dateRangeFilterItemOpt,
            valueToString = _.textLabel,
            items = Filter.dateRangeOptions.map(Dropdown.Item(_)),
            button = Dropdown.Button(
              appearance = Dropdown.Appearance.Full(isFullWidth = true)
            ),
            onChange = filterItem =>
              if (filterItem.filter.nonEmpty) {
                props.onChange(Option(filterItem))
              } else {
                scope.modState(_.copy(isOpenCustomDateRangeModal = true))
              }
          )(),
          props.dateRangeFilterItemOpt.flatMap(_.filter).fold(TagMod.empty) { dateRangeFilter =>
            TagMod.when(dateRangeFilter.isCustom) {
              <.div(
                tw.flex.mt8,
                <.div(
                  AuditLogFilterSection.timeRangeToText(dateRangeFilter.from, dateRangeFilter.to)
                ),
                <.div(
                  tw.ml4,
                  Button(
                    style = Button.Style.Text(),
                    onClick = scope.modState(_.copy(isOpenCustomDateRangeModal = true))
                  )("Edit")
                )
              )
            }
          }
        )
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
