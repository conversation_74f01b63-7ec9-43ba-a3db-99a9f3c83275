// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.share

import com.raquo.laminar.api.L.*
import design.anduin.style.tw.*

import anduin.utils.StringUtils

private[laminar] final case class InactiveReminderText(
  daysSignal: Signal[Int]
) {

  def apply(): HtmlElement = {
    div(
      tw.textSuccess5.fontMedium,
      child <-- daysSignal.map { days =>
        s"Received ${StringUtils.pluralItem(days, "day")} ago. Reply?"
      }
    )
  }

}
