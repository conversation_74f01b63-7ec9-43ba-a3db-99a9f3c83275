// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.ria

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.field.laminar.FieldL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL, ModalL}
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.ria.{RiaFundGroup, UpdateRiaFundGroupParams}
import anduin.id.fundsub.ria.FundSubRiaGroupId
import com.anduin.stargazer.fundsub.client.FundSubRiaEndpointClient

private[ria] case class RenameAdvisorGroupModal(
  group: RiaFundGroup,
  existingGroupNameSignal: Signal[Seq[String]],
  renderTarget: Observer[Unit] => HtmlElement,
  onClose: Observer[Unit],
  onDoneRenameGroup: Observer[Unit]
) {

  private val groupNameVar = Var(group.name)
  private val groupNameSignal = groupNameVar.signal.distinct

  private val isValidSignal =
    groupNameSignal.combineWith(existingGroupNameSignal).map { (groupName, existingGroupName) =>
      groupName.nonEmpty && groupName != group.name && !existingGroupName.contains(groupName)
    }

  private val renameGroupEvent = new EventBus[Unit]

  private val isUpdatingGroupName = Var(false)
  private val isUpdatingGroupNameSignal = isUpdatingGroupName.signal.distinct

  def apply(): HtmlElement = {
    ModalL(
      size = ModalL.Size(width = ModalL.Width.Px480),
      afterUserClose = onClose,
      renderContent = onClose => {
        div(
          ModalBodyL(
            div(
              tw.spaceY24,
              div(
                span("Enter a new name for "),
                span(tw.fontSemiBold, group.name)
              ),
              FieldL(
                label = None,
                requirement = FieldL.Requirement.Required,
                validation = isValidSignal.combineWith(existingGroupNameSignal, groupNameSignal).map {
                  case (_, existingGroupName, groupName) =>
                    if (groupName.isEmpty) {
                      FieldL.Validation.Invalid("Advisor entity name is required")
                    } else if (existingGroupName.filterNot(_ == group.name).contains(groupName)) {
                      FieldL.Validation.Invalid("This name is already used by another advisor entity")
                    } else
                      FieldL.Validation.None
                }
              )(
                TextBoxL(
                  isAutoFocus = true,
                  value = groupNameSignal,
                  onChange = groupNameVar.writer
                )()
              )
            )
          ),
          ModalFooterWCancelL(
            cancel = onClose
          )(
            ButtonL(
              isDisabled = isValidSignal.invert,
              style = ButtonL.Style.Full(
                color = ButtonL.Color.Primary,
                isBusy = isUpdatingGroupNameSignal
              ),
              onClick = Observer { _ =>
                renameGroupEvent.emit(())
              }
            )("Save")
          ),
          renameGroupEvent.events.sample(groupNameSignal).flatMapSwitch { name =>
            handleRenameAdvisorGroup(
              fundRiaGroupId = group.id,
              name = name,
              onClose = onClose
            )
          } --> Observer.empty
        )
      },
      renderTitle = _ => "Edit advisor entity name",
      renderTarget = renderTarget
    )()
  }

  private def handleRenameAdvisorGroup(
    fundRiaGroupId: FundSubRiaGroupId,
    name: String,
    onClose: Observer[Unit]
  ) = {
    AirStreamUtils.taskToStream(
      for {
        _ <- ZIO.succeed(isUpdatingGroupName.set(true))
        _ <- FundSubRiaEndpointClient
          .updateRiaFundGroup(
            UpdateRiaFundGroupParams(
              fundRiaGroupId = fundRiaGroupId,
              name = name
            )
          )
          .map(
            _.fold(
              error => {
                isUpdatingGroupName.set(false)
                Toast.error("Failed to update advisor entity")
              },
              res => {
                isUpdatingGroupName.set(false)
                onClose.onNext(())
                onDoneRenameGroup.onNext(())
                Toast.success("Advisor entity name updated")
              }
            )
          )
      } yield ()
    )
  }

}
