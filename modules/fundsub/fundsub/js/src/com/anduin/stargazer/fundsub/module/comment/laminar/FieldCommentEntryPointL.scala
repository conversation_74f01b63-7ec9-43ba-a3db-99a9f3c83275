// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar

import com.raquo.laminar.api.L.*
import design.anduin.components.drawer.Drawer
import design.anduin.components.drawer.laminar.DrawerL
import design.anduin.components.responsive.ScreenWidth
import design.anduin.components.responsive.laminar.WithScreenWidthL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import org.scalajs.dom
import org.scalajs.dom.HTMLElement

import anduin.fundsub.comment.CommentBubbleL
import anduin.id.fundsub.FundSubLpId
import anduin.protobuf.fundsub.comment.FormQuestionData
import anduin.scalajs.pluralize.Pluralize
import com.anduin.stargazer.fundsub.module.comment.laminar.drawer.gp.GpCommentDrawerL
import com.anduin.stargazer.fundsub.module.comment.laminar.drawer.CommentDrawerUIModels
import com.anduin.stargazer.fundsub.module.comment.laminar.drawer.lp.LpCommentDrawerL
import com.anduin.stargazer.fundsub.module.comment.models.{
  CommentNotificationSetting,
  CommentTarget,
  CommentsData,
  DocCommentCount
}

private[fundsub] final case class CommentPanelParams(
  isPublic: Boolean
)

private[fundsub] case class FieldCommentEntryPointL(
  amlKycNamesSignal: Signal[Option[List[String]]],
  commentNotificationSettingSignal: Signal[Option[CommentNotificationSetting]],
  isFundSideViewSignal: Signal[Boolean],
  commentsDataSignal: Signal[CommentsData],
  inactiveDaysThresholdSignal: Signal[Option[Int]],
  formQuestionData: FormQuestionData,
  isHiddenFieldThread: String => Signal[Boolean],
  isInitialActive: Boolean = false,
  isPublicModeInitialActive: Boolean = true,
  lpId: FundSubLpId,
  onJumpToField: Observer[String]
) {

  private val commentPanelParamsVar = Var[Option[CommentPanelParams]](None)

  def apply(): HtmlElement = {
    val publicDocCommentCountSignal =
      commentsDataSignal.map(_.getCommentCountOfField(formQuestionData.fieldAlias, isPublic = true))
    val internalDocCommentCountSignal =
      commentsDataSignal.map(_.getCommentCountOfField(formQuestionData.fieldAlias, isPublic = false))

    val shouldShowCreateNewCommentBubble =
      publicDocCommentCountSignal
        .combineWith(internalDocCommentCountSignal)
        .map { case (publicComment, internalComment) =>
          !publicComment.hasComment && !internalComment.hasComment
        }
        .distinct

    WithScreenWidthL(
      render = screenWidthSignal => {
        val isSmallScreenSignal = screenWidthSignal.map(_.minWidth <= ScreenWidth.Small.minWidth)
        div(
          // Absolute position
          // Show the icon at the right side of form
          tw.absolute.group.hPc100.top0,
          left.percent(100),
          transform <-- isSmallScreenSignal.map {
            if (_) "translateX(-52px)" else ""
          },
          onMountCallback { ctx =>
            // Disable the tab for icon
            Option(ctx.thisNode.ref.querySelector("button")).foreach(_.setAttribute("tabindex", "-1"))
          },
          // Reverse some spaces in the field container for the comment icon
          inContext { thisNode =>
            isSmallScreenSignal --> Observer[Boolean] { smallScreen =>
              Option(thisNode.ref.closest("[data-field]")).foreach {
                case ele: HTMLElement =>
                  ele.style.paddingRight = if (smallScreen) "52px" else "0"
                case _ =>
                  ()
              }
            }
          },
          div(
            tw.relative,
            left.px(16),
            top.percent(50),
            transform := "translateY(-50%)",
            child.maybe <-- internalDocCommentCountSignal
              .map(_.hasComment)
              .distinct
              .map { hasInternalComment =>
                Option.when(hasInternalComment) {
                  div(
                    publicDocCommentCountSignal.map(_.hasComment).distinct.cls(tw.mb4),
                    renderCommentBubble(
                      internalDocCommentCountSignal,
                      isPublic = false
                    )
                  )
                }
              },
            child.maybe <-- publicDocCommentCountSignal
              .map(_.hasComment)
              .distinct
              .map { hasPublicComment =>
                Option.when(hasPublicComment) {
                  div(
                    renderCommentBubble(
                      publicDocCommentCountSignal,
                      isPublic = true
                    )
                  )
                }
              },
            child.maybe <-- shouldShowCreateNewCommentBubble
              .combineWith(isFundSideViewSignal)
              .map { case (showNewCommentBubble, isFundSideView) =>
                Option.when(showNewCommentBubble) {
                  div(
                    renderCommentBubble(
                      if (isFundSideView) {
                        internalDocCommentCountSignal
                      } else {
                        publicDocCommentCountSignal
                      },
                      isPublic = !isFundSideView
                    )
                  )
                }
              },
            child.maybe <-- commentPanelParamsVar.signal
              .map(_.map(_.isPublic))
              .distinct
              .map(isPublicOpt =>
                isPublicOpt.map(isPublic =>
                  renderDrawer(
                    isPublic = isPublic
                  )
                )
              ),
            onMountCallback { _ =>
              if (isInitialActive) {
                commentPanelParamsVar.set(
                  Option(
                    CommentPanelParams(
                      isPublic = isPublicModeInitialActive
                    )
                  )
                )
              }
            }
          )
        )
      }
    )()
  }

  private def renderDrawer(isPublic: Boolean) = {
    val _ = isPublic
    DrawerL(
      width = Drawer.Width.Px400,
      widthConstrain = Option { constrainProps =>
        constrainProps.drawerWidth >= 320 &&
        constrainProps.drawerWidth <= constrainProps.containerWidth * 0.5
      },
      defaultIsOpened = true,
      afterUserClose = Observer[Unit] { _ =>
        commentPanelParamsVar.set(None)
      },
      renderContent = close => {
        div(
          tw.hPc100,
          child <-- isFundSideViewSignal.splitBoolean(
            whenFalse = _ =>
              LpCommentDrawerL(
                amlKycNamesSignal = amlKycNamesSignal,
                commentsDataSignal = commentsDataSignal,
                initialView = CommentDrawerUIModels.DrawerView.Field(formQuestionData, isPublic),
                isHiddenFieldThread = isHiddenFieldThread,
                lpIdSignal = Val(lpId),
                targetSignal = Val(CommentTarget.Form),
                onJumpToTarget = onJumpToField,
                onClose = close
              )(),
            whenTrue = _ =>
              GpCommentDrawerL(
                amlKycNamesSignal = amlKycNamesSignal,
                commentNotificationSettingSignal = commentNotificationSettingSignal,
                commentsDataSignal = commentsDataSignal,
                initialView = CommentDrawerUIModels.DrawerView.Field(formQuestionData, isPublic),
                isHiddenFieldThread = isHiddenFieldThread,
                lpIdSignal = Val(lpId),
                targetSignal = Val(CommentTarget.Form),
                inactiveDaysThresholdSignal = inactiveDaysThresholdSignal,
                onJumpToTarget = onJumpToField,
                onClose = close,
                fundSubId = lpId.parent
              )()
          )
        )

      }
    )()
  }

  private def renderCommentBubble(
    docCommentCountSignal: Signal[DocCommentCount],
    isPublic: Boolean
  ) = {
    val isResolvedSignal = docCommentCountSignal.map(_.isResolved).distinct
    val commentsCountSignal = docCommentCountSignal.map(_.totalComments).distinct

    TooltipL(
      renderContent = _.amend(
        child <-- isResolvedSignal.combineWith(commentsCountSignal).map { case (isResolved, commentsCount) =>
          if (commentsCount == 0) {
            "Add comment"
          } else {
            s"View${if (isResolved) " resolved" else ""} ${if (isPublic) "shared" else "internal"} ${Pluralize("comment", commentsCount)}"
          }
        }
      ),
      renderTarget = div(
        docCommentCountSignal
          .map { data =>
            !data.hasComment
          }
          .cls(tw.invisible.groupHover(tw.visible)),
        child <-- docCommentCountSignal.map { docComment =>
          CommentBubbleL(
            hasComment = docComment.hasComment,
            hasNewComment = docComment.unreadComments > 0,
            isResolved = docComment.isResolved,
            isInternalComment = !isPublic,
            theme = CommentBubbleL.Theme.Full,
            totalComments = docComment.totalComments
          )().amend(
            ComponentUtils.testIdL("CommentBubble"),
            onClick --> Observer[dom.MouseEvent] { _ =>
              commentPanelParamsVar.set(
                Option(
                  CommentPanelParams(
                    isPublic = isPublic
                  )
                )
              )
            }
          )
        }
      )
    )()
  }

}
