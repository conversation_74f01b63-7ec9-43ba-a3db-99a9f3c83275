// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.exporttemplate

import scala.reflect.TypeTest

import com.raquo.laminar.api.L.*
import design.anduin.components.checkbox.CheckboxL
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.icon.Icon
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import anduin.dashboard.model.{ContactColumn, DashboardColumn}
import anduin.fundsub.endpoint.dashboard.v2.TypedDashboardColumn
import anduin.fundsub.endpoint.dataexport.ExportTemplateFieldIdentifier
import anduin.id.fundsub.InvestmentFundId
import com.anduin.stargazer.fundsub.module.investor.dashboard.v2.columns.WorkflowColumnSection
import com.anduin.stargazer.fundsub.module.model.FundSubClientModel.InvestmentFundClientModel

private[exporttemplate] case class SelectDashboardColumnsPanel(
  dashboardColumnInfosSignal: Signal[Seq[TypedDashboardColumn]],
  selectedColumnIdsSignal: Signal[Set[ExportTemplateFieldIdentifier.DashboardColumnField]],
  onSelectColumns: Observer[(Seq[ExportTemplateFieldIdentifier.DashboardColumnField], Boolean)],
  investmentFundsSignal: Signal[Seq[InvestmentFundClientModel]]
) {

  private def getColumnsByType[T <: TypedDashboardColumn](
    columns: Seq[TypedDashboardColumn]
  )(
    using TypeTest[TypedDashboardColumn, T]
  ): Seq[T] = {
    columns.collect { case col: T => col }
  }

  private val workFlowColumnsGroupedBySectionSignal: Signal[Map[WorkflowColumnSection, Seq[TypedDashboardColumn]]] = {
    dashboardColumnInfosSignal.combineWith(investmentFundsSignal).map { case (dashboardColumns, investmentFunds) =>
      getColumnsByType[TypedDashboardColumn.WorkflowColumn](dashboardColumns)
        .flatMap { workFlowColumn =>
          WorkflowColumnSection
            .getWorkFlowColumnSectionOpt(workFlowColumn.column, investmentFunds)
            .map(_ -> workFlowColumn)
        }
        .groupMap(_._1)(_._2)
    }
  }

  private def getWorkflowColumnSectionsOrder(investmentFundIds: Seq[InvestmentFundId]): Seq[WorkflowColumnSection] =
    Seq(
      WorkflowColumnSection.Informational,
      WorkflowColumnSection.Date,
      WorkflowColumnSection.Other,
      WorkflowColumnSection.Status
    ) ++
      investmentFundIds.map(WorkflowColumnSection.SubFundCommitmentAmount(_))

  private val workflowColumnsSignal: Signal[Seq[TypedDashboardColumn]] =
    workFlowColumnsGroupedBySectionSignal.combineWith(investmentFundsSignal).map {
      case (workFlowColumnsGroupedBySection, investmentFunds) =>
        getWorkflowColumnSectionsOrder(investmentFunds.map(_.fundId))
          .flatMap(workFlowColumnsGroupedBySection.get)
          .flatten
    }

  private val customDataColumnsSignal: Signal[Seq[TypedDashboardColumn]] =
    dashboardColumnInfosSignal.map(getColumnsByType[TypedDashboardColumn.CustomColumn])

  private val formDataColumnsSignal: Signal[Seq[TypedDashboardColumn]] =
    dashboardColumnInfosSignal.map(getColumnsByType[TypedDashboardColumn.FormFieldColumn])

  private val selectAllWorkflowColumnsEventBus = new EventBus[Boolean]

  private val selectAllCustomDataColumnsEventBus = new EventBus[Boolean]

  private val selectAllFormDataColumnsEventBus = new EventBus[Boolean]

  private def extractExportTemplateDashboardIdentifier(dashboardColumn: TypedDashboardColumn)
    : ExportTemplateFieldIdentifier.DashboardColumnField =
    ExportTemplateFieldIdentifier.DashboardColumnField(dashboardColumn.id)

  def apply(): HtmlElement = {
    div(
      tw.flex.hPc100,
      renderWorkflowColumnGroup,
      renderOtherColumnGroup(
        columnLabel = "Form data",
        availableColumnsInGroupSignal = formDataColumnsSignal
      ),
      renderOtherColumnGroup(
        columnLabel = "Custom columns",
        availableColumnsInGroupSignal = customDataColumnsSignal,
        isCustomDataColumns = true
      ),
      selectAllWorkflowColumnsEventBus.events
        .withCurrentValueOf(workflowColumnsSignal)
        .map { case (isSelected, allColumns) =>
          allColumns.map(extractExportTemplateDashboardIdentifier) -> isSelected
        } --> onSelectColumns,
      selectAllFormDataColumnsEventBus.events
        .withCurrentValueOf(formDataColumnsSignal)
        .map { case (isSelected, allColumns) =>
          allColumns.map(extractExportTemplateDashboardIdentifier) -> isSelected
        } --> onSelectColumns,
      selectAllCustomDataColumnsEventBus.events
        .withCurrentValueOf(customDataColumnsSignal)
        .map { case (isSelected, allColumns) =>
          allColumns.map(extractExportTemplateDashboardIdentifier) -> isSelected
        } --> onSelectColumns
    )
  }

  private def renderOtherColumnGroup(
    columnLabel: String,
    availableColumnsInGroupSignal: Signal[Seq[TypedDashboardColumn]],
    isCustomDataColumns: Boolean = false
  ) = {
    val isAllColumnsSelected = availableColumnsInGroupSignal
      .combineWith(selectedColumnIdsSignal)
      .map { case (availableColumns, selectedColumns) =>
        availableColumns.forall(column => selectedColumns.exists(_.columnId == column.id))
      }
    val columnsCountSignal = availableColumnsInGroupSignal.map(_.size).distinct
    val hasNoColumnsSignal = columnsCountSignal.map(_ == 0).distinct
    div(
      tw.flexGrow.flex1,
      tw.px16.py12.overflowYAuto,
      tw.borderLeft.borderGray3,
      CheckboxL(
        isChecked = (isAllColumnsSelected && !hasNoColumnsSignal).distinct,
        onChange = if (isCustomDataColumns) {
          selectAllCustomDataColumnsEventBus.writer
        } else {
          selectAllFormDataColumnsEventBus.writer
        },
        isDisabled = hasNoColumnsSignal
      )(
        div(
          tw.fontSemiBold,
          text <-- columnsCountSignal.map { columnCount =>
            s"$columnLabel ($columnCount)"
          }
        )
      ),
      div(
        tw.mt8.spaceY8,
        tw.pl24,
        children <-- availableColumnsInGroupSignal.split(_.id) { (columnId, _, columnSignal) =>
          CheckboxL(
            isChecked = selectedColumnIdsSignal.map(_.exists(_.columnId == columnId)).distinct,
            onChange = onSelectColumns.contramap(isSelected =>
              (Seq(ExportTemplateFieldIdentifier.DashboardColumnField(columnId)), isSelected)
            )
          )(
            div(
              text <-- columnSignal.map(_.column.title)
            )
          )
        }
      )
    )
  }

  private def renderWorkflowColumnGroup = {
    val isAllWorkflowDataColumnsSelected = workflowColumnsSignal
      .combineWith(selectedColumnIdsSignal)
      .map { case (workflowColumns, selectedColumns) =>
        workflowColumns.forall(column => selectedColumns.exists(_.columnId == column.id))
      }
      .distinct
    val workflowColumnCountSignal = workflowColumnsSignal.map(_.size).distinct
    val hasNoColumnsSignal = workflowColumnCountSignal.map(_ == 0)
    div(
      tw.flexGrow.flex1,
      tw.px16.py12.overflowYAuto,
      CheckboxL(
        isChecked = (isAllWorkflowDataColumnsSelected && !hasNoColumnsSignal).distinct,
        onChange = selectAllWorkflowColumnsEventBus.writer,
        isDisabled = hasNoColumnsSignal.distinct
      )(
        div(
          tw.fontSemiBold,
          text <-- workflowColumnCountSignal.map { wfColumnCnt =>
            s"Work flow data ($wfColumnCnt)"
          }
        )
      ),
      div(
        tw.pl24,
        child.maybe <-- renderWorkflowColumnSubSectionIfNonEmptySignal(
          WorkflowColumnSection.Informational,
          "Informational"
        ),
        child.maybe <-- renderWorkflowColumnSubSectionIfNonEmptySignal(WorkflowColumnSection.Date, "Date"),
        child.maybe <-- renderWorkflowColumnSubSectionIfNonEmptySignal(WorkflowColumnSection.Other, "Other"),
        child.maybe <-- renderWorkflowColumnSubSectionIfNonEmptySignal(WorkflowColumnSection.Status, "Status"),
        children <-- investmentFundsSignal.split(_.fundId) { (investmentFundId, investmentFundClientModel, _) =>
          val moreThanOneInvestmentFundSignal = investmentFundsSignal.map(_.size > 1)
          div(
            child.maybe <-- moreThanOneInvestmentFundSignal.distinct.flatMapSwitch { moreThanOneInvestmentFund =>
              val commitmentSectionLabel = if (moreThanOneInvestmentFund) {
                s"${investmentFundClientModel.name} (${investmentFundClientModel.currency.name})"
              } else {
                "Commitment"
              }
              renderWorkflowColumnSubSectionIfNonEmptySignal(
                WorkflowColumnSection.SubFundCommitmentAmount(investmentFundId),
                commitmentSectionLabel
              )
            }
          )
        }
      )
    )
  }

  private def getTooltipDescriptionOptOfColumnItem(
    column: DashboardColumn
  ): Option[String] = {
    column match {
      case _: ContactColumn =>
        Some(
          "This includes: investment entity, first name, last name, email address and collaborators' information"
        )
      case _ => Option.empty[String]
    }
  }

  private def renderWorkflowColumnSubSectionIfNonEmptySignal(
    section: WorkflowColumnSection,
    sectionLabel: String
  ) = {
    workFlowColumnsGroupedBySectionSignal
      .combineWith(investmentFundsSignal)
      .map { case (columnsGroupedBySection, _) =>
        columnsGroupedBySection.getOrElse(section, Seq.empty)
      }
      .distinct
      .map { columnsInSection =>
        Option.when(columnsInSection.nonEmpty) {
          div(
            tw.mt8,
            div(
              tw.fontSemiBold,
              sectionLabel
            ),
            div(
              tw.spaceY8.mt8,
              columnsInSection.map { typedColumn =>
                CheckboxL(
                  isChecked = selectedColumnIdsSignal.map(_.exists(_.columnId == typedColumn.id)).distinct,
                  onChange = onSelectColumns.contramap(isSelected =>
                    (Seq(ExportTemplateFieldIdentifier.DashboardColumnField(typedColumn.id)), isSelected)
                  )
                )(
                  div(
                    tw.flex.itemsCenter,
                    typedColumn.column.title,
                    getTooltipDescriptionOptOfColumnItem(typedColumn.column).map { tooltipDescription =>
                      TooltipL(
                        renderTarget = IconL(
                          name = Val(Icon.Glyph.Question),
                          size = Icon.Size.Custom(12)
                        )().amend(
                          tw.textGray7.ml4
                        ),
                        renderContent = _.amend(tooltipDescription)
                      )()
                    }
                  )
                )
              }
            )
          )
        }
      }
  }

}
