// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.ria

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.ria.{GetRiaFundGroupParams, RiaFundGroup}
import anduin.id.fundsub.FundSubId
import com.raquo.laminar.api.L.*
import design.anduin.components.toast.Toast
import zio.ZIO
import com.anduin.stargazer.fundsub.client.FundSubRiaEndpointClient

import com.anduin.stargazer.fundsub.module.investor.ria.FetchAdvisorGroups.*

final case class FetchAdvisorGroups(
  fundSubId: FundSubId
) {

  def apply(renderChildren: FetchAdvisorGroups.GroupsRenderProps => HtmlElement = _ => div()): HtmlElement = {
    renderChildren(
      FetchAdvisorGroups.GroupsRenderProps(
        isFetchingSignal = isFetchingSignal,
        groupsSignal = groupsSignal,
        onRefetch = refetchAdvisorGroupsEvent.writer
      )
    ).amend(
      refetchAdvisorGroupsEvent.events.flatMapSwitch { _ =>
        getAdvisorGroups(fundSubId)
      } --> Observer.empty,
      onMountCallback { _ =>
        refetchAdvisorGroupsEvent.emit(())
      },
      onUnmountCallback { _ =>
        groupsVar.set(List.empty)
      }
    )
  }

  private def getAdvisorGroups(
    fundSubId: FundSubId
  ): EventStream[Unit] = {
    AirStreamUtils.taskToStream(
      for {
        _ <- ZIO.succeed(isFetchingVar.set(true))
        _ <- FundSubRiaEndpointClient
          .getRiaFundGroup(
            GetRiaFundGroupParams(
              getBy = Right(fundSubId)
            )
          )
          .map(
            _.fold(
              _ => {
                Toast.error("Failed to fetch advisor entities")
                isFetchingVar.set(false)
              },
              res => {
                groupsVar.set(res.fundRiaGroups)
                isFetchingVar.set(false)
              }
            )
          )
      } yield ()
    )
  }

}

object FetchAdvisorGroups {

  val groupsVar: Var[Seq[RiaFundGroup]] = Var(Seq.empty[RiaFundGroup])
  private val isFetchingVar: Var[Boolean] = Var(false)
  private val refetchAdvisorGroupsEvent = EventBus[Unit]()

  val groupsSignal: Signal[Seq[RiaFundGroup]] = groupsVar.signal.distinct
  val isFetchingSignal: Signal[Boolean] = isFetchingVar.signal.distinct
  val onRefetch: Observer[Unit] = refetchAdvisorGroupsEvent.writer

  final case class GroupsRenderProps(
    isFetchingSignal: Signal[Boolean],
    groupsSignal: Signal[Seq[RiaFundGroup]],
    onRefetch: Observer[Unit]
  )

}
