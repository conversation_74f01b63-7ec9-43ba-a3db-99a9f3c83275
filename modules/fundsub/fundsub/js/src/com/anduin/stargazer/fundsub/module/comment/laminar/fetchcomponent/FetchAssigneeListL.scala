// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent

import com.raquo.laminar.api.L.*
import design.anduin.components.toast.Toast

import anduin.frontend.AirStreamUtils
import anduin.id.fundsub.FundSubId
import anduin.model.notichannel.FundSubNotificationChannels
import anduin.stargazer.service.formcomment.FormCommentCommons.GetAssigneeFilterListParams
import anduin.stargazer.service.formcomment.FormCommentCommons.MentionInfo.MentionedGroupInfo
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.notiCenter.NotificationCenter

private[laminar] case class FetchAssigneeListL(
  fundSubId: FundSubId,
  skipActorGroupSignal: Signal[Boolean],
  renderChildren: FetchAssigneeListL.RenderChildren => HtmlElement
) {

  private val fetchAssigneeListEventBus = new EventBus[Unit]

  private val resultVar = Var(
    FetchAssigneeListL.Data(
      isFetching = true,
      assigneeList = Seq.empty
    )
  )

  private val trackingSignal = NotificationCenter.eventSignal(FundSubNotificationChannels.fundSubFormComment(fundSubId))

  private def fetchComments(skipActorGroup: Boolean) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      FundSubEndpointClient
        .getAssigneeList(
          GetAssigneeFilterListParams(
            fundSubId = fundSubId,
            skipActorGroup = skipActorGroup
          )
        )
        .map {
          _.fold(
            _ => {
              Toast.error("Failed to fetch assignable list")
              FetchAssigneeListL.Data(
                isFetching = false,
                assigneeList = Seq.empty
              )
            },
            response => {
              FetchAssigneeListL.Data(
                isFetching = false,
                assigneeList = response.assigneeList
              )
            }
          )
        }
    }
  }

  def apply(): HtmlElement = {
    renderChildren(
      FetchAssigneeListL.RenderChildren(
        dataSignal = resultVar.signal
      )
    ).amend(
      onMountCallback { _ =>
        NotificationCenter.subscribeSync(Seq(FundSubNotificationChannels.fundSubFormComment(fundSubId)))
      },
      fetchAssigneeListEventBus.events
        .withCurrentValueOf(skipActorGroupSignal)
        .flatMapSwitch(fetchComments) --> resultVar.writer,
      trackingSignal --> Observer[NotificationCenter.Watch] { _ =>
        fetchAssigneeListEventBus.emit(())
      }
    )
  }

}

private[laminar] object FetchAssigneeListL {

  case class Data(
    isFetching: Boolean,
    assigneeList: Seq[MentionedGroupInfo]
  )

  case class RenderChildren(dataSignal: Signal[Data])
}
