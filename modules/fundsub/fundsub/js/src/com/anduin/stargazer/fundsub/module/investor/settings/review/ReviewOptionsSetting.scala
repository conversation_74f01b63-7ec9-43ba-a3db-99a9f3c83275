// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.review

import design.anduin.components.radio.Radio
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import zio.ZIO

import anduin.fundsub.endpoint.reviewpackage.UpdateReviewPackageSettingParams
import anduin.id.fundsub.FundSubId
import anduin.service.GeneralServiceException
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils

private[review] final case class ReviewOptionsSetting(
  fundSubId: FundSubId,
  isEnabledUnsignedReviewSetting: Boolean,
  onChangeOption: Boolean => Callback,
  onFailed: Callback
) {
  def apply(): VdomElement = ReviewOptionsSetting.component(this)
}

private[review] object ReviewOptionsSetting {

  private type Props = ReviewOptionsSetting

  def render(props: Props): VdomElement = {
    React.Fragment(
      <.div(
        Radio(
          isChecked = !props.isEnabledUnsignedReviewSetting,
          onChange = onUpdateOptions(props, isEnabledUnsignedReviewSetting = false)
        )(
          <.div(
            <.div(tw.fontSemiBold, "Only review signed documents"),
            <.div(
              tw.mt4,
              s"Team members assigned as reviewers will be notified to review the ${FundSubCopyUtils.getFlowTerm.standAloneTerm} as soon as it is signed by the",
              <.br(),
              "investor."
            )
          )
        )
      ),
      <.div(
        tw.mt16,
        Radio(
          isChecked = props.isEnabledUnsignedReviewSetting,
          onChange = onUpdateOptions(props, isEnabledUnsignedReviewSetting = true)
        )(
          <.div(
            <.div(tw.fontSemiBold, "Review both signed and unsigned documents"),
            <.div(
              tw.mt4,
              s"Allow investors to request a review of their ${FundSubCopyUtils.getFlowTerm.standAloneTerm} before they sign. Team members assigned as reviewers",
              <.br(),
              s"will be notified to review the ${FundSubCopyUtils.getFlowTerm.standAloneTerm} when the investor requests a review of unsigned ${FundSubCopyUtils.getFlowTerm.standAloneTerm} and as",
              <.br(),
              "soon as it is signed by investor."
            )
          )
        )
      )
    )
  }

  private def onUpdateOptions(props: Props, isEnabledUnsignedReviewSetting: Boolean) = {
    for {
      _ <- props.onChangeOption(isEnabledUnsignedReviewSetting)
      _ <-
        ZIOUtils.toReactCallback(
          FundSubEndpointClient
            .updateReviewPackageSetting(
              UpdateReviewPackageSettingParams(
                props.fundSubId,
                isEnabledUnsignedReviewSetting = isEnabledUnsignedReviewSetting
              )
            )
            .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
            .map(
              _.fold(
                _ => Toast.errorCallback("Failed to change setting, try again") >> props.onFailed,
                _ => Callback.empty
              )
            )
        )
    } yield ()
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
