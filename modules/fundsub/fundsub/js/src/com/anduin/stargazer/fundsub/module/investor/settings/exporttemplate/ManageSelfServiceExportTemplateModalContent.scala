// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.exporttemplate

import java.time.Instant

import com.raquo.laminar.api.L.*
import design.anduin.components.button.Button
import design.anduin.components.clipboard.react.CopyToClipboardR
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.menu.react.{MenuDividerR, MenuItemR, MenuR}
import design.anduin.components.modal.{Modal, ModalBody, ModalFooterWCancel}
import design.anduin.components.nonidealstate.react.NonIdealStateR
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.{PortalPosition, PortalUtils, PortalWrapper}
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.components.tag.react.TagR
import design.anduin.components.text.react.TruncateR
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.tour.TourAppearance
import design.anduin.components.tour.react.{TourR, TourStepR, TourTargetR}
import design.anduin.components.wrapper.react.WrapperR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import design.anduin.table.react.TableWithMetaDataR
import design.anduin.table.react.TableWithMetaDataR.SelectableMode
import io.circe.Codec
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.{^, *}

import anduin.actionlogger.ActionEventLoggerJs
import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.fundsub.endpoint.common.UpdateUserTrackingParams
import anduin.fundsub.endpoint.dataexport.SelfServiceExportTemplateDetailInfo
import anduin.fundsub.model.FundSubSharedClientModel.ParticipantInfo
import anduin.id.fundsub.{FundSubId, FundSubSelfServiceExportTemplateId}
import anduin.model.codec.MapCodecs.given
import anduin.model.common.user.UserId
import anduin.utils.DateTimeUtils
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.{FundSubDataExportEndpointClient, FundSubEndpointClient}
import com.anduin.stargazer.fundsub.module.investor.dashboard.lp.dataexport.ExportInvestorDataSelectFilterModal
import com.anduin.stargazer.fundsub.module.investor.dashboard.lp.dataexport.ExportInvestorDataSelectTemplate.TemplateType
import com.anduin.stargazer.fundsub.module.model.FundSubClientModel.InvestmentFundClientModel

case class ManageSelfServiceExportTemplateModalContent(
  fundSubId: FundSubId,
  investmentFunds: Seq[InvestmentFundClientModel],
  onCloseModal: Callback
) {
  def apply(): VdomElement = ManageSelfServiceExportTemplateModalContent.component(this)
}

object ManageSelfServiceExportTemplateModalContent {
  private type Props = ManageSelfServiceExportTemplateModalContent

  private val ApiExportTemplateGuideTourId = "api_export_template-tour"
  private val CreateExportTemplateStop = "create_export_template_step"

  private case class State(
    templateInfos: List[SelfServiceExportTemplateDetailInfo] = List.empty,
    userInfosMap: Map[UserId, ParticipantInfo] = Map.empty,
    isFetchingTemplates: Boolean = true,
    userSeenApiExportTemplateGuideTour: Boolean = false
  )

  private case class TemplateTableRowData(
    templateName: String,
    templateId: FundSubSelfServiceExportTemplateId,
    forApiExport: Boolean,
    lastUpdatedAt: Option[Instant],
    fieldsCount: Int,
    lastUpdatedBy: UserId
  ) derives CanEqual

  private object TemplateTableRowData {
    given Codec.AsObject[TemplateTableRowData] = deriveCodecWithDefaults
  }

  private case class TemplateTableRowMetaData(
    existingTemplateNames: Set[String],
    fundSubId: FundSubId,
    investmentFunds: Seq[InvestmentFundClientModel],
    userInfosMap: Map[UserId, ParticipantInfo]
  ) derives CanEqual

  private object TemplateTableRowMetaData {
    given Codec.AsObject[TemplateTableRowMetaData] = deriveCodecWithDefaults
  }

  private val TableOfTemplates = (new TableWithMetaDataR[TemplateTableRowData, TemplateTableRowMetaData])()

  private case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      <.div(
        ModalBody()(
          if (state.isFetchingTemplates) {
            BlockIndicatorR()()
          } else if (state.templateInfos.nonEmpty) {
            <.div(
              renderSelfServiceTemplateDescription,
              <.div(
                tw.mt16.spaceY16,
                TourR(
                  steps = Seq(
                    TourStepR(
                      isDynamic = true,
                      target = CreateExportTemplateStop,
                      title = "Create custom template for APIs",
                      content = <.div(
                        <.div(
                          tw.mb12,
                          "Templates are now available for API integrations"
                        ),
                        <.img(^.src := "/web/gondor/images/fundsub/exporttemplate/api-export-template-guide.svg")
                      ),
                      popoverPosition = PortalPosition.RightTop
                    )
                  ),
                  tourId = ApiExportTemplateGuideTourId,
                  isRunning = !state.userSeenApiExportTemplateGuideTour,
                  onSkip = markApiExportTemplateGuideTourAsSeen,
                  onFinish = markApiExportTemplateGuideTourAsSeen,
                  appearance = TourAppearance.Dark,
                  isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false))
                )(),
                renderCreateTemplateButton(props, state),
                renderExportTemplatesTable(props, state)
              )
            )
          } else {
            renderNoTemplatesCreated(props, state)
          }
        ),
        ModalFooterWCancel(cancel = props.onCloseModal, cancelLabel = "Close")()
      )
    }

    private def renderNoTemplatesCreated(props: Props, state: State) = {
      <.div(
        ^.height := 452.px,
        tw.flex.itemsCenter,
        NonIdealStateR(
          icon = <.img(^.src := "/web/gondor/images/fundsub/permission/dashboard_illustration.svg"),
          title = "No custom templates yet",
          action = renderCreateTemplateButton(props, state),
          description = <.div(
            tw.textCenter,
            renderSelfServiceTemplateDescription
          )
        )()
      )
    }

    private def renderCreateTemplateButton(props: Props, state: State): VdomElement = {
      PopoverR(
        position = PortalPosition.BottomLeft,
        renderTarget = (onOpenPopover, isOpened) =>
          TourTargetR(
            target = CreateExportTemplateStop,
            tourIds = List(ApiExportTemplateGuideTourId)
          )(
            Button(
              style = Button.Style.Full(
                icon = Some(Icon.Glyph.Plus),
                color = Button.Color.Primary,
                isSelected = isOpened
              ),
              testId = "CreateTemplateBtn",
              onClick = onOpenPopover
            )(
              <.span(tw.flexFill.textLeft, "Create template"),
              <.span(tw.flexNone.ml8, IconR(name = Icon.Glyph.CaretDown)())
            )
          ),
        renderContent = onClosePopover =>
          MenuR()(
            renderCreateExcelTemplateMenuItem(props, state, onClosePopover),
            renderCreateApiTemplateMenuItem(props, state, onClosePopover)
          )
      )()
    }

    private def renderCreateExcelTemplateMenuItem(props: Props, state: State, onClosePopover: Callback): VdomElement = {
      Modal(
        renderTarget = open =>
          MenuItemR(
            icon = Some(Icon.Glyph.FileExport),
            onClick = open
          )("For Excel export"),
        renderContent = close =>
          CreateNewOrDuplicateSelfServiceTemplateModal(
            fundSubId = props.fundSubId,
            closeModal = close >> onClosePopover,
            existingTemplateNames = state.templateInfos.map(_.templateName).toSet,
            forApiExport = false,
            investmentFunds = props.investmentFunds,
            refetchTemplates = fetchExportTemplates
          )(),
        title = "Create template",
        isClosable = Some(PortalUtils.IsClosable(onOutsideClick = false, onEsc = false))
      )()
    }

    private def renderCreateApiTemplateMenuItem(props: Props, state: State, onClosePopover: Callback): VdomElement = {
      Modal(
        renderTarget = open =>
          TooltipR(
            renderTarget = MenuItemR(
              icon = Some(Icon.Glyph.CodeLine),
              onClick = open
            )("For API integration"),
            renderContent = _("Create template for API integrations"),
            position = PortalPosition.TopRight
          )(),
        renderContent = close =>
          CreateNewOrDuplicateSelfServiceTemplateModal(
            fundSubId = props.fundSubId,
            closeModal = close >> onClosePopover,
            existingTemplateNames = state.templateInfos.map(_.templateName).toSet,
            forApiExport = true,
            investmentFunds = props.investmentFunds,
            refetchTemplates = fetchExportTemplates
          )(),
        title = "Create API template",
        isClosable = Some(PortalUtils.IsClosable(onOutsideClick = false, onEsc = false))
      )()
    }

    private def renderExportTemplatesTable(props: Props, state: State) = {
      <.div(
        ^.height := 360.px,
        tw.overflowYAuto,
        TableOfTemplates(
          columns = List(
            templateNameColumn,
            templateIdColumn,
            fieldsCountColumn,
            lastUpdatedColumn,
            actionColumn
          ),
          options = TableWithMetaDataR.Options(
            rowHeight = Some(69.0),
            indexColumn = Some("templateId"),
            layout = TableWithMetaDataR.Layout.FitColumns,
            selectableMode = SelectableMode.Unselectable
          ),
          data = state.templateInfos.map { templateInfo =>
            TemplateTableRowData(
              templateName = templateInfo.templateName,
              templateId = templateInfo.templateId,
              forApiExport = templateInfo.forApiExport,
              lastUpdatedAt = templateInfo.lastUpdatedAt,
              fieldsCount = templateInfo.fieldsCount,
              lastUpdatedBy = templateInfo.lastUpdatedBy
            )
          },
          initialSortColumns = List(
            TableWithMetaDataR
              .SortColumn(column = lastUpdatedColumn, direction = TableWithMetaDataR.ColumnSortDirection.Desc)
          ),
          metaData = TemplateTableRowMetaData(
            existingTemplateNames = state.templateInfos.map(_.templateName).toSet,
            fundSubId = props.fundSubId,
            investmentFunds = props.investmentFunds,
            userInfosMap = state.userInfosMap
          )
        )()
      )
    }

    private val templateNameColumn: TableWithMetaDataR.Column[TemplateTableRowData, TemplateTableRowMetaData] = {
      TableWithMetaDataR.Column[TemplateTableRowData, TemplateTableRowMetaData](
        field = "templateName",
        isSortable = true,
        title = "Template",
        renderCell = renderProps => {
          renderProps.metaData.fold(
            EmptyVdom
          ) { metaData =>
            Modal(
              renderTarget = open =>
                <.div(
                  tw.wPc100.hPc100.flex.itemsCenter.spaceX8,
                  ^.onClick --> open,
                  TooltipR(
                    renderTarget = <.div(
                      tw.flex.spaceX8.itemsCenter,
                      <.div(
                        tw.textGray6,
                        IconR(name = Icon.Glyph.Table)()
                      ),
                      <.div(
                        tw.truncate.flexFill,
                        renderProps.data.templateName
                      )
                    ),
                    renderContent = _("Click to edit"),
                    targetWrapper = PortalWrapper.BlockContent
                  )(),
                  if (renderProps.data.forApiExport) {
                    TooltipR(
                      renderTarget = TagR(label = "API")(),
                      renderContent = _("This template is used for API")
                    )()
                  } else {
                    EmptyVdom
                  }
                ),
              renderContent = close =>
                WrapperR(
                  node = SelectFieldsForSelfServiceExport(
                    fundSubId = metaData.fundSubId,
                    initialTemplateName = renderProps.data.templateName,
                    forApiExportSignal = Val(renderProps.data.forApiExport),
                    onCloseOuterModal = Observer(_ => close.runNow()),
                    investmentFundsSignal = Val(metaData.investmentFunds),
                    onRefetchTemplates = Observer(_ => fetchExportTemplates.runNow()),
                    openTemplateMode =
                      SelectFieldsForSelfServiceExport.OpenTemplateMode.Edit(renderProps.data.templateId),
                    existingTemplateNamesSignal = Val(metaData.existingTemplateNames - renderProps.data.templateName)
                  )(),
                  containerTag = <.div(tw.hPc100)
                )(),
              size = Modal.Size(width = Modal.Width.Full, height = Modal.Height.Full),
              isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false))
            )()
          }
        }
      )
    }

    private val templateIdColumn: TableWithMetaDataR.Column[TemplateTableRowData, TemplateTableRowMetaData] = {
      TableWithMetaDataR.Column[TemplateTableRowData, TemplateTableRowMetaData](
        field = "templateId",
        isSortable = true,
        title = "Template ID",
        renderCell = renderProps =>
          CopyToClipboardR(
            content = renderProps.data.templateId.idString
          )(),
        maxWidth = Some(300.0)
      )
    }

    private val fieldsCountColumn: TableWithMetaDataR.Column[TemplateTableRowData, TemplateTableRowMetaData] = {
      TableWithMetaDataR.Column[TemplateTableRowData, TemplateTableRowMetaData](
        field = "fieldsCount",
        isSortable = true,
        title = "Fields",
        renderCell = renderProps => <.div(renderProps.data.fieldsCount),
        maxWidth = Some(120.0)
      )
    }

    private val lastUpdatedColumn: TableWithMetaDataR.Column[TemplateTableRowData, TemplateTableRowMetaData] = {
      TableWithMetaDataR.Column[TemplateTableRowData, TemplateTableRowMetaData](
        field = "lastUpdatedAt",
        isSortable = true,
        title = "Last edited",
        renderCell = renderProps => {
          val userInfosMap = renderProps.metaData.fold[Map[UserId, ParticipantInfo]](Map.empty)(_.userInfosMap)
          <.div(
            tw.wPc100.textWrap,
            renderProps.data.lastUpdatedAt.fold[VdomNode]("-") { lastUpdatedAt =>
              TruncateR(
                lineClamp = Some(2),
                target = <.div(
                  DateTimeUtils.formatInstant(lastUpdatedAt, DateTimeUtils.DateAndTimeFormatter2)(
                    using DateTimeUtils.defaultTimezone
                  ),
                  userInfosMap.get(renderProps.data.lastUpdatedBy).map { userInfo =>
                    <.span(
                      s" by ${userInfo.fullName}"
                    )
                  }
                )
              )()
            }
          )
        }
      )
    }

    private val actionColumn: TableWithMetaDataR.Column[TemplateTableRowData, TemplateTableRowMetaData] = {
      TableWithMetaDataR.Column[TemplateTableRowData, TemplateTableRowMetaData](
        field = "",
        isSortable = false,
        title = "",
        renderCell = renderProps =>
          <.div(
            PopoverR(
              renderTarget = (open, isOpened) =>
                Button(
                  style = Button.Style.Minimal(icon = Some(Icon.Glyph.EllipsisHorizontal), isSelected = isOpened),
                  onClick = open
                )(),
              renderContent =
                onClosePopover => renderActionPopoverContent(renderProps.data, renderProps.metaData, onClosePopover),
              position = PortalPosition.TopLeft
            )()
          ),
        maxWidth = Some(56.0)
      )
    }

    private def renderActionPopoverContent(
      rowData: TemplateTableRowData,
      rowMetaData: Option[TemplateTableRowMetaData],
      onClosePopover: Callback
    ) = {
      MenuR()(
        renderEditTemplateAction(rowData, rowMetaData, onClosePopover),
        renderDuplicateTemplateAction(rowData, rowMetaData, onClosePopover),
        if (!rowData.forApiExport) {
          renderExportLpsDataUsingTemplate(rowData, rowMetaData, onClosePopover)
        } else {
          EmptyVdom
        },
        MenuDividerR()(),
        renderDeleteTemplate(rowData, onClosePopover)
      )
    }

    private def renderEditTemplateAction(
      rowData: TemplateTableRowData,
      rowMetaData: Option[TemplateTableRowMetaData],
      onClosePopover: Callback
    ) = {
      rowMetaData.fold(
        EmptyVdom
      ) { metaData =>
        Modal(
          renderTarget = open =>
            MenuItemR(
              icon = Some(Icon.Glyph.Edit),
              onClick = open
            )("Edit template"),
          renderContent = close =>
            WrapperR(
              node = SelectFieldsForSelfServiceExport(
                fundSubId = metaData.fundSubId,
                initialTemplateName = rowData.templateName,
                forApiExportSignal = Val(rowData.forApiExport),
                onCloseOuterModal = Observer(_ => onClosePopover.runNow()),
                investmentFundsSignal = Val(metaData.investmentFunds),
                onRefetchTemplates = Observer(_ => fetchExportTemplates.runNow()),
                openTemplateMode = SelectFieldsForSelfServiceExport.OpenTemplateMode.Edit(rowData.templateId),
                existingTemplateNamesSignal = Val(metaData.existingTemplateNames - rowData.templateName)
              )(),
              containerTag = <.div(tw.hPc100)
            )(),
          size = Modal.Size(width = Modal.Width.Full, height = Modal.Height.Full),
          isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false))
        )()
      }
    }

    private def renderDuplicateTemplateAction(
      rowData: TemplateTableRowData,
      rowMetaData: Option[TemplateTableRowMetaData],
      onClosePopover: Callback
    ) = {
      rowMetaData.fold(EmptyVdom) { metaData =>
        Modal(
          renderTarget = open =>
            MenuItemR(
              icon = Some(Icon.Glyph.Duplicate),
              onClick = open
            )("Duplicate template"),
          renderContent = close =>
            CreateNewOrDuplicateSelfServiceTemplateModal(
              fundSubId = metaData.fundSubId,
              closeModal = close >> onClosePopover,
              existingTemplateNames = rowMetaData.fold(Set.empty)(_.existingTemplateNames),
              investmentFunds = rowMetaData.fold(Seq.empty)(_.investmentFunds),
              refetchTemplates = fetchExportTemplates,
              duplicatedFromOpt = Some(rowData.templateId),
              initialTemplateNameOpt = Some(s"Duplicated template from ${rowData.templateName}"),
              forApiExport = rowData.forApiExport
            )(),
          isClosable = Some(PortalUtils.IsClosable(onOutsideClick = false, onEsc = false)),
          title = "Duplicate template"
        )()
      }
    }

    private def renderExportLpsDataUsingTemplate(
      rowData: TemplateTableRowData,
      rowMetaData: Option[TemplateTableRowMetaData],
      onClosePopover: Callback
    ) = {
      rowMetaData.fold(
        EmptyVdom
      ) { metaData =>
        Modal(
          title = "Select investors",
          renderTarget = open =>
            MenuItemR(
              icon = Some(Icon.Glyph.FileExport),
              onClick = open
            )("Export data"),
          renderContent = closeModal =>
            ExportInvestorDataSelectFilterModal(
              fundSubId = metaData.fundSubId,
              onClose = closeModal,
              templateType = TemplateType.SelfServiceTemplate(rowData.templateId),
              lpIds = Seq.empty,
              filterProps = None
            )(),
          size = Modal.Size(width = Modal.Width.Px960),
          afterUserClose = onClosePopover,
          isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false))
        )()
      }
    }

    private def renderDeleteTemplate(
      rowData: TemplateTableRowData,
      onClosePopover: Callback
    ) = {
      Modal(
        renderTarget = open =>
          MenuItemR(
            onClick = open,
            color = MenuItemR.ColorDanger,
            icon = Some(Icon.Glyph.Trash)
          )("Delete template"),
        renderContent = onCloseModal =>
          DeleteTemplateConfirmationModal(
            templateId = rowData.templateId,
            templateName = rowData.templateName,
            onCloseModal = onCloseModal,
            refetchTemplates = fetchExportTemplates
          )(),
        title = "Delete template",
        isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false)),
        afterUserClose = onClosePopover
      )()
    }

    private def renderSelfServiceTemplateDescription = {
      <.div(
        tw.textGray8,
        "Custom templates let you choose specific fields from PDFs and dashboard columns to export into" +
          " Excel or via API"
      )
    }

    private def markApiExportTemplateGuideTourAsSeen: Callback = {
      scope.modState(
        _.copy(userSeenApiExportTemplateGuideTour = true),
        ZIOUtils.toReactCallback(
          FundSubEndpointClient
            .updateUserTracking(
              UpdateUserTrackingParams(
                seenApiExportTemplateGuideTourOpt = Option(true)
              )
            )
            .map(
              _.fold(
                _ => Toast.errorCallback("Failed to update user tracking info, try again"),
                _ => Callback.empty
              )
            )
        )
      )
    }

    private def fetchExportTemplates: Callback = {
      for {
        props <- scope.props
        _ <- ZIOUtils.toReactCallback(
          FundSubDataExportEndpointClient
            .getSelfServiceExportTemplatesForFundSetting(props.fundSubId)
            .map(
              _.fold(
                _ =>
                  scope.modState(
                    _.copy(isFetchingTemplates = false),
                    Toast.errorCallback("Failed to fetch export templates, please try again")
                  ),
                resp =>
                  scope.modState(
                    _.copy(
                      templateInfos = resp.templateInfos,
                      userInfosMap = resp.userInfosMap,
                      userSeenApiExportTemplateGuideTour = resp.userSeenApiExportTemplateGuideTour,
                      isFetchingTemplates = false
                    )
                  )
              )
            )
        )
      } yield ()
    }

    def didMount: Callback = {
      for {
        _ <- fetchExportTemplates
        _ <- ActionEventLoggerJs.logPageViewCb(subPage = Some("Manage self-service export templates"))
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .componentDidMount(_.backend.didMount)
    .build

}
