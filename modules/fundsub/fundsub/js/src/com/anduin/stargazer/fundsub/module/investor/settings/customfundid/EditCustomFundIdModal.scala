// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.customfundid

import design.anduin.components.button.Button
import design.anduin.components.modal.{ModalBody, ModalFooterWCancel}
import design.anduin.components.textbox.TextBox
import design.anduin.components.toast.Toast
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import anduin.fundsub.endpoint.lp.UpdateCustomFundIdSettingParams
import anduin.id.fundsub.FundSubId
import anduin.utils.StringUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import com.anduin.stargazer.client.utils.ZIOUtils

private[customfundid] final case class EditCustomFundIdModal(
  fundSubId: FundSubId,
  customFundIdWording: String,
  editType: EditCustomFundIdModal.EditType,
  initialCustomFundId: String,
  onUpdated: String => Callback,
  onCancel: Callback
) {
  def apply(): VdomElement = EditCustomFundIdModal.component(this)
}

private[customfundid] object EditCustomFundIdModal {

  private type Props = EditCustomFundIdModal

  sealed trait EditType derives CanEqual

  object EditType {
    case object EnableNew extends EditType
    case object EditExisting extends EditType
  }

  private final case class State(
    id: String,
    isUpdating: Boolean = false
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      <.div(
        ModalBody()(
          <.div(
            tw.mb12,
            s"Enter ${StringUtils.getDeterminer(props.customFundIdWording)} ${props.customFundIdWording}"
          ),
          <.div(
            ComponentUtils.testId(EditCustomFundIdModal, "IdTextBox"),
            TextBox(
              value = state.id,
              onChange = value => scope.modState(_.copy(id = value)),
              placeholder = props.customFundIdWording,
              isDisabled = state.isUpdating
            )()
          )
        ),
        renderFooter(props, state)
      )
    }

    private def renderFooter(props: Props, state: State) = {
      val (isDisabled, label) = props.editType match {
        case EditType.EnableNew =>
          state.id.trim.isEmpty -> "Enable"
        case EditType.EditExisting =>
          (state.id.trim.isEmpty || state.id == props.initialCustomFundId) -> "Save"
      }
      ModalFooterWCancel(cancel = props.onCancel)(
        Button(
          style = Button.Style.Full(color = Button.Color.Primary, isBusy = state.isUpdating),
          isDisabled = isDisabled,
          onClick = updateSetting()
        )(label)
      )
    }

    private def updateSetting(): Callback = {
      for {
        props <- scope.props
        state <- scope.state
        _ <- scope.modState(
          _.copy(isUpdating = true),
          ZIOUtils.toReactCallback {
            FundSubEndpointClient
              .updateCustomFundIdSetting(
                UpdateCustomFundIdSettingParams(
                  props.fundSubId,
                  isEnabled = true,
                  state.id
                )
              )
              .map { resp =>
                scope.modState(
                  _.copy(isUpdating = false),
                  resp.fold(
                    _ => Toast.errorCallback(s"Failed to update ${props.customFundIdWording}, try again"),
                    _ => Toast.successCallback(s"${props.customFundIdWording} updated") >> props.onUpdated(state.id)
                  )
                )
              }
          }
        )
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps { props =>
      State(props.initialCustomFundId)
    }
    .renderBackend[Backend]
    .build

}
