// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings

import design.anduin.components.table.Table
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.endpoint.graphql.MetadataField

private[settings] final case class MetadataTable(metadata: Seq[MetadataField]) {
  def apply(): VdomElement = MetadataTable.component(this)
}

private[settings] object MetadataTable {
  private type Props = MetadataTable

  private val MetadataTable = (new Table[MetadataField])()

  private val NameColumnWidth = 250
  private val ValueColumnWidth = 300

  private def renderNameCell(metadataField: MetadataField) = {
    Table.Cell(
      content = <.div(
        ^.maxWidth := s"${NameColumnWidth}px",
        tw.truncate.fontMedium.leading16,
        metadataField.key
      )
    )
  }

  private def renderValueCell(metadataField: MetadataField) = {
    Table.Cell(
      content = <.div(
        ^.maxWidth := s"${ValueColumnWidth}px",
        tw.textGray8.truncate,
        metadataField.value
      )
    )
  }

  private def render(props: Props): VdomElement = {
    MetadataTable(
      columns = Seq[Table.Column[MetadataField]](
        Table.Column[MetadataField](
          head = "Name",
          render = renderNameCell,
          width = s"${NameColumnWidth}px"
        ),
        Table.Column[MetadataField](
          head = "Value",
          render = renderValueCell,
          width = s"${ValueColumnWidth}px"
        )
      ),
      rows = props.metadata,
      getKey = _.key,
      testId = "MetadataTable"
    )()
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
