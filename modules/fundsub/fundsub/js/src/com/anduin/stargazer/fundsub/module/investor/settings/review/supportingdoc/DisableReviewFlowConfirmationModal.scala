// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.review.supportingdoc

import design.anduin.components.button.Button
import design.anduin.components.modal.{ModalBody, ModalFooterWCancel}
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import fundsub.review.supportingdoc.SupportingDocReviewConfigMode.SUPPORTING_DOC_REVIEW_CONFIG_MODE_DISABLED
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.endpoint.review.UpdateSupportingDocReviewConfigParams
import anduin.id.fundsub.FundSubId
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubSupportingDocReviewEndpointClient

private[settings] final case class DisableReviewFlowConfirmationModal(
  fundId: FundSubId,
  onClose: Callback,
  onDisable: Callback
) {
  def apply(): VdomElement = DisableReviewFlowConfirmationModal.component(this)
}

private[settings] object DisableReviewFlowConfirmationModal {
  private type Props = DisableReviewFlowConfirmationModal

  private final case class State(
    isUpdating: Boolean = false
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      React.Fragment(
        ModalBody()(
          <.div(
            "The review workflow for AML/KYC and other documents will be removed along with any document groups"
          ),
          <.div(
            tw.mt8,
            "Are you sure you want to continue?"
          )
        ),
        ModalFooterWCancel(props.onClose)(
          Button(
            testId = "Disable",
            style = Button.Style.Full(
              color = Button.Color.Danger,
              isBusy = state.isUpdating
            ),
            onClick = disableReviewFlow
          )("Disable review workflow")
        )
      )
    }

    private def disableReviewFlow = {
      val cb = for {
        props <- scope.props
        _ <- ZIOUtils.toReactCallback {
          FundSubSupportingDocReviewEndpointClient
            .updateFundSubSupportingDocReviewConfig(
              UpdateSupportingDocReviewConfigParams(
                fundSubId = props.fundId,
                mode = SUPPORTING_DOC_REVIEW_CONFIG_MODE_DISABLED
              )
            )
            .map(
              _.fold(
                ex =>
                  scope.modState(
                    _.copy(isUpdating = false),
                    Toast.errorCallback(s"Failed to disable review flow: ${ex.message}")
                  ),
                _ =>
                  scope.modState(
                    _.copy(isUpdating = false),
                    props.onDisable
                  )
              )
            )
        }
      } yield ()

      scope.modState(
        _.copy(isUpdating = true),
        cb
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
