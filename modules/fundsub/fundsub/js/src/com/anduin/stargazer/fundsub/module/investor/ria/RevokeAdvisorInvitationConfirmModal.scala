// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.ria

import design.anduin.style.tw.*
import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL}
import design.anduin.components.toast.Toast
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.ria.RevokeFundAdvisorInvitationParams
import anduin.fundsub.endpoint.ria.RiaFundGroupState.InvitedAdvisor
import anduin.id.fundsub.ria.FundSubRiaGroupId
import com.anduin.stargazer.fundsub.client.FundSubRiaEndpointClient

final case class RevokeAdvisorInvitationConfirmModal(
  fundSubRiaGroupId: FundSubRiaGroupId,
  toRevokeAdvisor: InvitedAdvisor,
  onClose: Observer[Unit],
  onDoneRevokeAdvisorInvitation: Observer[Unit]
) {

  private val isRevokingAdvisorVar = Var(false)
  private val isRevokingAdvisorSignal = isRevokingAdvisorVar.signal.distinct

  private val revokeAdvisorEvent = new EventBus[Unit]

  def apply(): Node = {
    div(
      ModalBodyL(
        div(
          span(
            tw.fontSemiBold,
            toRevokeAdvisor.info.userInfo.fullNameString
          ),
          span(s"(${toRevokeAdvisor.info.userInfo.emailAddressStr}) won't be able to join this fund as an advisor.")
        )
      ),
      ModalFooterWCancelL(
        cancel = onClose,
        cancelLabel = "Keep invitation",
        isCancelDisabled = isRevokingAdvisorSignal
      )(
        ButtonL(
          style = ButtonL.Style.Full(
            color = ButtonL.Color.Danger,
            isBusy = isRevokingAdvisorSignal
          ),
          onClick = revokeAdvisorEvent.toObserver.contramap(_ => ())
        )("Cancel").amend(
          revokeAdvisorEvent.events.flatMapSwitch { _ =>
            handleRevokeAdvisorInvitation()
          } --> Observer.empty
        )
      )
    )
  }

  private def handleRevokeAdvisorInvitation() = {
    AirStreamUtils.taskToStream(
      for {
        _ <- ZIO.succeed(isRevokingAdvisorVar.set(true))
        _ <- FundSubRiaEndpointClient
          .revokeFundAdvisorInvitation(
            RevokeFundAdvisorInvitationParams(
              fundSubRiaGroupId = fundSubRiaGroupId,
              advisor = toRevokeAdvisor.info.userId
            )
          )
          .map(
            _.fold(
              error => {
                isRevokingAdvisorVar.set(false)
                Toast.error("Failed to cancel advisor invitation")
              },
              res => {
                isRevokingAdvisorVar.set(false)
                onClose.onNext(())
                onDoneRevokeAdvisorInvitation.onNext(())
                Toast.success("Advisor invitation canceled")
              }
            )
          )
      } yield ()
    )
  }

}
