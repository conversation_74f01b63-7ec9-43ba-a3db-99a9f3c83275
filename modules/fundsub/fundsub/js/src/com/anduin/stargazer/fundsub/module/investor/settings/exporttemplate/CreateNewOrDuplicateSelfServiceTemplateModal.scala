// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.exporttemplate

import com.raquo.airstream.core.Observer
import com.raquo.laminar.api.L.*
import design.anduin.components.button.Button
import design.anduin.components.field.Field
import design.anduin.components.modal.{Modal, ModalBody, ModalFooterWCancel}
import design.anduin.components.portal.PortalUtils
import design.anduin.components.textbox.TextBox
import design.anduin.components.wrapper.react.WrapperR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.id.fundsub.{FundSubId, FundSubSelfServiceExportTemplateId}
import com.anduin.stargazer.fundsub.module.investor.settings.exporttemplate.SelectFieldsForSelfServiceExport.OpenTemplateMode
import com.anduin.stargazer.fundsub.module.model.FundSubClientModel.InvestmentFundClientModel

private[exporttemplate] case class CreateNewOrDuplicateSelfServiceTemplateModal(
  fundSubId: FundSubId,
  closeModal: Callback,
  existingTemplateNames: Set[String],
  initialTemplateNameOpt: Option[String] = None,
  forApiExport: Boolean,
  investmentFunds: Seq[InvestmentFundClientModel],
  refetchTemplates: Callback,
  duplicatedFromOpt: Option[FundSubSelfServiceExportTemplateId] = None
) {
  def apply(): VdomElement = CreateNewOrDuplicateSelfServiceTemplateModal.component(this)
}

private[exporttemplate] object CreateNewOrDuplicateSelfServiceTemplateModal {
  private type Props = CreateNewOrDuplicateSelfServiceTemplateModal

  private case class State(
    templateName: String
  )

  private case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      val isTemplateNameDuplicate = props.existingTemplateNames.contains(state.templateName)
      val isTemplateNameInvalid = state.templateName.isEmpty
      React.Fragment(
        ModalBody()(
          <.div(
            Field(
              label = Some("Enter template name"),
              requirement = Field.Requirement.Required,
              validation = if (isTemplateNameDuplicate) {
                Field.Validation.Invalid(
                  "A template with this name already exists. Please enter a different name."
                )
              } else {
                Field.Validation.None
              }
            )(
              TextBox(
                value = state.templateName,
                onChange = name => scope.modState(_.copy(templateName = name)),
                isRequired = true,
                status = if (isTemplateNameDuplicate) {
                  TextBox.Status.Invalid
                } else {
                  TextBox.Status.None
                },
                isAutoFocus = true
              )()
            )
          )
        ),
        ModalFooterWCancel(
          cancel = props.closeModal
        )(
          Modal(
            renderTarget = open =>
              Button(
                onClick = open,
                style = Button.Style.Full(color = Button.Color.Primary),
                isDisabled = isTemplateNameInvalid || isTemplateNameDuplicate
              )("Next: Select fields"),
            renderContent = _ =>
              WrapperR(
                node = SelectFieldsForSelfServiceExport(
                  fundSubId = props.fundSubId,
                  initialTemplateName = state.templateName,
                  forApiExportSignal = Val(props.forApiExport),
                  onCloseOuterModal = Observer(_ => props.closeModal.runNow()),
                  investmentFundsSignal =
                    Val(props.investmentFunds), // this param may need to be inside a reactive wrapper
                  onRefetchTemplates = Observer(_ => props.refetchTemplates.runNow()),
                  openTemplateMode = props.duplicatedFromOpt
                    .fold[OpenTemplateMode](OpenTemplateMode.CreateNew)(OpenTemplateMode.Duplicate(_)),
                  existingTemplateNamesSignal = Val(props.existingTemplateNames - state.templateName)
                )(),
                containerTag = <.div(tw.hPc100)
              )(),
            size = Modal.Size(height = Modal.Height.Full, width = Modal.Width.Full),
            isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false))
          )()
        )
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialStateFromProps(props => State(templateName = props.initialTemplateNameOpt.getOrElse("")))
    .renderBackend[Backend]
    .build

}
