// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.contact.modal

import java.util.concurrent.TimeUnit
import scala.concurrent.Future
import scala.concurrent.duration.FiniteDuration
import scala.util.Random

import design.anduin.components.animate.react.AutoAnimateR
import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.modal.Modal
import design.anduin.components.textbox.TextBox
import design.anduin.components.toast.Toast
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import design.anduin.validators.rules.{EmailAddressRule, RequiredRule}
import design.anduin.validators.{ErrorMessage, FieldValidator, Result, Rule}
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import org.scalajs.macrotaskexecutor.MacrotaskExecutor.Implicits.global

import anduin.contact.endpoint.ContactModel
import anduin.contact.widget.ContactWidget
import anduin.fundsub.constants.Terms
import anduin.fundsub.endpoint.contact.{
  ContactData,
  CreateContactGroupParams,
  EditContactGroupParams,
  FundSubContactInfo
}
import anduin.id.fundsub.{FundSubContactGroupId, FundSubId}
import anduin.protobuf.fundsub.contact.FundSubContactGroup
import anduin.utils.StringUtils
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

final case class AddOrEditGroupModal(
  fundSubId: FundSubId,
  // None = add new group, or edit existing group otherwise.
  groupOpt: Option[FundSubContactGroup],
  enabledCustomId: Boolean,
  allContactsInfo: FundSubContactInfo,
  onClose: Callback,
  reFetch: Callback
) {

  def apply(): VdomElement = AddOrEditGroupModal.component(this)

  private lazy val isCreateNew = groupOpt.isEmpty

  private lazy val contacts: Seq[ContactModel] =
    groupOpt.fold[Seq[ContactModel]](Seq())(allContactsInfo.groups.getOrElse(_, Seq.empty))

  private val investorNameValidator = new Rule {

    override def validate(valueCb: CallbackTo[String]): Future[CallbackTo[Result]] = {
      Future.successful {
        for {
          value <- valueCb
        } yield {
          if (value.isEmpty) {
            Result.Valid
          } else {
            val isUnchanged = groupOpt.forall(group => value.trim.equalsIgnoreCase(group.name))
            lazy val isDuplicated = allContactsInfo.groups.keys.map(_.name).exists(_.equalsIgnoreCase(value.trim))
            Result(
              isUnchanged || !isDuplicated,
              ErrorMessage("This investor contact group already exists")
            )
          }
        }
      }
    }

  }

}

object AddOrEditGroupModal {

  private type Props = AddOrEditGroupModal

  case class ContactToAdd(
    id: String = Random.alphanumeric.take(16).mkString,
    firstName: String = "",
    lastName: String = "",
    email: String = "",
    firstNameResult: Result = Result.Valid,
    lastNameResult: Result = Result.Valid,
    emailResult: Result = Result.Valid
  ) {
    lazy val isValid: Boolean = firstNameResult.valid && lastNameResult.valid && emailResult.valid

    lazy val toContactData: ContactData = ContactData(
      firstName,
      lastName,
      email,
      Seq.empty
    )

    def changeFirstName(value: String): ContactToAdd = copy(firstName = value, firstNameResult = Result.Valid)
    def changeLastName(value: String): ContactToAdd = copy(lastName = value, lastNameResult = Result.Valid)
    def changeEmail(value: String): ContactToAdd = copy(email = value, emailResult = Result.Valid)
  }

  private final case class State(
    name: String = "",
    customId: String = "",
    busy: Boolean = false,
    contacts: Seq[ContactToAdd] = Seq.empty,
    investorResult: Result = Result.Valid
  ) {

    val emailValidator: FieldValidator = FieldValidator(
      List(
        RequiredRule(ErrorMessage("Email is required")),
        EmailAddressRule(ErrorMessage("Invalid email")),
        new Rule {

          override def validate(valueCb: CallbackTo[String]): Future[CallbackTo[Result]] = {
            Future.successful {
              for {
                value <- valueCb
              } yield {
                if (value.isEmpty) {
                  Result.Valid
                } else {
                  Result(
                    contacts.map(_.email.toLowerCase).count(_ == value) < 2,
                    ErrorMessage("This email is not unique")
                  )
                }
              }
            }
          }

        }
      )
    )

  }

  private val firstNameValidator = FieldValidator(List(RequiredRule(ErrorMessage("First name is required"))))

  private val lastNameValidator = FieldValidator(List(RequiredRule(ErrorMessage("Last name is required"))))

  private class Backend(scope: BackendScope[Props, State]) {

    private def validateContact(state: State)(contactToAdd: ContactToAdd): Future[CallbackTo[ContactToAdd]] = {
      val firstName = contactToAdd.firstName.trim
      val lastName = contactToAdd.lastName.trim
      val email = contactToAdd.email.trim
      for {
        firstNameResultCb <- firstNameValidator.validate(CallbackTo.pure(firstName))
        lastNameResultCb <- lastNameValidator.validate(CallbackTo.pure(lastName))
        emailResultCb <- state.emailValidator.validate(CallbackTo.pure(email))
      } yield {
        for {
          firstNameResult <- firstNameResultCb
          lastNameResult <- lastNameResultCb
          emailResult <- emailResultCb
        } yield ContactToAdd(
          id = contactToAdd.id,
          firstName = firstName,
          lastName = lastName,
          email = email,
          firstNameResult = firstNameResult,
          lastNameResult = lastNameResult,
          emailResult = emailResult
        )
      }
    }

    private def validate(props: Props, state: State, cb: Callback) = {
      Callback.future {
        for {
          contactsCb <-
            Future
              .traverse(state.contacts)(validateContact(state))
              .map((xs: Seq[CallbackTo[ContactToAdd]]) => CallbackTo.sequence(xs))
          investorResultCb <- props.investorNameValidator.validate(CallbackTo.pure(state.name))
        } yield {
          for {
            contacts <- contactsCb
            investorResult <- investorResultCb
            _ <- scope.modState(
              _.copy(contacts = contacts, investorResult = investorResult),
              Callback.when(contacts.forall(_.isValid) && investorResult.valid)(cb)
            )
          } yield ()
        }
      }
    }

    private def onChangeContact(index: Int, info: ContactToAdd): Callback = {
      scope.modState(state =>
        state.copy(contacts =
          state.contacts.patch(
            index,
            Seq(info),
            1
          )
        )
      )
    }

    private def onRemove(index: Int): Callback = {
      scope.modState(state =>
        state.copy(contacts =
          state.contacts.patch(
            index,
            Seq.empty,
            1
          )
        )
      )
    }

    private def onAdd: Callback = {
      scope.modState(state => state.copy(contacts = state.contacts :+ ContactToAdd()))
    }

    def render(props: Props, state: State): VdomElement = {
      <.div(
        tw.p20.pt16,
        renderInvestorInput(props, state),
        <.div(
          tw.mt16.mb4.flex,
          <.div(tw.fontSemiBold, "Contacts"),
          <.div(tw.ml4.textGray6, "(optional)")
        ),
        renderContacts(state),
        <.div(tw.mt12, renderContactButtons()),
        <.div(tw.mt20, renderButtons(props, state))
      )
    }

    private def renderInvestorInput(props: Props, state: State) = {
      <.div(
        tw.flex.itemsStart,
        <.div(
          tw.mr16,
          ^.width := "404px",
          <.p(tw.fontSemiBold.mb4, s"Name of ${Terms.InvesteeEntityLabelLowerCase}"),
          <.div(
            ComponentUtils.testId(AddOrEditGroupModal, "InvestorName"),
            TextBox(
              isAutoFocus = true,
              value = state.name,
              onChange = name => scope.modState(_.copy(name = name, investorResult = Result.Valid)),
              placeholder = s"Enter ${Terms.InvesteeEntityLabelLowerCase}...",
              status = if (state.investorResult.valid) TextBox.Status.None else TextBox.Status.Invalid
            )(),
            renderResult(state.investorResult).unless(state.investorResult.valid)
          )
        ),
        TagMod.when(props.enabledCustomId) {
          <.div(
            ^.width := "220px",
            <.p(
              tw.mb4,
              <.span(tw.fontSemiBold, "Custom ID"),
              <.span(tw.textGray8, " (optional)")
            ),
            <.div(
              ComponentUtils.testId(AddOrEditGroupModal, "CustomId"),
              TextBox(
                value = state.customId,
                onChange = id => scope.modState(_.copy(customId = id)),
                placeholder = "Enter custom ID..."
              )()
            )
          )
        }
      )
    }

    private def renderContactButtons(): VdomNode = {
      <.div(
        tw.flex.textPrimary4,
        Button(style = Button.Style.Text(), onClick = onAdd)(
          <.div(
            ComponentUtils.testId(AddOrEditGroupModal, "AddButton"),
            tw.flex.itemsCenter,
            IconR(Icon.Glyph.PlusCircle)(),
            <.div(tw.ml8, "Add another")
          )
        ),
        <.div(tw.textGray7.mx12, "or"),
        Modal(
          renderTarget = open =>
            Button(style = Button.Style.Text(), onClick = open)(
              <.div(
                ComponentUtils.testId(AddOrEditGroupModal, "ImportButton"),
                tw.flex.itemsCenter,
                IconR(Icon.Glyph.UserInfo)(),
                <.div(tw.ml8, "Import from existing contacts")
              )
            ),
          renderContent = close =>
            ContactWidget(
              selection =>
                scope.modState(state =>
                  state.copy(contacts = state.contacts ++ selection.allContacts.map { contact =>
                    ContactToAdd(
                      contact.firstName,
                      contact.lastName,
                      contact.email
                    )
                  })
                ),
              close
            )(),
          size = Modal.Size(Modal.Width.Full, Modal.Height.Full)
        )()
      )
    }

    private def renderContacts(state: State): VdomNode = {
      AutoAnimateR(
        duration = FiniteDuration(250, TimeUnit.MILLISECONDS),
        renderChildren = renderProps => {
          <.div.withRef(renderProps.ref)(
            state.contacts.zipWithIndex.toVdomArray(
              using { case (contact, index) =>
                <.div(
                  ComponentUtils.testId(AddOrEditGroupModal, "ContactRow"),
                  ^.key := contact.id,
                  tw.flex.mb8,
                  <.div(
                    ComponentUtils.testId(AddOrEditGroupModal, "FirstName"),
                    ^.width := "200px",
                    tw.mr4,
                    TextBox(
                      isAutoFocus = true,
                      value = contact.firstName,
                      onChange = value => onChangeContact(index, contact.changeFirstName(value)),
                      placeholder = "First name",
                      icon = Some(Icon.Glyph.UserSingle),
                      status = if (contact.firstNameResult.valid) TextBox.Status.None else TextBox.Status.Invalid
                    )(),
                    renderResult(contact.firstNameResult).unless(contact.isValid)
                  ),
                  <.div(
                    ComponentUtils.testId(AddOrEditGroupModal, "LastName"),
                    ^.width := "200px",
                    tw.mr16,
                    TextBox(
                      value = contact.lastName,
                      onChange = value => onChangeContact(index, contact.changeLastName(value)),
                      placeholder = "Last name",
                      status = if (contact.lastNameResult.valid) TextBox.Status.None else TextBox.Status.Invalid
                    )(),
                    renderResult(contact.lastNameResult).unless(contact.isValid)
                  ),
                  <.div(
                    ComponentUtils.testId(AddOrEditGroupModal, "Email"),
                    tw.mr8.flexFill,
                    TextBox(
                      value = contact.email,
                      onChange = value => onChangeContact(index, contact.changeEmail(value)),
                      placeholder = "Email",
                      status = if (contact.emailResult.valid) TextBox.Status.None else TextBox.Status.Invalid
                    )(),
                    renderResult(contact.emailResult).unless(contact.isValid)
                  ),
                  Button(
                    style = Button.Style.Minimal(icon = Some(Icon.Glyph.Cross)),
                    title = "Remove",
                    onClick = onRemove(index)
                  )()
                )
              }
            )
          )
        }
      )()
    }

    private def renderResult(result: Result): VdomNode = {
      <.div(
        ^.minHeight := "16px",
        tw.mt4,
        if (result.valid) {
          EmptyVdom
        } else {
          <.div(
            tw.text11.leading16.textDanger5,
            result.errors.headOption.fold("")(_.toString)
          )
        }
      )
    }

    private def renderButtons(props: Props, state: State): VdomNode = {
      <.div(
        tw.flex.itemsCenter,
        <.div(tw.mlAuto.mr8, Button(onClick = props.onClose)("Cancel")),
        Button(
          style = Button.Style.Full(color = Button.Color.Primary, isBusy = state.busy),
          onClick = validate(
            props,
            state,
            props.groupOpt.fold[Callback](onAddGroup(props, state)) { group =>
              onEditGroup(
                props,
                state,
                group.id
              )
            }
          ),
          isDisabled = StringUtils.isEmptyOrWhitespace(state.name)
        )(if (props.isCreateNew) "Create" else "Save")
      )
    }

    private def onAddGroup(props: Props, state: State): Callback = {
      scope.modState(
        _.copy(busy = true),
        ZIOUtils.toReactCallback(
          FundSubEndpointClient
            .createContactGroup(
              CreateContactGroupParams(
                props.fundSubId,
                state.name.trim,
                state.customId.trim,
                None,
                state.contacts.map(_.toContactData)
              )
            )
            .map(
              _.fold(
                _ =>
                  scope
                    .modState(
                      _.copy(busy = false),
                      Toast.errorCallback(s"Unable to add ${Terms.InvesteeEntityLabelLowerCase}, try again")
                    ),
                _ => Toast.successCallback(s"${Terms.InvesteeEntityLabel} added") >> props.reFetch >> props.onClose
              )
            )
        )
      )
    }

    private def onEditGroup(props: Props, state: State, groupId: FundSubContactGroupId): Callback = {
      val contactsToAdd =
        state.contacts.filterNot(contact => props.contacts.map(_.contactInfo.email).contains(contact.email))
      val contactsToRemove = props.contacts
        .filterNot(contact => state.contacts.map(_.email).contains(contact.contactInfo.email))
        .map(_.contactId)
      scope.modState(
        _.copy(busy = true),
        ZIOUtils.toReactCallback(
          FundSubEndpointClient
            .editContactGroup(
              EditContactGroupParams(
                groupId,
                state.name.trim,
                state.customId.trim,
                contactsToAdd.map(_.toContactData),
                contactsToRemove
              )
            )
            .map(
              _.fold(
                _ =>
                  scope
                    .modState(
                      _.copy(busy = false),
                      Toast.errorCallback(s"Unable to edit ${Terms.InvesteeEntityLabelLowerCase}, try again")
                    ),
                _ => Toast.successCallback(s"${Terms.InvesteeEntityLabel} edited") >> props.reFetch >> props.onClose
              )
            )
        )
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps { props =>
      props.groupOpt.fold[State](State()) { group =>
        State(
          name = group.name,
          customId = group.customId,
          contacts = props.contacts.map(contact =>
            ContactToAdd(
              contact.contactInfo.firstName,
              contact.contactInfo.lastName,
              contact.contactInfo.email
            )
          )
        )
      }
    }
    .renderBackend[Backend]
    .build

}
