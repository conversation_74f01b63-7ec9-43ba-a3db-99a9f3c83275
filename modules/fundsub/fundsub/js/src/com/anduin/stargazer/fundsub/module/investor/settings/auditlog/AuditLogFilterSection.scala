// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.auditlog

import anduin.copy.Bundle.I18N
import java.time.format.DateTimeFormatter
import java.time.{Instant, Period}
import scala.util.Try

import design.anduin.components.avatar.InitialAvatar
import design.anduin.components.badge.Badge
import design.anduin.components.badge.react.BadgeR
import design.anduin.components.button.Button
import design.anduin.components.dropdown.Dropdown
import design.anduin.components.icon.Icon
import design.anduin.components.modal.Modal
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.PortalPosition
import design.anduin.components.progress.react.CircleIndicatorR
import design.anduin.components.toast.Toast
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import io.circe.{Codec, Decoder, Encoder}
import japgolly.scalajs.react.{BackendScope, Callback, ScalaComponent}

import anduin.fundsub.auditlog.FundSubAuditLogUtils.getDescriptionForFundSubAuditLogEventType
import anduin.fundsub.auditlog.{AuditLogActorType, AuditLogEventType, AuditLogFilter}
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.model.common.user.{UserId, UserInfo}
import anduin.user.UserInitialAvatar
import anduin.utils.DateTimeUtils
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.investor.settings.auditlog.Filter.{
  ActorFilterItem,
  InvestmentEntityFilterItem,
  TextFilterItem
}
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.fundsub.auditlog.AuditLogFilter.*
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils

private[settings] final case class AuditLogFilterSection(
  fundSubId: FundSubId,
  copyConfig: I18N,
  onApplyFilter: ((Seq[AuditLogFilter], Option[TimestampFilter])) => Callback
) {
  private val filterActionOptions = Filter.actionOptions(copyConfig)
  def apply(): VdomElement = AuditLogFilterSection.component(this)
}

private[settings] object AuditLogFilterSection {
  private type Props = AuditLogFilterSection

  private def customDropdown[A](
    using SelfEqual[A]
  ) = (new Dropdown[A])()

  private def formatDropdown[A](dropdown: Dropdown[A]#Props, isFetchingData: Boolean) = {
    val dropdownMenu = dropdown.menu
    val dropdownButton = dropdown.button
    dropdown.copy(
      menu = dropdownMenu.copy(
        renderItemBody = Option(item =>
          <.div(
            ^.width := "186px",
            tw.ml16,
            dropdownMenu.renderItemBody.map(_(item)).getOrElse("")
          )
        )
      ),
      button = dropdownButton.copy(
        isDisabled = isFetchingData,
        renderValue = Option { item =>
          <.div(
            ^.width := "240px",
            tw.hPx32.flex.itemsCenter,
            if (isFetchingData) {
              CircleIndicatorR()()
            } else {
              dropdownButton.renderValue.map(_(item)).getOrElse(dropdown.valueToString(item))
            }
          )
        }
      )
    )
  }

  def timeRangeToText(from: Instant, to: Instant): String = {
    val fromTimeText = Try {
      DateTimeUtils.formatInstant(from, DateTimeFormatter.ofPattern("MMM d, yyyy"))(
        using DateTimeUtils.defaultTimezone
      )
    }.getOrElse("")
    val toTimeText = Try {
      DateTimeUtils.formatInstant(to, DateTimeFormatter.ofPattern("MMM d, yyyy"))(
        using DateTimeUtils.defaultTimezone
      )
    }.getOrElse("")
    s"$fromTimeText - $toTimeText"
  }

  private final case class State(
    dateRangeFilter: TextFilterItem[TimestampFilter] = Filter.defaultRangeFilterOption,
    actionFilter: TextFilterItem[EventTypeFilter],
    actorFirstLayerFilter: TextFilterItem[ActorTypeFilter] = Filter.actorFirstLayerOptions.head,
    actorFundManagementFilterOptions: Seq[ActorFilterItem] = Seq.empty,
    isFetchingActorFundManagementFilterOptions: Boolean = false,
    actorFundManagementFilter: ActorFilterItem = ActorFilterItem(),
    actorInvestmentEntityFilterOptions: Seq[InvestmentEntityFilterItem] = Seq.empty,
    isFetchingActorInvestmentEntityFilterOptions: Boolean = false,
    actorInvestmentEntityFilter: InvestmentEntityFilterItem = InvestmentEntityFilterItem(),
    actorInvestorFilterOptions: Seq[ActorFilterItem] = Seq.empty,
    isFetchingActorInvestorFilterOptions: Boolean = false,
    actorInvestorFilter: ActorFilterItem = ActorFilterItem(),
    isOpenCustomDateRangeModal: Boolean = false
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    private def renderFilter[A](label: String, dropdown: Dropdown[A]#Props, isFetching: Boolean = false) = {
      <.div(
        tw.flex.spaceX16.itemsCenter,
        <.div(
          ^.width := "70px",
          tw.textGray7.fontSemiBold.textRight,
          label
        ),
        <.div(
          tw.flex1,
          formatDropdown[A](dropdown, isFetching)()
        )
      )
    }

    private def renderUser(userInfo: UserInfo) = {
      <.div(
        tw.flex.itemsCenter,
        UserInitialAvatar(
          userInfo = userInfo,
          size = InitialAvatar.Size.Px24
        )(),
        <.div(
          tw.ml8.flexFill.truncate,
          <.div(tw.fontSemiBold, userInfo.fullNameString)
        )
      )
    }

    private def summarizeFilter(state: State): (Seq[AuditLogFilter], Option[TimestampFilter]) = {
      val generalFilters =
        Seq(
          state.actionFilter.filter,
          state.actorFirstLayerFilter.filter,
          state.actorFundManagementFilter.filter,
          state.actorInvestmentEntityFilter.filter,
          state.actorInvestorFilter.filter
        ).flatten
      val timestampFilter = state.dateRangeFilter.filter

      (generalFilters, timestampFilter)
    }

    private def renderFilterDescription(props: Props, state: State) = {
      val dateRangeFilterDescription = state.dateRangeFilter.filter.map { filter =>
        if (filter.isCustom) {
          timeRangeToText(filter.from, filter.to)
        } else {
          state.dateRangeFilter.textLabel
        }
      }
      val actorFilterDescription = Seq(
        Option.when(state.actorFirstLayerFilter != Filter.actorFirstLayerOptions.head) {
          state.actorFirstLayerFilter.textLabel
        },
        Option.when(
          state.actorFundManagementFilterOptions.nonEmpty && state.actorFundManagementFilter != state.actorFundManagementFilterOptions.head
        ) {
          state.actorFundManagementFilter.info.map(_._2.fullNameString).getOrElse("")
        },
        Option.when(
          state.actorInvestmentEntityFilterOptions.nonEmpty && state.actorInvestmentEntityFilter != state.actorInvestmentEntityFilterOptions.head
        ) {
          state.actorInvestmentEntityFilter.info.map(_._2).getOrElse("")
        },
        Option.when(
          state.actorInvestorFilterOptions.nonEmpty && state.actorInvestorFilter != state.actorInvestorFilterOptions.head
        ) {
          state.actorInvestorFilter.info.map(_._2.fullNameString).getOrElse("")
        }
      ).flatten.mkString(" > ")
      val actionFilterDescription = Option.when(state.actionFilter != props.filterActionOptions.head) {
        state.actionFilter.textLabel
      }

      val filterDescription = Seq(
        dateRangeFilterDescription.map(description => s"Date range ($description)"),
        actionFilterDescription.map(description => s"Action ($description)"),
        Option.when(actorFilterDescription.nonEmpty) { s"Actor ($actorFilterDescription)" }
      ).flatten.mkString(", ")

      <.div(
        tw.flex.spaceX8,
        <.div(
          <.span(tw.fontSemiBold.textGray7, "Filtered by "),
          filterDescription
        ),
        TagMod.unless(
          state.dateRangeFilter == Filter.defaultRangeFilterOption &&
            state.actionFilter == props.filterActionOptions.head &&
            state.actorFirstLayerFilter == Filter.actorFirstLayerOptions.head
        ) {
          Button(
            style = Button.Style.Text(),
            onClick = onClearFilter(
              props,
              shouldFetchEvent = true
            )
          )("Clear all")
        }
      )
    }

    private def onClearFilter(props: Props, shouldFetchEvent: Boolean = false): Callback = {
      scope.modState(
        _.copy(
          dateRangeFilter = Filter.defaultRangeFilterOption,
          actionFilter = props.filterActionOptions.head,
          actorFirstLayerFilter = Filter.actorFirstLayerOptions.head,
          actorInvestmentEntityFilter = InvestmentEntityFilterItem(),
          actorFundManagementFilter = ActorFilterItem(),
          actorInvestorFilter = ActorFilterItem()
        ),
        if (shouldFetchEvent) {
          for {
            state <- scope.state
            filter <- props.onApplyFilter(summarizeFilter(state))
          } yield filter
        } else { Callback.empty }
      )
    }

    private def renderCustomDateRangeModal(state: State) = {
      Modal(
        title = "Customize date range",
        renderContent = _ =>
          CustomDateRangeFilterModal(
            onApply = (from, to) => {
              scope.modState(
                _.copy(dateRangeFilter =
                  TextFilterItem(
                    "Custom Range",
                    Option(
                      TimestampFilter(
                        from,
                        to,
                        isCustom = true
                      )
                    )
                  )
                )
              )
            },
            onClose = scope.modState(_.copy(isOpenCustomDateRangeModal = false)),
            from = state.dateRangeFilter.filter.flatMap(filter =>
              if (filter.isCustom) { Option(filter.from) }
              else { None }
            ),
            to = state.dateRangeFilter.filter.flatMap(filter =>
              if (filter.isCustom) { Option(filter.to) }
              else { None }
            )
          )(),
        isOpened = Option(state.isOpenCustomDateRangeModal),
        afterUserClose = scope.modState(_.copy(isOpenCustomDateRangeModal = false))
      )()
    }

    def render(props: Props, state: State): VdomElement = {
      val filtersCount = Seq(
        state.dateRangeFilter != Filter.defaultRangeFilterOption,
        state.actionFilter != props.filterActionOptions.head,
        state.actorFirstLayerFilter != Filter.actorFirstLayerOptions.head
      ).count(identity)
      <.div(
        ComponentUtils.testId(AuditLogFilterSection),
        tw.flexCol.spaceY12,
        PopoverR(
          position = PortalPosition.BottomRight,
          renderTarget = (open, _) =>
            Button(
              style = Button.Style.Full(icon = Some(Icon.Glyph.Filter)),
              onClick = open
            )(
              <.div(
                tw.flex.itemsCenter,
                "Filter",
                TagMod.when(filtersCount > 0) {
                  <.div(
                    tw.ml4,
                    BadgeR(
                      color = Badge.Color.Primary,
                      theme = Badge.Theme.Bold,
                      count = Option(filtersCount)
                    )()
                  )
                }
              )
            ),
          renderContent = close => {
            def renderActorItem(item: ActorFilterItem) = item.info.fold[VdomNode]("All users") { case (_, userInfo) =>
              renderUser(userInfo)
            }
            def renderInvestmentEntityItem(item: InvestmentEntityFilterItem) = {
              renderTruncateText(item.info.fold[String]("All investment entities")(_._2))
            }
            def renderTruncateText(text: String) = {
              <.div(
                tw.truncate.wPc100,
                text
              )
            }
            <.div(
              tw.px16.pt12,
              <.div(
                tw.spaceY16,
                renderCustomDateRangeModal(state),
                <.div(
                  tw.spaceY8,
                  renderFilter(
                    "Date range",
                    customDropdown[TextFilterItem[TimestampFilter]](
                      menu = Dropdown.Menu(renderItemBody = Option(_.value.textLabel)),
                      value = Some(state.dateRangeFilter),
                      valueToString = _.textLabel,
                      items = Filter.dateRangeOptions.map(Dropdown.Item(_)),
                      onChange = value =>
                        if (value.filter.nonEmpty) {
                          scope.modState(
                            _.copy(dateRangeFilter = value)
                          )
                        } else {
                          scope.modState(_.copy(isOpenCustomDateRangeModal = true))
                        }
                    )
                  ),
                  state.dateRangeFilter.filter.fold(TagMod.empty) { dateRangeFilter =>
                    TagMod.when(dateRangeFilter.isCustom) {
                      <.div(
                        ^.marginLeft := "86px",
                        tw.flex.spaceX8,
                        <.div(
                          timeRangeToText(dateRangeFilter.from, dateRangeFilter.to)
                        ),
                        Button(
                          style = Button.Style.Text(),
                          onClick = scope.modState(_.copy(isOpenCustomDateRangeModal = true))
                        )("Edit")
                      )
                    }
                  }
                ),
                renderFilter(
                  "Action",
                  customDropdown[TextFilterItem[EventTypeFilter]](
                    menu = Dropdown.Menu(renderItemBody = Option(_.value.textLabel)),
                    value = Some(state.actionFilter),
                    valueToString = _.textLabel,
                    items = props.filterActionOptions.map(Dropdown.Item(_)),
                    button = Dropdown.Button(renderValue = Option(item => renderTruncateText(item.textLabel))),
                    onChange = value =>
                      scope.modState(
                        _.copy(actionFilter = value)
                      )
                  )
                ),
                renderFilter(
                  "Actor",
                  customDropdown[TextFilterItem[ActorTypeFilter]](
                    menu = Dropdown.Menu(renderItemBody = Option(_.value.textLabel)),
                    value = Some(state.actorFirstLayerFilter),
                    valueToString = _.textLabel,
                    items = Filter.actorFirstLayerOptions.map(Dropdown.Item(_)),
                    onChange = value => {
                      val fetchDropdownData = value.filter match {
                        case Some(ActorTypeFilter(t)) =>
                          t match {
                            case AuditLogActorType.FundSide     => fetchFundMemberForFilter(props.fundSubId)
                            case AuditLogActorType.InvestorSide => fetchInvestmentEntityForFilter(props.fundSubId)
                            case _                              => Callback.empty
                          }
                        case _ => Callback.empty
                      }
                      scope.modState(
                        _.copy(actorFirstLayerFilter = value),
                        fetchDropdownData
                      )
                    }
                  )
                ),
                state.actorFirstLayerFilter.filter.map { case ActorTypeFilter(actorType) =>
                  actorType match {
                    case AuditLogActorType.FundSide =>
                      renderFilter(
                        "",
                        customDropdown[ActorFilterItem](
                          menu = Dropdown.Menu(renderItemBody = Option(item => renderActorItem(item.value))),
                          value = Some(state.actorFundManagementFilter),
                          valueToString = _.info.map(_._2.fullNameString).getOrElse(""),
                          button = Dropdown.Button(renderValue = Option(item => renderActorItem(item))),
                          items = state.actorFundManagementFilterOptions.map(Dropdown.Item(_)),
                          onChange = value =>
                            scope.modState(
                              _.copy(actorFundManagementFilter = value)
                            )
                        ),
                        isFetching = state.isFetchingActorFundManagementFilterOptions
                      )
                    case AuditLogActorType.InvestorSide =>
                      renderFilter(
                        "",
                        customDropdown[InvestmentEntityFilterItem](
                          menu = Dropdown.Menu(renderItemBody = Option(item => renderInvestmentEntityItem(item.value))),
                          value = Some(state.actorInvestmentEntityFilter),
                          valueToString = item => item.info.fold[String]("All investment entities")(_._2),
                          button = Dropdown.Button(renderValue = Option(value => renderInvestmentEntityItem(value))),
                          items = state.actorInvestmentEntityFilterOptions.map(Dropdown.Item(_)),
                          onChange = value =>
                            scope.modState(
                              _.copy(actorInvestmentEntityFilter = value),
                              value.info.map(info => fetchInvestorForFilter(info._1)).getOrElse(Callback.empty)
                            )
                        ),
                        isFetching = state.isFetchingActorInvestmentEntityFilterOptions
                      )
                    case _ => <.div()
                  }
                },
                Option
                  .when(state.actorInvestmentEntityFilter.filter.nonEmpty) {
                    renderFilter(
                      "",
                      customDropdown[ActorFilterItem](
                        menu = Dropdown.Menu(renderItemBody = Option(item => renderActorItem(item.value))),
                        value = Some(state.actorInvestorFilter),
                        valueToString = _.info.map(_._2.fullNameString).getOrElse(""),
                        button = Dropdown.Button(renderValue = Option(value => renderActorItem(value))),
                        items = state.actorInvestorFilterOptions.map(Dropdown.Item(_)),
                        onChange = value =>
                          scope.modState(
                            _.copy(actorInvestorFilter = value)
                          )
                      ),
                      isFetching = state.isFetchingActorInvestorFilterOptions
                    )
                  }
                  .getOrElse(<.div())
              ),
              <.div(
                ^.height := "56px",
                tw.flex.justifyBetween.itemsCenter.borderTop.borderGray3.mt20.pt8,
                Button(
                  style = Button.Style.Text(),
                  onClick = onClearFilter(props)
                )("Clear all"),
                Button(
                  style = Button.Style.Full(color = Button.Color.Primary),
                  onClick = props.onApplyFilter(summarizeFilter(state)) >> close
                )("Apply")
              )
            )
          }
        )(),
        renderFilterDescription(props, state)
      )
    }

    private def fetchFundMemberForFilter(fundSubId: FundSubId): Callback = {
      scope.modState(_.copy(isFetchingActorFundManagementFilterOptions = true)) >> ZIOUtils.toReactCallback {
        FundSubEndpointClient
          .getFundMemberForFilter(fundSubId)
          .map(
            _.fold(
              _ => Toast.errorCallback("Failed to get fund admins for filter"),
              resp =>
                scope.modState(
                  _.copy(
                    actorFundManagementFilterOptions = ActorFilterItem() +: resp.map { fundMember =>
                      ActorFilterItem(
                        info = Option(fundMember),
                        filter = Option(ActorFilter(fundMember._1))
                      )
                    },
                    actorInvestorFilter = ActorFilterItem(),
                    actorFundManagementFilter = ActorFilterItem(),
                    actorInvestmentEntityFilter = InvestmentEntityFilterItem()
                  ),
                  scope.modState(_.copy(isFetchingActorFundManagementFilterOptions = false))
                )
            )
          )
      }
    }

    private def fetchInvestmentEntityForFilter(fundSubId: FundSubId): Callback = {
      scope.modState(_.copy(isFetchingActorInvestmentEntityFilterOptions = true)) >> ZIOUtils.toReactCallback {
        FundSubEndpointClient
          .getInvestmentEntityForFilter(fundSubId)
          .map(
            _.fold(
              _ => Toast.errorCallback("Failed to get investment entities for filter"),
              resp =>
                scope.modState(
                  _.copy(
                    actorInvestmentEntityFilterOptions = InvestmentEntityFilterItem() +: resp.map { fundMember =>
                      InvestmentEntityFilterItem(
                        info = Option(fundMember),
                        filter = Option(OrderFilter(fundMember._1))
                      )
                    },
                    actorInvestorFilter = ActorFilterItem(),
                    actorFundManagementFilter = ActorFilterItem(),
                    actorInvestmentEntityFilter = InvestmentEntityFilterItem()
                  ),
                  scope.modState(_.copy(isFetchingActorInvestmentEntityFilterOptions = false))
                )
            )
          )
      }
    }

    private def fetchInvestorForFilter(fundSubLpId: FundSubLpId): Callback = {
      scope.modState(_.copy(isFetchingActorInvestorFilterOptions = true)) >> ZIOUtils.toReactCallback {
        FundSubEndpointClient
          .getInvestorForFilter(fundSubLpId)
          .map(
            _.fold(
              _ => Toast.errorCallback("Failed to get investors for filter"),
              resp => {
                scope.modState(
                  _.copy(
                    actorInvestorFilterOptions = ActorFilterItem() +: resp.map { investor =>
                      ActorFilterItem(
                        info = Option(investor),
                        filter = Option(ActorFilter(investor._1))
                      )
                    },
                    actorInvestorFilter = ActorFilterItem()
                  ),
                  scope.modState(_.copy(isFetchingActorInvestorFilterOptions = false))
                )
              }
            )
          )
      }
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps { props =>
      State(actionFilter = props.filterActionOptions.head)
    }
    .renderBackend[Backend]
    .build

}

private[settings] object Filter {

  val defaultTimestampFilterOption: TimestampFilter =
    TimestampFilter(Instant.now().minus(Period.ofDays(90)), Instant.MAX)

  val dateRangeOptions: Seq[TextFilterItem[TimestampFilter]] = Seq(
    TextFilterItem("Last 24 hours", Option(TimestampFilter(Instant.now().minus(Period.ofDays(1)), Instant.MAX))),
    TextFilterItem("Last 7 days", Option(TimestampFilter(Instant.now().minus(Period.ofDays(7)), Instant.MAX))),
    TextFilterItem("Last 30 days", Option(TimestampFilter(Instant.now().minus(Period.ofDays(30)), Instant.MAX))),
    TextFilterItem("Last 60 days", Option(TimestampFilter(Instant.now().minus(Period.ofDays(60)), Instant.MAX))),
    TextFilterItem("Last 90 days", Option(defaultTimestampFilterOption)),
    TextFilterItem("Custom range", None)
  )

  val defaultRangeFilterOption: TextFilterItem[TimestampFilter] = dateRangeOptions(4)

  def actionOptions(copyConfig: I18N): Seq[TextFilterItem[EventTypeFilter]] =
    TextFilterItem[EventTypeFilter]("All actions", None) +:
      AuditLogEventType.values.filterNot(_ == AuditLogEventType.NULL).map { eventType =>
        TextFilterItem(
          getDescriptionForFundSubAuditLogEventType(eventType, copyConfig, FundSubCopyUtils.getFlowTerm),
          Option(EventTypeFilter(eventType))
        )
      }

  val actorFirstLayerOptions: Seq[TextFilterItem[ActorTypeFilter]] = Seq(
    TextFilterItem[ActorTypeFilter]("All actors", None),
    TextFilterItem("Fund management", Option(ActorTypeFilter(AuditLogActorType.FundSide))),
    TextFilterItem("Investors", Option(ActorTypeFilter(AuditLogActorType.InvestorSide))),
    TextFilterItem("Advisor", Option(ActorTypeFilter(AuditLogActorType.Ria))),
    TextFilterItem("System", Option(ActorTypeFilter(AuditLogActorType.System)))
  )

  final case class TextFilterItem[A <: RootAuditLogFilter](
    textLabel: String = "",
    filter: Option[A] = None
  ) derives CanEqual

  object TextFilterItem {
    given [A <: RootAuditLogFilter: {Encoder, Decoder}]: Codec.AsObject[TextFilterItem[A]] = deriveCodecWithDefaults
  }

  final case class ActorFilterItem(
    info: Option[(UserId, UserInfo)] = None,
    filter: Option[ActorFilter] = None
  ) derives CanEqual

  final case class InvestmentEntityFilterItem(
    info: Option[(FundSubLpId, String)] = None,
    filter: Option[OrderFilter] = None
  ) derives CanEqual

}
