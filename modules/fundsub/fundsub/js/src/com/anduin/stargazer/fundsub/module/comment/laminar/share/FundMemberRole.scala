// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.share

import com.raquo.laminar.api.L.*
import design.anduin.style.tw.*

import anduin.fundsub.endpoint.lp.{FundAdminRole, GetFundMemberRoleResponse}

private[laminar] final case class FundMemberRole(memberRoleSignal: Signal[GetFundMemberRoleResponse]) {

  private def getDisplayText(response: GetFundMemberRoleResponse) = {
    val fundAdminRoleOpt = response.fundMemberRoles
      .find {
        case _: FundAdminRole => true
        case _                => false
      }
      .map(_.displayName)

    fundAdminRoleOpt
      .orElse(
        response.fundMemberRoles.headOption.map(_.displayName)
      )
      .getOrElse("")
  }

  def apply(): Node = {
    div(
      tw.textGray7.text11.leading16,
      child.text <-- memberRoleSignal.map(getDisplayText)
    )
  }

}
