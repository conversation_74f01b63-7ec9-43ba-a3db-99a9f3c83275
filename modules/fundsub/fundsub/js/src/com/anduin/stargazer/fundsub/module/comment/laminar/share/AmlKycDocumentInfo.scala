// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.share

import com.raquo.laminar.api.L.*
import design.anduin.components.util.ComponentUtils
import org.scalajs.dom
import anduin.id.entity.EntityId
import anduin.id.fundsub.FundSubLpId
import anduin.protobuf.fundsub.comment.AmlKycDocData
import com.anduin.stargazer.client.modules.routing.RoutingUtils.Router
import stargazer.component.routing.laminar.WithReactRouterL
import stargazer.model.routing.DynamicAuthPage.FundSubManageLpDocPage

private[laminar] case class AmlKycDocumentInfo(
  amlKycData: AmlKycDocData,
  fundEntityId: EntityId,
  fundSubLpId: FundSubLpId,
  isPublic: Boolean,
  isHidden: Boolean
) {

  private def accessTargetPage(router: Router) = {
    val page = FundSubManageLpDocPage(
      entityId = fundEntityId,
      lpId = fundSubLpId,
      params = Map(
        FundSubManageLpDocPage.prevPageKey -> FundSubManageLpDocPage.PrevPage.FundAdminDashboard.value,
        FundSubManageLpDocPage.targetDoctype -> amlKycData.doctype,
        FundSubManageLpDocPage.targetThreadIsPublicComment -> isPublic.toString
      )
    )
    dom.window.open(router.urlFor(page).value, "_blank")
  }

  def apply(): HtmlElement = {
    WithReactRouterL { router =>
      div(
        ComponentUtils.testIdL("DocumentCommentSection"),
        CommentAnchorPointDescriptionL(
          topicDataSignal = Val(amlKycData),
          isHiddenSignal = Val(isHidden),
          onJumpToTarget = Observer { _ =>
            if (!isHidden) {
              accessTargetPage(router)
              ()
            }
          },
          moveToAnchorPointActionType = CommentAnchorPointDescriptionL.MoveToAnchorPointType.OpenInNewTab
        )()
      )
    }
  }

}
