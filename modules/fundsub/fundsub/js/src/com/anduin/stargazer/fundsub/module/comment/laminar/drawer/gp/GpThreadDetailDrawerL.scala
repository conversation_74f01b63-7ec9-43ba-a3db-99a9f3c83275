// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.drawer.gp

import java.time.Instant
import java.util.UUID

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.divider.Divider
import design.anduin.components.divider.laminar.DividerL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.tab.Tab
import design.anduin.components.tab.laminar.TabL
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import zio.ZIO

import anduin.fundsub.comment.utils.CommentUtils
import anduin.fundsub.endpoint.formcomment.{AddCommentReplyParams, CreateCommentParams}
import anduin.id.fundsub.FundSubLpId
import anduin.model.common.user.{UserId, UserInfo}
import anduin.protobuf.actionlogger.formcomment.FormCommentViewSource
import anduin.protobuf.fundsub.comment.{AmlKycDocData, FormQuestionData, TopicData}
import anduin.service.ServiceResponseCode
import anduin.stargazer.service.formcomment.FormCommentCommons.{
  CommentThread,
  CommentVisibilityType,
  MentionsData,
  PendingThreadDefaultId
}
import anduin.user.CurrentUserInfoProvider
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.comment.laminar.*
import com.anduin.stargazer.fundsub.module.comment.laminar.assignment.CommentAssignmentDropdownWrapperL
import com.anduin.stargazer.fundsub.module.comment.laminar.drawer.CommentDrawerUIModels
import com.anduin.stargazer.fundsub.module.comment.laminar.share.gp.{GpAddCommentPanelL, GpNonEmptyThreadL}
import com.anduin.stargazer.fundsub.module.comment.laminar.share.{CommentsListL, NewCommentBoxL, ThreadDetailSkeletonL}
import com.anduin.stargazer.fundsub.module.comment.models.CommentView.SwitchThreadVisibilityParams
import com.anduin.stargazer.fundsub.module.comment.models.{CommentNotificationSetting, CommentsData}

private[gp] case class GpThreadDetailDrawerL(
  lpIdSignal: Signal[FundSubLpId],
  topicDataSignal: Signal[TopicData],
  isPublicSignal: Signal[Boolean],
  commentsDataSignal: Signal[CommentsData],
  commentNotificationSettingSignal: Signal[Option[CommentNotificationSetting]],
  isCommentMentioningEnabledSignal: Signal[Boolean],
  isCommentAssignmentEnabledSignal: Signal[Boolean],
  inactiveDaysThresholdSignal: Signal[Option[Int]],
  initialComment: String,
  onSwitchAllComments: Observer[Unit],
  onSwitchToView: Observer[CommentDrawerUIModels.DrawerView],
  onClose: Observer[Unit]
) {

  private val isLoadingSignal = commentsDataSignal.map(_.isLoading)

  private val switchViewEventBus = new EventBus[SwitchThreadVisibilityParams]

  private val createNewCommentThreadEventBus = new EventBus[NewCommentBoxL.AddCommentData]

  private val localCommentsVar: Var[Seq[CommentsListL.ThreadActivityItem.Comment]] = Var(Seq.empty)

  private val pendingCommentThreadVar: Var[Option[CommentThread]] = Var(None)

  private val fetchedThreadOptSignal = commentsDataSignal
    .combineWith(
      topicDataSignal,
      isPublicSignal
    )
    .map { case (commentData, topicData, isPublic) =>
      commentData.threads.find { thread =>
        thread.isPublic == isPublic && CommentUtils.sameTopicData(thread.topicData, topicData)
      }
    }
    .distinct

  private val threadOptSignal = fetchedThreadOptSignal
    .combineWith(pendingCommentThreadVar.signal)
    .map { case (fetchedThreadOpt, pendingThreadOpt) =>
      fetchedThreadOpt.fold(pendingThreadOpt) { thread =>
        Some(thread)
      }
    }

  private def resetPendingDataWhenNewThreadDataIsFetched(
    fetchedThreadOpt: Option[CommentThread],
    pendingThreadOpt: Option[CommentThread],
    currentLocalComments: Seq[CommentsListL.ThreadActivityItem.Comment]
  ): Unit = {
    if (currentLocalComments.nonEmpty && (fetchedThreadOpt.isEmpty && pendingThreadOpt.isEmpty)) {
      // remove all local comments when the thread is deleted
      localCommentsVar.set(Seq.empty)
    }
    if (fetchedThreadOpt.nonEmpty) pendingCommentThreadVar.set(None)
  }

  private val resetCommentBoxEventBus = new EventBus[Unit]

  def apply(): HtmlElement = {
    div(
      ComponentUtils.testIdL(GpThreadDetailDrawerL),
      tw.hPc100.flex.flexCol,
      renderHeaderRows,
      renderInternalAndShareCommentTab,
      // Event handlers
      fetchedThreadOptSignal
        .withCurrentValueOf(pendingCommentThreadVar.signal, localCommentsVar.signal)
        .map { case (fetchedThreadOpt, pendingThreadOpt, currentLocalComments) =>
          resetPendingDataWhenNewThreadDataIsFetched(
            fetchedThreadOpt = fetchedThreadOpt,
            pendingThreadOpt = pendingThreadOpt,
            currentLocalComments = currentLocalComments
          )
        } --> Observer.empty,
      switchViewEventBus.events.withCurrentValueOf(topicDataSignal).map {
        case (switchThreadVisibilityParams, topicData) =>
          handleSwitchMode(
            currentTopicData = topicData,
            commentVisibilityType = switchThreadVisibilityParams.newVisibilityType,
            initialComment = switchThreadVisibilityParams.initialComment
          )
      } --> Observer.empty,
      createNewCommentThreadEventBus.events
        .withCurrentValueOf(
          topicDataSignal,
          isPublicSignal,
          lpIdSignal,
          CurrentUserInfoProvider.getCurrentUserInfoSignal
        )
        .map { case (addCommentData, topicData, isPublic, lpId, currentUserIdAndInfoOpt) =>
          handleCreateCommentThread(
            lpId,
            topicData,
            isPublic,
            addCommentData,
            currentUserIdAndInfoOpt
          )
        } --> Observer.empty
    )
  }

  private def renderInternalAndShareCommentTab = {
    div(
      tw.flexFill.mt4,
      TabL(
        activeTabSignal = isPublicSignal.map { isPublic =>
          Some(if (isPublic) 1 else 0)
        },
        panels = Seq(
          Tab.Panel(
            title = div(
              ComponentUtils.testIdL("InternalTab"),
              height.px := 36,
              tw.flex.itemsCenter,
              IconL(name = Val(Icon.Glyph.CommentLineLock))(),
              span(tw.ml8, "Internal")
            ),
            renderContent = _ => renderTabContent
          ),
          Tab.Panel(
            title = div(
              ComponentUtils.testIdL("SharedTab"),
              height.px := 36,
              tw.flex.itemsCenter,
              IconL(name = Val(Icon.Glyph.CommentLine))(),
              span(tw.ml8, "Shared")
            ),
            renderContent = _ => renderTabContent
          )
        ),
        renderHeader = Some { renderHeaderProps =>
          div(
            div(
              tw.mx16.flex.justifyBetween.itemsCenter,
              renderHeaderProps.renderTitles,
              child <-- threadOptSignal
                .splitOption(
                  project = (_, threadSignal) => renderResolveOrReopenButtonIfNeeded(threadSignal),
                  ifEmpty = Val(emptyNode)
                )
                .flattenSwitch
            ),
            renderHeaderProps.renderHeaderBorder
          )
        },
        onClick = Observer[Int] { index =>
          switchViewEventBus.emit(
            SwitchThreadVisibilityParams(
              newVisibilityType =
                if (index == 0) CommentVisibilityType.InternalComment
                else CommentVisibilityType.PublicComment,
              initialComment = ""
            )
          )
        },
        style = Tab.Style.Minimal(isFullHeight = true)
      )()
    )

  }

  private def renderTabContent = {
    div(
      tw.hPc100,
      child <-- isLoadingSignal.splitBoolean(
        whenTrue = _ => ThreadDetailSkeletonL()(),
        whenFalse = _ =>
          div(
            tw.hPc100,
            child <-- threadOptSignal.splitOption(
              (_, threadSignal) => {
                GpNonEmptyThreadL(
                  commentNotificationSettingSignal = commentNotificationSettingSignal,
                  threadSignal = threadSignal,
                  canMakePublicCommentSignal = commentsDataSignal.map(_.canMakePublicComment).distinct,
                  initialComment = initialComment,
                  isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
                  inactiveDaysThresholdSignal = inactiveDaysThresholdSignal,
                  onSwitchAllComments = onSwitchAllComments,
                  onSwitchMode = switchViewEventBus.writer,
                  localCommentsSignal = localCommentsVar.signal,
                  onUpdateLocalComments = localCommentsVar.writer,
                  handleAddCommentReply = handleAddCommentReply,
                  resetCommentBoxEventStream = resetCommentBoxEventBus.events,
                  isDrawerViewSignal = Val(true)
                )()
              },
              ifEmpty = GpAddCommentPanelL(
                commentNotificationSettingSignal = commentNotificationSettingSignal,
                lpIdSignal = lpIdSignal,
                isPublicThreadSignal = isPublicSignal,
                isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
                canMakePublicCommentSignal = commentsDataSignal.map(_.canMakePublicComment).distinct,
                initialComment = initialComment,
                onAddComment = createNewCommentThreadEventBus.writer
              )()
            )
          )
      )
    )

  }

  private def renderHeaderRows = {
    div(
      tw.flex.itemsCenter.px16.pt16,
      div(
        ComponentUtils.testIdL(GpAddCommentPanelL, "BackToAllComments"),
        TooltipL(
          renderContent = _.amend("Back to all comments"),
          renderTarget = ButtonL(
            testId = "BackToAllCommentsButton",
            style = ButtonL.Style.Minimal(icon = Option(Icon.Glyph.ChevronLeft)),
            onClick = onSwitchAllComments.contramap(_ => ())
          )()
        )()
      ),
      child <-- isCommentAssignmentEnabledSignal.splitBoolean(
        whenTrue = _ => renderCommentAssignmentDropdown,
        whenFalse = _ => emptyNode
      ),
      // Close button that is only shown in small screens
      renderCloseButtonForMobileView
    )

  }

  private def renderResolveOrReopenButtonIfNeeded(threadSignal: Signal[CommentThread]) = {
    val showResolveButtonSignal =
      threadSignal.map(!_.isPublic) || commentsDataSignal.map(_.canMakePublicComment)

    showResolveButtonSignal.splitBoolean(
      whenTrue = _ =>
        div(
          tw.textCenter,
          child <-- threadSignal
            .map(_.isResolved)
            .splitBoolean(
              whenTrue = _ =>
                ReopenCommentThreadButtonL(
                  threadSignal,
                  onReopenThread = resetCommentBoxEventBus.writer,
                  viewSource = FormCommentViewSource.FormCommentThreadPanel,
                  buttonType = ReopenCommentThreadButtonL.ButtonType.Icon
                )(),
              whenFalse = _ =>
                ResolveCommentThreadButtonL(
                  threadSignal,
                  onResolveThread = resetCommentBoxEventBus.writer,
                  viewSource = FormCommentViewSource.FormCommentThreadPanel,
                  buttonType = ResolveCommentThreadButtonL.ButtonType.Icon
                )()
            )
        ),
      whenFalse = _ => emptyNode
    )
  }

  private def renderCloseButtonForMobileView = {
    div(
      tw.block,
      tw.sm(tw.hidden),
      div(
        tw.flex.itemsCenter,
        DividerL(direction = Divider.Direction.Vertical)(),
        ButtonL(
          testId = "CloseCommentsThreadButton",
          style = ButtonL.Style.Full(
            height = ButtonL.Height.Fix32,
            icon = Option(Icon.Glyph.Cross)
          ),
          onClick = onClose.contramap(_ => ())
        )()
      )
    )
  }

  private def renderCommentAssignmentDropdown = {
    div(
      tw.flexFill.flex.justifyEnd,
      child <-- threadOptSignal
        .map(_.isEmpty)
        .splitBoolean(
          whenTrue = _ =>
            TooltipL(
              renderTarget = ButtonL(
                style = ButtonL.Style.Minimal(icon = Some(Icon.Glyph.UserAdd)),
                isDisabled = Val(true)
              )("Assign"),
              renderContent = _.amend("Leave a comment before you can assign")
            )(),
          whenFalse = _ =>
            div(
              child <-- lpIdSignal.map(_.parent).distinct.map { fundSubId =>
                CommentAssignmentDropdownWrapperL(
                  fundSubId = fundSubId,
                  lpIdSignal = lpIdSignal,
                  topicDataSignal = topicDataSignal,
                  onAssign = Observer.empty
                )()
              }
            )
        )
    )

  }

  private def handleCreateCommentThread(
    lpId: FundSubLpId,
    topicData: TopicData,
    isPublic: Boolean,
    data: NewCommentBoxL.AddCommentData,
    currentUserIdAndInfoOpt: Option[(UserId, UserInfo)]
  ): Unit = {
    val pendingThread =
      CommentThread(
        id = PendingThreadDefaultId,
        fundSubLpId = lpId,
        topicData = topicData,
        comment = data.comment,
        isResolved = false,
        isPublic = isPublic,
        creatorOpt = currentUserIdAndInfoOpt.map(_._1),
        creatorInfoOpt = currentUserIdAndInfoOpt.map(_._2),
        createdAtOpt = Some(Instant.now()),
        lastEditedAtOpt = None,
        replies = Seq.empty,
        pendingNotification = false,
        resolverInfoOpt = None,
        flaggedRootCommentMetadata = None
      )

    pendingCommentThreadVar.set(Some(pendingThread))

    ZIOUtils.runAsync {
      for {
        _ <- ZIO.succeed(data.onAdding.onNext(()))
        _ <- FundSubEndpointClient
          .createComment(
            CreateCommentParams(
              fundSubLpId = lpId,
              topicData = topicData,
              comment = data.comment,
              viewSource = FormCommentViewSource.FormCommentThreadPanel,
              isPublic = isPublic
            )
          )
          .map {
            _.fold(
              _ => (),
              response =>
                response.foreach { threadId =>
                  pendingCommentThreadVar.set(Some(pendingThread.copy(id = threadId)))
                }
            )
          }
        _ <- ZIO.succeed(data.onAddDone.onNext(()))
      } yield ()
    }
  }

  private def handleAddCommentReply(
    thread: CommentThread,
    data: NewCommentBoxL.AddCommentData,
    currentUserIdOpt: Option[UserId]
  ): Unit = {
    val newCommentItem = CommentsListL.ThreadActivityItem.Comment(
      threadId = thread.id,
      lpId = thread.fundSubLpId,
      comment = data.comment,
      commentIdOpt = None,
      actorOpt = currentUserIdOpt,
      createdAtOpt = Some(Instant.now().truncatedTo(java.time.temporal.ChronoUnit.SECONDS)),
      isPublic = thread.isPublic,
      lastEditedAtOpt = None,
      isPendingComment = true,
      itemId = UUID.randomUUID().toString,
      flaggedCommentMetadata = None,
      mentions = MentionsData.empty
    )
    // show the reply immediately
    val newTempCommentsList = localCommentsVar.now() :+ newCommentItem
    localCommentsVar.set(newTempCommentsList)

    ZIOUtils.runAsync {
      for {
        _ <- ZIO.succeed(data.onAdding.onNext(()))
        _ <- FundSubEndpointClient
          .addCommentReply(
            AddCommentReplyParams(
              fundSubLpId = thread.fundSubLpId,
              issueId = thread.id,
              reply = data.comment,
              viewSource = FormCommentViewSource.FormCommentThreadPanel
            )
          )
          .map(
            _.fold(
              _ => Toast.error("Couldn't add reply. Please try again later."),
              response => {
                if (
                  response.responseCode == ServiceResponseCode.MarkedAsDeleted ||
                  response.responseCode == ServiceResponseCode.NoPermission ||
                  !response.commentReplyIdOpt.exists(_.nonEmpty)
                ) {
                  Toast.info("The comment no longer exists")
                } else {
                  val newTempCommentsList = localCommentsVar
                    .now()
                    .map(item =>
                      if (item.itemId.equals(newCommentItem.itemId)) {
                        item.copy(isPendingComment = false, commentIdOpt = response.commentReplyIdOpt)
                      } else {
                        item
                      }
                    )
                  // sync between pending and real comment but keep the original itemId
                  localCommentsVar.set(newTempCommentsList)
                }
              }
            )
          )
        _ <- ZIO.succeed(data.onAddDone.onNext(()))
      } yield ()
    }
  }

  private def handleSwitchMode(
    currentTopicData: TopicData,
    commentVisibilityType: CommentVisibilityType,
    initialComment: String
  ): Unit = {
    val isPublic = commentVisibilityType match {
      case CommentVisibilityType.PublicComment   => true
      case CommentVisibilityType.InternalComment => false
    }
    currentTopicData match {
      case formQuestionData: FormQuestionData =>
        onSwitchToView.onNext(
          CommentDrawerUIModels.DrawerView
            .Field(
              formQuestionData,
              isPublic = isPublic,
              comment = initialComment
            )
        )
      case amlKycDocData: AmlKycDocData =>
        onSwitchToView.onNext(
          CommentDrawerUIModels.DrawerView
            .Doctype(
              amlKycDocData.doctype,
              isPublic = isPublic,
              comment = initialComment
            )
        )
      case _ => ()
    }
  }

}
