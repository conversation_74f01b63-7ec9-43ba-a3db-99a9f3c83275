package com.anduin.stargazer.fundsub.module.comment.laminar

import design.anduin.components.button.laminar.ButtonL
import com.raquo.laminar.api.L.*
import design.anduin.components.icon.Icon
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.TooltipL
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.formcomment.ReopenCommentParams
import anduin.id.issuetracker.IssueId
import anduin.protobuf.actionlogger.formcomment.FormCommentViewSource
import anduin.stargazer.service.formcomment.FormCommentCommons.{CommentThread, PendingThreadDefaultId}
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.comment.laminar.ReopenCommentThreadButtonL.ButtonType

case class ReopenCommentThreadButtonL(
  threadSignal: Signal[CommentThread],
  onReopenThread: Observer[Unit],
  viewSource: FormCommentViewSource,
  buttonType: ReopenCommentThreadButtonL.ButtonType
) {
  private val isReopeningThreadVar: Var[Boolean] = Var(false)

  private val reopenThreadEventBus = new EventBus[Unit]

  private val isPendingThreadSignal = threadSignal.map(_.id == PendingThreadDefaultId)

  def apply(): HtmlElement = {
    TooltipL(
      renderTarget = ButtonL(
        testId = "ReopenButton",
        style = ButtonL.Style.Full(
          height = ButtonL.Height.Fix32,
          isBusy = isReopeningThreadVar.signal.distinct,
          icon = Some(Icon.Glyph.Reopen)
        ),
        isDisabled = isPendingThreadSignal,
        onClick = reopenThreadEventBus.writer.contramap(_ => ()),
        hasChildren = buttonType == ButtonType.IconWithText
      )(
        buttonType match {
          case ButtonType.Icon         => emptyMod
          case ButtonType.IconWithText => "Reopen"
        }
      )
        .amend(
          reopenThreadEventBus.events
            .withCurrentValueOf(threadSignal.map(_.id))
            .flatMapSwitch(handleReopenThread) --> Observer.empty
        ),
      renderContent = _.amend("Reopen"),
      isDisabled = Val(buttonType == ButtonType.IconWithText)
    )()

  }

  private def handleReopenThread(threadId: IssueId) = {
    AirStreamUtils.taskToStream {
      for {
        _ <- ZIO.succeed(isReopeningThreadVar.set(true))
        resp <- FundSubEndpointClient.reopenComment(
          ReopenCommentParams(
            threadId,
            viewSource
          )
        )
      } yield {
        resp.fold(
          _ => {
            Toast.error("Couldn't reopen the thread, please try again.")
            isReopeningThreadVar.set(false)
          },
          _ => {
            Toast.success("Thread reopened successfully")
            isReopeningThreadVar.set(false)
            onReopenThread.onNext(())
          }
        )
      }
    }
  }

}

object ReopenCommentThreadButtonL {

  enum ButtonType {
    case Icon, IconWithText
  }

}
