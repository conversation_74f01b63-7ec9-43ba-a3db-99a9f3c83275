// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.review.supportingdoc

import design.anduin.components.button.{<PERSON><PERSON>, ButtonStyle}
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.menu.react.{MenuDividerR, MenuItemR, MenuR}
import design.anduin.components.modal.{<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dal<PERSON>ooter, ModalFooterWCancel}
import design.anduin.components.nonidealstate.react.NonIdealStateR
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.PortalPosition
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import fundsub.review.supportingdoc.SupportingDocGroup
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.id.fundsub.FundSubId
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.SupportingDocEndpointClient
import com.anduin.stargazer.fundsub.module.investor.settings.review.supportingdoc.SupportingDocGroupReviewSettingModal.UnassignedSupportingDocGroupName

private[settings] final case class ManageSupportingDocumentGroupModal(
  fundId: FundSubId,
  allSupportingDocs: Seq[(String, Int)],
  initialDocGroups: Seq[SupportingDocGroup],
  onSave: Map[String, SupportingDocGroup] => Callback, // newGroup -> initialName
  onClose: Callback
) {
  def apply(): VdomElement = ManageSupportingDocumentGroupModal.component(this)
}

private[settings] object ManageSupportingDocumentGroupModal {
  private type Props = ManageSupportingDocumentGroupModal

  private final case class State(
    isLoading: Boolean = false,
    currentDocGroups: Seq[(String, Seq[(String, Int)])] = Seq.empty, // groupName -> docTypes(docName, index)
    groupNameChangeMap: Map[String, String] = Map.empty, // newName -> initialName
    currentUnassignedDocs: Seq[(String, Int)] = Seq.empty,
    collapsedDocGroups: Set[String] = Set.empty,
    shouldShowInstruction: Boolean = false,
    formDocGroups: Seq[(String, Seq[(String, Int)])] = Seq.empty // groupName -> docTypes(docName, index)
  )

  private case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomNode = {

      React.Fragment(
        ModalBody()(
          "Create groups, then organize AML/KYC and other documents into them for review using drag-and-drop. " +
            "Manually requested documents will be automatically classified as \"Unassigned document types\" and won't be shown here."
        ),
        <.div(
          tw.flex.borderTop.borderBottom.borderGray3.mb24,
          ^.height := "464px",
          if (state.currentDocGroups.isEmpty) {
            <.div(
              tw.wPc50,
              NonIdealStateR(
                icon = <.div(
                  tw.textGray4,
                  IconR(name = Icon.Glyph.SearchInline, size = Icon.Size.Custom(48))()
                ),
                description = <.div(tw.my4, "No document groups yet"),
                action = renderAdditionalActions(props, state)
              )()
            )
          } else {
            <.div(
              tw.wPc50.flex.flexCol,
              <.div(
                tw.flex.justifyBetween.itemsCenter.borderBottom.borderGray3.py12.pr16,
                ^.paddingLeft := "28px",
                renderAdditionalActions(props, state),
                renderUpdateDocGroupSettingAction(
                  action = "Start over",
                  buttonStyle = Button.Style.Ghost(
                    height = Button.Height.Fix24,
                    icon = Some(Icon.Glyph.Refresh)
                  ),
                  onUpdate = scope.modState(
                    _.copy(
                      currentDocGroups = Seq.empty,
                      currentUnassignedDocs = props.allSupportingDocs
                    )
                  ),
                  warningMessageOpt = Option.when(state.currentDocGroups.nonEmpty) {
                    <.div("All document groups will be lost. Are you sure you want to continue?")
                  }
                )
              ),
              <.div(
                ComponentUtils.testId("DocGroups"),
                tw.overflowYScroll,
                state.currentDocGroups.zipWithIndex.toVdomArray(
                  using { case ((groupName, docTypes), groupIndex) =>
                    renderDocGroup(
                      props = props,
                      state = state,
                      groupName = groupName,
                      docTypes = docTypes,
                      isFirstGroup = groupIndex == 0
                    )
                  }
                )
              )
            )
          },
          renderUnassignedDocumentGroup(
            props = props,
            docTypes = state.currentUnassignedDocs
          )
        ),
        ModalFooter()(
          <.div(
            tw.flex.justifyEnd.spaceX8,
            renderCancelDocumentGroupSetUpAction(props, state),
            Button(
              testId = "SaveBtn",
              style = Button.Style.Full(color = Button.Color.Primary),
              isDisabled = state.currentDocGroups.isEmpty,
              onClick = props.onSave(
                state.currentDocGroups.map { case (groupName, docTypes) =>
                  val initialNameOpt = state.groupNameChangeMap.get(groupName)
                  val newDocGroup = SupportingDocGroup(
                    groupName = groupName,
                    docTypes = docTypes.map(_._1).sorted
                  )
                  (initialNameOpt.getOrElse(newDocGroup.groupName), newDocGroup)
                }.toMap
              ) >> props.onClose
            )("Confirm")
          )
        )
      )
    }

    private def renderUnassignedDocumentGroup(
      props: Props,
      docTypes: Seq[(String, Int)]
    ) = {
      val groupName = UnassignedSupportingDocGroupName
      <.div(
        ComponentUtils.testId("UnassignedGroup"),
        tw.flex.flexCol.wPc50.borderLeft.borderGray3.pl16,
        <.div(
          tw.flex.itemsCenter.py20.mr20.leading24,
          TooltipR(
            renderTarget = TagMod(
              tw.fontSemiBold.truncate,
              groupName
            ),
            renderContent = _(groupName)
          )(),
          Option.when(docTypes.nonEmpty) {
            <.div(
              tw.ml4.textGray7,
              s"(${docTypes.size})"
            )
          }
        ),
        if (docTypes.nonEmpty) {
          <.div(
            tw.overflowYScroll,
            <.div(
              tw.pb20,
              ManageSupportingDocumentGroup(
                items = docTypes,
                onChange = keys =>
                  scope.modState(
                    _.copy(currentUnassignedDocs = keys.flatMap(key => props.allSupportingDocs.find(_._2 == key)))
                  ),
                onRemoveOpt = None,
                onStart = scope.modState(_.copy(shouldShowInstruction = true)),
                onEnd = scope.modState(_.copy(shouldShowInstruction = false)),
                shouldShowInstruction = false
              )()
            )
          )
        } else {
          NonIdealStateR(
            icon = <.div(
              tw.textGray4,
              IconR(name = Icon.Glyph.CheckList, size = Icon.Size.Custom(48))()
            ),
            description = "All document types were assigned"
          )()
        }
      )
    }

    private def renderDocGroup(
      props: Props,
      state: State,
      groupName: String,
      docTypes: Seq[(String, Int)],
      isFirstGroup: Boolean
    ) = {
      val isCollapsed = state.collapsedDocGroups.contains(groupName)
      <.div(
        ComponentUtils.testId("DocGroup"),
        ^.key := groupName,
        ^.paddingLeft := "28px",
        tw.pb12.pt20,
        if ((isCollapsed || docTypes.isEmpty)) TagMod.empty else tw.pb20,
        if (isFirstGroup) TagMod.empty else tw.borderTop.borderGray3,
        <.div(
          tw.flex.itemsCenter,
          tw.mb20,
          if (isCollapsed) {
            <.button(
              IconR(Icon.Glyph.ChevronRight)(),
              ^.onClick --> scope.modState { currentState =>
                currentState.copy(
                  collapsedDocGroups = currentState.collapsedDocGroups.filterNot(_ == groupName)
                )
              }
            )
          } else {
            <.button(
              IconR(Icon.Glyph.ChevronDown)(),
              ^.onClick --> scope.modState { currentState =>
                currentState.copy(collapsedDocGroups = currentState.collapsedDocGroups + groupName)
              }
            )
          },
          TooltipR(
            renderTarget = TagMod(
              tw.fontSemiBold.ml8.truncate,
              groupName
            ),
            renderContent = _(groupName)
          )(),
          <.div(
            tw.ml4.mr20,
            if (docTypes.isEmpty) {
              tw.textWarning4
            } else {
              tw.textGray7
            },
            s"(${docTypes.size})"
          ),
          <.div(
            tw.mlAuto,
            PopoverR(
              renderTarget = (openPopover, isOpen) =>
                Button(
                  testId = "Actions",
                  style = Button.Style.Full(
                    icon = Some(Icon.Glyph.EllipsisHorizontal),
                    height = Button.Height.Fix24,
                    isSelected = isOpen
                  ),
                  onClick = openPopover
                )(),
              renderContent = closePopover =>
                MenuR()(
                  renderCreateOrEditGroupAction(
                    state = state,
                    currentGroupNameOpt = Some(groupName),
                    onClose = closePopover
                  ),
                  MenuDividerR()(),
                  renderDeleteDocGroupAction(
                    onClose = closePopover,
                    groupName = groupName
                  )
                ),
              position = PortalPosition.BottomRight
            )()
          )
        ),
        ManageSupportingDocumentGroup(
          items = docTypes,
          onChange = keys =>
            scope.modState { currentState =>
              currentState.copy(currentDocGroups =
                updateSupportingDocGroups(
                  newDocTypesOpt = Some(keys.flatMap(key => props.allSupportingDocs.find(_._2 == key))),
                  newGroupNameOpt = None,
                  currentGroupNameOpt = Some(groupName),
                  currentDocGroups = currentState.currentDocGroups
                )
              )
            },
          onRemoveOpt = Some(key =>
            scope.modState { currentState =>
              currentState.copy(
                currentUnassignedDocs = (
                  currentState.currentUnassignedDocs ++ props.allSupportingDocs.find(_._2 == key)
                ).distinct,
                currentDocGroups = updateSupportingDocGroups(
                  newDocTypesOpt = currentState.currentDocGroups
                    .collectFirst { case (name, docTypes) if name == groupName => docTypes.filterNot(_._2 == key) },
                  newGroupNameOpt = None,
                  currentGroupNameOpt = Some(groupName),
                  currentDocGroups = currentState.currentDocGroups
                )
              )
            }
          ),
          shouldHide = isCollapsed,
          onEnd = scope.modState { currentState =>
            currentState.copy(
              collapsedDocGroups = currentState.collapsedDocGroups.filterNot(_ == groupName),
              shouldShowInstruction = false
            )
          },
          onStart = scope.modState(_.copy(shouldShowInstruction = true)),
          shouldShowInstruction = state.shouldShowInstruction
        )()
      )
    }

    private def updateSupportingDocGroups(
      currentGroupNameOpt: Option[String],
      newGroupNameOpt: Option[String],
      newDocTypesOpt: Option[Seq[(String, Int)]],
      currentDocGroups: Seq[(String, Seq[(String, Int)])]
    ): Seq[(String, Seq[(String, Int)])] = {
      val docGroupIndexOpt = currentGroupNameOpt.flatMap { currentGroupName =>
        currentDocGroups.zipWithIndex.collectFirst {
          case (docGroup, index) if docGroup._1.trim == currentGroupName.trim => index
        }
      }
      val updatedDocNameOpt = newGroupNameOpt.fold(currentGroupNameOpt)(Some(_))
      val updatedDocTypes = newDocTypesOpt.getOrElse(
        currentGroupNameOpt
          .flatMap { currentGroupName =>
            currentDocGroups
              .collectFirst {
                case (groupName, docTypes) if groupName.trim == currentGroupName => docTypes
              }
          }
          .getOrElse(Seq.empty)
      )
      updatedDocNameOpt.fold(currentDocGroups) { updatedDocName =>
        docGroupIndexOpt.fold(currentDocGroups :+ (updatedDocName, updatedDocTypes)) { index =>
          currentDocGroups.updated(index, (updatedDocName, updatedDocTypes))
        }
      }
    }

    private def renderUpdateDocGroupSettingAction(
      action: String,
      buttonStyle: ButtonStyle,
      onUpdate: Callback,
      warningMessageOpt: Option[VdomElement]
    ) = {
      warningMessageOpt.fold(
        Button(
          testId = action,
          style = buttonStyle,
          onClick = onUpdate
        )(action)
      ) { warningMessage =>
        Modal(
          testId = action,
          title = action,
          renderTarget = openModal =>
            Button(
              testId = action,
              style = buttonStyle,
              onClick = openModal
            )(action),
          renderContent = closeModal =>
            React.Fragment(
              ModalBody()(
                warningMessage
              ),
              ModalFooter()(
                <.div(
                  tw.flex.justifyEnd.spaceX8,
                  Button(onClick = closeModal)("Back"),
                  Button(
                    testId = "Continue",
                    style = Button.Style.Full(color = Button.Color.Warning),
                    onClick = onUpdate >> closeModal
                  )("Continue")
                )
              )
            )
        )()
      }
    }

    private def renderAdditionalActions(props: Props, state: State) = {
      <.div(
        tw.flex.spaceX8,
        renderCreateOrEditGroupAction(
          state = state,
          currentGroupNameOpt = None,
          onClose = Callback.empty
        ),
        Option.when(state.formDocGroups.nonEmpty && !state.isLoading) {
          renderUpdateDocGroupSettingAction(
            action = "Use default document groups",
            buttonStyle = Button.Style.Ghost(
              color = Button.Color.Primary,
              height = Button.Height.Fix24
            ),
            onUpdate = scope.modState(currentState =>
              currentState.copy(
                currentDocGroups = currentState.formDocGroups,
                currentUnassignedDocs = props.allSupportingDocs.filterNot { case (_, index) =>
                  val assignedDocs = currentState.formDocGroups.flatMap(_._2)
                  assignedDocs.map(_._2).contains(index)
                },
                isLoading = false
              )
            ),
            warningMessageOpt =
              Option.when(state.currentDocGroups != state.formDocGroups && state.currentDocGroups.nonEmpty) {
                React.Fragment(
                  <.div(
                    tw.mb8,
                    "Any current document groups and settings will be replaced with the default group created in the form settings."
                  ),
                  <.div("Are you sure you want to continue?")
                )
              }
          )
        }
      )
    }

    private def renderCreateOrEditGroupAction(
      state: State,
      currentGroupNameOpt: Option[String],
      onClose: Callback
    ) = {
      val isCreatingNewGroup = currentGroupNameOpt.isEmpty
      val title = if (isCreatingNewGroup) {
        "Create document group"
      } else {
        "Edit document group name"
      }
      Modal(
        testId = "CreateDocGroup",
        title = title,
        renderTarget = openModal =>
          if (isCreatingNewGroup) {
            Button(
              testId = "Create",
              style = Button.Style.Ghost(
                color = Button.Color.Primary,
                height = Button.Height.Fix24
              ),
              onClick = openModal
            )(title)
          } else {
            MenuItemR(
              onClick = openModal,
              icon = Some(Icon.Glyph.Edit)
            )(title)
          },
        renderContent = closeModal =>
          EditDocumentGroupNameModal(
            docGroups = state.currentDocGroups.map(_._1).toList,
            initialGroupNameOpt = currentGroupNameOpt,
            onClose = closeModal,
            onSave = newName =>
              scope.modState(
                currentState => {
                  val initialNameOpt = currentGroupNameOpt.flatMap { currentGroupName =>
                    currentState.groupNameChangeMap.get(currentGroupName)
                  }
                  currentState.copy(
                    currentDocGroups = updateSupportingDocGroups(
                      currentDocGroups = currentState.currentDocGroups,
                      currentGroupNameOpt = currentGroupNameOpt,
                      newGroupNameOpt = Some(newName),
                      newDocTypesOpt = None
                    ),
                    groupNameChangeMap = currentState.groupNameChangeMap ++ initialNameOpt
                      .fold(currentGroupNameOpt)(Some(_))
                      .map(newName -> _)
                  )
                },
                closeModal >> Toast.successCallback(
                  if (isCreatingNewGroup) {
                    "Document group created"
                  } else {
                    "Document group name edited"
                  }
                )
              )
          )(),
        afterUserClose = onClose
      )()
    }

    private def renderDeleteDocGroupAction(
      groupName: String,
      onClose: Callback
    ) = {
      val title = "Delete document group"
      Modal(
        testId = "DeleteDocGroup",
        title = title,
        renderTarget = openModal =>
          MenuItemR(
            onClick = openModal,
            icon = Some(Icon.Glyph.Trash),
            color = MenuItemR.ColorDanger
          )(title),
        renderContent = closeModal =>
          React.Fragment(
            ModalBody()(
              <.div(
                "All document types in ",
                <.span(
                  tw.fontSemiBold,
                  groupName
                ),
                "  will be unassigned and moved back to the ",
                <.span(
                  tw.fontSemiBold,
                  UnassignedSupportingDocGroupName
                ),
                " section"
              ),
              <.div(
                tw.mt8,
                "Are you sure you want to continue?"
              )
            ),
            ModalFooterWCancel(closeModal)(
              Button(
                testId = "Delete",
                style = Button.Style.Full(color = Button.Color.Danger),
                onClick = scope.modState(
                  currentState => {
                    val docTypes = currentState.currentDocGroups
                      .collectFirst { case (name, docTypes) if name == groupName => docTypes }
                      .getOrElse(Seq.empty)
                    currentState.copy(
                      currentDocGroups = currentState.currentDocGroups.filterNot(_._1 == groupName),
                      currentUnassignedDocs = currentState.currentUnassignedDocs ++ docTypes
                    )
                  },
                  Toast.successCallback("Document group deleted")
                )
              )(title)
            )
          ),
        afterUserClose = onClose
      )()
    }

    private def renderCancelDocumentGroupSetUpAction(
      props: Props,
      state: State
    ) = {
      val initialDocGroups = props.initialDocGroups.map { group =>
        val sortedDocTypes = props.allSupportingDocs.collect {
          case (docType, index) if group.docTypes.exists(_.trim == docType.trim) => index
        }.toSet
        (group.groupName.trim, sortedDocTypes)
      }.toSet
      val currentDocGroup = state.currentDocGroups.map { case (groupName, docTypes) =>
        (groupName.trim, docTypes.map(_._2).toSet)
      }.toSet
      if (currentDocGroup == initialDocGroups) {
        Button(
          onClick = props.onClose
        )("Cancel")
      } else {
        Modal(
          testId = "WarningCancelDocGroupSetup",
          title = "Cancel document group set up",
          renderTarget = openModal =>
            Button(
              testId = "CancelBtn",
              onClick = openModal
            )("Cancel"),
          renderContent = closeModal =>
            React.Fragment(
              ModalBody()(
                "All progress will be lost. Are you sure you want to continue?"
              ),
              ModalFooter()(
                <.div(
                  tw.flex.justifyEnd.spaceX8,
                  Button(
                    testId = "Back",
                    onClick = closeModal
                  )("Back"),
                  Button(
                    testId = "CancelSetup",
                    style = Button.Style.Full(color = Button.Color.Warning),
                    onClick = props.onClose
                  )("Cancel set up")
                )
              )
            )
        )()
      }
    }

    def fetchSupportingDocGroupsFromForm: Callback = {
      val cb = for {
        props <- scope.props
        _ <- ZIOUtils.toReactCallback {
          SupportingDocEndpointClient
            .getFundSupportingDocGroupsFromForm(props.fundId)
            .map(
              _.fold(
                _ =>
                  scope.modState(
                    _.copy(isLoading = false),
                    Toast.errorCallback("Failed to get supporting document groups from form")
                  ),
                res =>
                  scope.modState(
                    _.copy(
                      formDocGroups = reformatDocGroups(props, res.supportingDocGroups),
                      isLoading = false
                    )
                  )
              )
            )
        }
      } yield ()
      scope.modState(_.copy(isLoading = true), cb)
    }

  }

  private def reformatDocGroups(props: Props, docGroups: Seq[SupportingDocGroup]) = {
    docGroups.map { group =>
      val sanitizedDocTypes = props.allSupportingDocs
        .filter { case (docType, _) =>
          group.docTypes.exists(_.trim.equalsIgnoreCase(docType.trim))
        }
      (group.groupName, sanitizedDocTypes)
    }
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps { props =>
      val currentUnassignedDocs = props.allSupportingDocs.filterNot { case (docType, _) =>
        props.initialDocGroups.flatMap(_.docTypes).exists(_.trim == docType.trim)
      }
      State(
        currentDocGroups = reformatDocGroups(props, props.initialDocGroups),
        currentUnassignedDocs = currentUnassignedDocs
      )
    }
    .renderBackend[Backend]
    .componentDidMount(_.backend.fetchSupportingDocGroupsFromForm)
    .build

}
