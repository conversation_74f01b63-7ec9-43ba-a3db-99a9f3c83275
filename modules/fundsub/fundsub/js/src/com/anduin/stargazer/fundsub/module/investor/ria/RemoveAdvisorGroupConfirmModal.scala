// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.ria

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL, ModalL}
import design.anduin.components.toast.Toast
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.ria.{RemoveRiaFundGroupParams, RiaFundGroup}
import com.anduin.stargazer.fundsub.client.FundSubRiaEndpointClient

final case class RemoveAdvisorGroupConfirmModal(
  group: RiaFundGroup,
  renderTarget: Observer[Unit] => HtmlElement,
  onClose: Observer[Unit],
  onDoneRemoveAdvisorGroup: Observer[Unit]
) {

  private val isRemovingVar = Var(false)
  private val isRemovingSignal = isRemovingVar.signal.distinct

  private val removeEvent = new EventBus[Unit]

  def apply(): Node = {
    ModalL(
      size = ModalL.Size(width = ModalL.Width.Px480),
      afterUserClose = onClose,
      renderContent = onCloseModel => {
        div(
          ModalBodyL(
            span(s"${group.name} will be removed from your fund")
          ),
          ModalFooterWCancelL(onCloseModel)(
            ButtonL(
              style = ButtonL.Style.Full(
                color = ButtonL.Color.Danger,
                isBusy = isRemovingSignal
              ),
              onClick = removeEvent.toObserver.contramap(_ => ())
            )("Remove").amend(
              removeEvent.events.flatMapSwitch { _ =>
                removeAdvisorGroup(onCloseModel)
              } --> Observer.empty
            )
          )
        )
      },
      renderTitle = _ => "Remove advisor entity?",
      renderTarget = renderTarget
    )()
  }

  private def removeAdvisorGroup(
    onClose: Observer[Unit]
  ) = {
    AirStreamUtils.taskToStream(
      for {
        _ <- ZIO.succeed(isRemovingVar.set(true))
        _ <- FundSubRiaEndpointClient
          .removeRiaFundGroup(
            RemoveRiaFundGroupParams(
              fundRiaGroupId = group.id
            )
          )
          .map(
            _.fold(
              _ => {
                isRemovingVar.set(false)
                Toast.error("Failed to remove advisor entity")
              },
              _ => {
                isRemovingVar.set(false)
                onDoneRemoveAdvisorGroup.onNext(())
                onClose.onNext(())
                Toast.success("Advisor entity removed")
              }
            )
          )
      } yield ()
    )
  }

}
