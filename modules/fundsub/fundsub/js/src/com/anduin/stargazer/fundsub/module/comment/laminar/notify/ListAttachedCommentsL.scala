// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.notify

import com.raquo.laminar.api.L.*
import design.anduin.components.collapse.laminar.CollapseWithIconL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import org.scalajs.dom

import anduin.id.entity.EntityId
import anduin.id.fundsub.FundSubLpId
import anduin.scalajs.pluralize.Pluralize
import anduin.stargazer.service.formcomment.FormCommentCommons.CommentThreadWithMetaData
import com.anduin.stargazer.fundsub.module.comment.laminar.share.{AmlKycDocumentInfo, FieldSummaryInfo}

private[notify] case class ListAttachedCommentsL(
  entitySignal: Signal[Option[EntityId]],
  fundSubLpId: FundSubLpId,
  threads: List[CommentThreadWithMetaData]
) {

  private val isExpandedVar = Var(false)

  private def renderThread(thread: CommentThreadWithMetaData) = {
    val sealedValue = thread.commentThread.topicData.asMessage.sealedValue
    div(
      tw.borderAll.border1.borderGray3.rounded6.p16.mt8,
      SingleThreadItemL(
        thread = thread.commentThread
      )(
        div(
          child.maybe <-- entitySignal.map {
            _.flatMap { entity =>
              if (sealedValue.isAmlKycDocData) {
                sealedValue.amlKycDocData.map { data =>
                  AmlKycDocumentInfo(
                    amlKycData = data,
                    fundEntityId = entity,
                    fundSubLpId = thread.commentThread.fundSubLpId,
                    isPublic = thread.commentThread.isPublic,
                    isHidden = thread.targetIsHidden
                  )().amend(tw.mb8)
                }
              } else {
                sealedValue.formQuestionData.map { data =>
                  FieldSummaryInfo(
                    formQuestionData = data,
                    fundEntityId = entity,
                    fundSubLpId = thread.commentThread.fundSubLpId,
                    isPublic = thread.commentThread.isPublic,
                    isHidden = thread.targetIsHidden
                  )().amend(tw.mb8)
                }
              }
            }
          }
        )
      )
    )
  }

  def apply(): HtmlElement = {
    CollapseWithIconL(
      render = renderProps => {
        div(
          // Tooltip
          div(
            tw.flex,
            TooltipL(
              renderContent = _.amend(
                child <-- isExpandedVar.signal.map {
                  if (_) "Hide attached comments" else "Show attached comments"
                }
              ),
              renderTarget = {
                div(
                  tw.inlineFlex.itemsCenter,
                  tw.borderAll.border1.borderGray4.roundedFull,
                  tw.textGray7.fontSemiBold.textSmall,
                  tw.py4.px8.cursorPointer.hover(tw.bgGray3),
                  onClick --> Observer[dom.MouseEvent] { _ =>
                    renderProps.toggle.onNext(())
                  },
                  div(
                    tw.mr4,
                    s"${threads.size} attached ${Pluralize("comment", threads.size)}"
                  ),
                  renderProps.renderIcon.amend(IconL(name = Val(Icon.Glyph.ChevronRight))())
                )
              }
            )()
          ),
          // Attached comments
          renderProps.renderContent.amend(
            tw.overflowAuto,
            display.none,
            threads.map(renderThread)
          )
        )
      },
      onAnimationEnd = Observer[CollapseWithIconL.OnAnimationEndData] { data =>
        isExpandedVar.set(!data.isExpanded)
      }
    )()
  }

}
