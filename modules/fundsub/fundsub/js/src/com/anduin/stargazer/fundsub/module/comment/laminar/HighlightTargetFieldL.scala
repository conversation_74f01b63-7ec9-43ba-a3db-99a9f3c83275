// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration
import scala.scalajs.js

import com.raquo.laminar.api.L.*
import design.anduin.facades.dom.Element
import design.anduin.facades.dom.animation.{Keyframe, KeyframeBuilder, KeyframeEffectOptions}
import org.scalajs.dom
import org.scalajs.dom.{Event, HTMLElement}

import anduin.forms.utils.FormValidationUtils.FieldID

private[laminar] case class HighlightTargetFieldL(field: String) {

  private val fieldId = FieldID(field).toDomId

  // A field can has the indentation
  private val marginLeftVar = Var(0.0)

  private val animationDuration = FiniteDuration(200, TimeUnit.MILLISECONDS).toMillis.toDouble

  private def buildKeyframes(element: HTMLElement): js.Array[Keyframe] = {
    val _ = element
    js.Array(
      new KeyframeBuilder()
        .withBackground("#FFF")
        .getKeyframe(),
      new KeyframeBuilder()
        .withBackground("rgba(211, 238, 255, 0.75)")
        .getKeyframe()
    )
  }

  private def highlight(ele: dom.HTMLElement) = {
    val currentMarginLeft = dom.window.getComputedStyle(ele).marginLeft
    val marginLeft = if (currentMarginLeft.endsWith("px")) {
      currentMarginLeft.stripSuffix("px").toDouble
    } else {
      0.0
    }
    marginLeftVar.set(marginLeft)

    ele.style.marginLeft = "-32px"
    ele.style.marginRight = "-32px"
    ele.style.paddingLeft = s"${32 + marginLeft}px"
    ele.style.paddingRight = "32px"

    // Fade in it
    ele
      .asInstanceOf[Element] // scalafix:ok DisableSyntax.asInstanceOf
      .animate(
        js.Array(
          new KeyframeBuilder()
            .withBackground("#FFF")
            .getKeyframe(),
          new KeyframeBuilder()
            .withBackground("rgba(211, 238, 255, 0.75)")
            .getKeyframe()
        ),
        KeyframeEffectOptions(
          durationParam = Option(animationDuration),
          fillParam = Option("forwards")
        )
      )
  }

  private def removeHighlight(ele: dom.HTMLElement) = {
    // Fade out it
    ele
      .asInstanceOf[Element] // scalafix:ok DisableSyntax.asInstanceOf
      .animate(
        buildKeyframes(ele).reverse,
        KeyframeEffectOptions(
          durationParam = Option(animationDuration),
          fillParam = Option("forwards")
        )
      )
      .addEventListener(
        "finish",
        (_: Event) => {
          ele.style.removeProperty("background-color")

          List(
            "margin-right",
            "padding-left",
            "padding-right"
          ).foreach {
            ele.style.removeProperty(_)
          }
          // Reset the indentation
          val marginLeft = marginLeftVar.now()
          if (marginLeft == 0) {
            ele.style.removeProperty("margin-left")
          } else {
            ele.style.marginLeft = s"${marginLeft}px"
          }
        }
      )
  }

  def apply(): HtmlElement = {
    div(
      onMountCallback { _ =>
        Option(dom.document.getElementById(fieldId)).foreach {
          case ele: dom.HTMLElement =>
            highlight(ele)
          case _ =>
            ()
        }
      },
      onUnmountCallback { _ =>
        Option(dom.document.getElementById(fieldId)).foreach {
          case ele: dom.HTMLElement =>
            removeHighlight(ele)
          case _ =>
            ()
        }
      }
    )
  }

}
