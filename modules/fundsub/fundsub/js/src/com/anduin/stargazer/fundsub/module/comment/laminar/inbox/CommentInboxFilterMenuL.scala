package com.anduin.stargazer.fundsub.module.comment.laminar.inbox

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.checkbox.CheckboxL
import design.anduin.components.divider.laminar.DividerL
import design.anduin.components.menu.laminar.MenuItemL
import design.anduin.components.radio.RadioL
import design.anduin.components.wrapper.laminar.WrapperL
import design.anduin.style.tw.*

import anduin.dashboard.data.DocumentRequestCell
import anduin.fundsub.endpoint.fundclose.FundSubCloseInfo
import anduin.id.fundsub.FundSubCloseId
import anduin.protobuf.flow.fundsub.admin.lpdashboard.{LpDocRequestStatus, LpStatus}
import anduin.stargazer.service.formcomment.FormCommentCommons.{AnchorPointTypeFilter, CommentVisibilityType}
import com.anduin.stargazer.fundsub.module.comment.laminar.inbox.CommentInboxFilterMenuL.CommentFilterData
import com.anduin.stargazer.fundsub.module.common.LpPlainStatusRenderer
import com.anduin.stargazer.fundsub.module.investor.dashboard.v2.renderer.DocumentRequestStatusRenderer

private[inbox] case class CommentInboxFilterMenuL(
  initialCommentFilterData: CommentFilterData,
  updateCommentFilter: Observer[CommentFilterData],
  fundSubCloseInfoSignal: Signal[Seq[FundSubCloseInfo]],
  validSupportingDocStatusListSignal: Signal[Seq[LpDocRequestStatus]],
  validLpStatusSignal: Signal[Seq[LpStatus]]
) {

  private val commentFilterDataVar: Var[CommentFilterData] = Var(initialCommentFilterData)

  private val commentFilterDataSignal: Signal[CommentFilterData] = commentFilterDataVar.signal

  private def renderUnreadCommentsFilter = {
    div(
      tw.mt6.px8,
      renderFilterCheckboxItem(
        label = "Unread comments",
        isSelectedSignal = commentFilterDataSignal.map(_.includeUnreadComments),
        onChange = commentFilterDataVar.updater { (currentData, isSelected) =>
          currentData.copy(includeUnreadComments = isSelected)
        }
      )
    )

  }

  private def renderInternalCommentsFilter = {
    renderFilterCheckboxItem(
      label = "Internal comments",
      isSelectedSignal =
        commentFilterDataSignal.map(_.commentVisibilityFilter.contains(CommentVisibilityType.InternalComment)),
      onChange = commentFilterDataVar.updater { (currentData, isSelected) =>
        val updatedVisibilityFilter = if (isSelected) {
          currentData.commentVisibilityFilter + CommentVisibilityType.InternalComment
        } else {
          currentData.commentVisibilityFilter - CommentVisibilityType.InternalComment
        }
        currentData.copy(commentVisibilityFilter = updatedVisibilityFilter)
      }
    )

  }

  private def renderSharedCommentsFilter = {
    renderFilterCheckboxItem(
      label = "External comments",
      isSelectedSignal = commentFilterDataSignal.map(
        _.commentVisibilityFilter.contains(CommentVisibilityType.PublicComment)
      ),
      onChange = commentFilterDataVar.updater { (currentData, isSelected) =>
        val updatedVisibilityFilter = if (isSelected) {
          currentData.commentVisibilityFilter + CommentVisibilityType.PublicComment
        } else {
          currentData.commentVisibilityFilter - CommentVisibilityType.PublicComment
        }
        currentData.copy(commentVisibilityFilter = updatedVisibilityFilter)
      }
    )
  }

  def apply(): HtmlElement = {
    div(
      width.px := 350,
      div(
        maxHeight.px := 520,
        tw.overflowYAuto,
        renderUnreadCommentsFilter,
        DividerL()(),
        renderCommentVisibilityFilter,
        renderClosesFilter,
        DividerL()(),
        renderDocumentTypeFilter,
        renderLpStatusFilter,
        renderAmlKycStatusFilter
      ),
      renderFooter
    )
  }

  private def renderCommentVisibilityFilter = {
    div(
      tw.px8,
      renderInternalCommentsFilter,
      renderSharedCommentsFilter
    )
  }

  private def renderFooter = {
    div(
      tw.hPx64.px16.flex,
      tw.itemsCenter.justifyBetween,
      tw.borderTop.borderGray3.mt8,
      ButtonL(
        style = ButtonL.Style.Text(
          color = ButtonL.Color.Primary
        ),
        onClick = commentFilterDataVar.writer.contramap(_ => CommentFilterData())
      )("Clear all"),
      ButtonL(
        style = ButtonL.Style.Full(
          color = ButtonL.Color.Primary
        ),
        onClick = Observer { _ =>
          updateCommentFilter.onNext(commentFilterDataVar.now())
        }
      )("Apply")
    )
  }

  private def renderAmlKycStatusFilter = {
    val shouldRenderAmlKycStatusFilterSignal =
      commentFilterDataSignal.map(_.documentTypeFilter == AnchorPointTypeFilter.AmlKycComments)
    child <-- shouldRenderAmlKycStatusFilterSignal.splitBoolean(
      whenTrue = _ =>
        div(
          DividerL()(),
          div(
            tw.px8,
            renderAmlKycStatusFilterHeader,
            children <-- validSupportingDocStatusListSignal.split(identity) { case (status, _, statusSignal) =>
              renderFilterCheckboxItem(
                label = div(
                  child <-- statusSignal.map { status =>
                    DocumentRequestStatusRenderer(
                      DocumentRequestCell.getDocRequestStatus(status)
                    )()
                  }
                ),
                isSelectedSignal = commentFilterDataSignal.map(_.supportingDocStatusFilter.contains(status)),
                onChange = commentFilterDataVar.updater { (currentData, isSelected) =>
                  val updatedLpStatusFilter = if (isSelected) {
                    currentData.supportingDocStatusFilter + status
                  } else {
                    currentData.supportingDocStatusFilter - status
                  }
                  currentData.copy(supportingDocStatusFilter = updatedLpStatusFilter)
                }
              )
            }
          )
        ),
      whenFalse = _ => emptyNode
    )
  }

  private def renderLpStatusFilter = {
    val shouldRenderLpStatusFilterSignal =
      commentFilterDataSignal.map(_.documentTypeFilter == AnchorPointTypeFilter.FormComments)
    child <-- shouldRenderLpStatusFilterSignal.splitBoolean(
      whenTrue = _ =>
        div(
          DividerL()(),
          div(
            tw.px8,
            renderLpStatusFilterHeader,
            children <-- validLpStatusSignal.split(identity) { case (status, _, statusSignal) =>
              renderFilterCheckboxItem(
                label = div(
                  child <-- statusSignal.map { status =>
                    WrapperL(
                      LpPlainStatusRenderer(status)()
                    )
                  }
                ),
                isSelectedSignal = commentFilterDataSignal.map(_.lpStatusFilter.contains(status)),
                onChange = commentFilterDataVar.updater { (currentData, isSelected) =>
                  val updatedLpStatusFilter = if (isSelected) {
                    currentData.lpStatusFilter + status
                  } else {
                    currentData.lpStatusFilter - status
                  }
                  currentData.copy(lpStatusFilter = updatedLpStatusFilter)
                }
              )
            }
          )
        ),
      whenFalse = _ => emptyNode
    )
  }

  private def renderDocumentTypeFilter = {
    val onSelect = commentFilterDataVar.updater[AnchorPointTypeFilter] { (currentData, selectedType) =>
      currentData.copy(documentTypeFilter = selectedType)
    }
    div(
      tw.px8,
      div(
        tw.hPx32.px8.fontSemiBold,
        "Document"
      ),
      Seq(
        AnchorPointTypeFilter.AllTypes,
        AnchorPointTypeFilter.FormComments,
        AnchorPointTypeFilter.AmlKycComments
      )
        .map { filterType =>
          MenuItemL(
            onClick = onSelect.contramap(_ => filterType)
          )(
            RadioL(
              isChecked = commentFilterDataSignal.map(_.documentTypeFilter == filterType).distinct,
              onChange = onSelect.contramap(_ => filterType)
            )(
              div(
                tw.leading20.ml4,
                filterType match {
                  case AnchorPointTypeFilter.AllTypes       => "All comments"
                  case AnchorPointTypeFilter.FormComments   => "Only comments on forms"
                  case AnchorPointTypeFilter.AmlKycComments => "Only comments on AML/KYC documents"
                }
              )
            )
          )
        }
    )

  }

  private def renderClosesFilter = {
    child <-- fundSubCloseInfoSignal
      .map(_.nonEmpty)
      .splitBoolean(
        whenTrue = _ =>
          div(
            DividerL()(),
            div(
              tw.px8,
              renderClosesFilterHeader,
              children <-- fundSubCloseInfoSignal.split(_.fundSubCloseId) { case (closeId, _, closeSignal) =>
                renderCloseItem(closeId, closeSignal)
              }
            )
          ),
        whenFalse = _ => emptyNode
      )

  }

  private def renderLpStatusFilterHeader = {
    val selectOrDeselectAllEventBus = new EventBus[Boolean]
    div(
      tw.px8.hPx32,
      tw.flex.justifyBetween.itemsCenter,
      div(
        tw.fontSemiBold,
        "Form status"
      ),
      div(
        tw.flex.itemsCenter.spaceX12,
        ButtonL(
          style = ButtonL.Style.Text(
            color = ButtonL.Color.Primary
          ),
          onClick = selectOrDeselectAllEventBus.writer.contramap(_ => true)
        )("Select all"),
        ButtonL(
          style = ButtonL.Style.Text(
            color = ButtonL.Color.Primary
          ),
          onClick = selectOrDeselectAllEventBus.writer.contramap(_ => false)
        )("Deselect all")
      ),
      selectOrDeselectAllEventBus.events.withCurrentValueOf(validLpStatusSignal).map {
        case (isSelected, validLpStatus) =>
          commentFilterDataVar.update(
            _.copy(lpStatusFilter = if (isSelected) {
              validLpStatus.toSet
            } else {
              Set.empty
            })
          )
      } --> Observer.empty
    )
  }

  private def renderAmlKycStatusFilterHeader = {
    val selectOrDeselectAllEventBus = new EventBus[Boolean]
    div(
      tw.px8.hPx32,
      tw.flex.justifyBetween.itemsCenter,
      div(
        tw.fontSemiBold,
        "AML/KYC status"
      ),
      div(
        tw.flex.itemsCenter.spaceX12,
        ButtonL(
          style = ButtonL.Style.Text(
            color = ButtonL.Color.Primary
          ),
          onClick = selectOrDeselectAllEventBus.writer.contramap(_ => true)
        )("Select all"),
        ButtonL(
          style = ButtonL.Style.Text(
            color = ButtonL.Color.Primary
          ),
          onClick = selectOrDeselectAllEventBus.writer.contramap(_ => false)
        )("Deselect all")
      ),
      selectOrDeselectAllEventBus.events.withCurrentValueOf(validSupportingDocStatusListSignal).map {
        case (isSelected, validAmlKycStatus) =>
          commentFilterDataVar.update(
            _.copy(supportingDocStatusFilter = if (isSelected) {
              validAmlKycStatus.toSet
            } else {
              Set.empty
            })
          )
      } --> Observer.empty
    )
  }

  private def renderClosesFilterHeader = {
    val selectOrDeselectAllEventBus = new EventBus[Boolean]
    div(
      tw.px8.hPx32,
      tw.flex.justifyBetween.itemsCenter,
      div(
        tw.fontSemiBold,
        "Close"
      ),
      div(
        tw.flex.itemsCenter.spaceX12,
        ButtonL(
          style = ButtonL.Style.Text(
            color = ButtonL.Color.Primary
          ),
          onClick = selectOrDeselectAllEventBus.writer.contramap(_ => true)
        )("Select all"),
        ButtonL(
          style = ButtonL.Style.Text(
            color = ButtonL.Color.Primary
          ),
          onClick = selectOrDeselectAllEventBus.writer.contramap(_ => false)
        )("Deselect all")
      ),
      selectOrDeselectAllEventBus.events.withCurrentValueOf(fundSubCloseInfoSignal).map {
        case (isSelected, fundSubCloseInfo) =>
          commentFilterDataVar.update(
            _.copy(lpCloseFilter = if (isSelected) {
              fundSubCloseInfo.map(_.fundSubCloseId).toSet
            } else {
              Set.empty
            })
          )
      } --> Observer.empty
    )
  }

  private def renderCloseItem(closeId: FundSubCloseId, closeSignal: Signal[FundSubCloseInfo]) = {
    renderFilterCheckboxItem(
      label = div(
        text <-- closeSignal.map(_.name)
      ),
      isSelectedSignal = commentFilterDataSignal.map(_.lpCloseFilter.contains(closeId)),
      onChange = commentFilterDataVar.updater { (currentData, isSelected) =>
        val updatedCloseFilter = if (isSelected) {
          currentData.lpCloseFilter + closeId
        } else {
          currentData.lpCloseFilter - closeId
        }
        currentData.copy(lpCloseFilter = updatedCloseFilter)
      }
    )
  }

  private def renderFilterCheckboxItem(
    label: Node,
    isSelectedSignal: Signal[Boolean],
    onChange: Observer[Boolean]
  ) = {
    val toggleEventBus = new EventBus[Unit]
    MenuItemL(
      onClick = toggleEventBus.writer
    )(
      CheckboxL(
        isChecked = isSelectedSignal.distinct,
        onChange = toggleEventBus.writer.contramap(_ => ())
      )(
        div(
          tw.leading20.ml4,
          label,
          toggleEventBus.events.withCurrentValueOf(isSelectedSignal).not --> onChange
        )
      )
    )
  }

}

private[inbox] object CommentInboxFilterMenuL {

  case class CommentFilterData(
    includeUnreadComments: Boolean = false,
    commentVisibilityFilter: Set[CommentVisibilityType] = Set.empty,
    lpCloseFilter: Set[FundSubCloseId] = Set.empty,
    documentTypeFilter: AnchorPointTypeFilter = AnchorPointTypeFilter.AllTypes,
    supportingDocStatusFilter: Set[LpDocRequestStatus] = Set.empty,
    lpStatusFilter: Set[LpStatus] = Set.empty
  ) derives CanEqual

}
