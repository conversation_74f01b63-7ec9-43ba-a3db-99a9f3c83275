// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.auditlog

import java.time.{Instant, LocalDate}
import design.anduin.components.button.Button
import design.anduin.components.date.react.DatePickerR
import design.anduin.components.modal.{ModalBody, ModalFooterWCancel}
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import anduin.utils.DateTimeUtils

import java.time.temporal.ChronoUnit

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[settings] final case class CustomDateRangeFilterModal(
  onApply: (Instant, Instant) => Callback,
  onClose: Callback,
  from: Option[Instant] = None,
  to: Option[Instant] = None
) {
  def apply(): VdomElement = CustomDateRangeFilterModal.component(this)
}

private[settings] object CustomDateRangeFilterModal {

  private type Props = CustomDateRangeFilterModal

  private final case class State(
    from: Option[LocalDate] = None,
    to: Option[LocalDate] = None
  )

  private case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      React.Fragment(
        ModalBody()(
          <.div(
            tw.flex.spaceX8,
            <.div(
              tw.flex.flexCol.spaceY4,
              <.div(tw.fontSemiBold, "From"),
              DatePickerR(
                date = state.from,
                maxDate = LocalDate.now,
                onSelectDate = dateOpt =>
                  scope.modState(
                    _.copy(
                      from = dateOpt,
                      to = if (state.to.nonEmpty) { state.to }
                      else { dateOpt }
                    )
                  )
              )()
            ),
            <.div(
              tw.flex.flexCol.spaceY4,
              <.div(tw.fontSemiBold, "To"),
              state.from.fold(
                <.div(
                  ^.key := "disable",
                  DatePickerR(isDisabled = true)()
                )
              ) { from =>
                <.div(
                  ^.key := state.from.toString,
                  DatePickerR(
                    date = state.to,
                    minDate = from,
                    maxDate = if (from.plusDays(90).toEpochDay > LocalDate.now().toEpochDay) {
                      LocalDate.now()
                    } else {
                      from.plusDays(90)
                    },
                    onSelectDate = dateOpt => scope.modState(_.copy(to = dateOpt)),
                    showTodayButton = true
                  )()
                )
              }
            )
          )
        ),
        ModalFooterWCancel(cancel = props.onClose)(
          Button(
            style = Button.Style.Full(color = Button.Color.Primary),
            onClick = {
              val cbOpt =
                for {
                  from <- state.from
                  to <- state.to
                } yield props.onApply(
                  from
                    .atStartOfDay()
                    .atZone(DateTimeUtils.defaultTimezone)
                    .toInstant(),
                  to.plusDays(1)
                    .atStartOfDay()
                    .atZone(DateTimeUtils.defaultTimezone)
                    .minus(1, ChronoUnit.MILLIS) // so that we still in the original date
                    .toInstant()
                ) >> props.onClose
              cbOpt.getOrElse(Callback.empty)
            },
            isDisabled = state.to.isEmpty || state.from.isEmpty
          )("Apply")
        )
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps(props =>
      State(
        from = props.from.map(time => time.atZone(DateTimeUtils.defaultTimezone).toLocalDate),
        to = props.to.map(time => time.atZone(DateTimeUtils.defaultTimezone).toLocalDate)
      )
    )
    .renderBackend[Backend]
    .build

}
