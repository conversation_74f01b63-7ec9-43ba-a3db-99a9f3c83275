// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.homepage

import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[homepage] final case class ArrowStep() {
  def apply(): VdomNode = ArrowStep.component()
}

private[homepage] object ArrowStep {

  private def render = {
    <.div(
      tw.my8.textGray5,
      tw.sm(tw.pt32),
      <.div(
        tw.flex.justifyCenter,
        tw.sm(tw.hidden),
        IconR(name = Icon.Glyph.ArrowDown, size = Icon.Size.Px32)()
      ),
      <.div(
        tw.hidden,
        tw.sm(tw.block),
        IconR(name = Icon.Glyph.ArrowRight, size = Icon.Size.Px32)()
      )
    )
  }

  private val component = ScalaComponent.builder
    .static(getClass.getSimpleName)(render)
    .build

}
