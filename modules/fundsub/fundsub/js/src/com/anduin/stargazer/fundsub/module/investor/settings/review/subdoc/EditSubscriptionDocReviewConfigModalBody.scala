// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.review.subdoc

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.modal.{ModalBody, ModalFooterWCancel}
import design.anduin.components.radio.Radio
import design.anduin.components.toast.Toast
import design.anduin.facades.reactbeautifuldnd.{DragDropContext, Draggable, Droppable}
import design.anduin.facades.util.ScalaJSUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.endpoint.subscriptiondoc.review.*
import anduin.id.entity.EntityId
import anduin.id.fundsub.FundSubId
import anduin.id.fundsub.group.FundSubInvestorGroupId
import anduin.id.review.ReviewStepConfigId
import anduin.protobuf.fundsub.FeatureSwitch
import anduin.review.UpdateReviewStepConfig
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubSubscriptionDocReviewEndpointClient
import com.anduin.stargazer.fundsub.module.investor.settings.review.model.AdminAndInvestorGroupInfo
import com.anduin.stargazer.fundsub.module.investor.settings.review.subdoc.SubscriptionDocConfigSummaryCardView.renderMissingGroupReviewer
import com.anduin.stargazer.fundsub.module.investor.settings.review.widget.InvestorGroupName
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils

private[settings] final case class EditSubscriptionDocReviewConfigModalBody(
  fundId: FundSubId,
  entityId: EntityId,
  config: SaveSubscriptionDocReviewConfigParams,
  adminAndInvestorGroupInfo: AdminAndInvestorGroupInfo,
  onSave: FundSubSubscriptionDocReviewConfig => Callback,
  onClose: Callback,
  isCreate: Boolean,
  featureSwitch: FeatureSwitch
) {
  def apply(): VdomElement = EditSubscriptionDocReviewConfigModalBody.component(this)

  def initEmptyStepIfNecessary(config: SaveSubscriptionDocReviewConfigParams): SaveSubscriptionDocReviewConfigParams = {
    config.mode match {
      case FundSubSubscriptionDocReviewConfigMode.Disabled => config // this case should not happens in this modal
      case FundSubSubscriptionDocReviewConfigMode.EnableSingleConfig =>
        config.copy(
          allInvestorGroupReviewConfig = if (config.allInvestorGroupReviewConfig.isEmpty) {
            Seq(UpdateReviewStepConfig())
          } else {
            config.allInvestorGroupReviewConfig
          }
        )
      case FundSubSubscriptionDocReviewConfigMode.EnableMultipleConfig =>
        config.copy(
          investorGroupReviewConfigs = adminAndInvestorGroupInfo.investorGroups
            .map(_.id)
            .filterNot(FundSubInvestorGroupId.PredefinedId.AllInvestorGroup.isMatch)
            .map { investorGroupId =>
              investorGroupId -> config.investorGroupReviewConfigs.getOrElse(investorGroupId, Seq(UpdateReviewStepConfig()))
            }
            .toMap,
          unassignedInvestorGroupReviewConfig = if (config.unassignedInvestorGroupReviewConfig.isEmpty) {
            Seq(UpdateReviewStepConfig())
          } else {
            config.unassignedInvestorGroupReviewConfig
          }
        )
    }
  }

  val isSignedDoc: Boolean = config.reviewType == FundSubSubscriptionDocReviewType.SignedSubscription
  val isUnsignedDoc: Boolean = config.reviewType == FundSubSubscriptionDocReviewType.UnsignedSubscription

}

private[settings] object EditSubscriptionDocReviewConfigModalBody {
  private type Props = EditSubscriptionDocReviewConfigModalBody

  private final case class State(
    config: SaveSubscriptionDocReviewConfigParams,
    investorsPendingReviewInStepMapping: Map[ReviewStepConfigId, Seq[InvestorPendingReviewInfo]] = Map.empty,
    isUpdating: Boolean = false
  ) {

    val usingSingleFlow: Boolean = config.mode == FundSubSubscriptionDocReviewConfigMode.EnableSingleConfig

  }

  private final case class Backend(scope: BackendScope[Props, State]) {

    def changeReviewMode(props: Props, mode: FundSubSubscriptionDocReviewConfigMode): Callback = {
      scope.modState { state =>
        state.copy(
          config = props.initEmptyStepIfNecessary(
            state.config.copy(
              mode = mode
            )
          )
        )
      }
    }

    private def renderSingleOrMultipleRadioBox(props: Props, state: State) = {
      <.div(
        tw.pb24.borderBottom.borderGray3.mb24,
        <.div("Create a review workflow for:"),
        if (state.config.mode != props.config.mode && !props.isCreate) {
          <.div(
            tw.mt16.py12.px16.bgGray2,
            s"Change in workflow type won't affect ${FundSubCopyUtils.getFlowTerm.standAloneTerm}s that have already been reviewed"
          )
        } else {
          EmptyVdom
        },
        <.div(
          tw.mt24.flex,
          <.div(
            tw.flexFill.borderAll.rounded6.px16.py12,
            if (state.usingSingleFlow) {
              TagMod(
                tw.borderPrimary3,
                ^.borderWidth := "3px"
              )
            } else {
              tw.borderGray3.border2
            },
            Radio(
              isChecked = state.usingSingleFlow,
              onChange = changeReviewMode(props, FundSubSubscriptionDocReviewConfigMode.EnableSingleConfig)
            )(
              <.div(
                <.div(
                  tw.fontSemiBold.leading20,
                  "All investors"
                ),
                <.div(
                  tw.text11,
                  "Create the same workflow for all investors"
                )
              )
            )
          ),
          <.div(
            tw.flexFill.borderAll.rounded6.ml16.px16.py12,
            if (!state.usingSingleFlow) {
              TagMod(
                tw.borderPrimary3,
                ^.borderWidth := "3px"
              )
            } else {
              tw.borderGray3.border2
            },
            Radio(
              isChecked = !state.usingSingleFlow,
              onChange = changeReviewMode(props, FundSubSubscriptionDocReviewConfigMode.EnableMultipleConfig)
            )(
              <.div(
                <.div(
                  tw.fontSemiBold.leading20,
                  "Investor groups"
                ),
                <.div(
                  tw.text11,
                  "Create a workflow for each investor group and unassigned investors"
                )
              )
            )
          )
        )
      )
    }

    private def renderInvestorGroupName(props: Props, groupIdOpt: Option[FundSubInvestorGroupId]) = {
      val groupOpt = props.adminAndInvestorGroupInfo.investorGroups.find { group =>
        groupIdOpt.contains(group.id)
      }

      val name = groupOpt.fold[String]("Unassigned investors")(_.name)
      val size = groupOpt.fold[Int](props.adminAndInvestorGroupInfo.unassignedInvestorsCount)(_.membersCount)

      InvestorGroupName(
        groupIdOpt,
        name,
        size
      )()
    }

    private def onDragEnd(
      result: DragDropContext.DropResult,
      groupConfig: Seq[UpdateReviewStepConfig],
      groupIdOpt: Option[FundSubInvestorGroupId]
    ) = {
      Callback.traverseOption(ScalaJSUtils.jsNullToOption(result.destination)) { dest =>
        val sourceIndex = result.source.index
        val sourceIssue = groupConfig(sourceIndex)
        val destIndex = dest.index
        updateGroupConfig(
          groupIdOpt = groupIdOpt,
          updateFnc = stepDetails =>
            stepDetails
              .patch(
                sourceIndex,
                Seq(),
                1
              )
              .patch(
                destIndex,
                Seq(sourceIssue),
                0
              )
        )
      }
    }

    private def draggableWrapper(renderer: VdomElement, index: Int, shouldWrap: Boolean) = {
      if (shouldWrap) {
        Draggable(
          draggableIdParam = s"$index",
          indexParam = index,
          childrenParam = (provided, _) => {
            <.div(
              VdomAttr("ref") := provided.innerRef,
              ScalaJSUtils.jsPropsToTagMod(provided.draggableProps),
              ScalaJSUtils.jsPropsToTagMod(provided.dragHandleProps),
              renderer
            ).rawElement
          }
        )
      } else {
        renderer
      }
    }

    private def renderGroupConfig(
      fundSubId: FundSubId,
      entityId: EntityId,
      reviewType: FundSubSubscriptionDocReviewType,
      adminAndInvestorGroupInfo: AdminAndInvestorGroupInfo,
      groupConfig: Seq[UpdateReviewStepConfig],
      groupIdOpt: Option[FundSubInvestorGroupId],
      state: State,
      props: Props
    ) = {
      <.div(
        tw.spaceY8,
        DragDropContext(
          onDragEndParam = dropResult =>
            onDragEnd(
              dropResult,
              groupConfig,
              groupIdOpt
            )
        )(
          Droppable(
            droppableIdParam = "list",
            childrenParam = (provided, _) => {
              val enableDragDrop = groupConfig.size > 1
              <.div(
                VdomAttr("ref") := provided.innerRef,
                ScalaJSUtils.jsPropsToTagMod(provided.droppableProps),
                tw.spaceY8,
                groupConfig.zipWithIndex.toVdomArray(
                  using { case (stepConfig, index) =>
                    <.div(
                      ^.key := s"$groupIdOpt $index",
                      draggableWrapper(
                        shouldWrap = enableDragDrop,
                        index = index,
                        renderer = SubscriptionStepConfigDetail(
                          fundSubId = fundSubId,
                          entityId = entityId,
                          reviewType = reviewType,
                          index = index,
                          adminAndInvestorGroupInfo = adminAndInvestorGroupInfo,
                          groupConfig = groupConfig,
                          groupIdOpt = groupIdOpt,
                          stepConfig = stepConfig,
                          investorsPendingReviewInStep =
                            stepConfig.stepIdOpt.fold[Seq[InvestorPendingReviewInfo]](Seq.empty) { stepId =>
                              state.investorsPendingReviewInStepMapping.getOrElse(stepId, Seq.empty)
                            },
                          onUpdateStep = (reviewers, description) => {
                            updateGroupConfig(
                              groupIdOpt = groupIdOpt,
                              updateFnc = stepDetails =>
                                updateGroupStepConfig(
                                  stepDetails = stepDetails,
                                  index = index,
                                  updateFnc = stepConfig =>
                                    stepConfig.copy(
                                      reviewers = reviewers,
                                      description = description
                                    )
                                )
                            )
                          },
                          onRemoveStep = updateGroupConfig(
                            groupIdOpt = groupIdOpt,
                            updateFnc = stepDetails => stepDetails.zipWithIndex.filterNot(_._2 == index).map(_._1)
                          ),
                          onGetInvestorsPendingReviewInStep = investorsPendingReviewInStep =>
                            stepConfig.stepIdOpt.fold(
                              Callback.empty
                            ) { stepId =>
                              scope.modState(state =>
                                state.copy(
                                  investorsPendingReviewInStepMapping =
                                    state.investorsPendingReviewInStepMapping + (stepId -> investorsPendingReviewInStep)
                                )
                              )
                            }
                        )()
                      )
                    )
                  }
                ),
                provided.placeholder
              ).rawElement
            }
          )
        ),
        if (props.featureSwitch.allowMultiStepInSubscriptionReview && groupConfig.size < 10) {
          Button(
            style = Button.Style.Minimal(color = Button.Color.Primary, icon = Option(Icon.Glyph.PlusCircle)),
            onClick = updateGroupConfig(
              groupIdOpt = groupIdOpt,
              updateFnc = stepDetails => stepDetails :+ UpdateReviewStepConfig()
            )
          )("Add another step")
        } else {
          EmptyVdom
        }
      )
    }

    private def renderGroupConfigDetail(
      props: Props,
      state: State,
      groupIdOpt: Option[FundSubInvestorGroupId],
      showBorder: Boolean = true
    ) = {
      val groupConfig = if (groupIdOpt.contains(props.adminAndInvestorGroupInfo.allInvestorGroupId)) {
        state.config.allInvestorGroupReviewConfig
      } else {
        groupIdOpt.fold[Seq[UpdateReviewStepConfig]](
          state.config.unassignedInvestorGroupReviewConfig
        ) { groupId =>
          state.config.investorGroupReviewConfigs.getOrElse(groupId, Seq.empty)
        }
      }

      val isMissingReviewerWhenStartEdit = groupIdOpt.fold(false) { groupId =>
        !props.config.investorGroupReviewConfigs.contains(groupId)
      } && !props.isCreate &&
        state.config.mode == FundSubSubscriptionDocReviewConfigMode.EnableMultipleConfig &&
        props.config.mode == FundSubSubscriptionDocReviewConfigMode.EnableMultipleConfig &&
        groupConfig.exists(_.reviewers.isEmpty)
      <.div(
        tw.pb24.pt16,
        TagMod.when(showBorder) {
          tw.borderBottom.borderGray3.pb24
        },
        if (!groupIdOpt.contains(FundSubInvestorGroupId.PredefinedId.AllInvestorGroup(props.fundId))) {
          <.div(
            tw.mb8,
            renderInvestorGroupName(props, groupIdOpt)
          )
        } else {
          EmptyVdom
        },
        if (isMissingReviewerWhenStartEdit) {
          <.div(
            tw.mb8,
            renderMissingGroupReviewer
          )
        } else {
          EmptyVdom
        },
        renderGroupConfig(
          state = state,
          fundSubId = props.fundId,
          entityId = props.entityId,
          reviewType = props.config.reviewType,
          adminAndInvestorGroupInfo = props.adminAndInvestorGroupInfo,
          groupConfig = groupConfig,
          groupIdOpt = groupIdOpt,
          props = props
        )
      )

    }

    private def updateGroupStepConfig(
      stepDetails: Seq[UpdateReviewStepConfig],
      index: Int,
      updateFnc: UpdateReviewStepConfig => UpdateReviewStepConfig
    ) = {
      stepDetails.zipWithIndex.map { case (step, currIndex) =>
        if (index == currIndex) {
          updateFnc(step)
        } else {
          step
        }
      }
    }

    private def updateGroupConfig(
      groupIdOpt: Option[FundSubInvestorGroupId],
      updateFnc: Seq[UpdateReviewStepConfig] => Seq[UpdateReviewStepConfig]
    ) = {
      for {
        state <- scope.state
        reviewConfig = state.config
        updatedConfig = groupIdOpt.fold[SaveSubscriptionDocReviewConfigParams] {
          reviewConfig.copy(
            unassignedInvestorGroupReviewConfig = updateFnc(reviewConfig.unassignedInvestorGroupReviewConfig)
          )
        } { groupId =>
          if (FundSubInvestorGroupId.PredefinedId.AllInvestorGroup.isMatch(groupId)) {
            reviewConfig.copy(
              allInvestorGroupReviewConfig = updateFnc(reviewConfig.allInvestorGroupReviewConfig)
            )
          } else {
            reviewConfig.copy(
              investorGroupReviewConfigs = reviewConfig.investorGroupReviewConfigs.map { case (currGroupId, config) =>
                val updatedConfig = if (currGroupId == groupId) {
                  updateFnc(config)
                } else {
                  config
                }
                currGroupId -> updatedConfig
              }
            )
          }
        }
        _ <- scope.modState(_.copy(config = updatedConfig))
      } yield ()
    }

    def render(props: Props, state: State): VdomElement = {
      val hasModified = (state.config.mode match {
        case FundSubSubscriptionDocReviewConfigMode.EnableSingleConfig =>
          state.config.allInvestorGroupReviewConfig != props.config.allInvestorGroupReviewConfig
        case FundSubSubscriptionDocReviewConfigMode.EnableMultipleConfig =>
          state.config.unassignedInvestorGroupReviewConfig != props.config.unassignedInvestorGroupReviewConfig ||
          state.config.investorGroupReviewConfigs != props.config.investorGroupReviewConfigs
        case _ => false
      }) || state.config.mode != props.config.mode

      val enableSave = (props.isCreate || hasModified) &&
        props.adminAndInvestorGroupInfo.isValidSubscriptionDocReviewConfig(state.config, !props.isCreate)

      val isModifiedReviewers =
        !props.isCreate &&
          props.config.mode == state.config.mode &&
          (state.config.mode match {
            case FundSubSubscriptionDocReviewConfigMode.EnableSingleConfig =>
              state.config.allInvestorGroupReviewConfig.map(_.reviewers) !=
                props.config.allInvestorGroupReviewConfig.map(_.reviewers)
            case FundSubSubscriptionDocReviewConfigMode.EnableMultipleConfig =>
              state.config.unassignedInvestorGroupReviewConfig.map(_.reviewers) !=
                props.config.unassignedInvestorGroupReviewConfig.map(_.reviewers) ||
                state.config.investorGroupReviewConfigs.flatMap(_._2.map(_.reviewers)).toSeq !=
                props.config.investorGroupReviewConfigs.flatMap(_._2.map(_.reviewers)).toSeq
            case _ => false
          })
      React.Fragment(
        ModalBody()(
          <.div(
            tw.overflowYScroll,
            ^.maxHeight := "504px",
            if (props.adminAndInvestorGroupInfo.investorGroups.size > 1) {
              renderSingleOrMultipleRadioBox(props, state)
            } else {
              EmptyVdom
            },
            <.div(
              if (props.featureSwitch.allowMultiStepInSubscriptionReview) {
                "Assign reviewers for single or multiple review steps. " +
                  "One reviewer in a step must approve documents before notification is sent to reviewers in the next step."
              } else {
                "Assign members to review investor documents. They'll be notified by email when the documents are ready for review."
              }
            ),
            if (isModifiedReviewers) {
              <.div(
                tw.mt16.py12.px16.bgGray2,
                s"Change in reviewer assignment won't affect ${FundSubCopyUtils.getFlowTerm.standAloneTerm}s that have already been reviewed"
              )
            } else {
              EmptyVdom
            },
            if (state.usingSingleFlow) {
              renderGroupConfigDetail(
                props,
                state,
                Option(FundSubInvestorGroupId.PredefinedId.AllInvestorGroup(props.fundId)),
                showBorder = false
              )
            } else {
              val groupIds = props.adminAndInvestorGroupInfo.investorGroups
                .sortBy(_.name)
                .map(_.id)
                .filterNot(_ == props.adminAndInvestorGroupInfo.allInvestorGroupId)
              <.div(
                groupIds.toVdomArray(
                  using { groupId =>
                    renderGroupConfigDetail(
                      props,
                      state,
                      Option(groupId)
                    )
                  }
                ),
                renderGroupConfigDetail(
                  props,
                  state,
                  None,
                  showBorder = false
                )
              )
            }
          )
        ),
        ModalFooterWCancel(props.onClose)(
          Button(
            style = Button.Style.Full(
              color = Button.Color.Primary,
              isBusy = state.isUpdating
            ),
            isDisabled = !enableSave,
            onClick = onSave
          )(if (props.isCreate) "Create review workflow" else "Save")
        )
      )
    }

    private def onSave = {
      val cb = for {
        props <- scope.props
        state <- scope.state
        _ <- ZIOUtils.toReactCallback {
          FundSubSubscriptionDocReviewEndpointClient
            .saveFundSubSubscriptionDocReviewConfig(state.config.mode match {
              case FundSubSubscriptionDocReviewConfigMode.Disabled =>
                state.config // this case should not happens in this modal
              case FundSubSubscriptionDocReviewConfigMode.EnableSingleConfig =>
                state.config.copy(
                  investorGroupReviewConfigs = props.config.investorGroupReviewConfigs,
                  unassignedInvestorGroupReviewConfig = props.config.unassignedInvestorGroupReviewConfig
                )
              case FundSubSubscriptionDocReviewConfigMode.EnableMultipleConfig =>
                state.config.copy(
                  allInvestorGroupReviewConfig = props.config.allInvestorGroupReviewConfig
                )
            })
            .map(
              _.fold(
                ex =>
                  scope.modState(
                    _.copy(
                      isUpdating = false
                    ),
                    Toast.errorCallback(s"Failed to save the settings: ${ex.message}")
                  ),
                response =>
                  scope.modState(
                    _.copy(
                      isUpdating = false
                    ),
                    props.onSave(response) >> Toast
                      .successCallback(
                        if (props.isCreate) {
                          if (props.isSignedDoc) {
                            s"Signed ${FundSubCopyUtils.getFlowTerm.standAloneTerm} review enabled"
                          } else { s"Unsigned ${FundSubCopyUtils.getFlowTerm.standAloneTerm} review enabled" }
                        } else {
                          if (props.isSignedDoc) {
                            s"Signed ${FundSubCopyUtils.getFlowTerm.standAloneTerm} review settings saved"
                          } else { s"Unsigned ${FundSubCopyUtils.getFlowTerm.standAloneTerm} review settings saved" }
                        }
                      )
                  )
              )
            )
        }
      } yield ()
      scope.modState(_.copy(isUpdating = true), cb)
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps { props =>
      State(
        config = props.initEmptyStepIfNecessary(props.config)
      )
    }
    .renderBackend[Backend]
    .build

}
