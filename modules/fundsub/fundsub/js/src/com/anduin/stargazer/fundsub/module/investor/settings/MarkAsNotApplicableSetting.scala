// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings

import design.anduin.components.toast.Toast

import anduin.fundsub.endpoint.lp.UpdateMarkAsNotApplicableParams
import anduin.id.fundsub.FundSubId
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import com.anduin.stargazer.client.utils.ZIOUtils

final case class MarkAsNotApplicableSetting(
  fundId: FundSubId,
  allowMarkSupportingDocAsNotApplicable: Boolean
) {
  def apply(): VdomElement = MarkAsNotApplicableSetting.component(this)
}

object MarkAsNotApplicableSetting {
  private type Props = MarkAsNotApplicableSetting

  private final case class State(
    isLoading: Boolean = false,
    optimisticUpdateValueOpt: Option[Boolean] = None
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      FundSubSettingSwitch(
        enabled = state.optimisticUpdateValueOpt.getOrElse(props.allowMarkSupportingDocAsNotApplicable),
        onChange = checked => Callback.unless(state.isLoading)(onChangeSetting(props.fundId)(checked)),
        isDisabled = state.isLoading,
        title = """Allow investors to mark additional documents as "Not applicable"""",
        description = () =>
          Option(
            <.div("Allow investors to skip the need to upload additionally requested documents.")
          )
      )()
    }

    private def onChangeSetting(fundId: FundSubId)(allowMarkSupportingDocAsNotApplicable: Boolean) = {
      val updateCallback = ZIOUtils.toReactCallback(
        FundSubEndpointClient
          .updateMarkAsNotApplicableSetting(
            UpdateMarkAsNotApplicableParams(
              fundId = fundId,
              disabledMarkAsNotApplicable = !allowMarkSupportingDocAsNotApplicable
            )
          )
          .map(
            _.fold(
              _ =>
                scope.modState(
                  _.copy(
                    isLoading = false,
                    optimisticUpdateValueOpt = None
                  ),
                  Toast.errorCallback("Failed to update setting")
                ),
              _ =>
                scope.modState(
                  _.copy(
                    isLoading = false
                  ),
                  Toast.successCallback("General settings updated successfully")
                )
            )
          )
      )

      scope.modState(
        _.copy(
          isLoading = true,
          optimisticUpdateValueOpt = Option(allowMarkSupportingDocAsNotApplicable)
        ),
        updateCallback
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
