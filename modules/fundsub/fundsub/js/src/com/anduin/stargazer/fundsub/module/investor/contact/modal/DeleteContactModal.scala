// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.contact.modal

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.modal.{<PERSON>dalBody, ModalFooter}
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import zio.Task

import anduin.contact.endpoint.ContactModel
import anduin.fundsub.endpoint.contact.{BatchDeleteContactAndGroupParams, DeleteContactParams}
import anduin.id.fundsub.{FundSubContactGroupId, FundSubId}
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

final case class DeleteContactModal(
  fundSubId: FundSubId,
  contactModel: ContactModel,
  parentGroupIdOpt: Option[FundSubContactGroupId],
  onClose: Callback,
  reFetch: Callback
) {

  def apply(): VdomElement = DeleteContactModal.component(this)
}

object DeleteContactModal {

  private type Props = DeleteContactModal

  private final case class State(
    busy: Boolean = false
  )

  private class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      React.Fragment(
        ModalBody()(
          <.div(
            "Are you sure you want to delete the contact ",
            <.span(tw.fontSemiBold, s"${props.contactModel.contactInfo.fullName}"),
            "?"
          )
        ),
        ModalFooter()(renderButtons(props, state))
      )
    }

    private def renderButtons(props: Props, state: State): VdomNode = {
      <.div(
        tw.flex.itemsCenter,
        <.div(
          tw.flex.itemsCenter,
          <.div(tw.textWarning4.mr8, IconR(Icon.Glyph.Info)()),
          <.div("This action cannot be undone")
        ),
        <.div(tw.mlAuto.mr8, Button(onClick = props.onClose)("Cancel")),
        Button(
          style = Button.Style.Full(color = Button.Color.Danger, isBusy = state.busy),
          onClick = onDeleteContact(props)
        )("Delete")
      )
    }

    private def onDeleteContact(props: Props): Callback = {
      scope.modState(
        _.copy(busy = true),
        ZIOUtils.toReactCallback {
          val successCb = Toast.successCallback("Contact deleted") >> props.reFetch >> props.onClose
          val failureCb =
            scope.modState(_.copy(busy = false), Toast.errorCallback("Unable to delete contact, try again"))
          props.parentGroupIdOpt.fold[Task[Callback]](
            FundSubEndpointClient
              .deleteContact(DeleteContactParams(props.contactModel.contactId, props.fundSubId))
              .map(
                _.fold(
                  _ => failureCb,
                  _ => successCb
                )
              )
          ) { parentId =>
            FundSubEndpointClient
              .batchDeleteContactAndGroup(
                BatchDeleteContactAndGroupParams(
                  fundSubId = props.fundSubId,
                  contacts = Seq(props.contactModel.contactId -> Seq(parentId)),
                  groups = Seq()
                )
              )
              .map(
                _.fold(
                  _ => failureCb,
                  _ => successCb
                )
              )
          }
        }
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
