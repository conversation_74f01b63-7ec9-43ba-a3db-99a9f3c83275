package com.anduin.stargazer.fundsub.module.comment.laminar.share

import com.raquo.laminar.api.L.*
import design.anduin.components.badge.Badge
import design.anduin.components.badge.laminar.DotL
import design.anduin.components.text.laminar.TruncateL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*

import anduin.id.fundsub.FundSubLpId
import anduin.id.issuetracker.IssueId
import anduin.stargazer.service.formcomment.FormCommentCommons.Comment
import anduin.utils.DateTimeUtils

private[laminar] case class TwoLinesCommentSummary(
  fundSubLpId: FundSubLpId,
  issueId: IssueId,
  commentInfo: Comment,
  isNewCommentSignal: Signal[Boolean] = Val(false),
  totalNumberOfCommentsInThread: Int
) {

  private def renderCreatorName = {
    div(
      tw.inlineBlock,
      TruncateL(
        target = {
          div(
            ComponentUtils.testIdL(DualThreadCommentsCountSummary, "CreatorName"),
            tw.fontSemiBold.mr4.text13.leading20,
            commentInfo.creatorInfoOpt.map(_.getDisplayName).getOrElse("")
          )
        }
      )()
    )
  }

  private def renderLastUpdatedTime = {
    span(
      tw.text11.textGray7,
      // Date
      commentInfo.lastUpdatedAt
        .orElse(commentInfo.createdAt)
        .fold[Node](emptyNode) { updatedAt =>
          DateTimeUtils
            .formatInstant(updatedAt, DateTimeUtils.MonthDayTimeWithoutYearWithoutLeadingZeroFormatter)(
              using DateTimeUtils.defaultTimezone
            )
        }
    )
  }

  private def renderNewRepliesNotification = {
    child <-- isNewCommentSignal.map { isNew =>
      if (isNew) {
        div(
          tw.mlAuto,
          DotL(
            color = Badge.Color.Primary
          )().amend(ComponentUtils.testIdL(DualThreadCommentsCountSummary, "NewThreadDot"))
        )
      } else {
        // Number of replies
        if (totalNumberOfCommentsInThread > 1) {
          div(
            tw.textGray7.text11.fontSemiBold.leading16.ml16,
            totalNumberOfCommentsInThread.toString
          )
        } else {
          emptyNode
        }
      }
    }
  }

  def apply(): HtmlElement = {
    div(
      ComponentUtils.testIdL(DualThreadCommentsCountSummary),
      renderFirstLine,
      renderSecondLine
    )
  }

  private def renderFirstLine = {
    div(
      tw.flex.itemsCenter,
      div(
        tw.flex1.flex.itemsCenter,
        // Name
        renderCreatorName,
        renderLastUpdatedTime
      )
    )
  }

  private def renderSecondLine = {
    div(
      tw.flex.itemsCenter,
      div(
        ComponentUtils.testIdL(DualThreadCommentsCountSummary, "CommentContent"),
        tw.whitespacePreWrap.flex1.lineClamp2,
        CommentContent(
          content = commentInfo.content,
          lpIdSignal = Val(fundSubLpId),
          fundSubId = fundSubLpId.parent,
          threadId = issueId,
          commentId = commentInfo.commentId,
          createdAtOpt = commentInfo.createdAt,
          editedAtOpt = commentInfo.lastUpdatedAt,
          hasMentionSignal = Val(commentInfo.mentions.nonEmpty)
        )()
      ),
      // Notification of new reply
      renderNewRepliesNotification
    )
  }

}
