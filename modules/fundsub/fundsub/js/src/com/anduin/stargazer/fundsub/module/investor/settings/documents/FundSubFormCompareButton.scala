// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.documents

import anduin.id.fundsub.FundSubId
import com.raquo.laminar.api.L
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import org.scalajs.dom
import anduin.protobuf.fundsub.models.FundSubFormVersion
import com.anduin.stargazer.fundsub.module.version.FundSubFormDiffView

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import com.raquo.laminar.api.L.*

final case class FundSubFormCompareButton(
  fundSubId: FundSubId,
  versions: Seq[FundSubFormVersion]
) {
  def apply(): VdomElement = FundSubFormCompareButton.component(this)
}

object FundSubFormCompareButton {

  private type Props = FundSubFormCompareButton

  private case class Backend(scope: BackendScope[Props, Unit]) {

    private val ref = Ref[dom.html.Div]
    private var rootNode: L.RootNode = scala.compiletime.uninitialized // scalafix:ok DisableSyntax.var

    def render(): VdomNode = {
      <.div.withRef(ref)
    }

    // Mount a Laminar node
    def mount: Callback = {
      for {
        props <- scope.props
        _ <- ref.foreach { ele =>
          rootNode = L.render(
            ele,
            div(
              ModalL(
                renderTarget = open =>
                  TooltipL(
                    renderTarget = ButtonL(style = ButtonL.Style.Text(), onClick = open.contramap(_ => ()))(
                      div(
                        tw.flex.itemsCenter.textGray7,
                        IconL(Val(Icon.Glyph.Comparison), size = Icon.Size.Custom(12))(),
                        span(tw.ml4, "Compare versions")
                      )
                    ),
                    renderContent = _.amend("Compare smart form versions")
                  )(),
                renderContent = closeModal =>
                  FundSubFormDiffView(
                    props.fundSubId,
                    props.versions,
                    closeModal
                  )(),
                size = ModalL.Size(ModalL.Width.Full, ModalL.Height.Full)
              )()
            )
          )
        }
      } yield ()
    }

    def unmount: Callback = Callback { rootNode.unmount() }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .renderBackend[Backend]
    .componentDidMount(_.backend.mount)
    .componentWillUnmount(_.backend.unmount)
    .build

}
