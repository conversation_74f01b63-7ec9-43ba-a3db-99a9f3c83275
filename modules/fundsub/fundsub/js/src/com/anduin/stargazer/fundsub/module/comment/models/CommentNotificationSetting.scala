// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.models

import anduin.id.entity.EntityId
import anduin.id.fundsub.FundSubLpId

private[fundsub] case class CommentNotificationSetting(
  formCommentDigestEmailExceptionLps: Seq[FundSubLpId],
  investorEntity: EntityId,
  suppressSendingFormCommentDigestEmail: Boolean
) {

  def notificationIsOffForLp(lpId: FundSubLpId): Boolean = {
    (suppressSendingFormCommentDigestEmail && !formCommentDigestEmailExceptionLps.contains(lpId)) ||
    (!suppressSendingFormCommentDigestEmail && formCommentDigestEmailExceptionLps.contains(lpId))
  }

}
