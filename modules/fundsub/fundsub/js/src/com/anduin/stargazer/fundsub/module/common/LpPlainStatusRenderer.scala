// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.common

import design.anduin.components.tag.Tag
import design.anduin.components.tag.react.TagR
import design.anduin.components.tooltip.react.TooltipR
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.status.LpStatusSharedUtils
import anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus
import com.anduin.stargazer.fundsub.module.status.LpStatusUtils

private[module] final case class LpPlainStatusRenderer(
  status: LpStatus
) {

  def apply(): VdomElement = {
    val label = LpStatusSharedUtils.getStatusName(status)
    val tooltip = LpStatusUtils.lpStatusTooltip.getOrElse(status, "")
    val tagColor = LpStatusUtils.lpStatusTagColor.getOrElse(status, Tag.Light.Gray)
    TooltipR(
      renderTarget = TagR(color = tagColor, label = label)(),
      renderContent = _(tooltip)
    )()
  }

}
