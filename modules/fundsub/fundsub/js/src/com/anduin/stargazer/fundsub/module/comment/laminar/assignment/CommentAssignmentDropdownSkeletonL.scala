package com.anduin.stargazer.fundsub.module.comment.laminar.assignment

import com.raquo.laminar.api.L.HtmlElement
import design.anduin.components.progress.Skeleton
import design.anduin.components.progress.laminar.SkeletonL

case class CommentAssignmentDropdownSkeletonL() {

  def apply(): HtmlElement = {
    SkeletonL(
      height = "32px",
      width = "180px",
      shape = Skeleton.Shape.Rectangle
    )()
  }

}
