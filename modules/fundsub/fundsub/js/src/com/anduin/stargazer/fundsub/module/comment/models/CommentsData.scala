// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.models

import anduin.id.fundsub.FundSubLpFormIdTrait
import anduin.id.issuetracker.IssueId
import anduin.id.notification.NotificationId
import anduin.protobuf.fundsub.comment.FormQuestionData
import anduin.stargazer.service.formcomment.FormCommentCommons.CommentThread

private[fundsub] final case class CommentsData(
  isLoading: Boolean = true,
  threads: List[CommentThread] = List.empty,
  newCommentIds: Map[String, List[NotificationId]] = Map.empty,
  newThreadIds: Map[IssueId, List[NotificationId]] = Map.empty,
  issueWithNewCommentIds: Map[IssueId, List[NotificationId]] = Map.empty,
  canMakePublicComment: Boolean = true
) {

  lazy val openThread: Int = threads.count(!_.isResolved)

  lazy val resolvedThread: Int = threads.count(_.isResolved)

  // Count the total number of unread open threads
  def countUnreadOpenThreads(): Int = {
    val matchedThreads = threads.filterNot(_.isResolved)
    newThreadIds.count { case (id, _) =>
      matchedThreads.exists(_.id == id)
    }
  }

  // Count the total number of unread resolved threads
  def countUnreadResolvedThreads(): Int = {
    val matchedThreads = threads.filter(_.isResolved)
    newThreadIds.count { case (id, _) =>
      matchedThreads.exists(_.id == id)
    }
  }

  private def getCommentCount(matchedThreads: List[CommentThread]) = {
    val commentIds = matchedThreads.flatMap(_.replies.map(_.commentId))

    val newThreads = newThreadIds.count { case (id, _) =>
      matchedThreads.exists(_.id == id)
    }
    val newComments = commentIds.count { id =>
      newCommentIds.exists(_._1 == id)
    }
    val isResolved = matchedThreads.forall(_.isResolved)
    DocCommentCount(
      idOpt = matchedThreads.headOption.map(_.id),
      hasComment = matchedThreads.nonEmpty,
      isResolved = isResolved,
      unreadComments = newThreads + newComments,
      totalComments = commentIds.size + matchedThreads.size
    )
  }

  // Queries for given doc type

  def getThreadIdOfDocument(doctype: String, isPublic: Boolean): Option[IssueId] = {
    threads
      .find(thread =>
        thread.isPublic == isPublic && thread.topicData.asMessage.sealedValue.amlKycDocData
          .exists(_.doctype.equalsIgnoreCase(doctype))
      )
      .map(_.id)
  }

  def getDocCommentCount(doctype: String): DocCommentCount = {
    val matchedThreads = threads
      .filter(_.topicData.asMessage.sealedValue.amlKycDocData.exists(_.doctype.equalsIgnoreCase(doctype)))
    getCommentCount(matchedThreads)
  }

  // Queries for form field

  def getThreadIdOfField(fieldAlias: String, isPublic: Boolean = true): Option[IssueId] = {
    threads
      .find(thread =>
        thread.isPublic == isPublic &&
          thread.topicData.asMessage.sealedValue.formQuestionData.exists(_.fieldAlias == fieldAlias)
      )
      .map(_.id)
  }

  def getCommentCountOfField(fieldAlias: String, isPublic: Boolean): DocCommentCount = {
    val matchedThreads = threads
      .filter { thread =>
        val formQuestionDataOpt = thread.topicData.asMessage.sealedValue.formQuestionData
        thread.isPublic == isPublic && formQuestionDataOpt.exists { data =>
          data.fieldAlias == fieldAlias
        }
      }
    getCommentCount(matchedThreads)
  }

  def getCommentCountOfDocument(doctype: String, isPublic: Boolean): DocCommentCount = {
    val matchedThreads = threads
      .filter { thread =>
        val amlKycDocData = thread.topicData.asMessage.sealedValue.amlKycDocData
        thread.isPublic == isPublic && amlKycDocData.exists { data =>
          data.doctype == doctype
        }
      }
    getCommentCount(matchedThreads)
  }

  private def filterOutDeprecatedId: CommentsData = {
    val threadIds = threads.map(_.id).toSet
    val filteredCommentIds = threads.flatMap(_.replies).map(_.commentId).toSet

    this.copy(
      threads = threads,
      newCommentIds = newCommentIds.filter { case (commentId, _) =>
        filteredCommentIds.contains(commentId)
      },
      newThreadIds = newThreadIds.filter { case (id, _) =>
        threadIds.contains(id)
      },
      issueWithNewCommentIds = issueWithNewCommentIds.filter { case (id, _) =>
        threadIds.contains(id)
      }
    )
  }

  def filterByCommentTarget(target: CommentTarget): CommentsData = {
    val filteredThreads = threads.filter { thread =>
      target match {
        case CommentTarget.Form           => thread.isFormComment
        case CommentTarget.AmlKycDocument => thread.isAmlKycComment
      }
    }

    this
      .copy(
        threads = filteredThreads
      )
      .filterOutDeprecatedId
  }

  def filterFormComments(formId: FundSubLpFormIdTrait): CommentsData = {
    val filteredThreads = threads.filter { thread =>
      thread.topicData match {
        case formField: FormQuestionData => formId.notificationId == formField.fundSubLpFormId.notificationId
        case _                           => false
      }
    }
    this
      .copy(
        threads = filteredThreads
      )
      .filterOutDeprecatedId
  }

  def filterPublicComments: CommentsData = {
    val filteredThreads = threads.filter(_.isPublic)

    this
      .copy(
        threads = filteredThreads
      )
      .filterOutDeprecatedId
  }

}
