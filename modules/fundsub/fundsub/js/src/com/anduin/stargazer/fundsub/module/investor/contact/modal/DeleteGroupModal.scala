// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.contact.modal

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.constants.Terms
import anduin.fundsub.endpoint.contact.DeleteContactGroupParams
import anduin.protobuf.fundsub.contact.FundSubContactGroup
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

final case class DeleteGroupModal(
  group: FundSubContactGroup,
  onClose: Callback,
  reFetch: Callback
) {

  def apply(): VdomElement = DeleteGroupModal.component(this)
}

object DeleteGroupModal {

  private type Props = DeleteGroupModal

  private final case class State(
    busy: Boolean = false
  )

  private class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      <.div(
        tw.p20.pt16,
        <.div(
          s"Are you sure you want to delete the ${Terms.InvesteeEntityLabelLowerCase}",
          <.span(tw.fontSemiBold, s"${props.group.name}"),
          "?"
        ),
        <.div("The entity's contacts won't be affected"),
        <.div(tw.mt20, renderButtons(props, state))
      )
    }

    private def renderButtons(props: Props, state: State): VdomNode = {
      <.div(
        tw.flex.itemsCenter,
        <.div(
          tw.flex.itemsCenter,
          <.div(tw.textWarning4.mr8, IconR(Icon.Glyph.Info)()),
          <.div("This action cannot be undone")
        ),
        <.div(tw.mlAuto.mr8, Button(onClick = props.onClose)("Cancel")),
        Button(
          style = Button.Style.Full(color = Button.Color.Danger, isBusy = state.busy),
          onClick = onDeleteGroup(props)
        )("Delete")
      )
    }

    private def onDeleteGroup(props: Props): Callback = {
      scope.modState(
        _.copy(busy = true),
        ZIOUtils.toReactCallback(
          FundSubEndpointClient
            .deleteContactGroup(DeleteContactGroupParams(props.group.id))
            .map(
              _.fold(
                _ =>
                  scope
                    .modState(
                      _.copy(busy = false),
                      Toast.errorCallback(s"Unable to delete ${Terms.InvesteeEntityLabel}, try again")
                    ),
                _ => Toast.successCallback(s"${Terms.InvesteeEntityLabel} deleted") >> props.reFetch >> props.onClose
              )
            )
        )
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
