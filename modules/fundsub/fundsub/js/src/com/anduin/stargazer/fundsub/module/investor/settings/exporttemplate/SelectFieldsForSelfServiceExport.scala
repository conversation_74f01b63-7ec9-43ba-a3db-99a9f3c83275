// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.exporttemplate

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration
import scala.scalajs.js

import com.raquo.airstream.core.Observer
import com.raquo.laminar.api.L.*
import com.raquo.laminar.nodes.ReactiveHtmlElement
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.collapse.Collapse
import design.anduin.components.collapse.laminar.CollapseL
import design.anduin.components.dropdown.laminar.DropdownL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.inline.laminar.InlineEditL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL, ModalL}
import design.anduin.components.portal.{PortalPosition, PortalUtils}
import design.anduin.components.progress.laminar.{BlockIndicatorL, CircleIndicatorL}
import design.anduin.components.tab.Tab
import design.anduin.components.tab.laminar.TabL
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.tour.laminar.TourTargetL
import design.anduin.components.viewer.laminar.{LayoutL, ThumbnailsL, ViewerL}
import design.anduin.facades.dom.Element
import design.anduin.facades.dom.animation.{KeyframeBuilder, KeyframeEffectOptions}
import design.anduin.style.tw.*
import zio.ZIO

import anduin.actionlogger.ActionEventLoggerJs
import anduin.dataexport.selfservice.SelfServiceExportTemplatePublicModels.FieldType
import anduin.dataexport.selfservice.SelfServiceExportTemplatePublicModels.FieldType.PdfFieldType
import anduin.forms.annotation.Area2D
import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.common.UpdateUserTrackingParams
import anduin.fundsub.endpoint.dashboard.v2.TypedDashboardColumn
import anduin.fundsub.endpoint.dataexport.*
import anduin.id.fundsub.{FundSubId, FundSubSelfServiceExportTemplateId}
import anduin.model.id.FileId
import anduin.utils.{ElementExtras, ScrollIntoViewOptions}
import com.anduin.stargazer.client.services.file.FileJsClient
import com.anduin.stargazer.fundsub.client.{FundSubDataExportEndpointClient, FundSubEndpointClient}
import com.anduin.stargazer.fundsub.module.investor.settings.exporttemplate.SelectFieldsForSelfServiceExport.{
  FileLoadingState,
  OpenTemplateMode,
  PageMountedData,
  SelectedPdfState
}
import com.anduin.stargazer.fundsub.module.model.FundSubClientModel.InvestmentFundClientModel
import com.anduin.stargazer.service.FileServiceEndpoints.GetPdfUrlParams

private[exporttemplate] case class SelectFieldsForSelfServiceExport(
  fundSubId: FundSubId,
  initialTemplateName: String,
  forApiExportSignal: Signal[Boolean],
  onCloseOuterModal: Observer[Unit],
  investmentFundsSignal: Signal[Seq[InvestmentFundClientModel]],
  onRefetchTemplates: Observer[Unit],
  openTemplateMode: SelectFieldsForSelfServiceExport.OpenTemplateMode,
  existingTemplateNamesSignal: Signal[Set[String]]
) {

  private val templateNameVar: Var[String] = Var(initialTemplateName)

  private val isFetchingDataForCustomExportVar: Var[Boolean] = Var(true)

  private val allFilesWithNameMapVar: Var[Map[FileId, String]] = Var(Map.empty)

  private val allFilesWithNameMapSignal: Signal[Map[FileId, String]] =
    allFilesWithNameMapVar.signal

  private val currentFileIdOptVar: Var[Option[SelectedPdfState]] = Var(None)

  private val currentFileInitialPageOptSignal: Signal[Option[Int]] =
    currentFileIdOptVar.signal.map(_.flatMap(_.initialPageIndexOpt)).distinct

  private val currentFileIdOptSignal: Signal[Option[FileId]] =
    currentFileIdOptVar.signal.map(_.map(_.selectedFileId)).distinct

  private val pdfAnnotationDataMapVar: Var[Map[FileId, GetAnnotationDataForSelfServiceExportResp]] = Var(Map.empty)

  private val filesStateVar = Var(Map.empty[FileId, FileLoadingState])

  private val filesStateSignal = filesStateVar.signal

  private val selectedTemplateFieldsSeqVar: Var[Seq[ExportTemplateFieldIdentifier]] = Var(
    Seq.empty
  ) // to preserve order

  private val selectedTemplateFieldsSeqSignal: Signal[Seq[ExportTemplateFieldIdentifier]] =
    selectedTemplateFieldsSeqVar.signal

  private val updatedFieldNameMapVar: Var[Map[ExportTemplateFieldIdentifier, String]] = Var(Map.empty)
  private val updatedFieldOptionNameMapVar: Var[Map[ExportTemplateFieldIdentifier, Map[String, String]]] = Var(Map.empty)

  private val previouslySelectedFieldInfosVar: Var[Seq[SelectedFieldOfSelfServiceExport]] =
    Var(Seq.empty)

  private val previouslySelectedFieldInfosSignal: Signal[Seq[SelectedFieldOfSelfServiceExport]] =
    previouslySelectedFieldInfosVar.signal

  private val selectedPdfFieldsSetSignal: Signal[Set[ExportTemplateFieldIdentifier.PdfField]] =
    selectedTemplateFieldsSeqSignal.map {
      _.collect { case pdfField: ExportTemplateFieldIdentifier.PdfField =>
        pdfField
      }.toSet
    }

  private val selectedColumnIdsSignal: Signal[Set[ExportTemplateFieldIdentifier.DashboardColumnField]] =
    selectedTemplateFieldsSeqSignal.signal.map {
      _.collect { case dashboardColumnId: ExportTemplateFieldIdentifier.DashboardColumnField =>
        dashboardColumnId
      }.toSet
    }

  private val currentFileAnnotationDataOptSignal: Signal[Option[GetAnnotationDataForSelfServiceExportResp]] =
    pdfAnnotationDataMapVar.signal
      .combineWith(currentFileIdOptSignal)
      .map { case (pdfAnnotationDataMap, currentFileIdOpt) =>
        currentFileIdOpt.flatMap(pdfAnnotationDataMap.get)
      }
      .distinct

  private val currentFileLoadingStateOptSignal: Signal[Option[FileLoadingState]] = currentFileIdOptSignal
    .combineWith(filesStateSignal)
    .map { case (currentFileOpt, filesState) =>
      currentFileOpt.flatMap { currentFile =>
        filesState.get(currentFile)
      }
    }
    .distinct

  private val fetchFileStateEventStream =
    currentFileIdOptSignal.withCurrentValueOf(currentFileLoadingStateOptSignal).flatMapSwitch {
      case (currentFileIdOpt, fileStateOpt) =>
        currentFileIdOpt.filter(_ => fileStateOpt.isEmpty).fold(EventStream.empty) { currentFile =>
          fetchFileUrlEventStream(currentFile)
        }
    }

  private val fetchFileAnnotationDataEventStream =
    currentFileIdOptSignal.signal.withCurrentValueOf(currentFileAnnotationDataOptSignal).flatMapSwitch {
      case (currentFileIdOpt, fileAnnotationDataOpt) =>
        currentFileIdOpt.filter(_ => fileAnnotationDataOpt.isEmpty).fold(EventStream.empty) { currentFile =>
          fetchPdfAnnotationData(currentFile)
        }
    }

  private val onToggleSelectField: Observer[ExportTemplateFieldIdentifier] = Observer { fieldId =>
    selectedTemplateFieldsSeqVar.update { selectedFields =>
      if (selectedFields.contains(fieldId)) {
        selectedFields.filterNot(_ == fieldId)
      } else {
        selectedFields.appended(fieldId)
      }
    }
  }

  private val onSelectFields: Observer[(Seq[ExportTemplateFieldIdentifier], Boolean)] = Observer {
    (fields, isSelected) =>
      selectedTemplateFieldsSeqVar.update { currentFields =>
        val currentFieldsSet = currentFields.toSet
        if (isSelected) {
          currentFields ++ fields.filterNot(currentFieldsSet.contains)
        } else {
          currentFields.filterNot(curItem => fields.contains(curItem))
        }
      }
  }

  private val fieldsGroupedByPageOfCurrentPdfOptSignal: Signal[
    Option[
      Map[Int, Seq[PdfFieldForSelfServiceExport]]
    ]
  ] = currentFileAnnotationDataOptSignal.map(
    _.map { annotationData =>
      annotationData.pdfFields
        .sortBy(_.firstAppearancePageIndex)
        .groupBy(_.firstAppearancePageIndex)
        .view
        .mapValues {
          _.sorted
        }
        .toMap
    }
  )

  private val dashboardColumnInfosVar: Var[Seq[TypedDashboardColumn]] = Var(Seq.empty)

  private val dashboardColumnInfosSignal: StrictSignal[Seq[TypedDashboardColumn]] = dashboardColumnInfosVar.signal

  private val allFieldInfosSignal: Signal[Map[ExportTemplateFieldIdentifier, SelectedFieldOfSelfServiceExport]] = {
    pdfAnnotationDataMapVar.signal
      .combineWith(
        updatedFieldNameMapVar.signal,
        updatedFieldOptionNameMapVar.signal,
        dashboardColumnInfosSignal,
        previouslySelectedFieldInfosSignal,
        forApiExportSignal
      )
      .map {
        case (
              annotationMap,
              updatedFieldNameMap,
              updatedFieldOptionNameMap,
              dashboardColumnInfos,
              previouslySelectedFieldInfos,
              forApiExport
            ) =>
          val pdfFields = annotationMap.values.flatMap(_.pdfFields)
          val previouslySelectedFieldInfosMap = previouslySelectedFieldInfos.map(field => field.fieldId -> field).toMap
          val pdfFieldInfosFromAnnotationsMap: Map[ExportTemplateFieldIdentifier, SelectedFieldOfSelfServiceExport] =
            pdfFields.map { field =>
              val fieldId = field.pdfFieldIdentifier
              fieldId -> SelectedFieldOfSelfServiceExport(
                fieldId = fieldId,
                displayNameOpt = Some(field.displayName),
                revisedName = if (forApiExport) fieldId.rawName else field.displayName,
                displayOptionNameMap = field.displayOptionNameMap,
                revisedOptionNameMap = if (forApiExport) {
                  field.displayOptionNameMap.map { case (rawName, _) =>
                    rawName -> rawName
                  }
                } else {
                  field.displayOptionNameMap
                },
                fieldType = field.fieldType,
                fieldPageLocationOpt = Some(
                  FieldPageLocationInfo(
                    fileId = field.fileId,
                    pageIndex = field.firstAppearancePageIndex
                  )
                )
              )
            }.toMap
          val dashboardColumnInfosMap: Map[ExportTemplateFieldIdentifier, SelectedFieldOfSelfServiceExport] =
            dashboardColumnInfos.map { dashboardColumn =>
              val fieldId = ExportTemplateFieldIdentifier.DashboardColumnField(dashboardColumn.id)
              fieldId -> SelectedFieldOfSelfServiceExport(
                fieldId = fieldId,
                fieldType = FieldType.DashboardField,
                revisedName = if (forApiExport) {
                  dashboardColumn.column.title.replaceAll("[^a-zA-Z0-9]+", "_").toLowerCase
                } else {
                  dashboardColumn.column.title
                },
                revisedOptionNameMap = Map.empty,
                fieldPageLocationOpt = None
              )
            }.toMap

          val allFieldInfosMap =
            dashboardColumnInfosMap ++ pdfFieldInfosFromAnnotationsMap ++ previouslySelectedFieldInfosMap
          allFieldInfosMap.view.mapValues { fieldInfo =>
            fieldInfo.copy(
              revisedName = updatedFieldNameMap.getOrElse(fieldInfo.fieldId, fieldInfo.revisedName),
              revisedOptionNameMap =
                fieldInfo.revisedOptionNameMap ++ updatedFieldOptionNameMap.getOrElse(fieldInfo.fieldId, Map.empty)
            )
          }.toMap
      }
  }

  private val allSelectedFieldInfosSignal: Signal[Seq[SelectedFieldOfSelfServiceExport]] =
    allFieldInfosSignal.signal.combineWith(selectedTemplateFieldsSeqSignal).map {
      case (fieldsGroupedByRawName, selectedTemplateFields) =>
        selectedTemplateFields.flatMap(fieldsGroupedByRawName.get)
    }

  private val jumpToPdfPageEventBus = new EventBus[SelectedPdfState]

  private val highlightedPdfFieldOptVar: Var[Option[ExportTemplateFieldIdentifier]] = Var(None)

  private val highlightAnimationDuration = FiniteDuration(3500, TimeUnit.MILLISECONDS).toMillis.toDouble

  private val userHasSeenGuideTourVar: Var[Boolean] = Var(true)

  private val showGuideTourVar: Var[Boolean] = Var(false)

  private val mountedPageLayersVar: Var[Set[PageMountedData]] = Var(Set.empty)

  private val mountedPagesVar: Var[Set[PageMountedData]] = Var(Set.empty)

  private val firstPdfFieldOptSignal: Signal[Option[PdfFieldForSelfServiceExport]] =
    currentFileAnnotationDataOptSignal.signal.map(_.flatMap(_.pdfFields.minOption)).distinct

  private val updateUserTrackingAfterSeeingTourEventBus = new EventBus[Unit]

  private val isRestartingTourVar: Var[Boolean] = Var(false)

  private val activeTabIndexVar: Var[Int] = Var(0)

  private val onClickRestartOnboarding: Observer[Unit] = Observer { _ =>
    if (!isRestartingTourVar.now()) {
      Var.set(
        isRestartingTourVar -> true,
        userHasSeenGuideTourVar -> false,
        activeTabIndexVar -> 0
      )
    }
  }

  private val scrollingIntoFieldOptVar: Var[Option[ExportTemplateFieldIdentifier.PdfField]] = Var(None)

  private val isSavingTemplateVar: Var[Boolean] = Var(false)

  private val saveTemplateEventBus = new EventBus[Unit]

  private val fileIdsSignal: Signal[Seq[FileId]] = allFilesWithNameMapSignal.map(_.keys.toSeq)

  private val templateToEditOpt: Option[FundSubSelfServiceExportTemplateId] = openTemplateMode match {
    case OpenTemplateMode.Edit(templateId) => Some(templateId)
    case _                                 => None
  }

  private val closeModalConfirmationOpened: Var[Boolean] = Var(false)

  private val isTemplateEditedVar: Var[Boolean] = Var(false)

  private val onClickCloseModalAndShowConfirmation: Observer[Unit] = Observer { _ =>
    if (isTemplateEditedVar.now()) {
      closeModalConfirmationOpened.set(true)
    } else {
      onCloseOuterModal.onNext(())
    }
  }

  private val editTemplateNameEventStream: EventStream[Unit] = templateNameVar.signal.changes.mapToUnit

  private val editTemplateFieldsEventStream: EventStream[Unit] = EventStream
    .merge(
      updatedFieldNameMapVar.signal.changes.mapToUnit,
      updatedFieldOptionNameMapVar.signal.changes.mapToUnit,
      selectedTemplateFieldsSeqVar.signal.changes.mapToUnit
    )
    .withCurrentValueOf(isFetchingDataForCustomExportVar.signal)
    // Ignore changes before the fields are fetched to the frontend
    .filter(!_)
    .mapToUnit

  private val editTemplateEventStream: EventStream[Unit] = EventStream.merge(
    editTemplateNameEventStream,
    editTemplateFieldsEventStream
  )

  private val firstFieldMountedEventStream: EventStream[Unit] =
    firstPdfFieldOptSignal
      .combineWith(mountedPageLayersVar.signal)
      .map { case (firstFieldOpt, mountedPageLayers) =>
        firstFieldOpt.exists { field =>
          mountedPageLayers.contains(PageMountedData(field.firstAppearancePageIndex, field.fileId))
        }
      }
      .changes
      .filter(identity)
      .mapToUnit

  private val pageOfFirstFieldMountedEventStream: EventStream[PdfFieldForSelfServiceExport] =
    firstPdfFieldOptSignal
      .combineWith(mountedPagesVar.signal)
      .map { case (firstFieldOpt, mountedPages) =>
        firstFieldOpt.flatMap { field =>
          Option.when(
            mountedPages.contains(PageMountedData(field.firstAppearancePageIndex, field.fileId))
          )(field)
        }
      }
      .changes
      .collectSome

  private val componentMountEventBus = new EventBus[Unit]

  def apply(): HtmlElement = {
    div(
      tw.hPc100,
      tw.flex.flexCol,
      renderHeader,
      renderModalContent,
      renderFooter,
      SelfServiceExportTour(
        tourIsRunning = showGuideTourVar.signal.distinct,
        onUserSeeTheGuide = Observer { _ =>
          Var.set(
            showGuideTourVar -> false,
            userHasSeenGuideTourVar -> true
          )
          updateUserTrackingAfterSeeingTourEventBus.emit(())
        }
      )(),
      fetchDataToCreateSelfServiceExport --> Observer.empty,

      // Jump to the page of the first field when user selects a new pdf
      pageOfFirstFieldMountedEventStream
        .combineWith(currentFileIdOptSignal.changes)
        .withCurrentValueOf(currentFileIdOptVar.signal)
        .map { case (firstField, _, currentFileState) =>
          Option.when(
            currentFileState.exists { currentFile =>
              currentFile.selectedFileId == firstField.fileId && currentFile.scrollIntoFieldOpt.isEmpty && currentFile.initialPageIndexOpt.isEmpty
            }
          ) {
            SelectedPdfState(
              selectedFileId = firstField.fileId,
              initialPageIndexOpt = Some(firstField.firstAppearancePageIndex),
              scrollIntoFieldOpt = Some(firstField.pdfFieldIdentifier)
            )
          }
        }
        .collectSome --> jumpToPdfPageEventBus.writer,
      // When the page layer of the first field is mounted, show the tour to the user
      firstFieldMountedEventStream
        .combineWith(
          userHasSeenGuideTourVar.signal.changes
        )
        .filter(!_)
        .mapToUnit --> Observer[Unit] { _ =>
        Var.set(
          isRestartingTourVar -> false,
          showGuideTourVar -> true
        )
      },
      updateUserTrackingAfterSeeingTourEventBus.events.flatMapSwitch { _ =>
        AirStreamUtils.taskToStream(
          FundSubEndpointClient.updateUserTracking(
            UpdateUserTrackingParams(
              seenSelfServiceGuideTourOpt = Some(true)
            )
          )
        )
      } --> Observer.empty,
      jumpToPdfPageEventBus.events.map(_.scrollIntoFieldOpt) --> scrollingIntoFieldOptVar.writer,
      currentFileIdOptSignal.changes.mapToUnit --> Observer { _ =>
        Var.set(
          mountedPageLayersVar -> Set.empty,
          mountedPagesVar -> Set.empty
        )
      },
      editTemplateEventStream --> isTemplateEditedVar.writer.contramap(_ => true),
      saveTemplateEventBus.events
        .withCurrentValueOf(
          templateNameVar.signal,
          allSelectedFieldInfosSignal,
          fileIdsSignal,
          forApiExportSignal
        )
        .flatMapSwitch(onSaveTemplateAndClose) --> Observer.empty,
      windowEvents(_.onBeforeUnload).withCurrentValueOf(isTemplateEditedVar.signal).filter(_._2).map { (event, _) =>
        event.preventDefault()
        event.returnValue = "true"
      } --> Observer.empty,
      componentMountEventBus.events
        .withCurrentValueOf(forApiExportSignal)
        .flatMapSwitch(
          logActionEventPageViewed
        ) --> Observer.empty,
      onMountCallback { _ =>
        componentMountEventBus.emit(())
      }
    )
  }

  private def renderModalContent = {
    div(
      tw.flexFill,
      tw.borderBottom.borderTop.borderGray3,
      allFilesWithNameMapSignal.map {
        _.headOption.map { case (fileId, _) =>
          SelectedPdfState(selectedFileId = fileId, initialPageIndexOpt = None)
        }
      } --> currentFileIdOptVar
        .updater[Option[SelectedPdfState]] { (firstFileStateOpt, currentFileStateOpt) =>
          currentFileStateOpt.orElse(firstFileStateOpt)
        },
      child <-- isFetchingDataForCustomExportVar.signal.map {
        if (_) {
          BlockIndicatorL()()
        } else {
          div(
            tw.hPc100.flex,
            div(
              tw.flexFill,
              TabL(
                activeTabSignal = activeTabIndexVar.signal.map(Some(_)).distinct,
                lazyLoading = false,
                style = Tab.Style.Minimal(isFullHeight = true),
                panels = Seq[Tab.Panel](
                  renderSelectPdfFieldsPanel,
                  renderSelectDashboardDataPanel
                ),
                renderHeader = Some(renderProps =>
                  div(
                    renderProps.renderTitles.amend(paddingLeft.px := 28),
                    renderProps.renderHeaderBorder
                  )
                ),
                onClick = activeTabIndexVar.writer
              )()
            ),
            renderDraggableSelectedFields
          )
        }
      }
    )
  }

  private def renderHeader = {
    div(
      paddingLeft.px := 28,
      paddingRight.px := 28,
      tw.py20,
      div(
        tw.flex.itemsCenter,
        div(
          tw.text20.leading32.fontSemiBold,
          tw.textGray6,
          "Select fields for"
        ),
        div(
          tw.ml8,
          InlineEditL(
            valueSignal = templateNameVar.signal,
            onSave = templateNameVar.writer,
            renderViewMode = renderProps =>
              div(
                tw.flex.itemsCenter.spaceX8,
                div(
                  tw.text20.leading32.fontSemiBold,
                  child.text <-- renderProps.valueSignal
                ),
                renderProps.renderDefaultSwitcher("Edit")
              ),
            validate = valueSignal =>
              existingTemplateNamesSignal
                .combineWith(valueSignal)
                .map { case (existingTemplateNames, currentName) =>
                  existingTemplateNames.contains(currentName) -> currentName
                }
                .distinct
                .map { case (nameExisted, currentName) =>
                  if (nameExisted) {
                    InlineEditL.Validation.Invalid(
                      s""""$currentName" already exists. Please enter a different name."""
                    )
                  } else if (currentName.isEmpty) {
                    InlineEditL.Validation.Invalid(
                      "Please enter a name"
                    )
                  } else {
                    InlineEditL.Validation.Valid
                  }
                }
          )()
        )
      ),
      div(
        tw.mt16.flex.itemsCenter.spaceX4,
        div(
          "Select the fields you'd like to include in the custom export template." +
            " Double click a field in the list of selected fields to quickly rename it."
        ),
        ButtonL(
          style = ButtonL.Style.Text(color = ButtonL.Color.Primary, isBlock = true),
          onClick = onClickRestartOnboarding.contramap(_ => ())
        )("View onboarding"),
        child <-- isRestartingTourVar.signal.distinct.map {
          if (_) {
            div(
              tw.textPrimary4,
              CircleIndicatorL()()
            )
          } else {
            emptyNode
          }
        }
      )
    )
  }

  private def renderFooter = {
    div(
      tw.py24.flex.itemsCenter.justifyEnd,
      paddingLeft.px := 28,
      paddingRight.px := 28,
      tw.spaceX8,
      ButtonL(
        style = ButtonL.Style.Full(),
        onClick = onClickCloseModalAndShowConfirmation.contramap(_ => ())
      )("Cancel"),
      child <-- forApiExportSignal.distinct.splitBoolean(
        whenTrue = _ =>
          ButtonL(
            style = ButtonL.Style.Full(color = ButtonL.Color.Primary, isBusy = isSavingTemplateVar.signal),
            onClick = Observer(_ => saveTemplateEventBus.emit(())),
            isDisabled = selectedTemplateFieldsSeqSignal.map(_.isEmpty).distinct
          )(
            if (templateToEditOpt.isEmpty) {
              "Create template"
            } else {
              "Save template"
            }
          ),
        whenFalse = _ =>
          ModalL(
            renderTarget = open =>
              ButtonL(
                style = ButtonL.Style.Full(color = ButtonL.Color.Primary),
                onClick = open.contramap(_ => ()),
                isDisabled = selectedTemplateFieldsSeqSignal.map(_.isEmpty).distinct
              )("Next: Review and reorder"),
            renderContent = close =>
              ReviewAndReorderFieldsForSelfServiceExport(
                fundSubId = fundSubId,
                templateToEditOpt = templateToEditOpt,
                selectedFieldsSignal = allSelectedFieldInfosSignal,
                onReorderFields = selectedTemplateFieldsSeqVar.writer,
                onRemoveField = Observer { removedField =>
                  selectedTemplateFieldsSeqVar.update(_.filterNot(_ == removedField))
                },
                onRenameField = Observer { (fieldId, updatedName) =>
                  updatedFieldNameMapVar.update(_.updated(fieldId, updatedName))
                },
                onRenameFieldOptionNameMap = Observer { (fieldId, updatedFieldOptionNameMap) =>
                  updatedFieldOptionNameMapVar.update(_.updated(fieldId, updatedFieldOptionNameMap))
                },
                onBack = close,
                onCloseModal = onClickCloseModalAndShowConfirmation,
                onSaveTemplate = Observer(_ => saveTemplateEventBus.emit(())),
                isSavingTemplateSignal = isSavingTemplateVar.signal,
                fileIdsSignal = fileIdsSignal
              )(),
            size = ModalL.Size(width = ModalL.Width.Full, height = ModalL.Height.Full)
          )()
      ),
      renderCloseOuterModalConfirmation
    )
  }

  private def renderCloseOuterModalConfirmation = {
    ModalL(
      renderContent = _ =>
        div(
          ModalBodyL(
            div(
              "Do you want to save ",
              span(
                tw.fontSemiBold,
                text <-- templateNameVar.signal
              ),
              "? You can update it later"
            )
          ),
          ModalFooterWCancelL(
            cancel = onCloseOuterModal,
            cancelLabel = "Cancel anyway"
          )(
            ButtonL(
              style = ButtonL.Style.Full(color = ButtonL.Color.Primary, isBusy = isSavingTemplateVar.signal),
              onClick = Observer(_ => saveTemplateEventBus.emit(()))
            )("Save template")
          )
        ),
      renderTitle = _ => "Save template",
      isOpened = Some(closeModalConfirmationOpened.signal),
      afterUserClose = Observer(_ => closeModalConfirmationOpened.set(false)),
      isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false))
    )()
  }

  private def onSaveTemplateAndClose(
    templateName: String,
    selectedFields: Seq[SelectedFieldOfSelfServiceExport],
    fileIds: Seq[FileId],
    forApiExport: Boolean
  ): EventStream[Unit] = {
    val task = for {
      _ <- ZIO.succeed(isSavingTemplateVar.set(true))
      _ <- FundSubDataExportEndpointClient
        .saveSelfServiceExportTemplate(
          SaveSelfServiceExportTemplateParams(
            fundSubId = fundSubId,
            templateName = templateName,
            selectedFields = selectedFields,
            fileIds = fileIds,
            templateIdOpt = templateToEditOpt,
            forApiExport = forApiExport
          )
        )
        .map(
          _.fold(
            _ => {
              Toast.error("Failed to save template data, please try again!")
              isSavingTemplateVar.set(false)
            },
            _ => {
              templateToEditOpt.fold(
                Toast.success("Export template created")
              ) { _ =>
                Toast.success("Export template updated")
              }
              onCloseOuterModal.onNext(())
              onRefetchTemplates.onNext(())
            }
          )
        )
    } yield ()
    AirStreamUtils.taskToStream(task)
  }

  private def fetchPdfAnnotationData(fileId: FileId): EventStream[Unit] = {
    val task =
      FundSubDataExportEndpointClient
        .getAnnotationDataForSelfServiceExport(
          GetAnnotationDataForSelfServiceExportParams(
            fundSubId = fundSubId,
            fileId = fileId
          )
        )
        .map(
          _.fold(
            _ => Toast.error("Failed to fetch PDF data, please try again"),
            resp => pdfAnnotationDataMapVar.update(_.updated(fileId, resp))
          )
        )
    AirStreamUtils.taskToStream(task)
  }

  private def renderDraggableSelectedFields = {
    DraggableSelectedTemplateFields(
      selectedFieldsSignal = allSelectedFieldInfosSignal,
      forApiExportSignal = forApiExportSignal,
      onReorderFields = selectedTemplateFieldsSeqVar.writer,
      onRemoveField = Observer { removedField =>
        selectedTemplateFieldsSeqVar.update(_.filterNot(_ == removedField))
      },
      onRenameField = Observer { (fieldId, updatedName) =>
        updatedFieldNameMapVar.update(_.updated(fieldId, updatedName))
      },
      onRenameFieldOptionNameMap = Observer { (fieldId, updatedFieldOptionNameMap) =>
        updatedFieldOptionNameMapVar.update(_.updated(fieldId, updatedFieldOptionNameMap))
      },
      onJumpToPdfFieldLocation = Observer { selectedPdfState =>
        jumpToPdfPageEventBus.emit(selectedPdfState)
        highlightedPdfFieldOptVar.set(selectedPdfState.scrollIntoFieldOpt)
      },
      filesWithNameMapSignal = allFilesWithNameMapSignal,
      onViewDashboardFields = activeTabIndexVar.writer.contramap(_ => 1)
    )()
  }

  private def getFileName(fileId: FileId, allFilesWithNameMap: Map[FileId, String]): String = {
    allFilesWithNameMap.getOrElse(fileId, "Unknown file")
  }

  private val renderPdfErrorView = {
    div(
      tw.flexFill.textDanger4.flex.itemsCenter.justifyCenter,
      "File not found"
    )
  }

  private def renderSelectPdfFieldsPanel: Tab.Panel = Tab.Panel(
    title = div(
      tw.flex.itemsCenter.spaceX8,
      IconL(name = Val(Icon.Glyph.FilePdf))(),
      div("Document fields")
    ),
    renderContent = _ =>
      div(
        tw.hPc100.flex.flexCol,
        renderSelectFileDropdown,
        div(
          tw.flexFill.flex,
          renderPdfViewer,
          renderPdfFieldsCheckList
        ),
        fetchFileStateEventStream --> Observer.empty,
        fetchFileAnnotationDataEventStream --> Observer.empty,
        jumpToPdfPageEventBus.events --> activeTabIndexVar.writer.contramap(_ => 0),
        jumpToPdfPageEventBus.events --> currentFileIdOptVar.writer.contramapSome
      )
  )

  private def renderPdfViewer = {
    div(
      tw.flexFill,
      child <--
        currentFileLoadingStateOptSignal
          .withCurrentValueOf(currentFileInitialPageOptSignal)
          .distinct
          .map { case (fileStateOpt, initialPageOpt) =>
            fileStateOpt.fold(emptyNode) {
              case FileLoadingState.Loading =>
                BlockIndicatorL(isFullHeight = true)()
              case FileLoadingState.Error =>
                renderPdfErrorView
              case FileLoadingState.Loaded(url) =>
                ViewerL(
                  initialPage = initialPageOpt,
                  url = url,
                  renderPageLayer = renderPdfLayer,
                  body = body => {
                    LayoutL.FloatingDarkToolbarLayout.copy(
                      container = container => {
                        div(
                          tw.hPc100.overflowHidden.flex.flexCol,
                          tw.relative,
                          container
                        )
                      },
                      main = mainEle => {
                        div(
                          tw.flex.overflowHidden.flexFill,
                          CollapseL(
                            defaultIsOpened = true,
                            direction = Collapse.Direction.LeftToRight,
                            renderTarget = render => {
                              val icon = render.currentStatus match {
                                case Collapse.Status.Open  => Icon.Glyph.ChevronDoubleLeft
                                case Collapse.Status.Close => Icon.Glyph.FileText
                              }
                              TooltipL(
                                position = PortalPosition.RightCenter,
                                renderContent = _.amend(
                                  render.currentStatus match {
                                    case Collapse.Status.Open  => "Hide PDF\npage thumbnails"
                                    case Collapse.Status.Close => "View PDF\npage thumbnails"
                                  }
                                ),
                                renderTarget = {
                                  div(
                                    tw.flex.itemsCenter.justifyCenter.hPc100.wPx24,
                                    tw.cursorPointer.hover(tw.bgGray2),
                                    onClick --> render.onToggle.contramap(_ => ()),
                                    IconL(name = Val(icon))()
                                  )
                                }
                              )()
                            }
                          )(
                            div(
                              tw.hPc100.overflowYAuto,
                              width.px(140),
                              ThumbnailsL(
                                doc = body.doc,
                                onJumpToPage = body.jumpToPage
                              )()
                            )
                          ),
                          div(
                            tw.bgGray4.flexFill.overflowAuto,
                            mainEle,
                            // When the field is mounted, we can scroll smoothly into the field, therefore jumpToPage is not needed
                            jumpToPdfPageEventBus.events
                              .withCurrentValueOf(mountedPageLayersVar.signal)
                              .map { case (fileState, mountedPageLayers) =>
                                val fieldMounted = fileState.initialPageIndexOpt.exists { pageIndex =>
                                  mountedPageLayers
                                    .contains(PageMountedData(pageIndex = pageIndex, fileId = fileState.selectedFileId))
                                }
                                Option.when(!fieldMounted)(fileState.initialPageIndexOpt.map(_ + 1)).flatten
                              }
                              .collectSome --> body.jumpToPage
                          )
                        )
                      },
                      toolbar = renderToolbarProps =>
                        LayoutL.FloatingDarkToolbarLayout
                          .toolbar(renderToolbarProps)
                          .amend(
                            tw.z5
                          )
                    )
                  },
                  onPageMounted = Observer[ViewerL.PageMountedData] { pageMountedData =>
                    currentFileIdOptVar.now().foreach { currentFileState =>
                      mountedPagesVar
                        .update(
                          _ + PageMountedData(
                            pageIndex = pageMountedData.pageIndex,
                            fileId = currentFileState.selectedFileId
                          )
                        )
                    }
                  }
                )()
            }
          }
    )
  }

  private def renderPdfFieldsCheckList = {
    SelectPdfFieldsFromCheckList(
      currentFileIdOptSignal = currentFileIdOptSignal,
      isLoadingPdfFields = fieldsGroupedByPageOfCurrentPdfOptSignal.map(_.isEmpty).distinct,
      forApiExportSignal = forApiExportSignal,
      fieldsGroupedByPageSignal = fieldsGroupedByPageOfCurrentPdfOptSignal.map(_.getOrElse(Map.empty)),
      selectedFieldsSignal = selectedPdfFieldsSetSignal.signal,
      onSelectPdfFields = onSelectFields,
      onJumpToFieldLocation = jumpToPdfPageEventBus.writer,
      userHasSeenTourSignal = userHasSeenGuideTourVar.signal,
      onHighlightField = highlightedPdfFieldOptVar.someWriter
    )()
  }

  private def renderSelectFileDropdown = {
    div(
      paddingLeft.px := 28,
      paddingRight.px := 28,
      tw.hPx48.flex.itemsCenter.borderBottom.borderGray3,
      child <-- allFilesWithNameMapSignal.map { allFilesWithNameMap =>
        TourTargetL(
          target = SelfServiceExportTour.ChangePdfDocumentTourTarget,
          tourIds = List(SelfServiceExportTour.SelfServiceExportGuideTour)
        )(
          div(
            DropdownL[FileId](
              minWidth = 512,
              value = currentFileIdOptSignal,
              valueToString = getFileName(_, allFilesWithNameMap),
              onChange = currentFileIdOptVar.writer.contramap { fileId =>
                Some(
                  SelectedPdfState(
                    selectedFileId = fileId,
                    initialPageIndexOpt = None,
                    scrollIntoFieldOpt = None
                  )
                )
              },
              items = allFilesWithNameMap.keys.toSeq.map(DropdownL.Item[FileId](_)),
              content = DropdownL.Content(
                renderItemBody = Some(item => {
                  val fileName = getFileName(item.value, allFilesWithNameMap)
                  div(
                    tw.flexFill.flex.itemsCenter.px8.spaceX8,
                    IconL(name = Val(Icon.File.ByExtension(fileName)), size = Icon.Size.Px24)(),
                    div(tw.flexFill.truncate, fileName)
                  )
                })
              ),
              target = DropdownL.Target(
                renderValue = Some(fileId => {
                  val fileName = getFileName(fileId, allFilesWithNameMap)
                  div(
                    tw.flex.itemsCenter.spaceX4,
                    IconL(name = Val(Icon.File.ByExtension(fileName)), size = Icon.Size.Px24)(),
                    div(fileName)
                  )
                }),
                appearance = DropdownL.Appearance.Minimal(isFullWidth = true)
              )
            )()
          )
        )
      }
    )
  }

  private def renderPdfLayer(layer: ViewerL.PageLayerRenderer) = {
    div(
      tw.absolute.inset0,
      div(
        tw.wPc100.hPc100.relative,
        children <-- currentFileAnnotationDataOptSignal.combineWith(forApiExportSignal).map {
          case (currentFileAnnotationDataOpt, forApiExport) =>
            currentFileAnnotationDataOpt.fold(Seq.empty) { annotationData =>
              annotationData.pdfFields
                .flatMap(field => field.locations.map(field -> _))
                .filter { case (_, location) =>
                  location.pageIndex == layer.pageIndex
                }
                .map { case (pdfField, location) =>
                  val isTourTarget = annotationData.pdfFields.minOption
                    .exists(_.pdfFieldIdentifier == pdfField.pdfFieldIdentifier)
                  val fieldName = if (forApiExport) pdfField.rawName else pdfField.displayName
                  if (isTourTarget) {
                    TourTargetL(
                      target = SelfServiceExportTour.SelectFieldTourTarget,
                      tourIds = List(SelfServiceExportTour.SelfServiceExportGuideTour)
                    )(
                      renderPdfFieldAnnotationArea(
                        fieldId = pdfField.pdfFieldIdentifier,
                        fieldType = pdfField.fieldType,
                        fieldAnnotationArea = location.area,
                        layer = layer,
                        displayName = fieldName,
                        isFirstAnnotationArea = pdfField.locations.head == location
                      )
                    )
                  } else {
                    renderPdfFieldAnnotationArea(
                      fieldId = pdfField.pdfFieldIdentifier,
                      fieldType = pdfField.fieldType,
                      fieldAnnotationArea = location.area,
                      layer = layer,
                      displayName = fieldName,
                      isFirstAnnotationArea = pdfField.locations.head == location
                    )
                  }
                }
            }
        }
      ),
      onMountUnmountCallback[Div](
        _ =>
          currentFileIdOptVar.now().foreach { currentFileState =>
            mountedPageLayersVar.update(
              _ + PageMountedData(pageIndex = layer.pageIndex, fileId = currentFileState.selectedFileId)
            )
          },
        _ =>
          currentFileIdOptVar.now().foreach { currentFileState =>
            mountedPageLayersVar.update(
              _ - PageMountedData(pageIndex = layer.pageIndex, fileId = currentFileState.selectedFileId)
            )
          }
      )
    )
  }

  private def onHighlightPdfField: Observer[HtmlElement] = Observer { htmlElement =>
    val highlightedKeyFrame = new KeyframeBuilder()
      .withBackground("rgba(252, 222, 164, 0.9)")
      .withColor("rgb(50, 60, 77)")
      .withBorderColor("rgba(252, 222, 164, 0.9)")
      .getKeyframe()

    val emptyKeyFrame = new KeyframeBuilder().getKeyframe()

    htmlElement.ref
      .asInstanceOf[Element] // scalafix:ok DisableSyntax.asInstanceOf
      .animate(
        js.Array(
          highlightedKeyFrame,
          emptyKeyFrame
        ),
        KeyframeEffectOptions(
          durationParam = Option(highlightAnimationDuration)
        )
      )
    highlightedPdfFieldOptVar.set(None)
    ()
  }

  private def onScrollIntoField: Observer[HtmlElement] = Observer { htmlElement =>
    htmlElement.ref
      .asInstanceOf[ElementExtras] // scalafix:ok DisableSyntax.asInstanceOf
      .scrollIntoView(ScrollIntoViewOptions.SmoothCenter)
    scrollingIntoFieldOptVar.set(None)
  }

  private def renderPdfFieldAnnotationArea(
    fieldId: ExportTemplateFieldIdentifier.PdfField,
    fieldType: PdfFieldType,
    fieldAnnotationArea: Area2D,
    layer: ViewerL.PageLayerRenderer,
    displayName: String,
    isFirstAnnotationArea: Boolean
  ) = {
    val isSelectedSignal = selectedPdfFieldsSetSignal
      .map(_.contains(fieldId))
      .distinct
    div(
      tw.absolute.cursorPointer,
      tw.borderAll.border1,
      tw.flex.itemsCenter,
      // Radio group field needs a lower value of z-index compared to checkbox and text field
      // because the shape of the radio group is usually big and it may cover other smaller checkboxes or text fields
      isSelectedSignal.cls(
        tw.z4,
        fieldType match {
          case PdfFieldType.Text | PdfFieldType.Checkbox => tw.z3
          case PdfFieldType.RadioGroup                   => tw.z2
        }
      ),
      isSelectedSignal.cls(
        tw.bgPrimary1.bgOpacity90.borderPrimary4.textPrimary4,
        tw.bgGray3.bgOpacity90.borderGray6.textGray8.borderDashed
      ),
      getItemPositionInLayer(fieldAnnotationArea, layer),
      onClick --> onToggleSelectField.contramap(_ => fieldId),
      TooltipL(
        renderTarget = fieldType match {
          case PdfFieldType.Text                               => displayName
          case PdfFieldType.Checkbox | PdfFieldType.RadioGroup => ""
        },
        renderContent = _.amend(
          div(
            div(text <-- isSelectedSignal.map {
              if (_) {
                "Click to deselect"
              } else {
                "Click to select"
              }
            }),
            div(displayName)
          )
        )
      )().amend(
        tw.hPc100.wPc100.px4.truncate
      ),
      inContext { thisNode =>
        thisNode.amend(
          Option.when(isFirstAnnotationArea) {
            scrollingIntoFieldOptVar.signal.map(_.contains(fieldId)) --> Observer[Boolean] { isScrollingIntoField =>
              if (isScrollingIntoField) {
                onScrollIntoField.onNext(thisNode)
              }
            }
          },
          highlightedPdfFieldOptVar.signal.map(_.contains(fieldId)) --> Observer[Boolean] { isSelected =>
            if (isSelected) {
              onHighlightPdfField.onNext(thisNode)
            }
          }
        )

      }
    )
  }

  // Implementation of Math.clamp
  private def clamp(value: Int, max: Int, min: Int) = {
    Math.min(max, Math.max(value, min))
  }

  private def getItemPositionInLayer(area2D: Area2D, layer: ViewerL.PageLayerRenderer) = {
    val pageWidth = layer.width * layer.scale
    val pageHeight = layer.height * layer.scale
    val leftPadding = (pageWidth * area2D.left).toInt
    val topPadding = (pageHeight * area2D.top).toInt
    val itemWidth = (pageWidth * area2D.width).toInt
    val itemHeight = (pageHeight * area2D.height).toInt
    val maxSize = 13
    val minSize = 8
    val calculatedSize = (itemHeight / 1.4).toInt
    val size = clamp(calculatedSize, maxSize, minSize)
    Seq(
      top.px := topPadding,
      left.px := leftPadding,
      width.px := itemWidth,
      height.px := itemHeight,
      fontSize.px := size,
      lineHeight.px := size
    )
  }

  private def renderSelectDashboardDataPanel: Tab.Panel = Tab.Panel(
    title = TourTargetL(
      target = SelfServiceExportTour.OtherDataFieldsTourTarget,
      tourIds = List(SelfServiceExportTour.SelfServiceExportGuideTour)
    )(
      div(
        tw.flex.itemsCenter.spaceX8,
        IconL(name = Val(Icon.Glyph.ListBullet))(),
        div("Dashboard fields")
      )
    ),
    renderContent = _ =>
      SelectDashboardColumnsPanel(
        dashboardColumnInfosSignal = dashboardColumnInfosSignal,
        selectedColumnIdsSignal = selectedColumnIdsSignal,
        onSelectColumns = onSelectFields,
        investmentFundsSignal = investmentFundsSignal
      )().amend(
        dashboardColumnInfosSignal.flatMapSwitch(fetchDashboardColumnInfosIfNeeded) --> Observer.empty
      )
  )

  private def fetchFileUrlEventStream(fileId: FileId) = AirStreamUtils.taskToStream {
    for {
      _ <- ZIO.attempt {
        filesStateVar.update(_.updated(fileId, FileLoadingState.Loading))
      }
      _ <- FileJsClient
        .getPdfUrlCache(GetPdfUrlParams(fileId, None))
        .map(
          _.fold(
            err => filesStateVar.update(_.updated(fileId, FileLoadingState.Error)),
            resp => filesStateVar.update(_.updated(fileId, FileLoadingState.Loaded(resp.url)))
          )
        )
    } yield ()
  }

  private def fetchDataToCreateSelfServiceExport = AirStreamUtils.taskToStream {
    val templateIdToFetchDataOpt = openTemplateMode match {
      case OpenTemplateMode.Edit(templateId)          => Some(templateId)
      case OpenTemplateMode.Duplicate(fromTemplateId) => Some(fromTemplateId)
      case OpenTemplateMode.CreateNew                 => None
    }
    FundSubDataExportEndpointClient
      .getDataToCreateSelfServiceExport(GetDataOfSelfServiceExportParams(fundSubId, templateIdToFetchDataOpt))
      .map(
        _.fold(
          _ => Toast.error("Failed to fetch subscription form for custom export, please try again"),
          resp => {
            Var.set(
              allFilesWithNameMapVar -> resp.formFiles.toMap,
              previouslySelectedFieldInfosVar -> resp.selectedFieldInfos,
              selectedTemplateFieldsSeqVar -> resp.selectedFieldInfos.map(_.fieldId),
              userHasSeenGuideTourVar -> resp.userHasSeenSelfServiceExportGuideTour
            )
            isFetchingDataForCustomExportVar.set(false)
          }
        )
      )
  }

  private def fetchDashboardColumnInfosIfNeeded(columnInfos: Seq[TypedDashboardColumn]) = {
    AirStreamUtils.taskToStream {
      ZIO.when(columnInfos.isEmpty) {
        FundSubDataExportEndpointClient
          .getDashboardColumnInfosForSelfServiceExport(fundSubId)
          .map(
            _.fold(
              _ => Toast.error("Failed to fetch dashboard fields, please try again"),
              resp => dashboardColumnInfosVar.set(resp.availableColumns)
            )
          )
      }
    }
  }

  private def logActionEventPageViewed(forApiExport: Boolean): EventStream[Unit] = {
    val subPage =
      if (forApiExport) {
        "Self-service export template builder for API"
      } else {
        "Self-service export template builder"
      }
    AirStreamUtils.taskToStream(
      ActionEventLoggerJs.logPageView(
        subPage = Some(subPage)
      )
    )
  }

}

private[exporttemplate] object SelectFieldsForSelfServiceExport {

  private enum FileLoadingState {
    case Error, Loading
    case Loaded(url: String)
  }

  case class SelectedPdfState(
    selectedFileId: FileId,
    initialPageIndexOpt: Option[Int], // 0-based index
    scrollIntoFieldOpt: Option[ExportTemplateFieldIdentifier.PdfField] = None
  )

  enum OpenTemplateMode {
    case CreateNew
    case Duplicate(fromTemplateId: FundSubSelfServiceExportTemplateId)
    case Edit(templateId: FundSubSelfServiceExportTemplateId)
  }

  private case class PageMountedData(
    pageIndex: Int,
    fileId: FileId
  )

}
