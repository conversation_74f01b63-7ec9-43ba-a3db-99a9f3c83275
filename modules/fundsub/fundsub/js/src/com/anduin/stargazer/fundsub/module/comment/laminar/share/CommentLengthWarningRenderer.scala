// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.share

import com.raquo.laminar.api.L.*
import design.anduin.components.portal.PortalPosition
import design.anduin.components.tooltip.laminar.TooltipL

import anduin.fundsub.utils.FormCommentUtils

private[laminar] final case class CommentLengthWarningRenderer(
  commentLengthSignal: Signal[Int],
  renderTarget: Node
) {

  def apply(): Node = {
    TooltipL(
      renderTarget = renderTarget,
      renderContent = _.amend(
        div("You've reached the character limit."),
        div(s"Please shorten your comment to ${FormCommentUtils.MaxCommentLength}"),
        div("characters or less to post")
      ),
      position = PortalPosition.BottomCenter,
      isDisabled = commentLengthSignal.map(_ <= FormCommentUtils.MaxCommentLength)
    )()
  }

}
