// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.ria

import com.raquo.laminar.api.L.*
import design.anduin.components.toast.Toast
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.ria.{GetRiaFundGroupParams, RiaFundGroup}
import anduin.id.fundsub.ria.FundSubRiaGroupId
import com.anduin.stargazer.fundsub.client.FundSubRiaEndpointClient

private[ria] case class FetchAdvisorGroup(
  fundSubRiaGroupId: FundSubRiaGroupId
) {

  private val isFetchingVar = Var(false)
  private val isFetchingSignal = isFetchingVar.signal.distinct

  private val groupOptSignal = FetchAdvisorGroups.groupsSignal.map(_.find(_.id == fundSubRiaGroupId)).distinct
  private val refetchGroupEvent = new EventBus[Unit]

  def apply(renderChildren: FetchAdvisorGroup.GroupRenderProps => HtmlElement): HtmlElement = {
    div(
      child <-- groupOptSignal.splitOption(
        project = (_, groupSignal) => {
          renderChildren(
            FetchAdvisorGroup.GroupRenderProps(
              isFetchingSignal = isFetchingSignal,
              groupSignal = groupSignal,
              onRefetch = refetchGroupEvent.writer
            )
          ).amend(
            refetchGroupEvent.events.flatMapSwitch { _ =>
              getAdvisorGroup(fundSubRiaGroupId)
            } --> Observer.empty,
            onMountCallback { _ =>
              refetchGroupEvent.emit(())
            }
          )
        },
        ifEmpty = emptyNode
      )
    )
  }

  private def getAdvisorGroup(
    fundSubRiaGroupId: FundSubRiaGroupId
  ): EventStream[Unit] = {
    AirStreamUtils.taskToStream(
      for {
        _ <- ZIO.succeed(isFetchingVar.set(true))
        _ <- FundSubRiaEndpointClient
          .getRiaFundGroup(
            GetRiaFundGroupParams(
              getBy = Left(fundSubRiaGroupId)
            )
          )
          .map(
            _.fold(
              error => {
                Toast.error("Failed to fetch advisor entity")
                isFetchingVar.set(false)
              },
              _.fundRiaGroups.headOption.foreach { toUpdateFundGroup =>
                FetchAdvisorGroups.groupsVar.update(_.map { group =>
                  if (group.id == fundSubRiaGroupId) {
                    toUpdateFundGroup
                  } else {
                    group
                  }
                })
                isFetchingVar.set(false)

              }
            )
          )
      } yield ()
    )
  }

}

private[ria] object FetchAdvisorGroup {

  final case class GroupRenderProps(
    isFetchingSignal: Signal[Boolean],
    groupSignal: Signal[RiaFundGroup],
    onRefetch: Observer[Unit]
  )

}
