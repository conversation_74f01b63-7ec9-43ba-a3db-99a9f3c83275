// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.notify

import com.raquo.laminar.api.L.*

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.admin.{GetLpParticipantInfosParams, LpFilter}
import anduin.fundsub.model.FundSubSharedClientModel.ParticipantInfo
import anduin.id.fundsub.FundSubLpId
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

private[notify] case class FetchRecipients(
  fundSubLpId: FundSubLpId,
  renderChildren: Option[Seq[ParticipantInfo]] => HtmlElement
) {

  private val fetchRecipientsEventBus = new EventBus[Option[FundSubLpId]]
  private val recipientsVar = Var(Option.empty[Seq[ParticipantInfo]])

  private def fetchRecipients(targetLpIdOpt: Option[FundSubLpId]) = {
    targetLpIdOpt
      .map { targetLpId =>
        AirStreamUtils.taskToStreamDEPRECATED {
          FundSubEndpointClient
            .getLpParticipantInfos(
              GetLpParticipantInfosParams(fundSubId = targetLpId.parent, lpFilter = LpFilter())
            )
            .map {
              _.fold(
                _ => Seq.empty,
                _.infos
                  .find(_._1 == targetLpId)
                  .map(lpInfo => lpInfo._2.collaboratorInfo :+ lpInfo._2.mainLpInfo)
                  .getOrElse(Seq.empty)
              )
            }
        }
      }
      .getOrElse(EventStream.empty)
  }

  def apply(): HtmlElement = {
    div(
      fetchRecipientsEventBus.events.flatMapSwitch(fetchRecipients) --> Observer[Seq[ParticipantInfo]] { data =>
        recipientsVar.set(Option(data))
      },
      child <-- recipientsVar.signal.map(renderChildren),
      onMountCallback { _ =>
        fetchRecipientsEventBus.emit(Option(fundSubLpId))
      }
    )
  }

}
