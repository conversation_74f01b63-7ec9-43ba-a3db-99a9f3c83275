package com.anduin.stargazer.fundsub.module.comment.laminar.drawer.lp

import com.raquo.laminar.api.L.*
import design.anduin.components.progress.Skeleton
import design.anduin.components.progress.laminar.SkeletonL
import design.anduin.style.tw.*

private[lp] case class LpThreadsListSkeletonL() {

  def apply(): HtmlElement = {
    div(
      (1 to 3).map { _ =>
        div(
          tw.pt12.mx16.pb16,
          tw.borderBottom.borderGray3,
          SkeletonL(
            shape = Skeleton.Shape.Circle,
            effect = Skeleton.Effect.Wave,
            height = "24px",
            width = "24px"
          )(),
          div(
            tw.mt8,
            SkeletonL(
              shape = Skeleton.Shape.Rounded,
              effect = Skeleton.Effect.Wave,
              height = "48px",
              width = "368px"
            )()
          ),
          div(
            tw.mt8.flex.spaceX8,
            SkeletonL(
              shape = Skeleton.Shape.Text(13),
              effect = Skeleton.Effect.Wave,
              height = "20px",
              width = "90px"
            )(),
            SkeletonL(
              shape = Skeleton.Shape.Text(11),
              effect = Skeleton.Effect.Wave,
              height = "20px",
              width = "90px"
            )()
          ),
          div(
            tw.flex.itemsCenter.spaceX16,
            SkeletonL(
              shape = Skeleton.Shape.Text(13),
              effect = Skeleton.Effect.Wave,
              height = "40px",
              width = "336px"
            )(),
            SkeletonL(
              shape = Skeleton.Shape.Text(11),
              effect = Skeleton.Effect.Wave,
              height = "16px",
              width = "16px"
            )()
          )
        )
      }
    )
  }

}
