package com.anduin.stargazer.fundsub.module.comment

import java.time.Instant
import java.time.format.DateTimeFormatter

import anduin.utils.DateTimeUtils
import com.anduin.stargazer.util.date.DateCalculator

object FundSubCommentUtils {

  /* If comment timestamp is within today
   * -> Show time in AM or PM only
   * If comment timestamp is not today
   * -> Show date only
   * If comment timestamp is not this year
   * -> Show date + year
   */
  def getCommentLastUpdateDisplayString(lastUpdate: Instant): String = {
    val zone = DateTimeUtils.getTimezone(None)
    val zonedLastUpdate = lastUpdate.atZone(zone)
    val now = DateCalculator.today(zone)
    val formatter =
      if (now.toLocalDate.isEqual(zonedLastUpdate.toLocalDate)) {
        DateTimeUtils.Time12hourFormatter
      } else if (now.getYear == zonedLastUpdate.getYear) {
        DateTimeFormatter.ofPattern("MMM d")
      } else {
        DateTimeUtils.DefaultDateFormatter
      }
    DateTimeUtils.formatInstant(
      lastUpdate,
      formatter
    )(
      using zone
    )
  }

}
