// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.notify

import com.raquo.laminar.api.L.*

import anduin.frontend.AirStreamUtils
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.stargazer.service.formcomment.FormCommentCommons.*
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.comment.models.CommentNotificationSetting

private[notify] case class FetchSharedAndOpenCommentsL(
  fundSubId: FundSubId,
  commentNotificationSettingSignal: Signal[Option[CommentNotificationSetting]],
  fundSubLpId: Option[FundSubLpId],
  preselectedLp: Option[FundSubLpId],
  getAllLpsHaveComment: Boolean = false,
  renderChildren: Option[FetchSharedAndOpenCommentsL.RenderChildren] => HtmlElement
) {

  case class EvenBusData(
    lpId: Option[FundSubLpId],
    commentNotificationSetting: Option[CommentNotificationSetting]
  )

  private val fetchCommentsEventBus = new EventBus[Option[EvenBusData]]
  private val resultVar = Var(Option.empty[FetchSharedAndOpenCommentsL.RenderChildren])

  private def fetchComments(data: Option[EvenBusData]) = {
    val lpIds = data.flatMap(_.lpId).toList
    val setting = data.flatMap(_.commentNotificationSetting)
    val formCommentDigestEmailException = setting.exists(_.suppressSendingFormCommentDigestEmail)
    val formCommentDigestEmailExceptionLps = setting.map(_.formCommentDigestEmailExceptionLps).getOrElse(Seq.empty)

    AirStreamUtils.taskToStreamDEPRECATED {
      FundSubEndpointClient
        .getFundComments(
          GetFundCommentParams(
            fundId = fundSubId,
            targetedLpIds = lpIds,
            getOnlyCommentsWithUnread = false,
            commentSortOrder = CommentSortOrder.LatestThreadsAndReplies,
            commentStatusOpt = Option(CommentStatus.OpenComment),
            commentTypeOpt = Option(CommentVisibilityType.PublicComment),
            commentTopicOpt = None,
            pageIndex = 1,
            getAllLpsHaveComment = getAllLpsHaveComment,
            itemsPerPage = 100
          )
        )
        .map {
          _.fold(
            _ => {
              Option(
                FetchSharedAndOpenCommentsL.RenderChildren(
                  lpsInfo = List.empty,
                  threads = List.empty
                )
              )
            },
            response => {
              val threads = response.threads.filter { thread =>
                lpIds.contains(thread.commentThread.fundSubLpId)
              }
              val allLpHasComments = response.allLpsMetaInfo.map(_.fundSubLpId)
              val lpUnsubscribedToEmails = if (formCommentDigestEmailException) {
                allLpHasComments.filter { lpId =>
                  !formCommentDigestEmailExceptionLps.contains(lpId)
                }
              } else {
                allLpHasComments.filter { lpId =>
                  formCommentDigestEmailExceptionLps.contains(lpId)
                }
              }
              val lpsInfo = response.allLpsMetaInfo.filter { lp =>
                preselectedLp.contains(lp.fundSubLpId) || lpUnsubscribedToEmails.contains(lp.fundSubLpId)
              }
              Option(
                FetchSharedAndOpenCommentsL.RenderChildren(
                  lpsInfo = lpsInfo,
                  threads = threads
                )
              )
            }
          )
        }
    }
  }

  def apply(): HtmlElement = {
    div(
      fetchCommentsEventBus.events.flatMapSwitch(fetchComments) --> resultVar.writer,
      child <-- resultVar.signal.map(renderChildren),
      commentNotificationSettingSignal --> Observer[Option[CommentNotificationSetting]] { setting =>
        fetchCommentsEventBus.emit(Option(EvenBusData(fundSubLpId, setting)))
      }
    )
  }

}

private[notify] object FetchSharedAndOpenCommentsL {

  case class RenderChildren(
    lpsInfo: List[FundSubLpMetaInfo],
    threads: List[CommentThreadWithMetaData]
  )

}
