// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.share

import java.time.Instant

import com.raquo.laminar.api.L.*
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.fundsub.comment.utils.CommentUtils
import anduin.fundsub.endpoint.lp.{GetFundMemberRoleParams, GetFundMemberRoleResponse}
import anduin.id.fundsub.FundSubLpId
import anduin.id.issuetracker.IssueId
import anduin.model.common.user.UserId
import anduin.model.common.user.UserInfo.fullNameString
import anduin.stargazer.service.formcomment.FormCommentCommons
import anduin.stargazer.service.formcomment.FormCommentCommons.{
  CommentThread,
  FlaggedCommentMetadata,
  MentionsData,
  ThreadActivityType
}
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.service.account.AccountUtils

private[laminar] case class CommentsListL(
  threadSignal: Signal[CommentThread],
  onCopyInternalComment: Observer[String] = Observer.empty,
  canMakePublicCommentSignal: Signal[Boolean],
  defaultOpenedCommentIdOpt: Option[String] = None,
  isCommentMentioningEnabledSignal: Signal[Boolean],
  localCommentsSignal: Signal[Seq[CommentsListL.ThreadActivityItem.Comment]] = Val(Seq.empty),
  onUpdateLocalComments: Observer[Seq[CommentsListL.ThreadActivityItem.Comment]] = Observer.empty,
  isPendingThreadSignal: Signal[Boolean] = Val(false),
  isFundSideViewSignal: Signal[Boolean],
  inactiveDaysThresholdSignal: Signal[Option[Int]],
  isDrawerViewSignal: Signal[Boolean]
) {

  private val fundMembersRoleAndInfoMapVar: Var[Map[UserId, GetFundMemberRoleResponse]] = Var(Map.empty)
  private val mountEventBus = new EventBus[Unit]
  private val participantsEventBus = new EventBus[Set[UserId]]
  private val deleteThreadCommentItemEventBus = new EventBus[String]

  private def threadActivityItemsSignal = threadSignal.map { thread =>
    val firstCommentAt: Option[Instant] = thread.createdAtOpt.fold(
      thread.replies.headOption.fold(Option.empty[Instant])(_.createdAtOpt)
    )(_ => thread.createdAtOpt)
    val rootComment = CommentsListL.ThreadActivityItem.Comment(
      threadId = thread.id,
      lpId = thread.fundSubLpId,
      comment = thread.comment,
      commentIdOpt = None,
      actorOpt = thread.creatorOpt,
      createdAtOpt = thread.createdAtOpt,
      isPublic = thread.isPublic,
      lastEditedAtOpt = thread.lastEditedAtOpt,
      itemId = thread.id.toString,
      flaggedCommentMetadata = thread.flaggedRootCommentMetadata,
      mentions = thread.rootCommentMentions
    )
    val replies = thread.replies.map { reply =>
      CommentsListL.ThreadActivityItem.Comment(
        threadId = thread.id,
        lpId = thread.fundSubLpId,
        comment = reply.content,
        commentIdOpt = Option(reply.commentId),
        actorOpt = reply.creatorOpt,
        createdAtOpt = reply.createdAtOpt,
        isPublic = thread.isPublic,
        lastEditedAtOpt = reply.lastEditedAt,
        itemId = reply.commentId,
        flaggedCommentMetadata = reply.flaggedCommentMetadata,
        mentions = reply.mentions
      )
    }
    val comments = (Seq(rootComment) ++ replies)
    val activities =
      thread.activities
        .filter(activity => List(activity.at, firstCommentAt).flatten.maxOption.equals(activity.at))
        .map { activity =>
          CommentsListL.ThreadActivityItem.Activity(
            threadId = thread.id,
            lpId = thread.fundSubLpId,
            actorOpt = activity.actorOpt,
            createdAtOpt = activity.at,
            isPublic = thread.isPublic,
            activityType = activity.activityType,
            itemId = activity.activityType.toString + activity.at.toString
          )
        }
    (comments ++ activities).sortBy(_.createdAtOpt)
  }

  private val fullThreadActivityItemsSignal = threadActivityItemsSignal
    .combineWith(localCommentsSignal, threadSignal.map(_.lastEditedAtOpt))
    .map { case (fetchedItemsList, localComments, threadLastEditedAtOpt) =>
      val lastTimeThreadUpdatedAtOpt = (fetchedItemsList.map {
        case comment: CommentsListL.ThreadActivityItem.Comment =>
          List(comment.createdAtOpt, comment.lastEditedAtOpt).flatten.maxOption
        case activity: CommentsListL.ThreadActivityItem.Activity => activity.createdAtOpt
      } :+ threadLastEditedAtOpt).flatten.maxOption

      // merge two lists but we keep itemId of local comments because this itemId is used in splitting
      val filteredFetchedItemsList = fetchedItemsList.map {
        case comment: CommentsListL.ThreadActivityItem.Comment =>
          localComments
            .find(localComment =>
              localComment.commentIdOpt.nonEmpty && localComment.commentIdOpt.equals(comment.commentIdOpt)
            )
            .fold(comment)(localComment => comment.copy(itemId = localComment.itemId, isDeleted = localComment.isDeleted))
        case activity: CommentsListL.ThreadActivityItem.Activity => activity
      }

      val filteredLocalCommentsList =
        localComments.filter(comment =>
          !comment.isDeleted && !filteredFetchedItemsList.exists(_.itemId.equals(comment.itemId)) && List(
            lastTimeThreadUpdatedAtOpt,
            comment.createdAtOpt
          ).flatten.maxOption == comment.createdAtOpt
        )

      val newItemsList =
        List.concat(filteredFetchedItemsList, filteredLocalCommentsList).sortBy(_.createdAtOpt).filterNot(_.isDeleted)

      if (newItemsList.nonEmpty) {
        val lastItem = newItemsList.last
        val modifiedLastItem = lastItem match {
          case comment: CommentsListL.ThreadActivityItem.Comment   => comment.copy(isLastItem = true)
          case activity: CommentsListL.ThreadActivityItem.Activity => activity.copy(isLastItem = true)
        }
        newItemsList.init :+ modifiedLastItem
      } else {
        newItemsList
      }
    }

  private def renderActivityItem(
    activitySignal: Signal[CommentsListL.ThreadActivityItem.Activity]
  ) = {
    val actorUserInfoOptSignal = activitySignal
      .combineWith(
        fundMembersRoleAndInfoMapVar.signal
      )
      .map { case (activity, fundMemberRoleMap) =>
        activity.actorOpt
          .flatMap { actor =>
            fundMemberRoleMap
              .get(actor)
              .map(_.userInfo)
          }
      }
      .distinct
    div(
      minHeight := "32px",
      child.maybe <-- activitySignal
        .combineWith(actorUserInfoOptSignal)
        .map { case (activity, actorUserInfoOpt) =>
          val resolvedTimeString = CommentUtils.getTimestampDisplayString(activity.createdAtOpt)
          actorUserInfoOpt.map { userInfo =>
            div(
              tw.textGray7.text11,
              tw.px16.py4,
              tw.flex.itemsCenter,
              div(
                tw.wPx24.pt4.textCenter.mr8,
                IconL(
                  name = Val(activity.activityType match {
                    case FormCommentCommons.ThreadActivityType.Reopen  => Icon.Glyph.Reopen
                    case FormCommentCommons.ThreadActivityType.Resolve => Icon.Glyph.CheckCircleLine
                  }),
                  size = Icon.Size
                    .Custom(px = 12)
                )().amend(tw.inlineBlock)
              ),
              div(
                tw.flexFill,
                TooltipL(
                  renderTarget = span(
                    (activity.activityType match {
                      case FormCommentCommons.ThreadActivityType.Reopen  => "Reopened by "
                      case FormCommentCommons.ThreadActivityType.Resolve => "Marked as resolved by "
                    }),
                    span(
                      tw.fontSemiBold,
                      fullNameString(userInfo)
                    )
                  ),
                  renderContent = _.amend(resolvedTimeString)
                )().amend(tw.inlineBlock)
              )
            )
          }
        }
    )
  }

  private def onParticipantsUpdate(lpId: FundSubLpId, participants: Set[UserId]) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      val currentParticipants = fundMembersRoleAndInfoMapVar.now().keySet
      val newParticipants = participants diff currentParticipants
      for {
        _ <- ZIO.foreach(newParticipants) { userId =>
          FundSubEndpointClient
            .getFundMemberRole(
              GetFundMemberRoleParams(
                lpId = lpId,
                userId = userId
              )
            )
            .map(
              _.fold(
                _ => (),
                response => fundMembersRoleAndInfoMapVar.update(_ + (userId -> response))
              )
            )
        }
      } yield ()
    }
  }

  private def renderItem(threadActivityItemSignal: Signal[CommentsListL.ThreadActivityItem], itemId: String) = {
    val commentOptSignal = threadActivityItemSignal.map {
      case comment: CommentsListL.ThreadActivityItem.Comment => Some(comment)
      case _                                                 => None
    }
    val activityOptSignal = threadActivityItemSignal.map {
      case activity: CommentsListL.ThreadActivityItem.Activity => Some(activity)
      case _                                                   => None
    }
    div(
      child <-- commentOptSignal.splitOption(
        (_, commentSignal) => renderCommentItem(commentSignal = commentSignal, itemId = itemId),
        ifEmpty = emptyNode
      ),
      child <-- activityOptSignal.splitOption(
        (_, activitySignal) => {
          renderActivityItem(
            activitySignal = activitySignal
          )
        },
        ifEmpty = emptyNode
      )
    )
  }

  private def onDeleteCommentItem(
    deletedItemId: String,
    thread: CommentThread,
    localComments: Seq[CommentsListL.ThreadActivityItem.Comment],
    currentUserIdOpt: Option[UserId]
  ): Unit = {
    val newLocalComments = localComments
      .find(_.itemId.equals(deletedItemId))
      .fold {
        val isRootComment = deletedItemId.equals(thread.id.toString)
        val newCommentItem = CommentsListL.ThreadActivityItem.Comment(
          threadId = thread.id,
          lpId = thread.fundSubLpId,
          comment = "",
          commentIdOpt = if (isRootComment) None else Some(deletedItemId),
          actorOpt = currentUserIdOpt,
          createdAtOpt = None,
          isPublic = thread.isPublic,
          lastEditedAtOpt = None,
          isPendingComment = true,
          itemId = deletedItemId,
          isDeleted = true,
          flaggedCommentMetadata = None,
          mentions = MentionsData.empty
        )
        localComments :+ newCommentItem
      } { _ =>
        localComments.map { item =>
          if (item.itemId == deletedItemId) {
            item.copy(isDeleted = true)
          } else {
            item
          }
        }
      }
    onUpdateLocalComments.onNext(newLocalComments)
  }

  def apply(): HtmlElement = {
    div(
      ComponentUtils.testIdL(this.getClass.getSimpleName),
      mountEventBus.events.sample(
        threadSignal
          .map(_.participants)
          .combineWith(AccountUtils.currentUserIdObs)
          .map { case (participants, currentUserOpt) =>
            participants ++ currentUserOpt.toList.toSet
          }
      ) --> participantsEventBus.writer,
      participantsEventBus.events
        .withCurrentValueOf(threadSignal.map(_.fundSubLpId))
        .flatMapSwitch { case (participants, lpId) =>
          onParticipantsUpdate(lpId, participants)
        } --> Observer.empty,
      threadSignal.map(_.participants) --> participantsEventBus.writer,
      deleteThreadCommentItemEventBus.events
        .withCurrentValueOf(
          threadSignal,
          localCommentsSignal,
          AccountUtils.currentUserIdObs
        )
        .map {
          case (deletedItemId, thread, localComments, currentUserIdOpt) => {
            onDeleteCommentItem(
              deletedItemId = deletedItemId,
              thread = thread,
              localComments = localComments,
              currentUserIdOpt = currentUserIdOpt
            )
          }
        } --> Observer.empty,
      div(
        children <-- fullThreadActivityItemsSignal.split(_.itemId) { case (itemId, _, threadActivityItemSignal) =>
          renderItem(threadActivityItemSignal = threadActivityItemSignal, itemId = itemId)
        },
        child.maybe <-- inactiveDaysThresholdSignal
          .combineWith(
            isFundSideViewSignal,
            threadSignal
          )
          .map { (inactiveDaysThresholdOpt, isFundSide, thread) =>
            Option.when(isFundSide && thread.isInactive(inactiveDaysThresholdOpt)) {
              div(
                tw.ml48.mb20,
                InactiveReminderText(
                  Val(thread.daysFromLastComment())
                )()
              )
            }
          }
      ),
      onMountCallback { _ =>
        mountEventBus.emit(())
      }
    )
  }

  private def renderCommentItem(
    commentSignal: Signal[CommentsListL.ThreadActivityItem.Comment],
    itemId: String
  ) = {
    val creatorInfoOptSignal = fundMembersRoleAndInfoMapVar.signal
      .combineWith(
        commentSignal.map(_.actorOpt)
      )
      .map { case (roleMap, userIdOpt) =>
        userIdOpt.flatMap { userId =>
          roleMap.get(userId)
        }
      }
    div(
      minHeight := "88px",
      tw.px16.py12,
      CommentItemL(
        threadIdSignal = commentSignal.map(_.threadId).distinct,
        lpIdSignal = commentSignal.map(_.lpId).distinct,
        commentSignal = commentSignal.map(_.comment).distinct,
        commentIdOptSignal = commentSignal.map(_.commentIdOpt).distinct,
        hasMentionSignal = commentSignal.map(_.mentions.nonEmpty),
        creatorOptSignal = commentSignal.map(_.actorOpt).distinct,
        createdAtOptSignal = commentSignal.map(_.createdAtOpt).distinct,
        lastEditedAtOptSignal = commentSignal.map(_.lastEditedAtOpt).distinct,
        creatorInfoOptSignal = creatorInfoOptSignal.distinct,
        isPublicSignal = commentSignal.map(_.isPublic).distinct,
        canMakePublicCommentSignal = canMakePublicCommentSignal.distinct,
        isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
        isPendingCommentSignal = commentSignal
          .map(_.isPendingComment)
          .combineWith(isPendingThreadSignal)
          .map { case (isPendingComment, isPendingThread) =>
            isPendingComment || isPendingThread
          }
          .distinct,
        fetchedFlaggedCommentMetadataOptSignal = commentSignal.map(_.flaggedCommentMetadata).distinct,
        onCopyInternalComment = onCopyInternalComment,
        onDeleteCommentItem = Observer(_ => deleteThreadCommentItemEventBus.emit(itemId)),
        isDrawerViewSignal = Val(false)
      )()
    )
  }

}

private[laminar] object CommentsListL {

  sealed trait ThreadActivityItem derives CanEqual {
    val itemId: String
    val actorOpt: Option[UserId]
    val createdAtOpt: Option[Instant]
    val threadId: IssueId
    val lpId: FundSubLpId
    val isPublic: Boolean
    val isLastItem: Boolean
    val isDeleted: Boolean
  }

  object ThreadActivityItem {

    case class Comment(
      itemId: String,
      actorOpt: Option[UserId],
      createdAtOpt: Option[Instant],
      threadId: IssueId,
      lpId: FundSubLpId,
      isPublic: Boolean,
      comment: String,
      commentIdOpt: Option[String],
      lastEditedAtOpt: Option[Instant],
      flaggedCommentMetadata: Option[FlaggedCommentMetadata],
      isPendingComment: Boolean = false,
      isLastItem: Boolean = false,
      isDeleted: Boolean = false,
      mentions: MentionsData
    ) extends ThreadActivityItem

    case class Activity(
      itemId: String,
      actorOpt: Option[UserId],
      createdAtOpt: Option[Instant],
      threadId: IssueId,
      lpId: FundSubLpId,
      isPublic: Boolean,
      activityType: ThreadActivityType,
      isLastItem: Boolean = false,
      isDeleted: Boolean = false
    ) extends ThreadActivityItem

  }

}
