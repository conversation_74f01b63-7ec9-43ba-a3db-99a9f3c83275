// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.common

import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.TimerSupport
import japgolly.scalajs.react.vdom.html_<^.*
import zio.ZIO

import anduin.fundsub.endpoint.subscriptiondoc.review.{
  FundSubSubscriptionDocReviewConfig,
  FundSubSubscriptionDocReviewType,
  GetReviewConfigParam
}
import anduin.id.fundsub.FundSubId
import anduin.model.notichannel.FundSubNotificationChannels
import anduin.service.GeneralServiceException
import anduin.stargazer.component.SignalReactor
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubSubscriptionDocReviewEndpointClient
import com.anduin.stargazer.notiCenter.NotificationCenter

final case class WithSubscriptionReviewConfig(
  fundSubId: FundSubId
) {

  def apply(renderFn: WithSubscriptionReviewConfig.RenderFn): VdomElement =
    WithSubscriptionReviewConfig.component(
      WithSubscriptionReviewConfig.Props(
        fundSubId,
        renderFn
      )
    )

}

object WithSubscriptionReviewConfig {

  private final case class Props(
    fundSubId: FundSubId,
    renderFn: RenderFn
  )

  final case class SubscriptionReviewConfig(
    unsignedConfig: Option[FundSubSubscriptionDocReviewConfig],
    signedConfig: Option[FundSubSubscriptionDocReviewConfig]
  ) derives CanEqual

  private type RenderFn = SubscriptionReviewConfig => VdomNode

  private final case class State(
    unsignedConfig: Option[FundSubSubscriptionDocReviewConfig] = None,
    signedConfig: Option[FundSubSubscriptionDocReviewConfig] = None
  )

  private final case class ReactorData(
    unsignedWatch: NotificationCenter.Watch,
    signedWatch: NotificationCenter.Watch
  ) derives CanEqual

  private case class Backend(scope: BackendScope[Props, State]) extends TimerSupport {

    val reactor: SignalReactor[ReactorData] = new SignalReactor[ReactorData] {

      override def isChanged(oldValue: ReactorData, newValue: ReactorData) = {

        newValue.unsignedWatch.isChanged(oldValue.unsignedWatch) || newValue.signedWatch.isChanged(oldValue.signedWatch)
      }

      override def onNewValue(oldValueOpt: Option[ReactorData], newValue: ReactorData) = fetchData(oldValueOpt, newValue)

    }

    private def fetchData(oldValueOpt: Option[ReactorData], newValue: ReactorData): Callback = {
      Callback.traverseOption(oldValueOpt) { oldValue =>
        for {
          _ <- Callback.when(newValue.unsignedWatch.isChanged(oldValue.unsignedWatch)) {
            fetchConfig(FundSubSubscriptionDocReviewType.UnsignedSubscription)
          }
          _ <- Callback.when(newValue.signedWatch.isChanged(oldValue.signedWatch)) {
            fetchConfig(FundSubSubscriptionDocReviewType.SignedSubscription)
          }
        } yield ()
      }
    }

    def render(props: Props, state: State): VdomNode = {
      props.renderFn(
        SubscriptionReviewConfig(
          state.unsignedConfig,
          state.signedConfig
        )
      )
    }

    def fetchConfig(reviewType: FundSubSubscriptionDocReviewType): Callback = {
      for {
        props <- scope.props
        _ <- ZIOUtils.toReactCallback(
          FundSubSubscriptionDocReviewEndpointClient
            .getFundSubSubscriptionDocReviewConfig(
              GetReviewConfigParam(
                props.fundSubId,
                reviewType
              )
            )
            .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
            .map { resp =>
              reviewType match {
                case FundSubSubscriptionDocReviewType.UnsignedSubscription =>
                  scope.modState(_.copy(unsignedConfig = resp.toOption))
                case FundSubSubscriptionDocReviewType.SignedSubscription =>
                  scope.modState(_.copy(signedConfig = resp.toOption))
              }
            }
        )
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .componentDidMount { scope =>
      for {
        _ <- scope.backend.fetchConfig(FundSubSubscriptionDocReviewType.UnsignedSubscription)
        _ <- scope.backend.fetchConfig(FundSubSubscriptionDocReviewType.SignedSubscription)
        _ <- NotificationCenter.subscribeCallback(
          Seq(
            FundSubNotificationChannels.fundSubReviewConfigUnsigned(scope.props.fundSubId),
            FundSubNotificationChannels.fundSubReviewConfigSigned(scope.props.fundSubId)
          )
        )
        _ <- scope.backend.reactor.subscribe(
          NotificationCenter
            .eventSignal(FundSubNotificationChannels.fundSubReviewConfigUnsigned(scope.props.fundSubId))
            .combineWith(
              NotificationCenter
                .eventSignal(FundSubNotificationChannels.fundSubReviewConfigSigned(scope.props.fundSubId))
            )
            .distinct
            .map { case (unsignedWatch, signedWatch) =>
              ReactorData(unsignedWatch = unsignedWatch, signedWatch = signedWatch)
            }
        )
      } yield ()
    }
    .componentWillUnmount(_.backend.reactor.cleanUp())
    .build

}
