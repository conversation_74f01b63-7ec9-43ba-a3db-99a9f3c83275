// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.auditlog

import anduin.copy.Bundle.I18N
import java.time.format.DateTimeFormatter
import java.time.{Instant, ZoneId}

import design.anduin.components.avatar.InitialAvatar
import design.anduin.components.avatar.react.InitialAvatarR
import design.anduin.components.button.Button
import design.anduin.components.collapse.react.CollapseWithIconR
import design.anduin.components.drawer.Drawer
import design.anduin.components.drawer.react.DrawerR
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.components.wrapper.react.WrapperR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import anduin.model.id.TeamId
import anduin.fundsub.auditlog.FundSubAuditLogUtils.getDescriptionForFundSubAuditLogEventType
import anduin.fundsub.auditlog.{AuditLogActorType, AuditLogEventType, FundSubAuditLogUtils}
import anduin.fundsub.endpoint.admin.LpBasicInfo
import anduin.fundsub.endpoint.auditlog.AuditLogData
import anduin.fundsub.endpoint.lp.NameEmailInfo
import anduin.id.fundsub.FundSubLpId
import anduin.id.fundsub.auditlog.FundSubAuditLogId
import anduin.model.common.user.UserId
import anduin.user.UserInitialAvatar
import anduin.utils.DateTimeUtils
import com.anduin.stargazer.fundsub.module.common.activitylog.{Activity, ActivityLogUtils}
import com.anduin.stargazer.fundsub.module.investor.settings.EmailDetailDrawer
import com.anduin.stargazer.fundsub.module.investor.settings.EmailDetailDrawer.{
  EmailEventStatus,
  calculateEmailEventStatus,
  renderEmailEventStatusIcon
}
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils

private[settings] final case class AuditLogTableRow(
  event: AuditLogData,
  index: Int,
  auditLogId: Option[FundSubAuditLogId],
  userInfoMap: Map[UserId, NameEmailInfo],
  copyConfig: I18N,
  lpInfoMap: Map[FundSubLpId, LpBasicInfo],
  gpTeamNameMap: Map[TeamId, String],
  timezone: ZoneId
) {
  def apply(): VdomElement = AuditLogTableRow.component(this)
}

private[settings] object AuditLogTableRow {

  private type Props = AuditLogTableRow

  private def render(props: Props) = {
    val index = props.index
    val event = props.event
    val recipients = event.eventEmails.flatMap(_.emailInfo.flatMap(_.receivers))
    val emailEventStatus = calculateEmailEventStatus(recipients.map(_._2)).status
    <.div(
      ComponentUtils.testId(AuditLogTableRow),
      ^.key := index,
      CollapseWithIconR(
        render = renderProps => {
          React.Fragment(
            <.div(
              ^.onClick --> renderProps.toggle,
              tw.cursorPointer.flex.borderTop.borderGray3.hover(tw.bgGray1),
              List(
                (if (event.eventEmails.nonEmpty || event.orderFirmName.nonEmpty || event.ipAddress.nonEmpty) {
                   renderProps.renderIcon(
                     tw.pt4,
                     IconR(name = Icon.Glyph.ChevronRight)()
                   )
                 } else {
                   EmptyVdom
                 }) -> AuditLogTable.CollapseColumn,
                renderTimeCell(event, props.timezone) -> AuditLogTable.TimeColumn,
                renderActorCell(event) -> AuditLogTable.ActorColumn,
                renderActionCell(
                  event,
                  emailEventStatus,
                  props.copyConfig
                ) -> AuditLogTable.ActionColumn,
                renderSummaryCell(event, props) -> AuditLogTable.SummaryColumn
              ).toVdomArray(
                using { case (cell, column) =>
                  <.div(
                    ^.key := s"$index-${column.title}",
                    ^.minWidth := column.minWidth,
                    ^.width := column.width,
                    tw.p8,
                    cell
                  )
                }
              )
            ),
            renderProps.renderContent(
              tw.flex.borderTop.borderGray3.bgGray1,
              ^.display.none,
              ^.key := s"$index-expand",
              List(
                EmptyVdom -> AuditLogTable.CollapseColumn,
                renderDetailTitle(event) -> AuditLogTable.TimeColumn,
                renderDetailContent(event, props) -> AuditLogTable.ActorColumn
              ).toVdomArray(
                using { case (cell, column) =>
                  <.div(
                    ^.key := s"$index-${column.title}",
                    ^.minWidth := column.minWidth,
                    ^.width := column.width,
                    tw.py16.px8.alignTop,
                    cell
                  )
                }
              )
            )
          )
        }
      )()
    )
  }

  private def renderTimeCell(event: AuditLogData, timezone: ZoneId) = {
    val time = event.timestamp.getOrElse(Instant.MIN)
    <.div(
      ComponentUtils.testId("TimeCell"),
      tw.textGray7,
      <.div(
        DateTimeUtils.formatInstant(time, DateTimeFormatter.ofPattern("dd MMM yyyy"))(
          using timezone
        )
      ),
      <.div(
        DateTimeUtils.formatInstant(time, DateTimeFormatter.ofPattern("HH:mm:ss"))(
          using timezone
        )
      )
    )
  }

  private def renderActorCell(event: AuditLogData) = {
    val render = for {
      actorInfo <- event.actorInfo
    } yield {
      val metaName = event.actorType match {
        case AuditLogActorType.FundSide                                   => "Fund management"
        case AuditLogActorType.InvestorSide                               => "Investor"
        case AuditLogActorType.Ria                                        => "Advisor"
        case AuditLogActorType.System | AuditLogActorType.Unrecognized(_) => ""
      }
      <.div(
        ComponentUtils.testId("ActorCell"),
        tw.flex.itemsCenter.hPc100,
        UserInitialAvatar(
          userInfo = actorInfo,
          size = InitialAvatar.Size.Px24
        )(),
        <.div(
          tw.ml8,
          <.div(tw.fontSemiBold, actorInfo.fullNameString),
          TagMod.when(metaName.nonEmpty) { <.div(tw.textGray7.text11, metaName) }
        )
      )
    }
    render.getOrElse(
      <.div(
        tw.flex.itemsCenter.hPc100,
        InitialAvatarR(
          id = "Anduin",
          initials = "A",
          size = InitialAvatar.Size.Px24
        )(),
        <.div(
          tw.ml12.fontSemiBold,
          "System"
        )
      )
    )
  }

  private def renderActionCell(
    event: AuditLogData,
    status: EmailEventStatus,
    copyConfig: I18N
  ) = {
    <.div(
      ComponentUtils.testId("ActionCell"),
      tw.fontSemiBold.flex.spaceX6.itemsCenter,
      <.div(
        getDescriptionForFundSubAuditLogEventType(event.eventType, copyConfig, FundSubCopyUtils.getFlowTerm)
      ),
      renderEmailEventStatusIcon(status)
    )
  }

  private def renderSummaryCell(event: AuditLogData, props: Props) = {
    val actorNameOpt = event.actorInfo.map(_.fullNameString)
    val activityOpt = (for {
      activityDetail <- event.activityDetail
    } yield ActivityLogUtils.buildActivityForAuditLog(
      orderIdOpt = event.orderId,
      actorName = event.eventType match {
        case AuditLogEventType.SUBSCRIPTION_SYNCED => actorNameOpt.getOrElse("System")
        case _                                     => actorNameOpt.getOrElse("")
      },
      activity = activityDetail,
      userInfoMap = props.userInfoMap,
      gpTeamNameMap = props.gpTeamNameMap,
      lpInfoMap = props.lpInfoMap,
      copyConfig = props.copyConfig,
      flowTermCollection = FundSubCopyUtils.getFlowTerm
    )).flatten
    activityOpt
      .map(activity => WrapperR(Activity.renderActivityContent(activity))())
      .getOrElse(<.div())
  }

  private def renderDetailTitle(event: AuditLogData) = {
    <.div(
      tw.spaceY8.fontSemiBold.textGray7,
      TagMod.when(event.ipAddress.nonEmpty) {
        <.div("IP address")
      },
      TagMod.when(event.orderFirmName.nonEmpty) {
        <.div("Investment entity")
      },
      TagMod.when(event.eventEmails.nonEmpty) {
        <.div("Emails")
      }
    )
  }

  private def renderDetailContent(
    event: AuditLogData,
    props: Props
  ) = {
    <.div(
      tw.spaceY8.textGray8,
      TagMod.when(event.ipAddress.nonEmpty) {
        <.div(s"${event.ipAddress.getOrElse("Missing ip")}")
      },
      TagMod.when(event.orderFirmName.nonEmpty) {
        <.div(s"${event.orderFirmName.getOrElse("")}")
      },
      <.div(
        tw.spaceY4.flex.flexCol,
        event.eventEmails
          .filter(_.emailInfo.nonEmpty)
          .toVdomArray(
            using { emailData =>
              val emailEventStatusDetail = calculateEmailEventStatus(emailData.emailInfo.flatMap(_.receivers).map(_._2))
              <.div(
                tw.flex.spaceX6.itemsCenter,
                DrawerR(
                  width = Drawer.Width.Px400,
                  renderTarget = open => {
                    <.div(
                      AdditionalContentOnHover(
                        renderTarget = TooltipR(
                          renderTarget = Button(
                            style = Button.Style.Text(),
                            onClick = open
                          )(
                            <.div(
                              tw.textLeft.overflowXHidden.whitespaceNowrap,
                              ^.className := "truncate",
                              FundSubAuditLogUtils
                                .getEmailDescriptionForFundSubEvent(
                                  emailData.fundSubEventType,
                                  FundSubCopyUtils.getFlowTerm
                                )
                            )
                          ),
                          renderContent = _("View details")
                        )(),
                        renderContent = (
                        ) => <.div(tw.textPrimary5.ml8, IconR(Icon.Glyph.Eye, size = Icon.Size.Px16)())
                      )()
                    )
                  },
                  renderContent = renderProps =>
                    props.auditLogId.fold(<.div()) { _ =>
                      <.div(
                        EmailDetailDrawer(
                          onClose = renderProps.onClose,
                          emailLogs = emailData.emailInfo,
                          emailEventStatusDetail = emailEventStatusDetail
                        )()
                      )
                    }
                )(),
                renderEmailEventStatusIcon(emailEventStatusDetail.status)
              )
            }
          )
      )
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
