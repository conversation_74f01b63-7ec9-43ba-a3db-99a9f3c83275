// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.contact

import design.anduin.components.button.Button
import design.anduin.components.checkbox.Checkbox
import design.anduin.components.collapse.react.CollapseWithIconR
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.modal.Modal
import design.anduin.components.portal.{PortalUtils, PortalWrapper}
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.contact.endpoint.ContactModel
import anduin.fundsub.constants.Terms
import anduin.fundsub.endpoint.contact.FundSubContactInfo
import anduin.id.contact.ContactId
import anduin.id.fundsub.{FundSubContactGroupId, FundSubId}
import anduin.protobuf.fundsub.contact.FundSubContactGroup
import anduin.utils.StringUtils
import com.anduin.stargazer.fundsub.module.investor.contact.modal.{AddOrEditGroupModal, DeleteGroupModal}

final case class FundSubContactGroupRow(
  fundSubId: FundSubId,
  enabledCustomId: Boolean,
  group: FundSubContactGroup,
  contactInfo: FundSubContactInfo,
  contactModels: Seq[(ContactModel, Boolean)], // (model, selected)
  selectedEmptyGroups: Seq[FundSubContactGroupId],
  onToggleContacts: (Seq[ContactId], Boolean) => Callback,
  onToggleEmptyGroup: Boolean => Callback,
  searchValue: String,
  reFetch: Callback
) {

  def apply(): VdomElement = FundSubContactGroupRow.component(this)
}

object FundSubContactGroupRow {

  private type Props = FundSubContactGroupRow

  private def render(props: Props) = {
    val count = props.contactInfo.groupMembership.getOrElse(props.group.id, 0)
    val customId = props.group.customId.trim
    val isEmptyGroup = props.contactModels.isEmpty

    <.div(
      ComponentUtils.testId(FundSubContactGroupRow, "Container"),
      tw.cursorPointer.py12.hover(tw.bgGray1),
      CollapseWithIconR(
        render = renderProps => {
          <.div(
            <.div(
              tw.flex.itemsCenter.pl16.group,
              renderGroupCheckbox(props),
              // Toggle button
              <.div(
                ComponentUtils.testId(FundSubContactGroupRow, "Toggle"),
                tw.ml24.mr20.textGray7.cursorPointer,
                ^.onClick ==> { e =>
                  e.stopPropagationCB >> renderProps.toggle
                },
                if (isEmptyGroup) {
                  <.div(tw.wPx16) // Hide the expand/collapse button when empty
                } else {
                  renderProps.renderIcon(IconR(Icon.Glyph.ChevronRight)())
                }
              ),
              <.div(tw.textGray7, IconR(Icon.Glyph.UserGroupLarge)()),
              <.div(
                tw.mx12.flex.itemsCenter,
                ^.width := "480px",
                <.div(
                  ^.maxWidth := "400px",
                  TooltipR(
                    renderTarget = <.p(
                      tw.wPc100.truncate,
                      <.span(
                        ComponentUtils.testId(FundSubContactGroupRow, "InvestorName"),
                        tw.fontSemiBold,
                        props.group.name
                      ),
                      TagMod.when(props.enabledCustomId && customId.nonEmpty) {
                        <.span(
                          ComponentUtils.testId(FundSubContactGroupRow, "InvestorId"),
                          tw.textGray7.ml4,
                          s"- $customId"
                        )
                      }
                    ),
                    renderContent = _(
                      if (customId.isEmpty) props.group.name else s"${props.group.name} - $customId"
                    ),
                    targetWrapper = PortalWrapper.BlockContent
                  )()
                ),
                <.div(
                  ^.flexShrink := "0",
                  tw.ml4.textGray6.flexNone,
                  if (count == 0) "(Empty)" else s"(${StringUtils.pluralItem(count, "contact")})"
                )
              ),
              <.div(tw.invisible.groupHover(tw.visible), renderActionGroup(props))
            ),
            renderProps.renderContent(
              ^.display.none,
              props.contactModels.toVdomArray(
                using { case (contactModel, isSelected) =>
                  <.div(
                    ^.key := s"${contactModel.contactId.idString}",
                    tw.mt12,
                    FundSubContactRow(
                      props.fundSubId,
                      contactModel,
                      props.contactInfo,
                      isSelected,
                      selected => props.onToggleContacts(Seq(contactModel.contactId), selected),
                      parentGroup = Some(props.group.id),
                      props.reFetch
                    )()
                  )
                }
              )
            )
          )
        }
      )()
    )
  }

  // Note that we also allow use to select the empty group, which defined by props.selectedEmptyGroups
  private def renderGroupCheckbox(props: Props) = {
    val isSelectedEmptyGroup = props.selectedEmptyGroups.contains(props.group.id)
    val isChecked = isSelectedEmptyGroup || (props.contactModels.forall(_._2) && props.contactModels.nonEmpty)
    val isIndeterminate = !isChecked && props.contactModels.exists(_._2)
    <.div(
      ComponentUtils.testId(FundSubContactGroupRow, "CheckBox"),
      ^.onClick ==> (_.stopPropagationCB),
      Checkbox(
        isChecked = isChecked,
        // If empty, select the empty group instead
        onChange = checked =>
          if (props.contactModels.isEmpty) {
            props.onToggleEmptyGroup(checked)
          } else {
            props.onToggleContacts(props.contactModels.map(_._1.contactId), checked)
          },
        isIndeterminate = isIndeterminate
      )()
    )
  }

  private def renderDelete(props: Props) = {
    Modal(
      renderTarget = open =>
        Button(style = Button.Style.Text(), onClick = open)(
          <.div(
            ComponentUtils.testId(FundSubContactGroupRow, "DeleteButton"),
            tw.flex.itemsCenter.textGray7.fontSemiBold,
            IconR(Icon.Glyph.UserRemove)(),
            <.span(tw.ml8, "Remove")
          )
        ),
      renderContent = close =>
        DeleteGroupModal(
          props.group,
          close,
          props.reFetch
        )(),
      title = s"Delete ${Terms.InvesteeEntityLabelLowerCase}?",
      isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false))
    )()
  }

  private def renderEdit(props: Props) = {
    Modal(
      renderTarget = open =>
        <.div(
          tw.mr16.flex,
          Button(style = Button.Style.Text(), onClick = open)(
            <.div(
              ComponentUtils.testId(FundSubContactGroupRow, "EditButton"),
              tw.flex.itemsCenter.textGray7.fontSemiBold,
              IconR(Icon.Glyph.Edit)(),
              <.span(tw.ml8, "Edit")
            )
          )
        ),
      renderContent = close =>
        AddOrEditGroupModal(
          props.fundSubId,
          Some(props.group),
          props.enabledCustomId,
          props.contactInfo,
          close,
          props.reFetch
        )(),
      title = s"Edit ${Terms.InvesteeEntityLabelLowerCase}",
      isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false)),
      size = Modal.Size(Modal.Width.Px720)
    )()
  }

  private def renderActionGroup(props: Props) = {
    <.div(
      tw.flex.itemsCenter,
      ^.onClick ==> (_.stopPropagationCB),
      renderEdit(props),
      renderDelete(props)
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
