// Copyright (C) 2014-2025 Anduin Transactions Inc.
package com.anduin.stargazer.fundsub.module.investor.settings.mailserver

import design.anduin.components.button.Button
import design.anduin.components.checkbox.Checkbox
import design.anduin.components.field.Field
import design.anduin.components.modal.{ModalBody, ModalFooterWCancel}
import design.anduin.components.textbox.TextBox
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import design.anduin.validators.{ErrorMessage, FieldName, FieldValidator, FormValidator, WithFormValidator}
import design.anduin.validators.rules.{EmailAddressRule, RequiredRule}
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.email.{CustomEmailAddress, CustomSmtpServerConfig}
import anduin.fundsub.endpoint.admin.UpdateSmtpConfigParams
import anduin.id.fundsub.FundSubId
import anduin.model.common.emailaddress.EmailAddress
import anduin.switch.SwitchConfig
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

private[mailserver] final case class SmtpConfigForm(
  fundSubId: FundSubId,
  config: CustomSmtpServerConfig,
  onSave: SwitchConfig[CustomSmtpServerConfig] => Callback,
  onCancel: Callback = Callback.empty
) {
  def apply(): VdomElement = SmtpConfigForm.component(this)
}

private[mailserver] object SmtpConfigForm {
  private type Props = SmtpConfigForm

  private val hostNameField = FieldName("host")
  private val portNameField = FieldName("port")
  private val userNameField = FieldName("userName")
  private val passwordField = FieldName("password")
  private val senderNameField = FieldName("senderName")
  private val senderEmailAddressField = FieldName("senderEmailAddress")

  private val hostNameValidator = FieldValidator(
    List(
      RequiredRule(ErrorMessage("Host name must not be empty")),
      SmtpHostNameRule(ErrorMessage("Invalid host name"))
    )
  )

  private val portNameValidator = FieldValidator(
    List(
      RequiredRule(ErrorMessage("Port must not be empty"))
    )
  )

  private val userNameValidator = FieldValidator(
    List(
      RequiredRule(ErrorMessage("User name must not be empty"))
    )
  )

  private val passwordValidator = FieldValidator(
    List(
      RequiredRule(ErrorMessage("Password must not be empty"))
    )
  )

  private val senderNameValidator = FieldValidator(
    List(
      RequiredRule(ErrorMessage("Sender name must not be empty"))
    )
  )

  private val senderEmailAddressValidator = FieldValidator(
    List(
      RequiredRule(ErrorMessage("Sender email address must not be empty")),
      EmailAddressRule(ErrorMessage("Invalid email address"))
    )
  )

  private val fromValidator = FormValidator(
    Map(
      hostNameField -> hostNameValidator,
      portNameField -> portNameValidator,
      userNameField -> userNameValidator,
      passwordField -> passwordValidator,
      senderNameField -> senderNameValidator,
      senderEmailAddressField -> senderEmailAddressValidator
    )
  )

  private final case class State(
    host: String,
    port: String,
    tls: Boolean,
    userName: String,
    password: String,
    senderName: String,
    senderEmailAddress: String,
    isSaving: Boolean
  )

  private def initializeStateFromProps(props: Props): State = {
    State(
      host = props.config.host,
      port = if (props.config.port == 0) "" else props.config.port.toString,
      tls = props.config.tls,
      userName = props.config.userName,
      password = "", // We never fetch the password to the client
      senderName = props.config.from.name,
      senderEmailAddress = props.config.from.address,
      isSaving = false
    )
  }

  private final case class Backend(scope: BackendScope[Props, State]) {

    private def renderForm(state: State, renderer: WithFormValidator.Renderer) =
      <.div(
        tw.spaceY24,
        Field(
          label = Some("Host"),
          helper = Field.Helper.BelowLabel("Host name or IP address of the SMTP server (e.g., mail.mycompany.com)"),
          validation = renderer.errorOf(hostNameField),
          requirement = Field.Requirement.Required,
          id = Some("host")
        )(
          TextBox(
            value = state.host,
            placeholder = "Enter host (e.g., mail.mycompany.com)",
            isReadOnly = state.isSaving,
            status = renderer.statusOf(hostNameField),
            onChange = (value: String) => scope.modState(_.copy(host = value)),
            onBlur = value => {
              renderer.validateField(
                hostNameField,
                value
              ) { _ => Callback.empty }
            },
            tpe = TextBox.Tpe.Text,
            id = Some("host")
          )()
        ),
        <.div(
          Field(
            label = Some("Port"),
            helper = Field.Helper.BelowLabel("Common ports: 465 (SSL), 587 (TLS), 25 (Unencrypted)"),
            validation = renderer.errorOf(portNameField),
            requirement = Field.Requirement.Required,
            id = Some("port")
          )(
            TextBox(
              value = state.port,
              placeholder = "Enter port (e.g., 587)",
              isReadOnly = state.isSaving,
              status = renderer.statusOf(portNameField),
              onChange = (value: String) => scope.modState(_.copy(port = value)),
              onBlur = value => {
                renderer.validateField(
                  portNameField,
                  value
                ) { _ => Callback.empty }
              },
              tpe = TextBox.Tpe.NumberInt,
              id = Some("port")
            )()
          ),
          <.div(
            tw.mt4,
            Checkbox(
              isChecked = state.tls,
              isReadOnly = state.isSaving,
              onChange = (requireTls: Boolean) => scope.modState(_.copy(tls = requireTls))
            )("Enable TLS (typically required for port 587)")
          )
        ),
        Field(
          label = Some("Username"),
          validation = renderer.errorOf(userNameField),
          requirement = Field.Requirement.Required,
          id = Some("user-name")
        )(
          TextBox(
            value = state.userName,
            placeholder = "Enter username",
            isReadOnly = state.isSaving,
            status = renderer.statusOf(userNameField),
            onChange = (value: String) => scope.modState(_.copy(userName = value)),
            onBlur = value => {
              renderer.validateField(
                userNameField,
                value
              ) { _ => Callback.empty }
            },
            tpe = TextBox.Tpe.Text,
            id = Some("user-name")
          )()
        ),
        Field(
          label = Some("Password"),
          validation = renderer.errorOf(passwordField),
          requirement = Field.Requirement.Required,
          id = Some("password")
        )(
          TextBox(
            value = state.password,
            placeholder = "Enter password",
            isReadOnly = state.isSaving,
            status = renderer.statusOf(passwordField),
            onChange = (value: String) => scope.modState(_.copy(password = value)),
            onBlur = value => {
              renderer.validateField(
                passwordField,
                value
              ) { _ => Callback.empty }
            },
            tpe = TextBox.Tpe.Password,
            id = Some("password")
          )()
        ),
        Field(
          label = Some("Send email as"),
          requirement = Field.Requirement.Required,
          helper = {
            val shouldRenderExample = {
              EmailAddress.isValid(state.senderEmailAddress) &&
              state.senderName.nonEmpty
            }
            if (shouldRenderExample) {
              Field.Helper.BelowLabel(
                <.p(
                  <.span(
                    "Recipients will see emails from "
                  ),
                  <.span(
                    tw.ml4.fontSemiBold,
                    s"${state.senderName} (${state.senderEmailAddress})"
                  )
                )
              )
            } else {
              Field.Helper.BelowLabel(
                "The sender name and email address recipients will see"
              )
            }
          },
          validation = if (EmailAddress.isValid(state.userName) && state.userName != state.senderEmailAddress) {
            Field.Validation.Warning(
              "As you're using an email address for username, the sender email address should match it"
            )
          } else {
            Field.Validation.None
          }
        )(
          <.div(
            tw.flex.gapX8,
            <.div(
              tw.flex1,
              Field(
                validation = renderer.errorOf(senderNameField),
                requirement = Field.Requirement.Required,
                id = Some("sender-name")
              )(
                TextBox(
                  value = state.senderName,
                  placeholder = "Enter sender name",
                  isReadOnly = state.isSaving,
                  status = renderer.statusOf(senderNameField),
                  onChange = (value: String) => scope.modState(_.copy(senderName = value)),
                  onBlur = value => {
                    renderer.validateField(
                      senderNameField,
                      value
                    ) { _ => Callback.empty }
                  },
                  id = Some("sender-name")
                )()
              )
            ),
            <.div(
              tw.flex1,
              Field(
                validation = renderer.errorOf(senderEmailAddressField),
                requirement = Field.Requirement.Required,
                id = Some("sender-email-address")
              )(
                <.div(
                  TextBox(
                    value = state.senderEmailAddress,
                    placeholder = "Enter sender email address",
                    isReadOnly = state.isSaving,
                    status = renderer.statusOf(senderEmailAddressField),
                    tpe = TextBox.Tpe.EmailNative,
                    onChange = (value: String) => scope.modState(_.copy(senderEmailAddress = value)),
                    onBlur = value => {
                      renderer.validateField(
                        senderEmailAddressField,
                        value
                      ) { _ => Callback.empty }
                    },
                    id = Some("sender-email-address")
                  )()
                )
              )
            )
          )
        )
      )

    private def renderSaveButton(state: State, renderer: WithFormValidator.Renderer) = {
      val isBusy = state.isSaving

      Button(
        style = Button.Style.Full(color = Button.Color.Primary, isBusy = isBusy),
        onClick = renderer.validateForm { result =>
          if (result.valid) {
            onSave
          } else {
            Callback.empty
          }
        }
      )("Save")
    }

    def render(props: Props, state: State) = {
      WithFormValidator(
        validator = fromValidator,
        render = renderer =>
          <.div(
            ModalBody()(
              renderForm(state, renderer)
            ),
            ModalFooterWCancel(
              props.onCancel
            )(
              renderSaveButton(state, renderer)
            )
          )
      )()
    }

    private def onSave = {
      val cb = for {
        props <- scope.props
        state <- scope.state
        config = SwitchConfig(
          enabled = true,
          config = Some(
            CustomSmtpServerConfig(
              from = CustomEmailAddress(
                name = state.senderName,
                address = state.senderEmailAddress
              ),
              host = SmtpHostNameRule.sanitizeHostName(state.host),
              port = state.port.toIntOption.getOrElse(0),
              userName = state.userName,
              rawPassword = state.password,
              tls = state.tls
            )
          )
        )
        _ <- ZIOUtils.toReactCallback(
          FundSubEndpointClient
            .updateSmtpConfig(
              UpdateSmtpConfigParams(
                fundSubId = props.fundSubId,
                smptConfig = config
              )
            )
            .map(
              _.fold(
                error => {
                  scope.modState(
                    _.copy(isSaving = false),
                    Toast.errorCallback("Failed to save settings " + error.getMessage)
                  )
                },
                _ => {
                  scope.modState(
                    _.copy(isSaving = false),
                    Toast.successCallback("Settings saved successfully") >>
                      props.onSave(
                        config
                      )
                  )
                }
              )
            )
        )
      } yield ()
      scope.modState(_.copy(isSaving = true), cb)
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps { props =>
      val state = initializeStateFromProps(props)
      fromValidator.setValue(
        hostNameValidator,
        state.host
      )
      fromValidator.setValue(
        portNameValidator,
        state.port
      )
      fromValidator.setValue(
        userNameValidator,
        state.userName
      )
      fromValidator.setValue(
        passwordValidator,
        state.password
      )
      fromValidator.setValue(
        senderNameValidator,
        state.senderName
      )
      fromValidator.setValue(
        senderEmailAddressValidator,
        state.senderEmailAddress
      )
      state
    }
    .renderBackend[Backend]
    .build

}
