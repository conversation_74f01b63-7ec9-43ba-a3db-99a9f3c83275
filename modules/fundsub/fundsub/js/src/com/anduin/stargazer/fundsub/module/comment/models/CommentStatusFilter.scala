// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.models

private[fundsub] sealed trait CommentStatusFilter derives CanEqual {
  val displayName: String
}

private[fundsub] object CommentStatusFilter {

  case object AllComments extends CommentStatusFilter {
    override val displayName = "Show all"
  }

  case object ResolvedOnly extends CommentStatusFilter {
    override val displayName = "Only resolved comments"
  }

  case object OpenOnly extends CommentStatusFilter {
    override val displayName = "Only open comments"
  }

}
