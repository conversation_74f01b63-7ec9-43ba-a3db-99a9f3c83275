// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.ria

import anduin.id.fundsub.FundSubId
import design.anduin.components.wrapper.react.WrapperR
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[investor] case class AdvisorGroupsEntryPoint(
  fundSubId: FundSubId,
  isEnableRia: Boolean
) {
  def apply(): VdomElement = AdvisorGroupsEntryPoint.component(this)
}

private[investor] object AdvisorGroupsEntryPoint {

  private type Props = AdvisorGroupsEntryPoint

  private def render(props: Props): VdomElement = {
    val _ = props
    WrapperR(
      node = AdvisorGroupsIndex(
        fundSubId = props.fundSubId,
        isEnableRia = props.isEnableRia
      )()
    )()
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .render_P(render)
    .build

}
