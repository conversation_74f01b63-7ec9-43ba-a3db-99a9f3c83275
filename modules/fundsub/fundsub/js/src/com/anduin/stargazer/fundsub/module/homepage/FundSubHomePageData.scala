// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.homepage

import java.time.Instant

import io.circe.Codec

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.fundsub.homepage.FundSubEnvironmentNavigationData
import anduin.fundsub.model.FundSubSharedClientModel.ParticipantInfo
import anduin.id.entity.EntityId
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.investmententity.InvestmentEntityInfo
import anduin.model.codec.ProtoCodecs.given
import anduin.model.common.user.UserId
import anduin.protobuf.external.squants.{CurrencyMessage, MoneyMessage}
import anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus
import anduin.protobuf.fundsub.{LpFlowType, LpOrderType}

final case class FundSubHomePageData(
  currentUserId: UserId,
  fundSubsJoinedAsAdmin: List[FundSubJoinedAsAdmin],
  fundSubsJoinedAsLp: List[FundSubJoinedAsLp],
  investmentEntityList: List[InvestmentEntityInfo],
  currentEnvironmentOpt: Option[FundSubEnvironmentNavigationData]
)

object FundSubHomePageData {
  given Codec.AsObject[FundSubHomePageData] = deriveCodecWithDefaults
}

final case class FundSubFundDashboardPageData(
  currentUserId: UserId,
  fundSubsJoinedAsLp: List[FundSubJoinedAsLp]
) {

  def filterByFundIds(fundIds: Set[FundSubId]): FundSubFundDashboardPageData = {
    val filteredFundSubsJoinedAsLp = fundSubsJoinedAsLp.filter { fund =>
      fundIds.contains(fund.fundSubLpId.parent)
    }
    this.copy(
      fundSubsJoinedAsLp = filteredFundSubsJoinedAsLp
    )
  }

}

object FundSubFundDashboardPageData {
  given Codec.AsObject[FundSubFundDashboardPageData] = deriveCodecWithDefaults
}

final case class FundSubJoinedAsAdmin(
  fundSubId: FundSubId,
  investorEntity: EntityId,
  fundName: String,
  fundCreatedAt: Option[Instant],
  numberOfLp: Int,
  approvedAmount: Double,
  underReviewAmount: Double,
  logoUrl: Option[String],
  logoBgColor: Option[String],
  currency: CurrencyMessage,
  customFundId: Option[String],
  isFundClosed: Boolean,
  targetAmount: Option[MoneyMessage],
  targetClosingDate: Option[Instant],
  lastActivityAt: Option[Instant],
  needCurrencySetup: Boolean
)

object FundSubJoinedAsAdmin {
  given Codec.AsObject[FundSubJoinedAsAdmin] = deriveCodecWithDefaults
}

final case class FundSubJoinedAsLp(
  fundSubLpId: FundSubLpId,
  lpFirmName: String,
  lpUser: ParticipantInfo,
  collaborators: List[ParticipantInfo],
  investorEntity: EntityId,
  fundName: String,
  fundCreatedAt: Option[Instant],
  lpStatus: LpStatus,
  logoUrl: Option[String],
  logoBgColor: Option[String],
  firmLabel: String,
  orderType: LpOrderType,
  flowType: LpFlowType,
  isFundClosed: Boolean,
  commitmentAmount: Option[MoneyMessage],
  lastActivityAt: Option[Instant]
) derives CanEqual

object FundSubJoinedAsLp {
  given Codec.AsObject[FundSubJoinedAsLp] = deriveCodecWithDefaults
}
