// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.integrations

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.style.tw.*

import anduin.frontend.AirStreamUtils.splitNonEmpty
import anduin.fundsub.endpoint.group.FundSubGroupRoleType
import anduin.id.fundsub.FundSubId

private[settings] case class FundSubIntegrationsSetting(
  fundSubId: FundSubId,
  userGroupRole: FundSubGroupRoleType,
  showAddIntegration: Boolean
) {

  private val showAddIntegrationModalVar = Var[Boolean](showAddIntegration)

  def apply(): HtmlElement = {
    div(
      IntegrationHubsConnectedInfoProvider(fundSubId)(),
      child <-- IntegrationHubsConnectedInfoProvider.isFetchingSignal.splitBoolean(
        whenTrue = _ => BlockIndicatorL(title = Val(Some("Loading… This will only take a moment.")))(),
        whenFalse = _ =>
          div(
            children <-- IntegrationHubsConnectedInfoProvider.entityHubsInfoSignal.splitNonEmpty(_.hubInfo.entityId)(
              projectEmpty = renderEmptyState,
              projectItem = { (entityId, _, integrationHubInfoSignal) =>
                FundSubConnectedHub(
                  fundSubId,
                  entityId,
                  userGroupRole,
                  integrationHubInfoSignal,
                  onRemoveConnector = Observer { _ =>
                    IntegrationHubsConnectedInfoProvider.refetch()
                  }
                )()
              }
            ),
            child.maybe <-- IntegrationHubsConnectedInfoProvider.entityHubsInfoSignal
              .map(_.nonEmpty)
              .map { nonEmpty =>
                Option.when(nonEmpty && userGroupRole.isAdmin) {
                  ButtonL(
                    onClick = showAddIntegrationModalVar.writer.contramap(_ => true)
                  )("Add integration")
                }
              }
          )
      ),
      Option.when(userGroupRole.isAdmin) {
        AddIntegrationModal(
          fundSubId,
          showAddIntegrationModalVar.signal,
          onCloseRefetch = Observer { shouldRefetch =>
            showAddIntegrationModalVar.set(false)
            if (shouldRefetch) {
              IntegrationHubsConnectedInfoProvider.refetch()
            }
          }
        )()
      }
    )
  }

  private def renderEmptyState = {
    div(
      tw.py24.borderAll.borderGray2.rounded4.bgGray1,
      NonIdealStateL(
        icon = div(
          tw.wPx32.hPx32.mr8,
          tw.borderAll.rounded3.borderGray6.bgGray6,
          tw.flex.itemsCenter.justifyAround,
          div(
            tw.textGray0,
            IconL(
              name = Val(Icon.Glyph.Key)
            )()
          )
        ),
        title = "No integrations found",
        description = "Click \"Add Integration\" to get started.",
        action = if (userGroupRole.isAdmin) {
          ButtonL(
            style = ButtonL.Style.Full(color = ButtonL.Color.Primary),
            onClick = showAddIntegrationModalVar.writer.contramap(_ => true)
          )("Add integration")
        } else {
          emptyNode
        }
      )()
    )
  }

}
