// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.preview

import com.raquo.laminar.api.L.*

import anduin.frontend.AirStreamUtils
import anduin.id.fundsub.FundSubLpId
import anduin.id.notification.NotificationId
import anduin.model.common.user.UserId
import anduin.model.id.notification.NotificationSpaceId
import anduin.model.notichannel.UserNotificationChannels
import anduin.notification.{NotificationClient, NotificationUtils}
import anduin.notification.NotificationEndpoints.GetNotificationBySpaceParams
import anduin.protobuf.notification.NotificationModel
import anduin.token.AuthenticationTokenService
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.notiCenter.NotificationCenter
import com.anduin.stargazer.service.account.AccountUtils

private[preview] case class FetchCommentNotificationsL(
  lpId: FundSubLpId,
  renderChildren: Signal[Option[List[FetchCommentNotificationsL.Notification]]] => HtmlElement
) {

  private val lastNotificationSignal: Signal[Option[(UserId, NotificationCenter.Watch)]] =
    AccountUtils.currentUserIdObs.flatMapSwitch {

      _.fold[Signal[Option[(UserId, NotificationCenter.Watch)]]](Signal.fromValue(None))(userId =>
        NotificationCenter
          .eventSignal(UserNotificationChannels.notiMessage(userId))
          .map(watch => Some((userId, watch)))
      )
    }.distinct

  private val notificationSpaceId = NotificationSpaceId
    .channelSystemNotificationSpaceId(lpId.parent)
    .addSuffix(NotificationSpaceId.generateSuffixFromRadixId(lpId))

  private val fetchNotificationsEventBus = new EventBus[UserId]
  private val notificationsVar = Var(Option.empty[List[FetchCommentNotificationsL.Notification]])

  private def filterNotification(model: NotificationModel, userId: UserId) = {
    !NotificationUtils.isUserSeenNotification(userId, model) &&
    (
      (model.notificationType.isAmlKycComment &&
        model.notificationData.asMessage.sealedValue.amlKycCommentNotificationData.isDefined) ||
        (model.notificationType.isFormComment &&
          model.notificationData.asMessage.sealedValue.formCommentNotificationData.isDefined)
    )
  }

  private def fetchNotifications(userId: UserId) = {
    AirStreamUtils.taskToStream {
      NotificationClient
        .getUserNotificationsBySpace(GetNotificationBySpaceParams(notificationSpaceId))
        .map {
          _.fold(
            ex => {
              scribe.warn(s"Error when getting notifications for space $notificationSpaceId", ex)
              Option(List.empty)
            },
            response => {
              val notifications = response.notifications
                .filter { model =>
                  filterNotification(model, userId)
                }
                .flatMap { model =>
                  model.notificationId.map(id => FetchCommentNotificationsL.Notification(id, model))
                }
              Option(notifications)
            }
          )
        }
    }
  }

  def apply(): HtmlElement = {
    renderChildren(notificationsVar.signal).amend(
      onMountCallback { _ =>
        ZIOUtils.runAsyncAndForget(
          ZIOUtils.traverseOptionUnit(AuthenticationTokenService.getAuthenticationTokenClaim().map(_.userId)) {
            userId =>
              NotificationCenter.subscribe(Seq(UserNotificationChannels.notiMessage(userId)))
          }
        )
      },
      child <-- lastNotificationSignal.map {
        _.fold[Node](emptyNode) { case (userId, _) =>
          div(
            fetchNotificationsEventBus.events
              .flatMapSwitch(fetchNotifications) --> Observer[Option[List[FetchCommentNotificationsL.Notification]]] {
              notifications =>
                notificationsVar.set(notifications)
            },
            onMountCallback { _ =>
              fetchNotificationsEventBus.emit(userId)
            }
          )
        }
      }
    )
  }

}

private[preview] object FetchCommentNotificationsL {

  case class Notification(id: NotificationId, model: NotificationModel)
}
