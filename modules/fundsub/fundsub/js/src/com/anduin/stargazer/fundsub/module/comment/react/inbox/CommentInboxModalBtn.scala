// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.react.inbox

import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.modal.{Modal, ModalBody}
import design.anduin.components.progress.CircleIndicator
import design.anduin.components.progress.react.CircleIndicatorR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import design.anduin.table.TaskUtils
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import anduin.id.fundsub.FundSubId
import anduin.id.issuetracker.IssueId
import anduin.stargazer.service.formcomment.FormCommentCommons.{GetFundCommentDualThreadsParams, NewInboxTab}
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.comment.models.InboxLocationParams

private[fundsub] final case class CommentInboxModalBtn(
  fundId: FundSubId,
  initialSelectedTabOpt: Option[NewInboxTab] = None,
  initialSelectedThreadOpt: Option[IssueId] = None,
  defaultIsOpened: Boolean = false,
  renderTarget: Callback => VdomNode,
  onClose: Callback = Callback.empty
) {
  def apply(): VdomElement = CommentInboxModalBtn.component(this)
}

private[fundsub] object CommentInboxModalBtn {
  private type Props = CommentInboxModalBtn

  private sealed trait State derives CanEqual

  private object LoadingLocation extends State

  private final case class LocationLoaded(
    location: InboxLocationParams = InboxLocationParams()
  ) extends State

  private object LoadingFailed extends State

  private def renderLoadingLocation = ModalBody()(
    <.div(
      tw.hPc100.flex.flexCol.itemsCenter.justifyCenter,
      <.div(
        tw.mb16.textPrimary3,
        CircleIndicatorR(
          size = CircleIndicator.Size.Px48
        )()
      ),
      <.div(
        tw.textGray3.mt16,
        "Loading comments"
      )
    )
  )

  private def renderLoadingFailed = ModalBody()(
    <.div(
      tw.hPc100.flex.itemsCenter.justifyCenter.textWarning3,
      <.div(
        tw.mb16.textDanger3,
        IconR(
          name = Icon.Glyph.Error,
          size = Icon.Size.Px32
        )()
      ),
      <.div(
        tw.textWarning3,
        "Failed to load comments, please try again!"
      )
    )
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      Modal(
        renderTarget = props.renderTarget,
        defaultIsOpened = props.defaultIsOpened,
        renderContent = onClose =>
          state match {
            case LoadingLocation => renderLoadingLocation
            case LoadingFailed   => renderLoadingFailed
            case LocationLoaded(location) =>
              CommentInboxModalBodyR(
                fundId = props.fundId,
                presetLocationOpt = Option(location),
                onClose = onClose
              )()
          },
        afterUserOpen = loadLocation,
        afterUserClose = props.onClose,
        size = Modal.Size(
          Modal.Width.Full,
          Modal.Height.Full
        )
      )()
    }

    def loadLocation: Callback = {
      val findLocationCallback = for {
        props <- scope.props
        lpIdOpt = props.initialSelectedTabOpt.flatMap {
          case NewInboxTab.IndividualInvestorTab(lpId) => Option(lpId)
          case _                                       => None
        }
        _ <- lpIdOpt.fold[Callback] {
          scope.setState(
            LocationLoaded(
              InboxLocationParams(
                tab = props.initialSelectedTabOpt.getOrElse(NewInboxTab.AllCommentsTab),
                selectedThreadIdOpt = props.initialSelectedThreadOpt
              )
            )
          )
        } { lpId =>
          TaskUtils.toReactCallback {
            FundSubEndpointClient
              .getFundCommentAnchorPoints(
                GetFundCommentDualThreadsParams(
                  fundId = lpId.parent,
                  inboxTab = NewInboxTab.IndividualInvestorTab(lpId)
                )
              )
              .map(
                _.fold(
                  _ => scope.modState(_ => LoadingFailed),
                  response => {
                    scope.setState(
                      LocationLoaded(
                        InboxLocationParams(
                          tab = NewInboxTab.IndividualInvestorTab(lpId),
                          // TODO(comment) @tuananhtd: use correct page
                          page = 1,
                          selectedThreadIdOpt = props.initialSelectedThreadOpt
                        )
                      )
                    )
                  }
                )
              )
          }
        }
      } yield ()

      scope.setState(
        LoadingLocation,
        findLocationCallback
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState[State](LoadingLocation)
    .renderBackend[Backend]
    .componentDidMount { scope =>
      Callback.when(scope.props.defaultIsOpened) {
        scope.backend.loadLocation
      }
    }
    .build

}
