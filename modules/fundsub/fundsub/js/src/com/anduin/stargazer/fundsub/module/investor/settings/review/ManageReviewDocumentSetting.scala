// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.review

import java.time.Instant

import design.anduin.components.modal.Modal
import design.anduin.components.portal.PortalUtils
import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.components.switcher.react.SwitcherR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.TimerSupport
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.endpoint.graphql.FundAdminInfoWithInvitationStatus
import anduin.fundsub.endpoint.reviewpackage.FundSubReviewPackageData
import anduin.id.entity.EntityId
import anduin.id.fundsub.FundSubId
import anduin.protobuf.fundsub.LpFlowType
import com.anduin.stargazer.fundsub.module.investor.permission.WithGroupMembers
import com.anduin.stargazer.fundsub.module.investor.reviewpackage.{EnableReviewPackageModal, RemoveReviewPackageModal}
import com.anduin.stargazer.fundsub.module.investor.settings.{FundSubSettingTab, SettingSwitchTemplate}
import com.anduin.stargazer.util.date.DateCalculator

private[settings] final case class ManageReviewDocumentSetting(
  fundSubId: FundSubId,
  entityId: EntityId,
  adminInfos: Seq[FundAdminInfoWithInvitationStatus],
  reviewPackageData: Option[FundSubReviewPackageData],
  onNavigateSettingGroup: FundSubSettingTab.SettingsGroup => Callback,
  lpFlowType: LpFlowType
) {
  def apply(): VdomElement = ManageReviewDocumentSetting.component(this)
}

private[settings] object ManageReviewDocumentSetting {

  private type Props = ManageReviewDocumentSetting

  private final case class Settings(
    enabled: Boolean,
    isEnabledUnsignedReviewSetting: Boolean,
    at: Instant
  )

  private final case class State(
    optimisticUpdateSetting: Option[Settings] = None
  )

  private final case class Backend(scope: BackendScope[Props, State]) extends TimerSupport {

    private def isReviewFlowEnabled(props: Props, state: State) = {
      state.optimisticUpdateSetting
        .map(_.enabled)
        .getOrElse(
          props.reviewPackageData.exists(_.isEnabled)
        )
    }

    private def getIsEnabledUnsignedReviewSetting(props: Props, state: State) = {
      state.optimisticUpdateSetting
        .map(_.isEnabledUnsignedReviewSetting)
        .getOrElse(
          props.reviewPackageData.exists(_.isEnabledUnsignedReviewSetting)
        )
    }

    def render(state: State, props: Props): VdomElement = {
      val reviewFlowEnabled = isReviewFlowEnabled(props, state)
      SettingSwitchTemplate(
        label = "Document review before countersignature",
        description = () =>
          Option(
            <.div(
              <.div(
                tw.mr4,
                "Assign team members to review and approve investor documents before requesting countersignature"
              ),
              TagMod.when(reviewFlowEnabled && props.lpFlowType == LpFlowType.Flexible)(
                <.div(tw.mt20, renderOptions(props, state))
              )
            )
          ),
        renderSwitch = () => renderSwitch(props, state),
        renderContent = () => None
      )()
    }

    private def renderOptions(props: Props, state: State) = {
      val isEnabled = isReviewFlowEnabled(props, state)
      val isEnabledUnsignedReviewSetting = getIsEnabledUnsignedReviewSetting(props, state)
      ReviewOptionsSetting(
        fundSubId = props.fundSubId,
        isEnabledUnsignedReviewSetting = isEnabledUnsignedReviewSetting,
        onChangeOption = option =>
          optimisticUpdate(
            isEnabled,
            option
          ),
        onFailed = clearOptimisticUpdate(None)
      )()
    }

    private def renderSwitch(props: Props, state: State) = {
      val reviewFlowEnabled = isReviewFlowEnabled(props, state)
      val isEnabledUnsignedReviewSetting = getIsEnabledUnsignedReviewSetting(props, state)
      if (reviewFlowEnabled) {
        Modal(
          title = "Remove document review before countersignature?",
          isClosable = Option(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false)),
          renderTarget = openModal =>
            SwitcherR(
              isChecked = true,
              onChange = _ => openModal
            )(),
          size = Modal.Size(Modal.Width.Px600),
          renderContent = closeModal =>
            RemoveReviewPackageModal(
              fundSubId = props.fundSubId,
              entityId = props.entityId,
              closeModal = closeModal,
              onDisabled = optimisticUpdate(isEnabled = false, isEnabledUnsignedReviewSetting)
            )()
        )()
      } else {
        Modal(
          title = "Assign reviewers",
          isClosable = Option(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false)),
          renderTarget = openModal =>
            SwitcherR(
              isChecked = false,
              onChange = _ => openModal
            )(),
          size = Modal.Size(Modal.Width.Px600),
          renderContent = closeModal =>
            WithGroupMembers(props.fundSubId) { groupMemberData =>
              if (groupMemberData.isGetting) {
                BlockIndicatorR()()
              } else {
                EnableReviewPackageModal(
                  fundSubId = props.fundSubId,
                  adminInfos = groupMemberData.groupMembers,
                  closeModal = closeModal,
                  onEnabled = _ => optimisticUpdate(isEnabled = true, isEnabledUnsignedReviewSetting)
                )()
              }
            }
        )()
      }
    }

    private def clearOptimisticUpdate(timestampOpt: Option[Instant]) = {
      for {
        state <- scope.state
        _ <- timestampOpt.fold(
          scope.modState(_.copy(optimisticUpdateSetting = None))
        ) { timestamp =>
          Callback.when(
            state.optimisticUpdateSetting
              .exists(_.at.compareTo(timestamp) == 0)
          ) {
            scope.modState(_.copy(optimisticUpdateSetting = None))
          }
        }
      } yield ()
    }

    def optimisticUpdate(
      isEnabled: Boolean,
      isEnabledUnsignedReview: Boolean
    ): Callback = {
      for {
        now <- CallbackTo.lift(() => DateCalculator.instantNow)
        _ <- scope.modState(
          _.copy(
            optimisticUpdateSetting = Option(
              Settings(
                enabled = isEnabled,
                isEnabledUnsignedReview,
                at = now
              )
            )
          ),
          setTimeoutMs(clearOptimisticUpdate(Some(now)), 10000)
        )
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .configure(TimerSupport.install)
    .build

}
