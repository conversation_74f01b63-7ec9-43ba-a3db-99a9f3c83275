// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.ria

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL}
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.ria.ResendFundAdvisorInvitationParams
import anduin.fundsub.endpoint.ria.RiaFundGroupState.InvitedAdvisor
import anduin.id.fundsub.ria.FundSubRiaGroupId
import com.anduin.stargazer.fundsub.client.FundSubRiaEndpointClient

final case class ResendAdvisorInvitationConfirmModal(
  fundSubRiaGroupId: FundSubRiaGroupId,
  toResendInvitationAdvisor: InvitedAdvisor,
  onClose: Observer[Unit],
  onDoneResendAdvisorInvitation: Observer[Unit]
) {

  private val isResendingInvitationVar = Var(false)
  private val isResendingInvitationSignal = isResendingInvitationVar.signal.distinct

  private val resendAdvisorEvent = new EventBus[Unit]

  def apply(): Node = {
    div(
      ModalBodyL(
        span("An invitation will be sent again to "),
        span(tw.fontSemiBold, toResendInvitationAdvisor.info.userInfo.fullNameString),
        span(s" (${toResendInvitationAdvisor.info.userInfo.emailAddressStr})")
      ),
      ModalFooterWCancelL(onClose)(
        ButtonL(
          style = ButtonL.Style.Full(
            color = ButtonL.Color.Primary,
            isBusy = isResendingInvitationSignal
          ),
          onClick = resendAdvisorEvent.toObserver.contramap(_ => ())
        )("Send").amend(
          resendAdvisorEvent.events.flatMapSwitch { _ =>
            resendFundAdvisorInvitation()
          } --> Observer.empty
        )
      )
    )
  }

  private def resendFundAdvisorInvitation() = {
    AirStreamUtils.taskToStream(
      for {
        _ <- ZIO.succeed(isResendingInvitationVar.set(true))
        _ <- FundSubRiaEndpointClient
          .resendFundAdvisorInvitation(
            ResendFundAdvisorInvitationParams(
              fundSubRiaGroupId = fundSubRiaGroupId,
              advisor = toResendInvitationAdvisor.info.userId
            )
          )
          .map(
            _.fold(
              error => {
                isResendingInvitationVar.set(false)
                Toast.error("Failed to resend invitation")
              },
              res => {
                isResendingInvitationVar.set(false)
                onDoneResendAdvisorInvitation.onNext(())
                onClose.onNext(())
                Toast.success("Invitation resent")
              }
            )
          )
      } yield ()
    )
  }

}
