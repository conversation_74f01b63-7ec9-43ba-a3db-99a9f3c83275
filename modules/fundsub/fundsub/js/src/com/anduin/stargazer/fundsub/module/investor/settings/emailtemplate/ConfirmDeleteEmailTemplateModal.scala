// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.emailtemplate

import anduin.frontend.AirStreamUtils
import anduin.id.fundsub.FundSubEmailTemplateId
import anduin.fundsub.endpoint.emailtemplate.{DeleteFundSubEmailTemplateParams, FundSubEmailTemplate}
import anduin.protobuf.fundsub.FundSubEvent
import com.anduin.stargazer.fundsub.client.FundSubEmailTemplateEndpointClient
import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL, ModalL}
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*

private[emailtemplate] final case class ConfirmDeleteEmailTemplateModal(
  renderTarget: Observer[Unit] => Node,
  template: FundSubEmailTemplate,
  emailType: FundSubEvent,
  onClose: Observer[Unit],
  onComplete: Observer[FundSubEmailTemplateId]
) {

  private val isDeletingVar = Var(false)
  private val deleteEmailTemplateEventBus = new EventBus[Unit]

  def apply(): HtmlElement = {
    ModalL(
      renderTitle = _ => "Delete email template",
      afterUserClose = onClose,
      renderTarget = renderTarget,
      renderContent = close =>
        div(
          ModalBodyL(
            span("Are you sure you want to delete the invitation template "),
            span(tw.fontSemiBold, template.name),
            span("?")
          ),
          ModalFooterWCancelL(
            cancel = onClose.contramap(_ => close.onNext(()))
          )(
            ButtonL(
              style = ButtonL.Style.Full(color = ButtonL.Color.Danger, isBusy = isDeletingVar.signal),
              onClick = deleteEmailTemplateEventBus.toObserver.contramap(_ => ())
            )("Delete template").amend(
              deleteEmailTemplateEventBus.events.flatMapSwitch { _ =>
                isDeletingVar.set(true)
                deleteTemplate()
              } --> Observer.empty
            )
          )
        )
    )()
  }

  private def deleteTemplate(): EventStream[Unit] = {
    AirStreamUtils.taskToStream {
      for {
        _ <- FundSubEmailTemplateEndpointClient
          .deleteEmailTemplate(
            DeleteFundSubEmailTemplateParams(
              id = template.id,
              emailType = emailType
            )
          )
          .map(
            _.fold(
              _ => {
                isDeletingVar.set(false)
                Toast.error("Failed to delete email template")
              },
              res => {
                isDeletingVar.set(false)
                onComplete.onNext(template.id)
                Toast.success("Email template deleted successfully")
              }
            )
          )
      } yield ()
    }
  }

}
