// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.notify

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.checkbox.CheckboxL
import design.anduin.components.editor.OnChangeData
import design.anduin.components.editor.laminar.{BulletListButton, NumberListButton, RichEditorL, TextFormatButton}
import design.anduin.components.field.laminar.FieldL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterL, ModalHeaderL}
import design.anduin.components.progress.Skeleton
import design.anduin.components.progress.laminar.{BlockIndicatorL, SkeletonL}
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.style.tw.*
import org.scalajs.dom
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.admin.FundAdminSendNotifyNewCommentsToInvestorParams
import anduin.fundsub.endpoint.lp.UserEmailTemplate
import anduin.id.entity.EntityId
import anduin.id.fundsub.FundSubLpId
import anduin.model.common.user.UserId
import anduin.protobuf.fundsub.FundSubEvent
import anduin.stargazer.service.formcomment.FormCommentCommons.CommentThreadWithMetaData
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

private[notify] case class ReviewStepL(
  entitySignal: Signal[Option[EntityId]],
  fundSubLpId: FundSubLpId,
  selectedThreads: List[CommentThreadWithMetaData],
  onClose: Observer[Unit],
  onGoToPreviousStep: Observer[Unit]
) {

  private val selectedRecipientsVar = Var(Set.empty[UserId])
  private val subjectVar = Var("")
  private val messageVar = Var("")
  private val ctaVar = Var("")
  private val isSendingVar = Var(false)

  private val EmailTemplateEvent = FundSubEvent.notifyCommentsToInvestor
  private val emailTemplateVar = Var(UserEmailTemplate(EmailTemplateEvent))

  private def handleSelectRecipient(userId: UserId, select: Boolean) = {
    selectedRecipientsVar.update { selectedRecipients =>
      if (select) {
        selectedRecipients + userId
      } else {
        selectedRecipients - userId
      }
    }
  }

  private def handleSendEmail() = {
    val emailTemplate = emailTemplateVar
      .now()
      .copy(
        body = messageVar.now(),
        primaryCTA = ctaVar.now(),
        subject = subjectVar.now()
      )

    AirStreamUtils.taskToStreamDEPRECATED {
      for {
        _ <- ZIO.succeed(isSendingVar.set(true))
        _ <- FundSubEndpointClient.sendNotifyNewCommentsToInvestor(
          FundAdminSendNotifyNewCommentsToInvestorParams(
            fundSubLpId = fundSubLpId,
            receivers = selectedRecipientsVar.now().toSeq,
            emailTemplate = Option(emailTemplate),
            attachedComments = selectedThreads.map(_.commentThread)
          )
        )
        _ <- ZIO.succeed(isSendingVar.set(false))
        _ <- ZIO.logInfo("on send email")
        _ <- ZIO.succeed(onClose.onNext(()))
      } yield ()
    }
  }

  private def renderRecipients() = {
    FetchRecipients(
      fundSubLpId = fundSubLpId,
      renderChildren = _.fold(
        div(
          SkeletonL(
            height = "40px",
            width = "200px",
            shape = Skeleton.Shape.Rounded
          )()
        )
      ) { recipients =>
        div(
          recipients.map { recipient =>
            div(
              tw.mb12,
              CheckboxL(
                isChecked = selectedRecipientsVar.signal.map(_.contains(recipient.userId)),
                onChange = Observer[Boolean](handleSelectRecipient(recipient.userId, _))
              )(
                div(
                  tw.flex.itemsCenter,
                  div(
                    tw.mr4,
                    recipient.fullName
                  ),
                  Option.when(recipient.email.nonEmpty) {
                    div(
                      tw.textGray6,
                      s"(${recipient.email})"
                    )
                  }
                )
              )
            )
          }
        )
      }
    )()
  }

  private def renderMessageEditor(initialValue: String) = {
    RichEditorL(
      initialValue = initialValue,
      render = renderProps => {
        div(
          tw.borderAll.border1.borderGray3,
          // Toolbar
          div(
            tw.flex.itemsCenter.flexWrap.p4,
            tw.borderBottom.border1.borderGray3,
            div(
              tw.mr2,
              TextFormatButton(
                editorInstance = renderProps.editorInstance,
                formatType = TextFormatButton.FormatType.Bold
              )()
            ),
            div(
              tw.mr2,
              TextFormatButton(
                editorInstance = renderProps.editorInstance,
                formatType = TextFormatButton.FormatType.Italic
              )()
            ),
            div(
              tw.mr2,
              TextFormatButton(
                editorInstance = renderProps.editorInstance,
                formatType = TextFormatButton.FormatType.Underline
              )()
            ),
            div(
              tw.mr2,
              TextFormatButton(
                editorInstance = renderProps.editorInstance,
                formatType = TextFormatButton.FormatType.StrikeThrough
              )()
            ),
            div(
              tw.mr2,
              BulletListButton(
                editorInstance = renderProps.editorInstance
              )()
            ),
            div(
              tw.mr2,
              NumberListButton(
                editorInstance = renderProps.editorInstance
              )()
            )
          ),
          // Editor content
          renderProps.editorNode
        )
      },
      onChange = Observer[OnChangeData] { data =>
        messageVar.set(data.value)
      }
    )()
  }

  private def renderEmailTemplate() = {
    FetchEmailTemplateL(
      event = EmailTemplateEvent,
      fundSubLpId = fundSubLpId,
      renderChildren = _.fold(
        BlockIndicatorL(
          title = Val(Option("Load email template"))
        )()
      ) { emailTemplate =>
        div(
          div(
            tw.mb16,
            FieldL(
              label = Option("Subject")
            )(
              TextBoxL(
                isAutoFocus = true,
                value = subjectVar.signal,
                onChange = subjectVar.writer
              )()
            )
          ),
          div(
            tw.mb16,
            FieldL(
              label = Option("Message"),
              requirement = FieldL.Requirement.Optional
            )(renderMessageEditor(emailTemplate.body))
          ),
          div(
            FieldL(
              label = Option("Email call-to-action")
            )(
              TextBoxL(
                value = ctaVar.signal,
                onChange = ctaVar.writer
              )()
            )
          )
        )
      },
      onDidFetch = Observer[UserEmailTemplate] { data =>
        emailTemplateVar.set(data)
        subjectVar.set(data.subject)
        messageVar.set(data.body)
        ctaVar.set(data.primaryCTA)
      }
    )()
  }

  def apply(): HtmlElement = {
    div(
      width := "600px",
      ModalHeaderL(
        title = "Review and send",
        close = onClose
      )(),
      div(
        tw.borderTop.borderBottom.border1.borderGray3.mt20,
        ModalBodyL(
          ListAttachedCommentsL(
            entitySignal = entitySignal,
            fundSubLpId = fundSubLpId,
            threads = selectedThreads
          )(),
          div(tw.my16.wPc100.bgGray3.hPx1),
          div(
            tw.mb16,
            FieldL(
              label = Option("Recipients")
            )(renderRecipients())
          ),
          renderEmailTemplate()
        )
      ),
      ModalFooterL(
        div(
          tw.pt24.flex.itemsCenter.justifyBetween,
          ButtonL(
            style = ButtonL.Style.Full(color = ButtonL.Color.Gray0),
            onClick = Observer[dom.MouseEvent] { _ =>
              onGoToPreviousStep.onNext(())
            }
          )("Back"),
          ButtonL(
            isDisabled = isSendingVar.signal.combineWith(selectedRecipientsVar.signal).mapN {
              case (isSending, selectedRecipients) =>
                isSending || selectedRecipients.isEmpty
            },
            style = ButtonL.Style.Full(color = ButtonL.Color.Primary),
            onClick = Observer[dom.MouseEvent] { _ =>
              handleSendEmail()
              ()
            }
          )("Send")
        )
      )
    )
  }

}
