// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.customfundid

import design.anduin.components.button.Button
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.clipboard.react.CopyToClipboardR
import design.anduin.components.icon.Icon
import design.anduin.components.modal.Modal
import design.anduin.components.portal.PortalUtils
import design.anduin.components.switcher.react.SwitcherR
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.components.wrapper.TargetWrapper
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import zio.ZIO

import anduin.fundsub.endpoint.lp.UpdateCustomFundIdSettingParams
import anduin.id.fundsub.FundSubId
import anduin.protobuf.fundsub.CustomFundIdSetting
import anduin.service.GeneralServiceException
import anduin.utils.StringUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.investor.settings.SettingSwitchTemplate
import com.raquo.laminar.api.L.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.module.copy.WithFundSubCopyR

private[settings] final case class ManageCustomFundIdSetting(
  fundSubId: FundSubId,
  initialCustomFundIdSetting: CustomFundIdSetting
) {

  def apply(): VdomElement = {
    <.div(
      WithFundSubCopyR(
        renderer = { customFundIdWording =>
          ManageCustomFundIdSetting.component(
            ManageCustomFundIdSetting.Props(
              fundSubId,
              initialCustomFundIdSetting,
              customFundIdWording.value
            )
          )
        }
      )(_.fundManagementSide.settingsTab.customFundIdWording)
    )
  }

}

private[settings] object ManageCustomFundIdSetting {

  private final case class Props(
    fundSubId: FundSubId,
    initialCustomFundIdSetting: CustomFundIdSetting,
    customFundIdWording: String
  )

  private final case class State(
    setting: CustomFundIdSetting,
    pendingSetting: Option[CustomFundIdSetting] = None
  ) {
    lazy val isUpdating: Boolean = pendingSetting.nonEmpty
    lazy val displaySetting: CustomFundIdSetting = pendingSetting.getOrElse(setting)
  }

  private final case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      <.div(
        ComponentUtils.testId(ManageCustomFundIdSetting, "CustomFundIdContainer"),
        SettingSwitchTemplate(
          label = s"Show ${props.customFundIdWording}",
          renderSwitch = () => renderSwitch(props, state),
          description = () => Some(renderDescription(props)),
          renderContent = () =>
            Option.when(state.displaySetting.isEnabled) {
              renderCustomFundId(props, state)
            }
        )()
      )
    }

    private def renderDescription(props: Props) =
      <.div(
        tw.mt4.textGray7,
        s"Show ${StringUtils.getDeterminer(props.customFundIdWording)} ",
        <.span(
          tw.fontSemiBold,
          props.customFundIdWording
        ),
        " on all dashboards in the fund"
      )

    private def renderSwitch(props: Props, state: State) = {
      if (!state.displaySetting.isEnabled) {
        Modal(
          title = s"Create ${props.customFundIdWording}",
          renderTarget = openModal => switch(state, openModal),
          renderContent = closeModal =>
            EditCustomFundIdModal(
              fundSubId = props.fundSubId,
              customFundIdWording = props.customFundIdWording,
              editType = EditCustomFundIdModal.EditType.EnableNew,
              initialCustomFundId = state.displaySetting.customFundId,
              onUpdated = customFundId =>
                scope.modState(
                  _.copy(setting = CustomFundIdSetting(isEnabled = true, customFundId = customFundId)),
                  closeModal
                ),
              onCancel = closeModal
            )(),
          isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false))
        )()
      } else {
        switch(state, onDisableSetting)
      }
    }

    private def switch(state: State, onClick: Callback) = {
      SwitcherR(
        isChecked = state.displaySetting.isEnabled,
        onChange = _ => onClick,
        isDisabled = state.isUpdating
      )()
    }

    private def renderCustomFundId(props: Props, state: State) = {
      <.div(
        tw.border1.borderAll.borderGray3.rounded2.wFit,
        tw.flex.itemsCenter.px12.py8,
        <.div(
          ComponentUtils.testId(ManageCustomFundIdSetting, "CustomFundId"),
          tw.maxWPx512.truncate.textGray8.ml8,
          state.displaySetting.customFundId
        ),
        <.div(
          ComponentUtils.testId(ManageCustomFundIdSetting, "CopyButton"),
          tw.flexNone.ml16.wPx32.hPx32.overflowHidden,
          CopyToClipboardR(
            content = state.displaySetting.customFundId,
            renderTarget = content => {
              div(
                ButtonL(
                  style = ButtonL.Style.Minimal(icon = Option(Icon.Glyph.Duplicate)),
                  title = "Copy to clipboard"
                )(),
                content
              )
            },
            targetWrapper = TargetWrapper.Block
          )()
        ),
        <.div(
          tw.ml4,
          Modal(
            title = s"Edit ${props.customFundIdWording}",
            renderTarget = open =>
              TooltipR(
                renderTarget = <.div(
                  ComponentUtils.testId(ManageCustomFundIdSetting, "EditButton"),
                  Button(
                    style = Button.Style.Minimal(icon = Option(Icon.Glyph.Edit)),
                    onClick = open
                  )()
                ),
                renderContent = _("Edit")
              )(),
            renderContent = closeModal =>
              EditCustomFundIdModal(
                fundSubId = props.fundSubId,
                customFundIdWording = props.customFundIdWording,
                editType = EditCustomFundIdModal.EditType.EditExisting,
                initialCustomFundId = state.displaySetting.customFundId,
                onUpdated = customFundId =>
                  scope.modState(
                    _.copy(setting = CustomFundIdSetting(isEnabled = true, customFundId = customFundId)),
                    closeModal
                  ),
                onCancel = closeModal
              )(),
            isClosable = Some(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false))
          )()
        )
      )
    }

    private def onDisableSetting = {
      for {
        props <- scope.props
        state <- scope.state
        settingToUpdate = CustomFundIdSetting(
          customFundId = state.displaySetting.customFundId
        )
        _ <- scope.modState(
          _.copy(pendingSetting = Some(settingToUpdate)),
          ZIOUtils.toReactCallback(
            FundSubEndpointClient
              .updateCustomFundIdSetting(
                UpdateCustomFundIdSettingParams(
                  fundSubId = props.fundSubId,
                  isEnabled = settingToUpdate.isEnabled,
                  newCustomFundId = settingToUpdate.customFundId
                )
              )
              .catchAll(err => ZIO.succeed(Left(GeneralServiceException(err.getMessage))))
              .map(
                _.fold(
                  _ =>
                    scope.modState(
                      _.copy(pendingSetting = None),
                      Toast.errorCallback("Failed to update setting")
                    ),
                  _ =>
                    scope.modState(
                      _.copy(setting = settingToUpdate, pendingSetting = None)
                    )
                )
              )
          )
        )
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialStateFromProps { props =>
      State(setting = props.initialCustomFundIdSetting)
    }
    .renderBackend[Backend]
    .componentDidUpdate { scope =>
      Callback.when(scope.currentProps.initialCustomFundIdSetting != scope.prevProps.initialCustomFundIdSetting) {
        scope.modState(_.copy(setting = scope.currentProps.initialCustomFundIdSetting))
      }
    }
    .build

}
