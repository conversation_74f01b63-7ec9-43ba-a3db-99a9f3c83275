// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.contact.widget.tab

import anduin.contact.endpoint.{ContactGroupModel, ContactModel}
import anduin.contact.widget.component.FilterView.{ContactFilterProperties, FilterType}
import anduin.contact.widget.component.{ContactTable, FilterView}
import anduin.contact.widget.{ContactWidgetModel, ContactWidgetUtils}
import anduin.fundsub.endpoint.contact.{GetContactsWithSameEmailForGroupParams, ListContactsByEmailOrderParams}
import anduin.id.contact.ContactGroupId
import anduin.id.fundsub.FundSubId
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[widget] final case class ContactTabView(
  fundSubId: FundSubId,
  isVisible: Boolean,
  selectedContacts: Set[String],
  groupMap: Map[ContactGroupId, ContactGroupModel] = Map.empty,
  onContactSelected: Set[String] => Callback,
  onContactRemoved: Set[String] => Callback,
  onContactModelsLoaded: Seq[ContactModel] => Callback,
  onGroupModelsLoaded: Seq[ContactGroupModel] => Callback
) {
  def apply(): VdomElement = ContactTabView.component(this)
}

private[widget] object ContactTabView {

  type Props = ContactTabView

  private val ContactPerPage: Int = 20

  final case class State(
    loadedListContacts: Seq[ContactWidgetModel] = Seq.empty, // List of contacts by email order loaded so far
    hasMore: Boolean = true,
    isFetching: Boolean = false,
    filterProps: ContactFilterProperties = ContactFilterProperties(None, Seq.empty),
    searchResults: Seq[ContactWidgetModel] = Seq.empty,
    // groupId -> list of contacts that has email matches one of the contact within the groupId
    groupContacts: Map[ContactGroupId, Seq[ContactWidgetModel]] = Map.empty
  ) {

    val displayContacts: Seq[ContactWidgetModel] = ContactWidgetUtils.filterContacts(
      loadedListContacts,
      groupContacts,
      filterProps,
      searchResults
    )

  }

  final class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomNode = {
      val contacts = state.displayContacts.sortBy(_.email)
      val showLoadMore = state.hasMore && state.filterProps.isEmpty
      <.div(
        tw.flex.flexCol.flexFill,
        TagMod.unless(props.isVisible)(tw.hidden),
        FilterView(
          filterProperties = state.filterProps,
          groupMap = props.groupMap,
          onFilterRemoved = removeContactFilter,
          onSearchRemoved = removeContactSearch()
        )(),
        ContactTable(
          isFetching = state.isFetching,
          displayContacts = contacts,
          selectedContacts = props.selectedContacts,
          onContactsSelected = props.onContactSelected,
          onContactsRemoved = props.onContactRemoved,
          onGroupFilterSelected = groupId => addContactFilter(FilterType.ByGroup(groupId)),
          groupMap = props.groupMap,
          showLoadMore = showLoadMore,
          onLoadMore = Callback.when(showLoadMore)(loadMoreContacts())
        )()
      )
    }

    private def addContactFilter(filter: FilterType) = {
      scope.modState(
        state =>
          state.copy(filterProps = state.filterProps.copy(filters = (state.filterProps.filters :+ filter).distinct)),
        afterContactFilterChanged()
      )
    }

    private def afterContactFilterChanged() = {
      loadFilterGroupContactsIfNeeded()
    }

    // Load all contacts for selected filters groups. For optimization purpose, don't load anything if search
    // result is active, or only load the contacts for the first filter group
    private def loadFilterGroupContactsIfNeeded() = {
      for {
        state <- scope.state
        filters = state.filterProps
        firstGroupFilterOpt = ContactWidgetUtils.getFilterByGroups(filters.filters).headOption
        _ <- Callback.traverseOption(firstGroupFilterOpt) { firstGroupFilter =>
          val shouldLoadFirstFilterGroup = !state.groupContacts.contains(firstGroupFilter)
          Callback.when(filters.nonEmpty && filters.search.isEmpty && shouldLoadFirstFilterGroup) {
            loadAllContactsWithSameEmailForGroups(Seq(firstGroupFilter))
          }
        }
      } yield ()
    }

    private def loadAllContactsWithSameEmailForGroups(groupIds: Seq[ContactGroupId]) = {
      for {
        props <- scope.props
        state <- scope.state
        queryGroupIds = groupIds.filterNot(state.groupContacts.contains)
        _ <- Callback.when(queryGroupIds.nonEmpty) {
          for {
            _ <- startFetching()
            _ <- ZIOUtils.toReactCallback {
              FundSubEndpointClient
                .getContactsWithSameEmailForGroups(
                  GetContactsWithSameEmailForGroupParams(props.fundSubId, queryGroupIds)
                )
                .map {
                  _.fold(
                    _ => stopFetching(),
                    resp =>
                      for {
                        _ <- props.onContactModelsLoaded(resp.contacts)
                        _ <- props.onGroupModelsLoaded(resp.allParentGroups.values.toSeq)
                        _ <- scope.modState(
                          s =>
                            s.copy(
                              groupContacts = s.groupContacts ++
                                ContactWidgetUtils.constructGroupsContactWidgetModels(queryGroupIds, resp.contacts)
                            ),
                          stopFetching()
                        )
                      } yield ()
                  )
                }
            }
          } yield ()
        }
      } yield ()
    }

    def loadMoreContacts(): Callback = {
      for {
        props <- scope.props
        state <- scope.state
        lastEmailOpt = state.loadedListContacts.lastOption.map(_.email)
        _ <- startFetching()
        _ <- ZIOUtils.toReactCallback {
          FundSubEndpointClient
            .listContactsByEmailOrder(
              ListContactsByEmailOrderParams(
                props.fundSubId,
                lastEmailOpt,
                ContactPerPage
              )
            )
            .map {
              _.fold(
                _ => Toast.errorCallback("Failed to fetch contacts") >> stopFetching(),
                resp =>
                  for {
                    _ <- scope.modState(
                      s =>
                        s.copy(
                          loadedListContacts =
                            s.loadedListContacts ++ ContactWidgetUtils.constructContactWidgetModels(resp.contacts),
                          hasMore = resp.hasMore
                        ),
                      stopFetching()
                    )
                    _ <- props.onContactModelsLoaded(resp.contacts)
                    _ <- props.onGroupModelsLoaded(resp.allParentGroups.values.toSeq)
                  } yield ()
              )
            }
        }
      } yield ()
    }

    private def startFetching(): Callback = {
      scope.modState(_.copy(isFetching = true))
    }

    private def stopFetching(): Callback = {
      scope.modState(_.copy(isFetching = false))
    }

    private def removeContactFilter(filter: FilterType) = {
      scope.modState(
        s => s.copy(filterProps = s.filterProps.copy(filters = s.filterProps.filters.filterNot(_ == filter))),
        afterContactFilterChanged()
      )
    }

    private def removeContactSearch() = {
      scope.modState(
        s => s.copy(filterProps = s.filterProps.copy(search = None)),
        afterContactFilterChanged()
      )
    }

    def setSearchResult(contacts: Seq[ContactWidgetModel]): Callback = {
      scope.modState(_.copy(searchResults = contacts))
    }

    def setFilterProps(filterProps: ContactFilterProperties): Callback = {
      scope.modState(_.copy(filterProps = filterProps), afterContactFilterChanged())
    }

  }

  val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .componentDidMount(_.backend.loadMoreContacts())
    .build

}
