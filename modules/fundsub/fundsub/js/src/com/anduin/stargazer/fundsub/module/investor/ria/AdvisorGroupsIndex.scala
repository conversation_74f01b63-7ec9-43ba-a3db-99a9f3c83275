// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.ria

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.layout.HStack
import design.anduin.components.list.laminar.ListL
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.components.text.laminar.TruncateL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*

import anduin.id.fundsub.FundSubId
import anduin.id.fundsub.ria.FundSubRiaGroupId

private[ria] case class AdvisorGroupsIndex(
  fundSubId: FundSubId,
  isEnableRia: Boolean
) {

  private val selectedGroupIdOptVar = Var(Option.empty[FundSubRiaGroupId])
  private val selectedGroupSignal = selectedGroupIdOptVar.signal.distinct

  private def renderSidebar(
    refinedSelectedGroupSignal: Signal[Option[FundSubRiaGroupId]]
  ): HtmlElement = {
    div(
      child <-- FetchAdvisorGroups.groupsSignal.map { groups =>
        ListL(
          items = Seq(
            ListL.Item(
              icon = Option(Icon.Glyph.ViewGrid),
              isSelected = refinedSelectedGroupSignal.map(_.isEmpty).map(Option(_)),
              renderContent = () => s"All advisor entities (${groups.length})",
              onClick = Observer[Unit] { _ =>
                selectedGroupIdOptVar.set(None)
                FetchAdvisorGroups.onRefetch.onNext(())
              }
            ),
            ListL.Item.Divider
          ) ++ groups.sortBy(_.name).map { group =>
            val isDisabled = !group.canCreateOrder
            ListL.Item(
              icon = Option(
                if (isDisabled) {
                  Icon.Glyph.NoAccess
                } else {
                  Icon.Glyph.LightBulb
                }
              ),
              isSelected = selectedGroupSignal.map(_.contains(group.id)).map(Option(_)),
              renderContent = () => {
                div(
                  tw.flex.itemsCenter.gapX4,
                  TruncateL(target = span(group.name))(),
                  span(s"(${group.numberOfMembers})")
                )
              },
              onClick = selectedGroupIdOptVar.writer.contramap(_ => Option(group.id))
            )
          }
        )()
      }
    )
  }

  private def renderContent(
    refinedSelectedGroupSignal: Signal[Option[FundSubRiaGroupId]]
  ): HtmlElement = {
    val isGroupEmptySignal = FetchAdvisorGroups.groupsSignal.map(_.isEmpty)
    div(
      child <-- refinedSelectedGroupSignal.splitOption(
        project = (_, selectedGroupSignal) => {
          div(
            child <-- selectedGroupSignal.map { selectedGroupId =>
              FetchAdvisorGroup(
                fundSubRiaGroupId = selectedGroupId
              ) { renderAdvisorGroupProps =>
                div(
                  child <-- renderAdvisorGroupProps.isFetchingSignal.splitBoolean(
                    whenTrue = _ => BlockIndicatorL(title = Val(Option("Loading advisor entity ...")))(),
                    whenFalse = _ =>
                      div(
                        child <-- renderAdvisorGroupProps.groupSignal.map { group =>
                          AdvisorGroupDetails(
                            group = group,
                            groupsSignal = FetchAdvisorGroups.groupsSignal,
                            onRefetchGroup = renderAdvisorGroupProps.onRefetch,
                            onRefetchAllGroup = FetchAdvisorGroups.onRefetch
                          )()
                        }
                      )
                  )
                )
              }

            }
          )
        },
        ifEmpty = div(
          // Heading
          h2(
            tw.heading2.mb16,
            "Advisor entities"
          ),
          child <-- isGroupEmptySignal.splitBoolean(
            whenTrue = _ =>
              EmptyAdvisorGroups(
                fundSubId = fundSubId,
                onDoneAddAdvisorGroup = FetchAdvisorGroups.onRefetch.contramap(_ => ())
              )(),
            whenFalse = _ =>
              div(
                div(
                  tw.mb16,
                  AddAdvisorGroupModal(
                    fundSubId = fundSubId,
                    existingGroupNameSignal = FetchAdvisorGroups.groupsSignal.map(_.map(_.name)),
                    renderTarget = onOpen => {
                      ButtonL(
                        style = ButtonL.Style.Full(icon = Option(Icon.Glyph.Plus)),
                        onClick = onOpen.contramap(_ => ())
                      )("Add advisor entity")
                    },
                    onDoneAddAdvisorGroup = FetchAdvisorGroups.onRefetch.contramap(_ => ())
                  )()
                ),
                AdvisorGroupsTable(
                  fundSubId = fundSubId,
                  groupsSignal = FetchAdvisorGroups.groupsSignal,
                  onSelectAdvisorGroup = selectedGroupIdOptVar.writer.contramap(Option(_))
                )()
              )
          )
        )
      )
    )
  }

  def apply(): HtmlElement = {
    val refinedSelectedGroupSignal = selectedGroupIdOptVar.signal
      .combineWith(FetchAdvisorGroups.groupsSignal)
      .map { (selectedGroupIdOpt, groups) =>
        selectedGroupIdOpt.flatMap { selectedGroupId =>
          groups.find(_.id == selectedGroupId).map(_.id)
        }
      }
      .distinct
    div(
      if (isEnableRia) {
        div(
          onMountCallback { _ =>
            FetchAdvisorGroups.onRefetch.onNext(())
          },
          child <-- FetchAdvisorGroups.isFetchingSignal.splitBoolean(
            whenTrue = _ => BlockIndicatorL(title = Val(Option("Loading advisor entities ...")))(),
            whenFalse = _ =>
              HStack(
                modifier = HStack
                  .Modifier()
                  .main((ele: HtmlElement) => ele.amend(ComponentUtils.testIdL("AdvisorGroupsIndex")))
                  .content((ele: HtmlElement) => ele.amend(tw.flex.flexCol.p16.wPc100))
                  .toggleButton((ele: HtmlElement) => ele.amend(tw.p16))
                  .withLeftSidebar()
                  .withStickySidebar(width = Option(280))
              )(
                HStack.Slots.Sidebar(
                  div(
                    tw.p16,
                    ComponentUtils.testIdL(AdvisorGroupsIndex, "Navigation"),
                    renderSidebar(
                      refinedSelectedGroupSignal
                    )
                  )
                ),
                HStack.Slots.Content(
                  renderContent(
                    refinedSelectedGroupSignal
                  )
                )
              ).amend(
                onUnmountCallback { _ =>
                  Var.set(
                    selectedGroupIdOptVar -> None
                  )
                }
              )
          )
        )
      } else {
        RiaPromotionContent(fundSubId)()
      }
    )
  }

}
