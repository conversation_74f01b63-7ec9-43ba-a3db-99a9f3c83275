// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.notify

import com.raquo.laminar.api.L.*
import design.anduin.components.date.laminar.TimeAgoL
import design.anduin.components.text.laminar.TruncateL
import design.anduin.style.tw.*

import anduin.scalajs.pluralize.Pluralize
import anduin.stargazer.service.formcomment.FormCommentCommons.CommentThread
import com.anduin.stargazer.fundsub.module.comment.laminar.share.CommentContent

private[notify] case class SingleThreadItemL(
  thread: CommentThread
) {

  def apply(children: Node): HtmlElement = {
    val numReplies = thread.replies.size
    val commentInfo = thread.lastComment
    div(
      children,
      div(
        tw.mb8,
        div(
          tw.mb4,
          tw.flex.itemsCenter,
          // Name
          commentInfo.creatorInfoOpt.fold[Node](emptyNode) { userInfo =>
            TruncateL(
              target = {
                div(
                  tw.fontSemiBold.mr8,
                  userInfo.getDisplayName
                )
              }
            )()
          },
          // Date
          commentInfo.createdAt.fold[Node](emptyNode) { createdAt =>
            TimeAgoL(instant = createdAt)().amend(tw.textSmall.textGray6)
          }
        ),
        div(
          tw.lineClamp2,
          CommentContent(
            content = commentInfo.content,
            lpIdSignal = Val(thread.fundSubLpId),
            fundSubId = thread.fundSubLpId.parent,
            threadId = thread.id,
            commentId = commentInfo.commentId,
            createdAtOpt = commentInfo.createdAt,
            editedAtOpt = commentInfo.lastUpdatedAt,
            hasMentionSignal = Val(commentInfo.mentions.nonEmpty)
          )()
        )
      ),
      // Number of replies
      Option.when(numReplies > 0) {
        div(
          tw.textGray7.textSmall.inlineFlex.itemsCenter.justifyCenter,
          tw.borderAll.borderGray3.py2.px6.rounded4,
          Pluralize(
            "reply",
            numReplies,
            true
          )
        )
      }
    )
  }

}
