// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.emailtemplate

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.emailtemplate.{
  AnduinTemplate,
  CreateFundSubEmailTemplateParams,
  FundSubEmailTemplate,
  UpdateFundSubEmailTemplateParams
}
import anduin.id.fundsub.{FundSubEmailTemplateId, FundSubId}
import anduin.protobuf.fundsub.FundSubEvent
import anduin.utils.DateTimeUtils
import com.anduin.stargazer.fundsub.client.FundSubEmailTemplateEndpointClient
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils
import com.anduin.stargazer.fundsub.module.email.EmailTemplateEditorL
import com.anduin.stargazer.fundsub.module.email.EmailTemplateEditorL.EmailTemplate
import com.anduin.stargazer.fundsub.module.investor.settings.emailtemplate.EditEmailTemplateModal.*
import com.anduin.stargazer.fundsub.module.investor.settings.emailtemplate.EditEmailTemplateModal.ActionType.Create
import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.field.laminar.FieldL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterL, ModalL}
import design.anduin.components.switcher.laminar.SwitcherL
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*

private[emailtemplate] final case class EditEmailTemplateModal(
  fundSubId: FundSubId,
  emailType: FundSubEvent,
  templateNamesSignal: Signal[Seq[String]],
  initialIsDefault: Boolean,
  renderTarget: Observer[Unit] => Node,
  onComplete: Observer[OnComplete],
  onClose: Observer[Unit] = Observer.empty,
  action: ActionType = Create() // TODO: @huyha make this to signal to optimize performance
) {

  private val isDefaultVar = Var(initialIsDefault)

  private val templateNameVar = Var(action match {
    case ActionType.Create(_)      => ""
    case ActionType.Edit(template) => template.name
  })

  private val emailTemplateVar = Var(action match {
    case ActionType.Create(template) => template
    case ActionType.Edit(template) =>
      EmailTemplate(
        subject = template.subject,
        body = template.body,
        cta = template.cta
      )
  })

  private val isUpdatingVar = Var(false)

  private val editTemplateEventBus = new EventBus[Unit]

  def apply(): HtmlElement = {
    ModalL(
      size = ModalL.Size(width = ModalL.Width.Px720),
      renderTitle = _ =>
        action match {
          case _: ActionType.Create => "Create template"
          case _: ActionType.Edit   => "Edit template"
        },
      beforeClose = onClose,
      renderTarget = renderTarget,
      renderContent = renderModalContent
    )()
  }

  private def renderModalContent(close: Observer[Unit]) = {
    div(
      ModalBodyL(
        div(
          tw.spaceY16,
          div(
            tw.borderAll.borderGray3.rounded8.py8.px12,
            SwitcherL(
              isChecked = isDefaultVar.signal,
              onChange = isDefaultVar.writer
            )(
              renderSetDefaultSwitchDescription(emailType)
            )
          ),
          child <-- templateNamesSignal.map { templateNames =>
            FieldL(
              label = Option("Template name"),
              requirement = FieldL.Requirement.Required,
              validation = templateNameVar.signal.map { templateName =>
                if (validateTemplateName(templateName, templateNames)) {
                  FieldL.Validation.None
                } else {
                  FieldL.Validation.Invalid(s"$templateName already exists. Please enter a different name.")
                }
              }
            )(
              TextBoxL(
                value = templateNameVar.signal,
                onChange = templateNameVar.writer,
                placeholder = "Enter template name"
              )()
            )
          },
          EmailTemplateEditorL(
            templateSignal = emailTemplateVar.signal,
            onChange = emailTemplateVar.writer
          )()
        )
      ),
      ModalFooterL(
        div(
          tw.flex.justifyBetween,
          div(
            action match {
              case ActionType.Create(_) => emptyNode
              case ActionType.Edit(template) =>
                template.updatedAt.fold(
                  div(
                    tw.textGray7,
                    template.createdAt.map { createdAt =>
                      s"Created by ${template.updatedBy.fullName} on ${DateTimeUtils.formatInstant(createdAt, DateTimeUtils.DateAndTimeFormatter2)(
                          using DateTimeUtils.defaultTimezone
                        )}"
                    }
                  )
                ) { updatedAt =>
                  div(
                    tw.textGray7,
                    s"Updated by ${template.updatedBy.fullName} on ${DateTimeUtils.formatInstant(updatedAt, DateTimeUtils.DateAndTimeFormatter2)(
                        using DateTimeUtils.defaultTimezone
                      )}"
                  )
                }
            }
          ),
          div(
            tw.flex.spaceX8,
            ButtonL(
              onClick = close.contramap(_ => ())
            )("Cancel"),
            ButtonL(
              style = ButtonL.Style.Full(color = ButtonL.Color.Primary, isBusy = isUpdatingVar.signal),
              onClick = editTemplateEventBus.toObserver.contramap(_ => ()),
              isDisabled = emailTemplateVar.signal
                .combineWith(templateNameVar.signal, templateNamesSignal, isDefaultVar.signal)
                .map(validateTemplate)
                .invert
            )(action match {
              case _: ActionType.Create => "Create"
              case _: ActionType.Edit   => "Update"
            }).amend(
              editTemplateEventBus.events
                .withCurrentValueOf(templateNameVar.signal, emailTemplateVar.signal, isDefaultVar.signal)
                .flatMapSwitch { case (templateName, emailTemplate, isDefault) =>
                  isUpdatingVar.set(true)
                  action match {
                    case _: ActionType.Create =>
                      createEmailTemplate(
                        name = templateName,
                        template = emailTemplate,
                        isDefault = isDefault,
                        close = close
                      )
                    case ActionType.Edit(template) =>
                      updateEmailTemplate(
                        id = template.id,
                        name = templateName,
                        template = emailTemplate,
                        isDefault = isDefault,
                        close = close
                      )
                  }
                } --> Observer.empty
            )
          )
        )
      )
    )
  }

  private def validateTemplate(
    template: EmailTemplate,
    name: String,
    templateNames: Seq[String],
    isDefault: Boolean
  ) = {
    val (isContentChanged, isNameChanged) = action match {
      case ActionType.Edit(initialTemplate) =>
        val isContentChanged = initialTemplate.subject != template.subject.trim ||
          initialTemplate.body != template.body.trim ||
          initialTemplate.cta != template.cta.trim
        val isNameChanged = initialTemplate.name != name.trim
        (isContentChanged, isNameChanged)
      case _: ActionType.Create => (true, true)
    }
    val isDefaultChanged = initialIsDefault != isDefault
    val isNameEmpty = name.trim.isEmpty
    val isContentFilled = template.subject.trim.nonEmpty &&
      template.body.trim.nonEmpty &&
      template.cta.trim.nonEmpty

    isContentFilled && (isContentChanged || isNameChanged || isDefaultChanged) && !isNameEmpty && validateTemplateName(
      name,
      templateNames
    )

  }

  private def validateTemplateName(name: String, templateNames: Seq[String]) = {
    (action match {
      case _: ActionType.Create      => !templateNames.contains(name.trim)
      case ActionType.Edit(template) => name.trim == template.name || !templateNames.contains(name.trim)
    }) && name.trim != AnduinTemplate.AnduinTemplateName
  }

  private def updateEmailTemplate(
    id: FundSubEmailTemplateId,
    name: String,
    template: EmailTemplate,
    isDefault: Boolean,
    close: Observer[Unit]
  ): EventStream[Unit] = {
    AirStreamUtils.taskToStream {
      for {
        _ <- FundSubEmailTemplateEndpointClient
          .updateEmailTemplate(
            UpdateFundSubEmailTemplateParams(
              id = id,
              emailType = emailType,
              name = name,
              subject = template.subject,
              body = template.body,
              cta = template.cta,
              isDefaultTemplate = isDefault
            )
          )
          .map(
            _.fold(
              _ => {
                isUpdatingVar.set(false)
                Toast.error("Failed to update email template")
              },
              res => {
                isUpdatingVar.set(false)
                onComplete.onNext(OnComplete(res.template, isDefault))
                close.onNext(())
                Toast.success("Email template updated successfully")
              }
            )
          )
      } yield ()
    }
  }

  private def createEmailTemplate(
    name: String,
    template: EmailTemplate,
    isDefault: Boolean,
    close: Observer[Unit]
  ): EventStream[Unit] = {
    AirStreamUtils.taskToStream {
      for {
        _ <- FundSubEmailTemplateEndpointClient
          .createEmailTemplate(
            CreateFundSubEmailTemplateParams(
              fundSubId = fundSubId,
              emailType = emailType,
              name = name,
              subject = template.subject,
              body = template.body,
              cta = template.cta,
              isDefaultTemplate = isDefault
            )
          )
          .map(
            _.fold(
              _ => {
                isUpdatingVar.set(false)
                Toast.error("Failed to create email template")
              },
              res => {
                isUpdatingVar.set(false)
                onComplete.onNext(OnComplete(res.template, isDefault))
                close.onNext(())
                Toast.success("Email template created successfully")
              }
            )
          )
      } yield ()
    }
  }

}

private[emailtemplate] object EditEmailTemplateModal {

  final case class OnComplete(
    template: FundSubEmailTemplate,
    isDefault: Boolean
  )

  enum ActionType {
    case Create(initialTemplate: EmailTemplate = EmailTemplate())
    case Edit(initialTemplate: FundSubEmailTemplate)
  }

  def renderSetDefaultSwitchDescription(emailType: FundSubEvent): String = {
    emailType match {
      case FundSubEvent.inviteLp      => "Set as default for all new invitation emails"
      case FundSubEvent.countersigned => "Set as default for all new executed document distribution emails"
      case FundSubEvent.remindLpCompleteForm =>
        s"Set as default for all new ${FundSubCopyUtils.getFlowTerm.termInDocumentContext} reminder emails"
      case _ => "???"
    }
  }

}
