// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.contact

import scala.annotation.unused

import design.anduin.components.checkbox.Checkbox
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.contact.endpoint.ContactModel
import anduin.fundsub.endpoint.contact.FundSubContactInfo
import anduin.id.contact.ContactId
import anduin.id.fundsub.{FundSubContactGroupId, FundSubId}
import anduin.protobuf.fundsub.contact.FundSubContactGroup

final case class FundSubContactTable(
  fundSubId: FundSubId,
  enabledCustomId: Boolean,
  contactInfo: FundSubContactInfo,
  selectedContacts: Seq[ContactId],
  selectedGroupContacts: Seq[(FundSubContactGroupId, ContactId)],
  selectedEmptyGroups: Seq[FundSubContactGroupId],
  onToggleContact: (Seq[ContactId], Boolean) => Callback,
  onToggleGroup: (Seq[(FundSubContactGroupId, ContactId)], Boolean) => Callback,
  onToggleEmptyGroup: (Seq[FundSubContactGroupId], Boolean) => Callback,
  searchValue: String = "",
  reFetch: Callback
) {

  lazy val hasFilter: Boolean = searchValue.trim.nonEmpty

  def apply(): VdomElement = FundSubContactTable.component(this)

}

object FundSubContactTable {

  private type Props = FundSubContactTable

  private class Backend(@unused scope: BackendScope[Props, Unit]) { // scalafix:ok

    def render(props: Props): VdomNode = {
      val (filteredGroups, filteredContacts) = getDisplayData(props)
      if (props.contactInfo.contactWithoutGroup.isEmpty && props.contactInfo.groups.isEmpty) {
        renderEmpty(
          Icon.Glyph.UserGroupLarge,
          label = "No investors or contacts added yet",
          text = "Organize contacts by investor to speed up the invitation process"
        )
      } else if (props.searchValue.trim.nonEmpty && filteredGroups.isEmpty && filteredContacts.isEmpty) {
        renderEmpty(
          Icon.Glyph.Search,
          label = "No results found",
          text = "We couldn't find any contacts or investors matching your search"
        )
      } else {
        renderTable(
          props,
          filteredGroups,
          filteredContacts
        )
      }
    }

    private def matchSearchValue(searchValue: String, values: Seq[String]): Boolean = {
      values.exists(_.toLowerCase.contains(searchValue))
    }

    private def renderTable(
      props: Props,
      filteredGroups: Map[FundSubContactGroup, Seq[ContactModel]],
      filteredContacts: Seq[ContactModel]
    ): VdomNode = {
      <.div(
        renderHeader(
          props,
          filteredGroups,
          filteredContacts
        ),
        filteredGroups.toSeq
          .sortBy(_._1.name)
          .toVdomArray(
            using { case (group, contactModels) =>
              <.div(
                ^.key := s"${group.id.idString}",
                tw.borderGray3.borderBottom.borderLeft.borderRight,
                FundSubContactGroupRow(
                  fundSubId = props.fundSubId,
                  enabledCustomId = props.enabledCustomId,
                  group = group,
                  contactInfo = props.contactInfo,
                  contactModels =
                    contactModels.map(model => (model, props.selectedGroupContacts.contains(group.id -> model.contactId))),
                  selectedEmptyGroups = props.selectedEmptyGroups,
                  onToggleContacts =
                    (contactIds, selected) => props.onToggleGroup(contactIds.map(group.id -> _), selected),
                  onToggleEmptyGroup = isSelected => props.onToggleEmptyGroup(Seq(group.id), isSelected),
                  searchValue = props.searchValue,
                  reFetch = props.reFetch
                )()
              )
            }
          ),
        filteredContacts
          .sortBy(_.contactInfo.email)
          .toVdomArray(
            using { contactModel =>
              <.div(
                ^.key := s"${contactModel.contactId.idString}",
                tw.borderGray3.borderBottom.borderLeft.borderRight,
                FundSubContactRow(
                  props.fundSubId,
                  contactModel,
                  props.contactInfo,
                  props.selectedContacts.contains(contactModel.contactId),
                  selected => props.onToggleContact(Seq(contactModel.contactId), selected),
                  parentGroup = None,
                  props.reFetch
                )()
              )
            }
          )
      )
    }

    private def renderHeader(
      props: Props,
      filteredGroups: Map[FundSubContactGroup, Seq[ContactModel]],
      filteredContacts: Seq[ContactModel]
    ): VdomNode = {
      val isDataEmpty = filteredGroups.isEmpty && filteredContacts.isEmpty
      val allContactAndGroupSelected = isAllContactAndGroupSelected(
        props,
        filteredGroups,
        filteredContacts
      )
      val isIndeterminate = !allContactAndGroupSelected && isSomeContactOrGroupSelected(
        props,
        filteredGroups,
        filteredContacts
      )
      <.div(
        tw.flex.itemsCenter.borderGray3.borderAll.py8.pr8.pl16,
        tw.textGray7.bgGray1.fontSemiBold,
        Checkbox(
          isChecked = !isDataEmpty && allContactAndGroupSelected,
          onChange = selected => {
            val toggleContactCb = props.onToggleContact(filteredContacts.map(_.contactId), selected)
            val toggleGroupContactCb = props.onToggleGroup(
              filteredGroups.toSeq.flatMap { case (group, contactModels) =>
                contactModels.map(group.id -> _.contactId)
              },
              selected
            )
            val toggleEmptyGroupCb = props.onToggleEmptyGroup(
              filteredGroups.collect { case (group, contacts) if contacts.isEmpty => group.id }.toSeq,
              selected
            )
            toggleContactCb >> toggleGroupContactCb >> toggleEmptyGroupCb
          },
          isIndeterminate = isIndeterminate,
          isDisabled = isDataEmpty
        )(),
        <.div(
          tw.ml16,
          ^.marginRight := "238px",
          "Contact"
        ),
        <.div("Email")
      )
    }

    private def renderEmpty(icon: Icon.Name, label: String, text: String): VdomNode = {
      <.div(
        ^.marginTop := "132px",
        <.div(
          tw.flex.flexCol.textCenter,
          <.div(
            tw.roundedFull.bgPrimary1.mxAuto.flex,
            ^.width := "70px",
            ^.height := "70px",
            <.div(tw.mAuto.textGray7, IconR(icon, size = Icon.Size.Custom(px = 24))())
          ),
          <.div(
            tw.mt12.mb4.text17.leading28.fontBold,
            label
          ),
          <.div(
            tw.text13.leading20.textGray7,
            text
          )
        )
      )
    }

    private def isAllContactAndGroupSelected(
      props: Props,
      groups: Map[FundSubContactGroup, Seq[ContactModel]],
      contacts: Seq[ContactModel]
    ): Boolean = {
      groups.forall { case (group, contactModels) =>
        if (contactModels.isEmpty) {
          props.selectedEmptyGroups.contains(group.id)
        } else {
          contactModels.forall(contact => props.selectedGroupContacts.contains(group.id -> contact.contactId))
        }
      } && contacts.forall { contactModel =>
        props.selectedContacts.contains(contactModel.contactId)
      }
    }

    private def isSomeContactOrGroupSelected(
      props: Props,
      groups: Map[FundSubContactGroup, Seq[ContactModel]],
      contacts: Seq[ContactModel]
    ): Boolean = {
      groups.exists { case (group, contactModels) =>
        if (contactModels.isEmpty) {
          props.selectedEmptyGroups.contains(group.id)
        } else {
          contactModels.exists(contact => props.selectedGroupContacts.contains(group.id -> contact.contactId))
        }
      } || contacts.exists { contactModel =>
        props.selectedContacts.contains(contactModel.contactId)
      }
    }

    private def getDisplayData(props: Props) = {
      if (props.hasFilter) {
        val matchedNameGroup = props.contactInfo.groups.filter { case (group, _) =>
          matchSearchValue(props.searchValue, Seq(group.name))
        }
        val matchedSomeContactGroups = props.contactInfo.groups.flatMap { case (group, contacts) =>
          val matchedContacts = contacts.filter(contact =>
            matchSearchValue(props.searchValue, Seq(contact.contactInfo.fullName, contact.contactInfo.email))
          )
          if (matchedContacts.nonEmpty) Some(group -> matchedContacts) else None
        }
        val groups = matchedSomeContactGroups ++ matchedNameGroup
        val contacts = props.contactInfo.contactWithoutGroup.filter { contactModel =>
          matchSearchValue(props.searchValue, Seq(contactModel.contactInfo.fullName, contactModel.contactInfo.email))
        }
        groups -> contacts
      } else {
        props.contactInfo.groups -> props.contactInfo.contactWithoutGroup
      }
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .renderBackend[Backend]
    .build

}
