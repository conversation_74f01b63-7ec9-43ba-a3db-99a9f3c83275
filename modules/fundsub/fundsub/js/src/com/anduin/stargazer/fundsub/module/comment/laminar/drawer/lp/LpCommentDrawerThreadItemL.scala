// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.drawer.lp

import com.raquo.laminar.api.L.*
import design.anduin.components.avatar.AvatarList
import design.anduin.components.avatar.laminar.AvatarListL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.tooltip.laminar.SingleTooltipL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import org.scalajs.dom

import anduin.id.issuetracker.IssueId
import anduin.model.common.user.UserInfo
import anduin.stargazer.service.formcomment.FormCommentCommons.CommentThread
import com.anduin.stargazer.fundsub.module.comment.laminar.share.TwoLinesCommentSummary

private[lp] case class LpCommentDrawerThreadItemL(
  isNewSignal: Signal[Boolean],
  thread: CommentThread,
  onViewThread: Observer[IssueId]
) {

  private def renderParticipants = {
    val participants = (thread.replies.flatMap(_.creatorInfoOpt) ++ thread.creatorInfoOpt.toList).distinct
    val avatars = participants
      .sortBy(UserInfo.fullNameString)
      .map { participant =>
        AvatarList.Item(
          id = participant.emailAddressStr,
          initials = participant.fullNameString.split(" ").take(2).mkString("")
        )
      }
    AvatarListL(
      items = Val(avatars.toList),
      renderItem = renderProps => {
        SingleTooltipL(
          renderContent = _.amend(renderProps.item.id),
          renderTarget = renderProps.renderAvatar
        )()
      }
    )()
  }

  def apply(children: Node): HtmlElement = {
    SingleTooltipL(
      position = PortalPosition.LeftCenter,
      renderContent = _.amend("View comments in this thread"),
      renderTarget = {
        div(
          ComponentUtils.testIdL(LpCommentDrawerThreadItemL, id = "Container"),
          tw.cursorPointer,
          onClick --> Observer[dom.MouseEvent] { _ =>
            onViewThread.onNext(thread.id)
          },
          div(
            tw.pb16.borderBottom.borderGray3.border1,
            // Avatars
            div(
              tw.flex.itemsCenter.mb8.spaceX8,
              div(
                ComponentUtils.testIdL(LpCommentDrawerThreadItemL, "Avatar"),
                tw.flexFill,
                renderParticipants
              ),
              Option.when(thread.isResolved) {
                div(
                  tw.textGray5,
                  IconL(
                    name = Val(Icon.Glyph.CheckCircle),
                    size = Icon.Size.Px16
                  )()
                )
              }
            ),
            children,
            // The last comment
            TwoLinesCommentSummary(
              fundSubLpId = thread.fundSubLpId,
              issueId = thread.id,
              commentInfo = thread.lastComment,
              isNewCommentSignal = isNewSignal,
              totalNumberOfCommentsInThread = thread.replies.size + 1
            )()
          )
        )
      }
    )().amend(
      tw.px16.pt16.hover(tw.bgGray1)
    )
  }

}
