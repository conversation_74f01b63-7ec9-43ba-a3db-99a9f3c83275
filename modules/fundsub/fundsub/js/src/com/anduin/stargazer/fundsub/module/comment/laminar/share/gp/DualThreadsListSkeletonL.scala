package com.anduin.stargazer.fundsub.module.comment.laminar.share.gp

import com.raquo.laminar.api.L.*
import design.anduin.components.progress.Skeleton
import design.anduin.components.progress.laminar.SkeletonL
import design.anduin.style.tw.*

private[laminar] case class DualThreadsListSkeletonL() {

  def apply(): HtmlElement = {
    div(
      tw.px16,
      0.to(3).map { _ =>
        div(
          tw.borderBottom.border1.borderGray3.py16,
          div(
            tw.mb8.flex.justifyBetween.itemsCenter,
            SkeletonL(
              effect = Skeleton.Effect.Wave,
              height = "20px",
              width = "180px",
              shape = Skeleton.Shape.Rounded
            )(),
            SkeletonL(
              effect = Skeleton.Effect.Wave,
              height = "16px",
              width = "35px",
              shape = Skeleton.Shape.Rounded
            )()
          ),
          div(
            tw.mb8,
            SkeletonL(
              effect = Skeleton.Effect.Wave,
              height = "32px",
              width = "100%",
              shape = Skeleton.Shape.Rounded
            )()
          ),
          div(
            tw.mb8.flex.spaceX48,
            SkeletonL(
              effect = Skeleton.Effect.Wave,
              height = "20px",
              width = "20px",
              shape = Skeleton.Shape.Circle
            )(),
            SkeletonL(
              effect = Skeleton.Effect.Wave,
              height = "20px",
              width = "20px",
              shape = Skeleton.Shape.Circle
            )()
          ),
          SkeletonL(
            effect = Skeleton.Effect.Wave,
            height = "16px",
            width = "180px",
            shape = Skeleton.Shape.Text(11)
          )()
        )
      }
    )
  }

}
