// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.review.model

import fundsub.review.supportingdoc.SupportingDocReviewConfigMode.*
import fundsub.review.supportingdoc.{
  InvestorGroupSupportingDocReviewConfig,
  SupportingDocGroup,
  SupportingDocGroupReviewConfig
}

import anduin.fundsub.endpoint.investorgroup.InvestorGroupDetail
import anduin.fundsub.endpoint.review.FundSubSupportingDocReviewConfig
import anduin.fundsub.endpoint.subscriptiondoc.review.FundSubSubscriptionDocReviewConfigMode.{
  Disabled,
  EnableMultipleConfig,
  EnableSingleConfig
}
import anduin.fundsub.endpoint.subscriptiondoc.review.{
  AdminGroupDetail,
  FundSubSubscriptionDocReviewConfig,
  SaveSubscriptionDocReviewConfigParams
}
import anduin.id.fundsub.FundSubId
import anduin.id.fundsub.group.FundSubInvestorGroupId
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.TeamId

private[settings] final case class AdminAndInvestorGroupInfo(
  isLoading: Boolean = false,
  fundId: FundSubId,
  adminGroups: Seq[AdminGroupDetail] = Seq.empty,
  investorGroups: Seq[InvestorGroupDetail] = Seq.empty,
  adminUserInfos: Map[UserId, UserInfo] = Map.empty
) {

  lazy val allInvestorGroupId: FundSubInvestorGroupId = FundSubInvestorGroupId.PredefinedId.AllInvestorGroup(fundId)

  lazy val emailToUserId: Map[String, UserId] = adminUserInfos.toList.map { case (k, v) =>
    v.emailAddressStr -> k
  }.toMap

  lazy val userIdToAdminGroupId: Map[UserId, TeamId] = adminGroups
    .flatMap(
      _.members
    )
    .map { member =>
      member.userId -> member.groupId
    }
    .toMap

  lazy val investorsCount: Int = investorGroups
    .find(_.id == allInvestorGroupId)
    .map(_.membersCount)
    .getOrElse(0)

  lazy val unassignedInvestorsCount: Int = investorsCount - investorGroups
    .filterNot(_.id == allInvestorGroupId)
    .map(_.membersCount)
    .sum

  def isValidReviewerOfInvestorGroup(
    email: String,
    investorGroupIdOpt: Option[FundSubInvestorGroupId], // None means all investor group,
    isSubscriptionDocReview: Boolean
  ): Boolean = {
    emailToUserId
      .get(email)
      .flatMap { userId =>
        userIdToAdminGroupId.get(userId)
      }
      .exists { adminGroupId =>
        adminGroups
          .exists { group =>
            val canAccessInvestorDocsToReview = if (isSubscriptionDocReview) {
              group.canAccessLpSubscriptionDocs
            } else {
              group.canAccessLpSupportingDocs
            }
            group.groupId == adminGroupId
            && canAccessInvestorDocsToReview
            && group.accessibleInvestorGroups.map(_.id).exists { id =>
              FundSubInvestorGroupId.PredefinedId.AllInvestorGroup.isMatch(id) || investorGroupIdOpt == Option(id)
            }
          }
      }
  }

  def isValidReviewerOfInvestorGroup(
    userId: UserId,
    investorGroupIdOpt: Option[FundSubInvestorGroupId], // None means all investor group
    isSubscriptionDocReview: Boolean
  ): Boolean = {
    adminUserInfos
      .get(userId)
      .map(_.emailAddressStr)
      .exists(isValidReviewerOfInvestorGroup(_, investorGroupIdOpt, isSubscriptionDocReview))
  }

  def isValidGroupConfig(
    reviewers: Seq[UserId],
    investorGroupId: FundSubInvestorGroupId,
    isInvalidReviewerAllowed: Boolean,
    isSubscriptionDocReview: Boolean
  ): Boolean = {
    reviewers.nonEmpty &&
    (if (isInvalidReviewerAllowed) {
       reviewers.exists { reviewer =>
         isValidReviewerOfInvestorGroup(reviewer, Option(investorGroupId), isSubscriptionDocReview)
       }
     } else {
       reviewers.forall { reviewer =>
         isValidReviewerOfInvestorGroup(reviewer, Option(investorGroupId), isSubscriptionDocReview)
       }
     })
  }

  def isValidSupportingDocReviewConfig(
    config: FundSubSupportingDocReviewConfig,
    isSupportingDocGroupEnabled: Boolean,
    isInvalidReviewerAllowed: Boolean
  ): Boolean = {
    val refinedSupportingDocGroups = if (isSupportingDocGroupEnabled) {
      config.supportingDocGroups
    } else {
      Seq.empty
    }
    config.mode match {
      case SUPPORTING_DOC_REVIEW_CONFIG_MODE_DISABLED => true
      case SUPPORTING_DOC_REVIEW_CONFIG_MODE_ENABLED_WITH_SINGLE_CONFIG =>
        config.allInvestorGroupConfigOpt.exists { allInvestorGroupConfig =>
          isValidInvestorGroupSupportingDocReviewConfig(
            refinedSupportingDocGroups,
            allInvestorGroupConfig,
            Option(allInvestorGroupId),
            isInvalidReviewerAllowed
          )
        }
      case SUPPORTING_DOC_REVIEW_CONFIG_MODE_ENABLED_WITH_MULTI_GROUP_CONFIG =>
        val hasValidConfigForUnassignedGroup = config.unassignedInvestorGroupConfigOpt.exists {
          unassignedInvestorGroupConfig =>
            isValidInvestorGroupSupportingDocReviewConfig(
              refinedSupportingDocGroups,
              unassignedInvestorGroupConfig,
              None,
              isInvalidReviewerAllowed
            )
        }

        val allOtherGroupsHasValidConfig = investorGroups
          .filterNot(_.id == allInvestorGroupId)
          .forall { investorGroup =>
            config.investorGroupConfigMap
              .get(investorGroup.id)
              .exists { investorGroupConfig =>
                isValidInvestorGroupSupportingDocReviewConfig(
                  refinedSupportingDocGroups,
                  investorGroupConfig,
                  Some(investorGroup.id),
                  isInvalidReviewerAllowed
                )
              }
          }

        hasValidConfigForUnassignedGroup && allOtherGroupsHasValidConfig

      case _: Unrecognized => false
    }
  }

  def isMissingSubscriptionDocReviewer(config: FundSubSubscriptionDocReviewConfig): Boolean = {
    config.mode match {
      case Disabled => true
      case EnableSingleConfig =>
        config.allInvestorGroupReviewConfigOpt.exists(_.steps.exists(_.reviewers.isEmpty))
      case EnableMultipleConfig =>
        val unassignedInvestorMissingReviewer =
          config.unassignedInvestorGroupReviewConfigOpt.exists(_.steps.exists(_.reviewers.isEmpty))

        val atLeastOneInvestorGroupMissingReviewer =
          config.investorGroupReviewConfigs.exists { case (_, investorGroupReviewConfigs) =>
            investorGroupReviewConfigs.steps.exists(_.reviewers.isEmpty)
          }

        val hasUnSetupGroup =
          investorGroups
            .map(_.id)
            .filterNot(_ == allInvestorGroupId)
            .diff(config.investorGroupReviewConfigs.keys.toSeq)
            .nonEmpty
        unassignedInvestorMissingReviewer || atLeastOneInvestorGroupMissingReviewer || hasUnSetupGroup
    }
  }

  def isMissingSubscriptionDocReviewerDueToInvalidPermission(config: FundSubSubscriptionDocReviewConfig): Boolean = {
    config.mode match {
      case Disabled => true
      case EnableSingleConfig =>
        config.allInvestorGroupReviewConfigOpt.exists(
          _.steps.exists(step =>
            step.reviewers.nonEmpty && step.reviewers.forall { reviewer =>
              !isValidReviewerOfInvestorGroup(
                reviewer.userId,
                Option(allInvestorGroupId),
                isSubscriptionDocReview = true
              )
            }
          )
        )
      case EnableMultipleConfig =>
        val hasValidConfigForUnassignedGroup =
          config.unassignedInvestorGroupReviewConfigOpt.exists(
            _.steps.exists(step =>
              step.reviewers.nonEmpty && step.reviewers.forall { reviewer =>
                !isValidReviewerOfInvestorGroup(
                  reviewer.userId,
                  Option(allInvestorGroupId),
                  isSubscriptionDocReview = true
                )
              }
            )
          )

        val allOtherGroupsHasValidConfig =
          config.investorGroupReviewConfigs.exists { case (investorGroupId, investorGroupReviewConfigs) =>
            investorGroupReviewConfigs.steps.exists(step =>
              step.reviewers.nonEmpty && step.reviewers.forall { reviewer =>
                !isValidReviewerOfInvestorGroup(
                  reviewer.userId,
                  Option(investorGroupId),
                  isSubscriptionDocReview = true
                )
              }
            )
          }
        hasValidConfigForUnassignedGroup || allOtherGroupsHasValidConfig
    }
  }

  def isValidSubscriptionDocReviewConfig(
    config: SaveSubscriptionDocReviewConfigParams,
    isInvalidReviewerAllowed: Boolean
  ): Boolean = {
    config.mode match {
      case Disabled => true
      case EnableSingleConfig =>
        config.allInvestorGroupReviewConfig.forall { allInvestorGroupConfig =>
          isValidGroupConfig(
            allInvestorGroupConfig.reviewers,
            allInvestorGroupId,
            isInvalidReviewerAllowed,
            isSubscriptionDocReview = true
          )
        }
      case EnableMultipleConfig =>
        val hasValidConfigForUnassignedGroup = config.unassignedInvestorGroupReviewConfig.forall {
          unassignedInvestorGroupConfig =>
            isValidGroupConfig(
              unassignedInvestorGroupConfig.reviewers,
              allInvestorGroupId,
              isInvalidReviewerAllowed,
              isSubscriptionDocReview = true
            )
        }

        val allOtherGroupsHasValidConfig =
          config.investorGroupReviewConfigs
            .forall { case (investorGroupId, investorGroupReviewConfigs) =>
              investorGroupReviewConfigs.forall { investorGroupReviewConfig =>
                isValidGroupConfig(
                  investorGroupReviewConfig.reviewers,
                  investorGroupId,
                  isInvalidReviewerAllowed,
                  isSubscriptionDocReview = true
                )
              }
            }

        hasValidConfigForUnassignedGroup && allOtherGroupsHasValidConfig
    }
  }

  def isValidDocGroupReviewConfig(
    docGroupConfig: SupportingDocGroupReviewConfig,
    investorGroupIdOpt: Option[FundSubInvestorGroupId], // None means all investor group
    isInvalidReviewerAllowed: Boolean,
    isSubscriptionDocReview: Boolean
  ): Boolean = {
    val reviewers = docGroupConfig.reviewers
    if (isInvalidReviewerAllowed) {
      reviewers.exists { reviewer =>
        isValidReviewerOfInvestorGroup(
          userId = reviewer,
          investorGroupIdOpt = investorGroupIdOpt,
          isSubscriptionDocReview = isSubscriptionDocReview
        )
      }
    } else {
      reviewers.nonEmpty && reviewers.forall { reviewer =>
        isValidReviewerOfInvestorGroup(
          userId = reviewer,
          investorGroupIdOpt = investorGroupIdOpt,
          isSubscriptionDocReview = isSubscriptionDocReview
        )
      }
    }

  }

  def isValidInvestorGroupSupportingDocReviewConfig(
    docGroups: Seq[SupportingDocGroup],
    investorGroupConfig: InvestorGroupSupportingDocReviewConfig,
    investorGroupIdOpt: Option[FundSubInvestorGroupId], // None means all investor group
    isInvalidReviewerAllowed: Boolean
  ): Boolean = {
    investorGroupConfig.unassignedDocGroupConfigOpt.exists { docGroupConfig =>
      isValidDocGroupReviewConfig(
        docGroupConfig,
        investorGroupIdOpt,
        isInvalidReviewerAllowed,
        isSubscriptionDocReview = false
      )
    } && docGroups.forall { docGroupInfo =>
      investorGroupConfig.docGroupConfigs.exists { docGroupConfig =>
        docGroupConfig.groupName.trim.equalsIgnoreCase(docGroupInfo.groupName.trim) &&
        isValidDocGroupReviewConfig(
          docGroupConfig,
          investorGroupIdOpt,
          isInvalidReviewerAllowed,
          isSubscriptionDocReview = false
        )
      }
    }
  }

  def isMissingSupportingDocReviewer(config: FundSubSupportingDocReviewConfig): Boolean = {
    config.mode match {
      case SUPPORTING_DOC_REVIEW_CONFIG_MODE_DISABLED | _: Unrecognized => false
      case SUPPORTING_DOC_REVIEW_CONFIG_MODE_ENABLED_WITH_SINGLE_CONFIG =>
        config.allInvestorGroupConfigOpt.exists(isMissingSupportingDocReviewer)
      case SUPPORTING_DOC_REVIEW_CONFIG_MODE_ENABLED_WITH_MULTI_GROUP_CONFIG =>
        val unassignedInvestorGroupMissingReviewer =
          config.unassignedInvestorGroupConfigOpt.exists(isMissingSupportingDocReviewer)

        val atLeastOneInvestorGroupMissingReviewer =
          config.investorGroupConfigMap.exists { case (_, investorGroupConfig) =>
            isMissingSupportingDocReviewer(investorGroupConfig)
          }

        val hasUnSetupGroup =
          investorGroups
            .map(_.id)
            .filterNot(_ == allInvestorGroupId)
            .diff(config.investorGroupConfigMap.keys.toSeq)
            .nonEmpty
        unassignedInvestorGroupMissingReviewer || atLeastOneInvestorGroupMissingReviewer || hasUnSetupGroup
    }
  }

  def hasSupportingDocReviewerLosingAccess(config: FundSubSupportingDocReviewConfig): Boolean = {
    config.mode match {
      case SUPPORTING_DOC_REVIEW_CONFIG_MODE_DISABLED | _: Unrecognized => false
      case SUPPORTING_DOC_REVIEW_CONFIG_MODE_ENABLED_WITH_SINGLE_CONFIG =>
        config.allInvestorGroupConfigOpt.exists(hasSupportingDocReviewerLosingAccess(Some(allInvestorGroupId), _))
      case SUPPORTING_DOC_REVIEW_CONFIG_MODE_ENABLED_WITH_MULTI_GROUP_CONFIG =>
        val unassignedInvestorGroupHasReviewerLosingAccess =
          config.unassignedInvestorGroupConfigOpt.exists(hasSupportingDocReviewerLosingAccess(None, _))

        val atLeastOneInvestorGroupHasReviewerLosingAccess =
          config.investorGroupConfigMap.exists { case (investorGroupId, investorGroupConfig) =>
            hasSupportingDocReviewerLosingAccess(Some(investorGroupId), investorGroupConfig)
          }

        unassignedInvestorGroupHasReviewerLosingAccess || atLeastOneInvestorGroupHasReviewerLosingAccess
    }
  }

  private def isMissingSupportingDocReviewer(investorGroupConfig: InvestorGroupSupportingDocReviewConfig): Boolean = {
    (investorGroupConfig.docGroupConfigs ++ investorGroupConfig.unassignedDocGroupConfigOpt).exists(_.reviewers.isEmpty)
  }

  private def hasSupportingDocReviewerLosingAccess(
    investorGroupIdOpt: Option[FundSubInvestorGroupId],
    investorGroupConfig: InvestorGroupSupportingDocReviewConfig
  ): Boolean = {
    (investorGroupConfig.docGroupConfigs ++ investorGroupConfig.unassignedDocGroupConfigOpt).exists(_.reviewers.forall {
      reviewer => !isValidReviewerOfInvestorGroup(reviewer, investorGroupIdOpt, isSubscriptionDocReview = false)
    })
  }

}
