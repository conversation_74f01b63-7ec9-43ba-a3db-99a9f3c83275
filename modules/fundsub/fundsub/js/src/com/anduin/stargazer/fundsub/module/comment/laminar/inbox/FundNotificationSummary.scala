// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.inbox

import com.raquo.laminar.api.L.*
import design.anduin.components.badge.Badge
import design.anduin.components.badge.laminar.BadgeL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*

private[fundsub] final case class FundNotificationSummary(
  unreadGeneralComments: Int,
  unreadMentionComments: Int,
  unreadParticipatedThreads: Int,
  assignedToMe: Int,
  onOpen: Observer[Unit],
  onClose: Observer[Unit]
) {

  private def renderNotificationLine(
    category: String,
    countOpt: Option[Int],
    usingGrayBadge: Boolean = false
  ) = {
    val color = if (usingGrayBadge) Badge.Color.Gray else Badge.Color.Primary
    val theme = if (usingGrayBadge) Badge.Theme.Light else Badge.Theme.Bold
    div(
      width.px := 212,
      tw.flex.itemsCenter.hPx32.px12.spaceX12,
      BadgeL(
        color = color,
        theme = theme,
        count = Val(countOpt)
      )(),
      div(
        tw.text13.fontNormal.leading16.textGray9,
        category
      )
    )
  }

  def apply(): HtmlElement = {
    div(
      ComponentUtils.testIdL(FundNotificationSummary),
      Option.when(unreadGeneralComments > 0 && unreadMentionComments + unreadParticipatedThreads == 0) {
        renderNotificationLine(
          category = "Unread comments",
          countOpt = None
        )
      },
      Option.when(unreadMentionComments > 0) {
        renderNotificationLine(
          category = "Unread mentions",
          countOpt = Some(unreadMentionComments)
        )
      },
      Option.when(unreadParticipatedThreads > 0) {
        renderNotificationLine(
          category = "Unread threads",
          countOpt = Some(unreadParticipatedThreads)
        )
      },
      Option.when(assignedToMe > 0) {
        renderNotificationLine(
          category = "Threads assigned to me",
          countOpt = Some(assignedToMe),
          usingGrayBadge = true
        )
      },
      onMountCallback { _ =>
        onOpen.onNext(())
      },
      onUnmountCallback { _ =>
        onClose.onNext(())
      }
    )
  }

}
