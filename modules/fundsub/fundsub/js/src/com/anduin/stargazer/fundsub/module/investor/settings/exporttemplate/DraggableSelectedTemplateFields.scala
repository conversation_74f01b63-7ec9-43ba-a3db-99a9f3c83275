// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.exporttemplate

import com.raquo.laminar.api.L.*
import com.raquo.laminar.codecs.StringAsIsCodec
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.menu.laminar.{MenuDividerL, MenuItemL, MenuL}
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.portal.laminar.PortalWrapperL
import design.anduin.components.switcher.laminar.SwitcherL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.util.NodeListSeq
import design.anduin.style.tw.*
import io.circe.parser.decode
import io.circe.syntax.*
import org.scalajs.dom.html.Element

import anduin.facades.core.sortablejs.mod.{Options, Sortable}
import anduin.fundsub.endpoint.dataexport.{ExportTemplateFieldIdentifier, SelectedFieldOfSelfServiceExport}
import anduin.model.id.FileId
import anduin.scalajs.pluralize.Pluralize
import com.anduin.stargazer.fundsub.module.investor.settings.exporttemplate.DraggableSelectedTemplateFields.FieldInfo
import com.anduin.stargazer.fundsub.module.investor.settings.exporttemplate.SelectFieldsForSelfServiceExport.SelectedPdfState

private[exporttemplate] case class DraggableSelectedTemplateFields(
  selectedFieldsSignal: Signal[Seq[SelectedFieldOfSelfServiceExport]],
  forApiExportSignal: Signal[Boolean],
  onReorderFields: Observer[Seq[ExportTemplateFieldIdentifier]],
  onRemoveField: Observer[ExportTemplateFieldIdentifier],
  onRenameField: Observer[(ExportTemplateFieldIdentifier, String)],
  onRenameFieldOptionNameMap: Observer[(ExportTemplateFieldIdentifier, Map[String, String])],
  onJumpToPdfFieldLocation: Observer[SelectedPdfState],
  filesWithNameMapSignal: Signal[Map[FileId, String]],
  onViewDashboardFields: Observer[Unit]
) {
  private val numberOfSelectedFieldsSignal = selectedFieldsSignal.map(_.size).distinct

  private val isSelectedFieldsEmptySignal = numberOfSelectedFieldsSignal.map(_ == 0).distinct

  private val errorFieldIndexesSignal =
    forApiExportSignal.combineWith(selectedFieldsSignal).map { case (forApiExport, selectedFields) =>
      if (forApiExport) {
        selectedFields.zipWithIndex.flatMap { case (selectedField, selectedFieldIndex) =>
          val hasDuplicateInAnotherField = selectedFields.exists { field =>
            field.fieldId != selectedField.fieldId && field.revisedName.trim == selectedField.revisedName.trim
          }
          val hasDuplicateValues =
            selectedField.revisedOptionNameMap.values.map(_.trim).toSet.size < selectedField.revisedOptionNameMap.size
          Option.when(hasDuplicateInAnotherField || hasDuplicateValues)(selectedFieldIndex)
        }.toSet
      } else {
        Set.empty
      }
    }

  private val shouldShowErrorFieldsOnlyVar = Var[Boolean](false)

  private val sortableVar = Var[Option[Sortable]](None)

  private val sortableContainer = div()

  private val GhostCssClass = "sortable-ghost"

  private def handleDropCompleted(): Unit = {
    val fieldsOrderStr = NodeListSeq(sortableContainer.ref.querySelectorAll("[data-column]"))
      .map(_.asInstanceOf[Element].getAttribute("data-column")) // scalafix:ok DisableSyntax.asInstanceOf
      .toList
    onReorderFields.onNext {
      fieldsOrderStr.flatMap { fieldStr =>
        decode[ExportTemplateFieldIdentifier](fieldStr).toOption
      }
    }
  }

  private val editingFieldNameOptVar: Var[Option[ExportTemplateFieldIdentifier]] = Var(Option.empty)

  def apply(): HtmlElement = {
    div(
      width.px := 280,
      tw.borderLeft.borderGray3,
      child.maybe <-- isSelectedFieldsEmptySignal.map { isSelectedFieldsEmpty =>
        Option.when(isSelectedFieldsEmpty) {
          div(
            tw.hPc100.flex.flexCol.itemsCenter.justifyCenter,
            div(
              tw.textGray5,
              IconL(name = Val(Icon.Glyph.ListBullet), size = Icon.Size.Custom(40))()
            ),
            div(
              tw.mt8,
              "No fields selected"
            )
          )
        }
      },
      renderNonEmptyState.amend(isSelectedFieldsEmptySignal.cls(tw.hidden))
    )
  }

  private def renderNonEmptyState = {
    div(
      tw.hPc100.flex.flexCol,
      div(
        height.px := 44,
        tw.px16.flex.itemsCenter,
        child.text <-- numberOfSelectedFieldsSignal.map { numberOfSelectedFields =>
          Pluralize(word = "field", count = numberOfSelectedFields, inclusive = true) + " selected"
        }
      ),
      child <-- errorFieldIndexesSignal
        .map(_.nonEmpty)
        .splitBoolean(
          whenTrue = _ =>
            div(
              div(
                tw.flex.itemsCenter.bgWarning1.p12,
                div(
                  tw.textWarning4,
                  IconL(name = Val(Icon.Glyph.Info))()
                ),
                div(
                  tw.flexFill.ml8,
                  text <-- errorFieldIndexesSignal.map { errorFieldIndexes =>
                    s"${Pluralize("error field", errorFieldIndexes.size, inclusive = true)} found"
                  }
                )
              ),
              div(
                tw.py8.px16,
                SwitcherL(
                  isChecked = shouldShowErrorFieldsOnlyVar.signal,
                  onChange = shouldShowErrorFieldsOnlyVar.writer
                )("Show only error field")
              )
            ),
          whenFalse = _ => emptyNode
        ),
      sortableContainer.amend(
        tw.borderTop.borderGray3,
        tw.flexFill.overflowYScroll.borderGray3.py8,
        tw.spaceY8,
        children <-- selectedFieldsSignal
          .map(_.zipWithIndex)
          .combineWith(shouldShowErrorFieldsOnlyVar.signal, errorFieldIndexesSignal)
          .map { case (allSelectedFields, onlyShowErrorFields, errorFieldIndexes) =>
            allSelectedFields.flatMap { case (field, fieldIndex) =>
              val hasError = errorFieldIndexes.contains(fieldIndex)
              Option.unless(onlyShowErrorFields && !hasError) {
                FieldInfo(
                  field = field,
                  fieldIndex = fieldIndex,
                  hasError = hasError
                )
              }
            }
          }
          .split(_._1.fieldId) { case (fieldId, _, fieldInfoSignal) =>
            renderFieldRow(fieldId, fieldInfoSignal)
          },
        onMountCallback { ctx =>
          val options = Options().setGhostClass(GhostCssClass).setOnEnd(_ => handleDropCompleted())
          val sortable = design.anduin.facades.sortablejs.SortableJsFacades.create(ctx.thisNode.ref, options)
          sortableVar.set(Option(sortable))
        },
        onUnmountCallback { _ =>
          sortableVar.now().foreach(_.destroy())
        }
      )
    )
  }

  private def renderFieldRow(
    fieldId: ExportTemplateFieldIdentifier,
    fieldInfoSignal: Signal[FieldInfo]
  ) = {
    val fieldSignal = fieldInfoSignal.map(_.field)
    val indexSignal = fieldInfoSignal.map(_.fieldIndex)
    val hasErrorSignal = fieldInfoSignal.map(_.hasError)
    div(
      tw.flex,
      div(
        width.px := 28,
        tw.flex.itemsCenter.justifyCenter.textGray7,
        child.text <-- indexSignal.map(_ + 1)
      ),
      div(
        htmlAttr("data-column", StringAsIsCodec) <-- fieldSignal.map(_.fieldId.asJson.noSpaces).distinct,
        tw.flexFill.flex.itemsCenter.justifyCenter,
        hasErrorSignal.cls(tw.borderAll.borderWarning3.bgWarning1.rounded4),
        hasErrorSignal.not.cls(tw.borderAll.borderGray3.bgGray1.rounded4),
        tw.py6.px8,
        cursor.grab,
        div(
          tw.mr12.textGray7,
          IconL(Val(Icon.Glyph.Drag))()
        ),
        renderFieldRowActionPopover(
          fieldId = fieldId,
          fieldSignal = fieldSignal,
          renderTarget = (open, _) =>
            TooltipL(
              renderTarget = div(
                tw.truncate.mr12.textGray8.cursorText,
                child.text <-- fieldSignal.map(_.revisedName),
                onDblClick.mapToUnit --> Observer { _ =>
                  editingFieldNameOptVar.set(Some(fieldId))
                  open.onNext(())
                }
              ),
              renderContent = _.amend("Double click to edit"),
              targetWrapper = PortalWrapperL.BlockContent
            )()
        ).amend(tw.flexFill),
        child <-- hasErrorSignal.splitBoolean(
          whenTrue = _ =>
            div(
              tw.mr8.textWarning4,
              TooltipL(
                renderTarget = IconL(
                  name = Val(Icon.Glyph.Info),
                  size = Icon.Size.Custom(12)
                )(),
                renderContent = _.amend("Duplicate field")
              )()
            ),
          whenFalse = _ => emptyNode
        ),
        renderFieldRowActionPopover(
          fieldId = fieldId,
          fieldSignal = fieldSignal,
          renderTarget = (open, isOpenedSignal) =>
            ButtonL(
              style = ButtonL.Style.Minimal(
                height = ButtonL.Height.Fix24,
                icon = Some(Icon.Glyph.EllipsisHorizontal),
                isSelected = isOpenedSignal
              ),
              onClick = open.contramap(_ => ())
            )()
        )
      )
    )
  }

  private def renderFieldRowActionPopover(
    fieldId: ExportTemplateFieldIdentifier,
    fieldSignal: Signal[SelectedFieldOfSelfServiceExport],
    renderTarget: (Observer[Unit], Signal[Boolean]) => Node
  ) = {
    PopoverL(
      renderTarget = renderTarget,
      renderContent = onClosePopover =>
        div(
          child <-- editingFieldNameOptVar.signal.map(_.contains(fieldId)).distinct.map {
            if (_) {
              EditFieldNamePopoverContent(
                fieldId = fieldId,
                fieldSignal = fieldSignal,
                selectedFieldsSignal = selectedFieldsSignal,
                forApiExportSignal = forApiExportSignal,
                onRenameField = onRenameField,
                onRenameFieldOptionNameMap = onRenameFieldOptionNameMap,
                onClosePopover = onClosePopover
              )()
            } else {
              MenuL(
                Seq(
                  renderFieldLocation(fieldId, fieldSignal, onClosePopover),
                  MenuDividerL(),
                  MenuItemL(
                    icon = Some(Icon.Glyph.Edit),
                    onClick = editingFieldNameOptVar.writer.contramap(_ => Some(fieldId))
                  )("Edit field name"),
                  MenuItemL(
                    icon = Some(Icon.Glyph.Cross),
                    onClick = onRemoveField.contramap(_ => fieldId),
                    color = MenuItemL.ColorDanger
                  )("Remove field")
                )
              ).amend(
                width.px := 320
              )
            }
          }
        ),
      position = PortalPosition.BottomRight,
      onToggle = editingFieldNameOptVar.writer.contracollect { case false =>
        Option.empty[ExportTemplateFieldIdentifier]
      },
      targetWrapper = PortalWrapperL.Block
    )()

  }

  private def renderFieldLocation(
    fieldId: ExportTemplateFieldIdentifier,
    fieldSignal: Signal[SelectedFieldOfSelfServiceExport],
    onClosePopover: Observer[Unit]
  ) = {
    MenuItemL()(
      div(
        tw.wPc100,
        div(
          tw.fontSemiBold,
          "Field location"
        ),
        Option.when(!fieldId.isPdfField) {
          Seq(
            div(
              tw.textGray7.text11.pt4,
              "Dashboard data field"
            ),
            onClick --> Observer { _ =>
              onViewDashboardFields.onNext(())
              onClosePopover.onNext(())
            }
          )
        },
        child.maybe <-- fieldSignal.map(_.fieldPageLocationOpt).distinct.mapSome { location =>
          div(
            tw.p8,
            child <-- filesWithNameMapSignal.map(_.getOrElse(location.fileId, "Unknown file")).distinct.map {
              fileName =>
                div(
                  tw.flex.itemsCenter,
                  IconL(name = Val(Icon.File.ByExtension(fileName)), size = Icon.Size.Px24)(),
                  div(
                    tw.ml4.flexFill.truncate,
                    fileName
                  )
                )
            },
            div(
              paddingLeft.px := 28,
              tw.textGray7,
              s"Page ${location.pageIndex + 1}"
            ),
            onClick --> Observer { _ =>
              fieldId match {
                case pdfFieldId: ExportTemplateFieldIdentifier.PdfField =>
                  onJumpToPdfFieldLocation.onNext(
                    SelectedPdfState(
                      selectedFileId = location.fileId,
                      initialPageIndexOpt = Some(location.pageIndex),
                      scrollIntoFieldOpt = Some(pdfFieldId)
                    )
                  )
                  onClosePopover.onNext(())
                case _ => ()
              }
            }
          )
        }
      )
    )
  }

}

private[exporttemplate] object DraggableSelectedTemplateFields {

  final case class FieldInfo(
    field: SelectedFieldOfSelfServiceExport,
    fieldIndex: Int,
    hasError: Boolean
  )

}
