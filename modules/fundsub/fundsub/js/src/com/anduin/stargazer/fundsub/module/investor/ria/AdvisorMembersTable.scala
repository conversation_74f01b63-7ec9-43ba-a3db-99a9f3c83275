// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.ria

import com.raquo.laminar.api.L.*
import design.anduin.components.avatar.laminar.AvatarLabelL
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.tag.Tag
import design.anduin.components.tag.laminar.TagL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import design.anduin.table.laminar.TableL
import design.anduin.table.laminar.TableL.VerticalAlignment

import anduin.fundsub.endpoint.ria.RiaFundGroupState.InvitedAdvisor
import anduin.fundsub.endpoint.ria.RiaFundGroupState.{FundGroupAdvisor, RiaEntityLinked}
import anduin.fundsub.endpoint.ria.{FundSubRiaEntityAdvisor, RiaFundGroup, RiaFundGroupState}
import anduin.protobuf.ria.RiaEntityFundAdvisorRole
import anduin.utils.DateTimeUtils

private[ria] case class AdvisorMembersTable(
  group: RiaFundGroup,
  onRefetch: Observer[Unit]
) {

  private val advisorColumn = TableL.Column[FundGroupAdvisor](
    title = "Advisor",
    field = "advisor",
    renderCell = renderProps => {
      val userInfo = renderProps.data match {
        case advisor: InvitedAdvisor          => advisor.info.userInfo
        case advisor: FundSubRiaEntityAdvisor => advisor.info.userInfo
      }
      AvatarLabelL(
        emailAddress = userInfo.emailAddressStr,
        fullName = Val(userInfo.fullNameString),
        renderCopyIndicator = _ => emptyNode
      )()
    }
  )

  private val subscriptionsCreatedColumn = TableL.Column[FundGroupAdvisor](
    title = "Subscriptions created",
    field = "order",
    renderCell = renderProps => {
      div(
        renderProps.data match {
          case _: InvitedAdvisor => "--"
          case advisor: FundSubRiaEntityAdvisor =>
            if (advisor.subscriptions.nonEmpty) {
              advisor.subscriptions.size.toString
            } else {
              "--"
            }
        }
      )
    }
  )

  private val roleColumn = TableL.Column[FundGroupAdvisor](
    title = "Role",
    field = "role",
    renderCell = renderProps => {
      div(
        renderProps.data match {
          case _: InvitedAdvisor => "--"
          case advisor: FundSubRiaEntityAdvisor =>
            advisor.role match {
              case RiaEntityFundAdvisorRole.AdvisorMember   => "Member"
              case RiaEntityFundAdvisorRole.AdvisorAdmin    => "Admin"
              case RiaEntityFundAdvisorRole.Unrecognized(_) => "--"
            }
        }
      )
    }
  )

  private val statusColumn = TableL.Column[FundGroupAdvisor](
    title = "Status",
    field = "status",
    verticalAlignment = Option(VerticalAlignment.Middle),
    renderCell = renderProps => {
      div(
        renderProps.data match {
          case advisor: InvitedAdvisor =>
            div(
              if (group.isLinked) {
                TooltipL(
                  position = PortalPosition.TopCenter,
                  renderTarget = TagL(
                    label = Val("Inactive"),
                    color = Val(Tag.Light.Warning),
                    icon = Option(Icon.Glyph.Info)
                  )(),
                  renderContent = _.amend(
                    "Advisor's email domain is not associated with the advisor entity's. Please contact the advisor entity admin to resolve this issue."
                  )
                )()
              } else {
                advisor.invitedAt.map { invitedAt =>
                  div(
                    div(
                      s"Invited ${DateTimeUtils.formatInstant(invitedAt, DateTimeUtils.LongLocalDatePattern)(
                          using DateTimeUtils.defaultTimezone
                        )}"
                    ),
                    div(
                      ModalL(
                        renderTitle = _ => "Resend invitation",
                        renderTarget = onOpen => {
                          ButtonL(
                            style = ButtonL.Style.Text(),
                            onClick = onOpen.contramap(_ => ())
                          )(
                            div(
                              tw.flex.itemsCenter.spaceX4.textPrimary5,
                              IconL(name = Val(Icon.Glyph.Envelope), size = Icon.Size.Custom(12))(),
                              div(
                                tw.text11,
                                "Resend invitation"
                              )
                            )
                          )
                        },
                        renderContent = onClose =>
                          ResendAdvisorInvitationConfirmModal(
                            fundSubRiaGroupId = group.id,
                            toResendInvitationAdvisor = advisor,
                            onClose = onClose,
                            onDoneResendAdvisorInvitation = onRefetch
                          )()
                      )()
                    )
                  )
                }
              }
            )
          case advisor: FundSubRiaEntityAdvisor =>
            div(
              advisor.joinedAt.fold(
                div(
                  advisor.invitedAt.map { invitedAt =>
                    s"Invited ${DateTimeUtils.formatInstant(invitedAt, DateTimeUtils.LongLocalDatePattern)(
                        using DateTimeUtils.defaultTimezone
                      )}"
                  }
                )
              ) { joinedAt =>
                div(s"Joined ${DateTimeUtils.formatInstant(joinedAt, DateTimeUtils.LongLocalDatePattern)(
                    using DateTimeUtils.defaultTimezone
                  )}")
              },
              div(
                tw.textGray7,
                s"by ${advisor.addedBy.map(_.userInfo.fullNameString).getOrElse("System")}"
              )
            )
        }
      )
    }
  )

  private val actionColumn = TableL.Column[FundGroupAdvisor](
    title = "",
    field = "id",
    renderCell = renderProps => {
      renderProps.data match {
        case advisor: InvitedAdvisor =>
          ModalL(
            renderContent = onCloseModal =>
              RevokeAdvisorInvitationConfirmModal(
                fundSubRiaGroupId = group.id,
                toRevokeAdvisor = advisor,
                onClose = onCloseModal,
                onDoneRevokeAdvisorInvitation = onRefetch
              )(),
            renderTitle = _ => "Cancel invitation?",
            renderTarget = onOpen => {
              TooltipL(
                position = PortalPosition.TopCenter,
                renderTarget = ButtonL(
                  style = ButtonL.Style.Minimal(
                    icon = Option(Icon.Glyph.Cross),
                    height = ButtonL.Height.Fix32
                  ),
                  onClick = onOpen.contramap(_ => ())
                )(),
                renderContent = _.amend("Cancel invitation")
              )()
            }
          )()
        case _: FundSubRiaEntityAdvisor => div()
      }
    },
    width = Option(56)
  )

  def apply(): HtmlElement = {
    TableL[FundGroupAdvisor](
      options = TableL.Options(
        layout = TableL.Layout.FitColumns,
        rowHeight = Option(64)
      ),
      columns = List(
        advisorColumn,
        subscriptionsCreatedColumn,
        roleColumn,
        statusColumn,
        actionColumn
      ),
      dataSignal = Val(group.state match {
        case RiaFundGroupState.RiaEntityNotLinked(invitedAdvisors) => invitedAdvisors.toList
        case RiaEntityLinked(linkedRiaEntity, invalidAdvisors) =>
          invalidAdvisors.toList ++ linkedRiaEntity.advisors.toList
      })
    )
  }

}
