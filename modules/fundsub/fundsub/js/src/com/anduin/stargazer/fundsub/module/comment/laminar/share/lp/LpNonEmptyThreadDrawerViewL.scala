// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.share.lp

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.divider.Divider
import design.anduin.components.divider.laminar.DividerL
import design.anduin.components.icon.Icon
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.formcomment.*
import anduin.id.fundsub.FundSubLpId
import anduin.model.common.user.UserId
import anduin.protobuf.actionlogger.formcomment.FormCommentViewSource
import anduin.stargazer.service.formcomment.FormCommentCommons.CommentThread
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.comment.laminar.share.{CommentsListL, NewCommentBoxL}
import com.anduin.stargazer.fundsub.module.comment.laminar.{ReopenCommentThreadButtonL, ResolveCommentThreadButtonL}
import com.anduin.stargazer.service.account.AccountUtils

private[laminar] case class LpNonEmptyThreadDrawerViewL(
  threadSignal: Signal[CommentThread],
  isCommentMentioningEnabledSignal: Signal[Boolean],
  initialComment: String,
  onSwitchAllComments: Observer[Unit],
  onClose: Observer[Unit],
  localCommentsSignal: Signal[Seq[CommentsListL.ThreadActivityItem.Comment]],
  onUpdateLocalComments: Observer[Seq[CommentsListL.ThreadActivityItem.Comment]],
  isPendingThreadSignal: Signal[Boolean],
  handleAddCommentReply: (CommentThread, NewCommentBoxL.AddCommentData, Option[UserId]) => Unit,
  onSeenThread: Observer[FundSubLpId] = Observer.empty,
  isLpResolvingCommentDisabledSignal: Signal[Boolean]
) {

  private val onAddCommentEventBus = new EventBus[NewCommentBoxL.AddCommentData]
  private val markAsReadEventBus = new EventBus[Unit]
  private val resetCommentBoxVar = Var(false)

  private val showReplyBoxSignal = !threadSignal.map(_.isResolved)

  private val scrollToLatestCommentActivityEventBus = new EventBus[Unit]

  private def handleMarkAsRead(thread: CommentThread) = {
    AirStreamUtils.taskToStream {
      for {
        _ <- FundSubEndpointClient
          .markWholeCommentThreadAsSeen(
            MarkWholeCommentThreadAsSeenParams(thread.fundSubLpId, thread.id)
          )
          .unit
      } yield onSeenThread.onNext(thread.fundSubLpId)
    }
  }

  private def renderResolveOrReopenButtonIfNeeded = {
    val shouldShowResolveOrReopenButtonSignal =
      !isLpResolvingCommentDisabledSignal || threadSignal.map(_.isResolved)

    child <-- shouldShowResolveOrReopenButtonSignal.splitBoolean(
      whenTrue = _ =>
        div(
          tw.flexFill.flex.justifyEnd,
          child <-- threadSignal
            .map(_.isResolved)
            .splitBoolean(
              whenTrue = _ =>
                ReopenCommentThreadButtonL(
                  threadSignal = threadSignal,
                  onReopenThread = resetCommentBoxVar.writer.contramap(_ => true),
                  viewSource = FormCommentViewSource.FormCommentThreadPanel,
                  buttonType = ReopenCommentThreadButtonL.ButtonType.Icon
                )(),
              whenFalse = _ =>
                ResolveCommentThreadButtonL(
                  threadSignal = threadSignal,
                  onResolveThread = resetCommentBoxVar.writer.contramap(_ => true),
                  viewSource = FormCommentViewSource.FormCommentThreadPanel,
                  buttonType = ResolveCommentThreadButtonL.ButtonType.Icon
                )()
            )
        ),
      whenFalse = _ => emptyNode
    )

  }

  private def renderReplyBox = {
    child <-- showReplyBoxSignal.splitBoolean(
      whenTrue = _ =>
        div(
          ComponentUtils.testIdL("CommentBoxContainer"),
          tw.borderTop.borderGray3.p16,
          child <-- resetCommentBoxVar.signal.distinct
            .map { resetCommentBox =>
              NewCommentBoxL(
                isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
                lpIdSignal = threadSignal.map(_.fundSubLpId),
                isPublicSignal = threadSignal.map(_.isPublic),
                isBlockedSignal = isPendingThreadSignal,
                initialComment = if (resetCommentBox) {
                  ""
                } else {
                  initialComment
                },
                newCommentBoxView = NewCommentBoxL.View.InvestorSideSharedCommentView,
                onAddComment = Observer[NewCommentBoxL.AddCommentData] { data =>
                  onAddCommentEventBus.emit(data)
                },
                enableLpAutoNotificationOptSignal = Val(None),
                isDrawerViewSignal = Val(true)
              )()
            }
        ),
      whenFalse = _ => emptyNode
    )
  }

  def apply(): HtmlElement = {
    div(
      ComponentUtils.testIdL("LPCommentPanel"),
      markAsReadEventBus.events
        .sample(threadSignal)
        .flatMapSwitch(handleMarkAsRead) --> Observer.empty,
      onAddCommentEventBus.events
        .withCurrentValueOf(
          threadSignal,
          AccountUtils.currentUserIdObs
        )
        .map { case (data, thread, currentUserIdOpt) =>
          handleAddCommentReply(
            thread,
            data,
            currentUserIdOpt
          )
        } --> Observer.empty,
      tw.flex.flexCol.hPc100,
      renderHeaderRow,
      renderResolveCommentDisableReason,
      div(
        renderListComments,
        inContext { element =>
          element.amend(
            tw.flexFill.overflowAuto,
            scrollToLatestCommentActivityEventBus.events.map { _ =>
              element.ref.scrollTop = element.ref.scrollHeight.toDouble
            } --> Observer.empty,
            threadSignal
              .combineWith(localCommentsSignal)
              .map { case (thread, localComments) =>
                thread.numOfReply + thread.activities.size + localComments.size
              }
              .distinct
              .mapToUnit --> scrollToLatestCommentActivityEventBus.writer
          )
        }
      ),
      renderReplyBox,
      onMountCallback { _ =>
        markAsReadEventBus.emit(())
      }
    )
  }

  private def renderResolveCommentDisableReason = {
    child <-- (isLpResolvingCommentDisabledSignal && !threadSignal.map(_.isResolved))
      .splitBoolean(
        whenTrue = _ =>
          div(
            tw.py8.pl16.pr8.borderBottom.borderGray3,
            tw.textCenter.textGray7.text11,
            "Comment threads can only be resolved by the fund team"
          ),
        whenFalse = _ => emptyNode
      )
  }

  private def renderHeaderRow = {
    div(
      tw.flex.itemsCenter,
      tw.borderGray3.border1.borderBottom.p16,
      div(
        TooltipL(
          renderContent = _.amend("Back to all comments"),
          renderTarget = {
            ButtonL(
              testId = "BackToAllCommentsButton",
              style = ButtonL.Style.Minimal(icon = Option(Icon.Glyph.ChevronLeft)),
              onClick = onSwitchAllComments.contramap(_ => ())
            )()
          }
        )()
      ),
      renderResolveOrReopenButtonIfNeeded,
      renderCloseButtonForMobile
    )
  }

  private def renderCloseButtonForMobile = {
    // Close button that is only shown in small screens
    div(
      tw.block,
      tw.sm(tw.hidden),
      div(
        tw.flex.itemsCenter,
        DividerL(direction = Divider.Direction.Vertical)(),
        ButtonL(
          testId = "CloseCommentsThreadButton",
          style = ButtonL.Style.Full(
            height = ButtonL.Height.Fix32,
            icon = Option(Icon.Glyph.Cross)
          ),
          onClick = onClose.contramap(_ => ())
        )()
      )
    )
  }

  private def renderListComments = {
    div(
      threadSignal.map(_.isPublic.equals(false)).cls(tw.bgGray1),
      CommentsListL(
        threadSignal = threadSignal,
        canMakePublicCommentSignal = Val(true),
        isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
        onCopyInternalComment = Observer.empty,
        localCommentsSignal = localCommentsSignal,
        isPendingThreadSignal = isPendingThreadSignal,
        onUpdateLocalComments = onUpdateLocalComments,
        isFundSideViewSignal = Val(false),
        inactiveDaysThresholdSignal = Val(None),
        isDrawerViewSignal = Val(true)
      )()
    )
  }

}
