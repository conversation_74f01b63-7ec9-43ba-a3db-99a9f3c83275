// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.share

import com.raquo.laminar.api.L.*
import design.anduin.components.util.ComponentUtils
import org.scalajs.dom

import anduin.id.entity.EntityId
import anduin.id.fundsub.FundSubLpId
import anduin.protobuf.fundsub.comment.FormQuestionData
import com.anduin.stargazer.client.modules.routing.RoutingUtils.Router
import stargazer.component.routing.laminar.WithReactRouterL
import stargazer.model.routing.DynamicAuthPage.FundSubManageLpDocPage

private[laminar] case class FieldSummaryInfo(
  formQuestionData: FormQuestionData,
  fundEntityId: EntityId,
  fundSubLpId: FundSubLpId,
  isPublic: Boolean,
  isHidden: Boolean
) {

  private def viewCommentOnInvestorForm(router: Router): Unit = {
    val page = FundSubManageLpDocPage(
      entityId = fundEntityId,
      lpId = fundSubLpId,
      params = Map(
        FundSubManageLpDocPage.prevPageKey -> FundSubManageLpDocPage.PrevPage.FundAdminDashboard.value,
        FundSubManageLpDocPage.targetFieldAlias -> formQuestionData.fieldAlias,
        FundSubManageLpDocPage.targetThreadIsPublicComment -> isPublic.toString
      )
    )
    dom.window.open(router.urlFor(page).value, "_blank")
    ()
  }

  def apply(): HtmlElement = {
    WithReactRouterL { router =>
      div(
        ComponentUtils.testIdL("FieldCommentSection"),
        CommentAnchorPointDescriptionL(
          topicDataSignal = Val(formQuestionData),
          isHiddenSignal = Val(isHidden),
          onJumpToTarget = Observer { _ =>
            if (!isHidden) {
              viewCommentOnInvestorForm(router)
            }
          },
          moveToAnchorPointActionType = CommentAnchorPointDescriptionL.MoveToAnchorPointType.OpenInNewTab
        )()
      )
    }
  }

}
