// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent

import com.raquo.laminar.api.L.*
import design.anduin.components.toast.Toast

import anduin.frontend.AirStreamUtils
import anduin.id.fundsub.FundSubLpId
import com.anduin.stargazer.fundsub.client.FundSubInvestorGroupEndpointClient

private[laminar] case class FetchLpPermissionL(
  lpId: FundSubLpId,
  renderChildren: FetchLpPermissionL.RenderChildren => HtmlElement
) {

  private val fetchPermissionEventBus = new EventBus[FundSubLpId]
  private val permissionVar = Var[FetchLpPermissionL.Permission](FetchLpPermissionL.Permission.Checking)

  private def fetchPermission(lpId: FundSubLpId) = {
    AirStreamUtils.taskToStreamDEPRECATED {
      FundSubInvestorGroupEndpointClient
        .checkIfUserCanManageInvestor(lpId)
        .map(
          _.fold(
            _ => {
              Toast.error("Failed to fetch permission to investor")
              FetchLpPermissionL.Permission.NoPermission
            },
            hasPermission => {
              if (hasPermission) {
                FetchLpPermissionL.Permission.HasPermission
              } else {
                FetchLpPermissionL.Permission.NoPermission
              }
            }
          )
        )
    }
  }

  def apply(): HtmlElement = {
    renderChildren(
      FetchLpPermissionL.RenderChildren(
        permissionSignal = permissionVar.signal,
        setPermission = permissionVar.writer
      )
    ).amend(
      fetchPermissionEventBus.events.flatMapSwitch(fetchPermission) --> permissionVar.writer,
      onMountCallback { _ =>
        fetchPermissionEventBus.emit(lpId)
      }
    )
  }

}

private[laminar] object FetchLpPermissionL {

  sealed trait Permission derives CanEqual

  object Permission {
    object Checking extends Permission
    object NoPermission extends Permission
    object HasPermission extends Permission
  }

  case class RenderChildren(
    permissionSignal: Signal[Permission],
    setPermission: Observer[Permission]
  )

}
