// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings

import design.anduin.components.button.Button
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import japgolly.scalajs.react.{BackendScope, Callback, ScalaComponent}
import org.scalajs.dom.html.Div

import anduin.fundsub.endpoint.auditlog.EmailLogStatus.EmailLogStatusCode
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.id.email.InternalEmailId
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.investor.settings.EmailDetailDrawer.{
  renderEmailAddressString,
  renderEmailStatusIcon
}

private[settings] final case class EmailBodyFetcher(
  internalEmailId: InternalEmailId,
  emailAddress: EmailAddress,
  emailStatusCore: EmailLogStatusCode,
  onDoneFetch: Option[String] => Callback,
  onFetch: Callback,
  isSelected: Boolean
) {
  def apply(): VdomElement = EmailBodyFetcher.component(this)
}

private[settings] object EmailBodyFetcher {
  private type Props = EmailBodyFetcher

  private final case class State(
    emailBodyContentOpt: Option[String] = None,
    isContentLoaded: Boolean = false
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    def renderEmailStatusRow(
      props: Props,
      state: State
    ): VdomTagOf[Div] = {
      <.div(
        Button(
          style = Button.Style.Minimal(isSelected = props.isSelected, isFullWidth = true),
          onClick = if (state.isContentLoaded) { props.onDoneFetch(state.emailBodyContentOpt) }
          else { fetchEmailBodyContent(props) },
          unsafeTagMod = TagMod.when(props.isSelected) {
            ^.backgroundColor := "#DFE9F0"
          }
        )(
          <.div(
            tw.breakAll,
            tw.flex.spaceX8.wPc100.itemsCenter,
            renderEmailStatusIcon(props.emailStatusCore),
            <.div(tw.truncate.fontNormal.textGray8, renderEmailAddressString(props.emailAddress))
          )
        )
      )
    }

    def render(props: Props, state: State): VdomElement = {
      renderEmailStatusRow(props, state)
    }

    def fetchEmailBodyContent(props: Props): Callback = {
      for {
        _ <- props.onFetch
        _ <- ZIOUtils.toReactCallback {
          FundSubEndpointClient
            .getEmailBodyContentString(props.internalEmailId)
            .map(
              _.fold(
                _ => Toast.errorCallback("Failed to get email body content"),
                res => scope.modState(_.copy(emailBodyContentOpt = res, isContentLoaded = true), props.onDoneFetch(res))
              )
            )
        }
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .componentDidMount(scope =>
      if (scope.props.isSelected) {
        scope.backend.fetchEmailBodyContent(scope.props)
      } else {
        Callback.empty
      }
    )
    .build

}
