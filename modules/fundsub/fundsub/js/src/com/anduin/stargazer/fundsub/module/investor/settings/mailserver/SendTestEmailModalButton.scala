// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.mailserver

import design.anduin.components.button.Button
import design.anduin.components.modal.Modal
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.id.fundsub.FundSubId

private[mailserver] final case class SendTestEmailModalButton(
  fundSubId: FundSubId
) {
  def apply(): VdomElement = SendTestEmailModalButton.component(this)
}

private[mailserver] object SendTestEmailModalButton {
  private type Props = SendTestEmailModalButton

  private def render(props: Props): VdomElement = {
    Modal(
      title = "Send test email",
      renderTarget = onClick =>
        Button(
          onClick = onClick
        )("Send test email"),
      renderContent = onClose =>
        SendTestEmailModalBody(
          props.fundSubId,
          onClose
        )()
    )()
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
