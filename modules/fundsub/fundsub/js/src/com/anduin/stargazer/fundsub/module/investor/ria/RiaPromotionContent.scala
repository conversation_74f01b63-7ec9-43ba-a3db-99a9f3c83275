// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.ria

import design.anduin.style.tw.*
import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL, ModalL}
import design.anduin.components.toast.Toast
import design.anduin.components.wrapper.laminar.WrapperL
import japgolly.scalajs.react.callback.Callback
import zio.ZIO

import anduin.actionlogger.ActionEventLoggerJs
import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.admin.UpdateRiaBannerVisibilityParams
import anduin.id.fundsub.FundSubId
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.common.FundSubSupportingContactsModal
import com.anduin.stargazer.fundsub.module.investor.WithFundSubPublicModel
import com.anduin.stargazer.service.actionlogger.{
  ActionEventGpViewRiaBannerParams,
  ActionEventGpClickContactThroughRiaBannerParams
}

final case class RiaPromotionContent(
  fundSubId: FundSubId
) {

  private val isClosingVar = Var(false)
  private val isClosingSignal = isClosingVar.signal

  private val hideBannerEventBus = new EventBus[Unit]

  def apply(): HtmlElement = {
    div(
      onMountCallback { _ =>
        ActionEventLoggerJs
          .logEventCb(
            ActionEventGpViewRiaBannerParams(fundSubId)
          )
          .runNow()
      },
      tw.flex.flexCol.itemsCenter.p24,
      div(
        maxWidth.px := 540,
        tw.spaceY24,
        div(
          tw.rounded12.bgPrimary1.flex.itemsCenter.px16.py8.spaceX16,
          renderIllustration(),
          div(
            tw.flexFill.spaceY12,
            div(
              tw.fontSemiBold.text15,
              "Take your advisor relation to the next level with Anduin's Advisor Management"
            ),
            div(
              tw.textGray7,
              span("Streamline and improve the efficiency of your collaboration with our new "),
              span(tw.fontSemiBold, "Advisor Management"),
              span(" feature.")
            )
          )
        ),
        div(
          tw.spaceY16,
          div(
            tw.spaceY12,
            div(
              tw.fontSemiBold.text15,
              "Improve your workflow with Advisor Management"
            ),
            li("Streamlined onboarding for new and existing advisors"),
            li("Manage all invited advisors efficiently in one place"),
            li("Centralized dashboard to monitor all advisor's subscription activities")
          ),
          div(
            tw.spaceY12,
            div(
              tw.fontSemiBold.text15,
              "Ensure premium advisor experience via Advisor Advantage app"
            ),
            li(
              span("The "),
              span(tw.fontSemiBold, "Advisor Advantage"),
              span(" app is optimized for advisor's day-to-day workflow")
            ),
            li("Create and manage client's subscriptions through via an intuitive interface"),
            li("Enhanced organizational control to manage advisor entity's members")
          ),
          div(
            span("Contact us to learn more about Anduin's "),
            span(tw.fontSemiBold, "Advisor Management"),
            span(".")
          )
        ),
        div(
          tw.spaceX8.flex,
          ModalL(
            isClosable = None,
            renderTitle = _ => "Contact support",
            renderTarget = openModal =>
              ButtonL(
                style = ButtonL.Style.Full(
                  color = ButtonL.Color.Primary,
                  icon = Option(Icon.Glyph.Envelope)
                ),
                onClick = openModal.contramap(_ =>
                  ActionEventLoggerJs
                    .logEventCb(
                      ActionEventGpClickContactThroughRiaBannerParams(fundSubId)
                    )
                    .runNow()
                )
              )("Contact us"),
            renderContent = onCloseDlg => {
              WrapperL(
                WithFundSubPublicModel(fundSubId) { (fsModel, _) =>
                  FundSubSupportingContactsModal(
                    fundSubId = Some(fundSubId),
                    supportContacts = fsModel.supportingContacts,
                    onCloseModal = Callback(onCloseDlg.onNext(()))
                  )()
                }
              )
            }
          )(),
          ModalL(
            isClosable = None,
            renderTitle = _ => "Close advisor management tab?",
            renderTarget = open =>
              ButtonL(
                onClick = open.contramap(_ => ())
              )("Hide tab"),
            renderContent = close =>
              div(
                ModalBodyL(
                  div(
                    span("After closing, you can look up for Advisor Management information in "),
                    span(tw.fontSemiBold, "Settings / General")
                  )
                ),
                ModalFooterWCancelL(close)(
                  ButtonL(
                    style = ButtonL.Style.Full(
                      color = ButtonL.Color.Primary,
                      isBusy = isClosingSignal
                    ),
                    onClick = hideBannerEventBus.toObserver.contramap(_ => ())
                  )("Close this tab").amend(
                    hideBannerEventBus.events.flatMapSwitch(_ => hideRiaBanner(close)) --> Observer.empty
                  )
                )
              )
          )()
        )
      )
    )
  }

  private def hideRiaBanner(close: Observer[Unit]) = {
    AirStreamUtils.taskToStream(
      for {
        _ <- ZIO.succeed(isClosingVar.set(true))
        _ <- FundSubEndpointClient
          .updateRiaBannerVisibility(
            UpdateRiaBannerVisibilityParams(
              fundSubId = fundSubId,
              enabled = false
            )
          )
          .map(
            _.fold(
              _ => Toast.error("Failed close tab"),
              _ => close.onNext(())
            )
          )
      } yield isClosingVar.set(false)
    )
  }

  private def renderIllustration() = {
    svg.svg(
      svg.width := "120",
      svg.height := "120",
      svg.viewBox := "0 0 120 120",
      svg.fill := "none",
      svg.xmlns := "http://www.w3.org/2000/svg",
      svg.path(
        svg.d := "M81.5232 10.4038L4.99734 50.666L35.2392 108.146L111.765 67.8841L81.5232 10.4038Z",
        svg.fill := "url(#paint0_linear_15_14914)"
      ),
      svg.path(
        svg.d := "M35.0312 108.255L4.78639 50.7758C4.75806 50.7199 4.75275 50.6551 4.77159 50.5953C4.79042 50.5355 4.83192 50.4854 4.88719 50.4558L81.4112 10.1862C81.4669 10.1578 81.5315 10.1524 81.5912 10.1711C81.6512 10.1897 81.7016 10.2311 81.7312 10.2862L92.9896 31.6742L92.5648 31.8974L81.4216 10.7222L5.32319 50.7646L35.456 108.031L35.0312 108.255Z",
        svg.fill := "#001958"
      ),
      svg.path(
        svg.d := "M36.1088 107.958L35.8856 107.534L36.2392 107.348L36.4632 107.772L36.1088 107.958ZM38.5888 106.655L38.3648 106.23L38.7192 106.044L38.9424 106.469L38.5888 106.655ZM41.0688 105.351L40.8456 104.926L41.1992 104.74L41.4232 105.165L41.0688 105.351ZM43.5488 104.047L43.3256 103.622L43.68 103.436L43.9032 103.861L43.5488 104.047ZM46.0288 102.743L45.8056 102.318L46.1592 102.132L46.376 102.554L46.0288 102.743ZM48.5088 101.439L48.2856 101.014L48.6392 100.829L48.8632 101.253L48.5088 101.439ZM50.9888 100.136L50.7648 99.7111L51.1192 99.5247L51.3424 99.9495L50.9888 100.136ZM53.4688 98.8319L53.2456 98.4071L53.5992 98.2207L53.8224 98.6455L53.4688 98.8319ZM55.9488 97.5279L55.7256 97.1031L56.08 96.9167L56.3032 97.3415L55.9488 97.5279ZM58.4288 96.2239L58.2048 95.7991L58.5592 95.6127L58.7824 96.0375L58.4288 96.2239ZM60.9088 94.9199L60.6856 94.4951L61.0392 94.3087L61.2632 94.7335L60.9088 94.9199ZM63.3888 93.6159L63.1656 93.1919L63.52 93.0055L63.7432 93.4303L63.3888 93.6159ZM65.8688 92.3127L65.6456 91.8879L65.9992 91.7015L66.2224 92.1263L65.8688 92.3127ZM68.3488 91.0087L68.1256 90.584L68.456 90.3943L68.68 90.8191L68.3488 91.0087ZM70.8288 89.7048L70.6048 89.2799L70.9592 89.0935L71.1824 89.5183L70.8288 89.7048ZM73.3088 88.4007L73.0856 87.9759L73.4392 87.7895L73.6624 88.2143L73.3088 88.4007ZM75.7888 87.0967L75.5656 86.6719L75.9192 86.4863L76.1432 86.9111L75.7888 87.0967ZM78.2688 85.7935L78.0448 85.3687L78.3992 85.1823L78.6224 85.6071L78.2688 85.7935ZM80.7488 84.4895L80.5256 84.0648L80.8792 83.8783L81.1032 84.3031L80.7488 84.4895ZM83.2288 83.1855L83.0056 82.7607L83.36 82.5743L83.5832 82.9991L83.2288 83.1855ZM85.7088 81.8815L85.4856 81.4567L85.8392 81.2703L86.0624 81.6951L85.7088 81.8815ZM88.1888 80.5775L87.9656 80.1527L88.3192 79.9668L88.5432 80.3919L88.1888 80.5775ZM90.6688 79.274L90.4448 78.8492L90.7992 78.6628L91.0224 79.0876L90.6688 79.274ZM93.1488 77.97L92.9256 77.5452L93.2792 77.3588L93.5024 77.7836L93.1488 77.97ZM95.6288 76.666L95.4056 76.2412L95.76 76.0548L95.9832 76.4796L95.6288 76.666ZM98.1088 75.362L97.8848 74.9372L98.2392 74.7508L98.4624 75.1756L98.1088 75.362ZM100.589 74.058L100.366 73.6332L100.719 73.4476L100.943 73.8724L100.589 74.058ZM103.069 72.7548L102.845 72.33L103.199 72.1436L103.422 72.5684L103.069 72.7548ZM105.549 71.4508L105.326 71.026L105.679 70.8396L105.902 71.2644L105.549 71.4508ZM108.029 70.1468L107.806 69.722L108.159 69.5356L108.383 69.9604L108.029 70.1468ZM110.509 68.8428L110.285 68.418L110.639 68.2316L110.862 68.6564L110.509 68.8428ZM111.069 66.9436L110.882 66.5892L111.307 66.366L111.494 66.7196L111.069 66.9436ZM109.765 64.4636L109.578 64.11L110.004 63.886L110.19 64.2404L109.765 64.4636ZM108.462 61.9836L108.275 61.6292L108.7 61.406L108.886 61.7596L108.462 61.9836ZM107.096 59.514L106.91 59.1596L107.334 58.9364L107.521 59.29L107.096 59.514ZM105.792 57.034L105.606 56.6804L106.03 56.4564L106.217 56.8108L105.792 57.034ZM104.488 54.554L104.302 54.1996L104.726 53.9764L104.913 54.33L104.488 54.554ZM103.184 52.074L102.998 51.7204L103.423 51.4972L103.609 51.8508L103.184 52.074ZM101.881 49.594L101.694 49.2404L102.119 49.0164L102.306 49.3708L101.881 49.594ZM100.577 47.114L100.39 46.7596L100.815 46.5364L101.002 46.89L100.577 47.114ZM99.2728 44.634L99.0864 44.2804L99.5112 44.0564L99.6976 44.4108L99.2728 44.634ZM97.9688 42.154L97.7824 41.7996L98.2072 41.5764L98.3936 41.93L97.9688 42.154ZM96.6648 39.674L96.4784 39.3196L96.904 39.0964L97.0896 39.45L96.6648 39.674ZM95.3616 37.194L95.1752 36.8404L95.6 36.6164L95.7856 36.9708L95.3616 37.194ZM94.0576 34.714L93.8712 34.3596L94.296 34.154L94.4824 34.5076L94.0576 34.714ZM92.7536 32.234L92.5672 31.8804L92.992 31.6572L93.1784 32.0108L92.7536 32.234Z",
        svg.fill := "#001958"
      ),
      svg.path(svg.d := "M94.0704 19.2829L101.11 18.4077L100.044 42.7381L94.0704 19.2829Z", svg.fill := "#0049A6"),
      svg.path(
        svg.d := "M85.8576 21.1938C76.1504 26.7378 63.02 29.5642 50.6656 31.9138C50.0498 32.0297 49.4775 32.3118 49.0106 32.7296C48.5437 33.1474 48.2 33.6851 48.0168 34.2842C46.936 37.8338 44.536 45.1434 42.1696 48.5378C41.9326 48.8782 41.8043 49.2823 41.8016 49.697C41.792 51.7026 41.736 57.697 41.4136 60.1882C41.3907 60.3656 41.3934 60.5453 41.4216 60.7218C41.592 61.8018 42.2216 65.829 42.6216 69.1218C42.6489 69.3494 42.7613 69.5583 42.9363 69.7064C43.1113 69.8544 43.3358 69.9308 43.5648 69.92C43.7165 69.913 43.8639 69.8679 43.9936 69.7891C44.756 69.3187 45.9704 68.1202 46.1752 66.7698L46.6952 71.2498C46.7576 71.7746 47.5752 71.7242 48.0216 71.445C48.9168 70.885 49.9048 70.1026 50.1248 68.1202C50.1248 68.1202 52.4448 68.317 53.3424 65.0538C53.3424 65.0538 54.7024 65.0538 57.4872 60.5546C57.5864 60.3946 57.6632 60.221 57.7904 60.0818C58.4192 59.3946 60.3592 57.2306 61.0184 56.0122C61.1237 55.8171 61.2559 55.6377 61.4112 55.4794C62.1872 54.6858 64.4512 52.317 65.2712 50.9114C66.2632 49.205 70.1288 48.6306 72.9512 48.1386C74.7152 47.8306 79.28 46.645 81.3424 42.6626C81.3424 42.6626 83.5472 40.6002 88.5248 41.1698L85.8576 21.1938Z",
        svg.fill := "#FAA250"
      ),
      svg.path(
        svg.d := "M107.475 78.7315C105.653 78.7315 103.871 78.1912 102.356 77.1788C101.223 76.4219 100.272 75.4233 99.572 74.2548C98.8716 73.0863 98.4389 71.777 98.3053 70.4212C98.1716 69.0654 98.3402 67.6969 98.799 66.4141C99.2578 65.1313 99.9953 63.9662 100.958 63.0027C102.247 61.7139 103.888 60.8361 105.676 60.4802C107.463 60.1243 109.315 60.3063 110.999 61.0032C112.683 61.7 114.122 62.8806 115.135 64.3955C116.148 65.9105 116.689 67.6917 116.69 69.5139C116.687 71.9573 115.716 74.3 113.989 76.0283C112.261 77.7562 109.919 78.7284 107.475 78.7315ZM107.475 60.7827C105.747 60.7827 104.058 61.2951 102.622 62.255C101.186 63.2147 100.066 64.5791 99.4056 66.1754C98.7441 67.7714 98.5712 69.5279 98.9088 71.2223C99.2463 72.9166 100.078 74.4728 101.3 75.6944C102.522 76.9157 104.078 77.7473 105.773 78.0841C107.467 78.4207 109.224 78.2473 110.82 77.5857C112.416 76.9241 113.78 75.8041 114.739 74.3674C115.699 72.9306 116.21 71.2416 116.21 69.5139C116.206 67.1987 115.285 64.9793 113.647 63.3427C112.01 61.7058 109.79 60.7851 107.475 60.7827Z",
        svg.fill := "#0049A6"
      ),
      svg.path(
        svg.d := "M50.7928 109.821L50.5672 109.491L50.9672 109.22L51.1928 109.551L50.7928 109.821ZM49.216 107.507L48.9912 107.176L49.3912 106.907L49.616 107.237L49.216 107.507ZM47.64 105.195L47.4144 104.864L47.8144 104.594L48.04 104.924L47.64 105.195ZM46.064 102.88L45.8384 102.55L46.2384 102.279L46.464 102.611L46.064 102.88ZM44.4872 100.567L44.2624 100.235L44.6624 99.9658L44.8872 100.296L44.4872 100.567ZM42.9112 98.2522L42.6864 97.9218L43.0864 97.6514L43.312 97.9818L42.9112 98.2522ZM41.336 95.9362L41.1104 95.6058L41.5104 95.3354L41.736 95.6658L41.336 95.9362ZM39.7592 93.6218L39.5344 93.2914L39.9344 93.021L40.1592 93.3522L39.7592 93.6218ZM38.1832 91.3082L37.9584 90.977L38.3584 90.7074L38.5832 91.0378L38.1832 91.3082ZM36.6072 88.9938L36.3816 88.6634L36.7816 88.393L37.0072 88.7234L36.6072 88.9938ZM35.0304 86.6794L34.8056 86.349L35.2056 86.0786L35.4304 86.409L35.0304 86.6794ZM33.4544 84.365L33.2296 84.0346L33.6296 83.7642L33.8544 84.0954L33.4544 84.365ZM31.8784 82.0514L31.6528 81.721L32.0528 81.4506L32.2784 81.781L31.8784 82.0514ZM30.3024 79.7367L30.0768 79.4063L30.4768 79.1359L30.7016 79.4663L30.3024 79.7367ZM28.7256 77.4223L28.5008 77.0919L28.9008 76.8215L29.1256 77.1519L28.7256 77.4223ZM27.1496 75.1087L26.924 74.7775L27.324 74.5079L27.5496 74.8383L27.1496 75.1087ZM25.576 72.7943L25.3504 72.4639L25.7504 72.1935L25.9752 72.5239L25.576 72.7943ZM23.9992 70.4799L23.7744 70.1495L24.1744 69.8791L24.3992 70.2095L23.9992 70.4799ZM22.4232 68.1655L22.1944 67.8343L22.5944 67.5639L22.82 67.8951L22.4232 68.1655ZM20.8472 65.8519L20.616 65.5207L21.016 65.2511L21.2416 65.5815L20.8472 65.8519ZM19.2704 63.5375L19.0456 63.2071L19.4456 62.9367L19.6704 63.2671L19.2704 63.5375ZM17.6944 61.2231L17.4696 60.8927L17.8696 60.6223L18.088 60.9543L17.6944 61.2231ZM16.1184 58.9087L15.8928 58.5783L16.2928 58.3079L16.5184 58.6391L16.1184 58.9087ZM14.536 56.5951L14.3112 56.2639L14.7112 55.9943L14.936 56.3247L14.536 56.5951ZM12.96 54.2807L12.7352 53.9503L13.1352 53.6799L13.36 54.0103L12.96 54.2807ZM11.384 51.9663L11.1584 51.6359L11.5584 51.3655L11.784 51.6959L11.384 51.9663ZM9.8072 49.6527L9.5824 49.3215L9.9824 49.0519L10.2072 49.3823L9.8072 49.6527ZM8.2312 47.3383L8.0064 47.0079L8.4064 46.7375L8.6312 47.0679L8.2312 47.3383ZM6.6552 45.0239L6.4296 44.6935L6.8296 44.4231L7.0552 44.7535L6.6552 45.0239ZM5.0792 42.7095L4.856 42.3791L5.256 42.1087L5.4808 42.4399L5.0792 42.7095ZM3.5048 40.3943L3.28 40.0631L3.68 39.7935L3.9048 40.1239L3.5048 40.3943Z",
        svg.fill := "#001958"
      ),
      svg.path(
        svg.d := "M68.3648 77.8484C68.8773 77.8486 69.3826 77.7274 69.8392 77.4948C69.9008 77.4641 69.9602 77.4291 70.0168 77.39C70.364 77.1292 70.7992 76.6508 70.0168 75.2596C69.2344 73.8684 68.9768 72.7388 72.32 72.7388C75.6632 72.7388 75.4064 73.8684 74.624 75.2596C73.8416 76.6508 74.276 77.1292 74.624 77.39C74.6843 77.4317 74.7478 77.4689 74.8136 77.5012C75.303 77.748 75.8439 77.8755 76.392 77.8732H84.2608V101.639H60.4952V77.8732L68.3648 77.8484Z",
        svg.fill := "#FAA250"
      ),
      svg.path(
        svg.d := "M84.2616 101.879H60.4952C60.4316 101.879 60.3705 101.854 60.3255 101.809C60.2806 101.764 60.2554 101.703 60.2552 101.639V77.8734C60.2552 77.8098 60.2805 77.7487 60.3255 77.7037C60.3705 77.6587 60.4315 77.6334 60.4952 77.6334L68.3648 77.6086C68.8392 77.6088 69.3069 77.4966 69.7296 77.2814C69.7798 77.2574 69.8279 77.2293 69.8736 77.1974C70.092 77.0374 70.548 76.6918 69.808 75.3774C69.408 74.6638 69.0816 73.9238 69.4208 73.3438C69.76 72.7638 70.6792 72.499 72.3208 72.499C73.9624 72.499 74.8808 72.767 75.2216 73.3438C75.5624 73.9206 75.2352 74.6638 74.8344 75.3774C74.0944 76.6918 74.5504 77.0334 74.7688 77.1982C74.8181 77.2318 74.8701 77.2615 74.924 77.287C75.3796 77.5163 75.8828 77.635 76.3928 77.6334H84.2624C84.3264 77.6334 84.3872 77.6587 84.432 77.7037C84.4768 77.7487 84.5024 77.8097 84.5024 77.8734V101.639C84.5023 101.703 84.477 101.764 84.432 101.809C84.4096 101.831 84.3832 101.849 84.3536 101.861C84.3248 101.873 84.2936 101.879 84.2616 101.879ZM60.7352 101.399H84.0216V78.1134H76.392C75.806 78.1146 75.2279 77.9776 74.7048 77.7134C74.6265 77.6751 74.5513 77.6307 74.48 77.5806C74.0464 77.255 73.5672 76.6478 74.4152 75.1406C74.7304 74.5806 75.024 73.9566 74.8064 73.5846C74.5664 73.1846 73.7344 72.9774 72.32 72.9774C70.9056 72.9774 70.0704 73.1814 69.8344 73.5846C69.6168 73.9566 69.9104 74.5798 70.2256 75.1406C71.0736 76.6478 70.5944 77.255 70.1608 77.5806C70.0936 77.6284 70.0227 77.6707 69.9488 77.707C69.4583 77.9565 68.9159 78.0866 68.3656 78.087L60.7352 78.111V101.399Z",
        svg.fill := "#001958"
      ),
      svg.path(
        svg.d := "M36.7296 77.873L44.912 77.8978C45.4245 77.8973 45.9297 78.0182 46.3864 78.2506C46.4483 78.2812 46.5076 78.3166 46.564 78.3562C46.9112 78.617 47.3464 79.0946 46.564 80.4862C45.7816 81.8774 45.524 83.0078 48.868 83.0078C52.212 83.0078 51.9536 81.8774 51.1712 80.4862C50.3888 79.0946 50.824 78.617 51.1712 78.3562C51.2724 78.2886 51.3795 78.2303 51.4912 78.1818C51.9275 77.9781 52.4033 77.8727 52.8848 77.873H60.496V85.4294C60.494 85.9772 60.6214 86.5178 60.868 87.007C60.8998 87.0726 60.937 87.1366 60.9792 87.1966C61.24 87.5446 61.7184 87.979 63.1096 87.1966C64.5008 86.4142 65.6304 86.1566 65.6304 89.5006C65.6304 92.8446 64.5 92.587 63.1096 91.8046C61.7192 91.0222 61.24 91.4566 60.9792 91.8046C60.9396 91.8606 60.9046 91.9198 60.8744 91.9822C60.6413 92.4386 60.5201 92.944 60.5208 93.4566L60.496 101.639H36.7296V77.873Z",
        svg.fill := "#0049A6"
      ),
      svg.path(
        svg.d := "M36.7296 101.879C36.666 101.879 36.6049 101.854 36.5599 101.809C36.5151 101.764 36.4898 101.703 36.4896 101.639V77.8733C36.4896 77.8096 36.5149 77.7486 36.5599 77.7036C36.6049 77.6586 36.6659 77.6333 36.7296 77.6333L44.912 77.6581C45.4623 77.6581 46.0048 77.7883 46.4952 78.0381C46.5693 78.0743 46.6402 78.1166 46.7072 78.1645C47.1408 78.4901 47.62 79.0965 46.772 80.604C46.4568 81.164 46.1632 81.7888 46.3808 82.16C46.6168 82.56 47.4528 82.7672 48.8672 82.7672C50.2816 82.7672 51.1168 82.5632 51.3528 82.16C51.5704 81.7888 51.2728 81.1648 50.9616 80.604C50.1136 79.0965 50.5928 78.4901 51.0264 78.1645C51.1402 78.0863 51.2613 78.0193 51.388 77.9645C51.8564 77.7467 52.3666 77.6337 52.8832 77.6333H60.4944C60.558 77.6333 60.6191 77.6586 60.6641 77.7036C60.7091 77.7486 60.7344 77.8096 60.7344 77.8733V85.4296C60.7327 85.9396 60.8511 86.4427 61.08 86.8984C61.106 86.952 61.1359 87.004 61.1696 87.0536C61.3336 87.272 61.6752 87.7272 62.9896 86.988C63.704 86.588 64.444 86.2616 65.024 86.6008C65.604 86.94 65.8688 87.8592 65.8688 89.5008C65.8688 91.1424 65.6 92.0608 65.024 92.4016C64.448 92.7424 63.704 92.4152 62.9896 92.0136C61.6752 91.2744 61.3336 91.7296 61.1696 91.9488C61.1394 91.9944 61.1126 92.0416 61.0896 92.092C60.8749 92.5152 60.7631 92.9831 60.7632 93.4576L60.7384 101.64C60.7384 101.703 60.7131 101.764 60.6681 101.81C60.623 101.855 60.562 101.88 60.4984 101.88L36.7296 101.879ZM36.9696 78.1141V101.399H60.256L60.28 93.456C60.2799 92.9054 60.4102 92.3627 60.66 91.872C60.6964 91.7976 60.7386 91.7272 60.7864 91.6608C61.112 91.2264 61.7192 90.748 63.2264 91.5952C63.7864 91.9152 64.4104 92.204 64.7824 91.9872C65.1824 91.7472 65.3896 90.9144 65.3896 89.5008C65.3896 88.0872 65.1848 87.2504 64.7824 87.0152C64.4104 86.7976 63.7872 87.0952 63.2264 87.4064C61.7192 88.2536 61.112 87.7752 60.7864 87.3408C60.7356 87.2696 60.6912 87.1944 60.6536 87.116C60.3895 86.5932 60.2524 86.0154 60.2536 85.4296V78.1133H52.884C52.4377 78.1135 51.9968 78.211 51.592 78.3989C51.4953 78.4401 51.4026 78.4902 51.3152 78.5485C51.0968 78.7125 50.6408 79.0541 51.3808 80.3688C51.7808 81.0824 52.1072 81.8232 51.768 82.4024C51.4288 82.9816 50.5088 83.2472 48.868 83.2472C47.2272 83.2472 46.308 82.9792 45.9672 82.4024C45.6264 81.8256 45.9528 81.0824 46.3544 80.3688C47.0944 79.0541 46.6384 78.7125 46.42 78.5485C46.3742 78.5181 46.326 78.4914 46.276 78.4685C45.8532 78.2535 45.3855 78.1417 44.9112 78.1421L36.9696 78.1141Z",
        svg.fill := "#0049A6"
      ),
      svg.path(
        svg.d := "M57.8824 63.6525C59.2728 64.4349 59.7512 63.9997 60.012 63.6525C60.0449 63.6033 60.074 63.5517 60.0992 63.4981C60.3625 62.9713 60.4994 62.3903 60.4992 61.8013V54.1069H84.2648V77.8733H76.3608C75.8793 77.8733 75.4037 77.7679 74.9672 77.5645C74.8556 77.5159 74.7485 77.4576 74.6472 77.3901C74.2992 77.1293 73.8648 76.6509 74.6472 75.2597C75.4296 73.8685 75.6872 72.7389 72.3432 72.7389C68.9992 72.7389 69.256 73.8685 70.04 75.2597C70.824 76.6509 70.388 77.1293 70.04 77.3901C69.9836 77.4296 69.9242 77.4646 69.8624 77.4949C69.4058 77.7277 68.9005 77.8489 68.388 77.8485L60.4952 77.8733L60.4704 69.9141C60.4711 69.4016 60.3499 68.8962 60.1168 68.4397C60.0864 68.378 60.0514 68.3186 60.012 68.2621C59.7512 67.9141 59.2728 67.4797 57.8824 68.2621C56.492 69.0445 55.3608 69.3021 55.3608 65.9581C55.3608 62.6141 56.4912 62.8741 57.8824 63.6525Z",
        svg.fill := "#FFDE85"
      ),
      svg.path(
        svg.d := "M60.4952 78.1131C60.4318 78.1128 60.3712 78.0875 60.3264 78.0427C60.2813 77.9981 60.2557 77.9374 60.2552 77.8739L60.2304 69.9139C60.2303 69.4394 60.1185 68.9716 59.904 68.5483C59.8797 68.4985 59.8516 68.4506 59.82 68.4051C59.66 68.1859 59.3144 67.7307 58 68.4699C57.2864 68.8699 56.5456 69.1971 55.9664 68.8579C55.3872 68.5187 55.1208 67.5939 55.1208 65.9555C55.1208 64.3171 55.3896 63.3955 55.9664 63.0555C56.5432 62.7155 57.2856 63.0411 58 63.4427C59.3144 64.1819 59.656 63.7267 59.82 63.5083C59.8438 63.4703 59.8654 63.4311 59.8848 63.3907C60.1293 62.8965 60.2561 62.3525 60.2552 61.8011V54.1067C60.2552 54.043 60.2805 53.982 60.3254 53.9369C60.3705 53.892 60.4315 53.8667 60.4952 53.8667H84.2616C84.3256 53.8667 84.3864 53.892 84.4312 53.9369C84.476 53.982 84.5016 54.043 84.5016 54.1067V77.8731C84.5016 77.9367 84.476 77.9977 84.4312 78.0428C84.3864 78.0878 84.3256 78.1131 84.2616 78.1131H76.3608C75.8442 78.1124 75.3339 77.9991 74.8656 77.7811C74.7391 77.7262 74.618 77.6595 74.504 77.5819C74.0696 77.2563 73.5912 76.6491 74.4384 75.1419C74.7584 74.5819 75.0472 73.9579 74.8304 73.5859C74.5944 73.1859 73.7584 72.9787 72.344 72.9787C70.9296 72.9787 70.0936 73.1827 69.8584 73.5859C69.6408 73.9579 69.9384 74.5811 70.2496 75.1419C71.0976 76.6491 70.6184 77.2563 70.184 77.5819C70.117 77.6296 70.0464 77.6718 69.9728 77.7083C69.482 77.9577 68.9393 78.0879 68.3888 78.0883L60.496 78.1131H60.4952ZM72.344 72.4987C73.9856 72.4987 74.904 72.7667 75.2448 73.3435C75.5856 73.9203 75.2584 74.6635 74.8568 75.3771C74.1176 76.6915 74.5728 77.0331 74.792 77.1979C74.8797 77.2556 74.9723 77.3054 75.0688 77.3467C75.4735 77.5349 75.9144 77.6326 76.3608 77.6331H84.0216V54.3467H60.7352V61.8011C60.736 62.4269 60.5917 63.0444 60.3136 63.6051C60.2825 63.6718 60.2458 63.7357 60.204 63.7963C59.8784 64.2299 59.272 64.7091 57.7648 63.8611C57.2048 63.5459 56.58 63.2523 56.208 63.4699C55.808 63.7051 55.6008 64.5419 55.6008 65.9555C55.6008 67.3691 55.8056 68.2059 56.208 68.4419C56.58 68.6595 57.2032 68.3619 57.7648 68.0507C59.2712 67.2027 59.8784 67.6811 60.204 68.1155C60.2519 68.1826 60.2942 68.2535 60.3304 68.3275C60.5807 68.8188 60.7109 69.3625 60.7104 69.9139L60.7344 77.6355L68.3872 77.6115C68.8618 77.6117 69.3298 77.4996 69.7528 77.2843C69.8025 77.2613 69.8504 77.2346 69.896 77.2043C70.1152 77.0395 70.5704 76.6979 69.8312 75.3835C69.4312 74.6699 69.1048 73.9299 69.444 73.3499C69.7832 72.7699 70.7024 72.4987 72.344 72.4987Z",
        svg.fill := "#001958"
      ),
      svg.path(
        svg.d := "M48.868 83.247C47.2264 83.247 46.308 82.979 45.9672 82.4022C45.6264 81.8254 45.9528 81.0822 46.3544 80.3686C47.0944 79.0539 46.6384 78.7123 46.42 78.5483C46.3742 78.5179 46.326 78.4912 46.276 78.4683C45.8532 78.2533 45.3855 78.1415 44.9112 78.1419L36.7288 78.1171C36.6651 78.1171 36.6041 78.0918 36.5591 78.0468C36.5141 78.0018 36.4888 77.9408 36.4888 77.8771V54.1067C36.4888 54.043 36.5141 53.982 36.5591 53.937C36.6041 53.892 36.6651 53.8667 36.7288 53.8667H46.5472V54.3467H36.9696V77.6339L44.9128 77.6579C45.4631 77.6579 46.0056 77.7881 46.496 78.0379C46.5702 78.0739 46.6411 78.1161 46.708 78.1643C47.1416 78.4899 47.6208 79.0963 46.7728 80.6038C46.4576 81.1638 46.164 81.7886 46.3816 82.1598C46.6176 82.5598 47.4536 82.767 48.868 82.767C50.2824 82.767 51.1176 82.563 51.3536 82.1598C51.5712 81.7886 51.2736 81.1646 50.9624 80.6038C50.1144 79.0963 50.5936 78.4899 51.0272 78.1643C51.0982 78.1135 51.1734 78.0688 51.252 78.0307C51.7752 77.7669 52.3532 77.6298 52.9392 77.6307H60.2552V69.9723C60.2548 69.5259 60.157 69.085 59.9688 68.6803C59.9279 68.5836 59.8781 68.4909 59.82 68.4035C59.66 68.1843 59.3144 67.7291 58 68.4683C57.2864 68.8683 56.5456 69.1955 55.9664 68.8563C55.3872 68.5171 55.1208 67.5939 55.1208 65.9555C55.1208 64.3171 55.3896 63.3955 55.9664 63.0555C56.5432 62.7155 57.2856 63.0411 58 63.4427C59.3144 64.1819 59.656 63.7267 59.82 63.5083C59.8516 63.4625 59.8797 63.4143 59.904 63.3643C60.1186 62.9414 60.2305 62.4738 60.2304 61.9995L60.2544 54.3467H54.296V53.8667H60.4952C60.5589 53.8667 60.6199 53.892 60.665 53.9369C60.7099 53.982 60.7352 54.043 60.7352 54.1067L60.7104 61.9995C60.7105 62.5501 60.5802 63.0929 60.3304 63.5835C60.2941 63.6574 60.2518 63.7283 60.204 63.7955C59.8784 64.2291 59.2712 64.7083 57.7648 63.8603C57.2048 63.5451 56.58 63.2515 56.208 63.4691C55.808 63.7043 55.6008 64.5411 55.6008 65.9547C55.6008 67.3683 55.8056 68.2051 56.208 68.4411C56.58 68.6587 57.2032 68.3611 57.7648 68.0499C59.2712 67.2019 59.8784 67.6803 60.204 68.1147C60.2821 68.2286 60.349 68.3497 60.404 68.4763C60.6218 68.9447 60.7348 69.455 60.7352 69.9715V77.8723C60.7351 77.9359 60.7098 77.9969 60.6648 78.0419C60.6198 78.0869 60.5588 78.1122 60.4952 78.1123H52.936C52.426 78.1105 51.9228 78.2289 51.4672 78.4579C51.4131 78.4835 51.3613 78.5135 51.312 78.5475C51.0936 78.7115 50.6384 79.0531 51.3776 80.3678C51.7776 81.0814 52.104 81.8222 51.7648 82.4014C51.4256 82.9806 50.5088 83.247 48.868 83.247Z",
        svg.fill := "#001958"
      ),
      svg.path(
        svg.d := "M47.624 58.6198C47.5481 58.6199 47.4723 58.6159 47.3968 58.6078C47.1535 58.5856 46.9184 58.5082 46.7095 58.3815C46.5006 58.2548 46.3233 58.0821 46.1912 57.8766L46.5912 57.6086C46.6845 57.7558 46.8105 57.8795 46.9594 57.9701C47.1082 58.0607 47.276 58.1157 47.4496 58.131C49.4184 58.3446 53.1776 54.7638 54.4096 53.587C55.62 52.4342 56.636 51.6742 57.816 50.7942C58.6912 50.1398 59.5952 49.463 60.7112 48.5278C61.7432 47.663 64.0088 47.3822 66.8768 47.027C71.5352 46.4494 77.9168 45.659 82.3368 41.771L82.6568 42.1318C78.1168 46.1166 71.656 46.9182 66.936 47.503C64.1464 47.8494 61.9424 48.1222 61.016 48.8958C59.8896 49.839 58.936 50.551 58.1 51.1782C56.936 52.0494 55.9312 52.8014 54.74 53.9342C52.4336 56.1334 49.5672 58.6198 47.624 58.6198Z",
        svg.fill := "#001958"
      ),
      svg.path(
        svg.d := "M46.056 57.9267C44.1104 56.3083 47.5176 51.9923 50.2552 48.5235C50.62 48.0619 50.964 47.6259 51.2816 47.2139C52.5352 45.5883 53.3744 44.0555 54.1152 42.7035C54.9048 41.2635 55.5864 40.0187 56.5048 39.2155C58.296 37.6491 62.6512 33.1939 63.1216 31.7139L63.5792 31.8595C63.0616 33.4851 58.5536 38.0635 56.824 39.5763C55.9696 40.3243 55.3384 41.4763 54.5392 42.9363C53.7904 44.3035 52.9392 45.8531 51.6648 47.5083C51.3448 47.9227 51.0008 48.3595 50.6352 48.8227C48.164 51.9531 44.78 56.2403 46.3664 57.5595L46.056 57.9267Z",
        svg.fill := "#FFDE85"
      ),
      svg.path(
        svg.d := "M46.428 67.5139L45.948 67.4595C45.948 67.4291 46.2912 64.3555 45.6 62.2011C45.5877 62.1634 45.585 62.1233 45.592 62.0843L46.312 58.2355L46.784 58.3235L46.0792 62.1131C46.776 64.3587 46.4424 67.3835 46.428 67.5139ZM50.0392 64.6475C50.0184 64.3275 49.9496 63.4387 49.8912 63.2075C49.8782 63.1538 49.8841 63.0973 49.908 63.0475C49.9304 63.0011 52.1792 58.3507 52.38 55.5211L52.86 55.5547C52.664 58.3123 50.6808 62.5491 50.38 63.1835C50.452 63.5779 50.516 64.5083 50.5232 64.6235L50.0392 64.6475ZM53.416 63.0891C53.416 63.0827 53.416 62.4195 53.3728 61.8459C53.369 61.7944 53.3819 61.7431 53.4096 61.6995C53.4376 61.6555 56.216 57.2651 57.0784 54.0451C57.0908 53.9989 57.1167 53.9574 57.1528 53.9259C57.2664 53.8267 59.96 51.4923 61.732 50.3579L61.9904 50.7627C60.3848 51.7899 57.9208 53.8971 57.5224 54.2403C56.6712 57.3411 54.224 61.3067 53.8568 61.8899C53.8944 62.4587 53.896 63.0619 53.896 63.0899L53.416 63.0891Z",
        svg.fill := "#001958"
      ),
      svg.path(
        svg.d := "M42.1616 65.8291C42.1616 65.8291 43.8184 66.1099 43.9928 69.7931C43.584 70.0527 43.1161 70.2044 42.6328 70.2339C42.2072 68.0627 42.1616 65.8291 42.1616 65.8291ZM46.1896 67.4843C46.1896 67.4843 47.8472 67.7651 48.0208 71.4491C47.612 71.7087 47.1441 71.8604 46.6608 71.8899C46.2352 69.7187 46.1896 67.4843 46.1896 67.4843Z",
        svg.fill := "#FFDE85"
      ),
      svg.path(
        svg.d := "M84.2608 19.414C84.2608 19.414 80.9464 22.154 81.3416 26.1044C81.9104 31.7844 85.964 34.5244 85.964 34.5244C85.964 34.5244 82.4024 34.8348 81.804 38.4092C81.3088 41.3636 84.0504 44.8092 88.0024 43.6388C94.5448 41.6948 100.044 42.738 100.044 42.738L96.776 17.354L84.2608 19.414Z",
        svg.fill := "#FCEFC5"
      ),
      svg.path(
        svg.d := "M86.436 44.1157C85.2557 44.1236 84.1149 43.6915 83.236 42.9037C81.9488 41.7653 81.292 39.9853 81.5632 38.3693C81.8888 36.4277 83.0624 35.4197 83.9896 34.9157C84.4295 34.6784 84.8986 34.4996 85.3848 34.3837C84.7525 33.8491 84.1786 33.2489 83.6728 32.5933C82.2225 30.7241 81.3303 28.483 81.0992 26.1285C80.6952 22.0965 83.9648 19.3437 84.104 19.2285C84.1368 19.2013 84.176 19.1835 84.2184 19.1773L96.7336 17.1141C96.7656 17.1088 96.7976 17.1099 96.8296 17.1175C96.8608 17.1251 96.8904 17.1389 96.916 17.1581C96.9676 17.1967 97.0019 17.2543 97.0112 17.3181L100.282 42.7053C100.286 42.7427 100.282 42.7808 100.27 42.8164C100.257 42.852 100.236 42.8841 100.209 42.9101C100.182 42.9362 100.148 42.9554 100.111 42.9661C100.075 42.9768 100.037 42.9787 99.9992 42.9717C99.9448 42.9613 94.4792 41.9637 88.0704 43.8669C87.5404 44.0281 86.9899 44.1119 86.436 44.1157ZM84.3672 19.6397C83.9672 19.9909 81.228 22.5613 81.5808 26.0805C82.1312 31.5773 86.0608 34.2989 86.0984 34.3261C86.1392 34.3539 86.1704 34.3937 86.188 34.44C86.2056 34.4864 86.208 34.537 86.196 34.5849C86.1832 34.6329 86.1568 34.6758 86.1184 34.7078C86.0808 34.7398 86.0344 34.7593 85.9848 34.7637C85.8472 34.7765 82.6008 35.1061 82.0408 38.4485C81.8008 39.9053 82.392 41.5133 83.5608 42.5437C84.7432 43.5885 86.2968 43.8965 87.9376 43.4085C93.564 41.7373 98.4864 42.2669 99.768 42.4485L96.568 17.6261L84.3672 19.6397Z",
        svg.fill := "#001958"
      ),
      svg.path(
        svg.d := "M86.0592 41.09C87.1304 41.09 87.9992 40.2214 87.9992 39.15C87.9992 38.0785 87.1304 37.21 86.0592 37.21C84.988 37.21 84.1192 38.0785 84.1192 39.15C84.1192 40.2214 84.988 41.09 86.0592 41.09Z",
        svg.fill := "#0049A6"
      ),
      svg.path(
        svg.d := "M86.056 41.3302C85.6248 41.3302 85.2032 41.2024 84.8448 40.9628C84.4864 40.7231 84.2069 40.3827 84.0416 39.9845C83.8768 39.5861 83.8336 39.1478 83.9176 38.725C84.0021 38.3022 84.2097 37.9138 84.5144 37.6087C84.8193 37.3039 85.2076 37.0963 85.6304 37.0121C86.0534 36.928 86.4919 36.9712 86.8904 37.1362C87.2887 37.3013 87.6291 37.5807 87.8688 37.939C88.1081 38.2976 88.2359 38.7191 88.236 39.1502C88.2353 39.7282 88.0054 40.2823 87.5968 40.691C87.188 41.0996 86.6339 41.3295 86.056 41.3302ZM86.056 37.4502C85.72 37.4502 85.3912 37.5499 85.1112 37.7367C84.8319 37.9237 84.6142 38.1892 84.4856 38.4997C84.3569 38.8103 84.3232 39.152 84.3887 39.4818C84.4542 39.8115 84.616 40.1145 84.8536 40.3523C85.0915 40.5899 85.3943 40.7518 85.724 40.8175C86.0544 40.8831 86.396 40.8495 86.7064 40.7208C87.0171 40.5922 87.2827 40.3743 87.4696 40.0947C87.6562 39.8151 87.7558 39.4864 87.756 39.1502C87.7552 38.6996 87.576 38.2676 87.2576 37.9488C86.9387 37.6303 86.5067 37.451 86.056 37.4502Z",
        svg.fill := "#0049A6"
      ),
      svg.path(
        svg.d := "M86.056 39.3901C86.0151 39.3902 85.9748 39.3798 85.9392 39.3597C85.9017 39.339 85.8705 39.3086 85.8489 39.2717C85.8272 39.2349 85.8158 39.1928 85.816 39.1501V36.2437H86.296V38.7189L88.3944 37.4181L88.6472 37.8261L86.1856 39.3541C86.1464 39.3781 86.1016 39.3906 86.056 39.3901Z",
        svg.fill := "#FFDE85"
      ),
      svg.path(
        svg.d := "M36.7296 43.098H26.5696C26.5304 43.0981 26.4918 43.0885 26.4571 43.0702C26.4224 43.0519 26.3928 43.0253 26.3708 42.9928C26.3488 42.9604 26.3351 42.923 26.3309 42.8841C26.3266 42.8451 26.3319 42.8057 26.3464 42.7692L31.4256 30.0132C31.4389 29.9799 31.4595 29.95 31.4859 29.9257C31.5122 29.9014 31.5437 29.8834 31.578 29.8728C31.6123 29.8623 31.6485 29.8596 31.6839 29.865C31.7194 29.8703 31.7532 29.8835 31.7829 29.9036C31.8226 29.9305 31.8534 29.9687 31.8712 30.0132L36.952 42.7692C36.9664 42.8056 36.9718 42.845 36.9676 42.8839C36.9634 42.9228 36.9497 42.9601 36.9278 42.9925C36.9059 43.0249 36.8764 43.0515 36.8418 43.0699C36.8073 43.0883 36.7688 43.098 36.7296 43.098ZM26.9248 42.618H36.376L31.6504 30.7508L26.9248 42.618Z",
        svg.fill := "#FAA250"
      ),
      svg.path(
        svg.d := "M50.2792 64.634C50.3424 65.9292 50.3512 67.098 50.1072 68.3236C50.4164 68.2493 50.7168 68.142 51.0032 68.0036C50.9768 65.69 50.5984 64.8916 50.2792 64.634ZM53.656 63.0892C53.601 63.7522 53.4973 64.4102 53.3456 65.058C53.5181 65.0162 53.6872 64.9611 53.8512 64.8932C53.8848 63.8972 53.816 63.2588 53.656 63.0892ZM45.5272 56.9676C45.4728 56.7797 45.4617 56.582 45.4949 56.3892C45.528 56.1964 45.6044 56.0137 45.7184 55.8548C46.5456 54.6964 48.8216 52.1548 50.3144 53.6692C51.772 55.1492 49.392 57.4988 48.4384 58.3356C48.3122 58.4473 48.1634 58.5306 48.0022 58.5797C47.841 58.6289 47.6711 58.6427 47.504 58.6204C46.8696 58.5332 45.8864 58.194 45.5272 56.9676Z",
        svg.fill := "#FFDE85"
      ),
      svg.defs(
        svg.linearGradient(
          svg.idAttr := "paint0_linear_15_14914",
          svg.x1 := "97.1504",
          svg.y1 := "17.5122",
          svg.x2 := "24.5112",
          svg.y2 := "95.7616",
          svg.gradientUnits := "userSpaceOnUse",
          svg.stop(svg.stopColor := "#FFDE85"),
          svg.stop(svg.offsetAttr := "0.67", svg.stopColor := "#CCE0FE"),
          svg.stop(svg.offsetAttr := "1", svg.stopColor := "#B8D4FD")
        )
      )
    )

  }

}
