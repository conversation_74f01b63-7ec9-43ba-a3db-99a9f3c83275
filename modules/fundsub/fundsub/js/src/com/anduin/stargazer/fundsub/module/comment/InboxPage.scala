// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment

import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.vdom.html_<^.*
import anduin.id.ModelIdRegistry
import anduin.id.entity.EntityId
import anduin.id.issuetracker.IssueId
import anduin.stargazer.service.formcomment.FormCommentCommons.NewInboxTab
import com.anduin.stargazer.fundsub.module.comment.react.inbox.CommentInboxModalBtn
import stargazer.model.routing.DynamicAuthPage.{FundSubAdminPage, FundSubCommentInboxPage}
import stargazer.model.routing.Page

final case class InboxPage(
  page: FundSubCommentInboxPage,
  router: RouterCtl[Page]
) {

  val tab: NewInboxTab = NewInboxTab.fromUrlParams(
    page.params.getOrElse(FundSubCommentInboxPage.tab, ""),
    page.fundSubId
  )

  val threadIdOpt: Option[IssueId] =
    ModelIdRegistry.parser
      .parseAs[IssueId](page.params.getOrElse(FundSubCommentInboxPage.threadIdKey, ""))

  def apply(): VdomElement = InboxPage.component(this)
}

object InboxPage {
  private type Props = InboxPage

  private def render(props: Props) = {
    CommentInboxModalBtn(
      fundId = props.page.fundSubId,
      initialSelectedTabOpt = Option(props.tab),
      initialSelectedThreadOpt = props.threadIdOpt,
      defaultIsOpened = true,
      renderTarget = _ => EmptyVdom,
      onClose = props.router.set(
        FundSubAdminPage(
          EntityId.defaultValue.get,
          props.page.fundSubId
        )
      )
    )()
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
