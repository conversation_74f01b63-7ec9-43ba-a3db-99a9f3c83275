// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.review.supportingdoc

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.modal.Modal
import design.anduin.components.progress.Skeleton
import design.anduin.components.progress.react.SkeletonR
import design.anduin.components.switcher.react.SwitcherR
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.util.ComponentUtils
import design.anduin.components.well.Well
import design.anduin.components.well.react.WellR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import fundsub.review.supportingdoc.SupportingDocReviewConfigMode.{
  SUPPORTING_DOC_REVIEW_CONFIG_MODE_DISABLED,
  SUPPORTING_DOC_REVIEW_CONFIG_MODE_ENABLED_WITH_SINGLE_CONFIG
}
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.endpoint.review.{FundSubSupportingDocReviewConfig, GetFundSubSupportingDocReviewConfigParams}
import anduin.fundsub.model.FundSubSharedClientModel.ParticipantInfo
import anduin.id.fundsub.FundSubId
import anduin.model.common.user.UserId
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubSupportingDocReviewEndpointClient
import com.anduin.stargazer.fundsub.module.investor.settings.SettingSwitchTemplate
import com.anduin.stargazer.fundsub.module.investor.settings.review.model.AdminAndInvestorGroupInfo

private[settings] final case class ManageSupportingDocReviewSetting(
  fundId: FundSubId,
  adminAndInvestorGroupInfo: AdminAndInvestorGroupInfo
) {
  def apply(): VdomElement = ManageSupportingDocReviewSetting.component(this)
}

private[settings] object ManageSupportingDocReviewSetting {
  private type Props = ManageSupportingDocReviewSetting

  private final case class State(
    initialConfig: FundSubSupportingDocReviewConfig = FundSubSupportingDocReviewConfig(
      mode = SUPPORTING_DOC_REVIEW_CONFIG_MODE_DISABLED
    ),
    showEditModal: Boolean = false,
    isGuideMessageSeen: Boolean = false,
    isFetching: Boolean = true,
    reviewerUserInfos: Map[UserId, ParticipantInfo] = Map.empty
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    private def renderEditModal(
      props: Props,
      state: State,
      adminGroups: AdminAndInvestorGroupInfo
    ) = {
      val preSetMode =
        if (state.initialConfig.mode.isSupportingDocReviewConfigModeDisabled || adminGroups.investorGroups.size <= 1) {
          SUPPORTING_DOC_REVIEW_CONFIG_MODE_ENABLED_WITH_SINGLE_CONFIG
        } else {
          state.initialConfig.mode
        }

      val title = if (state.initialConfig.mode.isSupportingDocReviewConfigModeDisabled) {
        "Create review workflow for AML/KYC and other documents"
      } else {
        "Edit review workflow for AML/KYC and other documents"
      }

      Modal(
        renderContent = onClose =>
          SupportingDocGroupReviewSettingModal(
            fundId = props.fundId,
            initialConfig = state.initialConfig.copy(mode = preSetMode),
            adminAndInvestorGroupInfo = adminGroups,
            reviewerUserInfos = state.reviewerUserInfos,
            isCreating = state.initialConfig.mode.isSupportingDocReviewConfigModeDisabled,
            onClose = scope.modState(
              _.copy(
                showEditModal = false
              ),
              onClose
            ),
            onSave = scope.modState(
              currentState =>
                currentState.copy(
                  showEditModal = false,
                  isGuideMessageSeen = if (currentState.initialConfig.mode.isSupportingDocReviewConfigModeDisabled) {
                    false
                  } else {
                    currentState.isGuideMessageSeen
                  }
                ),
              fetchConfig
            )
          )(),
        isOpened = Option(state.showEditModal),
        afterUserClose = scope.modState(_.copy(showEditModal = false)),
        title = title,
        size = Modal.Size(Modal.Width.Px960),
        testId = "AMLReviewSetting"
      )()
    }

    private def renderDisableConfirmationModal(props: Props) = {
      Modal(
        testId = "ConfirmDisableAMLReview",
        title = "Disable AML/KYC document review",
        renderTarget = onClick =>
          SwitcherR(
            testId = "AMLReview",
            isChecked = true,
            onChange = _ => onClick
          )(),
        renderContent = onClose =>
          DisableReviewFlowConfirmationModal(
            props.fundId,
            onClose,
            onDisable = fetchConfig
          )()
      )()
    }

    private val settingPlaceholder = <.div(
      <.div(
        tw.flex,
        <.div(
          tw.block,
          SkeletonR(
            effect = Skeleton.Effect.Wave,
            height = "16px",
            width = "40px",
            shape = Skeleton.Shape.Rounded
          )()
        ),
        <.div(
          tw.ml16.flexFill,
          <.div(
            tw.wPc100,
            SkeletonR(
              effect = Skeleton.Effect.Wave,
              height = "28px",
              width = "100%",
              shape = Skeleton.Shape.Rounded
            )()
          ),
          <.div(
            tw.mt2.wPc100,
            SkeletonR(
              effect = Skeleton.Effect.Wave,
              height = "20px",
              width = "100%",
              shape = Skeleton.Shape.Rounded
            )()
          )
        )
      )
    )

    def render(props: Props, state: State): VdomElement = {
      React.Fragment(
        if (state.isFetching) {
          settingPlaceholder
        } else {
          <.div(
            ComponentUtils.testId(ManageSupportingDocReviewSetting),
            SettingSwitchTemplate(
              label = "Enable AML/KYC and other documents review",
              description = () =>
                Option(
                  React.Fragment(
                    <.div(
                      tw.mr4.mb8,
                      "Assign reviewers to approve investors' AML/KYC, tax forms, and other requested documents."
                    ),
                    Option.when(
                      !state.isGuideMessageSeen && !state.initialConfig.mode.isSupportingDocReviewConfigModeDisabled &&
                        state.initialConfig.supportingDocGroups.isEmpty
                    ) {
                      renderUpdateSupportingDocGroupGuideWell
                    }
                  )
                ),
              renderSwitch = () => {
                val isEnabled = !state.initialConfig.mode.isSupportingDocReviewConfigModeDisabled
                if (isEnabled) {
                  renderDisableConfirmationModal(props)
                } else {
                  SwitcherR(
                    isChecked = isEnabled,
                    isDisabled = props.adminAndInvestorGroupInfo.isLoading,
                    onChange = _ =>
                      scope.modState(
                        _.copy(
                          showEditModal = true
                        )
                      )
                  )()
                }
              },
              renderContent = () =>
                Option(
                  if (props.adminAndInvestorGroupInfo.isLoading) {
                    SkeletonR(
                      effect = Skeleton.Effect.Wave,
                      height = "110px",
                      width = "100%",
                      shape = Skeleton.Shape.Rounded
                    )()
                  } else {
                    SupportingDocConfigSummaryCardView(
                      state.initialConfig,
                      props.adminAndInvestorGroupInfo,
                      onEdit = scope.modState(
                        _.copy(
                          showEditModal = true
                        )
                      ),
                      state.reviewerUserInfos
                    )()

                  }
                )
            )(),
            renderEditModal(
              props,
              state,
              props.adminAndInvestorGroupInfo
            )
          )
        }
      )
    }

    private def renderUpdateSupportingDocGroupGuideWell = {
      WellR(
        style = Well.Style.Primary(),
        renderButton = button => {
          TooltipL(
            renderContent = _.amend("Don't show this again"),
            renderTarget = button
          )()
        },
        onClose = markGuideMessageAsSeen
      )(
        ButtonL(
          testId = "WellEditButton",
          style = ButtonL.Style.Text(),
          onClick = Observer { _ =>
            scope
              .modState(_.copy(showEditModal = true))
              .runNow()
          }
        )("Edit the review workflow"),
        span(" to add, edit, and delete document groups")
      )
    }

    private def markGuideMessageAsSeen: Callback = {
      for {
        props <- scope.props
        _ <- ZIOUtils.toReactCallback {
          FundSubSupportingDocReviewEndpointClient
            .markUpdateSupportingDocGroupGuideMessageAsSeen(props.fundId)
            .unit
            .orElseSucceed(())
            .map(_ => scope.modState(_.copy(isGuideMessageSeen = true)))
        }
      } yield ()
    }

    def fetchConfig: Callback =
      for {
        props <- scope.props
        _ <- scope.modState(
          _.copy(isFetching = true),
          ZIOUtils.toReactCallback {
            FundSubSupportingDocReviewEndpointClient
              .getFundSubSupportingDocReviewConfig(
                GetFundSubSupportingDocReviewConfigParams(fundSubId = props.fundId, shouldFetchUserInfo = true)
              )
              .map(
                _.fold(
                  _ =>
                    scope.modState(
                      _.copy(isFetching = false),
                      Toast.errorCallback(s"Failed to load aml/kyc review config")
                    ),
                  resp =>
                    scope.modState(
                      _.copy(
                        initialConfig = resp.config,
                        reviewerUserInfos = resp.reviewerUserInfos,
                        isFetching = false
                      )
                    )
                )
              )
          }
        )
      } yield ()

    def fetchInitialData: Callback = {
      for {
        props <- scope.props
        _ <- fetchConfig
        _ <- ZIOUtils.toReactCallback {
          FundSubSupportingDocReviewEndpointClient
            .verifyUpdateSupportingDocGroupGuideMessageSeen(props.fundId)
            .map(
              _.fold(
                _ => Callback(scribe.info("Failed to verify if updating doc group guide message is seen")),
                res => scope.modState(_.copy(isGuideMessageSeen = res))
              )
            )
        }
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .componentDidMount(_.backend.fetchInitialData)
    .build

}
