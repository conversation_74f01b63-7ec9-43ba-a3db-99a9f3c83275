// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.common.activitylog.render

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.laminar.PortalWrapperL
import design.anduin.style.tw.*

import anduin.utils.StringUtils

private[render] final case class CommentSentLpActivityRenderer(
  actorName: String,
  recipientNames: Seq[String]
) {

  def apply(): HtmlElement = {
    span(
      span(
        tw.fontSemiBold,
        actorName.capitalize
      ),
      " sent a comment notification to ",
      renderRecipientPart
    )
  }

  private def renderRecipientPart: Node =
    if (recipientNames.size == 1) {
      span(recipientNames.head)
    } else if (recipientNames.size == 2) {
      span(s"${recipientNames.head} and ${recipientNames.last}")
    } else if (recipientNames.size > 2) {
      span(
        s"${recipientNames.head} and ",
        PopoverL(
          renderTarget = (open, _) =>
            ButtonL(
              style = ButtonL.Style.Text(),
              onClick = open.contramap(_ => ())
            )(StringUtils.pluralItem(recipientNames.size - 1, "other")),
          renderContent = _ =>
            div(
              tw.py4.px8,
              recipientNames.drop(1).map(div(_))
            ),
          targetWrapper = PortalWrapperL.Inline
        )()
      )
    } else {
      emptyNode
    }

}
