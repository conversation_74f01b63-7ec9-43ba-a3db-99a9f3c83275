// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.inbox

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.style.tw.*
import org.scalajs.dom

import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.stargazer.service.formcomment.FormCommentCommons.NewInboxTab
import anduin.user.CurrentUserInfoProvider
import com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent.FetchLpPermissionL
import com.anduin.stargazer.fundsub.module.comment.models.InboxLocationParams

private[fundsub] case class LpInboxEntryPointL(
  fundSubId: FundSubId,
  lpId: FundSubLpId,
  onClose: Observer[Unit]
) {

  private def renderContent(closeModal: Observer[Unit]) = {
    FetchLpPermissionL(
      lpId = lpId,
      renderChildren = renderProps => {
        div(
          tw.wPc100.hPc100,
          child <-- renderProps.permissionSignal.map {
            case FetchLpPermissionL.Permission.Checking =>
              BlockIndicatorL(isFullHeight = true)().amend(tw.wPc100)

            case FetchLpPermissionL.Permission.NoPermission =>
              div(
                tw.wPc100.hPc100,
                child.maybe <-- CurrentUserInfoProvider.getCurrentUserInfoSignal.map(_.map { case (_, userInfo) =>
                  div(
                    tw.hVh100.wPc100,
                    NonIdealStateL(
                      icon = {
                        div(
                          tw.textPrimary4.bgPrimary1.p12.roundedFull,
                          IconL(name = Val(Icon.Glyph.Search), size = Icon.Size.Px32)()
                        )
                      },
                      title = "This comment thread is not available",
                      description = div(
                        "You are signed in as ",
                        if (userInfo.getDisplayName != userInfo.emailAddressStr) {
                          s"${userInfo.getDisplayName} (${userInfo.emailAddressStr})"
                        } else {
                          userInfo.emailAddressStr
                        },
                        "."
                      ),
                      action = {
                        ButtonL(
                          style = ButtonL.Style.Full(icon = Option(Icon.Glyph.Comment)),
                          onClick = Observer[dom.MouseEvent] { _ =>
                            renderProps.setPermission.onNext(FetchLpPermissionL.Permission.HasPermission)
                          }
                        )("View all comments")
                      }
                    )()
                  )
                })
              )

            case FetchLpPermissionL.Permission.HasPermission =>
              InboxEntryPointWrapperL(
                presetLocationOptSignal = Val(Option(InboxLocationParams(tab = NewInboxTab.IndividualInvestorTab(lpId)))),
                fundSubId = fundSubId,
                renderChildren = renderProps => {
                  val onCloseInbox = Observer[Unit] { _ =>
                    closeModal.onNext(())
                    onClose.onNext(())
                  }
                  renderProps.renderInboxModal(onCloseInbox)
                }
              )().amend(tw.hPc100.wPc100)
          }
        )
      }
    )()
  }

  def apply(): HtmlElement = {
    div(
      ModalL(
        defaultIsOpened = true,
        size = ModalL.Size(height = ModalL.Height.Full, width = ModalL.Width.Full),
        renderContent = closeModal => {
          renderContent(closeModal)
        },
        isClosable = None
      )()
    )
  }

}
