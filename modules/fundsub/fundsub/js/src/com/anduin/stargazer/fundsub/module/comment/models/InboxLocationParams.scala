// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.models

import anduin.id.issuetracker.IssueId
import anduin.protobuf.fundsub.comment.TopicData
import anduin.stargazer.service.formcomment.FormCommentCommons.NewInboxTab

final case class InboxLocationParams(
  tab: NewInboxTab = NewInboxTab.AllCommentsTab,
  page: Int = 1,
  selectedThreadIdOpt: Option[IssueId] = None,
  selectedTopicDataOpt: Option[TopicData] = None,
  showResolvedTab: Boolean = false
) derives CanEqual
