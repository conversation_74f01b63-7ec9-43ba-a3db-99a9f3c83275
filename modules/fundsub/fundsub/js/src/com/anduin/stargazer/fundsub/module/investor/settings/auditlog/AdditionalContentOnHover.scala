// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.auditlog

import design.anduin.components.portal.PortalWrapper
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import org.scalajs.dom.HTMLElement

import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

final case class AdditionalContentOnHover(
  renderTarget: TagMod,
  renderContent: () => VdomNode,
  targetWrapper: PortalWrapper = PortalWrapper.Block
) {
  def apply(): VdomElement = AdditionalContentOnHover.component(this)
}

object AdditionalContentOnHover {

  private type Props = AdditionalContentOnHover

  private case class State(isOpen: Boolean = false)

  private class Backend(scope: BackendScope[Props, State]) {

    private val targetRef: Ref.Simple[HTMLElement] = Ref[HTMLElement]

    private def renderContent(props: Props, state: State): VdomNode = {
      if (state.isOpen) {
        props.renderContent()
      } else {
        EmptyVdom
      }
    }

    private def renderTarget(props: Props): VdomElement = {
      props.targetWrapper.tag.withRef(targetRef)(
        // Mouse navigation
        ^.onMouseEnter --> scope.modState(_.copy(isOpen = true)),
        ^.onMouseLeave --> scope.modState(_.copy(isOpen = false)),
        // Keyboard navigation
        ^.onFocusCapture --> scope.modState(_.copy(isOpen = true)),
        ^.onBlurCapture --> scope.modState(_.copy(isOpen = false)),
        // Users' content
        props.renderTarget
      )
    }

    def render(props: Props, state: State): VdomElement = {
      <.div(
        tw.flex.flexRow.itemsCenter,
        renderTarget(props),
        renderContent(props, state)
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
