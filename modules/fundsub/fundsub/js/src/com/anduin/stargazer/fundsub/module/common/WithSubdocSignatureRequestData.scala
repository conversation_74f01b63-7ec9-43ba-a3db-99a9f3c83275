// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.common

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration

import io.circe.Codec
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.graphql.component.{GraphqlOptions, QueryComponent, QueryProps}
import anduin.id.fundsub.FundSubLpId
import anduin.rohan.operation.AnduinQuery
import com.anduin.stargazer.fundsub.module.common.WithSubdocSignatureRequestData.Variables
import com.anduin.stargazer.fundsub.module.model.FundSubClientModel.FundSubSubdocSignatureRequestData

private[fundsub] final case class WithSubdocSignatureRequestData(
  fundSubLpId: FundSubLpId,
  render: WithSubdocSignatureRequestData.Renderer
) {

  def apply(): VdomElement =
    WithSubdocSignatureRequestData.graphqlComponent(
      WithSubdocSignatureRequestData.OuterProps(
        fundSubLpId,
        render
      ),
      Variables(fundSubLpId)
    )

}

private[fundsub] object WithSubdocSignatureRequestData {
  type Renderer = (Option[FundSubSubdocSignatureRequestData], Callback) => VdomNode
  final case class Variables(fundSubLpId: FundSubLpId) derives CanEqual

  object Variables {
    given Codec.AsObject[Variables] = deriveCodecWithDefaults
  }

  final case class ChildProps(fundSubSubdocSignatureRequestData: Option[FundSubSubdocSignatureRequestData])
      derives CanEqual

  object ChildProps {
    given Codec.AsObject[ChildProps] = deriveCodecWithDefaults
  }

  private case class OuterProps(
    fundSubLpId: FundSubLpId,
    render: Renderer
  ) derives CanEqual

  private type Props = QueryProps[OuterProps, Variables, ChildProps]

  private case class Backend(scope: BackendScope[Props, Unit]) {

    def render(props: Props): VdomNode = {
      props.outerProps
        .render(props.data.fundSubSubdocSignatureRequestData, props.refetch)
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .renderBackend[Backend]
    .build

  private val graphqlComponent = QueryComponent(
    component,
    AnduinQuery.FundSubSubdocSignatureRequestData,
    GraphqlOptions(FiniteDuration(1, TimeUnit.MINUTES))
  )

}
