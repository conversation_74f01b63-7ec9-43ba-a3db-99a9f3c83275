// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.share

import com.raquo.airstream.core.Signal
import com.raquo.laminar.api.L.*
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.text.laminar.TruncateL
import design.anduin.components.tooltip.laminar.SingleTooltipL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*

import anduin.protobuf.fundsub.comment.TopicData

private[laminar] case class CommentAnchorPointDescriptionL(
  topicDataSignal: Signal[TopicData],
  isHiddenSignal: Signal[Boolean] = Val(false),
  onJumpToTarget: Observer[String],
  moveToAnchorPointActionType: CommentAnchorPointDescriptionL.MoveToAnchorPointType,
  disableOnClickSignal: Signal[Boolean] = Val(false)
) {

  private val shouldHandleOnClickEvent = !isHiddenSignal && !disableOnClickSignal

  private def renderAmlKycName = {
    val doctypeSignal =
      topicDataSignal.map(_.asMessage.sealedValue.amlKycDocData.map(_.doctype).getOrElse("Untitled document"))
    val tooltipContentSignal = isHiddenSignal.combineWith(disableOnClickSignal).map { case (isHidden, disableOnClick) =>
      if (isHidden) {
        "The document is not available"
      } else if (disableOnClick) {
        ""
      } else if (
        moveToAnchorPointActionType == CommentAnchorPointDescriptionL.MoveToAnchorPointType.JumpToAnchorPoint
      ) {
        "Jump to document"
      } else {
        "View thread on investor's document"
      }
    }
    SingleTooltipL(
      position = PortalPosition.TopCenter,
      renderContent = _.amend(
        text <-- tooltipContentSignal
      ),
      renderTarget = {
        div(
          tw.borderAll.rounded8.px12.py8.borderGray3,
          tw.flex.itemsCenter.spaceX12,
          shouldHandleOnClickEvent.cls(tw.cursorPointer.hover(tw.bgPrimary1.bgOpacity40.borderPrimary4)),
          isHiddenSignal.cls(tw.borderGray2.textGray5),
          TruncateL(
            target = div(
              ComponentUtils.testIdL(CommentAnchorPointDescriptionL, "DocumentType"),
              tw.flexFill.textSmall,
              text <-- doctypeSignal
            ),
            lineClamp = Some(2)
          )(),
          renderHiddenIcon,
          onClick
            .compose(
              _.map(_.stopPropagation())
                .filterWith(shouldHandleOnClickEvent)
                .sample(doctypeSignal)
            ) --> onJumpToTarget
        )
      },
      isDisabled = tooltipContentSignal.map(_.isEmpty)
    )()
  }

  private def renderHiddenIcon =
    child <-- isHiddenSignal.splitBoolean(
      whenTrue = _ => IconL(name = Val(Icon.Glyph.EyeOff))(),
      whenFalse = _ => emptyNode
    )

  private def renderFormQuestionLabel = {
    val formQuestionDataSignal = topicDataSignal.map { topicData =>
      topicData.asMessage.sealedValue.formQuestionData
    }
    val tooltipContentSignal = isHiddenSignal.combineWith(disableOnClickSignal).map { case (isHidden, disableOnClick) =>
      if (isHidden) {
        "Form question not applicable"
      } else if (disableOnClick) {
        ""
      } else if (
        moveToAnchorPointActionType == CommentAnchorPointDescriptionL.MoveToAnchorPointType.JumpToAnchorPoint
      ) {
        "Jump to form field"
      } else {
        "View thread on investor's form"
      }
    }
    SingleTooltipL(
      position = PortalPosition.TopCenter,
      renderContent = _.amend(
        text <-- tooltipContentSignal
      ),
      renderTarget = {
        val tocSectionSignal = formQuestionDataSignal.map(_.map(_.tocSection).getOrElse(""))
        val isTocSectionNonEmptySignal = tocSectionSignal.map(_.nonEmpty)
        val fieldDescriptionSignal = formQuestionDataSignal.map(_.map(_.fieldDescription).getOrElse(""))
        val fieldDescriptionNonEmptySignal = fieldDescriptionSignal.map(_.nonEmpty)
        div(
          tw.borderAll.rounded8.borderGray3.px12.py8,
          tw.flex.itemsCenter.spaceX12,
          isHiddenSignal.cls(tw.borderGray2.textGray5),
          shouldHandleOnClickEvent.cls(tw.cursorPointer.hover(tw.bgPrimary1.bgOpacity40.borderPrimary4)),
          div(
            child <-- isTocSectionNonEmptySignal.splitBoolean(
              whenTrue = _ =>
                TruncateL(
                  lineClamp = Some(1),
                  div(
                    tw.fontMedium.textSmall,
                    ComponentUtils.testIdL(CommentAnchorPointDescriptionL, "ToCSection"),
                    text <-- tocSectionSignal
                  )
                )(),
              whenFalse = _ => emptyNode
            ),
            child <-- fieldDescriptionNonEmptySignal.splitBoolean(
              whenTrue = _ =>
                TruncateL(
                  lineClamp = Some(1),
                  target = div(
                    tw.textSmall,
                    ComponentUtils.testIdL(CommentAnchorPointDescriptionL, "FieldDescription"),
                    text <-- fieldDescriptionSignal
                  )
                )(),
              whenFalse = _ => emptyNode
            )
          ),
          renderHiddenIcon,
          onClick
            .compose(_.withCurrentValueOf(shouldHandleOnClickEvent, formQuestionDataSignal).map {
              case (clickEvent, shouldShowClickableUi, formQuestionOpt) =>
                val alias = formQuestionOpt.map(_.fieldAlias).getOrElse("")
                if (shouldShowClickableUi) {
                  clickEvent.stopPropagation()
                  onJumpToTarget.onNext(alias)
                }
            }) --> Observer.empty
        )
      },
      isDisabled = tooltipContentSignal.map(_.isEmpty)
    )()
  }

  def apply(): HtmlElement = {
    div(
      child <-- topicDataSignal
        .map { topicData =>
          topicData.asMessage.sealedValue.isAmlKycDocData
        }
        .splitBoolean(
          whenTrue = _ => renderAmlKycName,
          whenFalse = _ => renderFormQuestionLabel
        )
    )
  }

}

private[laminar] object CommentAnchorPointDescriptionL {

  enum MoveToAnchorPointType {
    case JumpToAnchorPoint, OpenInNewTab
  }

}
