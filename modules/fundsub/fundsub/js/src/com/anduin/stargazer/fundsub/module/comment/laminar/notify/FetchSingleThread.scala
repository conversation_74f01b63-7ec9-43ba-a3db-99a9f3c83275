// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.notify

import com.raquo.laminar.api.L.*

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.formcomment.GetCommentThreadParams
import anduin.stargazer.service.formcomment.FormCommentCommons.CommentThread
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient

private[notify] case class FetchSingleThread(
  parentThread: CommentThread,
  renderChildren: FetchSingleThread.FetchData => HtmlElement
) {

  private val fetchCommentsEventBus = new EventBus[Option[CommentThread]]

  private val threadVar = Var(
    FetchSingleThread.FetchData(
      isFetching = true,
      thread = None
    )
  )

  private def fetchComments(thread: Option[CommentThread]) = {
    thread
      .map { thread =>
        AirStreamUtils.taskToStreamDEPRECATED {
          FundSubEndpointClient
            .getCommentThread(
              GetCommentThreadParams(
                thread.fundSubLpId,
                Option(thread.id)
              )
            )
            .map {
              _.fold(
                _ => {
                  FetchSingleThread.FetchData(
                    isFetching = false,
                    thread = None
                  )
                },
                response => {
                  FetchSingleThread.FetchData(
                    isFetching = false,
                    thread = response.commentDataOpt
                  )
                }
              )
            }
        }
      }
      .getOrElse(EventStream.empty)
  }

  def apply(): HtmlElement = {
    div(
      fetchCommentsEventBus.events.flatMapSwitch(fetchComments) --> threadVar.writer,
      child <-- threadVar.signal.map(renderChildren),
      onMountCallback { _ =>
        fetchCommentsEventBus.emit(Option(parentThread))
      }
    )
  }

}

private[notify] object FetchSingleThread {

  case class FetchData(
    isFetching: Boolean,
    thread: Option[CommentThread]
  )

}
