// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.contact.modal

import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.constants.Terms
import anduin.scalajs.pluralize.Pluralize

object ExplanationModal {

  def apply(): VdomElement = ExplanationModal.component()

  private def renderSection(icon: Icon.Name, label: String, text: String): VdomNode = {
    <.div(
      tw.px20.py24.flex.flexCol.itemsCenter,
      <.div(
        ^.width := "72px",
        ^.height := "72px",
        tw.roundedFull.bgPrimary1.textPrimary4.flex,
        <.div(tw.mAuto, IconR(icon, size = Icon.Size.Px32)())
      ),
      <.div(tw.mt16.mb8.fontSemiBold.text15, label),
      <.div(tw.textCenter.textGray7, text)
    )
  }

  private val component = ScalaComponent
    .builder[Unit](this.getClass.getSimpleName)
    .stateless
    .render_ {
      <.div(
        tw.pt16.px20.pb24.flex,
        <.div(
          tw.flexFill,
          renderSection(
            Icon.Glyph.UserSingle,
            "Contact",
            s"Contacts can belong to multiple ${Pluralize(
                Terms.InvesteeEntityLabelLowerCase,
                count = 2
              )} or be invited as individual investors."
          )
        ),
        <.div(
          tw.flexFill,
          renderSection(
            Icon.Glyph.UserGroupLarge,
            Terms.InvesteeEntityLabel,
            s"An ${Terms.InvesteeEntityLabelLowerCase} is a group of one or more contacts who invest collectively as an entity."
          )
        )
      )
    }
    .build

}
