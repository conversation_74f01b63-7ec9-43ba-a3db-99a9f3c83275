// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.homepage

import design.anduin.components.portal.PortalWrapper
import design.anduin.components.tag.Tag
import design.anduin.components.tag.react.TagR
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.entity.logo.EntityLogo
import anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus
import com.anduin.stargazer.fundsub.module.status.LpStatusHomepageTag

private[homepage] final case class FundSubCard(
  fundName: String,
  fundDescriptionOpt: Option[String] = None,
  lpStatusOpt: Option[LpStatus] = None,
  logoUrl: Option[String],
  logoBgColor: Option[String],
  onClick: Callback,
  isFundClosed: Boolean,
  clickableAreaOpt: Option[FundSubCard.ClickableArea] = Some(FundSubCard.ClickableArea.Full)
) {
  def apply(childRen: VdomNode*): VdomElement = FundSubCard.component(this)(childRen*)
}

private[homepage] object FundSubCard {

  sealed trait ClickableArea derives CanEqual

  object ClickableArea {
    case object Full extends ClickableArea
    case class TopRight(renderTarget: Callback => VdomElement) extends ClickableArea // onClick => VdomElement
  }

  private type Props = FundSubCard

  private def renderFundStatus(lpStatus: LpStatus) = {
    LpStatusHomepageTag(lpStatus)()
  }

  private def renderFundName(fundName: String) = {
    TooltipR(
      renderContent = _(fundName),
      targetWrapper = PortalWrapper.BlockContent,
      renderTarget = <.div(
        tw.text15.leading24.fontSemiBold,
        ^.className := "fund-card-name",
        ^.maxWidth := "600px",
        fundName
      )()
    )()
  }

  private def renderFundDescription(fundDescriptionOpt: Option[String]) = {
    fundDescriptionOpt.map { fundDescription =>
      <.div(
        tw.text13.leading20.textGray7.fontMono,
        fundDescription
      )
    }
  }

  private def render(props: Props, children: PropsChildren): VdomElement = {
    <.div(
      ComponentUtils.testId(FundSubCard, "FundSubCard"),
      tw.rounded8.bgGray0.borderAll.borderGray3,
      tw.px20.pt20.pb16.mb16,
      TagMod.unless(props.clickableAreaOpt.isEmpty) {
        TagMod(
          tw.hover(tw.borderGray4.bgGray1),
          TagMod.when(props.clickableAreaOpt.contains(ClickableArea.Full)) {
            TagMod(tw.cursorPointer, ^.onClick --> props.onClick)
          }
        )
      },
      <.div(
        tw.flex.justifyBetween,
        EntityLogo(
          entityId = None,
          logoUrl = props.logoUrl,
          longLogoUrl = None,
          size = EntityLogo.LogoSize.Dynamic(58),
          name = Some(props.fundName),
          logoBgColor = props.logoBgColor.orElse(Some("#fff"))
        )(),
        props.clickableAreaOpt.map {
          case ClickableArea.TopRight(renderTarget) => renderTarget(props.onClick)
          case _                                    => EmptyVdom
        }
      ),
      <.div(
        ComponentUtils.testId(FundSubCard, "Name"),
        tw.mt16,
        renderFundName(props.fundName),
        renderFundDescription(props.fundDescriptionOpt),
        if (props.isFundClosed) {
          <.div(tw.mt4, TagR(label = "Closed", color = Tag.Bold.Gray)())
        } else {
          props.lpStatusOpt.whenDefined(
            using { lpStatus =>
              <.div(
                tw.mt4,
                renderFundStatus(lpStatus)
              )
            }
          )
        }
      ),
      <.div(
        tw.mt16,
        children
      )
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .render_PC(render)
    .build

}
