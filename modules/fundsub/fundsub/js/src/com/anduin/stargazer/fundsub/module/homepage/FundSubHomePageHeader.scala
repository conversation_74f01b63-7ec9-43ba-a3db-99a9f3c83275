// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.homepage

import design.anduin.components.drawer.Drawer
import design.anduin.components.drawer.react.DrawerR
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.actionlogger.ActionEventLoggerJs
import anduin.appnavigation.CrossAppNavigator
import anduin.fundsub.homepage.FundSubEnvironmentNavigationData
import anduin.user.HeaderUser
import anduin.whitelabel.dashboard.DashboardWhitelabelValue
import stargazer.component.routing.react.WithReactRouterR

private[homepage] final case class FundSubHomePageHeader(
  currentEnvironmentOpt: Option[FundSubEnvironmentNavigationData],
  renderCenteredContent: () => VdomNode = () => EmptyVdom
) {

  private val currentEnvWhitelabelValues =
    currentEnvironmentOpt.map(_.environmentWhitelabelData.customValues).getOrElse(Map.empty)

  private val currentEnvLongLogoHeight =
    DashboardWhitelabelValue.LongLogoHeight.extractThis(currentEnvWhitelabelValues)

  private val longLogoMaxHeight = s"${DashboardWhitelabelValue.LongLogoMaxHeight}px"

  private val currentEnvColorOnBanner =
    DashboardWhitelabelValue.TextOnBannerColor.extractThis(currentEnvWhitelabelValues)

  def apply(): VdomElement = FundSubHomePageHeader.component(this)
}

private[homepage] object FundSubHomePageHeader {

  private type Props = FundSubHomePageHeader

  private def renderCurrentEnvironment(props: Props, isOpenedOpt: Option[Boolean], open: Callback) = {
    val canBeOpened = isOpenedOpt.isDefined
    val isOpened = isOpenedOpt.contains(true)
    <.div(
      tw.flex.itemsCenter.hover(tw.noUnderline),
      tw.px12.py8.spaceX8,
      TagMod.when(canBeOpened)(
        tw.cursorPointer.rounded4.hover(tw.bgGray4.bgOpacity40)
      ),
      TagMod.when(isOpened)(tw.bgGray4.bgOpacity40),
      ^.color := props.currentEnvColorOnBanner,
      ^.onClick --> open,
      props.currentEnvironmentOpt.fold[VdomNode](
        <.img(
          ^.height := "24px",
          ^.src := "/web/gondor/images/fundsub/light-fund-subscription.svg",
          ^.alt := s"Anduin's logo"
        )
      ) { currentEnvironment =>
        currentEnvironment.environmentWhitelabelData.enabledProviderLongLogo
          .fold[VdomNode](
            <.div(
              tw.fontSemiBold.text15.leading24,
              currentEnvironment.environmentName
            )
          ) { providerLongLogo =>
            <.img(
              ^.maxHeight := props.longLogoMaxHeight,
              ^.height := s"${props.currentEnvLongLogoHeight}px",
              ^.src := providerLongLogo,
              ^.alt := s"${currentEnvironment.environmentName}'s logo"
            )
          }
      },
      TagMod.when(isOpenedOpt.isDefined)(IconR(name = Icon.Glyph.CaretDown)())
    )
  }

  private def renderHeaderBody(props: Props) = {
    val colorOnBanner =
      DashboardWhitelabelValue.TextOnBannerColor.extractThis(props.currentEnvWhitelabelValues)

    React.Fragment(
      // Crossapp Navigator
      <.div(
        tw.flex.itemsCenter,
        <.div(
          tw.mr16,
          DrawerR(
            position = Drawer.Position.Left,
            renderTarget = open => {
              <.button(
                tw.borderAll.rounded4.hPx32.wPx32,
                tw.flex.itemsCenter.justifyCenter,
                tw.bgTransparent.hover(tw.bgGray0.bgOpacity20),
                ^.color := colorOnBanner,
                ^.borderColor := colorOnBanner,
                ComponentUtils.testId(FundSubHomePageHeader, "DrawerBt"),
                IconR(name = Icon.Glyph.Hamburger)(),
                ^.onClick --> (
                  ActionEventLoggerJs.logPageViewCb(subPage = Some("All Funds Dashboard - Switch Button")) >> open
                )
              )
            },
            renderContent = _ =>
              WithReactRouterR { router =>
                CrossAppNavigator(
                  currentPage = CrossAppNavigator.CurrentPage.FundSubDashboard,
                  router = router
                )()
              }
          )()
        ),
        // Environment Navigator
        renderCurrentEnvironment(
          props,
          isOpenedOpt = None,
          open = Callback.empty
        )
      ),
      <.div(
        ^.alignSelf := "center",
        tw.hidden,
        tw.lg(tw.block),
        props.renderCenteredContent()
      ),
      // User profile
      <.div(
        ^.alignSelf := "center",
        tw.lg(tw.mlAuto),
        HeaderUser(
          targetType = HeaderUser.TargetType.AvatarTargetType
        )()
      )
    )
  }

  private def render(props: Props): VdomElement = {
    <.div(
      <.div(
        ComponentUtils.testId(FundSubHomePage, "Header"),
        ^.gridTemplateColumns := "1fr repeat(1, auto) 1fr",
        tw.wPc100.px16.hPx64,
        tw.hidden,
        tw.lg(tw.grid),
        renderHeaderBody(props = props),
        <.div(
          ComponentUtils.testId(FundSubHomePage, "Header"),
          tw.grid,
          ^.gridTemplateColumns := "1fr min-content",
          tw.wPc100.px16.hPx64,
          tw.lg(tw.hidden),
          renderHeaderBody(props = props)
        )
      )
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
