// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration

import com.raquo.laminar.api.L.*
import io.circe.Codec

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.graphql.component.laminar.QueryComponentL
import anduin.graphql.component.{FetchStrategy, GraphqlOptions}
import anduin.id.entity.EntityId
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.model.notichannel.FundSubNotificationChannels
import anduin.rohan.operation.AnduinQuery
import com.anduin.stargazer.fundsub.module.model.FundSubClientModel.FundSubPublicClientModel
import com.anduin.stargazer.notiCenter.NotificationCenter

private[laminar] case class FetchCommentSettingL(
  fundSubId: FundSubId,
  renderChildren: Signal[Option[FetchCommentSettingL.RenderChildren]] => HtmlElement,
  onRefetchEventBus: EventBus[Unit] = new EventBus[Unit]
) {

  private def lastUpdatedAtSignal =
    NotificationCenter.eventSignal(FundSubNotificationChannels.fundSubPublic(fundSubId))

  def apply(): HtmlElement = {
    QueryComponentL[FetchCommentSettingL.Variables, FetchCommentSettingL.ChildProps](
      query = AnduinQuery.FundSubPublicModel,
      variableSignal = Val(
        FetchCommentSettingL.Variables(fundSubId = fundSubId)
      ),
      options = GraphqlOptions(pollInterval = FiniteDuration(120, TimeUnit.SECONDS)),
      initialFetchStrategy = FetchStrategy.ForcedFetch
    ).apply { queryData =>
      val fundSubPublicModelSignal = queryData.data.map(_.flatMap(_.fundSubPublicModel))
      val renderChildrenSignal = fundSubPublicModelSignal.map {
        _.map { fundSubPublicModel =>
          val isFormCommentEnabled = !fundSubPublicModel.disabledFormComment &&
            fundSubPublicModel.featureSwitch.exists(_.formCommentSwitch)
          val isAmlKycCommentEnabled = fundSubPublicModel.featureSwitch.exists(_.enableAmlKycCommenting)
          val isCommentMentioningEnabled = fundSubPublicModel.featureSwitch.exists(_.enableCommentMentioning)
          val isCommentAssignmentEnabled = fundSubPublicModel.featureSwitch.exists(_.enableCommentAssignment)
          val isLpResolvingCommentDisabled = fundSubPublicModel.featureSwitch.exists(_.disableLpResolvingComment)
          val inactiveDaysThreshold = fundSubPublicModel.inactiveCommentSetting
            .filter(_.enabled)
            .map(_.days)

          FetchCommentSettingL.RenderChildren(
            formCommentDigestEmailExceptionLps = fundSubPublicModel.formCommentDigestEmailExceptionLps,
            investorEntity = fundSubPublicModel.investorEntity,
            isAmlKycCommentEnabled = isAmlKycCommentEnabled,
            isFormCommentEnabled = isFormCommentEnabled,
            isCommentMentioningEnabled = isCommentMentioningEnabled,
            isCommentAssignmentEnabled = isCommentAssignmentEnabled,
            suppressSendingFormCommentDigestEmail = fundSubPublicModel.suppressSendingFormCommentDigestEmail,
            isLpResolvingCommentDisabled = isLpResolvingCommentDisabled,
            inactiveDaysThreshold = inactiveDaysThreshold
          )
        }
      }
      renderChildren(renderChildrenSignal)
        .amend(
          div(
            onRefetchEventBus.events --> Observer[Unit] { _ =>
              queryData.forceFetch.onNext(())
            },
            child <-- lastUpdatedAtSignal.distinct.map { _ =>
              div(
                onMountCallback { _ =>
                  queryData.forceFetch.onNext(())
                }
              )
            }
          )
        )
    }
  }

}

private[laminar] object FetchCommentSettingL {

  case class RenderChildren(
    formCommentDigestEmailExceptionLps: Seq[FundSubLpId],
    investorEntity: EntityId,
    isAmlKycCommentEnabled: Boolean,
    isFormCommentEnabled: Boolean,
    isCommentMentioningEnabled: Boolean,
    isCommentAssignmentEnabled: Boolean,
    suppressSendingFormCommentDigestEmail: Boolean,
    isLpResolvingCommentDisabled: Boolean,
    inactiveDaysThreshold: Option[Int]
  )

  final case class Variables(fundSubId: FundSubId) derives CanEqual

  object Variables {
    given Codec.AsObject[Variables] = deriveCodecWithDefaults
  }

  final case class ChildProps(
    fundSubPublicModel: Option[FundSubPublicClientModel]
  ) derives CanEqual

  object ChildProps {
    given Codec.AsObject[ChildProps] = deriveCodecWithDefaults
  }

}
