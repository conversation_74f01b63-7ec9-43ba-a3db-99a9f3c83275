// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.contact.widget

import anduin.contact.endpoint.*
import anduin.contact.widget.component.ContactWidgetTab
import anduin.contact.widget.{ContactWidgetModel, ContactWidgetUtils}
import anduin.contact.widget.component.ContactWidgetTab.{ContactTab, GroupTab, SelectedContactTab, Tab}
import anduin.contact.widget.component.FilterView.{ContactFilterProperties, FilterType, GroupFilterProperties}
import com.anduin.stargazer.fundsub.module.investor.contact.widget.component.FundSubContactSearchInput
import com.anduin.stargazer.fundsub.module.investor.contact.widget.tab.{ContactTabView, GroupTabView, SelectedTabView}
import anduin.id.contact.{ContactGroupId, ContactId}
import anduin.id.fundsub.FundSubId
import anduin.scalajs.pluralize.Pluralize
import com.anduin.stargazer.fundsub.module.investor.contact.widget.FundSubContactWidget.ContactSelection
import design.anduin.components.button.Button
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

// duplicated from ContactWidget to address security issue
final case class FundSubContactWidget(
  fundSubId: FundSubId,
  addContacts: ContactSelection => Callback,
  close: Callback
) {
  def apply(): VdomElement = FundSubContactWidget.component(this)
}

object FundSubContactWidget {

  private type Props = FundSubContactWidget

  final case class State(
    activeTab: Tab = ContactTab,
    selectedContacts: Set[String] = Set.empty, // Contact selected in contact tab (with key = email + full name)
    selectedContactIds: Set[ContactId] = Set.empty,
    groupMap: Map[ContactGroupId, ContactGroupModel] = Map.empty, // All groups loaded so far
    contactMap: Map[ContactId, ContactModel] = Map.empty // All contacts loaded so far
  ) {

    val allLoadedContacts: Seq[ContactWidgetModel] =
      ContactWidgetUtils.constructContactWidgetModels(contactMap.values.toList)

    val selectedContactModels: Seq[ContactWidgetModel] = allLoadedContacts.filter { contact =>
      selectedContacts.contains(contact.key)
    }

    val selectedContactCount: Int = (
      selectedContactIds.flatMap(contactMap.get(_).map(ContactWidgetUtils.contactInfoToKey)) ++ selectedContacts
    ).size

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

  private class Backend(scope: BackendScope[Props, State]) {

    private val contactTabRef = Ref.toScalaComponent(ContactTabView.component)

    private val groupTabRef = Ref.toScalaComponent(GroupTabView.component)

    def render(props: Props, state: State): VdomElement = {
      <.div(
        tw.flex.flexCol.hPc100,
        header(props, state),
        searchInput(props),
        renderMainContent(props, state),
        footer(props, state)
      )
    }

    private def header(props: Props, state: State) = {
      ContactWidgetTab(
        activeTab = state.activeTab,
        selectedContactCount = state.selectedContactCount,
        onTabChanged = tab => scope.modState(_.copy(activeTab = tab)),
        onClose = props.close
      )()
    }

    private def searchInput(props: Props): VdomElement = {
      <.div(
        tw.mt20.mx40.flex.itemsCenter.flexNone,
        FundSubContactSearchInput(
          fundSubId = props.fundSubId,
          onSelectContact = (contact, searchContactMap) =>
            scope.modState(
              state =>
                state.copy(
                  selectedContacts = state.selectedContacts + contact.key,
                  contactMap = state.contactMap ++ searchContactMap
                ),
              Toast.successCallback(s"${contact.fullName} added")
            ),
          onSelectGroup = (text, groups, group) =>
            scope.modState(
              s => s.copy(activeTab = GroupTab),
              for {
                _ <- groupTabRef.foreachCB { groupTab =>
                  groupTab.backend.setFilterAndSearchResult(
                    filterProps = GroupFilterProperties(Some(text), Seq(FilterType.ByGroup(group.groupId))),
                    searchResult = groups
                  )
                }
              } yield ()
            ),
          onViewAllContactsResult = (text, contacts, searchContactMap) =>
            scope.modState(
              s =>
                s.copy(
                  activeTab = ContactTab,
                  contactMap = s.contactMap ++ searchContactMap
                ),
              contactTabRef.foreachCB { contactTab =>
                for {
                  _ <- contactTab.backend.setFilterProps(ContactFilterProperties(Some(text), Seq.empty))
                  _ <- contactTab.backend.setSearchResult(contacts)
                } yield ()
              }
            ),
          onViewAllGroupsResult = (text, groups) =>
            scope.modState(
              s => s.copy(activeTab = GroupTab),
              groupTabRef.foreachCB { groupTab =>
                groupTab.backend.setFilterAndSearchResult(
                  filterProps = GroupFilterProperties(Some(text), Seq.empty),
                  searchResult = groups
                )
              }
            )
        )()
      )
    }

    private def renderMainContent(props: Props, state: State): VdomNode = {
      <.div(
        tw.flexFill.flex.justifyCenter.mx40.pb20,
        renderContactTabView(props, state),
        renderGroupTabView(props, state),
        renderSelectedTabView(state)
      )
    }

    private def renderContactTabView(props: Props, state: State) = {
      contactTabRef.component(
        ContactTabView(
          fundSubId = props.fundSubId,
          isVisible = state.activeTab == ContactTab,
          selectedContacts = state.selectedContacts,
          groupMap = state.groupMap,
          onContactSelected = contacts => scope.modState(s => s.copy(selectedContacts = s.selectedContacts ++ contacts)),
          onContactRemoved = contacts => scope.modState(s => s.copy(selectedContacts = s.selectedContacts -- contacts)),
          onContactModelsLoaded = contacts =>
            scope.modState(s => s.copy(contactMap = s.contactMap ++ contacts.map(c => c.contactId -> c).toMap)),
          onGroupModelsLoaded =
            groups => scope.modState(s => s.copy(groupMap = s.groupMap ++ groups.map(g => g.groupId -> g).toMap))
        )
      )
    }

    private def renderGroupTabView(props: Props, state: State) = {
      groupTabRef.component(
        GroupTabView(
          fundSubId = props.fundSubId,
          isVisible = state.activeTab == GroupTab,
          selectedContacts = state.selectedContactIds,
          contactMap = state.contactMap,
          groupMap = state.groupMap,
          onContactSelected =
            contacts => scope.modState(s => s.copy(selectedContactIds = s.selectedContactIds ++ contacts)),
          onContactRemoved =
            contacts => scope.modState(s => s.copy(selectedContactIds = s.selectedContactIds -- contacts)),
          onContactModelsLoaded = contacts =>
            scope.modState(s => s.copy(contactMap = s.contactMap ++ contacts.map(c => c.contactId -> c).toMap)),
          onGroupModelsLoaded =
            groups => scope.modState(s => s.copy(groupMap = s.groupMap ++ groups.map(g => g.groupId -> g).toMap))
        )
      )
    }

    private def renderSelectedTabView(state: State) = {
      SelectedTabView(
        isVisible = state.activeTab == SelectedContactTab,
        selectedContacts = state.selectedContacts,
        selectedContactIds = state.selectedContactIds,
        selectedContactModels = state.selectedContactModels,
        contactMap = state.contactMap,
        groupMap = state.groupMap,
        onGroupFilterSelected = groupId => setContactTabWithFilter(FilterType.ByGroup(groupId)),
        onContactsRemoved = (contactKeys, contactIds) =>
          scope.modState { s =>
            val newActiveTab = if (s.selectedContacts == contactKeys && s.selectedContactIds == contactIds) {
              ContactTab
            } else {
              s.activeTab
            }
            s.copy(
              selectedContacts = s.selectedContacts -- contactKeys,
              selectedContactIds = s.selectedContactIds -- contactIds,
              activeTab = newActiveTab
            )
          }
      )()
    }

    private def footer(props: Props, state: State): VdomElement = {
      <.div(
        tw.flex.flexNone.itemsCenter.justifyEnd.hPx64.px20,
        tw.bgGray0.z1,
        ^.boxShadow := "0px 0px 10px rgba(0, 0, 0, 0.114674)",
        Button(onClick = props.close)("Cancel"),
        <.div(
          tw.ml8,
          Button(
            style = Button.Style.Full(Button.Color.Primary),
            isDisabled = state.selectedContactCount == 0,
            onClick = onAddContacts() >> props.close
          )(
            if (state.selectedContactCount == 0) {
              "Add contacts"
            } else {
              s"Add ${Pluralize(
                  "contact",
                  state.selectedContactCount,
                  true
                )}"
            }
          )
        )
      )
    }

    private def onAddContacts(): Callback = {
      for {
        props <- scope.props
        state <- scope.state
        selectedGroups =
          state.selectedContactIds.toSeq
            .flatMap(state.contactMap.get)
            .groupBy(_.contactId.group)
            .flatMap { case (groupId, contacts) =>
              state.groupMap.get(groupId).map(_ -> contacts)
            }
            .toSeq
        _ <- props.addContacts(ContactSelection(state.selectedContactModels, selectedGroups))
      } yield ()
    }

    private def setContactTabWithFilter(filter: FilterType) = {
      scope.modState(
        s => s.copy(activeTab = ContactTab),
        for {
          _ <- contactTabRef.foreachCB { contactTab =>
            contactTab.backend.setFilterProps(filterProps = ContactFilterProperties(None, Seq(filter)))
          }
        } yield ()
      )
    }

  }

  final case class ContactBasicInfo(
    email: String,
    firstName: String,
    lastName: String
  ) {
    val fullName: String = s"$firstName $lastName".trim
  }

  final case class ContactSelection(
    // Contact selected in Contact Tab - one contact is group of multiple contactId with same (email. full name)
    selectedContacts: Seq[ContactWidgetModel],
    // Contact selected in Group Tab, grouped by same group.
    selectedGroups: Seq[(ContactGroupModel, Seq[ContactModel])]
  ) {

    // Construct all selected contacts from contact tab & group tab, distinguished by (email, full name)
    lazy val allContacts: Seq[ContactBasicInfo] = {
      val contacts = selectedContacts.map { model =>
        ContactBasicInfo(
          model.email,
          model.firstName,
          model.lastName
        )
      }
      val groupContacts = selectedGroups.flatMap(_._2).map { model =>
        ContactBasicInfo(
          model.contactInfo.email,
          model.contactInfo.firstName,
          model.contactInfo.lastName
        )
      }
      (contacts ++ groupContacts).distinct
    }

    lazy val allEmails: Seq[String] = allContacts.map(_.email).distinct

  }

}
