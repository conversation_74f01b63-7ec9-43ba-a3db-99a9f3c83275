package com.anduin.stargazer.fundsub.module.comment.laminar.drawer

import anduin.protobuf.fundsub.comment.FormQuestionData

object CommentDrawerUIModels {
  sealed trait DrawerView derives CanEqual

  object DrawerView {
    case object AllComments extends DrawerView

    case class Doctype(doctype: String, isPublic: Boolean = true, comment: String = "") extends DrawerView

    case class Field(formQuestionData: FormQuestionData, isPublic: Boolean = true, comment: String = "")
        extends DrawerView

  }

}
