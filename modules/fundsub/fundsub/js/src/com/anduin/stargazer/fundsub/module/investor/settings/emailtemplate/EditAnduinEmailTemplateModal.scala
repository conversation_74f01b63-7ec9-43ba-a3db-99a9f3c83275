// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.emailtemplate

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.emailtemplate.{AnduinTemplate, UpdateFundSubAnduinEmailTemplateParams}
import anduin.fundsub.endpoint.lp.UserEmailTemplate
import anduin.id.fundsub.FundSubId
import anduin.protobuf.fundsub.FundSubEvent
import anduin.utils.DateTimeUtils
import com.anduin.stargazer.fundsub.client.FundSubEmailTemplateEndpointClient
import com.anduin.stargazer.fundsub.module.email.EmailTemplateEditorL
import com.anduin.stargazer.fundsub.module.email.EmailTemplateEditorL.EmailTemplate
import com.anduin.stargazer.fundsub.module.investor.settings.emailtemplate.EditAnduinEmailTemplateModal.*
import com.anduin.stargazer.fundsub.module.investor.settings.emailtemplate.EditEmailTemplateModal.renderSetDefaultSwitchDescription
import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.field.laminar.FieldL
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterL, ModalL}
import design.anduin.components.switcher.laminar.SwitcherL
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*

private[emailtemplate] final case class EditAnduinEmailTemplateModal(
  fundSubId: FundSubId,
  emailType: FundSubEvent,
  initialTemplate: UserEmailTemplate,
  initialIsDefault: Boolean,
  renderTarget: Observer[Unit] => Node,
  onComplete: Observer[OnComplete],
  onClose: Observer[Unit]
) {

  private val isDefaultVar = Var(initialIsDefault)

  private val emailTemplateVar = Var(
    EmailTemplate(
      subject = initialTemplate.subject,
      body = initialTemplate.body,
      cta = initialTemplate.primaryCTA
    )
  )

  private val isUpdatingVar = Var(false)

  private val editTemplateEventBus = new EventBus[Unit]

  def apply(): HtmlElement = {
    ModalL(
      renderTitle = _ => s"Edit ${AnduinTemplate.AnduinTemplateName}",
      size = ModalL.Size(width = ModalL.Width.Px720),
      beforeClose = onClose,
      renderTarget = renderTarget,
      renderContent = renderModalContent
    )()
  }

  private def renderModalContent(close: Observer[Unit]) = {
    div(
      ModalBodyL(
        div(
          tw.spaceY16,
          div(
            tw.borderAll.borderGray3.rounded8.py8.px12,
            div(
              tw.wFit,
              TooltipL(
                renderTarget = SwitcherL(
                  isChecked = isDefaultVar.signal,
                  onChange = isDefaultVar.writer,
                  isDisabled = Val(initialIsDefault)
                )(renderSetDefaultSwitchDescription(emailType)),
                renderContent = _.amend("Select a new default template to unset the current one"),
                isDisabled = Val(!initialIsDefault)
              )()
            )
          ),
          FieldL(
            label = Option("Template name")
          )(
            TextBoxL(
              value = Val(AnduinTemplate.AnduinTemplateName),
              isDisabled = Val(true)
            )()
          ),
          EmailTemplateEditorL(
            templateSignal = emailTemplateVar.signal,
            onChange = emailTemplateVar.writer
          )()
        )
      ),
      ModalFooterL(
        div(
          tw.flex.justifyBetween.itemsCenter,
          div(
            initialTemplate.lastEditedAt.map { lastEditedAt =>
              div(
                tw.textGray7,
                s"Updated on ${DateTimeUtils.formatInstant(lastEditedAt, DateTimeUtils.DateAndTimeFormatter2)(
                    using DateTimeUtils.defaultTimezone
                  )}"
              )
            }
          ),
          div(
            tw.flex.spaceX8,
            ButtonL(
              onClick = close.contramap(_ => ())
            )("Cancel"),
            ButtonL(
              style = ButtonL.Style.Full(color = ButtonL.Color.Primary, isBusy = isUpdatingVar.signal),
              onClick = editTemplateEventBus.toObserver.contramap(_ => ()),
              isDisabled = emailTemplateVar.signal
                .combineWith(isDefaultVar.signal)
                .map(validateTemplate)
                .invert
            )("Update").amend(
              editTemplateEventBus.events
                .withCurrentValueOf(emailTemplateVar.signal, isDefaultVar.signal)
                .flatMapSwitch { case (emailTemplate, isDefault) =>
                  isUpdatingVar.set(true)
                  updateAnduinEmailTemplate(
                    template = emailTemplate,
                    isDefault = isDefault,
                    close = close
                  )
                } --> Observer.empty
            )
          )
        )
      )
    )
  }

  private def validateTemplate(
    template: EmailTemplate,
    isDefault: Boolean
  ) = {
    val isContentChanged = initialTemplate.subject != template.subject.trim ||
      initialTemplate.body != template.body.trim ||
      initialTemplate.primaryCTA != template.cta.trim
    val isContentFilled = template.subject.trim.nonEmpty &&
      template.body.trim.nonEmpty &&
      template.cta.trim.nonEmpty

    val isDefaultChanged = initialIsDefault != isDefault

    isContentFilled && (isContentChanged || isDefaultChanged)
  }

  private def updateAnduinEmailTemplate(
    template: EmailTemplate,
    isDefault: Boolean,
    close: Observer[Unit]
  ): EventStream[Unit] = {
    AirStreamUtils.taskToStream {
      for {
        _ <- FundSubEmailTemplateEndpointClient
          .updateAnduinEmailTemplate(
            UpdateFundSubAnduinEmailTemplateParams(
              fundSubId = fundSubId,
              emailType = emailType,
              subject = template.subject,
              body = template.body,
              cta = template.cta,
              isDefaultTemplate = isDefault
            )
          )
          .map(
            _.fold(
              _ => {
                isUpdatingVar.set(false)
                Toast.error("Failed to update anduin email template")
              },
              res => {
                Toast.success("Email template updated successfully")
                isUpdatingVar.set(false)
                onComplete.onNext(OnComplete(res.template, isDefault))
                close.onNext(())
              }
            )
          )
      } yield ()
    }
  }

}

object EditAnduinEmailTemplateModal {

  final case class OnComplete(
    template: UserEmailTemplate,
    isDefault: Boolean
  )

}
