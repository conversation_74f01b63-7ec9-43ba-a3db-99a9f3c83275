// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.mailserver

import scala.concurrent.Future

import design.anduin.validators.{ErrorMessage, Result, Rule}
import japgolly.scalajs.react.*

import anduin.utils.UrlValidatorUtils

private[mailserver] final case class SmtpHostNameRule(
  errorCb: CallbackTo[ErrorMessage]
) extends Rule {

  def validate(valueCb: CallbackTo[String]): Future[CallbackTo[Result]] = {
    Future.successful {
      for {
        hostName <- valueCb
        error <- errorCb
      } yield {
        val sanitized = SmtpHostNameRule.sanitizeHostName(hostName)
        Result(
          UrlValidatorUtils.HostNameValidator.matches(sanitized) ||
            UrlValidatorUtils.IPV4Validator.matches(sanitized),
          error
        )
      }
    }
  }

}

private[mailserver] object SmtpHostNameRule {

  val SmtpPart = "smtp://"

  def sanitizeHostName(host: String): String = {
    if (host.trim.toLowerCase.startsWith(SmtpPart)) {
      host.trim.drop(SmtpPart.length)
    } else {
      host.trim
    }
  }

  def apply(error: ErrorMessage): SmtpHostNameRule = {
    SmtpHostNameRule(
      errorCb = CallbackTo.pure(error)
    )
  }

}
