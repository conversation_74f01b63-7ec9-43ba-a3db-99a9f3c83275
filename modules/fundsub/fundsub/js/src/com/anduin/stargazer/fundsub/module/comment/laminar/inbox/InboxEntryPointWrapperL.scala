// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.inbox

import anduin.id.fundsub.FundSubId
import com.anduin.stargazer.fundsub.module.comment.models.{CommentNotificationSetting, InboxLocationParams}
import com.raquo.laminar.api.L.*

import anduin.fundsub.comment.models.CommentNotification
import anduin.fundsub.comment.utils.CommentUtils
import com.anduin.stargazer.client.services.user.UserUtils
import com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent.{
  FetchAdminsInfoDashboardSchemaL,
  FetchCommentNotificationsL,
  FetchCommentSettingL
}

// TODO(comment) @tuananhtd: check if we can remove this layer
private[inbox] case class InboxEntryPointWrapperL(
  presetLocationOptSignal: Signal[Option[InboxLocationParams]],
  fundSubId: FundSubId,
  renderChildren: InboxEntryPointWrapperL.RenderChildren => HtmlElement,
  onRefetchSettingsEventBus: EventBus[Unit] = new EventBus[Unit]
) {

  def apply(): HtmlElement = {
    FetchCommentSettingL(
      fundSubId = fundSubId,
      renderChildren = settingSignal => {
        val isFormCommentEnabledSignal = settingSignal.map(_.exists(_.isFormCommentEnabled)).distinct
        val isAmlKycCommentEnabledSignal = settingSignal.map(_.exists(_.isAmlKycCommentEnabled)).distinct
        val isCommentMentioningEnabledSignal = settingSignal.map(_.exists(_.isCommentMentioningEnabled)).distinct
        val isCommentAssignmentEnabledSignal = settingSignal.map(_.exists(_.isCommentAssignmentEnabled)).distinct
        val inactiveCommentThresholdSignal = settingSignal.map(_.flatMap(_.inactiveDaysThreshold))

        val commentNotificationSettingSignal = settingSignal
          .map(_.map { setting =>
            CommentNotificationSetting(
              formCommentDigestEmailExceptionLps = setting.formCommentDigestEmailExceptionLps,
              investorEntity = setting.investorEntity,
              suppressSendingFormCommentDigestEmail = setting.suppressSendingFormCommentDigestEmail
            )
          })
          .distinct

        div(
          child.maybe <-- isFormCommentEnabledSignal.map { isFormCommentEnabled =>
            Option.when(isFormCommentEnabled) {
              val userId = UserUtils.getCurrentUserIdWithDefaultValue
              FetchCommentNotificationsL(
                fundSubId = fundSubId,
                renderChildren = notificationsOpt => {
                  val notificationsSignal = notificationsOpt
                    .map(_.getOrElse(List.empty))
                    .map(_.map { item =>
                      item.id -> item.model
                    })
                    .map(CommentUtils.getNewCommentNotification(_, userId))

                  FetchAdminsInfoDashboardSchemaL(
                    fundSubId = fundSubId,
                    renderChildren = adminsSignal => {
                      renderChildren(
                        InboxEntryPointWrapperL.RenderChildren(
                          commentNotificationSignal = notificationsSignal,
                          renderInboxModal = closeModal => {
                            InboxModalL(
                              presetLocationOptSignal = presetLocationOptSignal,
                              commentNotificationSettingSignal = commentNotificationSettingSignal,
                              fundSubAdminsInfoSchema = adminsSignal,
                              fundId = fundSubId,
                              isAmlKycCommentEnabledSignal = isAmlKycCommentEnabledSignal,
                              isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
                              isCommentAssignmentEnabledSignal = isCommentAssignmentEnabledSignal,
                              inactiveThresholdSignal = inactiveCommentThresholdSignal,
                              notificationsSignal = notificationsSignal,
                              onClose = closeModal
                            )()
                          }
                        )
                      )
                    }
                  )()
                }
              )()
            }
          }
        )
      },
      onRefetchEventBus = onRefetchSettingsEventBus
    )()
  }

}

private[inbox] object InboxEntryPointWrapperL {

  case class RenderChildren(
    commentNotificationSignal: Signal[List[CommentNotification]],
    renderInboxModal: Observer[Unit] => HtmlElement
  )

}
