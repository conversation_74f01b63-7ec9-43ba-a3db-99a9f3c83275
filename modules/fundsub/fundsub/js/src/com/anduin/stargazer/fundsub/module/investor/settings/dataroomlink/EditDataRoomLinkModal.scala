// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.dataroomlink

import design.anduin.components.button.Button
import design.anduin.components.field.Field
import design.anduin.components.icon.Icon
import design.anduin.components.modal.{ModalBody, ModalFooterWCancel}
import design.anduin.components.textbox.TextBox
import design.anduin.components.toast.Toast
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import anduin.fundsub.endpoint.whitelabel.UpdateSharedDataRoomLinkParams
import anduin.id.fundsub.FundSubId
import anduin.utils.UrlValidatorUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import com.anduin.stargazer.client.utils.ZIOUtils

final case class EditDataRoomLinkModal(
  fundSubId: FundSubId,
  initialUrl: String,
  initialDisplayName: String,
  onUpdated: (String, String) => Callback,
  onCancel: Callback
) {
  def apply(): VdomElement = EditDataRoomLinkModal.component(this)
}

object EditDataRoomLinkModal {
  private type Props = EditDataRoomLinkModal

  private final case class State(
    url: String = "",
    displayName: String = "",
    showValidation: Boolean = false,
    isSaving: Boolean = false
  )

  private val supportedProtocols = List[String](
    "http://",
    "https://",
    "ftp://"
  )

  private def addProtocolIfNecessary(url: String) = {
    val trimmedUrl = url.trim
    val needAppendProtocol = !supportedProtocols.exists { protocol =>
      trimmedUrl.startsWith(protocol)
    }
    if (needAppendProtocol) {
      s"https://$trimmedUrl"
    } else {
      trimmedUrl
    }
  }

  private final case class Backend(scope: BackendScope[Props, State]) {

    private def onSave: Callback = {
      val saveCb = for {
        props <- scope.props
        state <- scope.state
        url = addProtocolIfNecessary(state.url)
        displayName = state.displayName.trim
        isValid = UrlValidatorUtils.SimpleWebLinkValidator.matches(url.trim)
        _ <-
          if (isValid) {
            ZIOUtils.toReactCallback(
              FundSubEndpointClient
                .updateSharedDrLink(
                  UpdateSharedDataRoomLinkParams(
                    fundSubId = props.fundSubId,
                    url = url,
                    displayName = displayName
                  )
                )
                .map(
                  _.fold(
                    _ =>
                      Toast.errorCallback("Failed to update external website link") >>
                        scope.modState(_.copy(isSaving = false)),
                    _ =>
                      Toast.successCallback("External website link updated successfully") >>
                        props.onUpdated(url, displayName)
                  )
                )
            )
          } else {
            scope.modState(_.copy(isSaving = false, showValidation = true))
          }
      } yield ()
      scope.modState(
        _.copy(isSaving = true),
        saveCb
      )
    }

    def render(props: Props, state: State): VdomElement = {
      val url = state.url
      val urlIsValid = UrlValidatorUtils.SimpleWebLinkValidator.matches(url.trim)
      val urlValidation = if (!state.showValidation || urlIsValid) {
        Field.Validation.None
      } else {
        Field.Validation.Invalid("Please enter a valid URL")
      }

      val urlStatus = if (!state.showValidation) {
        TextBox.Status.None
      } else if (urlIsValid) {
        TextBox.Status.Valid
      } else {
        TextBox.Status.Invalid
      }

      <.div(
        ModalBody()(
          <.div(
            tw.mb20,
            <.div(
              tw.fontMedium.mb4,
              "Link"
            ),
            Field(
              validation = urlValidation
            )(
              <.div(
                ComponentUtils.testId(EditDataRoomLinkModal, "LinkTextBox"),
                tw.wPc100,
                TextBox(
                  value = url,
                  placeholder = "https://www.mydomain.com",
                  status = urlStatus,
                  size = TextBox.Size.Px32,
                  icon = Option(Icon.Glyph.Link),
                  onChange = value => scope.modState(_.copy(url = value)),
                  isDisabled = state.isSaving
                )()
              )
            ),
            <.div(
              tw.fontMedium.mt16.mb4,
              "Name"
            ),
            Field()(
              <.div(
                ComponentUtils.testId(EditDataRoomLinkModal, "NameTextBox"),
                tw.wPc100,
                TextBox(
                  value = state.displayName,
                  placeholder = "Ex. Link to data room",
                  size = TextBox.Size.Px32,
                  onChange = value => scope.modState(_.copy(displayName = value)),
                  isDisabled = state.isSaving
                )()
              )
            )
          )
        ),
        ModalFooterWCancel(props.onCancel) {
          Button(
            style = Button.Style.Full(color = Button.Color.Primary, isBusy = state.isSaving),
            isDisabled =
              (state.showValidation && !urlIsValid) || (props.initialUrl == state.url.trim && props.initialDisplayName == state.displayName.trim),
            onClick = onSave
          )("Save")
        }
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps { props =>
      State(
        url = props.initialUrl.trim,
        displayName = props.initialDisplayName.trim
      )
    }
    .renderBackend[Backend]
    .build

}
