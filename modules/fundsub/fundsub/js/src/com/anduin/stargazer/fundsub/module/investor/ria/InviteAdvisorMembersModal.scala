// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.ria

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.field.laminar.FieldL
import design.anduin.components.icon.Icon
import design.anduin.components.modal.laminar.{ModalBodyL, ModalFooterWCancelL, ModalL}
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.ria.{InviteAdvisor, InviteFundAdvisorParams, RiaFundGroup, RiaFundGroupState}
import anduin.id.fundsub.ria.FundSubRiaGroupId
import anduin.model.common.emailaddress.EmailAddress
import com.anduin.stargazer.fundsub.client.FundSubRiaEndpointClient

private[ria] case class InviteAdvisorMembersModal(
  group: RiaFundGroup,
  renderTarget: Observer[Unit] => HtmlElement,
  onDoneAddAdvisorMembers: Observer[Unit],
  existingGroupEmailAddresses: Seq[String]
) {

  private val membersVar = Var(Seq(InviteAdvisor()))
  private val membersSignal = membersVar.signal.distinct
  private val inviteAdvisorEvent = new EventBus[Unit]
  private val clickedAddButtonVar = Var(false)
  private val clickedAddButtonSignal = clickedAddButtonVar.signal.distinct
  private val isInvitingAdvisorVar = Var(false)
  private val isInvitingAdvisorSignal = isInvitingAdvisorVar.signal.distinct

  private val areAllEmailHasSameDomainSignal = membersSignal.map { members =>
    val emailDomains = members.flatMap { member =>
      EmailAddress
        .unapply(
          name = Option(member.firstName),
          address = member.email
        )
        .map(_.domain.value)
    }

    val areAllEmailHasSameDomain = emailDomains.distinct.length == 1

    if (group.members.isEmpty) {
      areAllEmailHasSameDomain
    } else {
      group.state match {
        case RiaFundGroupState.RiaEntityNotLinked(invitedAdvisors) =>
          (
            invitedAdvisors.flatMap(_.info.userInfo.emailAddress).map(_.domain.value) ++
              emailDomains
          ).distinct.length == 1
        case _: RiaFundGroupState.RiaEntityLinked => false
      }
    }
  }

  private val isValidSignal = membersSignal.map { members =>
    members.forall { item =>
      item.firstName.trim.nonEmpty &&
      item.lastName.trim.nonEmpty &&
      item.email.trim.nonEmpty &&
      EmailAddress.isValid(item.email.trim) &&
      !existingGroupEmailAddresses.contains(item.email.trim)
    }
    && members.map(_.email.trim).distinct.length == members.length
  } && areAllEmailHasSameDomainSignal

  private def renderMembers(): HtmlElement = {
    div(
      child.maybe <-- membersSignal.map(_.isEmpty).map {
        Option.unless(_) {
          div(
            tw.mb4.text13.leading20,
            tw.flex.itemsCenter.gapX8,
            div(
              width.px(175),
              FieldL(
                label = Option("Advisor first name"),
                requirement = FieldL.Requirement.Required
              )()
            ),
            div(
              width.px(175),
              FieldL(
                label = Option("Advisor last name"),
                requirement = FieldL.Requirement.Required
              )()
            ),
            div(
              tw.flexFill,
              FieldL(
                label = Option("Advisor email"),
                requirement = FieldL.Requirement.Required
              )()
            )
          )
        }
      },
      children <-- membersSignal.splitByIndex { (index, _, memberSignal) =>
        div(
          tw.mb8,
          tw.flex.gapX8,
          div(
            width.px(175),
            FieldL(
              label = None,
              requirement = FieldL.Requirement.Required,
              validation = memberSignal.map(_.firstName.trim.isEmpty).combineWith(clickedAddButtonSignal).mapN {
                (isEmpty, clickedAddButton) =>
                  if (isEmpty && clickedAddButton) {
                    FieldL.Validation.Invalid("Advisor first name is required")
                  } else {
                    FieldL.Validation.None
                  }
              }
            )(
              TextBoxL(
                placeholder = "e.g. John",
                value = memberSignal.map(_.firstName),
                onChange = Observer[String] { firstName =>
                  membersVar.update { members =>
                    members.lift(index).fold(members) { member =>
                      members.updated(index, member.copy(firstName = firstName))
                    }
                  }
                }
              )()
            )
          ),
          div(
            width.px(175),
            FieldL(
              label = None,
              requirement = FieldL.Requirement.Required,
              validation = memberSignal.map(_.lastName.trim.isEmpty).combineWith(clickedAddButtonSignal).mapN {
                (isEmpty, clickedAddButton) =>
                  if (isEmpty && clickedAddButton) {
                    FieldL.Validation.Invalid("Advisor last name is required")
                  } else {
                    FieldL.Validation.None
                  }
              }
            )(
              TextBoxL(
                placeholder = "e.g. Smith",
                value = memberSignal.map(_.lastName),
                onChange = Observer[String] { lastName =>
                  membersVar.update { members =>
                    members.lift(index).fold(members) { member =>
                      members.updated(index, member.copy(lastName = lastName))
                    }
                  }
                }
              )()
            )
          ),
          div(
            tw.flexFill,
            FieldL(
              label = None,
              requirement = FieldL.Requirement.Required,
              validation = memberSignal
                .map(_.email)
                .combineWith(membersSignal, clickedAddButtonSignal, areAllEmailHasSameDomainSignal)
                .map { (email, members, clickedAddButton, areAllEmailHasSameDomain) =>
                  if (clickedAddButton && email.trim.isEmpty) {
                    FieldL.Validation.Invalid("Email is required")
                  } else if (clickedAddButton && !EmailAddress.isValid(email.trim)) {
                    FieldL.Validation.Invalid("Invalid email address")
                  } else if (clickedAddButton && existingGroupEmailAddresses.contains(email.trim)) {
                    FieldL.Validation.Invalid("Advisor already invited")
                  } else if (clickedAddButton && members.count(_.email.trim == email.trim) > 1) {
                    FieldL.Validation.Invalid("This email is already used by another advisor")
                  } else if (clickedAddButton && !areAllEmailHasSameDomain) {
                    FieldL.Validation.Invalid("All invited emails must have the same domain")
                  } else {
                    FieldL.Validation.None
                  }
                }
            )(
              TextBoxL(
                placeholder = "e.g. <EMAIL>",
                value = memberSignal.map(_.email),
                onChange = Observer[String] { email =>
                  membersVar.update { members =>
                    members.lift(index).fold(members) { member =>
                      members.updated(index, member.copy(email = email))
                    }
                  }
                }
              )()
            )
          ),
          ButtonL(
            isDisabled = membersSignal.map(_.length).map(_ == 1),
            style = ButtonL.Style.Minimal(icon = Option(Icon.Glyph.Cross)),
            onClick = Observer { _ =>
              membersVar.update(_.patch(index, Nil, 1))
            }
          )()
        )
      }
    )
  }

  private def cleanUpState(): Unit = {
    Var.set(
      membersVar -> Seq(InviteAdvisor()),
      clickedAddButtonVar -> false,
      isInvitingAdvisorVar -> false
    )
  }

  def apply(): HtmlElement = {
    ModalL(
      size = ModalL.Size(width = ModalL.Width.Px720),
      renderContent = onClose => {
        div(
          onUnmountCallback { _ =>
            cleanUpState()
          },
          ModalBodyL(
            div(
              // List of members
              renderMembers(),
              div(
                tw.py8,
                ButtonL(
                  style = ButtonL.Style.Text(color = ButtonL.Color.Primary, icon = Option(Icon.Glyph.Plus)),
                  onClick = Observer { _ =>
                    membersVar.update(_ :+ InviteAdvisor())
                  }
                )("Add advisor")
              )
            )
          ),
          ModalFooterWCancelL(
            cancel = onClose
          )(
            ButtonL(
              isDisabled = isValidSignal.combineWith(clickedAddButtonSignal).map(!_ && _),
              style = ButtonL.Style.Full(
                color = ButtonL.Color.Primary,
                isBusy = isInvitingAdvisorSignal
              ),
              onClick = Observer { _ =>
                clickedAddButtonVar.set(true)
                inviteAdvisorEvent.emit(())
              }
            )("Invite")
          ),
          inviteAdvisorEvent.events.sample(membersSignal, isValidSignal).filter(_._2).flatMapSwitch { (members, _) =>
            handleInviteAdvisor(
              fundRiaGroupId = group.id,
              invitedAdvisors = members,
              onClose = onClose
            )
          } --> Observer.empty
        )
      },
      renderTitle = _ => "Invite advisors",
      renderTarget = renderTarget
    )().amend(
      onUnmountCallback { _ =>
        Var.set(
          clickedAddButtonVar -> false
        )
      }
    )
  }

  private def handleInviteAdvisor(
    fundRiaGroupId: FundSubRiaGroupId,
    invitedAdvisors: Seq[InviteAdvisor],
    onClose: Observer[Unit]
  ) = {
    AirStreamUtils.taskToStream(
      for {
        _ <- ZIO.succeed(isInvitingAdvisorVar.set(true))
        _ <- FundSubRiaEndpointClient
          .inviteFundAdvisor(
            InviteFundAdvisorParams(
              fundRiaGroupId = fundRiaGroupId,
              inviteAdvisors = invitedAdvisors
            )
          )
          .map(
            _.fold(
              error => {
                isInvitingAdvisorVar.set(false)
                Toast.error("Failed to invite advisor")
              },
              res => {
                isInvitingAdvisorVar.set(false)
                onClose.onNext(())
                onDoneAddAdvisorMembers.onNext(())
                Toast.success("Advisor invited")
              }
            )
          )
      } yield ()
    )
  }

}
