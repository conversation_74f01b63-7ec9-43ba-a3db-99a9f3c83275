// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.inbox

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.pagination.laminar.PaginationL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*

import anduin.fundsub.comment.models.CommentNotification
import anduin.id.fundsub.{FundSubCloseId, FundSubId, FundSubLpId}
import anduin.model.common.user.UserId
import anduin.model.id.TeamId
import anduin.protobuf.flow.fundsub.admin.lpdashboard.{LpDocRequestStatus, LpStatus}
import anduin.protobuf.fundsub.comment.TopicData
import anduin.stargazer.service.formcomment.FormCommentCommons.*
import com.anduin.stargazer.fundsub.module.comment.laminar.inbox.CommentInboxFilterMenuL.CommentFilterData
import com.anduin.stargazer.fundsub.module.comment.laminar.share.OpenResolvedCommentSwitcherL
import com.anduin.stargazer.fundsub.module.comment.laminar.share.gp.DualThreadsListSkeletonL
import com.anduin.stargazer.fundsub.module.comment.models.CommentView

private[inbox] case class InboxThreadListSectionL(
  fundId: FundSubId,

  // filter signals
  pageIndexSignal: Signal[Int],
  tabSignal: Signal[NewInboxTab],
  isResolvedTabSignal: Signal[Boolean],
  anchorPointTypeFilterSignal: Signal[AnchorPointTypeFilter],
  unreadFilterSignal: Signal[Boolean],
  assigneeFilterSignal: Signal[Option[Either[UserId, TeamId]]],
  commentVisibilityFilterSignal: Signal[Set[CommentVisibilityType]],
  lpCloseIdsFilterSignal: Signal[Set[FundSubCloseId]],
  lpStatusFilterSignal: Signal[Set[LpStatus]],
  supportingDocStatusFilterSignal: Signal[Set[LpDocRequestStatus]],

  // data signals
  pageItemsSignal: Signal[List[DualCommentThread]],
  allPageItemsCountSignal: Signal[Int],
  allItemIdsAndStatusInInboxTabSignal: Signal[List[DualCommentThreadIdAndStatus]],
  isFetchingSignal: Signal[Boolean],
  notificationsSignal: Signal[List[CommentNotification]],
  selectedCommentViewSignal: Signal[CommentView],
  isCommentAssignmentEnabledSignal: Signal[Boolean],
  inactiveThresholdSignal: Signal[Option[Int]],

  // filter update observers
  onPageIndexUpdate: Observer[Int],
  onSwitchToResolvedTab: Observer[Boolean],
  onAnchorPointTypeFilterUpdate: Observer[AnchorPointTypeFilter],
  onUnreadFilterUpdate: Observer[Boolean],
  onAssigneeFilterUpdate: Observer[Option[Either[UserId, TeamId]]],
  onCommentVisibilityFilterUpdate: Observer[Set[CommentVisibilityType]],
  onLpCloseIdsFilterUpdate: Observer[Set[FundSubCloseId]],
  onLpStatusFilterUpdate: Observer[Set[LpStatus]],
  onSupportingDocStatusFilterUpdate: Observer[Set[LpDocRequestStatus]],

  // action triggers
  onMarkAllAsRead: Observer[Option[FundSubLpId]],
  onViewAnchorPoint: Observer[(DualCommentThread, Boolean)],
  refetchAssigneeInfoEventStream: EventStream[(FundSubLpId, TopicData)],
  lpInfosSignal: Signal[Seq[InboxDualThreadSummaryItemL.LpInfoForThreadSummary]]
) {

  // Total number of threads per page
  private val NumThreadsPerPage = 10

  private case class ThreadCount(
    open: Int,
    resolved: Int,
    openWithNewNotifications: Int,
    resolvedWithNewNotifications: Int
  )

  private val markAllAsReadEventBus = new EventBus[Unit]

  private val threadInInboxTabCountSignal = allItemIdsAndStatusInInboxTabSignal
    .combineWith(notificationsSignal)
    .map { case (allItemIdAndStatus, notifications) =>
      val openCount = allItemIdAndStatus.count(_.isOpen)
      val resolvedCount = allItemIdAndStatus.size - openCount
      val threadWithNewNotifications = notifications
        .filter(!_.seen)
        .map(_.threadId)
        .toSet

      val openWithNewNotifications = allItemIdAndStatus.count { item =>
        item.isOpen &&
        item.threadIds.exists(threadWithNewNotifications.contains)
      }

      val resolvedWithNewNotifications = allItemIdAndStatus.count { item =>
        !item.isOpen &&
        item.threadIds.exists(threadWithNewNotifications.contains)
      }

      ThreadCount(
        open = openCount,
        resolved = resolvedCount,
        openWithNewNotifications = openWithNewNotifications,
        resolvedWithNewNotifications = resolvedWithNewNotifications
      )
    }

  private val currentLpIdOptSignal = tabSignal.map {
    case NewInboxTab.IndividualInvestorTab(lpId) => Option(lpId)
    case _                                       => None
  }

  private def renderFilters = {
    val onCommentFilterUpdate: Observer[CommentFilterData] = Observer { commentFilterData =>
      onAnchorPointTypeFilterUpdate.onNext(commentFilterData.documentTypeFilter)
      onUnreadFilterUpdate.onNext(commentFilterData.includeUnreadComments)
      onCommentVisibilityFilterUpdate.onNext(commentFilterData.commentVisibilityFilter)
      onLpCloseIdsFilterUpdate.onNext(commentFilterData.lpCloseFilter)
      onLpStatusFilterUpdate.onNext(commentFilterData.lpStatusFilter)
      onSupportingDocStatusFilterUpdate.onNext(commentFilterData.supportingDocStatusFilter)
    }
    val commentFilterDataSignal = anchorPointTypeFilterSignal
      .combineWith(
        unreadFilterSignal,
        commentVisibilityFilterSignal,
        lpCloseIdsFilterSignal,
        lpStatusFilterSignal,
        supportingDocStatusFilterSignal
      )
      .map {
        case (
              anchorPointTypeFilter,
              unreadFilter,
              commentVisibilityType,
              lpClosesFilter,
              lpStatusFilter,
              supportingDocStatusFilter
            ) =>
          CommentFilterData(
            includeUnreadComments = unreadFilter,
            commentVisibilityFilter = commentVisibilityType,
            lpCloseFilter = lpClosesFilter,
            documentTypeFilter = anchorPointTypeFilter,
            lpStatusFilter = lpStatusFilter,
            supportingDocStatusFilter = supportingDocStatusFilter
          )
      }

    div(
      tw.flex,
      child.maybe <-- tabSignal.map { tab =>
        Option.when(tab == NewInboxTab.AssignedToOthersTab) {
          div(
            tw.pr8,
            AssigneeFilterButton(
              fundId = fundId,
              assigneeSignal = assigneeFilterSignal,
              skipActorGroupSignal = Val(true),
              onAssigneeUpdate = onAssigneeFilterUpdate
            )()
          )
        }
      },
      child <-- tabSignal
        .map(_ != NewInboxTab.MentionsTab)
        .splitBoolean(
          whenTrue = _ =>
            CommentInboxFilterButtonL(
              fundSubId = fundId,
              commentFilterDataSignal = commentFilterDataSignal,
              onCommentFilterUpdate = onCommentFilterUpdate
            )(),
          whenFalse = _ => emptyNode
        )
    )
  }

  private def renderEmptyTab = {
    div(
      height.px := 500,
      NonIdealStateL(
        icon = {
          div(
            tw.textGray4,
            IconL(
              name = Val(Icon.Glyph.Comment),
              size = Icon.Size.Custom(px = 48)
            )()
          )
        },
        title = span(tw.textGray7.fontMedium.text15.leading24, "No comments yet"),
        description = ""
      )().amend(
        tw.hPc100.wPc100,
        tw.flex.itemsCenter.justifyCenter.textCenter.px48
      )
    )
  }

  private def renderItems = {
    div(
      ComponentUtils.testIdL("InboxThreadListL-ThreadsContainer"),
      tw.flexFill.overflowAuto.hPc100,
      child <-- isFetchingSignal
        .combineWith(
          pageItemsSignal.map(_.isEmpty)
        )
        .distinct
        .map { (isFetching, isEmpty) =>
          if (isFetching) {
            DualThreadsListSkeletonL()()
          } else if (isEmpty) {
            renderEmptyTab
          } else {
            div(
              InboxThreadItemsListL(
                tabSignal = tabSignal,
                selectedCommentViewSignal = selectedCommentViewSignal,
                dualCommentThreadsSignal = pageItemsSignal,
                unreadThreadsSignal = notificationsSignal.map(_.map(_.threadId).toSet).distinct,
                onViewAnchorPoint = onViewAnchorPoint,
                isCommentAssignmentEnabledSignal = isCommentAssignmentEnabledSignal,
                inactiveThresholdSignal = inactiveThresholdSignal,
                fundSubId = fundId,
                refetchAssigneeInfoEventStream = refetchAssigneeInfoEventStream,
                isResolvedTabSignal = isResolvedTabSignal,
                lpInfosSignal = lpInfosSignal
              )(),
              // Pagination
              child.maybe <-- allPageItemsCountSignal.map { totalItems =>
                val totalPages = (totalItems + NumThreadsPerPage - 1) / NumThreadsPerPage
                Option.when(totalItems > 0 && totalPages > 1) {
                  div(
                    ComponentUtils.testIdL("InboxThreadListL-Pagination"),
                    tw.flex.itemsCenter.justifyCenter.p16,
                    PaginationL(
                      currentPage = pageIndexSignal,
                      totalPage = Val(totalPages),
                      onJumpToPage = onPageIndexUpdate
                    )()
                  )
                }
              }
            )
          }
        }
    )
  }

  private def renderMarkAllAsReadBtn = {
    val shouldRenderMarkAllAsReadSignal = threadInInboxTabCountSignal.map { count =>
      count.openWithNewNotifications > 0 || count.resolvedWithNewNotifications > 0
    }
    child <-- shouldRenderMarkAllAsReadSignal.splitBoolean(
      whenTrue = _ =>
        div(
          tw.mr8,
          TooltipL(
            renderContent = _.amend("Mark all as read"),
            renderTarget = {
              ButtonL(
                testId = "InboxListThreadsL-MarkAllReadButton",
                style = ButtonL.Style.Minimal(icon = Option(Icon.Glyph.CheckDouble)),
                onClick = markAllAsReadEventBus.writer.contramap(_ => ())
              )()
            }
          )()
        ),
      whenFalse = _ => emptyNode
    )

  }

  private def renderHeaderAndFilters = {
    div(
      ComponentUtils.testIdL("InboxThreadListL-InvestorFilterLabel"),
      tw.p16,
      tw.borderBottom.borderGray3.border1,
      tw.flex.itemsCenter,
      div(
        tw.flexFill,
        OpenResolvedCommentSwitcherL(
          isResolvedTabSignal = isResolvedTabSignal,
          onSwitchToResolvedTab = onSwitchToResolvedTab,
          unreadOpenCommentsSignal = threadInInboxTabCountSignal.map(_.openWithNewNotifications),
          unreadResolvedCommentsSignal = threadInInboxTabCountSignal.map(_.resolvedWithNewNotifications)
        )()
      ),
      renderMarkAllAsReadBtn,
      renderFilters
    )
  }

  def apply(): HtmlElement = {
    div(
      ComponentUtils.testIdL("InboxThreadListL"),
      tw.flex.flexCol,
      tw.hPc100.wPc100,
      renderHeaderAndFilters,
      renderItems,
      markAllAsReadEventBus.events.withCurrentValueOf(currentLpIdOptSignal) --> onMarkAllAsRead
    )
  }

}
