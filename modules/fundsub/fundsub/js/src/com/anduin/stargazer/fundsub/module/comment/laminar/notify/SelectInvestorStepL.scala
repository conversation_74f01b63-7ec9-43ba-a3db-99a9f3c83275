// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.notify

import com.raquo.laminar.api.L.*
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.ModalHeaderL
import design.anduin.style.tw.*

import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.utils.StringUtils
import com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent.FetchLpsMetaInfoL
import com.anduin.stargazer.fundsub.module.comment.models.CommentNotificationSetting

private[notify] case class SelectInvestorStepL(
  commentNotificationSettingSignal: Signal[Option[CommentNotificationSetting]],
  fundSubId: FundSubId,
  onClose: Observer[Unit],
  onGoToNextStep: Observer[FundSubLpId]
) {

  def apply(): HtmlElement = {
    FetchLpsMetaInfoL(
      fundSubId = fundSubId,
      getLpsWithCommentOnly = true,
      getLpsUnsubscribedToDigestEmailOnly = true,
      renderChildren = lpsMetaInfoData => {
        val listLpMetaSignal = lpsMetaInfoData.allLpsMetaInfoSignal
        div(
          width := "600px",
          ModalHeaderL(
            title = "Select an investor",
            close = onClose
          )(),
          div(
            tw.px24.mt20.mb24,
            div(
              tw.mb16,
              "Select an investor you'd like to notify of new comments"
            ),
            children <-- listLpMetaSignal.split(_.fundSubLpId) { case (lpId, _, lpMetaInfoSignal) =>
              val openSharedCommentCountSignal = lpMetaInfoSignal.map(_.openSharedCommentCount)
              val displayNameSignal = lpMetaInfoSignal.map(_.displayName)
              div(
                tw.borderBottom.border1.borderGray3,
                tw.py12.px16.hover(tw.bgGray1),
                tw.flex.itemsCenter.cursorPointer,
                onClick.mapToUnit --> onGoToNextStep.contramap[Unit](_ => lpId),
                div(
                  tw.mrAuto,
                  div(
                    tw.textBody.fontSemiBold,
                    text <-- displayNameSignal
                  ),
                  div(
                    tw.textGray6,
                    text <-- openSharedCommentCountSignal.map { openSharedCommentCount =>
                      s"$openSharedCommentCount open ${StringUtils.getSingularPlural("thread", openSharedCommentCount)}"
                    }
                  )
                ),
                div(
                  tw.flex.itemsCenter.textGray7,
                  div(tw.fontSemiBold.mr8, "Select"),
                  IconL(name = Val(Icon.Glyph.ChevronRight))()
                )
              )
            }
          )
        )
      }
    )()
  }

}
