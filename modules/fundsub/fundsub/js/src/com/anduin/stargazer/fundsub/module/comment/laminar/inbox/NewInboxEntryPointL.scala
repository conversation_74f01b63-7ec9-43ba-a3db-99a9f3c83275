// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.inbox

import anduin.actionlogger.ActionEventLoggerJs
import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.formcomment.{GetFundCommentNotificationParams, GetFundCommentNotificationResponse}
import anduin.fundsub.utils.FormCommentUtils
import anduin.id.fundsub.FundSubId
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.investor.dashboard.v2.PortalTracker
import com.anduin.stargazer.service.actionlogger.ActionEventViewFundCommentNotificationSummaryUiParams
import com.anduin.stargazer.util.date.DateCalculator
import com.raquo.laminar.api.L.*
import design.anduin.components.badge.Badge
import design.anduin.components.badge.laminar.BadgeL
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.CssVar
import org.scalajs.dom.document
import zio.ZIO

import anduin.fundsub.comment.models.CommentNotification

private[fundsub] final case class NewInboxEntryPointL(
  fundId: FundSubId
) {

  private val commentNotificationVar = Var(List.empty[CommentNotification])

  private val commentParticipationDataVar = Var(
    GetFundCommentNotificationResponse(
      List.empty,
      List.empty,
      List.empty
    )
  )

  private val portalIsOpenedVar = Var(false)

  private val commentNotificationDataSignal = commentNotificationVar.signal
    .combineWith(
      commentParticipationDataVar.signal
    )
    .map { case (notifications, participantInfo) =>
      getCommentNotificationData(notifications, participantInfo)
    }
    .distinct

  private val mountTimeVar = Var[Long](-1)

  private val unmountEventBus: EventBus[Long] = new EventBus[Long]

  private sealed trait BubbleStyle derives CanEqual

  private case object RedDot extends BubbleStyle

  private case object RedBadge extends BubbleStyle

  private case object GrayBadge extends BubbleStyle

  private case object Empty extends BubbleStyle

  private final case class CommentNotificationData(
    unreadGeneralComments: Int,
    unreadMentionComments: Int,
    unreadParticipatedThreads: Int,
    assignedToMe: Int
  ) {

    val bubbleStyle: BubbleStyle = {
      if (unreadMentionComments + unreadParticipatedThreads > 0) {
        RedBadge
      } else if (unreadGeneralComments > 0) {
        RedDot
      } else if (assignedToMe > 0) {
        GrayBadge
      } else {
        Empty
      }
    }

    val totalRelatedNotificationCount: Int = if (unreadMentionComments + unreadParticipatedThreads > 0) {
      unreadMentionComments + unreadParticipatedThreads
    } else {
      assignedToMe
    }

  }

  private def getCommentNotificationData(
    notifications: List[CommentNotification],
    participantInfo: GetFundCommentNotificationResponse
  ) = {
    val accessibleThreadIds = participantInfo.accessibleThreadIds.toSet
    val newCommentAndThreadIds = notifications
      .filter { notification =>
        accessibleThreadIds.contains(notification.threadId)
      }
      .flatMap { noti =>
        noti.commentIdOpt.map(_ -> noti.threadId)
      }

    CommentNotificationData(
      unreadGeneralComments = newCommentAndThreadIds.count { case (commentId, threadId) =>
        !participantInfo.mentionCommentIds.contains(commentId) &&
        !participantInfo.participatedThreadIds.contains(threadId)
      },
      unreadMentionComments = newCommentAndThreadIds.count { case (commentId, _) =>
        participantInfo.mentionCommentIds.contains(commentId)
      },
      unreadParticipatedThreads = participantInfo.participatedThreadIds
        .count { threadId =>
          notifications.exists(_.threadId == threadId)
        },
      assignedToMe = participantInfo.assignments.size
    )
  }

  private def renderButton(notificationData: CommentNotificationData, openModal: Observer[Unit]) = {
    if (notificationData.bubbleStyle == Empty) {
      ButtonL(
        testId = "NewCommentButtonEmpty",
        style = ButtonL.Style.Minimal(icon = Option(Icon.Glyph.Comment)),
        onClick = openModal.contramap(_ => ())
      )()
    } else {
      val color = if (notificationData.bubbleStyle == GrayBadge) {
        Badge.Color.Gray
      } else {
        Badge.Color.Danger
      }
      val theme = if (notificationData.bubbleStyle == GrayBadge) {
        Badge.Theme.Light
      } else {
        Badge.Theme.Bold
      }
      val shouldShowCommentCountInBubble = notificationData.bubbleStyle match {
        case RedBadge | GrayBadge =>
          notificationData.totalRelatedNotificationCount > 0
        case _ => false
      }
      val countOpt = Option.when(shouldShowCommentCountInBubble) {
        notificationData.totalRelatedNotificationCount
      }
      BadgeL(
        color = color,
        theme = theme,
        count = Val(countOpt)
      )(
        ButtonL(
          testId = "NewCommentButtonNonempty",
          style = ButtonL.Style.Minimal(icon = Option(Icon.Glyph.Comment)),
          onClick = openModal.contramap(_ => ())
        )()
      )
    }
  }

  private def renderButtonWithNotificationSummaryTooltip(openModal: Observer[Unit]) = {
    div(
      child <-- commentNotificationDataSignal
        .map { commentNotificationData =>
          TooltipL(
            position = PortalPosition.BottomCenter,
            renderTarget = renderButton(commentNotificationData, openModal),
            renderContent = _.amend(
              backgroundColor := CssVar.toColorVar(CssVar.Color.Gray0),
              color := CssVar.toColorVar(CssVar.Color.Gray7),
              maxWidth := "fit-content",
              borderRadius := "12px",
              FundNotificationSummary(
                unreadGeneralComments = commentNotificationData.unreadGeneralComments,
                unreadMentionComments = commentNotificationData.unreadMentionComments,
                unreadParticipatedThreads = commentNotificationData.unreadParticipatedThreads,
                assignedToMe = commentNotificationData.assignedToMe,
                onOpen = Observer[Unit] { _ =>
                  mountTimeVar.set(DateCalculator.instantNow.toEpochMilli)
                },
                onClose = Observer[Unit] { _ =>
                  unmountEventBus.emit(DateCalculator.instantNow.toEpochMilli)
                }
              )()
            ),
            isDisabled = Val(commentNotificationData.bubbleStyle == Empty)
          )()
        },
      PortalTracker(
        onHasPortalOpened = portalIsOpenedVar.writer
      )()
    )
  }

  def apply(): HtmlElement = {
    InboxEntryPointWrapperL(
      presetLocationOptSignal = Val(None),
      fundSubId = fundId,
      renderChildren = renderProps => {
        div(
          // fetch comment participant data once at mount, every 30s or when notification data updated.
          EventStream
            .delay(1)
            .map(_ => 0)
            .mergeWith(
              EventStream.periodic(30000)
            )
            .mergeWith(
              commentNotificationVar.signal.distinct.changes.map(_ => 0)
            )
            .throttle(2000)
            .flatMapSwitch(_ => fetchCommentParticipantData) --> Observer.empty,
          unmountEventBus.events
            .withCurrentValueOf(mountTimeVar.signal, commentNotificationDataSignal)
            .flatMapSwitch { case (end, start, notificationData) =>
              logDisplayDuration(end - start, notificationData)
            } --> Observer.empty,
          renderProps.commentNotificationSignal
            .map(_.map { noti =>
              // making root comment id
              if (noti.isComment) {
                noti.copy(
                  commentIdOpt = Some(FormCommentUtils.makeCommentId(noti.threadId, noti.commentIdOpt.getOrElse("")))
                )
              } else {
                noti
              }
            })
            .map(_.filterNot(_.seen)) --> commentNotificationVar.writer,
          ModalL(
            size = ModalL.Size(height = ModalL.Height.Full, width = ModalL.Width.Full),
            testId = "FormCommentingGPView",
            renderTarget = open => renderButtonWithNotificationSummaryTooltip(open),
            renderContent = closeModal => {
              renderProps.renderInboxModal(closeModal)
            },
            isClosable = None
          )()
        )
      }
    )()
  }

  private def fetchCommentParticipantData = {
    AirStreamUtils.taskToStream(
      ZIO.when(!document.hidden && !portalIsOpenedVar.now()) {
        FundSubEndpointClient
          .getFundCommentNotification(
            GetFundCommentNotificationParams(fundId)
          )
          .map(
            _.fold(
              _ => Toast.error("Failed to get fund comment notification data"),
              res => commentParticipationDataVar.set(res)
            )
          )
      }
    )
  }

  private def logDisplayDuration(durationMs: Long, commentNotificationData: CommentNotificationData) = {

    AirStreamUtils.taskToStream(
      ActionEventLoggerJs.logEventTask(
        ActionEventViewFundCommentNotificationSummaryUiParams(
          fundId = fundId,
          durationMs = durationMs,
          unreadGeneralComments = commentNotificationData.unreadGeneralComments,
          unreadMentionComments = commentNotificationData.unreadMentionComments,
          unreadParticipatedThreads = commentNotificationData.unreadParticipatedThreads,
          assignedToMe = commentNotificationData.assignedToMe
        )
      )
    )
  }

}
