// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.contact.widget.tab

import anduin.contact.endpoint.{ContactGroupModel, ContactModel}
import anduin.contact.widget.component.{GroupRow, GroupTags}
import anduin.contact.widget.{ContactWidgetModel, ContactWidgetUtils}
import anduin.id.contact.{ContactGroupId, ContactId}
import design.anduin.components.checkbox.Checkbox
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[widget] final case class SelectedTabView(
  isVisible: Boolean,
  selectedContacts: Set[String],
  selectedContactIds: Set[ContactId], // Contact select from group tab
  selectedContactModels: Seq[ContactWidgetModel], // Contact select from contac tab
  contactMap: Map[ContactId, ContactModel],
  groupMap: Map[ContactGroupId, ContactGroupModel],
  onGroupFilterSelected: ContactGroupId => Callback,
  onContactsRemoved: (Set[String], Set[ContactId]) => Callback
) {

  val isSelectionEmpty: Boolean = selectedContactIds.isEmpty && selectedContactModels.isEmpty

  val orderedSelectedGroups: Seq[(ContactGroupId, Set[ContactId])] = selectedContactIds.groupBy(_.group).toSeq.sortBy {
    case (groupId, _) => groupMap.get(groupId).map(_.groupInfo.groupName).getOrElse("")
  }

  val orderedSelectedContacts: Seq[ContactWidgetModel] = selectedContactModels.sortBy(_.key)

  def apply(): VdomElement = SelectedTabView.component(this)

}

private[widget] object SelectedTabView {

  private type Props = SelectedTabView

  def render(props: Props): VdomNode = {
    <.div(
      TagMod.unless(props.isVisible && !props.isSelectionEmpty)(tw.hidden),
      tw.wPc100.flex.flexCol.overflowYAuto,
      renderHeader(props),
      props.orderedSelectedContacts.toVdomArray(
        using { contactModel =>
          renderContactRow(props, contactModel)
        }
      ),
      props.orderedSelectedGroups.toVdomArray(
        using { case (groupId, contacts) =>
          renderGroupRow(
            props,
            groupId,
            contacts
          )
        }
      )
    )
  }

  private def renderHeader(props: Props) = {
    <.div(
      tw.flex.itemsCenter.p8.borderTop.borderBottom.bgGray1.borderGray3,
      Checkbox(
        isChecked = true,
        onChange = { isChecked =>
          if (isChecked) {
            Callback.empty
          } else {
            props.onContactsRemoved(props.selectedContactModels.map(_.key).toSet, props.selectedContactIds)
          }
        }
      )()
    )
  }

  private def renderGroupRow(
    props: Props,
    groupId: ContactGroupId,
    selectedContacts: Set[ContactId]
  ) = {
    props.groupMap.get(groupId).map { group =>
      GroupRow(
        groupId = groupId,
        groupInfo = group.groupInfo,
        displayContacts = ContactWidgetUtils.getContactInfoOrderByEmail(props.contactMap, selectedContacts),
        selectedContacts = selectedContacts,
        onContactsSelected = _ => Callback.empty,
        onContactsRemoved = contacts => props.onContactsRemoved(Set.empty, contacts)
      )()
    }
  }

  private def renderContactRow(props: Props, contact: ContactWidgetModel) = {
    val groups = contact.groupIds
      .flatMap(props.groupMap.get)
      .filter(_.groupInfo.groupName.nonEmpty)
      .sortBy(_.groupInfo.groupName)
    <.div(
      tw.flex.itemsCenter,
      tw.py12.px8.borderBottom.border1.borderGray3,
      Checkbox(
        isChecked = props.selectedContacts.contains(contact.key),
        onChange = { isChecked =>
          if (isChecked) {
            Callback.empty
          } else {
            props.onContactsRemoved(Set(contact.key), Set.empty)
          }
        }
      )(),
      <.div(
        tw.wPx24.hPx24.roundedFull.bgGray3.textGray7,
        tw.flex.itemsCenter.justifyCenter.ml16,
        ^.marginLeft := "64px",
        ^.flexShrink := "0",
        IconR(
          name = Icon.Glyph.UserSingle,
          size = Icon.Size.Custom(12)
        )()
      ),
      <.div(
        ^.width := "300px",
        tw.flex.flexCol.ml8,
        <.div(tw.fontSemiBold, contact.fullName),
        <.div(tw.text11.leading16.textGray7, contact.email)
      ),
      GroupTags(groups = groups, onGroupFilterSelected = props.onGroupFilterSelected)()
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
