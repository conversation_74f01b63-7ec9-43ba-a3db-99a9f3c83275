// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.share

import com.raquo.laminar.api.L.*
import design.anduin.components.avatar.InitialAvatar
import design.anduin.components.avatar.laminar.InitialAvatarL
import design.anduin.components.text.laminar.TruncateL
import design.anduin.components.util.ComponentUtils
import design.anduin.facades.dompurify.DomPurify
import design.anduin.style.tw.*
import anduin.id.fundsub.FundSubLpId
import anduin.user.CurrentUserInfoProvider

private[laminar] case class NewCommentBoxL(
  isBlockedSignal: Signal[Boolean] = Val(false),
  initialComment: String = "",
  isPublicSignal: Signal[Boolean],
  newCommentBoxView: NewCommentBoxL.View = NewCommentBoxL.View.InvestorSideSharedCommentView,
  isCommentMentioningEnabledSignal: Signal[Boolean],
  lpIdSignal: Signal[FundSubLpId],
  onAddComment: Observer[NewCommentBoxL.AddCommentData],
  enableLpAutoNotificationOptSignal: Signal[Option[Boolean]],
  isDrawerViewSignal: Signal[Boolean]
) {

  private val createStatusVar = Var[NewCommentBoxL.CreateStatus](NewCommentBoxL.CreateStatus.Reset)
  private val isSavingVar = Var(false)

  private def addComment(comment: String) = {
    isSavingVar.set(true)
    onAddComment.onNext(
      NewCommentBoxL.AddCommentData(
        comment = DomPurify.sanitize(comment),
        onAdding = Observer[Unit] { _ =>
          createStatusVar.set(NewCommentBoxL.CreateStatus.Adding)
        },
        onAddDone = Observer[Unit] { _ =>
          createStatusVar.set(NewCommentBoxL.CreateStatus.Reset)
          isSavingVar.set(false)
        }
      )
    )
  }

  def apply(): HtmlElement = {
    div(
      ComponentUtils.testIdL(NewCommentBoxL),
      tw.bgGray0,
      child.maybe <-- CurrentUserInfoProvider.getCurrentUserInfoSignal.signal
        .map(_.map { case (userId, userInfo) =>
          div(
            div(
              tw.mb4,
              tw.flex.itemsCenter,
              // Avatar
              div(
                tw.flex.itemsCenter,
                div(
                  ComponentUtils.testIdL(NewCommentBoxL, "AvatarIcon"),
                  tw.mr8,
                  InitialAvatarL(
                    id = userInfo.emailAddressStr,
                    initials = Val(userInfo.getDisplayName.split(" ").take(2).mkString("")),
                    size = InitialAvatar.Size.Px24
                  )()
                ),
                TruncateL(
                  target = {
                    div(
                      ComponentUtils.testIdL(NewCommentBoxL, "DisplayName"),
                      tw.fontSemiBold.flexFill,
                      userInfo.getDisplayName
                    )
                  }
                )()
              )
            ),
            div(
              tw.ml32,
              CommentEditorL(
                currentUserId = userId,
                initialComment = initialComment,
                fundSubLpIdSignal = lpIdSignal,
                isPublicSignal = isPublicSignal,
                showCancelButton = false,
                onSave = Observer[String] { comment => addComment(comment) },
                placeholder = newCommentBoxView.placeholder,
                postButtonLabel = newCommentBoxView.submitButtonText,
                isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
                isBlockedSignal = isBlockedSignal,
                isSavingSignal = isSavingVar.signal,
                enableLpAutoNotificationOptSignal = enableLpAutoNotificationOptSignal,
                isDrawerViewSignal = isDrawerViewSignal
              )()
            )
          )
        })
    )
  }

}

private[comment] object NewCommentBoxL {

  sealed trait CreateStatus derives CanEqual

  object CreateStatus {
    case object Reset extends CreateStatus

    case object Adding extends CreateStatus
  }

  case class AddCommentData(
    comment: String,
    onAdding: Observer[Unit],
    onAddDone: Observer[Unit]
  )

  sealed trait View derives CanEqual {
    val placeholder: String
    val submitButtonText: String
  }

  object View {

    case object InvestorSideSharedCommentView extends View {
      override val placeholder: String = "Add comment"
      override val submitButtonText: String = "Post"
    }

    case object FundSideSharedCommentView extends View {
      override val placeholder: String = "Add comment for investor"
      override val submitButtonText: String = "Share with investor"
    }

    case object FundSideInternalCommentView extends View {
      override val placeholder: String = "Add internal comment"
      override val submitButtonText: String = "Comment internally"
    }

  }

}
