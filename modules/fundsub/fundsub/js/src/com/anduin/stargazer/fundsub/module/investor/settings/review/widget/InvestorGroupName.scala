// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.review.widget

import design.anduin.components.avatar.ColorGenerator
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.id.fundsub.group.FundSubInvestorGroupId

private[settings] final case class InvestorGroupName(
  investorGroupIdOpt: Option[FundSubInvestorGroupId],
  name: String,
  membersCount: Int
) {
  def apply(): VdomElement = InvestorGroupName.component(this)
}

private[settings] object InvestorGroupName {
  private val dotSize = "8px"

  private type Props = InvestorGroupName

  private def render(props: Props) = {
    <.div(
      ComponentUtils.testId(InvestorGroupName),
      tw.flex.itemsCenter,
      <.div(
        tw.inlineBlock.ml4,
        tw.roundedFull,
        props.investorGroupIdOpt.fold[TagMod] {
          tw.borderAll.border2.borderGray6.bgGray0
        } { groupId =>
          ^.backgroundColor := ColorGenerator(groupId.idString)
        },
        ^.width := dotSize,
        ^.height := dotSize,
        ^.top := dotSize,
        ^.right := dotSize
      ),
      <.div(
        ^.maxWidth := "600px",
        tw.fontSemiBold.truncate.overflowHidden.ml12,
        props.name
      ),
      <.div(
        tw.textGray6.ml4,
        s"(${props.membersCount})"
      )
    )
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
