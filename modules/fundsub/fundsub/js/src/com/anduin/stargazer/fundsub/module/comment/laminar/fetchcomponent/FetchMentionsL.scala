// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent

import com.raquo.laminar.api.L.*
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.id.fundsub.FundSubId
import anduin.model.notichannel.FundSubNotificationChannels
import anduin.stargazer.service.formcomment.FormCommentCommons.{GetCommentMentionParams, GetCommentMentionsResponse}
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.notiCenter.NotificationCenter

private[comment] case class FetchMentionsL(
  pageIndexSignal: Signal[Int],
  fundSubId: FundSubId,
  renderChildren: FetchMentionsL.RenderChildren => HtmlElement
) {

  private val fetchMentionsEventBus = new EventBus[Unit]

  private val resultVar = Var(
    FetchMentionsL.Data(
      mentionsResponse = None,
      isFetching = true
    )
  )

  private val issuesTrackingSignal =
    NotificationCenter.eventSignal(FundSubNotificationChannels.fundSubFormComment(fundSubId))

  private def fetchMentions(pageIndex: Int) = {
    AirStreamUtils.taskToStream {
      for {
        _ <- ZIO.when(resultVar.now().mentionsResponse.exists(_.respondedPageIndex != pageIndex)) {
          ZIO.attempt {
            resultVar.update(_.copy(isFetching = true))
          }
        }
        result <- FundSubEndpointClient
          .getCommentMentions(
            GetCommentMentionParams(
              fundId = fundSubId,
              pageIndex = pageIndex,
              itemsPerPage = 10
            )
          )
          .map {
            _.fold(
              _ => {
                FetchMentionsL.Data(
                  mentionsResponse = None,
                  isFetching = false
                )
              },
              response => {
                FetchMentionsL.Data(
                  mentionsResponse = Some(response),
                  isFetching = false
                )
              }
            )
          }
      } yield result
    }
  }

  def apply(): HtmlElement = {
    renderChildren(
      FetchMentionsL.RenderChildren(
        dataSignal = resultVar.signal
      )
    ).amend(
      onMountCallback { _ =>
        NotificationCenter.subscribeSync(Seq(FundSubNotificationChannels.fundSubFormComment(fundSubId)))
      },
      fetchMentionsEventBus.events
        .withCurrentValueOf(
          pageIndexSignal
        )
        .flatMapSwitch(fetchMentions) --> resultVar.writer,
      pageIndexSignal.combineWith(issuesTrackingSignal).distinct --> Observer[(Int, ?)] { case (_, _) =>
        fetchMentionsEventBus.emit(())
      }
    )
  }

}

private[comment] object FetchMentionsL {

  case class Data(
    isFetching: Boolean,
    mentionsResponse: Option[GetCommentMentionsResponse]
  )

  case class RenderChildren(
    dataSignal: Signal[Data]
  )

}
