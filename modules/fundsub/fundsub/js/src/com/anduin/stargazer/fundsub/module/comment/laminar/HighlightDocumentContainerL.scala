// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration
import scala.scalajs.js

import com.raquo.laminar.api.L.*
import design.anduin.components.animate.laminar.JsAnimateL
import design.anduin.facades.dom.animation.{Keyframe, KeyframeBuilder}
import org.scalajs.dom.HTMLElement

private[fundsub] case class HighlightDocumentContainerL(
  duration: FiniteDuration = FiniteDuration(2000, TimeUnit.MILLISECONDS),
  renderChildren: HighlightDocumentContainerL.RenderChildren => HtmlElement,
  onAnimationEnd: Observer[Unit]
) {

  private def highlightAnimation(element: HTMLElement): js.Array[Keyframe] = {
    val _ = element
    js.Array(
      new KeyframeBuilder()
        .withBackground("#FFF")
        .getKeyframe(),
      new KeyframeBuilder()
        .withBackground("rgba(72, 175, 240, 0.1)")
        .getKeyframe()
    )
  }

  private def handleAnimationStart(e: JsAnimateL.OnAnimationStart) = {
    e.element.style.outline = "4px solid #48AFF0"
  }

  private def handleAnimationEnd(e: JsAnimateL.OnAnimationEnd) = {
    e.element.style.outline = ""
    onAnimationEnd.onNext(())
  }

  def apply(): HtmlElement = {
    JsAnimateL(
      animation = highlightAnimation(_),
      duration = duration,
      onAnimationStart = Observer[JsAnimateL.OnAnimationStart](handleAnimationStart),
      onAnimationEnd = Observer[JsAnimateL.OnAnimationEnd](handleAnimationEnd),
      renderChildren = renderChildren
    )()
  }

}

private[fundsub] object HighlightDocumentContainerL {

  type RenderChildren = JsAnimateL.RenderChildren
}
