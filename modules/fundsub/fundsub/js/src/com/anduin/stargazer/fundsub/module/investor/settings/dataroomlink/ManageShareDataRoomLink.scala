// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.dataroomlink

import anduin.id.fundsub.FundSubId
import com.anduin.stargazer.fundsub.module.investor.settings.SettingSwitchTemplate
import design.anduin.components.button.Button
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.clipboard.react.CopyToClipboardR
import design.anduin.components.icon.Icon
import design.anduin.components.modal.Modal
import design.anduin.components.switcher.react.SwitcherR
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.components.wrapper.TargetWrapper
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*

import com.raquo.laminar.api.L.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import design.anduin.components.icon.react.IconR

final case class ManageShareDataRoomLink(
  fundSubId: FundSubId,
  initialSharedDataRoomLink: String,
  initialSharedDataRoomLinkDisplayName: String
) {

  def apply(): VdomElement = ManageShareDataRoomLink.component(this)

}

object ManageShareDataRoomLink {
  private type Props = ManageShareDataRoomLink

  private final case class State(
    sharedDataRoomLink: String,
    sharedDataRoomLinkDisplayName: String
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    private def renderSwitcher(props: Props, state: State) = {
      val dataRoomLinkEnabled = state.sharedDataRoomLink.nonEmpty
      Modal(
        title = s"${if (dataRoomLinkEnabled) "Disable" else "Add"} external website link",
        renderTarget = onOpen =>
          SwitcherR(
            isChecked = dataRoomLinkEnabled,
            onChange = _ => onOpen
          )(),
        renderContent = onClose =>
          if (dataRoomLinkEnabled) {
            ConfirmRemoveDataRoomLinkModal(
              props.fundSubId,
              onDone = scope.modState(
                _.copy(sharedDataRoomLink = "", sharedDataRoomLinkDisplayName = ""),
                onClose
              ),
              onCancel = onClose
            )()
          } else {
            EditDataRoomLinkModal(
              props.fundSubId,
              initialUrl = state.sharedDataRoomLink,
              initialDisplayName = state.sharedDataRoomLinkDisplayName,
              onUpdated = (url, name) =>
                scope.modState(_.copy(sharedDataRoomLink = url, sharedDataRoomLinkDisplayName = name), onClose),
              onCancel = onClose
            )()
          }
      )()
    }

    private def renderLink(props: Props, state: State) = {
      <.div(
        tw.hPx40.flex.itemsCenter.borderAll.rounded8.borderGray3.border1.px12.wFit,
        IconR(
          name = Icon.Glyph.Link
        )(),
        <.div(
          tw.truncate.maxWPx256.mx12,
          if (state.sharedDataRoomLinkDisplayName.trim.isEmpty) {
            state.sharedDataRoomLink
          } else {
            state.sharedDataRoomLinkDisplayName
          }
        ),
        TooltipR(
          renderTarget = Button(
            tpe = Button.Tpe.Link(href = state.sharedDataRoomLink, target = Button.Target.Blank),
            style = Button.Style.Minimal(
              icon = Option(Icon.Glyph.OpenNewWindow),
              height = Button.Height.Fix24
            )
          )(),
          renderContent = _("Open link")
        )(),
        <.div(
          tw.ml4,
          CopyToClipboardR(
            content = state.sharedDataRoomLink,
            renderTarget = _ => {
              div(
                ButtonL(
                  style = ButtonL.Style.Minimal(
                    icon = Option(Icon.Glyph.Duplicate),
                    height = ButtonL.Height.Fix24
                  ),
                  title = "Copy to clipboard"
                )()
              )
            },
            targetWrapper = TargetWrapper.Block
          )()
        ),
        <.div(
          tw.ml4,
          Modal(
            title = "Edit external website link",
            renderTarget = open =>
              TooltipR(
                renderTarget = <.div(
                  ComponentUtils.testId(ManageShareDataRoomLink, "DrLinkEditButton"),
                  Button(
                    style = Button.Style.Minimal(
                      icon = Option(Icon.Glyph.Cog),
                      height = Button.Height.Fix24
                    ),
                    onClick = open
                  )()
                ),
                renderContent = _("Edit link")
              )(),
            renderContent = onClose =>
              EditDataRoomLinkModal(
                props.fundSubId,
                initialUrl = state.sharedDataRoomLink,
                initialDisplayName = state.sharedDataRoomLinkDisplayName,
                onUpdated = (url, name) =>
                  scope.modState(_.copy(sharedDataRoomLink = url, sharedDataRoomLinkDisplayName = name), onClose),
                onCancel = onClose
              )()
          )()
        )
      )
    }

    def render(props: Props, state: State): VdomElement = {
      <.div(
        ComponentUtils.testId(ManageShareDataRoomLink, "DrLinkContainer"),
        SettingSwitchTemplate(
          label = "External website link",
          renderSwitch = () => renderSwitcher(props, state),
          description = () =>
            Option(
              <.div("Share a website link with all investors in the fund. This appears in the right sidebar in their workspace.")
            ),
          renderContent = () =>
            Option.when(state.sharedDataRoomLink.nonEmpty) {
              renderLink(props, state)
            }
        )()
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialStateFromProps { props =>
      State(
        sharedDataRoomLink = props.initialSharedDataRoomLink.trim,
        sharedDataRoomLinkDisplayName = props.initialSharedDataRoomLinkDisplayName.trim
      )
    }
    .renderBackend[Backend]
    .build

}
