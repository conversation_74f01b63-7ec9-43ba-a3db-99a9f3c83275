// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent

import com.raquo.laminar.api.L.*
import design.anduin.components.toast.Toast
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.formcomment.GetCommentThreadParams
import anduin.id.fundsub.{FundSubId, FundSubLpId, IssueLpCompositeId}
import anduin.model.notichannel.FundSubNotificationChannels
import anduin.protobuf.fundsub.comment.TopicData
import anduin.stargazer.service.formcomment.FormCommentCommons.CommentThread
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.notiCenter.NotificationCenter
import com.anduin.stargazer.notiCenter.NotificationCenter.TypedMultiWatch

private[comment] case class FetchSingleThreadL(
  lpId: FundSubLpId,
  topicData: TopicData,
  isPublic: Boolean,
  renderChildren: FetchSingleThreadL.RenderChildren => HtmlElement
) {

  val fundSubId: FundSubId = lpId.parent

  private val fetchCommentsEventBus = new EventBus[Boolean]

  private val mountEventBus = new EventBus[Unit]

  private val refetchCommentsEventBus = new EventBus[Boolean]

  private val isFetchingVar: Var[Boolean] = Var(true)

  private val threadDataOptVar: Var[Option[CommentThread]] = Var(None)

  private val lastUpdatedEventStream =
    NotificationCenter
      .eventStreamMulti[IssueLpCompositeId](FundSubNotificationChannels.fundSubFormComment(fundSubId))
      .filter {
        case watch: TypedMultiWatch.Updated[IssueLpCompositeId] =>
          watch.ids.exists(_.child == lpId)
        case _ => false
      }

  private def fetchComments(setIsFetching: Boolean) = {
    val task = for {
      _ <- ZIO.succeed(isFetchingVar.set(setIsFetching))
      _ <- FundSubEndpointClient
        .getCommentThread(
          GetCommentThreadParams(
            lpId = lpId,
            topicDataOpt = Option(topicData),
            isPublic = isPublic
          )
        )
        .map {
          _.fold(
            _ => {
              isFetchingVar.set(false)
              Toast.error("Failed to fetch comment thread")

            },
            response => {
              Var.set(
                isFetchingVar -> false,
                threadDataOptVar -> response.commentDataOpt
              )
            }
          )
        }
    } yield ()
    AirStreamUtils.taskToStream(task)

  }

  def apply(): HtmlElement = {
    renderChildren(
      FetchSingleThreadL.RenderChildren(
        dataSignal = isFetchingVar.signal.combineWith(threadDataOptVar.signal).map { case (isFetching, threadOpt) =>
          FetchSingleThreadL.Data(
            isFetching = isFetching,
            thread = threadOpt
          )
        },
        refetch = refetchCommentsEventBus.writer
      )
    ).amend(
      fetchCommentsEventBus.events.flatMapSwitch(fetchComments) --> Observer.empty,
      EventStream
        .merge(
          lastUpdatedEventStream.mapTo(false),
          refetchCommentsEventBus.events,
          mountEventBus.events.mapTo(true)
        )
        .throttle(500) --> fetchCommentsEventBus.writer,
      onMountCallback { _ =>
        mountEventBus.emit(())
        NotificationCenter.subscribeSync(Seq(FundSubNotificationChannels.fundSubFormComment(fundSubId)))
      }
    )
  }

}

private[comment] object FetchSingleThreadL {

  case class Data(
    isFetching: Boolean,
    thread: Option[CommentThread]
  )

  case class RenderChildren(dataSignal: Signal[Data], refetch: Observer[Boolean])
}
