// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.comment

import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.endpoint.lp.UpdateInactiveCommentSettingParams
import anduin.id.fundsub.FundSubId
import anduin.protobuf.fundsub.models.InactiveCommentSetting
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.investor.settings.FundSubChildSettingSwitch

private[comment] final case class InactiveCommentSettingSwitch(
  fundId: FundSubId,
  setting: InactiveCommentSetting
) {
  def apply(): VdomElement = InactiveCommentSettingSwitch.component(this)
}

private[comment] object InactiveCommentSettingSwitch {
  private type Props = InactiveCommentSettingSwitch

  private final case class State(
    settingOptimisticUpdate: Option[InactiveCommentSetting] = None,
    isUpdating: Boolean = false
  )

  private val DefaultDuration = 3

  private final case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      val setting = state.settingOptimisticUpdate.getOrElse(props.setting)
      FundSubChildSettingSwitch(
        title = "Show reminders for comments to reply to",
        enabled = setting.enabled,
        isDisabled = state.isUpdating,
        onChange = enabled => onUpdateSetting(setting.copy(enabled = enabled)),
        description = () =>
          <.div(
            <.div(
              tw.textGray7,
              "Comments initiated by investors that might need the fund's response " +
                "will appear at the top of the comments inbox."
            ),
            if (setting.enabled) {
              <.div(
                tw.mt8,
                InactiveDaysThresholdOptions(
                  days = setting.days,
                  useCustomValue = setting.useCustomValue,
                  isDisable = state.isUpdating,
                  onUpdate = (days, useCustomValue) =>
                    onUpdateSetting(
                      setting.copy(
                        days = days,
                        useCustomValue = useCustomValue
                      )
                    )
                )()
              )
            } else {
              EmptyVdom
            }
          )
      )()
    }

    private def refineSetting(setting: InactiveCommentSetting): InactiveCommentSetting = {
      if (setting.enabled) {
        if (setting.days == 0) {
          setting.copy(
            days = DefaultDuration,
            useCustomValue = false
          )
        } else {
          setting
        }
      } else {
        setting.copy(
          days = 0,
          useCustomValue = false
        )
      }
    }

    private def onUpdateSetting(setting: InactiveCommentSetting) = {
      val refinedSetting = refineSetting(setting)
      val cb = for {
        props <- scope.props
        _ <- ZIOUtils.toReactCallback {
          FundSubEndpointClient
            .updateInactiveCommentSetting(
              UpdateInactiveCommentSettingParams(
                props.fundId,
                refinedSetting
              )
            )
            .map(
              _.fold(
                _ =>
                  scope.modState(
                    _.copy(
                      isUpdating = false,
                      settingOptimisticUpdate = None
                    ),
                    Toast.errorCallback("Failed to update comment setting")
                  ),
                _ =>
                  scope.modState(
                    _.copy(
                      isUpdating = false
                    ),
                    Toast.successCallback("Comment setting updated successfully")
                  )
              )
            )
        }
      } yield ()

      scope.modState(
        _.copy(
          settingOptimisticUpdate = Option(refinedSetting),
          isUpdating = true
        ),
        cb
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
