// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.emaillog.data

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import io.circe.Codec

private[emaillog] final case class RecipientInfo(
  email: String,
  firstName: String,
  lastName: String
) {
  lazy val fullName: String = s"$firstName $lastName".trim

  lazy val displayName: String = if (fullName.isEmpty) {
    email
  } else {
    fullName
  }

  lazy val initials: String = firstName.take(1) + lastName.take(1)

}

private[emaillog] object RecipientInfo {
  given Codec.AsObject[RecipientInfo] = deriveCodecWithDefaults
}
