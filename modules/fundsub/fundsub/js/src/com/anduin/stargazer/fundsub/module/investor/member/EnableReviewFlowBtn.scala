// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.member

import design.anduin.components.button.Button
import design.anduin.components.modal.Modal
import design.anduin.components.portal.PortalUtils
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.endpoint.group.FundSubGroupMember
import anduin.fundsub.endpoint.reviewpackage.FundSubReviewPackageData
import anduin.id.fundsub.FundSubId
import com.anduin.stargazer.fundsub.module.investor.reviewpackage.EnableReviewPackageModal

private[investor] case class EnableReviewFlowBtn(
  fundSubId: FundSubId,
  admins: Seq[FundSubGroupMember],
  onUpdate: FundSubReviewPackageData => Callback
) {
  def apply(): VdomElement = EnableReviewFlowBtn.component(this)
}

private[investor] object EnableReviewFlowBtn {
  private type Props = EnableReviewFlowBtn

  private def render(props: Props) = {
    Modal(
      title = "Assign reviewers",
      isClosable = Option(PortalUtils.IsClosable(onEsc = false, onOutsideClick = false)),
      renderTarget = onClick =>
        Button(
          testId = "Enable",
          style = Button.Style.Text(
            color = Button.Color.Primary
          ),
          onClick = onClick
        )("Enable"),
      renderContent = closeModal =>
        EnableReviewPackageModal(
          fundSubId = props.fundSubId,
          adminInfos = props.admins,
          closeModal = closeModal,
          onEnabled = reviewers =>
            props.onUpdate(
              FundSubReviewPackageData(
                isEnabled = true,
                isEnabledUnsignedReviewSetting = false,
                reviewers = reviewers
              )
            )
        )()
    )()
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
