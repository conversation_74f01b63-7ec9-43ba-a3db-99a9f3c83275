// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.link

import java.time.{Instant, LocalDate, ZoneId, ZonedDateTime}

import japgolly.scalajs.react.Reusability

import anduin.model.common.emailaddress.EmailAddress
import anduin.utils.Zoom

private[investor] final case class LinkSettings(
  password: LinkSettings.PasswordSetting,
  expiryDate: LinkSettings.ExpiryDateSetting,
  isDisabled: Boolean,
  whitelistedDomains: LinkSettings.WhitelistedDomainSetting,
  isEnterpriseLoginEnabled: Boolean
) derives CanEqual

private[investor] object LinkSettings extends Zoom[LinkSettings] {

  final case class PasswordSetting(
    password: PasswordSetting.ValueType,
    isEnabled: Boolean,
    initialIsEnabled: Boolean
  )

  object PasswordSetting {

    sealed trait ValueType derives CanEqual

    case object UseExisting extends ValueType

    final case class Change(password: String) extends ValueType
  }

  final case class WhitelistedDomainSetting(
    domains: Seq[String],
    isEnabled: Boolean
  )

  final case class ExpiryDateSetting(
    expiryDate: Instant,
    isEnabled: Boolean
  ) derives CanEqual

  object ExpiryDateSetting {

    def apply(localDate: LocalDate): Instant = {
      ZonedDateTime.of(localDate.atTime(23, 59), ZoneId.systemDefault()).toInstant
    }

    def default: Instant = {
      ExpiryDateSetting(LocalDate.now.plusDays(7))
    }

  }

  def isExpiryValid(linkSettings: LinkSettings): Boolean = {
    !linkSettings.expiryDate.isEnabled ||
    linkSettings.expiryDate.expiryDate.isAfter(ZonedDateTime.now(ZoneId.systemDefault()).toInstant)
  }

  def isPasswordValid(linkSettings: LinkSettings): Boolean = {
    val isPasswordNonEmpty = linkSettings.password.password match {
      case LinkSettings.PasswordSetting.UseExisting      => true
      case LinkSettings.PasswordSetting.Change(password) => password.nonEmpty
    }
    !linkSettings.password.isEnabled || isPasswordNonEmpty
  }

  def areWhitelistedDomainsValid(linkSettings: LinkSettings): Boolean = {
    val domains = linkSettings.whitelistedDomains.domains
    !linkSettings.whitelistedDomains.isEnabled || domains.nonEmpty && domains.forall {
      case EmailAddress.validDomain(_) => true
      case _                           => false
    }
  }

  def isValid(linkSettings: LinkSettings): Boolean = {
    isPasswordValid(linkSettings) &&
    areWhitelistedDomainsValid(linkSettings) &&
    isExpiryValid(linkSettings)
  }

  given linkSettingsReusability: Reusability[LinkSettings] = Reusability.by_==
}
