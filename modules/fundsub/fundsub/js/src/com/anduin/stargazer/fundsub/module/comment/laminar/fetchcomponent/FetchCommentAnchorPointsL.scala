// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent

import com.raquo.laminar.api.L.*
import design.anduin.components.toast.Toast
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.model.notichannel.FundSubNotificationChannels
import anduin.stargazer.service.formcomment.FormCommentCommons.*
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.notiCenter.NotificationCenter

private[laminar] case class FetchCommentAnchorPointsL(
  filterSignal: Signal[GetFundCommentDualThreadsParams],
  renderChildren: Signal[FetchCommentAnchorPointsL.Data] => HtmlElement
) {
  private val RE_FETCH_THROTTLE_MS = 1000

  private val fetchCommentAnchorPointsEventBus = new EventBus[Boolean] // silentLoad
  private val onMountEventStream = new EventBus[Unit]()

  private val issuesTrackingEventStream = filterSignal
    .map(_.fundId)
    .changes
    .flatMapSwitch { fundId =>
      NotificationCenter.eventStream(FundSubNotificationChannels.fundSubFormComment(fundId))
    }

  private val resultVar = Var(
    FetchCommentAnchorPointsL.Data(
      isFetching = true
    )
  )

  def apply(): HtmlElement = {
    renderChildren(resultVar.signal.distinct)
      .amend(
        issuesTrackingEventStream.distinct
          .map(_ => true) --> fetchCommentAnchorPointsEventBus.writer,
        filterSignal.changes
          .map(_ => false) --> fetchCommentAnchorPointsEventBus.writer,
        fetchCommentAnchorPointsEventBus.events
          .throttle(RE_FETCH_THROTTLE_MS)
          .withCurrentValueOf(filterSignal)
          .flatMapSwitch(fetchCommentAnchorPoints) --> resultVar.writer,
        onMountEventStream.events
          .withCurrentValueOf(filterSignal.map(_.fundId).distinct)
          .flatMapSwitch { fundId =>
            AirStreamUtils.taskToStream(
              ZIO.attempt {
                NotificationCenter.subscribeSync(Seq(FundSubNotificationChannels.fundSubFormComment(fundId)))
              }
            )
          } --> Observer.empty,
        onMountCallback { _ =>
          fetchCommentAnchorPointsEventBus.emit(false)
          onMountEventStream.emit(())
        }
      )
  }

  private def fetchCommentAnchorPoints(silentLoad: Boolean, params: GetFundCommentDualThreadsParams) = {
    AirStreamUtils.taskToStream {
      for {
        _ <- ZIO.attempt {
          resultVar.update(_.copy(isFetching = !silentLoad))
        }
        result <- FundSubEndpointClient
          .getFundCommentAnchorPoints(params)
          .map {
            _.fold(
              _ => {
                Toast.error("Failed to load comment!")
                FetchCommentAnchorPointsL.Data(
                  isFetching = false
                )
              },
              response => {
                FetchCommentAnchorPointsL.Data(
                  pageItems = response.pageItems,
                  allPageItemsCount = response.allPageItemsCount,
                  allItemIdsInInboxTab = response.allItemIdsInInboxTab,
                  isFetching = false
                )
              }
            )
          }
      } yield result
    }
  }

}

private[laminar] object FetchCommentAnchorPointsL {

  case class Data(
    isFetching: Boolean,
    pageItems: List[DualCommentThread] = List.empty,
    allPageItemsCount: Int = 0,
    allItemIdsInInboxTab: List[DualCommentThreadIdAndStatus] = List.empty
  )

}
