// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.exporttemplate

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.field.laminar.FieldL
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.style.tw.*

import anduin.dataexport.selfservice.SelfServiceExportTemplatePublicModels.FieldType.PdfFieldType
import anduin.fundsub.endpoint.dataexport.{ExportTemplateFieldIdentifier, SelectedFieldOfSelfServiceExport}
import com.anduin.stargazer.fundsub.module.investor.settings.exporttemplate.EditFieldNamePopoverContent.FieldOptionInfo

private[exporttemplate] case class EditFieldNamePopoverContent(
  fieldId: ExportTemplateFieldIdentifier,
  fieldSignal: Signal[SelectedFieldOfSelfServiceExport],
  selectedFieldsSignal: Signal[Seq[SelectedFieldOfSelfServiceExport]],
  forApiExportSignal: Signal[Boolean],
  onRenameField: Observer[(ExportTemplateFieldIdentifier, String)],
  onRenameFieldOptionNameMap: Observer[(ExportTemplateFieldIdentifier, Map[String, String])],
  onClosePopover: Observer[Unit]
) {
  private val updatedFieldNameVar = Var("")
  private val updatedFieldOptionNameMapVar = Var(Map.empty[String, String])

  private val fieldNameValidationSignal =
    updatedFieldNameVar.signal.combineWith(selectedFieldsSignal, forApiExportSignal).map {
      case (updatedFieldName, allFields, forApiExport) =>
        val duplicateFieldIndex = allFields.indexWhere { field =>
          field.fieldId != fieldId && field.revisedName.trim.equals(updatedFieldName.trim)
        }
        if (forApiExport && duplicateFieldIndex >= 0) {
          FieldL.Validation.Invalid(s"Duplicated with field number $duplicateFieldIndex")
        } else {
          FieldL.Validation.None
        }
    }

  private val shouldRenderFieldOptions: Signal[Boolean] =
    fieldSignal.map(_.fieldType).combineWith(forApiExportSignal).map { case (fieldType, forApiExport) =>
      fieldType match {
        case PdfFieldType.RadioGroup if forApiExport => true
        case _                                       => false
      }
    }

  def apply(): HtmlElement = {
    div(
      fieldSignal.map(_.revisedName).distinct --> updatedFieldNameVar,
      fieldSignal.map(_.revisedOptionNameMap).distinct --> updatedFieldOptionNameMapVar,
      width.px := 304,
      tw.p8,
      child <-- shouldRenderFieldOptions.splitBoolean(
        whenTrue = _ => renderFieldWithOptions,
        whenFalse = _ => renderFieldWithoutOptions
      ),
      div(
        tw.mt8.spaceX8.flex.itemsCenter.justifyEnd,
        ButtonL(
          onClick = onClosePopover.contramap(_ => ()),
          style = ButtonL.Style.Full(color = ButtonL.Color.Gray0, height = ButtonL.Height.Fix24)
        )("Cancel"),
        ButtonL(
          onClick = Observer { _ =>
            onRenameField.onNext(fieldId, updatedFieldNameVar.now().trim)
            onRenameFieldOptionNameMap.onNext(fieldId, updatedFieldOptionNameMapVar.now())
            onClosePopover.onNext(())
          },
          style = ButtonL.Style.Full(color = ButtonL.Color.Primary, height = ButtonL.Height.Fix24)
        )("Save")
      )
    )
  }

  private def renderFieldWithOptions = {
    div(
      tw.spaceY8,
      div(
        tw.fontMedium,
        child.text <-- fieldSignal.map(_.revisedOptionNameMap.size).map { valueCount =>
          s"Fields (${valueCount + 1})"
        }
      ),
      div(
        tw.text11.textGray7,
        child.text <-- fieldSignal.map(_.displayNameOpt.getOrElse(fieldId.value)),
        FieldL(
          validation = fieldNameValidationSignal
        )(
          TextBoxL(
            value = updatedFieldNameVar.signal,
            onChange = updatedFieldNameVar.writer
          )()
        )
      ),
      children <-- updatedFieldOptionNameMapVar.signal
        .combineWith(fieldSignal.map(_.displayOptionNameMap))
        .map { case (updatedFieldOptionNameMap, displayNameMap) =>
          updatedFieldOptionNameMap.map { case (rawName, updatedName) =>
            val displayName = displayNameMap.getOrElse(rawName, rawName)
            val duplicateValueOpt = updatedFieldOptionNameMap.collectFirst {
              case (duplicateValueRawName, duplicateValueUpdatedName)
                  if duplicateValueRawName != rawName && duplicateValueUpdatedName.trim.equals(updatedName.trim) =>
                displayNameMap.getOrElse(duplicateValueRawName, duplicateValueRawName)
            }
            FieldOptionInfo(
              rawName = rawName,
              displayName = displayName,
              updatedName = updatedName,
              validation = duplicateValueOpt.fold(FieldL.Validation.None) { duplicateValue =>
                FieldL.Validation.Invalid(s"Duplicated with field number $duplicateValue")
              }
            )
          }.toSeq
        }
        .split(_.rawName) { case (rawName, _, fieldOptionInfoSignal) =>
          div(
            tw.text11.textGray7,
            child.text <-- fieldOptionInfoSignal.map(_.displayName),
            FieldL(
              validation = fieldOptionInfoSignal.map(_.validation)
            )(
              TextBoxL(
                value = fieldOptionInfoSignal.map(_.updatedName),
                onChange = Observer { updatedName =>
                  updatedFieldOptionNameMapVar.update(_.updated(rawName, updatedName.trim))
                }
              )()
            )
          )
        }
    )
  }

  private def renderFieldWithoutOptions = {
    FieldL(
      label = Some("Field name"),
      validation = fieldNameValidationSignal
    )(
      TextBoxL(
        value = updatedFieldNameVar.signal,
        onChange = updatedFieldNameVar.writer
      )()
    )
  }

}

private[exporttemplate] object EditFieldNamePopoverContent {

  final case class FieldOptionInfo(
    rawName: String,
    displayName: String,
    updatedName: String,
    validation: FieldL.Validation
  )

}
