// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.homepage

import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.vdom.html_<^.*

import stargazer.model.routing.{DynamicAuthPage, Page}

private[fundsub] final case class FundSubHomePageEntryPoint(
  page: DynamicAuthPage.FundSubHomePage,
  params: FundSubHomePageEntryPoint.FundSubHomePageQueryParams,
  router: RouterCtl[Page]
) {

  def apply(): VdomElement = FundSubHomePageEntryPoint.component(this)

}

private[fundsub] object FundSubHomePageEntryPoint {

  final case class FundSubHomePageQueryParams(
    currentTabOpt: Option[FundSubHomePage.Tab],
    sortAndFilterOptions: FundSubHomePage.FundAndInvestmentSortAndFilterOptions
  ) {

    def toParams: Map[String, String] = {
      currentTabOpt.fold(Map.empty[String, String]) { currentTab =>
        val (tabStr, sortAndFilterParams) = currentTab match {
          case FundSubHomePage.Tab.Funds       => ("funds", sortAndFilterOptions.fundSortAndFilterOption)
          case FundSubHomePage.Tab.Investments => ("investments", sortAndFilterOptions.investmentSortAndFilterOption)
        }

        Map(
          "tab" -> tabStr,
          "key" -> sortAndFilterParams.searchKey,
          "sort" -> {
            sortAndFilterParams.sortBy match {
              case FundSubHomePage.SortOption.Alphabetically     => "alphabetically"
              case FundSubHomePage.SortOption.MostRecentActivity => "recent"
              case FundSubHomePage.SortOption.CreatedDate        => "createdDate"
            }
          },
          "closed" -> { if (sortAndFilterParams.showFundClosed) "1" else "0" }
        )
      }
    }

  }

  object FundSubHomePageQueryParams {

    def fromParams(params: Map[String, String]): FundSubHomePageQueryParams = {
      val tabOpt = params.getOrElse("tab", "funds") match {
        case "investments" => Some(FundSubHomePage.Tab.Investments)
        case "funds"       => Some(FundSubHomePage.Tab.Funds)
        case _             => None
      }

      val sortAndFilterOptions = tabOpt.fold(FundSubHomePage.FundAndInvestmentSortAndFilterOptions()) { tab =>
        val searchKey = params.getOrElse("key", "")

        val sortOption = params.getOrElse("sort", "alphabetically") match {
          case "recent"      => FundSubHomePage.SortOption.MostRecentActivity
          case "createdDate" => FundSubHomePage.SortOption.CreatedDate
          case _             => FundSubHomePage.SortOption.Alphabetically
        }

        val showFundClosed = params.getOrElse("closed", "1") == "1"

        val options = FundSubHomePage.SortAndFilterOption(
          searchKey,
          sortOption,
          showFundClosed
        )

        tab match {
          case FundSubHomePage.Tab.Funds =>
            FundSubHomePage.FundAndInvestmentSortAndFilterOptions(fundSortAndFilterOption = options)
          case FundSubHomePage.Tab.Investments =>
            FundSubHomePage.FundAndInvestmentSortAndFilterOptions(investmentSortAndFilterOption = options)
        }
      }

      FundSubHomePageQueryParams(tabOpt, sortAndFilterOptions)
    }

  }

  private type Props = FundSubHomePageEntryPoint

  private final case class State(
    params: FundSubHomePageQueryParams
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    private def updateRouter = {
      for {
        props <- scope.props
        state <- scope.state
        _ <- props.router.set(props.page.copy(params = state.params.toParams))
      } yield ()
    }

    def render(state: State): VdomNode = {
      FundSubHomePage(
        currentTabOpt = state.params.currentTabOpt,
        onChangeTab = tab => {
          scope.modState(
            state => state.copy(params = state.params.copy(currentTabOpt = Some(tab))),
            updateRouter
          )
        },
        sortAndFilterOptions = state.params.sortAndFilterOptions,
        onChangeSortAndFilterOptions = {
          case FundSubHomePage.OnChangeSortAndFilterOptionsParams(optionsUpdateFn, onDone) => {
            scope.modState(
              state =>
                state.copy(params =
                  state.params.copy(sortAndFilterOptions = optionsUpdateFn(state.params.sortAndFilterOptions))
                ),
              updateRouter >> onDone
            )
          }
        }
      )()
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialStateFromProps(props => State(props.params))
    .renderBackend[Backend]
    .build

}
