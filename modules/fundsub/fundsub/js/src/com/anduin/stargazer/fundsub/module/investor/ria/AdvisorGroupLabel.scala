// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.ria

import com.raquo.laminar.api.L.*
import design.anduin.components.icon.Icon
import design.anduin.components.tag.laminar.TagL

private[investor] final case class AdvisorGroupLabel(
  advisorGroupName: String
) {

  def apply(): HtmlElement = {
    TagL(
      label = Val(advisorGroupName),
      icon = Some(Icon.Glyph.UserCompany)
    )()
  }

}
