// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.inbox

import com.raquo.laminar.api.L.*
import design.anduin.components.modal.laminar.ModalL

import anduin.id.fundsub.FundSubId
import com.anduin.stargazer.fundsub.module.comment.models.InboxLocationParams

private[fundsub] final case class CommentInboxModalBodyL(
  fundId: FundSubId,
  presetLocationOptSignal: Signal[Option[InboxLocationParams]],
  onClose: Observer[Unit]
) {

  def apply(): HtmlElement = {
    InboxEntryPointWrapperL(
      presetLocationOptSignal = presetLocationOptSignal,
      fundSubId = fundId,
      renderChildren = renderProps => {
        ModalL(
          defaultIsOpened = true,
          size = ModalL.Size(height = ModalL.Height.Full, width = ModalL.Width.Full),
          testId = "FormCommentingGPView",
          renderContent = closeModal => {
            renderProps.renderInboxModal(Observer[Unit] { _ =>
              closeModal.onNext(())
              onClose.onNext(())
            })
          },
          isClosable = None
        )()
      }
    )()
  }

}
