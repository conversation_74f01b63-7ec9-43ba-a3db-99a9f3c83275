// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.homepage

import design.anduin.components.button.Button
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.util.EmailAddressConstants
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils

private[homepage] final case class FundSubHomePageGetStarted() {
  def apply(): VdomElement = FundSubHomePageGetStarted.component(this)
}

private[homepage] object FundSubHomePageGetStarted {

  private type Props = FundSubHomePageGetStarted

  private def render: VdomElement = {
    <.div(
      tw.bgGray0.flex.flexCol.itemsCenter.pt32.pb40.mb40.shadow3.rounded8,
      <.h2(tw.mb20, "To start a new fund:"),
      renderSteps,
      <.div(
        tw.mt24,
        But<PERSON>(
          style = Button.Style.Full(color = Button.Color.Primary, isFullWidth = true),
          tpe = Button.Tpe.Link(href = s"mailto:${EmailAddressConstants.SupportEmailAddress}")
        )("Send us an email")
      ),
      <.div(tw.textGray6.mt32, "Want to try out our product?"),
      <.div(
        Button(
          style = Button.Style.Text(),
          tpe = Button.Tpe.Link(href = "https://demo.anduin.app/", target = Button.Target.Blank)
        )(
          "Run the simulator"
        )
      )
    )
  }

  private def renderSteps: VdomElement = {
    <.div(
      tw.mt32,
      tw.sm(tw.flex),
      renderSingleStep(
        "files.svg",
        1,
        "Contact us and send the requirements for your fund."
      ),
      ArrowStep()(),
      renderSingleStep(
        "form.svg",
        2,
        s"We process your ${FundSubCopyUtils.getFlowTerm.standAloneTerm}s and turn them into smart forms."
      ),
      ArrowStep()(),
      renderSingleStep(
        "invite_multiple.svg",
        3,
        "Invite your investors to complete the smart form and sign the LPA."
      )
    )
  }

  private def renderSingleStep(iconName: String, stepIdx: Int, description: String): VdomElement = {
    <.div(
      tw.flexFill.flex.flexCol.itemsCenter.px24,
      <.img(
        ^.width := "96px",
        ^.height := "96px",
        ^.src := s"/web/gondor/images/fundsub/$iconName"
      ),
      <.div(tw.textPrimary4.fontSemiBold.mt24.mb8, s"STEP $stepIdx"),
      <.div(tw.textCenter, description)
    )
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_(render)
    .build

}
