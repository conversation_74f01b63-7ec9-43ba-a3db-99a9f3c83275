// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.emailtemplate

import anduin.fundsub.endpoint.emailtemplate.{FundSubEmailTemplate, FundSubEmailTemplateCollection}
import anduin.fundsub.endpoint.lp.UserEmailTemplate
import anduin.id.fundsub.FundSubEmailTemplateId
import anduin.utils.DateTimeUtils
import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.menu.laminar.{MenuItemL, MenuL}
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.tag.laminar.TagL
import design.anduin.components.text.laminar.TruncateL
import design.anduin.style.tw.*
import design.anduin.components.portal.PortalPosition
import anduin.fundsub.endpoint.emailtemplate.AnduinTemplate

private[emailtemplate] final case class ManageFundSubEmailTemplateCollection(
  collection: FundSubEmailTemplateCollection,
  onChange: Observer[FundSubEmailTemplateCollection] = Observer.empty
) {

  private val collectionVar = Var(collection)
  private val defaultTemplateSignal = collectionVar.signal.map(_.defaultTemplateId)
  private val templateNamesSignal = collectionVar.signal.map(_.templates.map(_.name))

  def apply(): HtmlElement = {
    div(
      collectionVar.signal --> onChange,
      tw.spaceY16,
      renderCreateTemplateButton(),
      div(
        children <-- collectionVar.signal.map(_.sortedByAlphabet).split(_.id) { case (_, _, templateSignal) =>
          div(
            child <-- templateSignal
              .combineWith(defaultTemplateSignal)
              .map { case (template, defaultTemplateIdOpt) =>
                (template, defaultTemplateIdOpt.contains(template.id))
              }
              .map(renderTemplate)
          )
        },
        child <-- defaultTemplateSignal
          .combineWith(collectionVar.signal)
          .map { case (defaultTemplate, collection) =>
            (defaultTemplate.isEmpty, collection.anduinTemplate)
          }
          .map(renderAnduinTemplate)
      )
    )
  }

  private def renderCreateTemplateButton() = {
    EditEmailTemplateModal(
      fundSubId = collection.fundSubId,
      emailType = collection.emailType,
      templateNamesSignal = templateNamesSignal,
      initialIsDefault = false,
      renderTarget = open =>
        ButtonL(
          style = ButtonL.Style.Full(icon = Option(Icon.Glyph.Plus)),
          onClick = open.contramap(_ => ())
        )("Create template"),
      onComplete = onTemplateCreated
    )()
  }

  private def renderTemplate(template: FundSubEmailTemplate, isDefault: Boolean) = {
    div(
      tw.hPx48.flex.itemsCenter.spaceX16.borderTop.borderGray3,
      div(
        tw.flexFill.flex.spaceX8.itemsCenter,
        div(
          tw.textGray7,
          IconL(name = Val(Icon.Glyph.FileText))()
        ),
        div(
          tw.flexFill.flex.spaceX8,
          TruncateL(
            target = span(template.name),
            title = Option(template.name)
          )(),
          Option.when(isDefault) {
            div(TagL(label = Val("Default"))())
          }
        )
      ),
      div(
        tw.spaceX16.flex.itemsCenter,
        template.updatedAt.fold(
          div(
            tw.textGray7,
            template.createdAt.map { createdAt =>
              s"Created on ${DateTimeUtils.formatInstant(createdAt, DateTimeUtils.DateAndTimeFormatter2)(
                  using DateTimeUtils.defaultTimezone
                )}"
            }
          )
        ) { updatedAt =>
          div(
            tw.textGray7,
            s"Updated on ${DateTimeUtils.formatInstant(updatedAt, DateTimeUtils.DateAndTimeFormatter2)(
                using DateTimeUtils.defaultTimezone
              )}"
          )
        },
        PopoverL(
          position = PortalPosition.BottomRight,
          renderTarget = (open, _) =>
            ButtonL(
              style = ButtonL.Style.Minimal(icon = Option(Icon.Glyph.EllipsisHorizontal)),
              onClick = open.contramap(_ => ())
            )(),
          renderContent = close =>
            MenuL(
              Seq(
                EditEmailTemplateModal(
                  fundSubId = collection.fundSubId,
                  emailType = collection.emailType,
                  templateNamesSignal = templateNamesSignal,
                  action = EditEmailTemplateModal.ActionType.Edit(template),
                  initialIsDefault = isDefault,
                  renderTarget = open =>
                    MenuItemL(
                      icon = Option(Icon.Glyph.Edit),
                      onClick = open
                    )("Edit template"),
                  onClose = close,
                  onComplete = onTemplateUpdated
                )(),
                ConfirmDeleteEmailTemplateModal(
                  renderTarget = open =>
                    MenuItemL(
                      icon = Option(Icon.Glyph.Trash),
                      color = MenuItemL.ColorDanger,
                      onClick = open
                    )("Delete template"),
                  template = template,
                  emailType = collection.emailType,
                  onClose = close,
                  onComplete = onTemplateDeleted
                )()
              )
            )
        )()
      )
    )
  }

  private def renderAnduinTemplate(
    isDefault: Boolean,
    template: UserEmailTemplate
  ) = {
    div(
      tw.hPx48.flex.itemsCenter.spaceX16.borderTop.borderBottom.borderGray3,
      div(
        tw.flexFill.flex.spaceX8.itemsCenter,
        div(
          tw.textGray7,
          IconL(name = Val(Icon.Glyph.FileText))()
        ),
        div(
          tw.flexFill.flex.spaceX8.itemsCenter,
          TruncateL(
            target = span(AnduinTemplate.AnduinTemplateName),
            title = Option(AnduinTemplate.AnduinTemplateName)
          )(),
          Option.when(isDefault) {
            div(TagL(label = Val("Default"))())
          }
        )
      ),
      div(
        tw.spaceX16.flex.itemsCenter,
        template.lastEditedAt.map { lastEditedAt =>
          div(
            tw.textGray7.flex,
            s"Updated on ${DateTimeUtils.formatInstant(lastEditedAt, DateTimeUtils.DateAndTimeFormatter2)(
                using DateTimeUtils.defaultTimezone
              )}"
          )
        },
        PopoverL(
          position = PortalPosition.BottomRight,
          renderTarget = (open, _) =>
            ButtonL(
              style = ButtonL.Style.Minimal(icon = Option(Icon.Glyph.EllipsisHorizontal)),
              onClick = open.contramap(_ => ())
            )(),
          renderContent = close =>
            MenuL(
              Seq(
                div(
                  child <-- templateNamesSignal.map { templateNames =>
                    EditAnduinEmailTemplateModal(
                      fundSubId = collection.fundSubId,
                      emailType = collection.emailType,
                      initialTemplate = template,
                      initialIsDefault = isDefault,
                      renderTarget = open =>
                        MenuItemL(
                          icon = Option(Icon.Glyph.Edit),
                          onClick = open
                        )("Edit template"),
                      onClose = close,
                      onComplete = onAnduinTemplateUpdated
                    )()
                  }
                )
              )
            )
        )()
      )
    )
  }

  private val onTemplateCreated = Observer[EditEmailTemplateModal.OnComplete] { onComplete =>
    collectionVar.update { currCollection =>
      currCollection.copy(
        defaultTemplateId = if (onComplete.isDefault) {
          Option(onComplete.template.id)
        } else {
          currCollection.defaultTemplateId
        },
        templates = currCollection.templates :+ onComplete.template
      )
    }
  }

  private val onTemplateDeleted = Observer[FundSubEmailTemplateId] { id =>
    collectionVar.update { currCollection =>
      currCollection.copy(
        defaultTemplateId = if (currCollection.defaultTemplateId.contains(id)) {
          None
        } else {
          currCollection.defaultTemplateId
        },
        templates = currCollection.templates.filterNot(_.id == id)
      )
    }
  }

  private val onTemplateUpdated = Observer[EditEmailTemplateModal.OnComplete] { onComplete =>
    collectionVar.update { currCollection =>
      currCollection.copy(
        defaultTemplateId = if (onComplete.isDefault) {
          Option(onComplete.template.id)
        } else if (currCollection.defaultTemplateId.contains(onComplete.template.id)) {
          None
        } else {
          currCollection.defaultTemplateId
        },
        templates = currCollection.templates.map { template =>
          if (template.id == onComplete.template.id) {
            onComplete.template
          } else {
            template
          }
        }
      )
    }
  }

  private val onAnduinTemplateUpdated = Observer[EditAnduinEmailTemplateModal.OnComplete] { onComplete =>
    collectionVar.update { currCollection =>
      currCollection.copy(
        defaultTemplateId = if (onComplete.isDefault) {
          None
        } else {
          currCollection.defaultTemplateId
        },
        anduinTemplate = onComplete.template
      )
    }
  }

}
