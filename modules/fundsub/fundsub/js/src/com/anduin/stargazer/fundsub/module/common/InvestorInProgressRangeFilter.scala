// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.common

import design.anduin.components.slider.RangeSlider
import design.anduin.components.slider.react.RangeSliderR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[module] final case class InvestorInProgressRangeFilter(
  range: (Int, Int),
  onChange: ((Int, Int)) => Callback
) {
  def apply(): VdomElement = InvestorInProgressRangeFilter.component(this)
}

private[module] object InvestorInProgressRangeFilter {

  private type Props = InvestorInProgressRangeFilter

  private def render(props: Props): VdomElement = {
    val range = RangeSlider.RangeValue(props.range._1, props.range._2)
    val normalizedRange = range.normalize()
    <.div(
      tw.px16.py12.borderTop.borderGray3.border1,
      tw.flex.itemsCenter.justifyBetween,
      <.div(
        <.div(tw.fontSemiBold.leading20, "Form progress"),
        <.div(tw.leading20.textGray7, s"${normalizedRange.lower}%-${normalizedRange.upper}%")
      ),
      <.div(
        ^.width := "280px",
        RangeSliderR(
          value = range,
          onChange = value => props.onChange(value.lower -> value.upper)
        )()
      )
    )
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
