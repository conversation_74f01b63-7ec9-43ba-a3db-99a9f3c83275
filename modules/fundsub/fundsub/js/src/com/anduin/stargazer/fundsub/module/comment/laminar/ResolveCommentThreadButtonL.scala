package com.anduin.stargazer.fundsub.module.comment.laminar

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.TooltipL
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.formcomment.ResolveCommentParams
import anduin.id.issuetracker.IssueId
import anduin.protobuf.actionlogger.formcomment.FormCommentViewSource
import anduin.stargazer.service.formcomment.FormCommentCommons.{CommentThread, PendingThreadDefaultId}
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.comment.laminar.ResolveCommentThreadButtonL.ButtonType

case class ResolveCommentThreadButtonL(
  threadSignal: Signal[CommentThread],
  onResolveThread: Observer[Unit],
  viewSource: FormCommentViewSource,
  buttonType: ResolveCommentThreadButtonL.ButtonType
) {
  private val resolveThreadEventBus = new EventBus[Unit]
  private val isResolvingThreadVar: Var[Boolean] = Var(false)

  private val isPendingThreadSignal = threadSignal.map(_.id == PendingThreadDefaultId)

  def apply(): HtmlElement = {
    TooltipL(
      renderTarget = ButtonL(
        testId = "ResolveButton",
        style = ButtonL.Style.Full(
          height = ButtonL.Height.Fix32,
          isBusy = isResolvingThreadVar.signal.distinct,
          icon = Some(Icon.Glyph.CheckCircleLine)
        ),
        isDisabled = isPendingThreadSignal,
        onClick = resolveThreadEventBus.writer.contramap(_ => ()),
        hasChildren = buttonType == ButtonType.IconWithText
      )(
        buttonType match {
          case ButtonType.IconWithText => "Mark as resolved"
          case ButtonType.Icon         => emptyMod
        }
      ).amend(
        resolveThreadEventBus.events
          .withCurrentValueOf(threadSignal.map(_.id))
          .flatMapSwitch(handleResolve) --> Observer.empty
      ),
      renderContent = _.amend("Mark as resolved"),
      isDisabled = Val(buttonType == ButtonType.IconWithText)
    )()

  }

  private def handleResolve(threadId: IssueId) = {
    AirStreamUtils.taskToStream {
      for {
        _ <- ZIO.succeed(isResolvingThreadVar.set(true))
        resp <- FundSubEndpointClient.resolveComment(
          ResolveCommentParams(
            threadId,
            viewSource
          )
        )
      } yield {
        resp.fold(
          _ => {
            Toast.error("Failed to resolve thread, please try again")
            isResolvingThreadVar.set(false)
          },
          _ => {
            Toast.success("Thread marked as resolved successfully")
            isResolvingThreadVar.set(false)
            onResolveThread.onNext(())
          }
        )
      }
    }
  }

}

object ResolveCommentThreadButtonL {

  enum ButtonType {
    case Icon, IconWithText
  }

}
