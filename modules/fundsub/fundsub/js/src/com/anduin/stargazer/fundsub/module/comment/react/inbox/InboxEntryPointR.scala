// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.react.inbox

import design.anduin.components.wrapper.react.WrapperR
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.id.fundsub.FundSubId
import com.anduin.stargazer.fundsub.module.comment.laminar.inbox.NewInboxEntryPointL

private[fundsub] case class InboxEntryPointR(
  fundSubId: FundSubId
) {

  def apply(): VdomElement = InboxEntryPointR.component(this)
}

private[fundsub] object InboxEntryPointR {

  private type Props = InboxEntryPointR

  private def render(props: Props) = {
    WrapperR(
      NewInboxEntryPointL(
        fundId = props.fundSubId
      )()
    )()
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
