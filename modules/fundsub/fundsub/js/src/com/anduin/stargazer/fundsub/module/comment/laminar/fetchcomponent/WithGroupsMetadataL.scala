// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent

import com.raquo.laminar.api.L.*
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.group.GetGroupsMetadataResponse
import anduin.id.fundsub.FundSubId
import com.anduin.stargazer.fundsub.client.FundSubGroupEndpointClient

private[laminar] case class WithGroupsMetadataL(
  fundSubId: FundSubId,
  renderChildren: WithGroupsMetadataL.RenderChildren => HtmlElement
) {

  private val fetchGroupMetadataEventBus = new EventBus[Unit]

  private val isFetchingVar: Var[Boolean] = Var(false)

  private val groupMetadataResOptVar: Var[Option[GetGroupsMetadataResponse]] = Var(None)

  private def fetchGroupMetadata = {
    AirStreamUtils.taskToStream {
      for {
        _ <- ZIO.succeed(isFetchingVar.set(true))
        res <-
          FundSubGroupEndpointClient
            .getGroupsMetadata(fundSubId)
            .map {
              _.fold(
                _ => isFetchingVar.set(false),
                response => {
                  Var.set(
                    groupMetadataResOptVar -> Some(response),
                    isFetchingVar -> false
                  )
                }
              )
            }
      } yield res
    }
  }

  def apply(): HtmlElement = {
    renderChildren(
      WithGroupsMetadataL.RenderChildren(
        dataSignal =
          isFetchingVar.signal.combineWith(groupMetadataResOptVar.signal).map { case (isFetching, groupMetadataOpt) =>
            WithGroupsMetadataL.Data(
              isFetching = isFetching,
              groupMetadataResOpt = groupMetadataOpt
            )
          }
      )
    ).amend(
      fetchGroupMetadataEventBus.events
        .flatMapSwitch(_ => fetchGroupMetadata) --> Observer.empty,
      onMountCallback { _ =>
        fetchGroupMetadataEventBus.emit(())
      }
    )
  }

}

private[laminar] object WithGroupsMetadataL {

  case class Data(
    isFetching: Boolean,
    groupMetadataResOpt: Option[GetGroupsMetadataResponse]
  )

  case class RenderChildren(dataSignal: Signal[Data])
}
