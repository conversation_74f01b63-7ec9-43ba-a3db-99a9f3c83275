// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.notify

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.checkbox.CheckboxL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.modal.laminar.{ModalFooterL, ModalHeaderL}
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.progress.laminar.BlockIndicatorL
import design.anduin.style.tw.*
import org.scalajs.dom

import anduin.id.fundsub.FundSubLpId
import anduin.id.issuetracker.IssueId
import anduin.stargazer.service.formcomment.FormCommentCommons.CommentThreadWithMetaData
import com.anduin.stargazer.fundsub.module.comment.models.CommentNotificationSetting

private[notify] case class SelectCommentsStepL(
  commentNotificationSettingSignal: Signal[Option[CommentNotificationSetting]],
  isCommentMentioningEnabledSignal: Signal[Boolean],
  excludeChooseInvestorStep: Boolean,
  fundSubLpId: FundSubLpId,
  selectedThreadIds: Set[IssueId],
  onClose: Observer[Unit],
  onGoToNextStep: Observer[SelectCommentsStepL.OnNextData],
  onGoToPrevStep: Observer[Unit]
) {

  private val selectedThreadsVar = Var(selectedThreadIds)
  private val selectedThreadVar = Var(Option.empty[IssueId])
  private val entitySignal = commentNotificationSettingSignal.map(_.map(_.investorEntity)).distinct

  private def handleToggleThread(issueId: IssueId, select: Boolean) = {
    selectedThreadsVar.update { threads =>
      if (select) {
        threads + issueId
      } else {
        threads.filterNot(_ == issueId)
      }
    }
  }

  private def handleToggleAllThreads(threads: List[CommentThreadWithMetaData], selectAll: Boolean) = {
    val ids = threads.map(_.commentThread.id)
    val newSelectedIds = if (selectAll) {
      ids
    } else {
      List.empty
    }
    selectedThreadsVar.set(newSelectedIds.toSet)
  }

  private def renderThreads(
    threads: Option[List[CommentThreadWithMetaData]]
  ) = {
    threads.fold {
      BlockIndicatorL()()
    } { threads =>
      if (threads.isEmpty) {
        NonIdealStateL(
          icon = {
            div(
              tw.textGray4,
              IconL(name = Val(Icon.Glyph.Comment), size = Icon.Size.Custom(48))()
            )
          },
          description = "There are no comments yet"
        )()
      } else {
        div(
          tw.hPc100.overflowAuto,
          threads.map { thread =>
            div(
              tw.borderBottom.borderGray3.hover(tw.bgGray1),
              tw.cursorPointer.py16.px24,
              tw.flex.itemsCenter,
              CheckboxL(
                isChecked = selectedThreadsVar.signal.map(_.contains(thread.commentThread.id)),
                onChange = Observer[Boolean](handleToggleThread(thread.commentThread.id, _))
              )(),
              OpenThreadItemL(
                thread = thread.commentThread
              )().amend(
                tw.flexFill.ml16,
                onClick --> Observer[dom.MouseEvent] { _ =>
                  selectedThreadVar.set(Option(thread.commentThread.id))
                }
              )
            )
          }
        )
      }
    }
  }

  private def renderFooter(threads: List[CommentThreadWithMetaData]) = {
    ModalFooterL(
      div(
        tw.pt24.flex.itemsCenter,
        Option.unless(excludeChooseInvestorStep) {
          ButtonL(
            style = ButtonL.Style.Full(),
            onClick = Observer[dom.MouseEvent] { _ =>
              onGoToPrevStep.onNext(())
            }
          )("Back")
        },
        div(
          tw.mlAuto,
          ButtonL(
            isDisabled = selectedThreadsVar.signal.map(_.isEmpty),
            style = ButtonL.Style.Full(color = ButtonL.Color.Primary),
            onClick = Observer[dom.MouseEvent] { _ =>
              val selectedThreadIds = selectedThreadsVar.now()
              val selectedThreads = threads.filter { thread =>
                selectedThreadIds.contains(thread.commentThread.id)
              }
              onGoToNextStep.onNext(
                SelectCommentsStepL.OnNextData(
                  selectedThreadIds = selectedThreadIds,
                  selectedThreads = selectedThreads
                )
              )
            }
          )("Next")
        )
      )
    )
  }

  def apply(): HtmlElement = {
    FetchSharedAndOpenCommentsL(
      commentNotificationSettingSignal = commentNotificationSettingSignal,
      fundSubId = fundSubLpId.parent,
      fundSubLpId = Option(fundSubLpId),
      preselectedLp = Option(fundSubLpId),
      renderChildren = renderProps => {
        val openThreads = renderProps.map(_.threads)
        val threads = openThreads.getOrElse(List.empty).filter(_.commentThread.fundSubLpId == fundSubLpId)
        val allItemsSelectedSignal = selectedThreadsVar.signal.map { selectedThreadIds =>
          threads.forall { formCommentThread =>
            selectedThreadIds.contains(formCommentThread.commentThread.id)
          }
        }
        val hasSelectedItemsSignal = selectedThreadsVar.signal.map { selectedThreadIds =>
          threads.exists { formCommentThread =>
            selectedThreadIds.contains(formCommentThread.commentThread.id)
          }
        }
        val isIndeterminateSignal = hasSelectedItemsSignal.combineWith(allItemsSelectedSignal).distinct.mapN {
          case (hasSelectedItems, allItemsSelected) =>
            hasSelectedItems && !allItemsSelected
        }
        val selectedThreadSignal = selectedThreadVar.signal.map { selectedThread =>
          threads
            .find { commentThread =>
              selectedThread.contains(commentThread.commentThread.id)
            }
        }

        div(
          width := "960px",
          ModalHeaderL(
            title = "Choose open comments to notify the investor",
            close = onClose
          )(),
          div(
            tw.py8.px24.mt20,
            tw.bgGray1.borderBottom.borderTop.borderGray3.border1,
            tw.text13.fontSemiBold.leading20.textGray7,
            tw.flex,
            div(
              tw.flexFill.flex.itemsCenter,
              CheckboxL(
                isChecked = allItemsSelectedSignal,
                isIndeterminate = isIndeterminateSignal,
                onChange = Observer[Boolean](handleToggleAllThreads(threads, _))
              )(),
              div(
                tw.ml16,
                "Comments"
              )
            ),
            div(tw.bgGray3.wPx1.hPx20),
            div(
              tw.flexFill.flex.itemsCenter,
              div(
                tw.ml16,
                "Detail"
              )
            )
          ),
          div(
            tw.flex,
            tw.borderBottom.borderGray3.border1,
            height := "436px",
            // Left side,
            renderThreads(
              openThreads
            ).amend(tw.flexFill),
            div(tw.bgGray3.wPx1.hPc100),
            // Right side
            div(
              tw.flexFill,
              child <-- selectedThreadSignal.map {
                _.fold(
                  NonIdealStateL(
                    icon = {
                      div(
                        tw.textGray4,
                        IconL(name = Val(Icon.Glyph.AlignLeft), size = Icon.Size.Custom(48))()
                      )
                    },
                    description = "Select a thread to read"
                  )()
                ) { thread =>
                  SingleThreadL(
                    entitySignal = entitySignal,
                    isHidden = thread.targetIsHidden,
                    isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
                    parentThread = thread.commentThread
                  )().amend(tw.hPc100.overflowAuto)
                }
              }
            )
          ),
          // Footer
          renderFooter(threads)
        )
      }
    )()
  }

}

private[notify] object SelectCommentsStepL {

  case class OnNextData(
    selectedThreadIds: Set[IssueId],
    selectedThreads: List[CommentThreadWithMetaData]
  )

}
