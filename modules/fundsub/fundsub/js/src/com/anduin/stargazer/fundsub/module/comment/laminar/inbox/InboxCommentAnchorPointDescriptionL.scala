// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.inbox

import com.raquo.airstream.core.Signal
import com.raquo.laminar.api.L.*
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.text.laminar.TruncateL
import design.anduin.components.tooltip.laminar.SingleTooltipL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*

import anduin.protobuf.fundsub.comment.TopicData

private[inbox] case class InboxCommentAnchorPointDescriptionL(
  topicDataSignal: Signal[TopicData],
  isHiddenSignal: Signal[Boolean] = Val(false)
) {

  private def renderAmlKycName = {
    val doctypeSignal =
      topicDataSignal.map(_.asMessage.sealedValue.amlKycDocData.map(_.doctype).getOrElse("Untitled document"))
    SingleTooltipL(
      position = PortalPosition.TopCenter,
      renderContent = _.amend(
        "The document is not available"
      ),
      isDisabled = !isHiddenSignal,
      renderTarget = {
        div(
          tw.textGray8.textSmall,
          isHiddenSignal.cls(tw.borderGray2.textGray5),
          tw.flex.itemsCenter.spaceX12,
          TruncateL(
            target = div(
              ComponentUtils.testIdL(InboxCommentAnchorPointDescriptionL, "DocumentType"),
              tw.flexFill,
              text <-- doctypeSignal
            ),
            lineClamp = Some(2)
          )(),
          child.maybe <-- isHiddenSignal.map { isHidden =>
            Option.when(isHidden) {
              IconL(name = Val(Icon.Glyph.EyeOff))()
            }
          }
        )
      }
    )()
  }

  private def renderFormQuestionLabel = {
    val formQuestionDataSignal = topicDataSignal.map { topicData =>
      topicData.asMessage.sealedValue.formQuestionData
    }
    val tocSectionSignal = formQuestionDataSignal.map(_.map(_.tocSection).getOrElse(""))
    val tocSectionEmptySignal = tocSectionSignal.map(_.isEmpty)
    val fieldDescriptionSignal = formQuestionDataSignal.map(_.map(_.fieldDescription).getOrElse(""))
    val fieldDescriptionEmptySignal = fieldDescriptionSignal.map(_.isEmpty)
    SingleTooltipL(
      position = PortalPosition.TopCenter,
      renderContent = _.amend(
        child <-- isHiddenSignal.map { isHidden =>
          if (isHidden) "Form question not applicable" else "Jump to form question"
        }
      ),
      isDisabled = !isHiddenSignal,
      renderTarget = {
        div(
          tw.textGray8.textSmall,
          isHiddenSignal.cls(tw.borderGray2.textGray5),
          tw.flex.itemsCenter.spaceX12,
          div(
            tw.flexFill,
            child <-- tocSectionEmptySignal.splitBoolean(
              whenTrue = _ => emptyNode,
              whenFalse = _ =>
                TruncateL(
                  lineClamp = Some(1),
                  div(
                    ComponentUtils.testIdL(InboxCommentAnchorPointDescriptionL, "ToCSection"),
                    text <-- tocSectionSignal
                  )
                )()
            ),
            child <-- fieldDescriptionEmptySignal.splitBoolean(
              whenTrue = _ => emptyNode,
              whenFalse = _ =>
                TruncateL(
                  lineClamp = Some(1),
                  target = div(
                    ComponentUtils.testIdL(InboxCommentAnchorPointDescriptionL, "FieldDescription"),
                    text <-- fieldDescriptionSignal
                  )
                )()
            )
          ),
          child <-- isHiddenSignal.splitBoolean(
            whenTrue = _ => IconL(name = Val(Icon.Glyph.EyeOff))(),
            whenFalse = _ => emptyNode
          )
        )
      }
    )()
  }

  def apply(): HtmlElement = {

    div(
      child <-- topicDataSignal
        .map { topicData =>
          topicData.asMessage.sealedValue.isAmlKycDocData
        }
        .splitBoolean(
          whenTrue = _ => renderAmlKycName,
          whenFalse = _ => renderFormQuestionLabel
        )
    )
  }

}
