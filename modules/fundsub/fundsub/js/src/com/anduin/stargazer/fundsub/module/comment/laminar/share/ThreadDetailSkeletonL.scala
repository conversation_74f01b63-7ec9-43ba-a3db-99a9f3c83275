package com.anduin.stargazer.fundsub.module.comment.laminar.share

import com.raquo.laminar.api.L.*
import design.anduin.components.progress.Skeleton
import design.anduin.components.progress.laminar.SkeletonL
import design.anduin.style.tw.*

private[laminar] case class ThreadDetailSkeletonL() {

  def apply(): HtmlElement = {
    div(
      0.to(2).map { _ =>
        div(
          tw.p16,
          div(
            tw.mb4.flex.itemsCenter,
            div(
              tw.mr8,
              SkeletonL(
                effect = Skeleton.Effect.Wave,
                height = "24px",
                width = "24px",
                shape = Skeleton.Shape.Circle
              )()
            ),
            div(
              tw.mr8,
              SkeletonL(
                effect = Skeleton.Effect.Wave,
                height = "15px",
                width = "100px",
                shape = Skeleton.Shape.Rounded
              )()
            ),
            SkeletonL(
              effect = Skeleton.Effect.Wave,
              height = "15px",
              width = "80px",
              shape = Skeleton.Shape.Rounded
            )()
          ),
          div(
            tw.ml32.spaceY8,
            SkeletonL(
              effect = Skeleton.Effect.Wave,
              height = "15px",
              width = "80%",
              shape = Skeleton.Shape.Rounded
            )(),
            SkeletonL(
              effect = Skeleton.Effect.Wave,
              height = "15px",
              width = "60%",
              shape = Skeleton.Shape.Rounded
            )()
          )
        )
      }
    )
  }

}
