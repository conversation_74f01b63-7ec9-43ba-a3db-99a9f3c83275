// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.inbox

import com.raquo.laminar.api.L.*
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.split.laminar.SplitterL
import design.anduin.components.toast.Toast
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import org.scalajs.dom

import anduin.fundsub.comment.models.CommentNotification
import anduin.fundsub.endpoint.formcomment.MarkFormCommentingNotifSpaceAsSeenParams
import anduin.id.entity.EntityId
import anduin.id.fundsub.{FundSubCloseId, FundSubId, FundSubLpId}
import anduin.model.common.user.UserId
import anduin.model.id.TeamId
import anduin.protobuf.flow.fundsub.admin.lpdashboard.{LpDocRequestStatus, LpStatus}
import anduin.protobuf.fundsub.comment.{AmlKycDocData, FormQuestionData, TopicData}
import anduin.stargazer.service.formcomment.FormCommentCommons.*
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent.*
import com.anduin.stargazer.fundsub.module.comment.laminar.inbox.sidebar.SidebarL
import com.anduin.stargazer.fundsub.module.comment.models.CommentView.CommentViewParams
import com.anduin.stargazer.fundsub.module.comment.models.{CommentNotificationSetting, CommentView, InboxLocationParams}

private[inbox] case class InboxBodyL(
  fundId: FundSubId,
  presetLocationOptSignal: Signal[Option[InboxLocationParams]],
  commentNotificationSettingSignal: Signal[Option[CommentNotificationSetting]],
  entitySignal: Signal[Option[EntityId]],
  isAmlKycCommentEnabledSignal: Signal[Boolean],
  isCommentMentioningEnabledSignal: Signal[Boolean],
  isCommentAssignmentEnabledSignal: Signal[Boolean],
  inactiveThresholdSignal: Signal[Option[Int]],
  notificationsSignal: Signal[List[CommentNotification]],
  canMakePublicCommentSignal: Signal[Boolean]
) {
  private val minFirstColumnWidth = 240
  private val firstColumnInitialWidth = 264
  private val minFirstColumnRatio = 0.3
  private val minSecondColumnWidth = 320
  private val secondColumnInitialWidth = 400

  private val tabVar: Var[NewInboxTab] = Var(NewInboxTab.AllCommentsTab)
  private val pageVar: Var[Int] = Var(1)
  private val showResolvedTabVar: Var[Boolean] = Var(false)
  private val anchorPointTypeFilterVar: Var[AnchorPointTypeFilter] = Var(AnchorPointTypeFilter.AllTypes)
  private val unreadFilterVar: Var[Boolean] = Var(false)
  private val assigneeFilterVar: Var[Option[Either[UserId, TeamId]]] = Var(None)
  private val commentVisibilityFilterVar: Var[Set[CommentVisibilityType]] = Var(Set.empty)
  private val lpCloseFilterVar: Var[Set[FundSubCloseId]] = Var(Set.empty)
  private val supportingDocStatusFilterVar: Var[Set[LpDocRequestStatus]] = Var(Set.empty)
  private val lpStatusFilterVar: Var[Set[LpStatus]] = Var(Set.empty)

  private val filterSignal = tabVar.signal
    .combineWith(
      pageVar.signal,
      showResolvedTabVar.signal,
      anchorPointTypeFilterVar.signal,
      unreadFilterVar.signal,
      assigneeFilterVar.signal,
      lpCloseFilterVar.signal,
      lpStatusFilterVar.signal,
      supportingDocStatusFilterVar.signal
    )
    .combineWith(
      commentVisibilityFilterVar.signal
    )
    .map {
      case (
            tab,
            page,
            showResolved,
            anchorPointType,
            unread,
            assigneeOpt,
            lpCloseFilter,
            lpStatusFilter,
            supportingDocStatusFilter,
            commentVisibilityFilter
          ) =>
        GetFundCommentDualThreadsParams(
          fundId = fundId,
          inboxTab = tab,
          anchorPointType = anchorPointType,
          resolved = showResolved,
          unread = unread,
          assigneeOpt = assigneeOpt,
          pageIndex = page,
          closeFilter = lpCloseFilter,
          lpStatusFilter = lpStatusFilter,
          supportingDocStatusFilter = supportingDocStatusFilter,
          commentVisibilityType = commentVisibilityFilter
        )
    }

  private val shouldRenderMentionTabSignal = tabVar.signal.map(_ == NewInboxTab.MentionsTab).distinct

  private val currentViewInfoVar: Var[CommentView] = Var(CommentView.NoThread)

  private val showLeftSplitterVar = Var(false)

  private val showRightSplitterVar = Var(false)

  private val onMountEventBus = new EventBus[Unit]

  private val assignThreadEventBus = new EventBus[(FundSubLpId, TopicData)]

  private def handleViewThread(
    commentViewParams: CommentViewParams
  ): Unit = {
    commentViewParams.topicData match {
      case amlKycDocData: AmlKycDocData =>
        currentViewInfoVar.set(
          CommentView.Doctype(
            amlKycDocData.doctype,
            fundSubLpId = commentViewParams.fundSubLpId,
            targetIsHidden = commentViewParams.targetIsHidden,
            isPublic = commentViewParams.isPublic,
            comment = commentViewParams.comment,
            defaultOpenedCommentIdOpt = commentViewParams.defaultOpenedCommentIdOpt
          )
        )
      case formQuestionData: FormQuestionData =>
        currentViewInfoVar.set(
          CommentView.Field(
            formQuestionData,
            fundSubLpId = commentViewParams.fundSubLpId,
            targetIsHidden = commentViewParams.targetIsHidden,
            isPublic = commentViewParams.isPublic,
            comment = commentViewParams.comment,
            defaultOpenedCommentIdOpt = commentViewParams.defaultOpenedCommentIdOpt
          )
        )
      case _ => ()
    }
  }

  private def handleMarkAllAsRead(
    lpId: Option[FundSubLpId],
    onSuccess: Observer[Unit]
  ): Unit = {
    ZIOUtils.runAsync {
      FundSubEndpointClient
        .markFormCommentNotifSpaceAsSeen(
          MarkFormCommentingNotifSpaceAsSeenParams(
            fundSubId = fundId,
            fundSubLpIdOpt = lpId
          )
        )
        .map(
          _.fold(
            _ => Toast.error("Failed to mark notification as seen"),
            _ => onSuccess.onNext(())
          )
        )
    }
  }

  private def renderEmptyTab(tab: NewInboxTab) = {
    div(
      tw.flexFill.hPc100,
      if (tab == NewInboxTab.MentionsTab) {
        NonIdealStateL(
          icon = {
            div(
              tw.textGray4,
              IconL(name = Val(Icon.Glyph.At), size = Icon.Size.Custom(48))()
            )
          },
          title = "No mentions yet"
        )()
      } else {
        div()
      }
    )
  }

  private def renderNoSelectedThread() = {
    NonIdealStateL(
      icon = {
        div(
          tw.textGray4.flex.flexCol.itemsCenter,
          IconL(name = Val(Icon.Glyph.AlignLeft), size = Icon.Size.Custom(48))(),
          div(
            tw.textGray7.text15.pt16.fontSemiBold,
            "Select a comment to read"
          )
        )
      }
    )()
  }

  private def renderThreadView(
    currentView: CommentView,
    onSeenThread: Observer[FundSubLpId]
  ) = {
    currentView match {
      case CommentView.NoThread => renderNoSelectedThread()
      case CommentView
            .Doctype(
              doctype,
              fundSubLpId,
              targetIsHidden,
              isPublic,
              initialComment,
              defaultOpenedCommentIdOpt
            ) =>
        val topicData = AmlKycDocData(doctype)
        InboxThreadDetailsL(
          commentNotificationSettingSignal = commentNotificationSettingSignal,
          entitySignal = entitySignal,
          topicData = topicData,
          initialComment = initialComment,
          isPublic = isPublic,
          fundSubLpId = fundSubLpId,
          defaultOpenedCommentIdOpt = defaultOpenedCommentIdOpt,
          targetIsHidden = targetIsHidden,
          isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
          isCommentAssignmentEnabledSignal = isCommentAssignmentEnabledSignal,
          inactiveDaysThresholdSignal = inactiveThresholdSignal,
          handleViewThread = handleViewThread,
          onSeenThread = onSeenThread,
          canMakePublicCommentSignal = canMakePublicCommentSignal,
          onAssignThread = assignThreadEventBus.writer.contramap(_ => (fundSubLpId, topicData))
        )()
      case CommentView.Field(
            formQuestionData,
            fundSubLpId,
            targetIsHidden,
            isPublic,
            initialComment,
            defaultOpenedCommentIdOpt
          ) =>
        InboxThreadDetailsL(
          commentNotificationSettingSignal = commentNotificationSettingSignal,
          entitySignal = entitySignal,
          topicData = formQuestionData,
          initialComment = initialComment,
          isPublic = isPublic,
          fundSubLpId = fundSubLpId,
          defaultOpenedCommentIdOpt = defaultOpenedCommentIdOpt,
          targetIsHidden = targetIsHidden,
          isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
          isCommentAssignmentEnabledSignal = isCommentAssignmentEnabledSignal,
          inactiveDaysThresholdSignal = inactiveThresholdSignal,
          handleViewThread = handleViewThread,
          onSeenThread = onSeenThread,
          canMakePublicCommentSignal = canMakePublicCommentSignal,
          onAssignThread = assignThreadEventBus.writer.contramap(_ => (fundSubLpId, formQuestionData))
        )()
    }
  }

  private def renderMiddleBoxWithThreadsList(lpMetaInfoData: FetchLpsMetaInfoL.RenderChildren) = {
    FetchCommentAnchorPointsL(
      filterSignal = filterSignal,
      renderChildren = dataSignal => {
        div(
          tw.flexFill.flex.hPc100,
          pageVar.signal.combineWith(dataSignal).distinct --> Observer[(Int, FetchCommentAnchorPointsL.Data)] {
            case (pageIndex, anchorPointsData) =>
              if (pageIndex > 1 && !anchorPointsData.isFetching && anchorPointsData.pageItems.isEmpty) {
                pageVar.set(1)
              }
          },
          InboxThreadListSectionL(
            fundId = fundId,
            pageIndexSignal = pageVar.signal.distinct,
            tabSignal = tabVar.signal.distinct,
            isResolvedTabSignal = showResolvedTabVar.signal.distinct,
            anchorPointTypeFilterSignal = anchorPointTypeFilterVar.signal.distinct,
            unreadFilterSignal = unreadFilterVar.signal.distinct,
            assigneeFilterSignal = assigneeFilterVar.signal.distinct,
            commentVisibilityFilterSignal = commentVisibilityFilterVar.signal.distinct,
            lpCloseIdsFilterSignal = lpCloseFilterVar.signal.distinct,
            lpStatusFilterSignal = lpStatusFilterVar.signal.distinct,
            supportingDocStatusFilterSignal = supportingDocStatusFilterVar.signal.distinct,
            pageItemsSignal = dataSignal.map(_.pageItems),
            allPageItemsCountSignal = dataSignal.map(_.allPageItemsCount).distinct,
            allItemIdsAndStatusInInboxTabSignal = dataSignal.map(_.allItemIdsInInboxTab),
            isFetchingSignal = dataSignal.map(_.isFetching),
            notificationsSignal = notificationsSignal,
            selectedCommentViewSignal = currentViewInfoVar.signal,
            isCommentAssignmentEnabledSignal = isCommentAssignmentEnabledSignal,
            inactiveThresholdSignal = inactiveThresholdSignal,
            onPageIndexUpdate = pageVar.writer,
            onSwitchToResolvedTab = showResolvedTabVar.writer,
            onAnchorPointTypeFilterUpdate = anchorPointTypeFilterVar.writer,
            onUnreadFilterUpdate = unreadFilterVar.writer,
            onAssigneeFilterUpdate = assigneeFilterVar.writer,
            onCommentVisibilityFilterUpdate = commentVisibilityFilterVar.writer,
            onLpCloseIdsFilterUpdate = lpCloseFilterVar.writer,
            onLpStatusFilterUpdate = lpStatusFilterVar.writer,
            onSupportingDocStatusFilterUpdate = supportingDocStatusFilterVar.writer,
            onMarkAllAsRead = Observer[Option[FundSubLpId]] { lpIdOpt =>
              handleMarkAllAsRead(
                lpIdOpt,
                Observer[Unit] { _ =>
                  if (lpIdOpt.isEmpty) {
                    Var.set(
                      tabVar -> NewInboxTab.AllCommentsTab,
                      pageVar -> 1,
                      showResolvedTabVar -> false
                    )
                    lpMetaInfoData.refetch.onNext(List.empty)
                  }
                }
              )
            },
            onViewAnchorPoint = Observer[(DualCommentThread, Boolean)] { (dualCommentThread, isInternalComment) =>
              val commentThreadOpt =
                if (isInternalComment) {
                  dualCommentThread.internalCommentThreadOpt
                } else {
                  dualCommentThread.sharedCommentThreadOpt
                }
              commentThreadOpt.foreach { thread =>
                currentViewInfoVar.set(CommentView.threadToCommentView(dualCommentThread.targetIsHidden, thread))
              }
            },
            refetchAssigneeInfoEventStream = assignThreadEventBus.events,
            lpInfosSignal = lpMetaInfoData.allLpsMetaInfoSignal.map {
              _.map { lpInfo =>
                InboxDualThreadSummaryItemL.LpInfoForThreadSummary(
                  lpId = lpInfo.fundSubLpId,
                  mainInvestorName = lpInfo.mainLpFullName,
                  investmentEntity = lpInfo.investmentEntityName
                )
              }.toSeq
            }
          )()
        )
      }
    )()
  }

  private def renderMiddleBoxWithMentionsList(
    allLpsMetaInfoSignal: Signal[List[FundSubLpMetaInfo]],
    refetchLpMetaInfoObserver: Observer[List[FundSubLpId]]
  ) = {
    FetchMentionsL(
      pageIndexSignal = pageVar.signal.distinct,
      fundSubId = fundId,
      renderChildren = mentionTabRenderProps => {
        val mentionsSignal = mentionTabRenderProps.dataSignal
          .map(_.mentionsResponse.map(_.mentions).getOrElse(List.empty))
          .distinct
        val numMentionsSignal = mentionTabRenderProps.dataSignal
          .map(_.mentionsResponse.map(_.totalItems).getOrElse(0))
          .distinct
        val isFetchingSignal = mentionTabRenderProps.dataSignal.map(_.isFetching).distinct
        val hasMentionsSignal = mentionsSignal.map(_.nonEmpty)

        div(
          tw.flexFill.flex.hPc100,
          child <-- hasMentionsSignal.combineWith(isFetchingSignal).distinct.map { case (hasMentions, isFetching) =>
            if (!hasMentions && !isFetching) {
              renderEmptyTab(NewInboxTab.MentionsTab)
            } else {
              div(
                tw.flexFill.hPc100.overflowHidden,
                ListMentionsL(
                  currentPageSignal = pageVar.signal.distinct,
                  isFetchingSignal = mentionTabRenderProps.dataSignal.map(_.isFetching),
                  listLpMetaInfoSignal = allLpsMetaInfoSignal,
                  unreadThreadsSignal = notificationsSignal.map(_.map(_.threadId).toSet),
                  numMentionsSignal = numMentionsSignal,
                  currentCommentViewSignal = currentViewInfoVar.signal,
                  isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
                  isCommentAssignmentEnabledSignal = isCommentAssignmentEnabledSignal,
                  mentionsSignal = mentionsSignal,
                  onJumpToPage = Observer[Int] { pageIndex =>
                    currentViewInfoVar.set(CommentView.NoThread)
                    pageVar.set(pageIndex)
                  },
                  onMarkAllAsRead = Observer[Unit] { _ =>
                    handleMarkAllAsRead(
                      lpId = None,
                      Observer[Unit] { _ =>
                        Var.set(
                          tabVar -> NewInboxTab.AllCommentsTab,
                          pageVar -> 1,
                          showResolvedTabVar -> false
                        )
                        refetchLpMetaInfoObserver.onNext(List.empty)
                      }
                    )
                  },
                  onViewThread = Observer[CommentMentionWithMetaData] { mention =>
                    handleViewThread(
                      CommentViewParams(
                        topicData = mention.topicData,
                        fundSubLpId = mention.fundSubLpId,
                        targetIsHidden = mention.targetIsHidden,
                        isPublic = mention.isPublic,
                        defaultOpenedCommentIdOpt = Some(mention.comment.commentId)
                      )
                    )
                  }
                )().amend(tw.hPc100.wPc100)
              )
            }
          }
        )
      }
    )()
  }

  private def renderColumnSplitter(splitterVisibilityVar: Var[Boolean]) = { (splitterElement: HtmlElement) =>
    {
      splitterElement.amend(
        tw.bgGray3.wPx1.hPc100.group.z9999,
        div(
          tw.textGray6.hPc100.wPx16.groupHover(tw.visible),
          splitterVisibilityVar.signal.not.cls(tw.invisible),
          div( // icon container
            tw.hPc100.flex.flexCol.justifyCenter.bgGray3,
            width.px(4),
            div(
              marginLeft.px(-6),
              IconL(name = Val(Icon.Glyph.EllipsisVertical), size = Icon.Size.Px16)()
            )
          )
        ),
        onMouseDown.map(_ => splitterVisibilityVar.set(true)) --> Observer.empty,
        documentEvents(_.onMouseUp).mapTo(()) --> Observer[Unit] { _ => splitterVisibilityVar.set(false) }
      )
    }
  }

  private def resetFilterForTabUpdate(): Unit = {
    pageVar.set(1)
    showResolvedTabVar.set(false)
  }

  private def renderNavBarAndThreadList(lpsMetaInfoData: FetchLpsMetaInfoL.RenderChildren) = {
    SplitterL(
      direction = SplitterL.Direction.Horizontal,
      renderSplitter = renderColumnSplitter(showLeftSplitterVar),
      firstHalfSizeConstrain = sizeConstraint =>
        sizeConstraint.numberOfPixels >= minFirstColumnWidth && sizeConstraint.numberOfPixels <= dom.window.innerWidth *
          minFirstColumnRatio && sizeConstraint.numberOfPixels <= sizeConstraint.containerPixels - minSecondColumnWidth,
      renderFirstHalf = _.amend(
        width.px(firstColumnInitialWidth),
        tw.hPc100,
        SidebarL(
          inboxTabSignal = tabVar.signal,
          listLpMetaInfoSignal = lpsMetaInfoData.allLpsMetaInfoSignal,
          notificationsSignal = notificationsSignal,
          isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
          isCommentAssignmentEnabledSignal = isCommentAssignmentEnabledSignal,
          onTabClick = Observer[NewInboxTab] { tab =>
            if (tabVar.now() != tab) {
              resetFilterForTabUpdate()
              currentViewInfoVar.set(CommentView.NoThread)
              tabVar.set(tab)
            }
          },
          fundId = fundId
        )()
      ),
      renderSecondHalf = _.amend(
        width.px(secondColumnInitialWidth),
        tw.hPc100,
        child <-- shouldRenderMentionTabSignal.splitBoolean(
          whenTrue = _ =>
            renderMiddleBoxWithMentionsList(
              lpsMetaInfoData.allLpsMetaInfoSignal,
              lpsMetaInfoData.refetch
            ),
          whenFalse = _ => renderMiddleBoxWithThreadsList(lpsMetaInfoData)
        )
      )
    )()
  }

  def apply(): HtmlElement = {
    FetchLpsMetaInfoL(
      fundSubId = fundId,
      getLpsWithCommentOnly = true,
      getLpsUnsubscribedToDigestEmailOnly = false,
      renderChildren = lpsMetaInfoData =>
        div(
          onMountEventBus.events.sample(presetLocationOptSignal).map { locationParamsOpt =>
            locationParamsOpt.foreach { location =>
              tabVar.set(
                location.tab
              )
              pageVar.set(location.page)
              showResolvedTabVar.set(
                location.showResolvedTab
              )
            }
          } --> Observer.empty,
          tw.flexFill.flex,
          SplitterL(
            direction = SplitterL.Direction.Horizontal,
            renderSplitter = renderColumnSplitter(showRightSplitterVar),
            firstHalfSizeConstrain = sizeConstraint =>
              sizeConstraint.numberOfPercentages <= 50 && sizeConstraint.numberOfPixels >= minFirstColumnWidth + minSecondColumnWidth,
            renderFirstHalf = _.amend(
              renderNavBarAndThreadList(lpsMetaInfoData)
            ),
            renderSecondHalf = _.amend(
              child <-- currentViewInfoVar.signal.distinct.map { currentView =>
                renderThreadView(
                  currentView = currentView,
                  onSeenThread = Observer[FundSubLpId] { lpId =>
                    lpsMetaInfoData.refetch.onNext(List(lpId))
                  }
                )
              }
            )
          )(),
          onMountCallback { _ =>
            onMountEventBus.emit(())
          }
        )
    )()
      .amend(
        ComponentUtils.testIdL(InboxBodyL)
      )
  }

}
