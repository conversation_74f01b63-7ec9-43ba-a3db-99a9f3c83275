// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.inbox.sidebar

import com.raquo.laminar.api.L.*
import design.anduin.components.wrapper.laminar.ReactiveWrapperL
import japgolly.scalajs.react.*

import anduin.id.fundsub.FundSubId
import anduin.model.id.{FolderId, TemporalWorkflowId}
import com.anduin.stargazer.fundsub.module.comment.laminar.inbox.sidebar.PendingTaskExportPopoverL.ReactiveProps

private[inbox] final case class PendingTaskExportPopoverL(
  fundId: FundSubId,
  workflowIdSignal: Signal[TemporalWorkflowId],
  folderIdOptSignal: Signal[Option[FolderId]],
  onRefetchTaskStatus: Observer[Unit],
  onDismiss: Observer[Unit]
) {

  def apply(): HtmlElement =
    ReactiveWrapperL[ReactiveProps](
      dataSignal = workflowIdSignal.combineWith(folderIdOptSignal).map { case (workflowId, folderIdOpt) =>
        ReactiveProps(workflowId, folderIdOpt)
      },
      render = reactiveProps =>
        PendingTaskExportPopover(
          fundId = fundId,
          workflowId = reactiveProps.workflowId,
          folderIdOpt = reactiveProps.folderIdOpt,
          onRefetchTaskStatus = Callback(onRefetchTaskStatus.onNext(())),
          onDismiss = Callback(onDismiss.onNext(()))
        )()
    )()

}

private[inbox] object PendingTaskExportPopoverL {

  private case class ReactiveProps(
    workflowId: TemporalWorkflowId,
    folderIdOpt: Option[FolderId]
  )

}
