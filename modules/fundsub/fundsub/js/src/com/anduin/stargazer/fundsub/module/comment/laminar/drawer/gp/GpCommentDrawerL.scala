// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.drawer.gp

import com.raquo.laminar.api.L.*
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.model.id.TeamId
import anduin.protobuf.fundsub.comment.AmlKycDocData
import com.anduin.stargazer.fundsub.module.comment.laminar.HighlightTargetFieldL
import com.anduin.stargazer.fundsub.module.comment.laminar.drawer.CommentDrawerUIModels
import com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent.{FetchCommentSettingL, WithGroupsMetadataL}
import com.anduin.stargazer.fundsub.module.comment.models.{CommentNotificationSetting, CommentTarget, CommentsData}

private[fundsub] case class GpCommentDrawerL(
  lpIdSignal: Signal[FundSubLpId],
  targetSignal: Signal[CommentTarget],
  amlKycNamesSignal: Signal[Option[List[String]]],
  commentNotificationSettingSignal: Signal[Option[CommentNotificationSetting]],
  commentsDataSignal: Signal[CommentsData],
  inactiveDaysThresholdSignal: Signal[Option[Int]],
  initialView: CommentDrawerUIModels.DrawerView,
  isHiddenFieldThread: String => Signal[Boolean],
  onJumpToTarget: Observer[String],
  onClose: Observer[Unit],
  fundSubId: FundSubId
) {

  private val internalSelectedViewVar = Var[CommentDrawerUIModels.DrawerView](initialView)
  // Manage the current target field
  private val activeFieldVar = Var(Option.empty[String])

  private val handleSwitchAllComments: Observer[Unit] = Observer { _ =>
    internalSelectedViewVar.set(CommentDrawerUIModels.DrawerView.AllComments)
  }

  private val handleClose: Observer[Unit] = Observer { _ =>
    activeFieldVar.set(None)
    onClose.onNext(())
  }

  private val filteredCommentData = commentsDataSignal
    .combineWith(targetSignal)
    .map { case (commentData, target) =>
      commentData.filterByCommentTarget(target)
    }

  private def renderContent(
    lpIdSignal: Signal[FundSubLpId],
    isCommentMentioningEnabledSignal: Signal[Boolean],
    isCommentAssignmentEnabledSignal: Signal[Boolean],
    userGPTeamIdOptSignal: Signal[Option[TeamId]]
  ) = {
    div(
      tw.hPc100,
      child <-- internalSelectedViewVar.signal.distinct.map {
        case CommentDrawerUIModels.DrawerView.AllComments =>
          GpAllDualCommentThreadsPanelL(
            lpIdSignal = lpIdSignal,
            targetSignal = targetSignal,
            commentsDataSignal = filteredCommentData,
            amlKycNamesSignal = amlKycNamesSignal,
            isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
            isCommentAssignmentEnabledSignal = isCommentAssignmentEnabledSignal,
            inactiveDaysThresholdSignal = inactiveDaysThresholdSignal,
            isHiddenFieldThread = isHiddenFieldThread,
            userGPTeamIdOptSignal = userGPTeamIdOptSignal,
            onJumpToTarget = onJumpToTarget,
            onSwitchToView = internalSelectedViewVar.writer,
            onClose = handleClose,
            fundSubId = fundSubId
          )().amend(
            onMountCallback { _ =>
              activeFieldVar.set(None)
            }
          )
        case CommentDrawerUIModels.DrawerView.Doctype(doctype, isPublic, initialComment) =>
          GpThreadDetailDrawerL(
            lpIdSignal = lpIdSignal,
            commentsDataSignal = filteredCommentData,
            topicDataSignal = Val(AmlKycDocData(doctype)),
            isPublicSignal = Val(isPublic),
            commentNotificationSettingSignal = commentNotificationSettingSignal,
            isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
            isCommentAssignmentEnabledSignal = isCommentAssignmentEnabledSignal,
            inactiveDaysThresholdSignal = inactiveDaysThresholdSignal,
            initialComment = initialComment,
            onSwitchAllComments = handleSwitchAllComments,
            onSwitchToView = Observer[CommentDrawerUIModels.DrawerView] { newView =>
              internalSelectedViewVar.set(newView)
            },
            onClose = handleClose
          )().amend(
            onMountCallback { _ =>
              activeFieldVar.set(Option(doctype))
            }
          )
        case CommentDrawerUIModels.DrawerView.Field(formQuestionData, isPublic, initialComment) =>
          GpThreadDetailDrawerL(
            lpIdSignal = lpIdSignal,
            commentsDataSignal = filteredCommentData,
            topicDataSignal = Val(formQuestionData),
            isPublicSignal = Val(isPublic),
            commentNotificationSettingSignal = commentNotificationSettingSignal,
            isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
            isCommentAssignmentEnabledSignal = isCommentAssignmentEnabledSignal,
            inactiveDaysThresholdSignal = inactiveDaysThresholdSignal,
            initialComment = initialComment,
            onSwitchAllComments = handleSwitchAllComments,
            onSwitchToView = internalSelectedViewVar.writer,
            onClose = handleClose
          )().amend(
            onMountCallback { _ =>
              activeFieldVar.set(Option(formQuestionData.fieldAlias))
            }
          )
      }
    )
  }

  def apply(): HtmlElement = {
    div(
      ComponentUtils.testIdL(GpCommentDrawerL),
      tw.hPc100,
      child <-- lpIdSignal.distinct.map { lpId =>
        FetchCommentSettingL(
          fundSubId = lpId.parent,
          renderChildren = settingSignal => {
            val isCommentMentioningEnabledSignal = settingSignal.map(_.exists(_.isCommentMentioningEnabled)).distinct
            val isCommentAssignmentEnabledSignal = settingSignal.map(_.exists(_.isCommentAssignmentEnabled)).distinct
            WithGroupsMetadataL(
              lpId.parent,
              renderChildren = groupsMetadataSignal =>
                renderContent(
                  lpIdSignal,
                  isCommentMentioningEnabledSignal,
                  isCommentAssignmentEnabledSignal,
                  userGPTeamIdOptSignal =
                    groupsMetadataSignal.dataSignal.map(_.groupMetadataResOpt.map(_.currentUserGroupId))
                )
            )()
          }
        )().amend(tw.hPc100)
      },
      child.maybe <-- activeFieldVar.signal.distinct.map {
        _.map(HighlightTargetFieldL(_)())
      }
    )
  }

}
