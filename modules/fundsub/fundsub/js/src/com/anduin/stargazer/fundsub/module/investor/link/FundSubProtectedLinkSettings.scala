// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.link

import java.time.{LocalDate, ZoneId, ZonedDateTime}

import design.anduin.components.button.Button
import design.anduin.components.date.react.DatePickerR
import design.anduin.components.icon.Icon
import design.anduin.components.switcher.react.SwitcherR
import design.anduin.components.textbox.TextBox
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.component.util.JsDateFormatterUtils
import anduin.id.fundsub.FundSubId
import anduin.utils.StateSnapshotWithModFn

private[fundsub] final case class FundSubProtectedLinkSettings(
  fundSubId: FundSubId,
  linkSettings: StateSnapshotWithModFn[LinkSettings],
  enterpriseLoginEnabledForFundsub: Boolean
) {

  def apply(): VdomElement = FundSubProtectedLinkSettings.component(this)
}

private[fundsub] object FundSubProtectedLinkSettings {

  private type Props = FundSubProtectedLinkSettings

  private def renderSubtitleHeader = {
    <.p(
      tw.textGray7.text13.leading20.fontNormal.pt8,
      "Configure your shareable link below."
    )
  }

  private def renderSection(
    title: String,
    testId: String,
    subtitleOpt: Option[String],
    isRequired: Boolean = false,
    switchOpt: Option[StateSnapshotWithModFn[Boolean]]
  )(
    children: VdomNode*
  ) = {
    <.div(
      TagMod.unless(testId.isEmpty)(ComponentUtils.testId(FundSubProtectedLinkSettings, testId)),
      tw.pt20,
      <.label(
        tw.flex.itemsCenter,
        TagMod.when(children.nonEmpty)(tw.pb8),
        <.div(
          tw.flexFill,
          <.p(
            tw.textGray8.text13.leading20.fontSemiBold,
            title,
            Option.when(isRequired) {
              <.span(
                tw.textDanger5,
                " *"
              )
            }
          ),
          subtitleOpt.map { subtitle =>
            <.p(
              tw.textGray7.text11.leading16.fontNormal,
              subtitle
            )
          }
        ),
        switchOpt.map { switch =>
          <.div(
            tw.flexNone,
            SwitcherR(
              isChecked = switch.value,
              onChange = switch.setState,
              testId = testId + "Toggle"
            )()
          )
        }
      ),
      React.Fragment(children*)
    )
  }

  private def renderExpiryDate(props: Props) = {
    val isEnabledZoom = props.linkSettings.withReuse.zoomState {
      LinkSettings.zoom {
        _.expiryDate.isEnabled
      } { newValue => setting =>
        setting.copy(expiryDate = setting.expiryDate.copy(isEnabled = newValue))
      }
    }
    val expiryDateZoom = props.linkSettings.withReuse.zoomState {
      LinkSettings.zoom { state =>
        ZonedDateTime.ofInstant(state.expiryDate.expiryDate, ZoneId.systemDefault()).toLocalDate
      } { newValue => setting =>
        setting.copy(
          expiryDate = setting.expiryDate.copy(expiryDate = LinkSettings.ExpiryDateSetting(newValue))
        )
      }
    }
    renderSection(
      title = "Expiry date",
      subtitleOpt = Some("This link will expire on the selected date"),
      switchOpt = Some(isEnabledZoom),
      testId = "ExpiryDate"
    )(
      if (isEnabledZoom.value) {
        React.Fragment(
          DatePickerR(
            date = Some(expiryDateZoom.value),
            minDate = LocalDate.now,
            onSelectDate = dateOpt => expiryDateZoom.setState(dateOpt.getOrElse(expiryDateZoom.value))
          )(),
          if (LinkSettings.isExpiryValid(props.linkSettings.value)) {
            <.p(
              tw.textWarning5.text11.leading16.fontNormal.pt8,
              s"This link will expire ",
              JsDateFormatterUtils.format(
                ZonedDateTime.ofInstant(props.linkSettings.value.expiryDate.expiryDate, ZoneId.systemDefault()),
                JsDateFormatterUtils.JsDateFormat.LongDateTimeAndZonePattern
              )
            )
          } else {
            <.p(
              tw.textDanger5.text11.leading16.fontNormal.pt8,
              "Link expired. Please select a new date to enable the link."
            )
          }
        )
      } else {
        EmptyVdom
      }
    )
  }

  private def renderPassword(props: Props) = {
    val isEnabledZoom = props.linkSettings.withReuse.zoomState {
      LinkSettings.zoom {
        _.password.isEnabled
      } { newValue => setting =>
        setting.copy(password = setting.password.copy(isEnabled = newValue))
      }
    }
    val passwordZoom = props.linkSettings.withReuse.zoomState {
      LinkSettings.zoom {
        _.password.password
      } { newValue => setting =>
        setting.copy(password = setting.password.copy(password = newValue))
      }
    }
    renderSection(
      title = "Password",
      subtitleOpt = Some("This password must be entered to register and gain access to the fund"),
      switchOpt = Some(isEnabledZoom),
      testId = "Password"
    )(
      if (isEnabledZoom.value) {
        <.div(
          passwordZoom.value match {
            case LinkSettings.PasswordSetting.UseExisting =>
              Button(
                testId = "Reset",
                style = Button.Style.Ghost(color = Button.Color.Primary),
                onClick = passwordZoom.setState(LinkSettings.PasswordSetting.Change(""))
              )("Reset password")
            case LinkSettings.PasswordSetting.Change(password) =>
              <.div(
                tw.flex.itemsCenter,
                <.div(
                  tw.flexNone,
                  ^.width := "300px",
                  TextBox(
                    testId = "Pw",
                    value = password,
                    onChange = newValue => passwordZoom.setState(LinkSettings.PasswordSetting.Change(newValue)),
                    tpe = TextBox.Tpe.Password,
                    placeholder = "Enter a password"
                  )()
                ),
                if (props.linkSettings.value.password.initialIsEnabled) {
                  <.div(
                    tw.flexNone.pl8,
                    TooltipR(
                      renderTarget = Button(
                        testId = "Cancel",
                        style = Button.Style.Minimal(color = Button.Color.Danger, icon = Some(Icon.Glyph.Cross)),
                        onClick = passwordZoom.setState(LinkSettings.PasswordSetting.UseExisting)
                      )(),
                      renderContent = _("Cancel")
                    )()
                  )
                } else {
                  EmptyVdom
                }
              )
          }
        )
      } else {
        EmptyVdom
      }
    )
  }

  private def renderWhitelistedDomains(props: Props) = {
    val isEnabledZoom = props.linkSettings.withReuse.zoomState {
      LinkSettings.zoom {
        _.whitelistedDomains.isEnabled
      } { newValue => setting =>
        setting.copy(whitelistedDomains = setting.whitelistedDomains.copy(isEnabled = newValue))
      }
    }
    val domainZoom = props.linkSettings.withReuse.zoomState {
      LinkSettings.zoom {
        _.whitelistedDomains.domains
      } { newValue => setting =>
        setting.copy(whitelistedDomains = setting.whitelistedDomains.copy(domains = newValue))
      }
    }

    renderSection(
      title = "Whitelisted domains",
      subtitleOpt = Some("Only users with a whitelisted domain can register and gain access to the fund"),
      switchOpt = Some(isEnabledZoom),
      testId = "WhitelistedDomain"
    )(
      if (isEnabledZoom.value) {
        React.Fragment(
          WhitelistedDomainsTextArea(domains = domainZoom)(),
          <.p(
            tw.textGray7.text11.leading16.fontNormal.pt8,
            tw.italic,
            "Enter multiple domains separated by a comma or semicolon"
          )
        )
      } else {
        EmptyVdom
      }
    )
  }

  private def renderEnterpriseLogin(props: Props) = {
    val isEnabledZoom = props.linkSettings.withReuse.zoomState {
      LinkSettings.zoom {
        _.isEnterpriseLoginEnabled
      } { newValue => setting =>
        setting.copy(isEnterpriseLoginEnabled = newValue)
      }
    }

    if (!props.enterpriseLoginEnabledForFundsub) {
      EmptyVdom
    } else {
      renderSection(
        title = "Single sign on link",
        subtitleOpt = Some("Use your account server to sign on your investors."),
        switchOpt = Some(isEnabledZoom),
        testId = "EnterpriseLogin"
      )()
    }
  }

  def render(props: Props): VdomElement = {
    React.Fragment(
      renderSubtitleHeader,
      renderExpiryDate(props),
      renderPassword(props),
      renderWhitelistedDomains(props),
      renderEnterpriseLogin(props)
    )
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
