// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.inbox.sidebar

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.progress.react.CircleIndicatorR
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import design.anduin.table.TaskUtils
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import zio.ZIO

import anduin.dms.tracking.DmsTrackingActivityType
import anduin.file.FileDownloaderUtils
import anduin.fundsub.endpoint.formcomment.CancelCommentExportTaskParams
import anduin.id.fundsub.FundSubId
import anduin.model.id.{FolderId, TemporalWorkflowId}
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.service.file.BatchDownloadRequest

private[inbox] final case class PendingTaskExportPopover(
  fundId: FundSubId,
  workflowId: TemporalWorkflowId,
  folderIdOpt: Option[FolderId],
  onRefetchTaskStatus: Callback,
  onDismiss: Callback
) {
  def apply(): VdomElement = PendingTaskExportPopover.component(this)
}

private[inbox] object PendingTaskExportPopover {
  private type Props = PendingTaskExportPopover
  private val PopoverWidth = 284

  private def renderPendingTask(props: Props) = {
    <.div(
      ^.width := PopoverWidth.px,
      tw.p8,
      <.div(
        tw.py8.px12.textPrimary3,
        CircleIndicatorR()()
      ),
      <.div(
        tw.py8.px12.fontSemiBold.leading16.text13,
        "Preparing your files..."
      ),
      <.div(
        tw.py4.px12.leading20,
        "A download button will appear when ready."
      ),
      <.div(
        tw.py8.px12.flex.itemsCenter,
        <.div(
          tw.mlAuto,
          Button(
            style = Button.Style.Minimal(
              height = Button.Height.Fix24
            ),
            onClick = onCancel(
              props.fundId,
              props.workflowId,
              props.onRefetchTaskStatus >> props.onDismiss
            )
          )("Cancel")
        )
      )
    )
  }

  private def renderCompletedTask(
    props: Props,
    folderId: FolderId
  ) = {

    <.div(
      ^.width := PopoverWidth.px,
      tw.p8,
      <.div(
        tw.py8.px12.textSuccess3,
        IconR(name = Icon.Glyph.CheckCircle)()
      ),
      <.div(
        tw.py8.px12.fontSemiBold.leading16.text13,
        "Export ready"
      ),
      <.div(
        tw.py4.px12.leading20.text13,
        "Your files are ready for download."
      ),
      <.div(
        tw.py8.px12.flex.itemsCenter,
        Button(
          style = Button.Style.Full(
            color = Button.Color.Primary,
            height = Button.Height.Fix24
          ),
          onClick = onDownload(props, folderId) >> props.onDismiss
        )("Download"),
        <.div(
          tw.flexFill
        ),
        Button(
          style = Button.Style.Minimal(
            height = Button.Height.Fix24
          ),
          onClick = onCancel(
            props.fundId,
            props.workflowId,
            props.onRefetchTaskStatus >> props.onDismiss
          )
        )("Cancel")
      )
    )
  }

  private def render(props: Props) = {
    props.folderIdOpt.fold[VdomElement] {
      renderPendingTask(props)
    } { folderId =>
      renderCompletedTask(props, folderId)
    }
  }

  private def onDownload(props: Props, folderId: FolderId) = {
    TaskUtils.toReactCallback {
      for {
        _ <- ZIO.attempt {
          FileDownloaderUtils.download(
            BatchDownloadRequest(
              "comments",
              Seq(folderId),
              Seq.empty,
              DmsTrackingActivityType.Download
            )
          )
        }
        _ <- FundSubEndpointClient.cancelCommentExportTask(
          CancelCommentExportTaskParams(
            props.fundId,
            props.workflowId
          )
        )
      } yield props.onRefetchTaskStatus >> props.onDismiss
    }
  }

  private def onCancel(fundId: FundSubId, workflowId: TemporalWorkflowId, onDone: Callback) = {
    TaskUtils.toReactCallback {
      FundSubEndpointClient
        .cancelCommentExportTask(
          CancelCommentExportTaskParams(
            fundId,
            workflowId
          )
        )
        .map(_ => onDone)
    }
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
