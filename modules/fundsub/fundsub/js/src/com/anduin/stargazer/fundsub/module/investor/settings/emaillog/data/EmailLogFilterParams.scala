// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.emaillog.data

import anduin.fundsub.auditlog.AuditLogFilter.TimestampFilter
import com.anduin.stargazer.fundsub.module.investor.settings.auditlog.Filter
import com.anduin.stargazer.fundsub.module.investor.settings.auditlog.Filter.TextFilterItem

private[emaillog] final case class EmailLogFilterParams(
  dateRangeFilterItemOpt: Option[TextFilterItem[TimestampFilter]] = Option(Filter.defaultRangeFilterOption),
  recipientEmail: String = ""
) {

  lazy val filterCount: Int = {
    (if (dateRangeFilterItemOpt.nonEmpty && !dateRangeFilterItemOpt.contains(Filter.defaultRangeFilterOption)) 1
     else 0) +
      (if (recipientEmail.nonEmpty) 1 else 0)
  }

}
