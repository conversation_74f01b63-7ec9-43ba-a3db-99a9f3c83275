// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.contact.widget.tab

import anduin.contact.endpoint.{ContactGroupModel, ContactModel}
import anduin.contact.widget.ContactWidgetUtils
import anduin.contact.widget.component.FilterView.{FilterType, GroupFilterProperties}
import anduin.contact.widget.component.{FilterView, GroupTable}
import anduin.fundsub.endpoint.contact.{GetAllGroupParams, GetGroupContactsParams}
import anduin.id.contact.{ContactGroupId, ContactId}
import anduin.id.fundsub.FundSubId
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[widget] final case class GroupTabView(
  fundSubId: FundSubId,
  isVisible: Boolean,
  selectedContacts: Set[ContactId],
  contactMap: Map[ContactId, ContactModel],
  groupMap: Map[ContactGroupId, ContactGroupModel],
  onContactSelected: Set[ContactId] => Callback,
  onContactRemoved: Set[ContactId] => Callback,
  onContactModelsLoaded: Seq[ContactModel] => Callback,
  onGroupModelsLoaded: Seq[ContactGroupModel] => Callback
) {
  def apply(): VdomElement = GroupTabView.component(this)
}

object GroupTabView {

  type Props = GroupTabView

  private val GroupPerPage: Int = 20

  final case class State(
    allGroupsOpt: Option[Seq[ContactGroupModel]] = None,
    displayCount: Int = GroupPerPage,
    isFetching: Boolean = false,
    filterProps: GroupFilterProperties = GroupFilterProperties(None, Seq.empty),
    searchResults: Seq[ContactGroupModel] = Seq.empty,
    loadedGroups: Set[ContactGroupId] = Set.empty // All groups that it's contacts were loaded
  ) {

    val allGroups: Seq[ContactGroupModel] = allGroupsOpt
      .getOrElse(Seq.empty)
      .filter(group => group.groupInfo.groupName.nonEmpty && group.contacts.nonEmpty)
      .sortBy(_.groupInfo.groupName)

    val displayGroups: Seq[ContactGroupModel] =
      ContactWidgetUtils.filterGroups(
        allGroups,
        filterProps,
        searchResults
      )

  }

  final class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomNode = {
      val displayCount = if (state.isFetching) {
        (state.displayCount - GroupPerPage).max(0)
      } else {
        state.displayCount
      }
      <.div(
        tw.flex.flexCol.flexFill,
        TagMod.unless(props.isVisible)(tw.hidden),
        FilterView(
          filterProperties = state.filterProps,
          groupMap = props.groupMap,
          onFilterRemoved = removeGroupFilter,
          onSearchRemoved = removeGroupSearch()
        )(),
        GroupTable(
          displayGroups = state.displayGroups.take(displayCount),
          isFetching = state.isFetching,
          contactMap = props.contactMap,
          showLoadMore = state.displayGroups.size > state.displayCount,
          onLoadMore = loadMoreGroups(),
          selectedContacts = props.selectedContacts,
          onContactsSelected = contacts => {
            props.onContactSelected(contacts)
          },
          onContactsRemoved = contacts => {
            props.onContactRemoved(contacts)
          }
        )()
      )
    }

    private def removeGroupFilter(filter: FilterType) = {
      scope.modState { s =>
        s.copy(
          filterProps = s.filterProps.copy(
            filters = s.filterProps.filters.filterNot(_ == filter)
          )
        )
      }
    }

    private def removeGroupSearch() = {
      scope.modState { s =>
        s.copy(filterProps = s.filterProps.copy(search = None), displayCount = GroupPerPage)
      }
    }

    private def loadMoreGroups() = {
      for {
        _ <- scope.modState(
          s => s.copy(displayCount = s.displayCount + GroupPerPage),
          loadNewDisplayGroupsContacts()
        )
      } yield ()
    }

    private def loadNewDisplayGroupsContacts() = {
      for {
        props <- scope.props
        state <- scope.state
        newLoadedGroups = state.displayGroups.take(state.displayCount).filterNot { group =>
          state.loadedGroups.contains(group.groupId)
        }
        _ <- Callback.when(newLoadedGroups.nonEmpty) {
          for {
            _ <- startFetching()
            _ <- ZIOUtils.toReactCallback {
              FundSubEndpointClient
                .getGroupContacts(
                  GetGroupContactsParams(
                    props.fundSubId,
                    newLoadedGroups.map(_.groupId)
                  )
                )
                .map {
                  _.fold(
                    _ => Toast.errorCallback("Failed to fetch groups") >> stopFetching(),
                    resp =>
                      scope.modState(
                        s =>
                          s.copy(
                            isFetching = false,
                            loadedGroups = s.loadedGroups ++ newLoadedGroups.map(_.groupId)
                          ),
                        props.onContactModelsLoaded(resp.contacts.values.flatten.toSeq)
                      )
                  )
                }
            }
          } yield ()
        }
      } yield ()
    }

    private def startFetching(): Callback = {
      scope.modState(_.copy(isFetching = true))
    }

    private def stopFetching(): Callback = {
      scope.modState(_.copy(isFetching = false))
    }

    def loadGroupInitialData(): Callback = {
      for {
        props <- scope.props
        _ <- scope.modState(_.copy(isFetching = true, displayCount = GroupPerPage))
        _ <- ZIOUtils.toReactCallback {
          FundSubEndpointClient.getAllGroups(GetAllGroupParams(props.fundSubId)).map {
            _.fold(
              _ => Toast.errorCallback("Failed to fetch groups"),
              resp =>
                scope.modState(
                  s =>
                    s.copy(
                      allGroupsOpt = Some(resp.groups),
                      isFetching = false
                    ),
                  loadNewDisplayGroupsContacts() >> props.onGroupModelsLoaded(resp.groups)
                )
            )
          }
        }
      } yield ()
    }

    def setFilterAndSearchResult(filterProps: GroupFilterProperties, searchResult: Seq[ContactGroupModel]): Callback = {
      scope.modState(
        _.copy(
          filterProps = filterProps,
          searchResults = searchResult,
          displayCount = GroupPerPage
        ),
        loadNewDisplayGroupsContacts()
      )
    }

  }

  val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .componentDidUpdate { scope =>
      // Load initial group data when user switch to this tab on the first time
      val tabSwitched = scope.currentProps.isVisible && !scope.prevProps.isVisible
      val initialDataEmpty = scope.currentState.allGroupsOpt.isEmpty && !scope.currentState.isFetching
      Callback.when(tabSwitched && initialDataEmpty) {
        scope.backend.loadGroupInitialData()
      }
    }
    .build

}
