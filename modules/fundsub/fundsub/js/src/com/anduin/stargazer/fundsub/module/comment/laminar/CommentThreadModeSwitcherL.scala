// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar

import com.raquo.laminar.api.L.*
import design.anduin.components.icon.Icon
import design.anduin.components.segment.laminar.SegmentL
import design.anduin.style.tw.*

import anduin.stargazer.service.formcomment.FormCommentCommons.CommentVisibilityType

private[fundsub] case class CommentThreadModeSwitcherL(
  isPublicSignal: Signal[Boolean],
  onSwitchMode: Observer[CommentVisibilityType]
) {

  def apply(): HtmlElement = {
    div(
      tw.flex,
      child <-- isPublicSignal.splitOne(identity) { case (_, initialIsPublic, _) =>
        SegmentL(
          initialActiveItem = if (initialIsPublic) 1 else 0,
          items = Seq(
            SegmentL.Item(
              icon = Option(Icon.Glyph.CommentLineLock),
              renderContent = () => "Internal"
            ),
            SegmentL.Item(
              icon = Option(Icon.Glyph.CommentLine),
              renderContent = () => "Shared"
            )
          ),
          onClick = Observer[Int] { index =>
            onSwitchMode.onNext(
              if (index == 1) {
                CommentVisibilityType.PublicComment
              } else {
                CommentVisibilityType.InternalComment
              }
            )
          }
        )()
      }
    )
  }

}
