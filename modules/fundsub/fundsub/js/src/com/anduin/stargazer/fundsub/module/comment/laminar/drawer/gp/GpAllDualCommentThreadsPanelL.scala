// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.drawer.gp

import java.time.Instant

import com.raquo.airstream.core.Signal
import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.divider.Divider
import design.anduin.components.divider.laminar.DividerL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.menu.laminar.{MenuItemL, MenuL}
import design.anduin.components.nonidealstate.laminar.NonIdealStateL
import design.anduin.components.popover.laminar.PopoverL
import design.anduin.components.portal.PortalPosition
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*

import anduin.fundsub.utils.FormCommentUtils
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.id.notification.NotificationId
import anduin.model.common.user.UserId
import anduin.model.id.TeamId
import anduin.stargazer.service.formcomment.FormCommentCommons.{CommentThread, DualCommentThread}
import com.anduin.stargazer.fundsub.module.comment.laminar.drawer.{
  CommentDrawerUIModels,
  FormCommentDrawerEmptyState,
  MarkAllCommentsAsReadButton
}
import com.anduin.stargazer.fundsub.module.comment.laminar.share.OpenResolvedCommentSwitcherL
import com.anduin.stargazer.fundsub.module.comment.laminar.share.gp.DualThreadsListSkeletonL
import com.anduin.stargazer.fundsub.module.comment.models.{CommentTarget, CommentsData}
import com.anduin.stargazer.service.account.AccountUtils

private[gp] case class GpAllDualCommentThreadsPanelL(
  lpIdSignal: Signal[FundSubLpId],
  amlKycNamesSignal: Signal[Option[List[String]]],
  commentsDataSignal: Signal[CommentsData],
  targetSignal: Signal[CommentTarget],
  userGPTeamIdOptSignal: Signal[Option[TeamId]],
  isCommentMentioningEnabledSignal: Signal[Boolean],
  inactiveDaysThresholdSignal: Signal[Option[Int]],
  isCommentAssignmentEnabledSignal: Signal[Boolean],
  isHiddenFieldThread: String => Signal[Boolean],
  onClose: Observer[Unit],
  onJumpToTarget: Observer[String],
  onSwitchToView: Observer[CommentDrawerUIModels.DrawerView],
  fundSubId: FundSubId
) {

  private val isResolvedTabVar: Var[Boolean] = Var(false)

  private val isResolvedTabSignal: Signal[Boolean] = isResolvedTabVar.signal

  private val showOnlyMentionsOfCurrentUserVar: Var[Boolean] = Var(false)

  private val isCommentDataLoadingSignal = commentsDataSignal.map(_.isLoading)

  private val isCommentThreadsEmptySignal = commentsDataSignal.map(_.threads.isEmpty)

  private val unreadThreadIdsSignal = commentsDataSignal
    .map(commentsData =>
      (commentsData.newThreadIds.keys ++
        commentsData.issueWithNewCommentIds.keys).toSet
    )
    .distinct

  private def aggregateThreadsIntoAnchorPoints(
    commentThreads: List[CommentThread],
    inactiveDaysThreshold: Option[Int]
  ): List[DualCommentThread] = {
    val groupedCommentThreads = commentThreads
      .groupBy(thread => FormCommentUtils.refineTopicData(thread.topicData))
      .toSeq

    val now = Instant.now()
    groupedCommentThreads
      .map { case (_, threads) =>
        val internalCommentThreadOpt = threads.find(_.isPublic.equals(false))
        val sharedCommentThreadOpt = threads.find(_.isPublic)
        val topicData = threads.head.topicData
        val fundSubLpId = threads.head.fundSubLpId

        DualCommentThread(
          lpId = fundSubLpId,
          targetIsHidden = false, // not used
          internalCommentThreadOpt = internalCommentThreadOpt,
          sharedCommentThreadOpt = sharedCommentThreadOpt,
          topicData = topicData,
          assigneeGpIdOpt =
            None // internal component will need to fetch this data, TODO @voxuannguyen2001: remove this hack
        )
      }
      .distinct
      .toList
      .sortWith { case (l, r) =>
        val lIndex = CommentThread.getDualThreadSortIndex(
          internalThreadOpt = l.internalCommentThreadOpt,
          sharedThreadOpt = l.sharedCommentThreadOpt,
          inactiveDaysThreshold = inactiveDaysThreshold,
          now = now
        )
        val rIndex = CommentThread.getDualThreadSortIndex(
          internalThreadOpt = r.internalCommentThreadOpt,
          sharedThreadOpt = r.sharedCommentThreadOpt,
          inactiveDaysThreshold = inactiveDaysThreshold,
          now = now
        )
        lIndex.isBefore(rIndex)
      }
  }

  private val filteredThreadsSignal = Signal
    .combine(
      commentsDataSignal,
      showOnlyMentionsOfCurrentUserVar.signal,
      AccountUtils.currentUserIdObs,
      userGPTeamIdOptSignal
    )
    .distinct
    .map {
      case (
            commentsData,
            showOnlyMentionsOfCurrentUserFilter,
            currentUserIdOpt,
            currentUserGPTeamIdOpt
          ) =>
        commentsData.threads
          .filter(thread =>
            checkMatchedThread(
              thread,
              showOnlyMentionsOfCurrentUserFilter,
              currentUserIdOpt,
              currentUserGPTeamIdOpt
            )
          )
          .sortBy(_.lastComment.createdAt)
          .reverse
    }
    .distinct

  private val anchorPointsSignal =
    filteredThreadsSignal.combineWith(inactiveDaysThresholdSignal).map { case (threads, inactiveDaysThreshold) =>
      aggregateThreadsIntoAnchorPoints(threads, inactiveDaysThreshold)
    }

  private val filteredAnchorPointsSignal =
    anchorPointsSignal
      .combineWith(isResolvedTabSignal)
      .map { case (anchorPoints, isResolved) =>
        anchorPoints.filter(_.isCompleted == isResolved)
      }
      .map(
        _.sortBy { duelCommentThread =>
          duelCommentThread.getLatestCommentTime.map(_.toEpochMilli).getOrElse(0L)
        }(
          using Ordering.Long.reverse
        )
      )

  private val completedTabUnreadItemsCountSignal =
    filteredThreadsSignal.combineWith(unreadThreadIdsSignal).map { case (threads, unreadIssueIds) =>
      threads.count(thread => thread.isResolved && unreadIssueIds.contains(thread.id))
    }

  private val openTabUnreadItemsCountSignal =
    filteredThreadsSignal.combineWith(unreadThreadIdsSignal).map { case (threads, unreadIssueIds) =>
      threads.count(thread => !thread.isResolved && unreadIssueIds.contains(thread.id))
    }

  private val newCommentNotificationIdsSignal: Signal[List[NotificationId]] = commentsDataSignal.map { data =>
    data.newThreadIds.values.toList.flatten ++ data.newCommentIds.values.toList.flatten
  }

  private def renderFilterMenuItems = {
    MenuL(
      List(
        div(
          child <-- showOnlyMentionsOfCurrentUserVar.signal.map { showOnlyMentionsOfCurrentUserFilter =>
            MenuItemL(
              isSelected = showOnlyMentionsOfCurrentUserFilter,
              onClick = Observer[Unit] { _ =>
                showOnlyMentionsOfCurrentUserVar.set(!showOnlyMentionsOfCurrentUserFilter)
              },
              icon = Some(Icon.Glyph.Blank)
            )("Only mentions of me")
          }
        )
      )
    )
  }

  private def renderHeader = {
    div(
      tw.px16.pt16.flex.itemsCenter,
      div(tw.heading2, "Comments"),
      renderCloseButtonForMobile
    )
  }

  private def renderCloseButtonForMobile = {
    div(
      tw.flex.justifyEnd,
      tw.sm(tw.hidden),
      DividerL(direction = Divider.Direction.Vertical)(),
      ButtonL(
        testId = "CloseAllCommentsPanelButton",
        style = ButtonL.Style.Full(
          height = ButtonL.Height.Fix32,
          icon = Option(Icon.Glyph.Cross)
        ),
        onClick = onClose.contramap(_ => ())
      )()
    )
  }

  private def renderMarkAllAsReadButton = {
    val shouldRenderBtnSignal = newCommentNotificationIdsSignal.map(
      _.nonEmpty
    ) && false // Todo @voxuannguyen2001 need to implement refetch mechanism here before showing this button
    child <-- shouldRenderBtnSignal.splitBoolean(
      whenTrue = _ =>
        div(
          tw.mr8,
          MarkAllCommentsAsReadButton(
            newCommentNotificationIdsSignal,
            onMarkAsSeen = Observer.empty
          )()
        ),
      whenFalse = _ => emptyNode
    )

  }

  private def renderFilterMentionsOfMeButton = {
    val isUsingFilterSignal = showOnlyMentionsOfCurrentUserVar.signal
    child <-- isCommentMentioningEnabledSignal.splitBoolean(
      whenTrue = _ =>
        PopoverL(
          renderContent = _ => {
            renderFilterMenuItems
          },
          renderTarget = (open, isOpened) => {
            TooltipL(
              renderContent = _.amend("Filter"),
              renderTarget = {
                div(
                  ComponentUtils.testIdL("CommentFilterButton"),
                  ButtonL(
                    style = ButtonL.Style.Minimal(
                      icon = Option(Icon.Glyph.Filter),
                      color = ButtonL.Color.Gray9,
                      isSelected = isOpened
                    ),
                    onClick = Observer { _ => open.onNext(()); }
                  )().amend(
                    tw.bgPrimary2.borderAll.border1.textPrimary4 <-- isUsingFilterSignal
                  )
                )
              }
            )()
          },
          position = PortalPosition.BottomCenter
        )(),
      whenFalse = _ => emptyNode
    )
  }

  private def checkMatchedThread(
    thread: CommentThread,
    showOnlyMentionsOfCurrentUserFilter: Boolean,
    currentUserIdOpt: Option[UserId],
    currentUserGPTeamIdOpt: Option[TeamId]
  ) = {
    val matchedMentionType = if (showOnlyMentionsOfCurrentUserFilter) {
      currentUserIdOpt.exists(thread.isUserMentioned) || currentUserGPTeamIdOpt.exists(thread.isGpTeamMentioned)
    } else {
      true
    }
    matchedMentionType
  }

  def apply(): HtmlElement = {
    div(
      ComponentUtils.testIdL(GpAllDualCommentThreadsPanelL),
      tw.flex.flexCol.hPc100.overflowHidden,
      renderHeader,
      div(
        tw.flexFill,
        ComponentUtils.testIdL(GpAllDualCommentThreadsPanelL, "Body"),
        child <-- isCommentDataLoadingSignal.splitBoolean(
          whenTrue = _ => DualThreadsListSkeletonL()(),
          whenFalse = _ =>
            div(
              tw.hPc100,
              child <-- (isCommentThreadsEmptySignal && targetSignal.map(_ == CommentTarget.Form)).splitBoolean(
                whenTrue = _ => FormCommentDrawerEmptyState()(),
                whenFalse = _ => renderNonEmptyState
              )
            )
        )
      )
    )
  }

  private def renderEmptyTab = {
    div(
      height.px := 500,
      NonIdealStateL(
        icon = {
          div(
            tw.textGray4,
            IconL(
              name = Val(Icon.Glyph.Comment),
              size = Icon.Size.Custom(px = 48)
            )()
          )
        },
        title = span(tw.textGray7.fontMedium.text15.leading24, "No comments yet"),
        description = emptyNode
      )().amend(
        tw.hPc100.wPc100.flex.itemsCenter.justifyCenter.textCenter.px48
      )
    )
  }

  private def renderAnchorPointItem(dualCommentThreadSignal: Signal[DualCommentThread]) = {
    val fieldSignal =
      dualCommentThreadSignal.map(_.topicData.asMessage.sealedValue.formQuestionData.map(_.fieldAlias)).distinct
    val doctypeSignal =
      dualCommentThreadSignal.map(_.topicData.asMessage.sealedValue.amlKycDocData.map(_.doctype)).distinct
    val isHiddenSignal = fieldSignal.combineWith(doctypeSignal).flatMapSwitch { case (fieldOpt, doctypeOpt) =>
      fieldOpt.fold[Signal[Boolean]] {
        val doctype = doctypeOpt.getOrElse("")
        amlKycNamesSignal.map { namesListOpt =>
          !namesListOpt.exists(
            _.exists(_.equals(doctype))
          )
        }
      } { field =>
        isHiddenFieldThread(field)
      }
    }
    DrawerDualThreadSummaryItemL(
      dualCommentThreadSignal = dualCommentThreadSignal,
      hasNewSharedThreadNotiSignal = unreadThreadIdsSignal
        .combineWith(
          dualCommentThreadSignal
        )
        .map { case (issueIds, dualCommentThread) =>
          dualCommentThread.sharedCommentThreadOpt
            .exists(thread => issueIds.contains(thread.id))
        },
      hasNewInternalThreadNotiSignal = unreadThreadIdsSignal
        .combineWith(
          dualCommentThreadSignal
        )
        .map { case (issueIds, dualCommentThread) =>
          dualCommentThread.internalCommentThreadOpt
            .exists(thread => issueIds.contains(thread.id))
        },
      inactiveThresholdSignal = inactiveDaysThresholdSignal,
      onViewAnchorPoint = Observer[(DualCommentThread, Boolean)] { case (dualCommentThread, isInternalComment) =>
        val commentThreadOpt = if (isInternalComment) {
          dualCommentThread.internalCommentThreadOpt
        } else {
          dualCommentThread.sharedCommentThreadOpt
        }
        commentThreadOpt.foreach { thread =>
          val currentThreadTopicData = thread.topicData
          val toIsPublic = thread.isPublic
          val view = if (currentThreadTopicData.asMessage.sealedValue.isFormQuestionData) {
            CommentDrawerUIModels.DrawerView
              .Field(currentThreadTopicData.asMessage.sealedValue.formQuestionData.get, toIsPublic)
          } else {
            CommentDrawerUIModels.DrawerView
              .Doctype(currentThreadTopicData.asMessage.sealedValue.amlKycDocData.get.doctype, toIsPublic)
          }
          onSwitchToView.onNext(view)
        }
      },
      isHiddenSignal = isHiddenSignal,
      isCommentAssignmentEnabledSignal = isCommentAssignmentEnabledSignal,
      onJumpToTarget = onJumpToTarget,
      lpIdSignal = lpIdSignal,
      isResolvedTabSignal = isResolvedTabSignal,
      fundSubId = fundSubId
    )()
  }

  private def renderDualThreadSummaryItemList = {
    div(
      ComponentUtils.testIdL(GpAllDualCommentThreadsPanelL, "ThreadsContainer"),
      tw.flexFill.overflowAuto.borderTop.borderGray3,
      child <-- filteredAnchorPointsSignal
        .map(_.isEmpty)
        .splitBoolean(
          whenTrue = _ => renderEmptyTab,
          whenFalse = _ => emptyNode
        ),
      children <-- filteredAnchorPointsSignal.split(anchorPoint => (anchorPoint.lpId, anchorPoint.topicData)) {
        case (_, _, anchorPointSignal) =>
          renderAnchorPointItem(anchorPointSignal)
      }
    )

  }

  private def renderNonEmptyState: HtmlElement = {
    div(
      ComponentUtils.testIdL(GpAllDualCommentThreadsPanelL),
      tw.flex.flexCol.hPc100,
      renderSwitcherAndActions,
      renderDualThreadSummaryItemList
    )
  }

  private def renderSwitcherAndActions = {
    div(
      tw.p16.flex.itemsCenter.justifyBetween,
      OpenResolvedCommentSwitcherL(
        isResolvedTabSignal = isResolvedTabSignal,
        onSwitchToResolvedTab = isResolvedTabVar.writer,
        unreadOpenCommentsSignal = openTabUnreadItemsCountSignal,
        unreadResolvedCommentsSignal = completedTabUnreadItemsCountSignal
      )(),
      div(
        tw.flex.itemsCenter.spaceX8,
        renderMarkAllAsReadButton,
        renderFilterMentionsOfMeButton
      )
    )

  }

}
