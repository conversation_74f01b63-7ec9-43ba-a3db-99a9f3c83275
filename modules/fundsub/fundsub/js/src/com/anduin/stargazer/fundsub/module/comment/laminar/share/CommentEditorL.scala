// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.share

import anduin.fundsub.utils.FormCommentUtils
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import org.scalajs.dom
import org.scalajs.dom.{HTMLParagraphElement, KeyboardEvent}
import com.raquo.laminar.api.L.*
import design.anduin.components.avatar.InitialAvatar
import design.anduin.components.avatar.laminar.InitialAvatarL
import design.anduin.components.editor.{Editor, OnChangeData}
import design.anduin.components.editor.laminar.{MentionL, RichEditorL}
import design.anduin.components.menu.laminar.MenuDividerL
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.components.util.{ComponentUtils, NodeListSeq}
import design.anduin.components.icon.laminar.IconL
import anduin.id.fundsub.FundSubId
import anduin.id.fundsub.FundSubLpId
import anduin.stargazer.service.formcomment.FormCommentCommons.MentionInfo
import anduin.stargazer.service.formcomment.FormCommentCommons.MentionInfo.{MentionedGroupInfo, MentionedUserInfo}
import anduin.model.common.user.UserId
import anduin.scalajs.dompurify.{DomPurify, SanitizeOptions}
import design.anduin.components.icon.Icon
import com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent.FetchMentionableListL

private[laminar] case class CommentEditorL(
  currentUserId: UserId,
  initialComment: String,
  fundSubLpIdSignal: Signal[FundSubLpId],
  isPublicSignal: Signal[Boolean],
  showCancelButton: Boolean,
  onCancel: Observer[Unit] = Observer.empty,
  onSave: Observer[String],
  placeholder: String,
  postButtonLabel: String,
  isCommentMentioningEnabledSignal: Signal[Boolean],
  isBlockedSignal: Signal[Boolean],
  isSavingSignal: Signal[Boolean],
  enableLpAutoNotificationOptSignal: Signal[Option[Boolean]],
  isDrawerViewSignal: Signal[Boolean] // Todo voxuannguyen2001: remove this ugly hack
) {

  private val commentVar = Var(initialComment)
  private val commentLengthVar = Var(0)
  private val saveCommentEventBus = new EventBus[Unit]
  private val filteredFirstMentionItemsInGroupsVar = Var(Seq.empty[MentionInfo])

  private def handleKeyDown(e: KeyboardEvent) = {
    if (e.key == "Enter" && !e.shiftKey) {
      e.preventDefault()
      saveCommentEventBus.emit(())
    }
  }

  private def renderEditor(fundSubId: FundSubId) = {
    FetchMentionableListL(
      fundSubId = fundSubId,
      lpIdSignal = fundSubLpIdSignal,
      isPublicSignal = isPublicSignal,
      renderChildren = mentionableListRenderProps => {
        val mentionableListSignal: Signal[Seq[MentionedGroupInfo]] =
          mentionableListRenderProps.dataSignal.map(_.mentionableList)

        RichEditorL(
          autoFocus = true,
          initialValue = initialComment,
          pasteTransformer = Val(Editor.PasteTransformer.RawText),
          placeholder = placeholder,
          onEnter = Option(
            RichEditorL.OnEnter(
              disableDefaultHandler = true,
              onHandle = Observer { _ => saveCommentEventBus.emit(()) }
            )
          ),
          onChange = Observer[OnChangeData] { data =>
            commentVar.set(data.value)
          },
          render = renderEditorProps => {
            // Each group: MentionedGroupInfo, MentionedUserInfo1, MentionedUserInfo2, ...
            val participantsSignal: Signal[Seq[MentionInfo]] =
              mentionableListSignal.map(
                _.flatMap(groupInfo =>
                  (if (groupInfo.isGPGroup) Seq(groupInfo) else Seq.empty)
                    ++ groupInfo.members.getOrElse(Seq.empty)
                )
              )

            MentionL[MentionInfo](
              content = MentionL.Content[MentionInfo](
                filterItemList = Option { filterData =>
                  val filteredItems = filterData.items.filter { item =>
                    val displayName = item.value.getDisplayName
                    displayName.toLowerCase.contains(filterData.keyword.trim.toLowerCase)
                  }
                  // extract filtered items that has group role different from its previous item
                  filteredFirstMentionItemsInGroupsVar.set(
                    filteredItems.headOption.fold(
                      Seq()
                    ) { firstItem =>
                      Seq(firstItem.value) ++
                        filteredItems
                          .sliding(2)
                          .collect {
                            case Seq(prev, current) if current.value.getGroupRole != prev.value.getGroupRole =>
                              current.value
                          }
                    }
                  )
                  filteredItems
                },
                renderItem = Option { renderProps =>
                  // Determine the group of participant
                  val participant = renderProps.item.value
                  val groupOptSignal =
                    mentionableListSignal.map(_.find(_.getGroupRole.contains(participant.getGroupRole)))
                  val isFirstItemSignal = filteredFirstMentionItemsInGroupsVar.signal.map(
                    _.exists(firstMentionOfAGroup =>
                      // prevent the case where LP is also a GP
                      firstMentionOfAGroup.toIdString.equals(
                        participant.toIdString
                      ) && firstMentionOfAGroup.getGroupRole.equals(participant.getGroupRole)
                    )
                  )

                  div(
                    child.maybe <-- isFirstItemSignal.map { isFirstItem =>
                      Option.when(isFirstItem) {
                        div(
                          // Don't show the top border for the first item
                          Option.when(renderProps.index > 0) {
                            MenuDividerL()
                          },
                          // Don't show the top border for the first item
                          child.maybe <-- groupOptSignal.distinct.map {
                            _.filter(group => !group.isGPGroup).map { group =>
                              div(
                                tw.text13.leading16.textGray8.fontSemiBold.px12.py8,
                                group.groupName
                              )
                            }
                          }
                        )
                      }
                    },
                    renderProps.defaultItemRender
                  )
                },
                renderItemBody = Option { item =>
                  TooltipL(
                    renderContent = _.amend(item.value match {
                      case MentionedUserInfo(_, _, _, emailAddress) => emailAddress
                      case MentionedGroupInfo(_, _, _, groupName)   => groupName
                    }),
                    renderTarget = {
                      div(
                        tw.flex.itemsCenter.spaceX8,
                        item.value match {
                          case MentionedUserInfo(_, _, _, _) =>
                            InitialAvatarL(
                              id = item.value.toIdString,
                              initials = Val(item.value.getDisplayName.split(" ").take(2).map(_.take(1)).mkString),
                              size = InitialAvatar.Size.Px24
                            )()
                          case MentionedGroupInfo(_, _, _, _) =>
                            div(
                              tw.wPx24.hPx24.flex.itemsCenter.justifyCenter,
                              IconL(name = Val(Icon.Glyph.UserGroupLarge), size = Icon.Size.Px16)()
                            )
                        },
                        div(
                          item.value.getDisplayName,
                          Option.when(item.value.toIdString.equals(currentUserId.idString)) {
                            span(tw.textGray6, " (you)")
                          }
                        )
                      )
                    }
                  )()
                }
              ),
              editorInstance = renderEditorProps.editorInstance,
              editorNode = renderEditorProps.editorNode,
              itemsSignal = participantsSignal.map(_.map(participant => MentionL.Item(value = participant))),
              readOnly = renderEditorProps.readOnly,
              valueToString = _.getDisplayName,
              valueToId = _.toIdString,
              render = renderProps => {
                div(
                  div(
                    tw.borderAll.borderGray3.border1.mb8,
                    // Editor content
                    renderEditorProps.editorNode.amend(
                      height.px := 100,
                      renderProps.renderContent
                    )
                  ),
                  div(
                    tw.flex.itemsCenter,
                    div(
                      tw.mrAuto,
                      TooltipL(
                        renderContent = _.amend("Mention someone"),
                        renderTarget = {
                          ButtonL(
                            testId = "MentionBtn",
                            style = ButtonL.Style.Full(height = ButtonL.Height.Fix24),
                            onClick = Observer[dom.MouseEvent] { _ =>
                              renderProps.onShowSuggestions.onNext(())
                            }
                          )("@")
                        }
                      )()
                    ),
                    renderLpAutoNotificationMessage,
                    renderRemainingCharacter,
                    commentVar.signal.distinct --> Observer[String] { comment =>
                      if (comment.isEmpty) {
                        // Clear the editor content
                        renderProps.onClearContents.onNext(())
                        renderProps.onFocus.onNext(())
                      }
                      // update comment raw text length
                      val rawCommentText = DomPurify
                        .sanitize(
                          comment,
                          SanitizeOptions(allowedTags = Some(List.empty), allowedAttributes = Some(List.empty))
                        )
                      commentLengthVar.set(rawCommentText.length)
                    },
                    if (showCancelButton) {
                      div(
                        tw.mr8,
                        ButtonL(
                          testId = "CancelBtn",
                          style = ButtonL.Style.Full(color = ButtonL.Color.Gray0, height = ButtonL.Height.Fix24),
                          onClick = Observer[dom.MouseEvent] { _ =>
                            onCancel.onNext(())
                          }
                        )("Cancel")
                      )
                    } else {
                      emptyNode
                    },
                    ButtonL(
                      testId = "PostButton",
                      isDisabled = commentLengthVar.signal.combineWith(isSavingSignal, isBlockedSignal).map {
                        case (commentLength, isSaving, isBlocked) =>
                          isBlocked || commentLength <= 0 || isSaving || commentLength > FormCommentUtils.MaxCommentLength
                      },
                      style = ButtonL.Style.Full(
                        color = ButtonL.Color.Primary,
                        height = ButtonL.Height.Fix24,
                        isBusy = isSavingSignal
                      ),
                      onClick = Observer[dom.MouseEvent] { _ =>
                        saveCommentEventBus.emit(())
                      }
                    )(postButtonLabel)
                  )
                )
              },
              renderClickedMentionPopover = Option { renderProps =>
                div(
                  child <-- participantsSignal.distinct.map {
                    _.find(participant => participant.toIdString == renderProps.id && participant.nonEmpty)
                      .fold(emptyNode) { participant =>
                        ParticipantPopoverBody(participant)()
                      }
                  }
                )
              }
            )()
          }
        )().amendThis { thisNode =>
          val node = thisNode.ref
          commentVar.signal.distinct --> Observer[String] { comment =>
            NodeListSeq(node.querySelectorAll("p")).foreach {
              case ele: HTMLParagraphElement =>
                ele.style.margin = "0px"
              case _ =>
                ()
            }
          }
        }
      }
    )()
  }

  private def renderCommentEditorWithoutMentioning() = {
    div(
      commentVar.signal.map(_.length) --> commentLengthVar.writer,
      div(
        tw.mb8,
        TextBoxL(
          testId = "CommentTextBox",
          isAutoFocus = true,
          value = commentVar.signal,
          tpe = TextBoxL.Tpe.Area(rows = 3),
          onChange = commentVar.writer,
          placeholder = placeholder,
          onKeyDown = Observer[KeyboardEvent](handleKeyDown)
        )()
      ),
      div(
        tw.flex.justifyEnd,
        CommentLengthWarningRenderer(
          commentLengthSignal = commentLengthVar.signal,
          renderTarget = div(
            tw.flex.itemsCenter,
            renderRemainingCharacter,
            if (showCancelButton) {
              div(
                tw.mr8,
                ButtonL(
                  testId = "CancelBtn",
                  style = ButtonL.Style.Full(color = ButtonL.Color.Gray0, height = ButtonL.Height.Fix24),
                  onClick = Observer[dom.MouseEvent] { _ =>
                    onCancel.onNext(())
                  }
                )("Cancel")
              )
            } else {
              emptyNode
            },
            ButtonL(
              testId = "PostButton",
              isDisabled = commentLengthVar.signal.combineWith(isSavingSignal, isBlockedSignal).map {
                case (commentLength, isSaving, isBlocked) =>
                  isBlocked || commentLength <= 0 || isSaving || commentLength > FormCommentUtils.MaxCommentLength
              },
              style = ButtonL.Style.Full(
                color = ButtonL.Color.Primary,
                height = ButtonL.Height.Fix24,
                isBusy = isSavingSignal
              ),
              onClick = Observer[dom.MouseEvent] { _ =>
                saveCommentEventBus.emit(())
              }
            )(postButtonLabel)
          )
        )()
      )
    )

  }

  private def renderRemainingCharacter = {
    child <-- commentVar.signal.map(_.length).distinct.map { commentLength =>
      val remainingCharacter = FormCommentUtils.MaxCommentLength - commentLength
      div(
        tw.textSmall.mr8,
        if (remainingCharacter < 0) {
          tw.textDanger5
        } else if (remainingCharacter == 0) {
          tw.textWarning5
        } else {
          tw.textGray7
        },
        remainingCharacter
      )
    }
  }

  def apply(): HtmlElement = {
    div(
      ComponentUtils.testIdL(CommentEditorL),
      saveCommentEventBus.events.withCurrentValueOf(
        commentVar.signal,
        commentLengthVar.signal,
        isBlockedSignal,
        isSavingSignal
      ) --> Observer[(String, Int, Boolean, Boolean)] { case (comment, commentLength, isBlocked, isSaving) =>
        val sanitizedComment = DomPurify.sanitize(
          comment,
          SanitizeOptions(
            allowedTags = Option(List("span", "p")),
            allowedAttributes = Option(List("class", "data-id"))
          )
        )
        if (
          sanitizedComment.nonEmpty && commentLength > 0 && commentLength <= FormCommentUtils.MaxCommentLength && !isSaving && !isBlocked
        ) {
          onSave.onNext(sanitizedComment)
          commentVar.set("")
        }
      },
      child <-- isCommentMentioningEnabledSignal.combineWith(fundSubLpIdSignal).distinct.map {
        case (isCommentMentioningEnabled, lpId) =>
          if (isCommentMentioningEnabled) {
            CommentLengthWarningRenderer(
              commentLengthSignal = commentLengthVar.signal,
              renderTarget = renderEditor(lpId.parent)
            )()
          } else {
            renderCommentEditorWithoutMentioning()
          }
      }
    )
  }

  private def renderLpAutoNotificationFullWarningMessage(enableAutoNotificationSignal: Signal[Boolean]) = {
    val notificationIconSignal = enableAutoNotificationSignal.map {
      if (_) {
        Icon.Glyph.Bell
      } else {
        Icon.Glyph.BellOff
      }
    }
    val messageSignal = enableAutoNotificationSignal.map {
      if (_) {
        "This investor is receiving auto-notifications"
      } else {
        "This investor is not receiving auto-notifications"
      }
    }
    div(
      ComponentUtils.testIdL("LpAutoNotificationWarningMessage"),
      tw.flex.itemsCenter.spaceX8.mx8,
      div(
        tw.flex.itemsCenter.spaceX4,
        IconL(name = notificationIconSignal, size = Icon.Size.Custom(12))().amend(tw.textGray6),
        div(tw.textGray7.textSmall, text <-- messageSignal)
      ),
      div(
        tw.wPx1.bgGray3.hPx20
      )
    )
  }

  private def renderLpAutoNotificationShortWarningMessage(enableAutoNotificationSignal: Signal[Boolean]) = {
    val notificationIconSignal = enableAutoNotificationSignal.map {
      if (_) {
        Icon.Glyph.Bell
      } else {
        Icon.Glyph.BellOff
      }
    }
    val tooltipMessageSignal = enableAutoNotificationSignal.map {
      if (_) {
        "This investor is receiving auto-notifications"
      } else {
        "This investor is not receiving auto-notifications"
      }
    }
    div(
      ComponentUtils.testIdL("LpAutoNotificationShortWarningMessage"),
      tw.flex.itemsCenter.mr8.spaceX8,
      TooltipL(
        renderTarget = IconL(name = notificationIconSignal, size = Icon.Size.Custom(12))().amend(tw.textGray6),
        renderContent = _.amend(text <-- tooltipMessageSignal)
      )(),
      div(
        tw.wPx1.bgGray3.hPx20
      )
    )

  }

  private def renderLpAutoNotificationMessage = {
    child <-- enableLpAutoNotificationOptSignal
      .splitOption(
        ifEmpty = emptyNode,
        project = (_, enableLpAutoNotificationSignal) =>
          div(
            child <-- isDrawerViewSignal.splitBoolean(
              whenTrue = _ => renderLpAutoNotificationShortWarningMessage(enableLpAutoNotificationSignal),
              whenFalse = _ => renderLpAutoNotificationFullWarningMessage(enableLpAutoNotificationSignal)
            )
          )
      )

  }

}
