// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.assignment

import com.raquo.laminar.api.L.*
import design.anduin.components.avatar.InitialAvatar
import design.anduin.components.avatar.laminar.InitialAvatarL
import design.anduin.components.dropdown.laminar.DropdownL
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import zio.ZIO

import anduin.frontend.AirStreamUtils
import anduin.fundsub.endpoint.formcomment.{AssignCommentParams, UnAssignCommentParams}
import anduin.id.fundsub.FundSubLpId
import anduin.model.common.user.UserId
import anduin.model.id.TeamId
import anduin.protobuf.actionlogger.formcomment.FormCommentViewSource
import anduin.protobuf.fundsub.comment.TopicData
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import CommentAssignmentDropdownL.*
import CommentAssignmentDropdownL.CommentAssignmentDropdownItem.*
import com.anduin.stargazer.service.account.AccountUtils

private[comment] case class CommentAssignmentDropdownL(
  lpIdSignal: Signal[FundSubLpId],
  topicDataSignal: Signal[TopicData],
  fundGroupsSignal: Signal[Seq[FundGroupsWithMembersToAssign]],
  initialAssigneeOpt: Option[AssigneeInfo],
  onAssign: Observer[Unit]
) {
  private val currentAssigneeIdOptVar: Var[Option[AssigneeId]] = Var(initialAssigneeOpt.map(_.assigneeId))

  private val currentAssigneeOptSignal: Signal[Option[AssigneeId]] = currentAssigneeIdOptVar.signal

  private val assignApiCallEventBus = new EventBus[Option[AssigneeId]]

  private val dropdownItems: Signal[Seq[CommentAssignmentDropdownItem]] =
    fundGroupsSignal.map { fundGroups =>
      val fundGroupDropdownItems = fundGroups.flatMap(_.toDropdownItems)
      val isCurrentAssigneeInaccessible = initialAssigneeOpt.exists { assignee =>
        !fundGroupDropdownItems.exists(_.idOpt.contains(assignee.assigneeId))
      }
      if (isCurrentAssigneeInaccessible) {
        initialAssigneeOpt.map(_.toDropdownItem(isAccessible = false)).toSeq ++ Seq(
          UnassignDropdownItem
        ) ++ fundGroupDropdownItems
      } else {
        Seq(UnassignDropdownItem) ++ fundGroupDropdownItems
      }
    }

  private val currentAssigneeDropdownItemSignal: Signal[CommentAssignmentDropdownItem] =
    currentAssigneeOptSignal.combineWith(dropdownItems).map { case (currentAssigneeIdOpt, dropdownItems) =>
      val assigneeOpt = for {
        assigneeId <- currentAssigneeIdOpt
        assigneeDropdownItem <- dropdownItems.find(_.idOpt.contains(assigneeId))
      } yield assigneeDropdownItem
      assigneeOpt.getOrElse(CommentAssignmentDropdownItem.UnassignDropdownItem)
    }

  private val isAssignedSignal: Signal[Boolean] = currentAssigneeOptSignal.map(_.nonEmpty)

  private val isAssigningVar: Var[Boolean] = Var(false)

  private val isUnassigningVar: Var[Boolean] = Var(false)

  private val isAssigningOrUnassigningSignal: Signal[Boolean] = isAssigningVar.signal || isUnassigningVar.signal

  def apply(): HtmlElement = {
    div(
      ComponentUtils.testIdL(CommentAssignmentDropdownL),
      child <-- isAssigningOrUnassigningSignal.splitBoolean(
        whenTrue = _ => CommentAssignmentDropdownSkeletonL()(),
        whenFalse = _ =>
          div(
            child <-- dropdownItems.combineWith(isAssignedSignal).distinct.map { case (dropdownItems, isAssigned) =>
              DropdownL[CommentAssignmentDropdownItem](
                items = dropdownItems.map(DropdownL.Item(_)),
                minWidth = 320,
                value = currentAssigneeDropdownItemSignal.map(Some(_)),
                valueToString = _.displayName,
                target = renderAssigneeDropdownTarget(isAssigned),
                content = renderAssigneeDropdownContent,
                onChange = Observer
                  .combine(
                    currentAssigneeIdOptVar.writer.contramap(_.idOpt),
                    assignApiCallEventBus.writer.contramap(_.idOpt),
                    onAssign.contramap(_ => ())
                  )
              )()
            }
          )
      ),
      assignApiCallEventBus.events.withCurrentValueOf(lpIdSignal, topicDataSignal).flatMapSwitch {
        case (assigneeIdOpt, lpId, topicData) =>
          assigneeIdOpt.fold(unAssignComment(lpId, topicData)) { assigneeId =>
            assignComment(lpId, assigneeId, topicData)
          }
      } --> Observer.empty
    )
  }

  private def unAssignComment(fundSubLpId: FundSubLpId, topicData: TopicData) = {
    AirStreamUtils.taskToStream {
      for {
        _ <- ZIO.succeed(isUnassigningVar.set(true))
        resp <- FundSubEndpointClient.unAssignComment(
          UnAssignCommentParams(
            topicData = topicData,
            fundSubLpId = fundSubLpId,
            FormCommentViewSource.FormCommentThreadPanel
          )
        )
      } yield resp.fold(
        _ => {
          isUnassigningVar.set(false)
          Toast.error("Failed to unassign comment, please try again")
        },
        _ => {
          isUnassigningVar.set(false)
          Toast.success("Thread unassigned successfully")
        }
      )
    }
  }

  private def renderSelectedIcon(isSelected: Signal[Boolean]) = {
    span(
      tw.mr8,
      marginLeft.px := -4,
      IconL(
        name = isSelected.distinct.map {
          if (_) {
            Icon.Glyph.Check
          } else {
            Icon.Glyph.Blank
          }
        },
        Icon.Size.Custom(12)
      )()
    )
  }

  private def renderAssigneeDropdownContent = DropdownL
    .Content[CommentAssignmentDropdownItem](
      renderItem = Option { renderProps =>
        val item = renderProps.item
        val isAccessible = item.value.isAccessible
        val assigneeIdOpt = item.value.idOpt
        val tooltipContentOpt = assigneeIdOpt.map { assigneeId =>
          if (!isAccessible) {
            "You don't have access to this " +
              assigneeId
                .fold(
                  _ => "user",
                  _ => "team"
                )
          } else {
            item.value match {
              case userDropdownItem: CommentAssignmentDropdownItem.UserDropdownItem =>
                userDropdownItem.emailAddress
              case teamDropdownItem: CommentAssignmentDropdownItem.TeamDropdownItem =>
                teamDropdownItem.displayName
              case CommentAssignmentDropdownItem.UnassignDropdownItem => ""
            }
          }
        }

        val renderTopDivider = item.value.idOpt match {
          case Some(Right(_)) => renderProps.index > 0
          case Some(Left(_))  => false
          case None           => renderProps.index > 0
        }

        div(
          Option.when(renderTopDivider) {
            div(
              tw.p12.bgGray0,
              div(tw.bgGray3.hPx1)
            )
          },
          TooltipL(
            renderContent = _.amend(
              tooltipContentOpt.getOrElse("")
            ),
            isDisabled = Val(tooltipContentOpt.forall(_.isEmpty)),
            renderTarget = button(
              isAccessible.cls(whenFalse = tw.opacity50),
              tw.bgGray0.hover(tw.bgGray2),
              tw.flex.itemsCenter,
              tw.py8.px16.leading16,
              tw.wPc100.textLeft.rounded4,
              item.isDisabled.cls(tw.textGray6, tw.cursorPointer),
              // Don't set the `disabled` attribute to allow using Tooltip inside an item body
              // `disabled := item.isDisabled`
              renderSelectedIcon(renderProps.isSelected),
              div(
                tw.flex.itemsCenter.spaceX8,
                renderDropdownItemIcon(item.value),
                div(
                  item.value.displayName,
                  child <-- AccountUtils.currentUserIdObs.map { currentUserIdOpt =>
                    if (currentUserIdOpt.exists(isCurrentUser(item.value, _))) {
                      span(tw.textGray6, " (you)")
                    } else {
                      emptyNode
                    }
                  }
                )
              ),
              renderProps.itemMods
            )
          )()
        )
      }
    )

  private def renderDropdownItemIcon(item: CommentAssignmentDropdownItem) = {
    item match {
      case userDropdownItem: CommentAssignmentDropdownItem.UserDropdownItem =>
        InitialAvatarL(
          id = userDropdownItem.id.idString,
          initials = Val(
            userDropdownItem.displayName
              .split(' ')
              .take(2)
              .map(_.take(1))
              .mkString
          ),
          size = InitialAvatar.Size.Px24
        )()
      case _: CommentAssignmentDropdownItem.TeamDropdownItem =>
        IconL(
          name = Val(Icon.Glyph.UserGroupLarge),
          size = Icon.Size.Px16
        )()
      case CommentAssignmentDropdownItem.UnassignDropdownItem => emptyNode
    }
  }

  private def renderAssigneeDropdownTarget(
    isAssigned: Boolean
  ) = {
    DropdownL.Target[CommentAssignmentDropdownItem](
      appearance = DropdownL.Appearance.Minimal(icon =
        Some(
          if (isAssigned) {
            Icon.Glyph.UserAccept
          } else {
            Icon.Glyph.UserAdd
          }
        )
      ),
      renderValue = Option { assignee =>
        assignee.idOpt.fold[Node](
          "Assign"
        ) { _ =>
          span(
            assignee.displayName,
            child <-- AccountUtils.currentUserIdObs.map { currentUserIdOpt =>
              if (currentUserIdOpt.exists(isCurrentUser(assignee, _))) {
                " (you)"
              } else {
                ""
              }
            }
          )
        }
      }
    )
  }

  private def assignComment(
    fundSubLpId: FundSubLpId,
    assigneeId: Either[UserId, TeamId],
    topicData: TopicData
  ) = {
    AirStreamUtils.taskToStream {
      for {
        _ <- ZIO.succeed(isAssigningVar.set(true))
        res <- FundSubEndpointClient.assignComment(
          AssignCommentParams(
            topicData = topicData,
            fundSubLpId = fundSubLpId,
            assignedGpEntityId = assigneeId,
            FormCommentViewSource.FormCommentThreadPanel
          )
        )
      } yield res.fold(
        _ => {
          Toast.error("Failed to assign comment, please try again")
          isAssigningVar.set(false)
        },
        data => {
          if (data.isReassignment) {
            Toast.success("Thread reassigned successfully")
          } else {
            Toast.success("Thread assigned successfully")
          }
          isAssigningVar.set(false)
        }
      )
    }
  }

  private def isCurrentUser(dropDownItem: CommentAssignmentDropdownItem, currentUserId: UserId): Boolean =
    dropDownItem match {
      case userDropdownItem: CommentAssignmentDropdownItem.UserDropdownItem => userDropdownItem.id == currentUserId
      case _                                                                => false
    }

}

private[comment] object CommentAssignmentDropdownL {

  type AssigneeId = Either[UserId, TeamId]

  sealed trait CommentAssignmentDropdownItem derives CanEqual {
    def displayName: String

    def isAccessible: Boolean

    def idOpt: Option[AssigneeId]
  }

  object CommentAssignmentDropdownItem {

    final case class UserDropdownItem(
      emailAddress: String,
      displayName: String,
      id: UserId,
      isAccessible: Boolean
    ) extends CommentAssignmentDropdownItem {
      override def idOpt: Option[AssigneeId] = Some(Left(id))
    }

    final case class TeamDropdownItem(
      displayName: String,
      id: TeamId,
      isAccessible: Boolean
    ) extends CommentAssignmentDropdownItem {
      override def idOpt: Option[AssigneeId] = Some(Right(id))
    }

    case object UnassignDropdownItem extends CommentAssignmentDropdownItem {
      override def displayName: String = "Unassign"

      override def isAccessible: Boolean = true

      override def idOpt: Option[AssigneeId] = None
    }

  }

  case class FundGroupsWithMembersToAssign(
    groupId: TeamId,
    groupName: String,
    members: Seq[FundMembersToAssign]
  ) {

    def toDropdownItems: Seq[CommentAssignmentDropdownItem] = {
      val groupItem = CommentAssignmentDropdownItem.TeamDropdownItem(
        displayName = groupName,
        id = groupId,
        isAccessible = true
      )
      val membersItems = members.map { member =>
        CommentAssignmentDropdownItem.UserDropdownItem(
          displayName = member.userFullName,
          id = member.userId,
          isAccessible = true,
          emailAddress = member.emailAddress
        )
      }
      groupItem +: membersItems
    }

  }

  case class FundMembersToAssign(
    userId: UserId,
    userFullName: String,
    emailAddress: String
  )

  case class AssigneeInfo(
    displayName: String,
    assigneeId: AssigneeId
  ) {

    def toDropdownItem(isAccessible: Boolean): CommentAssignmentDropdownItem = assigneeId match {
      case Left(userId) =>
        CommentAssignmentDropdownItem.UserDropdownItem(
          displayName = displayName,
          id = userId,
          isAccessible = isAccessible,
          emailAddress = ""
        )
      case Right(teamId) =>
        CommentAssignmentDropdownItem.TeamDropdownItem(
          displayName = displayName,
          id = teamId,
          isAccessible = isAccessible
        )
    }

  }

}
