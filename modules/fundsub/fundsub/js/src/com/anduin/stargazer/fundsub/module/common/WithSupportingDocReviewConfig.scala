// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.common

import design.anduin.components.progress.react.BlockIndicatorR
import design.anduin.components.toast.Toast
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.endpoint.review.{FundSubSupportingDocReviewConfig, GetFundSubSupportingDocReviewConfigParams}
import anduin.id.fundsub.FundSubId
import anduin.model.notichannel.FundSubNotificationChannels
import anduin.stargazer.component.SignalReactor
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubSupportingDocReviewEndpointClient
import com.anduin.stargazer.notiCenter.NotificationCenter

private[fundsub] final case class WithSupportingDocReviewConfig(
  fundId: FundSubId
)(
  renderer: WithSupportingDocReviewConfig.Renderer
) {

  def apply(): VdomElement = WithSupportingDocReviewConfig.component(
    WithSupportingDocReviewConfig.Props(
      fundId,
      renderer
    )
  )

}

private[fundsub] object WithSupportingDocReviewConfig {

  private case class Props(
    fundId: FundSubId,
    renderer: WithSupportingDocReviewConfig.Renderer
  )

  private type Renderer = FundSubSupportingDocReviewConfig => VdomElement

  final case class State(
    supportingDocReviewConfigOpt: Option[FundSubSupportingDocReviewConfig] = None
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    val reactor: SignalReactor[NotificationCenter.ReactorData] = NotificationCenter.reactor((_, _) => refetch)

    def render(props: Props, state: State): VdomElement = {
      state.supportingDocReviewConfigOpt.fold(BlockIndicatorR()())(props.renderer(_))
    }

    private def refetch: Callback = {
      for {
        props <- scope.props
        _ <- ZIOUtils.toReactCallback {
          FundSubSupportingDocReviewEndpointClient
            .getFundSubSupportingDocReviewConfig(
              GetFundSubSupportingDocReviewConfigParams(
                fundSubId = props.fundId,
                shouldFetchUserInfo = false
              )
            )
            .map(
              _.fold(
                _ => Toast.errorCallback(s"Failed to load supporting document review config"),
                resp =>
                  scope.modState(
                    _.copy(supportingDocReviewConfigOpt = Some(resp.config))
                  )
              )
            )
        }
      } yield ()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .componentDidMount { scope =>
      NotificationCenter.subscribeSingleChannelReactor(
        scope.backend.reactor,
        FundSubNotificationChannels.fundSubReviewConfigSupportingDoc(scope.props.fundId)
      )
    }
    .componentWillUnmount(_.backend.reactor.cleanUp())
    .build

}
