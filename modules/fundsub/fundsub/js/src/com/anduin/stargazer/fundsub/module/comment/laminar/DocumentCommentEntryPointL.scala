// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar

import com.raquo.laminar.api.L.*
import design.anduin.components.drawer.Drawer
import design.anduin.components.drawer.laminar.DrawerL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*
import org.scalajs.dom

import anduin.fundsub.comment.CommentBubbleL
import anduin.id.fundsub.FundSubLpId
import anduin.scalajs.pluralize.Pluralize
import com.anduin.stargazer.fundsub.module.comment.laminar.DocumentCommentEntryPointL.EntryPointDirection
import com.anduin.stargazer.fundsub.module.comment.laminar.drawer.gp.GpCommentDrawerL
import com.anduin.stargazer.fundsub.module.comment.laminar.drawer.CommentDrawerUIModels
import com.anduin.stargazer.fundsub.module.comment.laminar.drawer.lp.LpCommentDrawerL
import com.anduin.stargazer.fundsub.module.comment.models.{
  CommentNotificationSetting,
  CommentTarget,
  CommentsData,
  DocCommentCount
}

private[fundsub] case class DocumentCommentEntryPointL(
  isFundSideViewSignal: Signal[Boolean],
  showInternalCommentSignal: Signal[Boolean],
  amlKycNamesSignal: Signal[Option[List[String]]],
  commentNotificationSettingSignal: Signal[Option[CommentNotificationSetting]],
  inactiveDaysThresholdSignal: Signal[Option[Int]],
  commentsDataSignal: Signal[CommentsData],
  docType: String,
  lpId: FundSubLpId,
  onJumpDocument: Observer[String] = Observer.empty,
  isInitialActive: Boolean = false,
  isPublicModeInitialActive: Boolean = true,
  entryPointDirection: EntryPointDirection = EntryPointDirection.Horizontal
) {

  private val commentPanelParamsVar = Var[Option[CommentPanelParams]](None)

  def apply(): HtmlElement = {
    val docCommentCountSignal = commentsDataSignal.map(_.getDocCommentCount(docType))
    val publicDocCommentCountSignal =
      commentsDataSignal.map(_.getCommentCountOfDocument(docType, isPublic = true))
    val internalDocCommentCountSignal =
      commentsDataSignal.map(_.getCommentCountOfDocument(docType, isPublic = false))

    val shouldShowCreateNewCommentBubble =
      publicDocCommentCountSignal
        .combineWith(internalDocCommentCountSignal)
        .map { case (publicComment, internalComment) =>
          !publicComment.hasComment && !internalComment.hasComment
        }
        .distinct

    div(
      (docCommentCountSignal
        .map { data =>
          !data.hasComment || data.isResolved
        })
        .cls(tw.invisible.groupHover(tw.visible)),
      div(
        entryPointDirection match {
          case EntryPointDirection.Horizontal => tw.flexRow.spaceX4
          case EntryPointDirection.Vertical   => tw.flexCol.spaceY4
        },
        tw.flex,
        child.maybe <-- internalDocCommentCountSignal
          .map(_.hasComment)
          .distinct
          .map { hasInternalComment =>
            Option.when(hasInternalComment) {
              renderCommentBubble(
                internalDocCommentCountSignal,
                isPublic = false
              )
            }
          },
        child.maybe <-- publicDocCommentCountSignal
          .map(_.hasComment)
          .distinct
          .map { hasPublicComment =>
            Option.when(hasPublicComment) {
              renderCommentBubble(
                publicDocCommentCountSignal,
                isPublic = true
              )
            }
          }
      ),
      child.maybe <-- shouldShowCreateNewCommentBubble
        .combineWith(isFundSideViewSignal)
        .map { case (showNewCommentBubble, isFundSideView) =>
          Option.when(showNewCommentBubble) {
            div(
              renderCommentBubble(
                if (isFundSideView) {
                  internalDocCommentCountSignal
                } else {
                  publicDocCommentCountSignal
                },
                isPublic = !isFundSideView
              )
            )
          }
        },
      child.maybe <-- commentPanelParamsVar.signal
        .map(_.map(_.isPublic))
        .distinct
        .map(isPublicOpt =>
          isPublicOpt.map(isPublic =>
            renderDrawer(
              isPublic = isPublic
            )
          )
        ),
      onMountCallback { _ =>
        if (isInitialActive) {
          commentPanelParamsVar.set(
            Option(
              CommentPanelParams(
                isPublic = isPublicModeInitialActive
              )
            )
          )
          onJumpDocument.onNext(docType)
        }
      }
    )
  }

  private def renderDrawer(isPublic: Boolean) = {
    DrawerL(
      width = Drawer.Width.Px400,
      widthConstrain = Option { constrainProps =>
        constrainProps.drawerWidth >= 320 &&
        constrainProps.drawerWidth <= constrainProps.containerWidth * 0.5
      },
      defaultIsOpened = true,
      afterUserClose = Observer[Unit] { _ =>
        commentPanelParamsVar.set(None)
      },
      renderContent = close => {
        div(
          tw.hPc100,
          child <-- isFundSideViewSignal.splitBoolean(
            whenTrue = _ =>
              GpCommentDrawerL(
                amlKycNamesSignal = amlKycNamesSignal,
                commentNotificationSettingSignal = commentNotificationSettingSignal,
                inactiveDaysThresholdSignal = inactiveDaysThresholdSignal,
                commentsDataSignal = commentsDataSignal,
                initialView = CommentDrawerUIModels.DrawerView.Doctype(docType, isPublic),
                isHiddenFieldThread = _ => Val(false),
                lpIdSignal = Val(lpId),
                targetSignal = Val(CommentTarget.AmlKycDocument),
                onClose = close,
                onJumpToTarget = onJumpDocument,
                fundSubId = lpId.parent
              )(),
            whenFalse = _ =>
              LpCommentDrawerL(
                amlKycNamesSignal = amlKycNamesSignal,
                commentsDataSignal = commentsDataSignal,
                initialView = CommentDrawerUIModels.DrawerView.Doctype(docType, isPublic),
                isHiddenFieldThread = _ => Val(false),
                lpIdSignal = Val(lpId),
                targetSignal = Val(CommentTarget.AmlKycDocument),
                onClose = close,
                onJumpToTarget = onJumpDocument
              )()
          )
        )

      }
    )()
  }

  private def renderCommentBubble(
    docCommentCountSignal: Signal[DocCommentCount],
    isPublic: Boolean
  ) = {
    val isResolvedSignal = docCommentCountSignal.map(_.isResolved).distinct
    val commentsCountSignal = docCommentCountSignal.map(_.totalComments).distinct

    TooltipL(
      renderContent = _.amend(
        child <-- isResolvedSignal.combineWith(commentsCountSignal).map { case (isResolved, commentsCount) =>
          if (commentsCount == 0) {
            "Add comment"
          } else {
            s"View${if (isResolved) " resolved" else ""} ${if (isPublic) "shared" else "internal"} ${Pluralize("comment", commentsCount)}"
          }
        }
      ),
      renderTarget = div(
        docCommentCountSignal
          .map { data =>
            !data.hasComment
          }
          .cls(tw.invisible.groupHover(tw.visible)),
        child <-- docCommentCountSignal.map { docComment =>
          CommentBubbleL(
            hasComment = docComment.hasComment,
            hasNewComment = docComment.unreadComments > 0,
            isResolved = docComment.isResolved,
            isInternalComment = !isPublic,
            theme = CommentBubbleL.Theme.Full,
            totalComments = docComment.totalComments
          )().amend(
            onClick --> Observer[dom.MouseEvent] { _ =>
              commentPanelParamsVar.set(
                Option(
                  CommentPanelParams(
                    isPublic = isPublic
                  )
                )
              )
            }
          )
        }
      )
    )()
  }

}

private[fundsub] object DocumentCommentEntryPointL {
  sealed trait EntryPointDirection derives CanEqual

  object EntryPointDirection {
    object Horizontal extends EntryPointDirection

    object Vertical extends EntryPointDirection
  }

}
