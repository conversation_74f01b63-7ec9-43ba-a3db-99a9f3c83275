// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.link

import design.anduin.components.suggest.{MultiSuggest, Suggest}
import design.anduin.components.tag.TagColor
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.model.common.emailaddress.EmailAddress
import anduin.utils.StateSnapshotWithModFn

private[link] final case class WhitelistedDomainsTextArea(
  domains: StateSnapshotWithModFn[Seq[String]]
) {
  def apply(): VdomElement = WhitelistedDomainsTextArea.component(this)
}

private[link] object WhitelistedDomainsTextArea {

  private type Props = WhitelistedDomainsTextArea

  private val DomainMultiSuggest = (new MultiSuggest[String])()

  private def valueToTag(value: String) = {
    value match {
      case EmailAddress.validDomain(_) =>
        MultiSuggest.TagProps(label = value.prepended('@'))
      case _ =>
        MultiSuggest.TagProps(
          label = value,
          color = TagColor.Light.Danger,
          tooltip = Some(() => "Invalid email domain")
        )
    }

  }

  private def onChange(props: Props)(domainStrList: Seq[String]) = {
    props.domains.setState {
      domainStrList.map {
        _.trim.stripPrefix("@") match {
          case EmailAddress(email)              => email.domain.value
          case EmailAddress.validDomain(domain) => domain
          case domain @ _                       => domain
        }
      }
    }
  }

  private object Appearance extends MultiSuggest.Appearance {

    val mod: TagMod = TagMod(
      MultiSuggest.Appearance.Full.mod,
      ^.minHeight := "70px",
      ^.alignContent.flexStart
    )

  }

  private def render(props: Props) = {
    <.label(
      ComponentUtils.testId(WhitelistedDomainsTextArea),
      ^.`for` := "downshift-2-input", // TODO: Shouldn't hardcode this. Specify input for TextBox inside MultiSuggest
      tw.cursorText,
      DomainMultiSuggest(
        value = props.domains.value,
        valueToTag = valueToTag,
        onChange = onChange(props),
        options = Suggest.Options(Seq(), identity),
        textBox = MultiSuggest.TextBoxProps(if (props.domains.value.isEmpty) "E.g. acmecorp.com" else ""),
        optionSelectKeys = List(
          "Enter",
          "Space",
          ",",
          ";",
          " ",
          "\t"
        ),
        appearance = Appearance
      )()
    )
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
