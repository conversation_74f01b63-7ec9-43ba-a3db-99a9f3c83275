// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.dashboard.lp.table

import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.tag.Tag
import design.anduin.components.tag.react.TagR
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.model.FundSubSharedClientModel.ParticipantInfo
import anduin.protobuf.fundsub.LpOrderType
import anduin.utils.StringUtils
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils
import com.anduin.stargazer.fundsub.module.ordertype.OrderTypeUtils

private[table] final case class LpContactCell(
  firmName: String,
  customId: String,
  lpOrderType: LpOrderType,
  lpInfo: ParticipantInfo,
  isExpanding: Boolean,
  isNew: Boolean,
  isInactive: <PERSON>olean,
  inactiveDays: Option[Int],
  onToggleExpand: Callback
) {
  def apply(): VdomElement = LpContactCell.component(this)

  val hasFirmName: Boolean = firmName.nonEmpty

  val showManualTag: Boolean = lpOrderType.isOfflineOrder

  val showNewTag: Boolean = isNew
}

private[table] object LpContactCell {

  private type Props = LpContactCell

  private def render(props: Props): VdomElement = {
    <.div(
      tw.flex.itemsStart,
      renderExpandIcon(props),
      <.div(
        tw.ml8,
        if (props.hasFirmName) {
          React.Fragment(
            renderContactMain(
              props,
              props.firmName,
              "FirmName"
            ),
            renderInvestorId(props),
            renderFullName(props),
            renderSystemTags(props)
          )
        } else {
          React.Fragment(
            renderContactMain(
              props,
              props.lpInfo.fullName,
              "ContactName"
            ),
            renderInvestorId(props),
            renderSystemTags(props)
          )
        }
      )
    )
  }

  private def renderContactMain(props: Props, contact: String, testId: String) = {
    <.div(
      ^.display.table,
      ^.tableLayout.fixed,
      tw.wPc100.breakWords,
      ComponentUtils.testId(LpContactCell, testId),
      tw.textGray8.fontSemiBold.leading20,
      contact,
      renderInactiveTag(props)
    )
  }

  private def renderFullName(props: Props) = {
    <.div(
      ^.display.table,
      ^.tableLayout.fixed,
      tw.wPc100.breakWords,
      ComponentUtils.testId(LpContactCell, "ContactName"),
      tw.textGray7,
      tw.leading20,
      props.lpInfo.fullName
    )
  }

  private def renderSystemTags(props: Props) = {
    Option.when(props.showManualTag || props.showNewTag)(
      <.div(
        tw.my4,
        tw.flex.itemsCenter,
        renderManualTag(props),
        renderNewTag(props)
      )
    )
  }

  private def renderExpandIcon(props: Props) = {
    StopClickEventWrapper(
      TooltipR(
        renderTarget = <.div(
          tw.textGray7.cursorPointer.wPx24.hPx20.hover(tw.textGray8),
          tw.flex.itemsCenter.justifyCenter,
          IconR(name = if (props.isExpanding) Icon.Glyph.ChevronDown else Icon.Glyph.ChevronRight)(),
          ^.onClick --> props.onToggleExpand
        ),
        renderContent = _(if (props.isExpanding) "Click to collapse" else "Click to expand")
      )()
    )
  }

  private def renderInactiveTag(props: Props) = {
    Option.when(props.isInactive) {
      val tooltip = props.inactiveDays.fold[String]("Investor is inactive") { days =>
        s"Inactive for ${StringUtils.pluralItem(days, "day")}"
      }
      <.div(
        tw.ml8.textWarning4.inlineBlock,
        TooltipR(
          renderTarget = IconR(
            name = Icon.Glyph.Zz,
            size = Icon.Size.Custom(14)
          )(),
          renderContent = _(tooltip)
        )()
      )
    }
  }

  private def renderManualTag(props: Props) = {
    Option.when(props.showManualTag)(
      <.div(
        tw.mr8,
        TooltipR(
          renderTarget = <.div(
            tw.borderAll.borderGray4.rounded3.flex.itemsCenter.hPx20.px6,
            tw.fontMedium.text11,
            "Offline"
          ),
          renderContent = _(OrderTypeUtils.offlineOrderGuideline(FundSubCopyUtils.getFlowTerm))
        )()
      )
    )
  }

  private def renderNewTag(props: Props) = {
    Option.when(props.showNewTag)(
      <.div(
        tw.mr8,
        TagR(color = Tag.Light.Danger, label = "New")()
      )
    )
  }

  private def renderInvestorId(props: Props) = {
    Option.when(props.customId.nonEmpty) {
      <.div(
        ComponentUtils.testId(LpContactCell, "InvestorId"),
        tw.textGray8.fontSemiBold,
        tw.leading20,
        props.customId
      )
    }
  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .stateless
    .render_P(render)
    .shouldComponentUpdatePure { scope =>
      val nextProps = scope.nextProps
      val curProps = scope.currentProps
      nextProps.isInactive != curProps.isInactive ||
      nextProps.inactiveDays != curProps.inactiveDays ||
      nextProps.isNew != curProps.isNew ||
      nextProps.firmName != curProps.firmName || nextProps.lpInfo != curProps.lpInfo ||
      nextProps.isExpanding != curProps.isExpanding || nextProps.lpOrderType != curProps.lpOrderType ||
      curProps.customId != nextProps.customId
    }
    .build

}
