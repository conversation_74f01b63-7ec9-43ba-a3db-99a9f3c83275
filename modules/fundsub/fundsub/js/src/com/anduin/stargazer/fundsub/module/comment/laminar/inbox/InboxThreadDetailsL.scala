// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.comment.laminar.inbox

import java.time.Instant
import java.util.UUID

import com.raquo.laminar.api.L.*
import design.anduin.components.icon.Icon
import design.anduin.components.icon.laminar.IconL
import design.anduin.components.progress.Skeleton
import design.anduin.components.progress.laminar.SkeletonL
import design.anduin.components.tab.Tab
import design.anduin.components.tab.laminar.TabL
import design.anduin.components.toast.Toast
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import zio.ZIO

import anduin.fundsub.endpoint.formcomment.*
import anduin.id.entity.EntityId
import anduin.id.fundsub.FundSubLpId
import anduin.model.common.user.{UserId, UserInfo}
import anduin.protobuf.actionlogger.formcomment.FormCommentViewSource
import anduin.protobuf.fundsub.comment.TopicData
import anduin.service.ServiceResponseCode
import anduin.stargazer.service.formcomment.FormCommentCommons.{
  CommentThread,
  CommentVisibilityType,
  MentionsData,
  PendingThreadDefaultId
}
import anduin.user.CurrentUserInfoProvider
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import com.anduin.stargazer.fundsub.module.comment.laminar.fetchcomponent.FetchSingleThreadL
import com.anduin.stargazer.fundsub.module.comment.laminar.share.{
  AmlKycDocumentInfo,
  CommentsListL,
  FieldSummaryInfo,
  NewCommentBoxL,
  ThreadDetailSkeletonL
}
import com.anduin.stargazer.fundsub.module.comment.laminar.*
import com.anduin.stargazer.fundsub.module.comment.laminar.assignment.CommentAssignmentDropdownWrapperL
import com.anduin.stargazer.fundsub.module.comment.laminar.share.gp.{GpAddCommentPanelL, GpNonEmptyThreadL}
import com.anduin.stargazer.fundsub.module.comment.models.CommentNotificationSetting
import com.anduin.stargazer.fundsub.module.comment.models.CommentView.{CommentViewParams, SwitchThreadVisibilityParams}

private[inbox] case class InboxThreadDetailsL(
  commentNotificationSettingSignal: Signal[Option[CommentNotificationSetting]],
  entitySignal: Signal[Option[EntityId]],
  isCommentMentioningEnabledSignal: Signal[Boolean],
  isCommentAssignmentEnabledSignal: Signal[Boolean],
  inactiveDaysThresholdSignal: Signal[Option[Int]],
  handleViewThread: CommentViewParams => Unit,
  defaultOpenedCommentIdOpt: Option[String],
  initialComment: String = "",
  topicData: TopicData,
  isPublic: Boolean,
  targetIsHidden: Boolean,
  fundSubLpId: FundSubLpId,
  onSeenThread: Observer[FundSubLpId],
  canMakePublicCommentSignal: Signal[Boolean],
  onAssignThread: Observer[Unit]
) {
  private val onSwitchModeWithInitialCommentEventBus = new EventBus[String]
  private val createNewCommentThreadEventBus = new EventBus[NewCommentBoxL.AddCommentData]
  private val localCommentsVar: Var[Seq[CommentsListL.ThreadActivityItem.Comment]] = Var(Seq.empty)
  private val pendingCommentThreadVar: Var[Option[CommentThread]] = Var(None)
  private val resetCommentBoxContentEventBus = new EventBus[Unit]

  private def handleCreateCommentThread(
    data: NewCommentBoxL.AddCommentData,
    currentUserIdAndInfoOpt: Option[(UserId, UserInfo)]
  ): Unit = {
    val pendingThread =
      CommentThread(
        id = PendingThreadDefaultId,
        fundSubLpId = fundSubLpId,
        topicData = topicData,
        comment = data.comment,
        isResolved = false,
        isPublic = isPublic,
        creatorOpt = currentUserIdAndInfoOpt.map(_._1),
        creatorInfoOpt = currentUserIdAndInfoOpt.map(_._2),
        createdAtOpt = Some(Instant.now()),
        lastEditedAtOpt = None,
        replies = Seq.empty,
        pendingNotification = false,
        resolverInfoOpt = None,
        flaggedRootCommentMetadata = None
      )
    pendingCommentThreadVar.set(Some(pendingThread))

    ZIOUtils.runAsync {
      for {
        _ <- ZIO.succeed(data.onAdding.onNext(()))
        _ <- FundSubEndpointClient
          .createComment(
            CreateCommentParams(
              fundSubLpId = fundSubLpId,
              topicData = topicData,
              comment = data.comment,
              viewSource = FormCommentViewSource.FormCommentThreadPanel,
              isPublic = isPublic
            )
          )
          .map {
            _.fold(
              _ => (),
              response =>
                response.foreach { threadId =>
                  pendingCommentThreadVar.set(Some(pendingThread.copy(id = threadId)))
                }
            )
          }
        _ <- ZIO.succeed(data.onAddDone.onNext(()))
      } yield ()
    }
  }

  private def handleAddCommentReply(
    thread: CommentThread,
    data: NewCommentBoxL.AddCommentData,
    currentUserIdOpt: Option[UserId]
  ): Unit = {
    val newCommentItem = CommentsListL.ThreadActivityItem.Comment(
      threadId = thread.id,
      lpId = thread.fundSubLpId,
      comment = data.comment,
      commentIdOpt = None,
      actorOpt = currentUserIdOpt,
      createdAtOpt = Some(Instant.now().truncatedTo(java.time.temporal.ChronoUnit.SECONDS)),
      isPublic = thread.isPublic,
      lastEditedAtOpt = None,
      isPendingComment = true,
      itemId = UUID.randomUUID().toString,
      flaggedCommentMetadata = None,
      mentions = MentionsData.empty
    )
    // show the reply immediately
    val newTempCommentsList = localCommentsVar.now() :+ newCommentItem
    localCommentsVar.set(newTempCommentsList)

    ZIOUtils.runAsync {
      for {
        _ <- ZIO.succeed(data.onAdding.onNext(()))
        _ <- FundSubEndpointClient
          .addCommentReply(
            AddCommentReplyParams(
              fundSubLpId = thread.fundSubLpId,
              issueId = thread.id,
              reply = data.comment,
              viewSource = FormCommentViewSource.FormCommentThreadPanel
            )
          )
          .map(
            _.fold(
              _ => Toast.error("Couldn't add reply. Please try again later."),
              response => {
                if (
                  response.responseCode == ServiceResponseCode.MarkedAsDeleted ||
                  response.responseCode == ServiceResponseCode.NoPermission ||
                  !response.commentReplyIdOpt.exists(_.nonEmpty)
                ) {
                  Toast.info("The comment no longer exists")
                } else {
                  val newTempCommentsList = localCommentsVar
                    .now()
                    .map(item =>
                      if (item.itemId.equals(newCommentItem.itemId)) {
                        item.copy(isPendingComment = false, commentIdOpt = response.commentReplyIdOpt)
                      } else {
                        item
                      }
                    )
                  // sync between pending and real comment but keep the original itemId
                  localCommentsVar.set(newTempCommentsList)
                }
              }
            )
          )
        _ <- ZIO.succeed(data.onAddDone.onNext(()))
      } yield ()
    }
  }

  private val onSwitchMode: Observer[SwitchThreadVisibilityParams] =
    Observer[SwitchThreadVisibilityParams] { newVisibilityParams =>
      if ((newVisibilityParams.newVisibilityType == CommentVisibilityType.PublicComment) != isPublic) {
        onSwitchModeWithInitialCommentEventBus.emit(newVisibilityParams.initialComment)
      }
    }

  private def renderSelectedThreadSummaryInfo = {
    child.maybe <-- entitySignal.distinct.mapSome { entityId =>
      val sealedValue = topicData.asMessage.sealedValue
      div(
        tw.mt12.mx16,
        if (sealedValue.isAmlKycDocData) {
          sealedValue.amlKycDocData.map { data =>
            AmlKycDocumentInfo(
              amlKycData = data,
              fundEntityId = entityId,
              fundSubLpId = fundSubLpId,
              isPublic = isPublic,
              isHidden = targetIsHidden
            )()
          }
        } else {
          sealedValue.formQuestionData.map { data =>
            FieldSummaryInfo(
              formQuestionData = data,
              fundEntityId = entityId,
              fundSubLpId = fundSubLpId,
              isPublic = isPublic,
              isHidden = targetIsHidden
            )()
          }
        }
      )

    }

  }

  private def resetPendingDataWhenNewThreadDataIsFetched(
    fetchedThreadOpt: Option[CommentThread],
    pendingThreadOpt: Option[CommentThread],
    currentLocalComments: Seq[CommentsListL.ThreadActivityItem.Comment]
  ): Unit = {
    if (currentLocalComments.nonEmpty && (fetchedThreadOpt.isEmpty && pendingThreadOpt.isEmpty)) {
      // remove all local comments when the thread is deleted
      localCommentsVar.set(Seq.empty)
    }
    fetchedThreadOpt.foreach(_ => pendingCommentThreadVar.set(None))
  }

  def apply(): HtmlElement = {
    FetchSingleThreadL(
      lpId = fundSubLpId,
      topicData = topicData,
      isPublic = isPublic,
      renderChildren = renderSingleThread => {
        div(
          ComponentUtils.testIdL(InboxThreadDetailsL),
          tw.flexFill.flex.flexCol.hPc100,
          renderAssignmentButton,
          renderSelectedThreadSummaryInfo,
          renderInternalAndSharedCommentTab(renderSingleThread),
          // Event handlers
          onSwitchModeWithInitialCommentEventBus.events
            .map { comment =>
              handleViewThread(
                CommentViewParams(
                  fundSubLpId = fundSubLpId,
                  topicData = topicData,
                  isPublic = !isPublic,
                  targetIsHidden = targetIsHidden,
                  comment = comment,
                  defaultOpenedCommentIdOpt = defaultOpenedCommentIdOpt
                )
              )
            } --> Observer.empty,
          createNewCommentThreadEventBus.events
            .withCurrentValueOf(CurrentUserInfoProvider.getCurrentUserInfoSignal)
            .map { case (data, currentUserIdAndInfoOpt) =>
              handleCreateCommentThread(data, currentUserIdAndInfoOpt)
            } --> Observer.empty
        )
      }
    )()

  }

  private def renderInternalAndSharedCommentTab(renderSingleThread: FetchSingleThreadL.RenderChildren) = {
    val isFetchingSignal = renderSingleThread.dataSignal.map(_.isFetching)
    val fetchedThreadOptSignal = renderSingleThread.dataSignal.map(_.thread)
    val threadOptSignal = fetchedThreadOptSignal
      .combineWith(pendingCommentThreadVar.signal)
      .map { case (fetchedThreadOpt, pendingThreadOpt) =>
        fetchedThreadOpt.fold(pendingThreadOpt) { thread => Some(thread) }
      }
    TabL(
      activeTabSignal = Val(Some(if (isPublic) 1 else 0)),
      panels = Seq(
        Tab.Panel(
          title = div(
            height.px := 36,
            tw.flex.itemsCenter,
            IconL(name = Val(Icon.Glyph.CommentLineLock))(),
            span(tw.ml8, "Internal")
          ),
          renderContent = _ => renderTabContent(isFetchingSignal, threadOptSignal)
        ),
        Tab.Panel(
          title = div(
            height.px := 36,
            tw.flex.itemsCenter,
            IconL(name = Val(Icon.Glyph.CommentLine))(),
            span(tw.ml8, "Shared")
          ),
          renderContent = _ => renderTabContent(isFetchingSignal, threadOptSignal)
        )
      ),
      renderHeader = Some(renderTabHeader(_, threadOptSignal, isFetchingSignal, renderSingleThread.refetch)),
      onClick = Observer[Int] { index =>
        onSwitchMode.onNext(
          SwitchThreadVisibilityParams(
            newVisibilityType =
              if (index == 0) CommentVisibilityType.InternalComment
              else CommentVisibilityType.PublicComment,
            initialComment = ""
          )
        )
      },
      style = Tab.Style.Minimal(isFullHeight = true)
    )().amend(
      fetchedThreadOptSignal
        .withCurrentValueOf(pendingCommentThreadVar.signal, localCommentsVar.signal)
        .map { case (fetchedThreadOpt, pendingThreadOpt, currentLocalComments) =>
          resetPendingDataWhenNewThreadDataIsFetched(
            fetchedThreadOpt = fetchedThreadOpt,
            pendingThreadOpt = pendingThreadOpt,
            currentLocalComments = currentLocalComments
          )
        } --> Observer.empty
    )
  }

  private def renderTabHeader(
    renderHeaderProps: Tab.RenderHeader,
    threadOptSignal: Signal[Option[CommentThread]],
    isFetchingSignal: Signal[Boolean],
    refetchThreadData: Observer[Boolean]
  ) = {
    val isPublicThreadSignal = threadOptSignal.map(_.exists(_.isPublic))
    val showResolveButtonSignal =
      threadOptSignal.map(_.nonEmpty) && (!isPublicThreadSignal || canMakePublicCommentSignal)
    div(
      div(
        tw.mx16.flex.justifyBetween.itemsCenter,
        renderHeaderProps.renderTitles,
        child <-- showResolveButtonSignal.splitBoolean(
          whenTrue = _ =>
            renderResolveOrReopenButton(
              threadOptSignal,
              refetchThreadData.contramap(_ => true),
              isFetchingSignal
            ),
          whenFalse = _ => emptyNode
        )
      ),
      renderHeaderProps.renderHeaderBorder
    )
  }

  private def renderTabContent(isFetchingSignal: Signal[Boolean], threadOptSignal: Signal[Option[CommentThread]]) = {
    div(
      tw.hPc100,
      child <-- isFetchingSignal.splitBoolean(
        whenTrue = _ => ThreadDetailSkeletonL()(),
        whenFalse = _ =>
          div(
            tw.hPc100.wPc100,
            child <-- threadOptSignal.splitOption(
              (_, threadSignal) => {
                GpNonEmptyThreadL(
                  commentNotificationSettingSignal = commentNotificationSettingSignal,
                  threadSignal = threadSignal,
                  canMakePublicCommentSignal = canMakePublicCommentSignal,
                  initialComment = initialComment,
                  isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
                  inactiveDaysThresholdSignal = inactiveDaysThresholdSignal,
                  onSwitchAllComments = Observer.empty,
                  onSwitchMode = onSwitchMode,
                  localCommentsSignal = localCommentsVar.signal,
                  onUpdateLocalComments = localCommentsVar.writer,
                  handleAddCommentReply = handleAddCommentReply,
                  onSeenThread = onSeenThread,
                  resetCommentBoxEventStream = resetCommentBoxContentEventBus.events,
                  isDrawerViewSignal = Val(false)
                )()
              },
              ifEmpty = GpAddCommentPanelL(
                commentNotificationSettingSignal = commentNotificationSettingSignal,
                lpIdSignal = Val(fundSubLpId),
                isPublicThreadSignal = Val(isPublic),
                canMakePublicCommentSignal = canMakePublicCommentSignal,
                isCommentMentioningEnabledSignal = isCommentMentioningEnabledSignal,
                initialComment = initialComment,
                onAddComment = createNewCommentThreadEventBus.writer
              )()
            )
          )
      )
    )
  }

  private def renderResolveOrReopenButton(
    threadOptSignal: Signal[Option[CommentThread]],
    refetchThreadData: Observer[Unit],
    isFetchingSignal: Signal[Boolean]
  ) = {
    val isResolvedSignal = threadOptSignal.map(_.exists(_.isResolved))
    div(
      child <-- isFetchingSignal.splitBoolean(
        whenTrue = _ =>
          SkeletonL(
            height = "32px",
            width = "100px",
            shape = Skeleton.Shape.Rounded
          )(),
        whenFalse = _ =>
          div(
            child <-- threadOptSignal
              .splitOption(
                ifEmpty = Val(emptyNode),
                project = (_, threadSignal) =>
                  isResolvedSignal
                    .splitBoolean(
                      whenTrue = _ =>
                        ReopenCommentThreadButtonL(
                          threadSignal = threadSignal,
                          onReopenThread = Observer.combine(resetCommentBoxContentEventBus.writer, refetchThreadData),
                          viewSource = FormCommentViewSource.FormCommentThreadPanel,
                          buttonType = ReopenCommentThreadButtonL.ButtonType.IconWithText
                        )(),
                      whenFalse = _ =>
                        ResolveCommentThreadButtonL(
                          threadSignal = threadSignal,
                          onResolveThread = Observer.combine(resetCommentBoxContentEventBus.writer, refetchThreadData),
                          viewSource = FormCommentViewSource.FormCommentThreadPanel,
                          buttonType = ResolveCommentThreadButtonL.ButtonType.IconWithText
                        )()
                    )
              )
              .flattenSwitch
          )
      )
    )
  }

  private def renderAssignmentButton = {
    child <-- isCommentAssignmentEnabledSignal.splitBoolean(
      whenTrue = _ => {
        div(
          tw.flex.justifyEnd.mt16.mx16,
          CommentAssignmentDropdownWrapperL(
            fundSubLpId.parent,
            Val(fundSubLpId),
            Val(topicData),
            onAssign = onAssignThread
          )()
        )
      },
      whenFalse = _ => emptyNode
    )
  }

}
