// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.homepage

import design.anduin.components.icon.Icon
import design.anduin.components.textbox.TextBox
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

private[homepage] final case class FundSubHomePageSearchBox(
  placeholder: String,
  onSearch: String => Callback,
  initialValue: String
) {
  def apply(): VdomElement = FundSubHomePageSearchBox.component(this)
}

private[homepage] object FundSubHomePageSearchBox {

  private type Props = FundSubHomePageSearchBox

  private case class State(
    currentValue: String
  )

  private case class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomElement = {
      TextBox(
        value = state.currentValue,
        onChange = value => scope.modState(_.copy(currentValue = value)),
        placeholder = props.placeholder,
        icon = Option(Icon.Glyph.Search),
        onKeyDown = event =>
          Callback.when(event.key == "Enter") {
            props.onSearch(state.currentValue)
          },
        onClear = Some(scope.modState(_.copy(currentValue = ""), props.onSearch("")))
      )()
    }

  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialStateFromProps(props => State(props.initialValue))
    .renderBackend[Backend]
    .build

}
