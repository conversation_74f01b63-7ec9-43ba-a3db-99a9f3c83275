// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.ria

import com.raquo.laminar.api.L.*
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.Icon
import design.anduin.components.nonidealstate.laminar.NonIdealStateL

import anduin.fundsub.endpoint.ria.RiaFundGroup

private[ria] case class EmptyAdvisorMembers(
  group: RiaFundGroup,
  onDoneAddAdvisorMembers: Observer[Unit]
) {

  def apply(): HtmlElement = {
    div(
      paddingBottom.px(150),
      paddingTop.px(150),
      NonIdealStateL(
        icon = OrganizationIcon()(),
        title = "No advisors invited yet",
        description = "Click below to start inviting advisors",
        action = {
          InviteAdvisorMembersModal(
            group = group,
            renderTarget = onOpen => {
              ButtonL(
                style = ButtonL.Style.Full(color = ButtonL.Color.Primary, icon = Option(Icon.Glyph.UserAdd)),
                onClick = onOpen.contramap(_ => ())
              )("Invite advisors")
            },
            onDoneAddAdvisorMembers = onDoneAddAdvisorMembers,
            existingGroupEmailAddresses = Seq.empty
          )()
        }
      )()
    )
  }

}
