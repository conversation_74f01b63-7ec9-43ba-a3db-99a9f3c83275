// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.review.subdoc

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.icon.react.IconR
import design.anduin.components.popover.react.PopoverR
import design.anduin.components.portal.PortalWrapper
import design.anduin.components.textbox.TextBox
import design.anduin.components.toast.Toast
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.well.Well
import design.anduin.components.well.react.WellR
import design.anduin.components.wrapper.laminar.WrapperL
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.fundsub.endpoint.subscriptiondoc.review.{FundSubSubscriptionDocReviewType, InvestorPendingReviewInfo}
import anduin.id.entity.EntityId
import anduin.id.fundsub.FundSubId
import anduin.id.fundsub.group.FundSubInvestorGroupId
import anduin.id.review.ReviewStepConfigId
import anduin.model.common.user.UserId
import anduin.review.UpdateReviewStepConfig
import anduin.utils.StringUtils
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.fundsub.client.FundSubSubscriptionDocReviewEndpointClient
import com.anduin.stargazer.fundsub.module.investor.settings.review.model.AdminAndInvestorGroupInfo
import com.anduin.stargazer.fundsub.module.investor.settings.review.widget.ReviewerAssignmentEditor
import com.anduin.stargazer.fundsub.module.copy.FundSubCopyUtils

private[settings] final case class SubscriptionStepConfigDetail(
  fundSubId: FundSubId,
  entityId: EntityId,
  reviewType: FundSubSubscriptionDocReviewType,
  index: Int,
  adminAndInvestorGroupInfo: AdminAndInvestorGroupInfo,
  groupConfig: Seq[UpdateReviewStepConfig],
  groupIdOpt: Option[FundSubInvestorGroupId],
  stepConfig: UpdateReviewStepConfig,
  investorsPendingReviewInStep: Seq[InvestorPendingReviewInfo],
  onUpdateStep: (Seq[UserId], String) => Callback,
  onRemoveStep: Callback,
  onGetInvestorsPendingReviewInStep: Seq[InvestorPendingReviewInfo] => Callback
) {
  val shouldShowWarningBanner: Boolean = investorsPendingReviewInStep.nonEmpty

  def apply(): VdomElement = SubscriptionStepConfigDetail.component(this)
}

private[settings] object SubscriptionStepConfigDetail {
  private type Props = SubscriptionStepConfigDetail

  private final case class State(
    isChecking: Boolean = false
  )

  private final case class Backend(scope: BackendScope[Props, State]) {

    private def renderReviewerBox(
      adminAndInvestorGroupInfo: AdminAndInvestorGroupInfo,
      reviewers: Seq[UserId],
      investorGroupIdOpt: Option[FundSubInvestorGroupId],
      onUpdateReviewers: Seq[UserId] => Callback
    ) = {
      ReviewerAssignmentEditor(
        userIds = reviewers,
        adminAndInvestorGroupInfo = adminAndInvestorGroupInfo,
        investorGroupIdOpt = investorGroupIdOpt,
        isSubscriptionDocReview = true,
        onChange = onUpdateReviewers
      )()
    }

    private def getSubscriptionsInReview(stepId: ReviewStepConfigId): Callback = {
      val cb = for {
        props <- scope.props
        _ <- ZIOUtils.toReactCallback {
          FundSubSubscriptionDocReviewEndpointClient
            .getInvestorsPendingInStep(stepId)
            .map(
              _.fold(
                ex =>
                  scope.modState(
                    _.copy(isChecking = false),
                    Toast.errorCallback(s"Failed to get pending review flow: ${ex.message}")
                  ),
                response =>
                  scope.modState(
                    _.copy(isChecking = false),
                    if (response.isEmpty) { props.onRemoveStep }
                    else { props.onGetInvestorsPendingReviewInStep(response) }
                  )
              )
            )
        }
      } yield ()

      scope.modState(
        _.copy(isChecking = true),
        cb
      )
    }

    def render(props: Props, state: State): VdomElement = {
      <.div(
        tw.spaceY8,
        <.div(
          tw.borderAll.rounded6.border1.borderGray3.flex.px16.pb16,
          tw.bgGray0,
          ^.paddingTop := "8px",
          TagMod.when(props.groupConfig.size > 1) {
            <.div(
              tw.spaceX8.flex.itemsCenter.justifyCenter.mr16,
              <.div(tw.textGray7, IconR(name = Icon.Glyph.Drag, size = Icon.Size.Px16)()),
              <.div(
                tw.roundedFull.borderAll.border2.borderGray4,
                tw.wPx20.hPx20.flex.itemsCenter.justifyCenter,
                tw.fontSemiBold.textGray6.text11,
                s"${props.index + 1}"
              )
            )
          },
          <.div(
            tw.flexFill,
            <.div(
              tw.fontSemiBold.mb4,
              "Reviewers"
            ),
            renderReviewerBox(
              adminAndInvestorGroupInfo = props.adminAndInvestorGroupInfo,
              reviewers = props.stepConfig.reviewers,
              investorGroupIdOpt = props.groupIdOpt,
              onUpdateReviewers = reviewers => {
                val actualReviewers =
                  reviewers.filter(props.adminAndInvestorGroupInfo.adminUserInfos.keys.toSeq.contains(_))
                props.onUpdateStep(actualReviewers, props.stepConfig.description)
              }
            )
          ),
          <.div(
            tw.flexFill.ml16,
            <.div(
              tw.flex.itemsCenter.mb4,
              <.div(
                tw.fontSemiBold,
                "Notes"
              ),
              <.div(
                tw.ml8.textGray7,
                TooltipR(
                  renderTarget = IconR(
                    name = Icon.Glyph.Question,
                    size = Icon.Size.Custom(12)
                  )(),
                  renderContent = _("Team members will see this message when they've been assigned as reviewers")
                )()
              ),
              <.div(
                tw.textGray6.ml8,
                "(optional)"
              )
            ),
            <.div(
              ^.padding := "2px", // to match with the reviewer suggest
              TextBox(
                tpe = TextBox.Tpe.Text,
                size = TextBox.Size.Px32,
                value = props.stepConfig.description,
                onChange = value => props.onUpdateStep(props.stepConfig.reviewers, value),
                placeholder = "Enter optional note or instruction"
              )()
            )
          ),
          TagMod.when(props.groupConfig.size > 1) {
            <.div(
              tw.ml16.flex.itemsCenter.justifyCenter,
              Button(
                style = Button.Style.Minimal(
                  color = Button.Color.Gray9,
                  icon = Option(Icon.Glyph.Cross),
                  height = Button.Height.Fix24,
                  isBusy = state.isChecking
                ),
                isDisabled = props.shouldShowWarningBanner,
                onClick = props.stepConfig.stepIdOpt.fold(props.onRemoveStep) { stepId =>
                  getSubscriptionsInReview(stepId)
                }
              )()
            )
          }
        ),
        TagMod.when(props.shouldShowWarningBanner) {
          WellR(
            style = Well.Style.Warning()
          )(
            WrapperL(
              <.span(
                s"There ${
                    if (props.investorsPendingReviewInStep.length > 1) { "are" }
                    else { "is" }
                  } ",
                PopoverR(
                  renderTarget = (open, _) =>
                    Button(
                      style = Button.Style.Text(),
                      onClick = open
                    )(
                      StringUtils.pluralItem(
                        props.investorsPendingReviewInStep.length,
                        s"${FundSubCopyUtils.getFlowTerm.standAloneTerm}"
                      )
                    ),
                  renderContent = _ =>
                    <.div(
                      ^.maxHeight := "256px",
                      tw.wPx256.overflowYAuto.py12.px16,
                      props.investorsPendingReviewInStep.toVdomArray(
                        using investor => <.div(tw.truncate, investor.firmNameOrFullName)
                      )
                    ),
                  targetWrapper = PortalWrapper.Inline
                )(),
                " currently at this step. Mark them as approved first before updating the review workflow."
              )
            )
          )
        }
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
