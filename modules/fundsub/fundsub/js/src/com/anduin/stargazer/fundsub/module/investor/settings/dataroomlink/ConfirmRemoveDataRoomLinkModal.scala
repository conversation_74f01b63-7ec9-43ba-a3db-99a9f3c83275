// Copyright (C) 2014-2025 Anduin Transactions Inc.

package com.anduin.stargazer.fundsub.module.investor.settings.dataroomlink

import design.anduin.components.button.Button
import design.anduin.components.modal.{ModalBody, ModalFooterWCancel}
import design.anduin.components.toast.Toast
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import anduin.fundsub.endpoint.whitelabel.UpdateSharedDataRoomLinkParams
import anduin.id.fundsub.FundSubId
import com.anduin.stargazer.fundsub.client.FundSubEndpointClient
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
import com.anduin.stargazer.client.utils.ZIOUtils

final case class ConfirmRemoveDataRoomLinkModal(
  fundSubId: FundSubId,
  onCancel: Callback,
  onDone: Callback
) {
  def apply(): VdomElement = ConfirmRemoveDataRoomLinkModal.component(this)
}

object ConfirmRemoveDataRoomLinkModal {
  private type Props = ConfirmRemoveDataRoomLinkModal

  private final case class State(isRemoving: Boolean = false)

  private final case class Backend(scope: BackendScope[Props, State]) {

    private def onRemove = {
      val cb = for {
        props <- scope.props
        _ <- ZIOUtils.toReactCallback(
          FundSubEndpointClient
            .updateSharedDrLink(
              UpdateSharedDataRoomLinkParams(
                props.fundSubId,
                "",
                ""
              )
            )
            .map(
              _.fold(
                _ =>
                  Toast.errorCallback("Failed to remove external website link") >>
                    scope.modState(_.copy(isRemoving = false)),
                _ => props.onDone
              )
            )
        )
      } yield ()

      scope.modState(
        _.copy(isRemoving = true),
        cb
      )
    }

    def render(props: Props, state: State): VdomElement = {
      <.div(
        ModalBody()(
          <.div(
            tw.p20,
            "Investors will no longer see the link in their workspace. Are you sure you want to continue?"
          )
        ),
        ModalFooterWCancel(props.onCancel) {
          Button(
            style = Button.Style.Full(color = Button.Color.Primary, isBusy = state.isRemoving),
            onClick = onRemove
          )("Disable")
        }
      )
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
