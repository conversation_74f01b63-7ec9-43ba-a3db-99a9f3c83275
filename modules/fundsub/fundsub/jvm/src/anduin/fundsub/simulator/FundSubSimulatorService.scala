// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.simulator

import java.time.temporal.ChronoUnit
import java.time.{Instant, LocalDate, ZonedDateTime}
import scala.io.Source

import fundsub.review.supportingdoc.{
  InvestorGroupSupportingDocReviewConfig,
  SupportingDocGroupReviewConfig,
  SupportingDocReviewConfigMode
}
import io.circe.parser.parse
import io.temporal.api.enums.v1.WorkflowIdReusePolicy
import zio.implicits.*
import zio.telemetry.opentelemetry.tracing.Tracing
import zio.temporal.ZRetryOptions
import zio.temporal.workflow.ZWorkflowStub
import zio.{RIO, Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.dataroom.bot.{DataRoomBotService, DataRoomIntegrationBotUser}
import anduin.dataroom.integration.service.{DataRoomExternalIntegrationService, DataRoomInternalIntegrationService}
import anduin.dataroom.role.Member
import anduin.entity.model.EntityModel.EntityTrackingType
import anduin.entity.model.EntityRole
import anduin.fdb.record.{DefaultCluster, FDBRecordDatabase}
import anduin.forms.engine.GaiaState
import anduin.fundsub.comment.FormCommentService
import anduin.fundsub.constants.DemoData
import anduin.fundsub.constants.DemoData.DemoSampleFiles
import anduin.fundsub.dashboard.{ColumnConverter, FundSubDashboardService}
import anduin.fundsub.email.generate.demo.FundSubSimulatorVerificationEmailGenerate
import anduin.fundsub.endpoint.*
import anduin.fundsub.endpoint.DemoFormType.*
import anduin.fundsub.endpoint.admin.*
import anduin.fundsub.endpoint.customdata.UpdateInvestorCustomDataParams
import anduin.fundsub.endpoint.formcomment.{AddCommentReplyParams, CreateCommentParams, ResolveCommentParams}
import anduin.fundsub.endpoint.fundclose.UpdateFundSubCloseParams
import anduin.fundsub.endpoint.lp.{FundSubLpInfo, NameEmailInfo}
import anduin.fundsub.endpoint.operation.*
import anduin.fundsub.endpoint.participant.SetFundSubPointOfContactParams
import anduin.fundsub.endpoint.report.{UpdateFundClosingDateParams, UpdateFundTargetCapitalParams}
import anduin.fundsub.endpoint.review.UpdateSupportingDocReviewConfigParams
import anduin.fundsub.endpoint.simulator.{DemoSessionIdAndCreationTime, DemoSessionInfo, GetSandboxDashboardResponse}
import anduin.fundsub.endpoint.subscriptiondoc.review.{
  FundSubSubscriptionDocReviewConfigMode,
  FundSubSubscriptionDocReviewType,
  SaveSubscriptionDocReviewConfigParams
}
import anduin.fundsub.fundclose.FundSubCloseService
import anduin.fundsub.group.{FundSubGroupMemberService, FundSubGroupService}
import anduin.fundsub.investorgroup.FundSubInvestorGroupService
import anduin.fundsub.models.{FundSubModelStoreOperations, FundSubSgwModelUtils}
import anduin.fundsub.participant.FundSubParticipantService
import anduin.fundsub.rebac.FundSubRebacModel.LevelRelation
import anduin.fundsub.report.FundSubReportService
import anduin.fundsub.ria.FundSubRiaService
import anduin.fundsub.service.*
import anduin.fundsub.simulator.FundSubSimulatorService.*
import anduin.fundsub.simulator.SimulatorData.*
import anduin.fundsub.simulator.fdb.FundSubSimulatorStoreOperations
import anduin.fundsub.simulator.model.*
import anduin.fundsub.subscriptiondoc.review.FundSubSubscriptionDocReviewService
import anduin.fundsub.view.*
import anduin.id.entity.EntityId
import anduin.id.form.FormVersionId
import anduin.id.fundsub.*
import anduin.id.fundsub.group.FundSubInvestorGroupId
import anduin.id.offering.OfferingId
import anduin.investmententity.service.InvestmentEntitySimulatorService
import anduin.link.LinkGeneratorService
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.{UserId, UserInfo}
import anduin.model.id.*
import anduin.model.notichannel.FundSubNotificationChannels
import anduin.model.signature.ESignatureDataCompanion
import anduin.model.user.FullName
import anduin.orgbilling.model.plan.DataRoomPlan
import anduin.portaluser.ExecutiveAdmin
import anduin.protobuf.UserTypeMessage
import anduin.protobuf.actionlogger.formcomment.FormCommentViewSource
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.protobuf.fundsub.*
import anduin.protobuf.fundsub.comment.{AmlKycDocData, FormQuestionData}
import anduin.protobuf.fundsub.models.*
import anduin.protobuf.fundsub.models.customdata.item.*
import anduin.protobuf.fundsub.models.customdata.item.CustomDataMessage.SealedValue
import anduin.protobuf.fundsub.simulator.{FundSubSimulatorProgressModel, FundSubSimulatorProgressStatus}
import anduin.protobuf.signature.SignatureMessage.SignatureMessageEntry
import anduin.protobuf.signature.{SignatureDataBox, SignatureMessage, SignatureTypeMessage}
import anduin.rebac.RebacStoreOperation
import anduin.review.UpdateReviewStepConfig
import anduin.ria.constants.DemoData as RiaDemoData
import anduin.service.entity.EntityService
import anduin.service.entity.invitation.EntityInvitationService
import anduin.service.{AuthenticatedRequestContext, GeneralServiceException, PublicRequestContext, ServiceActor}
import anduin.signature.integration.{SignatureIntegrationService, UserSignatureService}
import anduin.stargazer.service.dataroom.*
import anduin.tapir.endpoint.UpdateOptionOperator
import anduin.telemetry.TelemetryEnvironment
import anduin.temporal.TemporalEnvironment
import anduin.utils.StringUtils
import anduin.workflow.fundsub.simulator.impl.FundSubSimulatorSetupDemoOrdersWorkflowImpl
import anduin.workflow.fundsub.simulator.{DemoFormTypeProto, SetupDemoOrdersParams}
import com.anduin.stargazer.endpoints.AssetPermissionChanges
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.fundsub.free.module.{
  FundSubLpInvitationService,
  ManageFundSubAdminM,
  ManageFundSubLpM
}
import com.anduin.stargazer.service.fundsub.operation.{FundSubOperationDataExtractService, FundSubOperationService}
import com.anduin.stargazer.service.fundsub.{FundSubContactService, FundSubLpService}
import com.anduin.stargazer.service.nats.NatsNotificationService
import com.anduin.stargazer.service.utils.ZIOTelemetryUtils.*
import com.anduin.stargazer.service.utils.{ZIOTelemetryUtils, ZIOUtils}
import com.anduin.stargazer.util.date.DateCalculator
import stargazer.model.routing.DynamicAuthPage

final case class FundSubSimulatorService(
  adminModule: ManageFundSubAdminM,
  entityInvitationService: EntityInvitationService,
  dataRoomInternalIntegrationService: DataRoomInternalIntegrationService,
  dataRoomExternalIntegrationService: DataRoomExternalIntegrationService,
  fundSubContactService: FundSubContactService,
  backendConfig: GondorBackendConfig,
  dataRoomIntegBotUser: DataRoomIntegrationBotUser,
  dataRoomBotService: DataRoomBotService,
  formCommentService: FormCommentService,
  fundSubLpTagUtilService: FundSubLpTagUtilService,
  executiveAdmin: ExecutiveAdmin,
  fundSubPermissionService: FundSubPermissionService,
  fundSubCloseService: FundSubCloseService,
  fundSubOperationService: FundSubOperationService,
  fundSubDashboardService: FundSubDashboardService,
  fundSubSimulatorOperationService: FundSubSimulatorOperationService,
  fundSubSimulatorUtilsService: FundSubSimulatorUtilsService,
  fundSubGroupService: FundSubGroupService,
  signatureIntegrationService: SignatureIntegrationService,
  fundSubParticipantService: FundSubParticipantService,
  fundSubViewService: FundSubViewService,
  userSignatureService: UserSignatureService,
  supportingDocReviewService: SupportingDocReviewService,
  fundSubInvestorGroupService: FundSubInvestorGroupService,
  fundSubWhiteLabelService: FundSubWhiteLabelService,
  fundSubReportService: FundSubReportService,
  tracingEnvironment: TelemetryEnvironment.Tracing,
  temporalEnvironment: TemporalEnvironment,
  natsNotificationService: NatsNotificationService,
  entityService: EntityService,
  investmentEntitySimulatorService: InvestmentEntitySimulatorService,
  fundSubOperationDataExtractService: FundSubOperationDataExtractService,
  fundSubLpInvitationService: FundSubLpInvitationService,
  fundSubLpService: FundSubLpService,
  fundSubRiaService: FundSubRiaService
)(
  using val userProfileService: UserProfileService,
  val fundSubLpDashboardService: FundSubLpDashboardService,
  val fundSubEmailService: FundSubEmailService,
  val fundSubGroupMemberService: FundSubGroupMemberService,
  val linkGeneratorService: LinkGeneratorService,
  val fundSubSubscriptionDocReviewService: FundSubSubscriptionDocReviewService,
  val manageFundSubLpM: ManageFundSubLpM
) {

  private val UnassignedSupportingDocGroupName = "Unassigned document types"

  def startSimulation(
    email: String,
    demoFormType: DemoFormType,
    demoEnvironment: DemoEnvironment,
    httpContext: Option[PublicRequestContext] = None
  ): Task[StartSimulationResponse] = {
    val blacklisted = EmailAddress.unapply(email).forall { email =>
      SimulatorData.blacklistedDomains.contains(email.domain.value)
    }
    for {
      _ <-
        if (blacklisted) {
          fundSubEmailService.sendWarningEmail(email).unit
        } else {
          startSimulationInternal(
            email,
            demoFormType,
            demoEnvironment,
            httpContext,
            createdViaSandboxDashboard = false
          )
        }
    } yield StartSimulationResponse(blacklisted)
  }

  def startSimulatorForAnduinMember(
    demoFormType: DemoFormType,
    demoEnvironment: DemoEnvironment,
    actor: UserId
  ): Task[InternalStartSimulationResponse] = {
    for {
      email <- userProfileService.getEmailAddress(actor).map(_.address)
      res <-
        if (isAnduinEmail(email)) {
          startSimulationInternal(
            email,
            demoFormType,
            demoEnvironment,
            httpContext = None,
            createdViaSandboxDashboard = true
          ).map { (_, fundId) =>
            InternalStartSimulationResponse(
              fundIdOpt = Option(fundId),
              errorMessage = ""
            )
          }.onErrorHandleWith { error =>
            ZIO.attempt(
              InternalStartSimulationResponse(
                fundIdOpt = None,
                errorMessage = error.getMessage
              )
            )
          }
        } else {
          ZIO.attempt(
            InternalStartSimulationResponse(
              fundIdOpt = None,
              errorMessage = "You don't have permission to create demo fund on this page"
            )
          )
        }
    } yield res
  }

  def startSimulatorWithPrivateToken(
    email: String,
    token: String,
    demoFormType: DemoFormType,
    demoEnvironment: DemoEnvironment,
    httpContext: Option[PublicRequestContext] = None
  ): Task[StartSimulationBackdoorResponse] = {
    for {
      _ <- ZIOUtils.failUnless(token == backendConfig.fundSubSimulatorConfig.privateToken) {
        GeneralServiceException("Invalid token")
      }
      (entityId, fundId) <- startSimulationInternal(
        email,
        demoFormType,
        demoEnvironment,
        httpContext,
        createdViaSandboxDashboard = false
      )
      userId <- userProfileService.getUserIdFromEmailAddress(email)
      link <- FundSubSimulatorVerificationEmailGenerate(
        entityId,
        fundId,
        userId,
        FundSubSimulatorProgressId(fundId)
      ).redirectUrlTerm.value.headOption.map(_.getter).getOrElse(ZIO.succeed(""))
    } yield StartSimulationBackdoorResponse(fundId, link)
  }

  private def startSimulationInternal(
    email: String,
    demoFormType: DemoFormType,
    demoEnvironment: DemoEnvironment,
    httpContext: Option[PublicRequestContext],
    createdViaSandboxDashboard: Boolean
  ): Task[(EntityId, FundSubId)] = {
    setupDemoFundSubs(
      email,
      httpContext,
      fundSubConfig = SimulatorData.getSetupConfig(demoFormType),
      additionalSetups = DemoEnvironmentAdditionalSetup.getOrElse(demoEnvironment, Seq.empty),
      demoFormType = demoFormType,
      createdViaSandboxDashboard = createdViaSandboxDashboard
    ).provideEnvironment(tracingEnvironment.environment)
  }

  def setupDemoFundSubs(
    email: String,
    httpContext: Option[PublicRequestContext],
    fundSubConfig: DemoFundDataSetupConfig,
    additionalSetups: Seq[AdditionalSetup], // Apply to all creating Fund
    demoFormType: DemoFormType = DemoFormType.UsDemoForm,
    createdViaSandboxDashboard: Boolean
  ): RIO[Tracing, (EntityId, FundSubId)] = {
    for {
      _ <- ZIO.logInfo(s"Starting simulation for email = $email - fund = ${fundSubConfig.fundData.fundName}")
      (entityId, userId) <-
        // Create new userId for this session
        for {
          adminId <- executiveAdmin.userId
          userId <-
            userProfileService
              .createRegisteredUser(
                userInfo = UserInfo(
                  email,
                  SimulatorData.guestName.firstName,
                  SimulatorData.guestName.lastName
                ),
                userType = UserTypeMessage.RegisteredUser,
                password = Some(SimulatorData.guestPassword)
              )
              .map(_.userId)
          entityId = EntityIdFactory.unsafeRandomId
          _ <- entityService.createEntityInternal(
            name = SimulatorData.orgName,
            alias = SimulatorData.orgName,
            entityTrackingType = EntityTrackingType.Internal,
            Map(adminId -> EntityRole.SuperAdmin),
            entityId,
            None
          )
          _ <- entityInvitationService.addEntityMember(
            userId,
            userId,
            EntityRole.Member,
            entityId,
            skipPermission = true
          )
        } yield (entityId, userId)

      // Create FundSubs
      mainFundSubId <- setupDemoFundSub(
        userId = userId,
        email = email,
        entityId = entityId,
        fundConfig = fundSubConfig,
        additionalSetups = additionalSetups,
        httpContext = httpContext,
        demoFormType = demoFormType
      ).withChildSpan("simulator/setup-demo-fundsub")

      additionalFundSubs <- ZIOUtils.foreachParN(2)(fundSubConfig.additionalFundSetupConfigs) { fundSubConfig =>
        setupDemoFundSub(
          userId = userId,
          email = email,
          entityId = entityId,
          fundConfig = fundSubConfig,
          additionalSetups = additionalSetups,
          httpContext = httpContext,
          demoFormType = demoFormType
        ).withChildSpan("simulator/setup-demo-fundsub").map { fundSubId =>
          (fundSubId = fundSubId, fundSubConfig = fundSubConfig)
        }
      }

      additionalFundSubIds = additionalFundSubs.map(_.fundSubId)

      _ <- fundSubSimulatorOperationService.updateFundSubDemoInfo(mainFundSubId, additionalFundSubIds)
      // Create Progress Bar
      _ <- FDBRecordDatabase.transact(FundSubSimulatorStoreOperations.Production) { op =>
        op.createFundSubSimulatorProgress(
          FundSubSimulatorProgressModel(
            progressId = FundSubSimulatorProgressId(mainFundSubId),
            creator = userId,
            creatorEmail = email,
            startedAt = Some(DateCalculator.instantNow),
            lastUpdatedAt = Some(Instant.now),
            demoFormType = toDisplayString(demoFormType),
            createdViaSandboxDashboard = createdViaSandboxDashboard
          )
        )
      }
      _ <- updateSimulatorProgressTracker(
        fundSubId = mainFundSubId,
        progressStatus = FundSubSimulatorService.ProgressStatus.Start
      )

      // Send email
      _ <- ZIOUtils.unless(createdViaSandboxDashboard) {
        fundSubEmailService.sendVerificationEmail(
          entityId,
          mainFundSubId,
          userId,
          FundSubSimulatorProgressId(mainFundSubId)
        )
      }

      // Async create FundSub Orders
      customColumns <-
        fundSubDashboardService
          .getDashboardGeneralInfo(
            mainFundSubId,
            None,
            userId
          )
          .map(_.availableCustomDataColumns.map(_.id))
          .withChildSpan("simulator/get-dashboard-general-info")

      _ <- ZIOUtils.when(fundSubConfig.shouldSetupDemoOrders) {
        for {
          workflow <- createSetupDemoOrdersWorkflowStub(mainFundSubId)
          mainFundSubLinkId <- fundSubSimulatorOperationService
            .getProtectedLinkId(mainFundSubId)
          additionalFundSubLinkIds <- ZIOUtils.foreachParN(4)(additionalFundSubIds) { fundSubId =>
            fundSubSimulatorOperationService.getProtectedLinkId(fundSubId)
          }
          additionalFundInfos = fundSubConfig.additionalFundSetupConfigs
            .map(_.fundData)
            .zip(additionalFundSubLinkIds)
            .zip(
              additionalFundSubIds
            )
            .map { case ((fundData, linkId), fundSubId) =>
              AdditionalFundInfo(
                fundSubId = fundSubId,
                fundData = Some(fundData),
                linkId = linkId
              )
            }
          _ <- ZWorkflowStub.start(
            workflow.setupDemoOrders(
              SetupDemoOrdersParams(
                entityId = entityId,
                fundSubId = mainFundSubId,
                actor = userId,
                email = email,
                linkId = mainFundSubLinkId,
                fundData = Some(fundSubConfig.fundData),
                additionalFundInfos = additionalFundInfos,
                shouldSendConfirmEmail = fundSubConfig.shouldSendVerificationEmail,
                customDataColumnIds = customColumns.toList,
                shouldDisableSoftReview = additionalSetups.exists(_ match {
                  case AdditionalSetup.SubscriptionDocReviewConfig(shouldDisableSoftReview) => shouldDisableSoftReview
                  case _                                                                    => false
                }),
                demoFormType = toDemoFormTypeProto(demoFormType),
                shouldSetupRiaEntity = fundSubConfig.shouldCreateRiaEntity
              )
            )
          )
          _ <- ZIO.logInfo(s"Started setup demo orders for fund ${mainFundSubId.idString}")
        } yield ()
      }

    } yield (entityId, mainFundSubId)
  }

  def getDemoSampleData(order: Option[DemoOrder], formVersionIdOpt: Option[FormVersionId]): Task[GetDemoDataResponse] =
    formVersionIdOpt match {
      case Some(formVersionId) =>
        order
          .flatMap(_.sampleFiles)
          .flatMap(_.eventFileMap.get(formVersionId))
          .orElse(DemoSampleFiles.eventFileMap.get(formVersionId))
          .map { fileName =>
            fundSubSimulatorUtilsService
              .getDemoSampleState(
                formVersionId.parent,
                formVersionId,
                fileName
              )
              .map(
                _.fold(
                  _ => GaiaState.empty,
                  identity
                )
              )
          }
          .getOrElse(ZIO.attempt(GaiaState.empty))
          .map(state => GetDemoDataResponse(Right(state)))

      case None =>
        val dataFileOpt = order.flatMap(_.sampleFiles).flatMap(_.dataFile).orElse(DemoSampleFiles.dataFile)
        val pageSectionFileOpt =
          order.flatMap(_.sampleFiles).flatMap(_.pageSectionFile).orElse(DemoSampleFiles.pageSectionFile)
        dataFileOpt
          .zip(pageSectionFileOpt)
          .map { case (dataFile, pageSectionFile) =>
            for {
              data <- getDemoSampleData(dataFile)
              pageSections <- getDemoSamplePageSections(pageSectionFile)
            } yield GetDemoDataResponse(Left((data, pageSections)))
          }
          .getOrElse(ZIO.attempt(GetDemoDataResponse(Left((Map.empty, Seq.empty)))))
    }

  private def createSetupDemoOrdersWorkflowStub(fundSubId: FundSubId) =
    FundSubSimulatorSetupDemoOrdersWorkflowImpl.instance
      .getWorkflowStub(
        TemporalWorkflowId.unsafeFromSuffix(s"FundSubSimulatorSetupDemoOrders-${fundSubId.idString}"),
        _.withWorkflowIdReusePolicy(WorkflowIdReusePolicy.WORKFLOW_ID_REUSE_POLICY_REJECT_DUPLICATE)
          .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(1))
      )
      .provideEnvironment(temporalEnvironment.workflowClient)

  def createMarketingDataRoom(
    emailAddress: String,
    entityId: EntityId,
    fundSubLpId: FundSubLpId
  ): Task[List[StartMarketingDataRoomResponse]] = {
    for {
      fundSubUrl <- linkGeneratorService.generateLink(
        offeringId = OfferingId.FundSub,
        page = DynamicAuthPage.FundSubLpPage(fundSubLpId = fundSubLpId)
      )
      resps <- dataRoomInternalIntegrationService.createDemoMarketingDataRoom(
        emailAddress = emailAddress,
        entityId = entityId,
        fundSubUrl = fundSubUrl,
        ctx = PublicRequestContext(Seq.empty, Seq.empty)
      )
    } yield resps
  }

  private def setupDemoFundSub(
    userId: UserId,
    email: String,
    entityId: EntityId,
    fundConfig: DemoFundDataSetupConfig,
    additionalSetups: Seq[AdditionalSetup],
    httpContext: Option[PublicRequestContext],
    demoFormType: DemoFormType
  ): RIO[Tracing, FundSubId] = {
    for {
      // Init data room
      folderIdOpt <-
        if (additionalSetups.contains(SimulatorData.AdditionalSetup.Dataroom)) {
          createDataRoom(
            userId,
            entityId,
            fundConfig.fundData.dataRoomNameForIntegration
          ).withChildSpan("simulator/create-dataroom-for-demo-fundsub")
        } else {
          ZIO.attempt(None)
        }
      // Create demo fund sub
      fundSubId <- createDemoFundSub(
        entityId = entityId,
        email = email,
        userId = userId,
        fundConfig = fundConfig,
        dataRoomFolderIdOpt = folderIdOpt,
        additionalSetups = additionalSetups,
        httpContext = httpContext,
        demoFormType = demoFormType
      )
      _ <- ZIOUtils.traverseOption(fundConfig.exportTemplateConfigOpt) { config =>
        fundSubSimulatorOperationService
          .initExportTemplate(
            config,
            fundConfig.taxForms,
            fundSubId,
            demoFormType
          )
          .withChildSpan("simulator/init-export-template")
      }

      // Create demo investor access with data
      _ <- ZIOUtils.when(fundConfig.shouldCreateDemoInvestorAccess) {
        investmentEntitySimulatorService.createDemoInvestmentEntityForFundSub(userId, fundSubId)
      }
    } yield fundSubId
  }

  // Create data room and return its root folder id
  private def createDataRoom(
    userId: UserId,
    entityId: EntityId,
    dataRoomName: String
  ): Task[Option[FolderId]] = {
    for {
      userInfo <- userProfileService.getUserInfo(userId, useCache = false)
      actor = ServiceActor.defaultServiceActor.copy(
        userId = userId,
        userInfo = userInfo
      )
      dataRoomWorkflowId <-
        dataRoomInternalIntegrationService
          .createDataRoom(
            CreateDataRoomParams(
              dataRoomName,
              entityId,
              None,
              None,
              showIndex = false
            ),
            AuthenticatedRequestContext(
              actor,
              Seq.empty,
              Seq.empty
            ),
            // Assume that we use this simulator within 1 year
            planOpt = Some(DataRoomPlan.DataRoomBusinessPlan(LocalDate.now().plusYears(1)))
          )
          .map(_.dataRoomWorkflowId)
      folderId <- ZIO.attempt(FolderId.channelSystemFolderId(dataRoomWorkflowId))
      // Invite data room integration bot user to the new created data room
      _ <- dataRoomInternalIntegrationService.inviteUsers(
        InviteUsersToDataRoomParams(
          dataRoomWorkflowId,
          Map(
            dataRoomIntegBotUser.userInfo.emailAddressStr -> DataRoomPermissionChanges(
              roleSet = Some(Member()),
              assetPermissions =
                AssetPermissionChanges(recursivePermissions = Map(folderId -> Some(FileFolderPermission.Write)))
            )
          ),
          isToaRequired = false,
          subject = "Subject",
          message = "",
          buttonLabel = "CTA"
        ),
        actor
      )
      // Auto accept the invitation to make sure bot user has access to the data room
      context <- dataRoomExternalIntegrationService.getBotAccountContext()
      _ <- dataRoomBotService.autoAcceptInvitationAndTermsOfAccess(
        AutoAcceptInvitationAndTermsOfAccessParams(dataRoomWorkflowId),
        context
      )
    } yield Some(folderId)
  }

  def onSetupDemoOrdersError(fundSubId: FundSubId): Task[Unit] = {
    updateSimulatorProgressTracker(
      fundSubId = fundSubId,
      progressStatus = FundSubSimulatorService.ProgressStatus.Error
    ).unit
  }

  def setupDemoSignature(actor: UserId): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"Setup demo signature for $actor")
      _ <- signatureIntegrationService.agreeToESignature(agreed = true, actor)
      _ <- signatureIntegrationService.updateSignature(
        SignatureMessage(
          preferredSignatureType = SignatureTypeMessage.Typing,
          signature = Seq(
            SignatureMessageEntry(
              SignatureTypeMessage.Typing,
              Some(
                SignatureDataBox().withTyping(
                  ESignatureDataCompanion.DefaultTypingData.copy(
                    text = SimulatorData.guestName.firstName + " " + SimulatorData.guestName.lastName
                  )
                )
              )
            )
          )
        ),
        actor
      )
    } yield ()
  }

  def resetDemoSignature(actor: UserId): Task[Unit] = {
    userSignatureService
      .updateUserSignature(
        actor,
        _.copy(
          signatureId = None,
          agreedToESignature = false,
          seenESignDragDropTour = false
        )
      )
      .unit
  }

  def completeSetupDemoOrders(fundSubId: FundSubId): Task[Unit] = {
    for {
      lpIdOpt <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubPublicModel(fundSubId).map(_.demoInfo.map(_.demoLpId))
      }
      _ <- updateSimulatorProgressTracker(
        fundSubId = fundSubId,
        progressStatus = FundSubSimulatorService.ProgressStatus.Done(lpIdOpt)
      )
      progressModel <- FDBRecordDatabase.transact(FundSubSimulatorStoreOperations.Production) { op =>
        op.getFundSubSimulatorProgress(FundSubSimulatorProgressId(fundSubId))
      }
      _ <- ZIO.logInfo(
        s"[DEMO SANDBOX TIME] of $fundSubId: ${progressModel.lastUpdatedAt.zip(progressModel.startedAt).map(_.getEpochSecond - _.getEpochSecond).getOrElse(-1)} seconds"
      )
    } yield ()
  }

  def createDemoLpInternal(
    actor: UserId,
    fundSubId: FundSubId,
    fundData: DemoFundData,
    order: DemoOrder,
    customDataColumnIds: List[CustomDataColumnId] = List.empty,
    shouldDisableSoftReview: Boolean = false
  ): RIO[Tracing, CreateDemoLpResponse] = {
    for {
      (lpId, lpUserId, collaboratorIds) <- ZIOTelemetryUtils.traceWithChildSpan("simulator/create-demo-order") {
        createDemoOrder(
          fundSubId,
          actor,
          order,
          customDataColumnIds,
          fundData.formData.flatMap(_.formVersionIds.lastOption)
        )
      }
      _ <- ZIOTelemetryUtils.traceWithChildSpan("simulator/change-lp-status") {
        ZIOUtils.traverseOption(fundData.formData.flatMap(_.formVersionIds.lastOption)) { formVersionId =>
          fundSubSimulatorUtilsService.changeLpStatus(
            order = order,
            formVersionId = formVersionId,
            fundSubId = fundSubId,
            lpId = lpId,
            lpUserId = lpUserId,
            collaboratorIds = collaboratorIds,
            admin = actor,
            shouldDisableSoftReview = shouldDisableSoftReview
          )
        }
      }
      _ <- ZIOUtils.when(order.tags.nonEmpty) {
        ZIOTelemetryUtils.traceWithChildSpan("simulator/update-lp-tags") {
          for {
            allTags <- fundSubLpTagUtilService.getTagByFundSubId(fundSubId)
            tagList <-
              ZIO
                .foreach(order.tags) { tagName =>
                  allTags
                    .find(_.name == tagName.trim)
                    .fold(
                      fundSubLpTagUtilService.createTag(
                        fundSubId,
                        actor,
                        tagName,
                        LpTagColor.Gray1
                      )
                    )(tag => ZIO.attempt(Option(tag)))
                }
                .map(_.flatten)
            _ <- fundSubLpDashboardService.updateTagsOfLp(lpId, tagList.map(_.id).toList, actor)
          } yield ()
        }
      }

      // Mark the demo lp for this demo fundsub
      _ <- ZIOUtils.when(order.isMainLpDemo || order.isAdditionalLpDemo) {
        for {
          _ <- FDBRecordDatabase
            .transact(FundSubModelStoreOperations.Production) { ops =>
              ops.updateFundSubPublicModel(fundSubId) { (model: FundSubPublicModel) =>
                val oldDemoInfo = model.demoInfo.getOrElse(DemoInfo())
                if (order.isMainLpDemo) model.withDemoInfo(oldDemoInfo.withDemoLpId(lpId))
                else model.withDemoInfo(oldDemoInfo.withAdditionalDemoLpIds(oldDemoInfo.additionalDemoLpIds :+ lpId))
              }
            }
          _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubPublicModel(fundSubId)(natsNotificationService)
        } yield ()
      }

      // Populating commenting data
      _ <- ZIOUtils
        .when(order.isCommentingSupported && fundData.formCommentData.nonEmpty) {
          ZIOTelemetryUtils
            .traceWithChildSpan("simulator/create-form-comments") {
              createFormComments(
                lpId,
                lpUserId,
                fundData,
                actor
              )
            }
        }
        .catchAllCause { ex =>
          ZIO.logErrorCause(s"Failed to create form comment: ", ex)
        }

      // Populating AML/KYC commenting
      _ <- ZIOUtils
        .when(order.amlKycCommentThreads.nonEmpty) {
          ZIOTelemetryUtils.traceWithChildSpan("simulator/create-aml-kyc-comments") {
            createAmlKycComments(
              lpId,
              lpUserId,
              actor,
              order.amlKycCommentThreads
            )
          }
        }
        .catchAllCause { ex =>
          ZIO.logErrorCause(s"Failed to create AML/KYC comment: ", ex)
        }

      sideLetterEnabled <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.getFundSubPublicModel(fundSubId)
          .map(_.featureSwitch.exists(_.enableSideLetter))
      )
      _ <- ZIOUtils
        .when(sideLetterEnabled) {
          ZIOTelemetryUtils.traceWithChildSpan("simulator/upload-side-letter") {
            val versions = order.sideLetterPackage.map(_.versions).getOrElse(Seq.empty)
            val folderName = order.sideLetterPackage.map(_.folderName).getOrElse("")
            fundSubSimulatorUtilsService.uploadSideLetter(
              lpId,
              lpUserId,
              actor,
              folderName,
              versions
            )
          }

        }
        .catchAllCause { ex =>
          ZIO.logErrorCause(s"Failed to upload Side Letter", ex)
        }
      _ <- FundSubSgwModelUtils.modifyLastUpdateFundSubPublicModel(fundSubId)(natsNotificationService)
      _ <- ZIO.logInfo(s"Created demo LP with name = ${order.name}")
    } yield CreateDemoLpResponse(lpId)
  }

  private def createFormComments(
    lpId: FundSubLpId,
    lpUserId: UserId,
    fundData: DemoFundData,
    currentDemoUserId: UserId
  ) = {
    for {
      lpFormId <- FDBRecordDatabase.transact(LPDataOperations.Production)(_.getLastFormId(lpId))
      admins <- fundSubPermissionService.getAllFundManagers(lpId.parent)
      _ <- ZIO.foreachDiscard(fundData.formCommentData.zipWithIndex) { (data, index) =>
        val actorOpt = if (data.isPublic) {
          Option(lpUserId)
        } else {
          if (index % 2 == 0) {
            admins.headOption
          } else {
            admins.lastOption
          }
        }
        ZIOUtils.traverseOption(actorOpt) { actor =>
          for {
            formattedCommentWithMentions <- fundSubSimulatorUtilsService.formatCommentForFundsubDemoSimulator(
              data.comment,
              currentDemoUserId,
              lpUserId
            )
            _ <- formCommentService.createComment(
              params = CreateCommentParams(
                fundSubLpId = lpId,
                topicData = FormQuestionData(
                  fundSubLpFormId = lpFormId,
                  fieldAlias = data.fieldAlias,
                  fieldDescription = data.fieldDescription,
                  tocSection = data.tocSection
                ),
                comment = formattedCommentWithMentions,
                viewSource = FormCommentViewSource.FormCommentThreadPanel,
                isPublic = data.isPublic
              ),
              actor = actor,
              httpContext = None
            )
          } yield ()
        }
      }
    } yield ()
  }

  private def createAmlKycComments(
    lpId: FundSubLpId,
    lpUserId: UserId,
    actor: UserId,
    threads: Seq[AmlKycCommentThread]
  ) = {
    ZIO.foreachDiscard(threads) { threadData =>
      createAmlKycCommentThread(
        lpId,
        lpUserId,
        threadData,
        actor
      ).orElse(ZIO.unit)
    }
  }

  private def createAmlKycCommentThread(
    lpId: FundSubLpId,
    lpUserId: UserId,
    threadData: AmlKycCommentThread,
    actor: UserId
  ) = {
    ZIOUtils
      .traverseOption(
        threadData.comments.headOption
      ) { firstComment =>
        for {
          firstCommentor <- fundSubSimulatorUtilsService.getUserIdFromCommentCreatorEmail(
            firstComment.commentorEmail,
            actor
          )
          formattedCommentWithMentions <- fundSubSimulatorUtilsService.formatCommentForFundsubDemoSimulator(
            firstComment.comment,
            actor,
            lpUserId
          )

          threadIdOpt <- formCommentService.createComment(
            params = CreateCommentParams(
              fundSubLpId = lpId,
              topicData = AmlKycDocData(threadData.doctype),
              comment = formattedCommentWithMentions,
              viewSource = FormCommentViewSource.FormCommentThreadPanel,
              isPublic = threadData.isPublic
            ),
            actor = firstCommentor,
            httpContext = None
          )
          threadId <- ZIOUtils.optionToTask(threadIdOpt, new RuntimeException("Could not found threadId"))
          _ <- ZIO
            .foreach(threadData.comments.drop(1)) { comment =>
              for {
                commentor <- fundSubSimulatorUtilsService.getUserIdFromCommentCreatorEmail(comment.commentorEmail, actor)
                formattedCommentWithMentions <- fundSubSimulatorUtilsService.formatCommentForFundsubDemoSimulator(
                  comment.comment,
                  actor,
                  lpUserId
                )
                _ <- formCommentService.addCommentReply(
                  params = AddCommentReplyParams(
                    fundSubLpId = lpId,
                    issueId = threadId,
                    reply = formattedCommentWithMentions,
                    viewSource = FormCommentViewSource.FormCommentThreadPanel
                  ),
                  actor = commentor,
                  httpContext = None
                )
              } yield ()
            }
          _ <- ZIOUtils.when(threadData.isResolved) {
            formCommentService
              .resolveComment(
                ResolveCommentParams(
                  threadId,
                  FormCommentViewSource.FormCommentThreadPanel
                ),
                actor,
                httpContext = None
              )
              .unit
          }
        } yield ()
      }
      .unit
  }

  private def prepareInvestorGroups(
    fundSubId: FundSubId,
    actor: UserId
  ): Task[Map[String, FundSubInvestorGroupId]] = {
    ZIO
      .foreach(DemoData.investorGroups) { investorGroupName =>
        fundSubInvestorGroupService
          .createGroup(
            fundSubId,
            investorGroupName,
            actor
          )
          .map { response =>
            investorGroupName -> response.investorGroupId
          }
      }
      .map(_.toMap)
  }

  private def prepareAmlKycReviewData(
    fundSubId: FundSubId,
    investorGroups: Map[String, FundSubInvestorGroupId],
    actor: UserId
  ) = {
    val task = for {
      madison <- userProfileService.getUserIdFromEmailAddress("<EMAIL>")
      duncan <- userProfileService.getUserIdFromEmailAddress("<EMAIL>")
      noah <- userProfileService.getUserIdFromEmailAddress("<EMAIL>")
      thomas <- userProfileService.getUserIdFromEmailAddress("<EMAIL>")
      usInvestorGroupIdOpt = investorGroups.find(_._1.equalsIgnoreCase(DemoData.usInvestorGroupName)).map(_._2)
      euInvestorGroupIdOpt = investorGroups.find(_._1.equalsIgnoreCase(DemoData.euInvestorGroupName)).map(_._2)
      _ <- supportingDocReviewService.updateFundSubSupportingDocReviewConfig(
        UpdateSupportingDocReviewConfigParams(
          fundSubId = fundSubId,
          mode = SupportingDocReviewConfigMode.SUPPORTING_DOC_REVIEW_CONFIG_MODE_ENABLED_WITH_MULTI_GROUP_CONFIG,
          docGroups = Seq.empty,
          allInvestorGroupReviewConfigOpt = None,
          unassignedInvestorGroupReviewConfigOpt = Option(
            InvestorGroupSupportingDocReviewConfig(
              unassignedDocGroupConfigOpt = Option(
                SupportingDocGroupReviewConfig(
                  groupName = UnassignedSupportingDocGroupName,
                  reviewers = Seq(
                    actor,
                    noah,
                    thomas
                  )
                )
              )
            )
          ),
          investorGroupReviewConfigMap = List(
            usInvestorGroupIdOpt -> InvestorGroupSupportingDocReviewConfig(
              unassignedDocGroupConfigOpt = Option(
                SupportingDocGroupReviewConfig(
                  groupName = UnassignedSupportingDocGroupName,
                  reviewNote = "Please help review tax docs and AML docs for the US investors",
                  reviewers = Seq(
                    actor,
                    duncan
                  )
                )
              )
            ),
            euInvestorGroupIdOpt -> InvestorGroupSupportingDocReviewConfig(
              unassignedDocGroupConfigOpt = Option(
                SupportingDocGroupReviewConfig(
                  groupName = UnassignedSupportingDocGroupName,
                  reviewNote = "Please help review tax docs and AML docs for the EU investors",
                  reviewers = Seq(
                    actor,
                    madison
                  )
                )
              )
            )
          ).flatMap { case (keyOpt, config) =>
            keyOpt.map(_ -> config)
          }.toMap
        ),
        actor
      )
    } yield ()
    task.catchAllCause { ex =>
      ZIO.logErrorCause(
        s"Failed to prepare aml/kyc review config for fund $fundSubId, actor: $actor. Error: ",
        ex
      )
    }
  }

  private def getInvestorGroupConfig(
    reviewers: Seq[UserId],
    firstStepDescription: Option[String],
    secondStepDescription: Option[String],
    actor: UserId,
    isMultiStepReviewEnabled: Boolean
  ): Seq[UpdateReviewStepConfig] = {
    val firstStepConfig = Seq(
      UpdateReviewStepConfig(
        reviewers = reviewers :+ actor,
        description = firstStepDescription.getOrElse("Please review all sessions")
      )
    )
    if (isMultiStepReviewEnabled) {
      firstStepConfig :+ UpdateReviewStepConfig(
        reviewers = Seq(actor),
        description = secondStepDescription.getOrElse("")
      )
    } else {
      firstStepConfig
    }
  }

  private def prepareMultiStepReviewData(
    fundSubId: FundSubId,
    investorGroups: Map[String, FundSubInvestorGroupId],
    actor: UserId,
    shouldDisableSoftReview: Boolean,
    isMultiStepReviewEnabled: Boolean
  ) = {
    for {
      madison <- userProfileService.getUserIdFromEmailAddress("<EMAIL>")
      duncan <- userProfileService.getUserIdFromEmailAddress("<EMAIL>")
      johnathan <- userProfileService.getUserIdFromEmailAddress("<EMAIL>")
      george <- userProfileService.getUserIdFromEmailAddress("<EMAIL>")
      usInvestorGroupIdOpt = investorGroups.find(_._1.equalsIgnoreCase(DemoData.usInvestorGroupName)).map(_._2)
      euInvestorGroupIdOpt = investorGroups.find(_._1.equalsIgnoreCase(DemoData.euInvestorGroupName)).map(_._2)
      _ <- fundSubSubscriptionDocReviewService.saveSubscriptionDocReviewConfig(
        SaveSubscriptionDocReviewConfigParams(
          fundSubId,
          FundSubSubscriptionDocReviewType.SignedSubscription,
          FundSubSubscriptionDocReviewConfigMode.EnableMultipleConfig,
          unassignedInvestorGroupReviewConfig = Seq(
            UpdateReviewStepConfig(
              reviewers = Seq(johnathan, actor)
            )
          ),
          investorGroupReviewConfigs = Seq(
            usInvestorGroupIdOpt -> getInvestorGroupConfig(
              reviewers = Seq(george),
              firstStepDescription = Some("Please review appendix sections").filter(_ => isMultiStepReviewEnabled),
              secondStepDescription = Some("Please review LP signatures"),
              actor = actor,
              isMultiStepReviewEnabled = isMultiStepReviewEnabled
            ),
            euInvestorGroupIdOpt -> getInvestorGroupConfig(
              reviewers = Seq(madison),
              firstStepDescription = Some("Please review appendix sections").filter(_ => isMultiStepReviewEnabled),
              secondStepDescription = Some("Please review LP signatures"),
              actor = actor,
              isMultiStepReviewEnabled = isMultiStepReviewEnabled
            )
          ).flatMap { case (keyOpt, config) =>
            keyOpt.map(_ -> config)
          }.toMap
        ),
        actor
      )
      _ <- ZIOUtils.unless(shouldDisableSoftReview) {
        fundSubSubscriptionDocReviewService
          .saveSubscriptionDocReviewConfig(
            SaveSubscriptionDocReviewConfigParams(
              fundSubId,
              FundSubSubscriptionDocReviewType.UnsignedSubscription,
              FundSubSubscriptionDocReviewConfigMode.EnableMultipleConfig,
              unassignedInvestorGroupReviewConfig = Seq(
                UpdateReviewStepConfig(
                  reviewers = Seq(johnathan, actor)
                )
              ),
              investorGroupReviewConfigs = Seq(
                usInvestorGroupIdOpt -> getInvestorGroupConfig(
                  reviewers = Seq(duncan),
                  firstStepDescription = Some("Please review questionnaire").filter(_ => isMultiStepReviewEnabled),
                  secondStepDescription = Some("Please review all sections and check commitment amount"),
                  actor = actor,
                  isMultiStepReviewEnabled = isMultiStepReviewEnabled
                ),
                euInvestorGroupIdOpt -> getInvestorGroupConfig(
                  reviewers = Seq(madison),
                  firstStepDescription = Some("Please review questionnaire").filter(_ => isMultiStepReviewEnabled),
                  secondStepDescription = Some("Please review all sections and check commitment amount"),
                  actor = actor,
                  isMultiStepReviewEnabled = isMultiStepReviewEnabled
                )
              ).flatMap { case (keyOpt, config) =>
                keyOpt.map(_ -> config)
              }.toMap
            ),
            actor
          )
          .unit
      }
    } yield ()
  }

  def prepareAdditionalGroupAndDashboard(
    fundSubId: FundSubId,
    actor: UserId,
    noteColumnIds: List[CustomDataColumnId]
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"Setup additional groups and dashboards")
      adminUser <- executiveAdmin.userId
      // create business view
      masterDashboardId <- fundSubDashboardService.getMasterDashboardId(fundSubId)
      masterDashboardInfo <- fundSubDashboardService.getDashboardGeneralInfo(
        fundSubId,
        Some(masterDashboardId),
        actor
      )
      businessDashboardDemoData = SimulatorData.FundSubViewDemoData.BusinessDashboard(
        visibleColumnIds = SimulatorData.businessDashboardFixedColumn.map(_.id) ++
          noteColumnIds.map(_.idString) ++
          masterDashboardInfo.formFieldConfigs.map(ColumnConverter.formFieldConfigToColumnId),
        availableColumnIds = masterDashboardInfo.availableTypedColumns.map(_.id) ++
          noteColumnIds.map(_.idString)
      )
      businessViewId <- fundSubViewService.createViewUnsafe(
        fundSubId,
        Some(masterDashboardId),
        businessDashboardDemoData.name,
        actor,
        businessDashboardDemoData.visibleColumnIds.toList,
        businessDashboardDemoData.availableColumnIds.toList,
        isPrivateView = false
      )
      // create business group
      businessGroup = SimulatorData.FundSubGroupDemoData.BusinessGroup(fundSubId)
      businessGroupId <- fundSubGroupService.createGroupUnsafe(
        fundSubId,
        businessGroup.name,
        businessGroup.roleType,
        actor
      )
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)

      // add members
      _ <- ZIO.foreach(businessGroup.invitedMembers) { invitedMember =>
        for {
          invitedUserId <- userProfileService
            .maybeCreateZombieUser(
              invitedMember.email,
              FullName(invitedMember.firstName, invitedMember.lastName)
            )
            .map(_.userId)
          _ <- fundSubGroupMemberService.inviteGroupMemberUnsafe(
            groupId = businessGroupId,
            toAddMemberUserId = invitedUserId,
            actor = adminUser,
            httpContext = None
          )
          _ <- fundSubGroupMemberService.accessFund(
            fundSubId,
            invitedUserId,
            None
          )
        } yield ()
      }
      // update permission map of resource
      _ <- fundSubViewService.updateFundManagerGroupPermissionToDashboard(
        actor = actor,
        dashboardId = businessViewId,
        permissionMap = Map(
          businessGroupId -> Some(LevelRelation.Viewer)
        )
      )
      _ <- FundSubViewUtils.updateViewTrackingModel(businessViewId)(natsNotificationService)
    } yield ()
  }

  private def createDemoFundSub(
    entityId: EntityId,
    email: String,
    userId: UserId,
    fundConfig: DemoFundDataSetupConfig,
    demoFormType: DemoFormType,
    dataRoomFolderIdOpt: Option[FolderId] = None,
    httpContext: Option[PublicRequestContext] = None,
    additionalSetups: Seq[SimulatorData.AdditionalSetup] = Seq.empty,
    fundSubIdOpt: Option[FundSubId] = None
  ) = {
    val userAdminInfo = FundAdminInfo(
      email,
      SimulatorData.guestName.firstName,
      SimulatorData.guestName.lastName,
      FundSubAdminRole.FundSubAdmin
    )
    val featureSwitch = fundSubSimulatorOperationService
      .getFeatureSwitch(demoFormType)
      .copy(enableLpProfile = fundConfig.enableLpProfile)
    val enableStandardDashboard = additionalSetups.exists {
      case AdditionalSetup.StandardDashboard => true
      case _                                 => false
    }
    val importMappingIds = fundConfig.fundData.formTemplateMappingConfigOpt.map(_.mappingVersionId).toSeq
    for {
      adminUser <- executiveAdmin.userId
      investmentFunds <- fundSubSimulatorOperationService.getInvestmentFunds(fundConfig.fundData, demoFormType)
      createFundSubParams = CreateFundSubParams(
        entityId = entityId,
        fundName = fundConfig.fundData.fundName,
        initialAdmins = Seq(userAdminInfo),
        lpFlowType = LpFlowType.Flexible,
        supportingContacts = Seq(SimulatorData.supportingContact),
        formVersionOpt = fundConfig.fundData.formData
          .flatMap(_.formVersionIdOpt)
          .map { formVersionId =>
            FundSubFormVersion(
              formVersionId,
              createdAt = Some(Instant.now),
              name = "Version 1",
              description = DemoData.formUpdateNotes.getOrElse(formVersionId, ""),
              importMappingIds = importMappingIds.filter(_.parent.parent == formVersionId)
            )
          },
        investmentFunds = investmentFunds,
        featureSwitch = featureSwitch,
        lpGeneralConfig = Some(FundSubLpGeneralConfig()),
        signatureConfig = CreateFundSubParams.DefaultSignatureConfig,
        enableStandardDashboard = enableStandardDashboard,
        enableAdvancedDashboard = true,
        flowType = demoFormType match {
          case DemoFormType.LpTransferDemoForm       => FlowType.LpTransfer
          case DemoFormType.MasterSideLetterDemoForm => FlowType.MasterSideLetter
          case _                                     => FlowType.Subscription
        }
      )
      fundSubId <- adminModule.createFundSub(
        createFundSubParams,
        adminUser,
        httpContext,
        fundSubIdOpt
      )
      _ <- fundSubOperationService.updateSupportingFormConfig(
        params = UpdateSupportingFormConfigParams(
          fundSubId = fundSubId,
          taxFormGroups = fundConfig.taxForms.map { case (name, forms) =>
            name -> TaxFormGroup(formVersionIds = forms.flatMap(_.formVersionIdOpt))
          },
          additionalTaxFormVersions = fundConfig.taxForms.flatMap(_._2.flatMap(_.formVersionIdOpt)).toSeq,
          additionalTaxForms = Seq.empty
        ),
        actor = adminUser
      )
      _ <- fundSubOperationService.updateFundSubStorageIntegrationConfig(
        params = UpdateFundSubStorageIntegrationConfigParams(
          fundSubId = fundSubId,
          dataRoomIntegrationConfig = DataRoomIntegrationConfig(
            isEnabled = true,
            rootFolderId = dataRoomFolderIdOpt,
            whenToSend = FundSubStorageWhenToSend.values
          ),
          lpFileDownloadConfig = LpFileDownloadConfig()
        ),
        actor = adminUser
      )
      _ <- fundSubSimulatorOperationService.disableFundEmails(fundSubId, adminUser)
      superAdminGroupId <- ZIOUtils.taskOfOptionToTask(
        fundSubGroupService.getSuperAdminGroupOpt(fundSubId),
        GeneralServiceException("No super admin group")
      )

      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(fundSubId)
      _ <- fundSubGroupMemberService.inviteGroupMemberUnsafe(
        groupId = superAdminGroupId,
        toAddMemberUserId = userId,
        actor = adminUser,
        httpContext = httpContext
      )
      _ <- fundSubGroupMemberService.accessFund(
        fundSubId,
        userId,
        httpContext
      )

      // Edit fundsub form versions
      _ <- ZIO.foreach(fundConfig.fundData.formData.map(_.formVersionIds).toSeq.flatMap(_.tail).zipWithIndex) {
        case (formVersionId, index) =>
          adminModule.editFundSub(
            EditFundSubParams(
              fundSubId = fundSubId,
              fundName = fundConfig.fundData.fundName,
              supportingContacts = Seq(SimulatorData.supportingContact),
              investmentFunds = investmentFunds,
              formVersionOpt = Some(
                FundSubFormVersion(
                  formVersionId,
                  createdAt = Some(Instant.now),
                  name = s"Version ${index + 2}",
                  description = DemoData.formUpdateNotes.getOrElse(formVersionId, ""),
                  importMappingIds = importMappingIds.filter(_.parent.parent == formVersionId)
                )
              ),
              featureSwitch = featureSwitch,
              lpGeneralConfig = Some(FundSubLpGeneralConfig()),
              signatureConfig = CreateFundSubParams.DefaultSignatureConfig
            ),
            adminUser
          )
      }

      fundAdminUserIds <- ZIO.foreach(fundConfig.fundAdmins) { userToInviteInfo =>
        for {
          userToInvite <- userProfileService
            .maybeCreateZombieUser(
              emailAddressStr = userToInviteInfo.email,
              fullName = FullName(userToInviteInfo.firstName, userToInviteInfo.lastName)
            )
            .map(_.userId)
          groupId <- ZIOUtils.taskOfOptionToTask(
            fundSubGroupService.getGroupByOldRoleUnsafe(fundSubId, userToInviteInfo.role),
            GeneralServiceException(s"There is no group for role ${userToInviteInfo.role}")
          )
          _ <- fundSubGroupMemberService.inviteGroupMemberUnsafe(
            groupId = groupId,
            toAddMemberUserId = userToInvite,
            actor = adminUser,
            httpContext = httpContext
          )
          _ <- fundSubGroupMemberService.accessFund(
            fundSubId,
            userToInvite,
            httpContext
          )
        } yield userToInvite
      }
      _ <- ZIO.when(demoFormType == DemoFormType.DataExtractFundForm) {
        fundSubOperationDataExtractService.updateFundSubDataExtractionServiceConfig(
          fundSubId,
          FundSubDataExtractionConfig(
            isEnabled = true,
            dataExtractProjectId = Option(DemoData.dataExtractProjectId),
            isLiveExtractionEnabled = true
          ),
          adminUser
        )
      }
      _ <- fundSubSimulatorOperationService.syncFundData(
        fundSubId = fundSubId,
        params = createFundSubParams,
        fundAdminUserIds = fundAdminUserIds
      )
      _ <- fundSubSimulatorOperationService.prepareMasterDashboardData(
        fundSubId = fundSubId,
        enableStandardDashboard = enableStandardDashboard,
        demoFormType = demoFormType,
        actor = userId
      )
      investorGroups <- prepareInvestorGroups(fundSubId, userId)
      shouldDisableSoftReview = additionalSetups.exists(_ match {
        case AdditionalSetup.SubscriptionDocReviewConfig(shouldDisableSoftReview) => shouldDisableSoftReview
        case _                                                                    => false
      })
      _ <- prepareMultiStepReviewData(
        fundSubId,
        investorGroups,
        userId,
        shouldDisableSoftReview,
        featureSwitch.allowMultiStepInSubscriptionReview
      )
      _ <- ZIO.when(featureSwitch.enableSupportingDocReview) {
        prepareAmlKycReviewData(
          fundSubId,
          investorGroups,
          userId
        )
      }
      _ <- fundSubSimulatorOperationService.setUpReferenceDocs(
        fundSubId,
        userId,
        fundConfig.refDocs
      )
      _ <- fundSubContactService.createFundGroup(
        fundSubId,
        userId,
        fundConfig.fundData.fundName
      )
      _ <- fundSubReportService.updateFundClosingDateUnsafe(
        params = UpdateFundClosingDateParams(
          fundSubId = fundSubId,
          closingDate = Some(LocalDate.now.plusDays(fundConfig.daysUntilClosing))
        ),
        actor = userId
      )
      _ <- fundSubReportService.updateFundTargetCapital(
        params = UpdateFundTargetCapitalParams(
          fundSubId = fundSubId,
          targetCapital = fundConfig.targetCapital
        ),
        actor = userId
      )
      _ <- ZIO.when(demoFormType == DemoFormType.OpenEndedDemoForm) {
        fundSubSimulatorOperationService.updateReportingModelForOpenEndedFund(fundSubId)
      }
      closeInfo <- fundSubCloseService.getFundSubCloseDataInternal(fundSubId).map(_.closeInfo)
      closeIdOpt = closeInfo.headOption.map(_.fundSubCloseId)
      _ <- ZIOUtils.traverseOption(closeIdOpt)(closeId =>
        fundSubCloseService.updateFundSubClose(
          UpdateFundSubCloseParams(
            fundSubCloseId = closeId,
            name = Some("Close 1"),
            customCloseId = Some(""),
            closingDate = UpdateOptionOperator(newValueOpt = Some(Some(ZonedDateTime.now.plusDays(30)))),
            toDefaultClose = false
          ),
          userId
        )
      )
      _ <- fundSubCloseService.createFundSubClose(
        fundSubId,
        name = "Close 2",
        "",
        Some(ZonedDateTime.now.plusDays(70)),
        userId
      )
      _ <- fundSubSimulatorOperationService.setUpProtectedLink(fundSubId)
      _ <- fundSubParticipantService.setFundSubPointOfContact(
        SetFundSubPointOfContactParams(fundSubId, fundAdminUserIds),
        userId
      )
      _ <- ZIO.foreach(additionalSetups) {
        case whiteLabel: AdditionalSetup.WhiteLabel =>
          fundSubSimulatorOperationService.setUpWhiteLabel(fundSubId, whiteLabel)
        case _ =>
          ZIO.unit
      }
    } yield fundSubId
  }

  private def createDemoOrder(
    fundSubId: FundSubId,
    fundAdmin: UserId,
    order: DemoOrder,
    customDataColumnIds: List[CustomDataColumnId],
    formVersionIdOpt: Option[FormVersionId]
  ): Task[(FundSubLpId, UserId, Seq[UserId])] = {
    val (firstName, lastName) = StringUtils.splitName(order.name)
    for {
      (userId, _) <- userProfileService.getUserFromEmailAddress(order.email)
      closeData <- fundSubCloseService.getFundSubCloseDataInternal(fundSubId)
      collaboratorIds <- ZIO.foreach(order.collaborators) {
        case DemoOrderCollaborator(isDemoUser, email, firstName, lastName, _) =>
          if (isDemoUser) {
            ZIO.attempt(fundAdmin)
          } else {
            userProfileService.maybeCreateZombieUser(email, FullName(firstName, lastName)).map(_.userId)
          }
      }
      advisorIds <- ZIO.foreach(order.riaData.map(_.advisors).toSeq.flatten) { advisor =>
        userProfileService
          .maybeCreateZombieUser(
            advisor.email,
            FullName(advisor.firstName, advisor.lastName)
          )
          .map(_.userId)
      }

      closeIdOpt = closeData.closeInfo.lift(order.closeIndex).map(_.fundSubCloseId)
      gpAutofillGaiaStateOpt <- ZIO
        .when(order.isGpAutofilled) {
          ZIOUtils.traverseOption2(formVersionIdOpt) { formVersionId =>
            for {
              eventFile <- ZIOUtils.optionToTask(
                order.sampleFiles.flatMap(_.eventFileMap.get(formVersionId)),
                GeneralServiceException(s"Can't find prefill data for order with email ${order.email}")
              )
              demoState <- fundSubSimulatorUtilsService.getDemoSampleState(
                formVersionId.parent,
                formVersionId,
                eventFile
              )
            } yield demoState.toOption
          }
        }
        .map(_.flatten)
      lpId <- fundSubLpInvitationService.addMainLp(
        fundSubId = fundSubId,
        initialFormData = gpAutofillGaiaStateOpt.map(Right(_)),
        actor = fundAdmin,
        toAddLp = userId,
        toAddCollaborators = collaboratorIds ++ advisorIds,
        fundSubCloseIdToAdd = closeIdOpt,
        lpInfo = FundSubLpInfo(
          lpContact = NameEmailInfo(
            email = order.email,
            firstName = firstName,
            lastName = lastName
          ),
          firmName = order.firmName
        )
      )
      customDataColumnsInfo <- fundSubDashboardService.customDataService.getCustomDataColumnByIds(
        fundId = fundSubId,
        customDataColumnIds = customDataColumnIds,
        actor = fundAdmin
      )
      _ <- ZIO.foreach(customDataColumnsInfo) { customDataColumnInfo =>
        val customDataOpt = customDataColumnInfo.dataType.asMessage.sealedValue match {
          case _: SealedValue.MetadataValue | SealedValue.Empty | _: SealedValue.CurrencyValue |
              _: SealedValue.ChecklistValue =>
            None
          case SealedValue.DateTimeValue(_) =>
            Option(
              DateTimeValue(
                DemoData.DateTimeColumns.find(_._1 == customDataColumnInfo.name).flatMap(_._3.get(order.email))
              )
            )
          case SealedValue.SingleStringValue(_) =>
            Option(
              SingleStringValue(
                DemoData.SingleSelectColumns
                  .filter(_._1 == customDataColumnInfo.name)
                  .flatMap(
                    _._3
                      .get(order.email)
                      .map(StringWithColor(_))
                  )
              )
            )
          case SealedValue.MultipleStringValue(_) =>
            Option(
              MultipleStringValue(
                DemoData.MultipleSelectColumns
                  .filter(_._1 == customDataColumnInfo.name)
                  .flatMap(
                    _._3
                      .get(order.email)
                      .map(_.map(StringWithColor(_)))
                  )
                  .flatten
              )
            )
          case SealedValue.StringValue(_) =>
            Option(
              StringValue(
                DemoData.NoteColumns
                  .find(_._1 == customDataColumnInfo.name)
                  .flatMap(_._3.get(order.email))
                  .fold[String]("")(identity)
              )
            )
        }
        ZIOUtils.traverseOption(customDataOpt) { customData =>
          fundSubDashboardService.customDataService
            .updateInvestorCustomData(
              UpdateInvestorCustomDataParams(
                lpId,
                customDataColumnInfo.id,
                customData
              ),
              fundAdmin
            )
            .unit
        }
      }
      _ <- ZIO.logInfo(s"create demo order ${order.name} finished")
    } yield (lpId, userId, collaboratorIds)
  }

  private def getDemoSampleData(
    fileName: String
  ): Task[Map[String, String]] = for {
    result <- ZIO.attempt {
      val sourceStr = Source.fromResource(s"fundsub/samples/$fileName").mkString
      val data = parse(sourceStr).toOption
      data
        .flatMap(_.asObject)
        .map(_.toMap.map { case (name, json) =>
          name -> json.asString.getOrElse("")
        })
        .getOrElse(Map.empty[String, String])
    }
  } yield result

  private def getDemoSamplePageSections(
    fileName: String
  ): Task[Seq[String]] = for {
    result <- ZIO.attempt {
      val sourceStr = Source.fromResource(s"fundsub/samples/$fileName").mkString
      val sections = parse(sourceStr).toOption
      sections
        .flatMap(_.asArray)
        .map(_.map(_.asString.getOrElse("")))
        .fold(Seq.empty[String])(_.toSeq)
    }
  } yield result

  def initializeRequestChangesCommentData(
    actor: UserId,
    fundSubId: FundSubId,
    requestChangesCommentData: Seq[RequestChangeCommentData]
  ): Task[Unit] = {
    ZIO.foreachDiscard(requestChangesCommentData) { data =>
      for {
        lpUserId <- userProfileService.getUserIdFromEmailAddress(data.lpEmailAddress)
        allLpIds <- fundSubPermissionService.getLpIdsOfUser(fundSubId, lpUserId)
        lpId <- ZIOUtils.optionToTask(
          allLpIds.headOption,
          new RuntimeException(s"No lp id found for lp ${data.lpEmailAddress}")
        )
        lpFormIdOpt <- FDBRecordDatabase.transact(LPDataOperations.Production)(_.getLastFormIdOpt(lpId))
        lpFormId <- ZIOUtils.optionToTask(
          lpFormIdOpt,
          new RuntimeException(s"No lp form id found for lp ${data.lpEmailAddress}")
        )
        _ <- ZIO.foreachDiscard(data.comments) { commentData =>
          for {
            formattedCommentWithMentions <- fundSubSimulatorUtilsService.formatCommentForFundsubDemoSimulator(
              commentData.comment,
              actor,
              lpUserId
            )
            _ <- formCommentService.createComment(
              params = CreateCommentParams(
                fundSubLpId = lpId,
                topicData = FormQuestionData(
                  fundSubLpFormId = lpFormId,
                  fieldAlias = commentData.fieldAlias,
                  fieldDescription = commentData.fieldDescription,
                  tocSection = commentData.tocSection
                ),
                comment = formattedCommentWithMentions,
                viewSource = FormCommentViewSource.FormCommentThreadPanel
              ),
              actor = actor,
              httpContext = None
            )
          } yield ()
        }
      } yield ()
    }
  }

  def updateSimulatorProgressTracker(
    fundSubId: FundSubId,
    progressStatus: FundSubSimulatorService.ProgressStatus
  ): Task[Unit] = {
    for {
      _ <- FDBRecordDatabase.transact(FundSubSimulatorStoreOperations.Production) { op =>
        op.updateFundSubSimulatorProgress(FundSubSimulatorProgressId(fundSubId)) { model =>
          val oldProgress = model.progress
          val nextModel = progressStatus match {
            case ProgressStatus.Start =>
              model
                .withProgress(Math.max(0, oldProgress))
                .withStatus(FundSubSimulatorProgressStatus.Processing)
            case ProgressStatus.Processing(progress) =>
              val refinedProgress = Math.max(0, Math.min(100, progress))
              model
                .withProgress(Math.max(refinedProgress, oldProgress))
                .withStatus(FundSubSimulatorProgressStatus.Processing)
            case ProgressStatus.Done(lpIdOpt) =>
              model
                .withProgress(Math.max(100, oldProgress))
                .withFundSubLpIdOpt(lpIdOpt)
                .withStatus(FundSubSimulatorProgressStatus.Done)
            case ProgressStatus.Error =>
              model
                .withStatus(FundSubSimulatorProgressStatus.Error)
          }
          val now = Instant.now
          if (nextModel.lastUpdatedAt.forall(_.isBefore(now))) nextModel.withLastUpdatedAt(now) else nextModel
        }
      }
      _ <- natsNotificationService.publish(
        FundSubSimulatorProgressId(fundSubId),
        FundSubNotificationChannels.fundSubSimulatorProgress(fundSubId)
      )
    } yield ()
  }

  def getSimulatorProgressPosition(
    progressId: FundSubSimulatorProgressId
  ): Task[Option[Int]] = {
    for {
      simulatorProgresses <- FDBRecordDatabase.transact(FundSubSimulatorStoreOperations.Production) { op =>
        op.getAllProgressModels
      }
      pendingSimulatorProgressIds = simulatorProgresses
        .filter { simulatorProgress =>
          (simulatorProgress.status.isProcessing || simulatorProgress.status.isNotStarted) &&
          simulatorProgress.startedAt.exists(_.compareTo(Instant.now().minus(2, ChronoUnit.HOURS)) >= 0)
        }
        .sortBy { simulatorProgress =>
          simulatorProgress.startedAt
        }
        .map(_.progressId)
      progressPosition = pendingSimulatorProgressIds.zipWithIndex.collectFirst {
        case (currentProgressId, index) if currentProgressId == progressId => index
      }
    } yield progressPosition
  }

  def updateDemoLp(lpId: FundSubLpId): Task[Unit] = {
    val fundSubId = lpId.parent
    FDBRecordDatabase
      .transact(FundSubModelStoreOperations.Production) { ops =>
        ops.updateFundSubPublicModel(fundSubId) { (model: FundSubPublicModel) =>
          model.copy(demoInfo = model.demoInfo.map(_.withDemoLpId(lpId)))
        }
      }
      .unit
  }

  private def isAnduinEmail(email: String) = {
    email.endsWith("anduintransact.com")
  }

  private def getSessionInfo(fundId: FundSubId, session: FundSubSimulatorProgressModel, actor: UserId) = {
    for {
      fsModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.getFundSubPublicModel(fundId)
      )
      whiteLabelData <- fundSubWhiteLabelService.getWhiteLabelData(actor, fundId)
    } yield DemoSessionInfo(
      progressId = FundSubSimulatorProgressId(fundId),
      fundId = fundId,
      entityId = fsModel.investorEntity,
      fundName = fsModel.fundName,
      logoUrl = whiteLabelData.logoUrl.getOrElse(""),
      createdAt = session.startedAt,
      formName = session.demoFormType,
      lpProfileEnabled = fsModel.featureSwitch.exists(_.enableLpProfile)
    )
  }

  def getSandboxDashboardResponse(actor: UserId): Task[GetSandboxDashboardResponse] = {
    for {
      email <- userProfileService.getEmailAddress(actor).map(_.address)
      _ <- ZIOUtils.validate(isAnduinEmail(email))(new RuntimeException("No permission"))
      sessions <- FDBRecordDatabase.transact(FundSubSimulatorStoreOperations.Production) { op =>
        op.getAllProgressModels
      }
      mySessions <- ZIO
        .collectAll(
          sessions
            .filter(_.creator == actor)
            .map { session =>
              getSessionInfo(
                session.progressId.parent,
                session,
                actor
              ).map(Option(_)).onErrorHandleWith { _ =>
                ZIO.attempt(Option.empty)
              }
            }
        )
        .map(_.flatten)
      pendingSessions = sessions
        .filter { session =>
          session.status.isProcessing || session.status.isNotStarted
        }
        .map { session =>
          DemoSessionIdAndCreationTime(
            fundId = session.progressId.parent,
            createdAt = session.startedAt
          )
        }
    } yield GetSandboxDashboardResponse(mySessions, pendingSessions)
  }

  def getSandboxSession(actor: UserId, progressId: FundSubSimulatorProgressId): Task[FundSubSimulatorProgressModel] = {
    for {
      session <- FDBRecordDatabase.transact(FundSubSimulatorStoreOperations.Production) { op =>
        op.getFundSubSimulatorProgress(progressId)
      }
      _ <- ZIOUtils.failWhen(session.creator != actor) {
        GeneralServiceException(s"Actor $actor cannot get sandbox session of user ${session.creator}")
      }
    } yield session
  }

}

object FundSubSimulatorService {

  given demoAdvisorDataConversion: Conversion[DemoOrderRiaAdvisor, RiaDemoData.DemoAdvisorData] = {
    case advisor: DemoOrderRiaAdvisor =>
      RiaDemoData.DemoAdvisorData(
        firstName = advisor.firstName,
        lastName = advisor.lastName,
        email = advisor.email
      )
  }

  def toDemoFormTypeProto(form: DemoFormType): DemoFormTypeProto = {
    form match {
      case UsDemoForm               => DemoFormTypeProto.USDemoForm
      case LuxDemoForm              => DemoFormTypeProto.LuxDemoForm
      case LightLogicDemoForm       => DemoFormTypeProto.LightLogicForm
      case LpTransferDemoForm       => DemoFormTypeProto.LpTransferForm
      case MasterSideLetterDemoForm => DemoFormTypeProto.MasterSideLetterForm
      case OpenEndedDemoForm        => DemoFormTypeProto.OpenEndedForm
      case DataExtractFundForm      => DemoFormTypeProto.DataExtractFund
    }
  }

  sealed trait ProgressStatus derives CanEqual

  object ProgressStatus {

    case object Start extends ProgressStatus

    final case class Processing(progress: Int) extends ProgressStatus

    final case class Done(demoLpIdOpt: Option[FundSubLpId]) extends ProgressStatus

    case object Error extends ProgressStatus

  }

}
