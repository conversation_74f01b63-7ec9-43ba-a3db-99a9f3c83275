// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.subscriptiondoc

import zio.Task

import anduin.account.profile.UserProfileService
import anduin.fundsub.dataextract.FundSubDataExtractJvmUtils
import anduin.fundsub.dataextract.FundSubDataExtractSchema.FundSubDataExtractRequestStatus
import anduin.fundsub.endpoint.subscriptiondoc.*
import anduin.fundsub.model.FundSubSharedClientModel.ParticipantInfo
import anduin.fundsub.submission.version.*
import anduin.fundsub.submission.version.event.{ReviewType, SubmittedDocument}
import anduin.fundsub.submission.version.signature.*
import anduin.protobuf.fundsub.*
import anduin.service.GeneralServiceException
import com.anduin.stargazer.service.utils.ZIOUtils

object FundSubSubscriptionFetchingUtils {

  def convertToSubscriptionVersionBasicInfo(
    versionInfo: SubmissionVersionModel,
    shouldIncludeCueTableMetadata: Boolean = false
  )(
    using userProfileService: UserProfileService
  ): Task[SubscriptionVersionBasicInfo] = {
    val formProgress = versionInfo.formProgress.fold(SubscriptionVersionFormProgress()) { progress =>
      SubscriptionVersionFormProgress(
        formFillingProgress = progress.formFillingProgress,
        missingRequiredFields = progress.missingRequiredFields,
        missingRecommendedFields = progress.missingRecommendedFields
      )
    }
    val packageSubmitStatus = versionInfo.packageSubmitStatus.map { status =>
      SubscriptionPackageSubmitStatus(
        submittedBy = status.submittedBy,
        submittedAt = status.submittedAt,
        isManualSubmit = status.isManualSubmit,
        submittedDocuments = status.submittedDocuments.map { submittedDoc =>
          SubscriptionSubmittedDocument(submittedDoc.document, submittedDoc.versionIndex)
        }
      )
    }
    for {
      relatedUserIds = Set(
        versionInfo.lastModifiedBy.toSeq,
        versionInfo.signatureStatus.asMessage.sealedValue match {
          case SignatureStatusMessage.SealedValue.Empty | _: SignatureStatusMessage.SealedValue.SignatureReused |
              _: SignatureStatusMessage.SealedValue.SignatureRequired |
              _: SignatureStatusMessage.SealedValue.SignatureRequestPending |
              _: SignatureStatusMessage.SealedValue.SignatureBlockByReview =>
            Seq.empty
          case SignatureStatusMessage.SealedValue.SignatureCompleted(value) => value.signatureBy
        },
        versionInfo.requestReviewStatus.asMessage.sealedValue match {
          case RequestReviewStatusMessage.SealedValue.Empty |
              _: RequestReviewStatusMessage.SealedValue.WorkInProgress =>
            Seq.empty
          case RequestReviewStatusMessage.SealedValue.SubmittedForReview(value) => Seq(value.submitBy)
          case RequestReviewStatusMessage.SealedValue.RequestedChanges(value)   => Seq(value.requestBy)
          case RequestReviewStatusMessage.SealedValue.Accepted(value)           => Seq(value.acceptBy)
        }
      ).flatten
      userInfoMap <- userProfileService.batchGetUserInfos(relatedUserIds)
      participantInfoMap = userInfoMap.map { case (userId, userInfo) =>
        userId -> ParticipantInfo.convert(userId, userInfo)
      }
      cueTableMetadataOpt <- ZIOUtils
        .traverseOption(versionInfo.dataExtractRequestIdOpt.filter(_ => shouldIncludeCueTableMetadata))(
          FundSubDataExtractJvmUtils.getRequest(_).map { dataExtractRequest =>
            dataExtractRequest.extractedCueTableMetadataOpt
              .filter(_ => dataExtractRequest.status == FundSubDataExtractRequestStatus.Approved)
          }
        )
        .map(_.flatten)
    } yield SubscriptionVersionBasicInfo(
      versionIndex = versionInfo.versionIndex,
      lpFormVersionId = versionInfo.lpFormId.asNewLpFormIdUnsafe(),
      cueTableMetadataOpt = cueTableMetadataOpt,
      formFiles = versionInfo.formFiles,
      formProgress = formProgress,
      lastModified = versionInfo.lastModified,
      lastModifiedBy = versionInfo.lastModifiedBy,
      reviewStatus = convertReviewStatus(versionInfo.requestReviewStatus),
      signedDocs = getOldSubmittedDocs(versionInfo),
      signatureStatus = convertSignatureStatus(versionInfo.signatureStatus),
      packageSubmitStatus = packageSubmitStatus,
      userInfoMap = participantInfoMap
    )
  }

  def convertSignatureStatus(
    signatureStatus: SignatureStatus
  ): SubscriptionSignatureStatus = {
    signatureStatus match {
      case status: SignatureReused => SubscriptionSignatureStatus.SignatureReusedDeprecated(status.signatureVersionIndex)
      case status: SignatureCompleted =>
        SubscriptionSignatureStatus.Completed(
          signedDocuments = status.signedDocuments,
          signatureType = status.signatureType match {
            case LpSignatureType.SignaturePagesReused => SubscriptionSignatureType.SignaturePagesReused
            case LpSignatureType.UploadSigned         => SubscriptionSignatureType.UploadSigned
            case LpSignatureType.ESignature | LpSignatureType.Unrecognized(_) => SubscriptionSignatureType.ESignature
          },
          signatureBy = status.signatureBy,
          signatureAt = status.signatureAt,
          signaturePagesReusedFromVersionOpt = status.signatureReusedFromSubmissionVersionIndexOpt
        )
      case status: SignatureRequired =>
        SubscriptionSignatureStatus.Required(
          reason = status.reason match {
            case SignatureRequiredReason.RequestedChangeSignature =>
              SubscriptionSignatureRequiredReason.RequestedChangeSignature
            case SignatureRequiredReason.UnableReuseSignedVersion =>
              SubscriptionSignatureRequiredReason.UnableReuseSignedVersion
            case SignatureRequiredReason.NewSignature | _: SignatureRequiredReason.Unrecognized =>
              SubscriptionSignatureRequiredReason.NewSignature
          }
        )
      case _: SignatureRequestPending => SubscriptionSignatureStatus.RequestPending
      case _: SignatureBlockByReview  => SubscriptionSignatureStatus.BlockByReview
      case SignatureStatus.Empty      => SubscriptionSignatureStatus.Empty
    }
  }

  private def convertReviewType(reviewType: ReviewType): SubscriptionReviewType = {
    reviewType match {
      case ReviewType.SubscriptionForm      => SubscriptionReviewType.SubscriptionForm
      case ReviewType.SubscriptionSignedDoc => SubscriptionReviewType.SubscriptionSignedDoc
      case _: ReviewType.Unrecognized       => throw GeneralServiceException("Unrecognized review type")
    }
  }

  def convertSubmittedDocs(submittedDocuments: Seq[SubmittedDocument]): Seq[SubscriptionSubmittedDocument] = {
    submittedDocuments.map { submittedDoc =>
      SubscriptionSubmittedDocument(submittedDoc.document, submittedDoc.versionIndex)
    }
  }

  def convertToOldSubmittedDocs(submittedDocuments: Seq[SubmittedDocument]): Seq[FundSubFile] = {
    submittedDocuments.map(_.document).flatMap {
      case signedDoc: LpSignedDocument =>
        FundSubFile(signedDoc.fileId, FundSubDocType.LpSignedDoc) +:
          signedDoc.certificateFileIdOpt.map(FundSubFile(_, FundSubDocType.SigningCertificate)).toSeq
      case filledDoc: FilledDocument =>
        Seq(FundSubFile(filledDoc.fileId, FundSubDocType.FilledForm))
      case FundSubDocument.Empty =>
        Seq.empty
    }
  }

  def getOldSubmittedDocs(latestVersion: SubmissionVersionModel): Seq[FundSubFile] = {
    convertToOldSubmittedDocs(latestVersion.packageSubmitStatus.toSeq.flatMap(_.submittedDocuments))
  }

  private def convertReviewStatus(
    status: RequestReviewStatus
  ) = {
    status match {
      case _: WorkInProgress => SubscriptionVersionReviewStatus.None
      case status: SubmittedForReview =>
        SubscriptionVersionReviewStatus.Submitted(
          reviewType = convertReviewType(status.reviewType),
          submittedBy = status.submitBy,
          submittedTime = status.submitTime
        )
      case status: RequestedChanges =>
        FundSubSubscriptionDocUtils.convertRequestedChanges(status)
      case status: Accepted =>
        SubscriptionVersionReviewStatus.Approved(
          reviewType = convertReviewType(status.reviewType),
          approvedBy = status.acceptBy,
          approvedTime = status.acceptTime
        )
      case RequestReviewStatus.Empty => SubscriptionVersionReviewStatus.None
    }
  }

}
