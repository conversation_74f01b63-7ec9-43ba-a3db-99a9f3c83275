// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.subscriptiondoc

import java.time.Instant
import java.util.UUID

import io.circe.Json
import zio.temporal.workflow.ZWorkflowStub
import zio.{Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.autofill.ComputeFormMatchingMode
import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.endpoint.signature.SignatureSharedModels.{ESignatureProvider, SignerAuthTypeData}
import anduin.endpoint.signature.{DocusignRecipientAuthData, SignatureProviderParams}
import anduin.entity.repository.EntityModelStoreOperations
import anduin.fdb.record.model.{RecordIO, RecordReadIO}
import anduin.fdb.record.{DefaultCluster, FDBOperations, FDBRecordDatabase}
import anduin.forms.Form.DefaultNamespace
import anduin.forms.FormFieldState
import anduin.forms.endpoint.GetFormResponse
import anduin.forms.engine.GaiaEngine.{EngineConfiguration, EngineContext}
import anduin.forms.engine.{GaiaEngine, GaiaState}
import anduin.forms.model.FormDataSource
import anduin.forms.service.FormService
import anduin.forms.utils.{FormDataConverters, FormDataUtils}
import anduin.fundsub.activitylog.{ActivityLogService, FundSubAdminActivityLogUtils}
import anduin.fundsub.auditlog.FundSubAuditLogService.{AddEventEmailParam, AddEventParam}
import anduin.fundsub.auditlog.{AuditLogActorType, AuditLogEventType, FundSubAuditLogService}
import anduin.fundsub.autoprefill.{
  AllPastSubscriptionInfoForLpAutoPrefill,
  FundInfoForAutoPrefill,
  InvestorInfoForAutoPrefill,
  InvestorUserContactInfo
}
import anduin.fundsub.comment.FormCommentService
import anduin.fundsub.commitment.{FundSubCommitmentHelper, FundSubCommitmentUtils}
import anduin.fundsub.dashboard.LpInfoOperations
import anduin.fundsub.dataextract.FundSubDataExtractJvmUtils
import anduin.fundsub.dataextract.FundSubDataExtractSchema.{FundSubDataExtractRequest, FundSubDataExtractRequestStatus}
import anduin.fundsub.dataextract.request.FundSubDataExtractRequestDiscardReason
import anduin.fundsub.dataextract.service.{
  DataExtractRequestCueTableDataUtils,
  DataExtractRequestFormDataUtils,
  FundSubSubdocDataExtractService
}
import anduin.fundsub.datalakeingestion.FundSubDataLakeIngestionService
import anduin.fundsub.email.generate.{
  AttachedCommentsOnRequestChangeEmail,
  FundSubEmailTemplate,
  FundSubEmailUtils,
  RequestLpChangeEmailGenerate
}
import anduin.fundsub.endpoint.subscriptiondoc.*
import anduin.fundsub.endpoint.subscriptiondoc.review.{
  ApproveSubscriptionVersionParams,
  FundSubSubscriptionDocReviewType,
  RequestChangeSubscriptionVersionParams
}
import anduin.fundsub.flow.FundSubLpFlowStatus
import anduin.fundsub.form.FundSubFormService
import anduin.fundsub.form.utils.{FundSubCommonUtils, LpFormUtils}
import anduin.fundsub.investorgroup.FundSubInvestorGroupUtils
import anduin.fundsub.model.FundSubSharedClientModel.{ParticipantInfo, SubscriptionRequestChangeType}
import anduin.fundsub.model.FundSubSignatureRequestDocType
import anduin.fundsub.models.signature.FundSubSignatureRequestModels
import anduin.fundsub.models.signature.FundSubSignatureRequestModels.FundSubSignatureEnvelopeType.SingleDocTypeEnvelope
import anduin.fundsub.models.signature.FundSubSignatureRequestModels.{
  FundSubSignatureRequestBasic,
  FundSubSignatureRequestStatus,
  FundSubSignerStatus
}
import anduin.fundsub.models.{FundSubLpModelStoreOperations, FundSubModelStoreOperations}
import anduin.fundsub.rebac.FundSubRebacModel.{InvestorPermission, Type}
import anduin.fundsub.service.*
import anduin.fundsub.service.FundSubFormIntegrationService.GaiaFormInfo
import anduin.fundsub.signature.integration.FundSubSignatureIntegrationService
import anduin.fundsub.signature.integration.FundSubSignatureIntegrationService.FundSubSignerParams
import anduin.fundsub.signature.{FundSubSignatureDataUtils, FundSubSignatureJvmUtils, SchwabTemplateUtils}
import anduin.fundsub.status.{FundSubLpStatusHistoryService, LpStatusSharedUtils}
import anduin.fundsub.storageintegration.FundSubStorageIntegrationService
import anduin.fundsub.submission.LPSubmissionOperations
import anduin.fundsub.submission.version.*
import anduin.fundsub.submission.version.event.RequestChangeType.{ChangeSignedDoc, ChangeSubscriptionForm}
import anduin.fundsub.submission.version.event.{FormProgress, ReviewType}
import anduin.fundsub.submission.version.signature.*
import anduin.fundsub.subscriptiondoc.FundSubSubscriptionDocService.{CreateFirstVersionResponse, given}
import anduin.fundsub.subscriptiondoc.FundSubSubscriptionQueryService.GenerateCleanDocDataStrategy
import anduin.fundsub.subscriptiondoc.review.FundSubSubscriptionDocReviewService
import anduin.fundsub.user.FundSubUserService
import anduin.fundsub.utils.{FundSubDataLakeUtils, FundSubFileOperations}
import anduin.fundsub.{FundSubLoggingService, InvestmentEntityHelper, LpFormDataOperations}
import anduin.greylin.GreylinDataService
import anduin.greylin.modelti.SubscriptionOrderVersion
import anduin.greylin.operation.SubscriptionOrderVersionOperations
import anduin.id.entity.EntityId
import anduin.id.form.FormVersionId
import anduin.id.fundsub.*
import anduin.id.fundsub.FundSubLpFolderTypeId.FolderType
import anduin.id.fundsub.dataextract.FundSubDataExtractRequestId
import anduin.id.issuetracker.IssueId
import anduin.id.signature.SignatureRequestId
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.UserId
import anduin.model.id.email.InternalEmailId
import anduin.model.id.{DynamicFormId, FileId, FolderId, TemporalWorkflowId}
import anduin.portaluser.PortalUserService
import anduin.protobuf.actionlogger.event.ClearLpFormValuesDone
import anduin.protobuf.activitylog.GeneralActivity
import anduin.protobuf.activitylog.GeneralActivity.Value
import anduin.protobuf.activitylog.fundsub.admin.*
import anduin.protobuf.external.squants.MoneyMessage
import anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus.LPPendingSubmission
import anduin.protobuf.flow.fundsub.admin.lpdashboard.{LpInfoRecord, LpStatus}
import anduin.protobuf.fundsub.*
import anduin.protobuf.fundsub.activitylog.lp.*
import anduin.protobuf.fundsub.lpformdata.LpFormData
import anduin.protobuf.fundsub.models.lp.FundSubLpModel
import anduin.protobuf.fundsub.models.{FundSubAdminRestrictedModel, FundSubPublicModel}
import anduin.protobuf.issuetracker.IssueComment
import anduin.protobuf.issuetracker.internal.issue.IssueMessage
import anduin.rebac.RebacModel.implicits.given
import anduin.rebac.RebacStoreOperation
import anduin.review.ReviewFlowStatus
import anduin.service.{AuthenticatedRequestContext, GeneralServiceException, RequestContext}
import anduin.temporal.TemporalEnvironment
import anduin.utils.{ScalaUtils, StringUtils}
import anduin.workflow.fundsub.syncinvestoraccess.AutoSaveSubscriptionFormParams
import anduin.workflow.fundsub.syncinvestoraccess.impl.FundSubAutoSaveSubscriptionDataWorkflowImpl
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.actionlogger.ActionLoggerService
import com.anduin.stargazer.service.nats.NatsNotificationService
import com.anduin.stargazer.service.utils.ZIOUtils
import com.anduin.stargazer.util.date.DateCalculator

final case class FundSubSubscriptionDocService(
  formCommentService: FormCommentService,
  fundSubSignatureIntegrationService: FundSubSignatureIntegrationService,
  fundSubEmailService: FundSubEmailService,
  fundSubUserService: FundSubUserService,
  newSupportingDocService: NewSupportingDocService,
  fsLoggingService: FundSubLoggingService,
  fundSubSubscriptionQueryService: FundSubSubscriptionQueryService,
  fundSubSubscriptionCountersignService: FundSubSubscriptionCountersignService,
  fundSubAuditLogService: FundSubAuditLogService,
  autoPrefillService: AutoPrefillService,
  fundSubSubscriptionDocReviewService: FundSubSubscriptionDocReviewService,
  actionLoggerService: ActionLoggerService,
  backendConfig: GondorBackendConfig,
  temporalEnvironment: TemporalEnvironment,
  fundSubSubdocDataExtractService: FundSubSubdocDataExtractService
)(
  using val fundSubPermissionService: FundSubPermissionService,
  val lpDashboardService: FundSubLpDashboardService,
  val formService: FormService,
  val fundSubFormService: FundSubFormService,
  val fundSubStorageIntegrationService: FundSubStorageIntegrationService,
  val fundSubFormIntegrationService: FundSubFormIntegrationService,
  val fileService: FileService,
  val fundSubLpActivityLogService: FundSubLpActivityLogService,
  val dataLakeIngestionService: FundSubDataLakeIngestionService,
  val activityLogService: ActivityLogService,
  val fundSubEmailUtils: FundSubEmailUtils,
  val fundSubSubscriptionSubmitService: FundSubSubscriptionSubmitService,
  val portalUserService: PortalUserService,
  val fundSubLpStatusHistoryService: FundSubLpStatusHistoryService,
  val greylinDataService: GreylinDataService,
  val userProfileService: UserProfileService,
  natsNotificationService: NatsNotificationService
) extends FundSubFileOperations {
  private val parallelism = 64

  given fundSubFileOperation: FundSubFileOperations = this

  def getOriginalSubscriptionFormName(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[String] = {
    for {
      _ <- ZIO.logInfo(s"User $actor get original subscription form name for $lpId")
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      getFormRes <- getLpOriginalSubscriptionForm(lpId, actor)
    } yield getFormRes.formModel.name
  }

  def getOriginalSubscriptionDocuments(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[Seq[FileId]] = {
    for {
      _ <- ZIO.logInfo(s"User $actor get original subscription documents for $lpId")
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      getFormRes <- getLpOriginalSubscriptionForm(lpId, actor)
    } yield {
      getFormRes.formData.uploadedPdf.keySet.toSeq.flatMap(FormDataConverters.fileIdTypeToFileId)
    }
  }

  private def getLpOriginalSubscriptionForm(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[GetFormResponse] = {
    for {
      lpFormVersionId <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        for {
          lpResModel <- ops.getFundSubLpRestrictedModel(FundSubLpRestrictedId(lpId))
          formVersionId <- RecordIO.fromOption(
            lpResModel.formVersionIdOpt,
            GeneralServiceException(s"Form version id is empty for $lpId")
          )
        } yield formVersionId
      }
      getFormRes <- formService.getForm(
        formId = lpFormVersionId.parent,
        Option(lpFormVersionId),
        actor,
        shouldCheckPermission = false
      )
    } yield getFormRes
  }

  def getAllSubscriptionVersionBasicInfo(
    lpId: FundSubLpId,
    actor: UserId,
    shouldIncludeCueTableMetadata: Boolean = false
  ): Task[GetAllSubscriptionVersionBasicInfoResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor get basic info of all versions $lpId")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
      _ <-
        if (shouldIncludeCueTableMetadata) {
          fundSubPermissionService.validateFundManagerCanAccessLpSubscriptionDocsR(lpId, actor)
        } else {
          fundSubPermissionService
            .validateFundManagerCanAccessLpSubscriptionDocsR(lpId, actor)
            .orElse(fundSubPermissionService.validateUserCanServeOnInvestorSideR(lpId, actor))
            .orElse(fundSubPermissionService.validateAnduinEditor(actor))
        }
      allVersions <- FDBRecordDatabase.transact(LPSubmissionOperations.Production) { ops =>
        ops.getAllVersions(lpId)
      }
      allVersionsWithCleanDocs <- ZIO.foreach(allVersions) { versionInfo =>
        fundSubSubscriptionQueryService.getSubscriptionVersionModel(
          versionInfo,
          actor,
          GenerateCleanDocDataStrategy.OnlyIfMissing
        )
      }
      versions <- ZIO.foreach(allVersionsWithCleanDocs) { versionInfo =>
        FundSubSubscriptionFetchingUtils.convertToSubscriptionVersionBasicInfo(
          versionInfo = versionInfo,
          shouldIncludeCueTableMetadata = shouldIncludeCueTableMetadata
        )
      }
    } yield GetAllSubscriptionVersionBasicInfoResponse(versions)
  }

  def getNumberOfSignedVersion(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[Int] = {
    for {
      _ <- ZIO.logInfo(s"User $actor get number of signed version $lpId")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
      _ <- fundSubPermissionService
        .validateFundManagerCanAccessLpSubscriptionDocsR(lpId, actor)
        .orElse(fundSubPermissionService.validateUserCanServeOnInvestorSideR(lpId, actor))
      allVersions <- FDBRecordDatabase.transact(LPSubmissionOperations.Production) { ops =>
        ops.getAllVersions(lpId)
      }
    } yield allVersions.count { subscriptionVersion =>
      subscriptionVersion.signatureStatus match {
        case _: SignatureCompleted => true
        case _                     => false
      }
    }
  }

  def getSubscriptionVersionBasicInfo(
    params: GetSubscriptionVersionBasicInfoParams,
    actor: UserId
  ): Task[GetSubscriptionVersionBasicInfoResponse] = {
    getSubscriptionVersionBasicInfo(
      params.lpId,
      params.versionIndex,
      GenerateCleanDocDataStrategy.OnlyIfMissing,
      actor
    )
  }

  def getSubscriptionVersionBasicInfo(
    lpId: FundSubLpId,
    versionIndex: SubscriptionVersionIndex,
    generateCleanDocDataStrategy: GenerateCleanDocDataStrategy,
    actor: UserId
  ): Task[GetSubscriptionVersionBasicInfoResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor get form info of version $versionIndex")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
      _ <- fundSubPermissionService
        .validateFundManagerCanAccessLpSubscriptionDocsR(lpId, actor)
        .orElse(fundSubPermissionService.validateUserCanServeOnInvestorSideR(lpId, actor))
      versionInfoOpt <- fundSubSubscriptionQueryService.getSubscriptionVersionModelFromVersionIndex(
        lpId,
        versionIndex,
        actor,
        generateCleanDocDataStrategy
      )
      refinedVersionOpt <- ZIOUtils.traverseOption(versionInfoOpt) { versionInfo =>
        FundSubSubscriptionFetchingUtils.convertToSubscriptionVersionBasicInfo(versionInfo)
      }
    } yield GetSubscriptionVersionBasicInfoResponse(refinedVersionOpt)
  }

  def getSubscriptionVersionFormInfo(
    params: GetSubscriptionVersionFormInfoParams,
    actor: UserId
  ): Task[GetSubscriptionVersionFormInfoResponse] = {
    val lpId = params.lpId
    val versionIndex = params.versionIndex
    for {
      _ <- ZIO.logInfo(s"User $actor get form info of lp $lpId - version $versionIndex")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
      _ <- fundSubPermissionService
        .validateFundManagerCanAccessLpSubscriptionDocsR(lpId, actor)
        .orElse(fundSubPermissionService.validateUserCanServeOnInvestorSideR(lpId, actor))
      (lpFormId, lpFormDataOpt) <- FDBRecordDatabase.transact(LPSubmissionOperations.Production) { ops =>
        ops.getFormData(lpId, versionIndex)
      }
      lpFormVersionId = lpFormId.asNewLpFormIdUnsafe().parent
      getFormRes <- formService.getForm(
        formId = lpFormVersionId.parent,
        Option(lpFormVersionId),
        actor,
        shouldCheckPermission = false
      )
    } yield GetSubscriptionVersionFormInfoResponse(
      version = lpFormDataOpt.map { lpFormData =>
        SubscriptionVersionFormInfo(
          params.versionIndex,
          lpFormId.asNewLpFormIdUnsafe(),
          formModel = GaiaFormModel(
            formName = getFormRes.formModel.name,
            formData = getFormRes.formData,
            formVersionId = lpFormVersionId
          ),
          gaiaState = lpFormData.gaiaState,
          lastImportDataSource = lpFormData.lastImportSource
        )
      }
    )
  }

  def getSubscriptionDocSigningType(
    params: GetSubscriptionDocSigningTypeParams,
    actor: UserId
  ): Task[GetSubscriptionDocSigningTypeResponse] = {
    val lpId = params.lpId
    val versionIndex = params.versionIndex
    for {
      _ <- ZIO.logInfo(s"$actor is getting subscription doc signing type for $lpId - version $versionIndex")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
      _ <- fundSubPermissionService
        .validateFundManagerCanAccessLpSubscriptionDocsR(lpId, actor)
        .orElse(fundSubPermissionService.validateUserCanServeOnInvestorSideR(lpId, actor))
      (lpFormId, lpFormDataOpt) <- FDBRecordDatabase.transact(LPSubmissionOperations.Production) { ops =>
        ops.getFormData(lpId, versionIndex)
      }
      (signingTypes, guidelineOpt) <- fundSubFormService.getSubscriptionDocSigningType(
        formVersionId = lpFormId.asNewLpFormIdUnsafe().parent,
        gaiaStateOpt = lpFormDataOpt.map(_.gaiaState),
        actor = actor
      )
    } yield GetSubscriptionDocSigningTypeResponse(signingTypes, guidelineOpt)
  }

  private[fundsub] def updateFormGaiaStateData(
    actorId: UserId,
    lpId: FundSubLpId,
    gaiaFormInfo: FundSubFormIntegrationService.GaiaFormInfo,
    gaiaState: GaiaState,
    isFlexibleFlow: Boolean,
    formDataSourceOpt: Option[FormDataSource]
  ): Task[Unit] = {
    if (isFlexibleFlow) {
      val fillingProgress = FormDataUtils.calculateProgress(gaiaFormInfo.formData.form, gaiaState.defaultStateMap)
      val formProgress = FormProgress(
        formFillingProgress = fillingProgress,
        missingRequiredFields = gaiaFormInfo.missingRequiredFields,
        missingRecommendedFields = gaiaFormInfo.missingRecommendedFields
      )
      FDBRecordDatabase
        .transact(LPSubmissionOperations.Production) { ops =>
          ops.updateFormData(
            lpId,
            actorId,
            formData = gaiaState,
            formProgress = formProgress,
            formDataSourceOpt = formDataSourceOpt
          )
        }
        .unit
    } else {
      LpFormUtils.saveGaiaFormValues(
        gaiaFormInfo,
        gaiaState,
        _ => ZIO.unit,
        formDataSourceOpt
      )
    }
  }

  def getLpFormData(userId: UserId, lpId: FundSubLpId): Task[(FormVersionId, Map[String, Json])] = {
    for {
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, userId)
      (lpFormVersionId, lpData) <- FDBRecordDatabase.transact(
        FDBOperations[(LPDataOperations, LpFormDataOperations)].Production
      ) { case (dataOps, formDataOps) =>
        for {
          lpFormIdOpt <- dataOps.getLastFormIdOpt(lpId)
          formId <- RecordIO.fromOption(
            lpFormIdOpt.flatMap(_.asNewLpFormId),
            new RuntimeException(s"Failed to get last LP form version ID from $lpId")
          )
          res <- formDataOps.getOpt(formId).map { lpFormDataOpt =>
            formId -> lpFormDataOpt
              .flatMap(
                _.namespaceDataMap.get(DefaultNamespace).map(_.values)
              )
              .getOrElse(Map.empty)
              .map { case (k, v) =>
                k.rawAlias -> FormFieldState(v).getValue
              }
          }
        } yield res
      }
    } yield (lpFormVersionId.parent, lpData)
  }

  def getLpFormVersionId(userId: UserId, lpId: FundSubLpId): Task[FormVersionId] = {
    for {
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, userId)
      lpFormVersionId <- FDBRecordDatabase.transact(
        FDBOperations[LPDataOperations].Production
      ) { dataOps =>
        for {
          lpFormIdOpt <- dataOps.getLastFormIdOpt(lpId)
          formId <- RecordIO.fromOption(
            lpFormIdOpt.flatMap(_.asNewLpFormId),
            new RuntimeException(s"Failed to get last LP form version ID from $lpId")
          )
        } yield formId
      }
    } yield lpFormVersionId.parent
  }

  private def getLpRecordFirmName(lpRecord: LpInfoRecord): String = {
    val fullName = s"${lpRecord.firstName} ${lpRecord.lastName}"
    if (!StringUtils.isEmptyOrWhitespace(lpRecord.firmName)) {
      lpRecord.firmName
    } else if (!StringUtils.isEmptyOrWhitespace(fullName)) {
      fullName
    } else {
      lpRecord.email
    }
  }

  def getAllPastSubscriptionForLpAutofill(
    actor: UserId,
    curLpId: FundSubLpId,
    formSimilarityThresholdOpt: Option[Double] = None
  ): Task[AllPastSubscriptionInfoForLpAutoPrefill] = {
    for {
      _ <- fundSubPermissionService.validateUserCanAccessLpView(curLpId, actor)
      allLpIds <- fundSubUserService.getUserFundSubLp(actor)
      pastLpIds = allLpIds.filterNot(_ == curLpId)
      (curEntityId, curEntityModel, enableOntologyFormMatching) <- FDBRecordDatabase.transact(
        FDBOperations[(FundSubModelStoreOperations, EntityModelStoreOperations)].Production
      ) { case (fundSubOps, entityOps) =>
        for {
          curEntityId <- fundSubOps.getFundSubPublicModel(curLpId.parent).map(_.investorEntity)
          enableOntologyFormMatching <- fundSubOps
            .getFundSubPublicModel(curLpId.parent)
            .map(_.featureSwitch.exists(_.enableOntologyFormMatching))
          curEntityModel <- entityOps.getEntityModel(curEntityId)
        } yield (curEntityId, curEntityModel, enableOntologyFormMatching)
      }
      curFundFormId <- autoPrefillService.getFundFormId(curLpId.parent)
      demoFundSubInfoOpt <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubPublicModel(curLpId.parent).map(_.demoInfo)
      }
      pastFundIds = pastLpIds
        .map(_.parent)
        .filter(fundId =>
          demoFundSubInfoOpt.fold(true)(_.demoLpId.parent == fundId)
        ) // When lp in a demo fund, only get pastSub belong to the same funds
      fundInfos <- ZIOUtils
        .foreachParN(parallelism)(pastFundIds.toList)(
          getFundInfoForAutoPrefill(
            actor,
            curFundFormId,
            enableOntologyFormMatching,
            runComputeFormMatchingAsync = true
          )
        )
        .map(_.flatten)
      // filtering Fund Form with similarity higher than threshold
      filteredFundInfos = formSimilarityThresholdOpt.fold(fundInfos)(threshold =>
        fundInfos.filter(_.formSimilarity > threshold)
      )
      // Filter investors by fundInfo query res which already has same entity check
      sameEntityLpIds = pastLpIds.filter(lpId => filteredFundInfos.map(_.fundSubId).contains(lpId.parent))
      // No need to check permissions here since the actor already has the LP role
      lpRecords <- FDBRecordDatabase
        .transact(LpInfoOperations.Production) { ops =>
          RecordIO.parTraverseN(parallelism)(sameEntityLpIds)(ops.getOpt)
        }
        .map(_.flatten.toList)
      // Only get subscriptions where `actor` is in the collaborator list (ignore GP accessing subscription case)
      validLpRecords = lpRecords.filter(rec => rec.userId == actor || rec.collaborators.map(_.userId).contains(actor))
      lastSubscriptionFormModifiedAts <- ZIOUtils.foreachParN(parallelism)(validLpRecords.map(_.lpId)) { lpId =>
        FDBRecordDatabase.transact(LPSubmissionOperations.Production)(
          _.getLastVersionOpt(lpId).map(_.flatMap(_.lastModified))
        )
      }
    } yield {
      // Filter needed FundInfo by validLpRecords
      val fundInfoList =
        filteredFundInfos.filter(fundInfo => validLpRecords.map(_.lpId.parent).contains(fundInfo.fundSubId))
      val investorInfoList = validLpRecords
        .zip(lastSubscriptionFormModifiedAts)
        .map { (lpRecord, lastSubscriptionFormModifiedAt) =>
          val investmentEntity = getLpRecordFirmName(lpRecord)
          val mainLpContact = InvestorUserContactInfo(
            userId = lpRecord.userId,
            email = lpRecord.email,
            firstName = lpRecord.firstName,
            lastName = lpRecord.lastName
          )
          val collaborators = lpRecord.collaborators.map { userInfo =>
            InvestorUserContactInfo(
              userInfo.userId,
              userInfo.email,
              userInfo.firstName,
              userInfo.lastName
            )
          }

          InvestorInfoForAutoPrefill(
            lpId = lpRecord.lpId,
            investmentEntity = investmentEntity,
            mainLpContactInfo = mainLpContact,
            collaboratorContactInfos = collaborators,
            status = lpRecord.status,
            commitmentAmount = FundSubCommitmentUtils.getInUseLpCommitmentDouble(lpRecord.commitments),
            lastUpdated = if (lastSubscriptionFormModifiedAt.isEmpty) {
              lpRecord.lastActiveAt
            } else {
              lastSubscriptionFormModifiedAt
            },
            customId = lpRecord.customId,
            tagNames = Seq.empty,
            conflictLpInfo = None,
            formFillingProgress = lpRecord.formFillingProgress.toDouble
          )
        }

      AllPastSubscriptionInfoForLpAutoPrefill(
        currentFundEntityId = curEntityId,
        currentFundEntityName = curEntityModel.name,
        fundInfoList = fundInfoList,
        investorInfoList = investorInfoList
      )
    }
  }

  // GP autofill
  def getFundInfoForAutoPrefill(
    actor: UserId,
    currentFundFormId: Either[DynamicFormId, FormVersionId],
    enableOntologyFormMatching: Boolean,
    runComputeFormMatchingAsync: Boolean = false,
    entityIdToCheckOpt: Option[EntityId] = None
  )(
    toGetInfoFundSubId: FundSubId
  ): Task[Option[FundInfoForAutoPrefill]] = {
    for {
      (fundSubModel, fundSubAdminModel) <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        for {
          fundSubModel <- ops.getFundSubPublicModel(toGetInfoFundSubId)
          adminResModel <-
            if (entityIdToCheckOpt.exists(_ != fundSubModel.investorEntity)) {
              // Avoid loading restricted model for funds from different entities
              RecordReadIO.succeed(FundSubAdminRestrictedModel())
            } else {
              ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(toGetInfoFundSubId))
            }
        } yield fundSubModel -> adminResModel
      }
      res <-
        if (entityIdToCheckOpt.exists(_ != fundSubModel.investorEntity)) {
          // Ignore funds which are not in the same entity with the current fund
          ZIO.attempt(None)
        } else {
          for {
            matchingResOpt <- autoPrefillService
              .computeFundFormMatchingGivenDestinationForm(
                currentFundFormId,
                toGetInfoFundSubId,
                actor,
                matchingMode = if (enableOntologyFormMatching) {
                  ComputeFormMatchingMode.AutoSelect
                } else {
                  ComputeFormMatchingMode.AliasAsaHeuristic
                },
                runComputeFormMatchingAsync
              )
              .map(Option(_))
              .catchAllCause { ex =>
                ZIO
                  .logWarningCause(
                    s"Failed to getFundInfoForAutoPrefill [srcFormId: $currentFundFormId, targetFundId: $toGetInfoFundSubId]. Error: ",
                    ex
                  )
                  .as(None)
              }
          } yield matchingResOpt.map { matchingRes =>
            val currencies = fundSubAdminModel.investmentFunds.map(_.currency)
            val currencyOpt = if (currencies.size == 1) currencies.headOption else None
            FundInfoForAutoPrefill(
              toGetInfoFundSubId,
              fundName = fundSubModel.fundName,
              numOfInvestors = fundSubAdminModel.lpIds.size,
              currencyOpt = currencyOpt,
              formSimilarity = matchingRes.matchingScore,
              demoInfoOpt = fundSubModel.demoInfo,
              computeSimilarityStatus = matchingRes.computeMatchingStatus,
              similarityStatusUpdatedAt = matchingRes.statusUpdateAt
            )
          }
        }
    } yield res

  }

  def clearLpFormValues(
    lpId: FundSubLpId,
    actorId: UserId,
    httpContextOpt: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actorId is clearing out form values of subscription $lpId")
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actorId)
      fsModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubPublicModel(lpId.parent)
      }
      lpFormInfo <- FundSubCommonUtils.getLpFormInfo(lpId, actorId)
      _ <- lpFormInfo match {
        case dynamicFormInfo: FundSubFormIntegrationService.DynamicFormInfo =>
          LpFormUtils.saveDynamicFormValues(
            dynamicFormInfo,
            Map.empty,
            _ => ZIO.unit
          )
        case gaiaFormInfo: FundSubFormIntegrationService.GaiaFormInfo =>
          val emptyStateEither = GaiaEngine
            .make(
              gaiaFormInfo.formData.form,
              EngineConfiguration.default,
              EngineContext.default
            )
            .flatMap(_.replay(List.empty).map(_._1))

          emptyStateEither.fold(
            err =>
              ZIO.fail(
                GeneralServiceException(
                  s"Failed to generate empty state for subscription of $lpId. Error: $err"
                )
              ),
            emptyState =>
              updateFormGaiaStateData(
                actorId,
                lpId,
                gaiaFormInfo,
                emptyState,
                fsModel.lpFlowType.isFlexible,
                Option(FormDataSource.Empty)
              )
          )
      }
      _ <- fsLoggingService.actionLoggerService.addEventLog(
        actorId,
        Seq(
          ClearLpFormValuesDone(lpId)
        ),
        httpContextOpt
      )
    } yield ()
  }

  def saveSubscriptionFormData(
    params: SaveSubscriptionFormDataParams,
    actor: UserId,
    httpContext: Option[RequestContext] = None
  ): Task[SaveSubscriptionFormDataResponse] = {
    val lpId = params.fundSubLpId
    val newGaiaState = params.gaiaState
    val formVersionId = params.formVersionId
    for {
      _ <- ZIO.logInfo(s"User $actor save subscription form data $lpId")
      _ <- fundSubPermissionService
        .validateUserCanAccessLpView(lpId, actor)
        .orElse(fundSubPermissionService.validateAnduinEditor(actor))
      _ <- fundSubPermissionService.validateLpStatus(
        lpId,
        Set(LpStatus.LPInProgress, LpStatus.LPChangeInProgress)
      )
      _ <- ZIOUtils.validate(newGaiaState.nonEmpty)(
        GeneralServiceException("Cannot save form empty state data")
      )
      lastVersionIndex <- FDBRecordDatabase.transact(LPSubmissionOperations.Production)(_.getLastVersionIndex(lpId))
      gaiaFormInfo <- fundSubSubscriptionQueryService.getSubmissionVersionGaiaFormInfo(
        lpId,
        lastVersionIndex,
        actor
      )
      _ <- ZIOUtils.failUnless(gaiaFormInfo.lpFormIdUnsafe.parent == formVersionId)(
        GeneralServiceException(
          s"Cannot save form data, version $formVersionId is outdated, expect version ${gaiaFormInfo.lpFormIdOpt}"
        )
      )
      updatedGaiaFormInfo = gaiaFormInfo.copy(gaiaState = newGaiaState)
      formFillingProgress = FormDataUtils.calculateProgress(
        updatedGaiaFormInfo.formData.form,
        updatedGaiaFormInfo.gaiaState.defaultStateMap
      )
      formProgress = FormProgress(
        formFillingProgress = formFillingProgress,
        missingRequiredFields = updatedGaiaFormInfo.missingRequiredFields,
        missingRecommendedFields = updatedGaiaFormInfo.missingRecommendedFields
      )
      (updatedVersion, oldVersion, oldDataOpt) <- FDBRecordDatabase.transact(LPSubmissionOperations.Production) { ops =>
        ops.updateFormData(
          lpId,
          actor,
          formData = updatedGaiaFormInfo.gaiaState,
          formProgress = formProgress
        )
      }
      adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(lpId.parent))
      }
      _ <- FundSubCommitmentHelper.lpUpdateSubmittedCommitment(
        lpId = lpId,
        subFundAmounts = fundSubFormService.getSubmittedAmounts(adminResModel.investmentFunds, updatedGaiaFormInfo)
      )
      investmentEntityNameOpt <- fundSubFormService.getInvestmentEntity(updatedGaiaFormInfo)
      _ <- ZIO.foreach(investmentEntityNameOpt) { investmentEntityName =>
        InvestmentEntityHelper
          .updateFirmName(
            lpId,
            investmentEntityName,
            isFromFormData = true,
            actor = actor
          )(natsNotificationService)
      }
      _ <- ZIOUtils.when(shouldUpdateRequiredDocList(formProgress)) {
        val requiredFiles = fundSubFormService.getFormRequiredDocs(updatedGaiaFormInfo)
        newSupportingDocService.updateFormRequiredDocs(lpId, requiredFiles)
      }
      _ <- lpDashboardService.updateLpInfoRecord(
        lpId,
        _.copy(formFillingProgress = formFillingProgress)
      )
      _ <- fundSubLpActivityLogService.formEdit(lpId, actor)
      _ <- FundSubDataLakeUtils.updateLpFormData(
        lpId = lpId,
        form = gaiaFormInfo.formData.form,
        state = newGaiaState,
        progress = formFillingProgress,
        missingRequiredFields = formProgress.missingRequiredFields,
        missingRecommendedFields = formProgress.missingRecommendedFields,
        existingProgressOpt = oldVersion.formProgress,
        existingFormDataOpt = oldDataOpt
      )(dataLakeIngestionService)
      _ <- FundSubGreylinDataService.updateFormProgress(
        lpId = lpId,
        formProgress = formProgress.formFillingProgress
      )
      _ <- {
        val oldVersionCompleted = oldVersion.formProgress.exists(_.missingRequiredFields == 0)
        val updatedVersionCompleted = updatedVersion.formProgress.exists(_.missingRequiredFields == 0)
        ZIOUtils.when(!oldVersionCompleted && updatedVersionCompleted) {
          fsLoggingService.logEventFormFilled(
            userId = actor,
            fundSubLpId = lpId,
            httpContext = httpContext
          )
        }
      }
    } yield SaveSubscriptionFormDataResponse(updatedVersion.lastModified, formFillingProgress)
  }

  def updateSubscriptionFormVersion(
    lpId: FundSubLpId,
    actor: UserId,
    lpFormData: LpFormData,
    gaiaFormInfo: FundSubFormIntegrationService.GaiaFormInfo
  ): Task[Unit] = {
    for {
      formFillingProgress <- ZIO.attempt(
        FormDataUtils.calculateProgress(
          gaiaFormInfo.formData.form,
          gaiaFormInfo.gaiaState.defaultStateMap
        )
      )
      formProgress = FormProgress(
        formFillingProgress = formFillingProgress,
        missingRequiredFields = gaiaFormInfo.allErrorFields.map(_._2.size).sum,
        missingRecommendedFields = gaiaFormInfo.allWarningFields.map(_._2.size).sum
      )
      latestVersion <- FDBRecordDatabase.transact(LPSubmissionOperations.Production)(
        _.updateFormVersion(
          lpId,
          actor,
          lpFormData,
          formProgress = formProgress
        )
      )
      _ <- lpDashboardService.updateLpInfoRecord(
        lpId,
        _.copy(formFillingProgress = formFillingProgress)
      )
      inProgressSignatureRequests <- fundSubSignatureIntegrationService.getSubscriptionDocSignatureRequestsMetadata(
        lpId,
        statuses = Set(FundSubSignatureRequestModels.FundSubSignatureRequestStatus.InProgress),
        versionIndex = latestVersion.versionIndex
      )
      shouldRegenerateCleanDocData = {
        val hasInProgressRequests = inProgressSignatureRequests.nonEmpty
        val hasSignedDocs = ScalaUtils.isMatch[SignatureCompleted](latestVersion.signatureStatus)
        val hasCleanDocsData = latestVersion.formFiles.nonEmpty
        val hasSubmitted = latestVersion.packageSubmitStatus.nonEmpty
        hasCleanDocsData && !hasInProgressRequests && !hasSignedDocs && !hasSubmitted
      }
      _ <- ZIOUtils.when(shouldRegenerateCleanDocData)(
        fundSubSubscriptionQueryService.getSubscriptionVersionModelFromVersionIndex(
          lpId = lpId,
          versionIndex = SubscriptionVersionIndex.Latest,
          actor = actor,
          generateCleanDocDataStrategy = GenerateCleanDocDataStrategy.ForceRegenerate,
          // This API is designed to be used by the internal users who don't have the permission to access LPs' files and folders
          shouldBypassLpFolderPermissionCheck = true
        )
      )
      _ <- ZIOUtils.when(shouldRegenerateCleanDocData && shouldUpdateRequiredDocList(formProgress)) {
        val requiredFiles = fundSubFormService.getFormRequiredDocs(gaiaFormInfo)
        newSupportingDocService.updateFormRequiredDocs(lpId, requiredFiles)
      }
      _ <- greylinDataService.runUnit(
        SubscriptionOrderVersionOperations.updateFormDataId(
          latestVersion.fundSubLpId,
          latestVersion.versionIndex,
          latestVersion.lpFormId.asNewLpFormIdUnsafe()
        )
      )
    } yield ()
  }

  private def shouldUpdateRequiredDocList(formProgress: FormProgress): Boolean =
    formProgress.missingRequiredFields == 0

  // TODO: activity log for LpCancelledSubmission and UndidSubscriptionPackage
  def startEditingSubscriptionForm(
    params: StartEditingSubscriptionFormParams,
    actor: UserId
  ): Task[StartEditingSubscriptionFormResponse] = {
    val lpId = params.fundSubLpId
    for {
      _ <- ZIO.logInfo(s"User $actor start editing subscription form $lpId")
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      lpStatus <- fundSubPermissionService.validateLpStatus(
        lpId,
        Set(
          LpStatus.LPNotStarted,
          LpStatus.LPInProgress,
          LpStatus.LPChangeInProgress,
          LpStatus.LPPendingUnsignedReview,
          LpStatus.LPFormReviewed,
          LpStatus.LPRequestedSignature,
          LpStatus.LPPendingSubmission,
          LpStatus.LPPendingReview,
          LpStatus.LPSubmitted,
          LpStatus.LPCountersigned
        )
      )
      _ <- fundSubPermissionService.validateFundActive(params.fundSubLpId.parent)
      latestVersion <- createFirstVersionIfNotExisted(lpId, actor)
      _ <- fundSubSubscriptionCountersignService.removePendingAndDistributedCounterSignedDocs(lpId)
      _ <- fundSubSubscriptionCountersignService.cancelAndRemoveCounterSignRequest(lpId, actor)
      _ <- cancelLatestVersionSignatureRequest(lpId, actor)
      shouldCreateNewVersion = {
        val hasReviewStatus = latestVersion.requestReviewStatus match {
          case _: SubmittedForReview | _: RequestedChanges | _: Accepted => true
          case _                                                         => false
        }
        val hasSignatureCompleted = ScalaUtils.isMatch[SignatureCompleted](latestVersion.signatureStatus)
        val hasSubmittedSubscription = latestVersion.packageSubmitStatus.nonEmpty
        hasReviewStatus || hasSignatureCompleted || hasSubmittedSubscription
      }
      _ <- cancelSubmittedSubscriptionReview(lpId, actor)
      _ = latestVersion.requestReviewStatus.asMessage.sealedValue.requestedChanges
      versionToEdit <-
        if (shouldCreateNewVersion) {
          createNewVersion(
            lpId,
            actor,
            GenerateCleanDocDataStrategy.ForceRegenerate
          )
        } else {
          ZIO.succeed(latestVersion)
        }
      _ <- markLpHasEditedFormIfApplicable(lpId, actor)
      _ <- ZIOUtils.when(lpStatus != LpStatus.LPInProgress && lpStatus != LpStatus.LPChangeInProgress) {
        FundSubLpFlowStatus.updateLpFlowStatus(
          lpId,
          LpStatus.LPInProgress
        )(natsNotificationService)
      }
    } yield StartEditingSubscriptionFormResponse(versionToEdit.versionIndex)
  }

  def submitSubscriptionVersionForReview(
    params: SubmitSubscriptionVersionForReviewParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext] = None
  ): Task[Unit] = {
    val lpId = params.fundSubLpId
    for {
      _ <- ZIO.logInfo(s"User $actor submit version for review $lpId")
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      _ <- fundSubPermissionService.validateLpStatus(
        lpId,
        Set(LpStatus.LPInProgress, LpStatus.LPChangeInProgress)
      )
      _ <- fundSubPermissionService.validateFundActive(params.fundSubLpId.parent)
      latestVersion <- fundSubSubscriptionQueryService.getSubscriptionVersionModelFromVersionIndex(
        lpId,
        versionIndex = SubscriptionVersionIndex.Latest,
        actor,
        GenerateCleanDocDataStrategy.OnlyIfMissing
      )
      _ <- ZIOUtils.validate(latestVersion.exists(_.packageSubmitStatus.isEmpty))(
        GeneralServiceException("LP cannot submit version for review after submitted")
      )
      (isEnabledReview, reviewers) <- attemptToSubmitUnsignedSubscriptionForReview(
        lpId,
        latestVersion.map(_.versionIndex).getOrElse(1),
        actor
      )
      _ <- ZIOUtils.validate(isEnabledReview)(
        GeneralServiceException("Unsigned review function is not enabled")
      )
      (fsModel, lpModel) <- FDBRecordDatabase.transact(
        FDBOperations[((LPSubmissionOperations, FundSubModelStoreOperations), FundSubLpModelStoreOperations)].Production
      ) { case ((lpSubmissionOps, fsModelOps), lpModelOps) =>
        for {
          pendingReviewVersion <- lpSubmissionOps.submitToReview(
            lpId,
            actor,
            ReviewType.SubscriptionForm
          )
          fsModel <- fsModelOps.getFundSubPublicModel(lpId.parent)
          _ <- {
            // If LP submits for review before signature, if fund enforce review, we have to make sure LP signature is blocked
            val enableEnforceReviewBeforeSignature =
              fsModel.featureSwitch.exists(_.enableEnforceLpSubmitReviewBeforeSignature)
            val isEnabledUnsignedReview = isEnabledReview
            val shouldBlockSignatureForReview = enableEnforceReviewBeforeSignature && isEnabledUnsignedReview
            val signatureStatusIsBlockByReview =
              ScalaUtils.isMatch[SignatureBlockByReview](pendingReviewVersion.signatureStatus)
            RecordIO.when(shouldBlockSignatureForReview && !signatureStatusIsBlockByReview)(
              lpSubmissionOps.updateSignatureStatus(
                lpId,
                actor,
                SignatureBlockByReview()
              )
            )
          }
          lpModel <- lpModelOps.getFundSubLpModel(lpId)
        } yield (fsModel, lpModel)
      }
      _ <- FundSubLpFlowStatus.updateLpFlowStatus(
        lpId,
        LpStatus.LPPendingUnsignedReview
      )(natsNotificationService)
      _ <- fundSubStorageIntegrationService.syncFilesToDataRoomIntegration(
        lpId,
        actor,
        latestVersion
          .map(_.formFiles.values.toSeq)
          .getOrElse(Seq.empty)
          .map(_ -> FundSubStorageWhenToSend.LP_SUBMIT_FOR_SOFT_REVIEW),
        ctx
      )
      emailIds <- fundSubEmailService
        .sendUnsignedDocumentReadyForReviewEmail(
          fsModel.investorEntity,
          lpId,
          lpModel.mainLp,
          actor,
          reviewers
        )
        .map(_.flatMap(_.internalIdOpt))
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = lpId.parent,
        params = AddEventParam(
          actor = Some(actor),
          actorType = AuditLogActorType.InvestorSide,
          eventType = AuditLogEventType.SUBSCRIPTION_DOCUMENT_SUBMITTED_FOR_INITIAL_REVIEW,
          orderId = Option(lpId),
          eventEmail = Seq(
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.unsignedDocumentReadyForReview,
              emailIds = emailIds
            )
          ),
          activityDetail = LpSubmitForSoftReview(
            versionIndex = latestVersion.fold(1)(_.versionIndex)
          )
        )
      )
      _ <- fundSubLpActivityLogService.logActivity(
        lpId = lpId,
        actorOpt = Option(actor),
        at = None,
        detail = LpSubmitForSoftReview(
          versionIndex = latestVersion.fold(1)(_.versionIndex)
        )
      )
      _ <- fsLoggingService.logEventRequestReview(
        actor,
        params.fundSubLpId,
        fsModel.fundName,
        isSoftReview = true,
        ctx
      )
    } yield ()
  }

  private def attemptToSubmitUnsignedSubscriptionForReview(
    lpId: FundSubLpId,
    versionIndex: Int,
    actor: UserId
  ): Task[(Boolean, Seq[UserId])] = {
    for {
      reviewFlowOpt <- fundSubSubscriptionDocReviewService.attemptToStartReviewFlowInternal(
        lpId,
        versionIndex,
        reviewType = FundSubSubscriptionDocReviewType.UnsignedSubscription,
        actor
      )
      reviewers = reviewFlowOpt.flatMap(_.currentStepOpt).map(_.reviewers).getOrElse(Seq.empty)
    } yield reviewFlowOpt.nonEmpty -> reviewers.map(_.userId)
  }

  def approveSubscriptionVersion(
    params: ApproveSubscriptionVersionParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext] = None
  ): Task[Boolean] = {
    val lpId = params.fundSubLpId
    for {
      _ <- ZIO.logInfo(s"$actor approve subscription type ${params.reviewType} of $lpId")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
      _ <- fundSubPermissionService.validateFundManagerCanAccessLpSubscriptionDocsR(lpId, actor)
      _ <- fundSubPermissionService.validateLpStatusFn(
        lpId,
        {
          case LpStatus.LPPendingUnsignedReview =>
            params.reviewType == FundSubSubscriptionDocReviewType.UnsignedSubscription
          case LpStatus.LPPendingReview => params.reviewType == FundSubSubscriptionDocReviewType.SignedSubscription
          case _                        => false
        }
      )
      (isReviewFlowDone, nextReviewers) <- approveSubscriptionVersionReviewFlow(
        lpId,
        params.reviewType,
        comment = params.emailTemplateMessageOpt.flatMap(_.body).flatMap(_.html).getOrElse(""),
        actor
      )
      _ <-
        if (isReviewFlowDone) {
          for {
            acceptedVersion <- FDBRecordDatabase.transact(LPSubmissionOperations.Production) { lpSubmissionOps =>
              lpSubmissionOps.acceptReview(
                lpId,
                actor,
                params.reviewType
              )
            }
            // Recalculate signature status after approving version if signature is blocked by review
            _ <- ZIOUtils.when(ScalaUtils.isMatch[SignatureBlockByReview](acceptedVersion.signatureStatus)) {
              for {
                isEnabledUnsignedReview <- checkIsEnabledUnsignedReview(lpId)
                _ <- fundSubSubscriptionQueryService.calculateAndUpdateSignatureStatusInternal(
                  acceptedVersion,
                  isEnabledUnsignedReview,
                  actor
                )
              } yield ()
            }
            _ <- FundSubLpFlowStatus.updateLpFlowStatus(
              lpId,
              lpStatus = params.reviewType match {
                case FundSubSubscriptionDocReviewType.UnsignedSubscription => LpStatus.LPFormReviewed
                case FundSubSubscriptionDocReviewType.SignedSubscription   => LpStatus.LPSubmitted
              }
            )(natsNotificationService)
            lpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production)(
              _.getFundSubLpModel(lpId)
            )
            emailIds <-
              if (params.reviewType == FundSubSubscriptionDocReviewType.UnsignedSubscription) {
                val receivers = lpModel.mainLp +: lpModel.collaborators
                val refinedReceivers = params.selectedUsersToSendEmail.fold(receivers) { usersToSendEmail =>
                  receivers.filter(usersToSendEmail.contains)
                }
                ZIO
                  .foreach(refinedReceivers)(
                    fundSubEmailService.sendApproveSoftReviewEmail(
                      lpId,
                      actor,
                      _,
                      params.emailTemplateMessageOpt
                    )
                  )
                  .map(_.flatten.flatMap(_.internalIdOpt))
              } else {
                ZIO.attempt(Seq.empty)
              }
            _ <- logEventWhenApproveSubscriptionVersion(
              lpId,
              actor,
              params.reviewType,
              emailIds,
              ctx
            )
          } yield ()
        } else {
          logAndNotifyNextStepReviewers(
            params.fundSubLpId,
            params.reviewType,
            nextReviewers,
            actor
          )
        }
    } yield isReviewFlowDone
  }

  private def checkIsEnabledUnsignedReview(lpId: FundSubLpId) = {
    fundSubSubscriptionDocReviewService
      .checkIfUnsignedReviewEnabled(lpId)
  }

  private def approveSubscriptionVersionReviewFlow(
    lpId: FundSubLpId,
    reviewType: FundSubSubscriptionDocReviewType,
    comment: String,
    actor: UserId
  ): Task[(Boolean, Seq[UserId])] = {
    for {
      latestVersionIndex <- FDBRecordDatabase.transact(LPSubmissionOperations.Production)(
        _.getLastVersionIndex(lpId)
      )
      reviewFlow <- fundSubSubscriptionDocReviewService.approveReviewFlowStep(
        lpId,
        latestVersionIndex,
        reviewType,
        comment,
        actor
      )
      nextReviewers = reviewFlow.currentStepOpt.map(_.reviewers).getOrElse(Seq.empty)
    } yield (reviewFlow.status != ReviewFlowStatus.Pending, nextReviewers.map(_.userId))
  }

  private def logAndNotifyNextStepReviewers(
    lpId: FundSubLpId,
    reviewType: FundSubSubscriptionDocReviewType,
    nextReviewers: Seq[UserId],
    actor: UserId
  ) = {
    for {
      (fsModel, lpModel) <- FDBRecordDatabase.transact(
        FDBOperations[(FundSubModelStoreOperations, FundSubLpModelStoreOperations)].Production
      ) { case (fsOps, lpOps) =>
        for {
          fsModel <- fsOps.getFundSubPublicModel(lpId.parent)
          lpModel <- lpOps.getFundSubLpModel(lpId)
        } yield fsModel -> lpModel
      }

      emailIdOpt <- reviewType match {
        case FundSubSubscriptionDocReviewType.UnsignedSubscription =>
          fundSubEmailService
            .sendUnsignedDocumentReadyForReviewEmail(
              fsModel.investorEntity,
              lpId,
              lpModel.mainLp,
              actor,
              nextReviewers
            )
            .map(_.flatMap(_.internalIdOpt))
        case FundSubSubscriptionDocReviewType.SignedSubscription =>
          fundSubEmailService
            .sendDocumentReadyForReviewEmail(
              fsModel.investorEntity,
              lpId,
              actor,
              nextReviewers
            )
            .map(_.flatMap(_.internalIdOpt))
      }
      _ <- fundSubAuditLogService.addEvent(
        lpId.parent,
        AddEventParam(
          actor = Some(actor),
          actorType = AuditLogActorType.FundSide,
          eventType = reviewType match {
            case FundSubSubscriptionDocReviewType.UnsignedSubscription =>
              AuditLogEventType.UNSIGNED_SUBSCRIPTION_DOCUMENT_REVIEWED
            case FundSubSubscriptionDocReviewType.SignedSubscription =>
              AuditLogEventType.SIGNED_SUBSCRIPTION_DOCUMENT_REVIEWED
          },
          orderId = Some(lpId),
          eventEmail = Seq(
            AddEventEmailParam(
              fundSubEventType = reviewType match {
                case FundSubSubscriptionDocReviewType.UnsignedSubscription =>
                  FundSubEvent.unsignedDocumentReadyForReview
                case FundSubSubscriptionDocReviewType.SignedSubscription => FundSubEvent.documentReadyForReview
              },
              emailIds = emailIdOpt
            )
          ),
          activityDetail = reviewType match {
            case FundSubSubscriptionDocReviewType.UnsignedSubscription =>
              UnsignedSubscriptionDocReviewed(lpModel.firmName)
            case FundSubSubscriptionDocReviewType.SignedSubscription =>
              SignedSubscriptionDocReviewed(lpModel.firmName)
          }
        )
      )
    } yield ()
  }

  private def logEventWhenApproveSubscriptionVersion(
    lpId: FundSubLpId,
    actor: UserId,
    reviewType: FundSubSubscriptionDocReviewType,
    emailIds: Seq[InternalEmailId],
    ctx: Option[AuthenticatedRequestContext]
  ) = {
    for {
      (fsModel, lpModel) <- FDBRecordDatabase.transact(
        FDBOperations[(FundSubModelStoreOperations, FundSubLpModelStoreOperations)].Production
      ) { case (fsOps, lpOps) =>
        for {
          fsModel <- fsOps.getFundSubPublicModel(lpId.parent)
          lpModel <- lpOps.getFundSubLpModel(lpId)
        } yield fsModel -> lpModel
      }
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = lpId.parent,
        params = AddEventParam(
          actor = Some(actor),
          actorType = AuditLogActorType.FundSide,
          eventType = reviewType match {
            case FundSubSubscriptionDocReviewType.UnsignedSubscription =>
              AuditLogEventType.UNSIGNED_SUBSCRIPTION_DOCUMENT_MARKED_AS_APPROVED
            case FundSubSubscriptionDocReviewType.SignedSubscription =>
              AuditLogEventType.SIGNED_SUBSCRIPTION_DOCUMENT_MARKED_AS_APPROVED
          },
          orderId = Option(lpId),
          eventEmail = Seq(Option.when(emailIds.nonEmpty) {
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.approveSoftReview,
              emailIds = emailIds
            )
          }).flatten,
          activityDetail = reviewType match {
            case FundSubSubscriptionDocReviewType.UnsignedSubscription =>
              UnsignedSubscriptionMarkedAsApproved(lpModel.firmName)
            case FundSubSubscriptionDocReviewType.SignedSubscription =>
              SignedSubscriptionMarkedAsApproved(lpModel.firmName)
          }
        )
      )
      _ <- fundSubLpActivityLogService.logActivity(
        lpId = lpId,
        actorOpt = Option(actor),
        detail = reviewType match {
          case FundSubSubscriptionDocReviewType.UnsignedSubscription =>
            UnsignedSubscriptionMarkedAsApproved(lpModel.firmName)
          case FundSubSubscriptionDocReviewType.SignedSubscription =>
            SignedSubscriptionMarkedAsApproved(lpModel.firmName)
        }
      )
      _ <- FundSubAdminActivityLogUtils.addActivity(
        fundSubId = lpId.parent,
        actor = actor,
        fundAdminActivity = reviewType match {
          case FundSubSubscriptionDocReviewType.UnsignedSubscription =>
            UnsignedSubscriptionDocumentMarkedAsApproved(
              investorUserId = lpModel.mainLp,
              lpId = lpId
            )
          case FundSubSubscriptionDocReviewType.SignedSubscription =>
            SignedSubscriptionDocumentMarkedAsApproved(
              investorUserId = lpModel.mainLp,
              lpId = lpId
            )
        }
      )
      _ <- fsLoggingService.logEventApproveRequestReview(
        actor,
        lpId,
        fsModel.fundName,
        isSoftReview = reviewType == FundSubSubscriptionDocReviewType.UnsignedSubscription,
        ctx
      )
    } yield ()
  }

  def requestChangeSubscriptionVersion(
    params: RequestChangeSubscriptionVersionParams,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext] = None
  ): Task[Unit] = {
    val lpId = params.fundSubLpId
    val refillFormRequired = params.requestChangeType == SubscriptionRequestChangeType.ChangeSubscriptionForm
    for {
      _ <- ZIO.logInfo(s"User $actor request change subscription version $lpId")
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = lpId.parent,
        userId = actor,
        permission = InvestorPermission.RequestLpChangeSubscription,
        obj = Type.Investor(lpId)
      )
      lpStatus <- fundSubPermissionService.validateLpStatus(
        lpId,
        Set(
          LpStatus.LPPendingUnsignedReview,
          LpStatus.LPPendingReview,
          LpStatus.LPSubmitted,
          LpStatus.LPCountersigned,
          LpStatus.LPCompleted
        )
      )
      latestVersion <- FDBRecordDatabase.transact(LPSubmissionOperations.Production)(
        _.getLastVersion(lpId)
      )
      _ <- ZIOUtils.when(params.requestChangeType == SubscriptionRequestChangeType.ChangeSignedDoc)(
        ZIOUtils.validate(latestVersion.packageSubmitStatus.nonEmpty)(
          GeneralServiceException("Request change signed doc since it's not submitted")
        )
      )
      _ <- fundSubSubscriptionCountersignService.removePendingAndDistributedCounterSignedDocs(lpId)
      _ <- fundSubSubscriptionCountersignService.cancelAndRemoveCounterSignRequest(lpId, actor)
      _ <- fundSubSubdocDataExtractService.discardExistingRequestOfLp(
        lpId = lpId,
        reason = FundSubDataExtractRequestDiscardReason.ByChangeRequest,
        shouldSendUpdateToDatalake = true
      )
      lpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops.updateFundSubLpModel(lpId)(
          _.copy(lastRequestChangeVersionId = latestVersion.versionIndex)
        )
      }
      requestChangeEmailGenerate <- prepareRequestLpChangeEmailGenerate(
        lpId,
        latestVersion.lpFormId,
        lpModel.mainLp,
        lpModel.collaborators,
        params.messageOpt.filter(_ => params.sendNotificationEmail),
        params.attachedComments,
        actor,
        params.ccRecipients
      )
      populatedMessageOpt <- requestChangeEmailGenerate.getUserVariableMappings.map { userVariableMappings =>
        requestChangeEmailGenerate.customBodyOpt.map(
          FundSubEmailTemplate.populateUserVariableMapping(_, userVariableMappings)
        )
      }
      _ <- fundSubSubscriptionDocReviewService.requestChangeReviewFlowStep(
        lpId,
        latestVersion.versionIndex,
        reviewType = lpStatus match {
          case LpStatus.LPPendingUnsignedReview => FundSubSubscriptionDocReviewType.UnsignedSubscription
          case LpStatus.LPPendingReview         => FundSubSubscriptionDocReviewType.SignedSubscription
          case _                                => FundSubSubscriptionDocReviewType.SignedSubscription
        },
        comment = populatedMessageOpt.getOrElse(""),
        actor
      )
      _ <- FDBRecordDatabase.transact(LPSubmissionOperations.Production)(
        _.requestChange(
          lpId,
          actor,
          requestChangeType = params.requestChangeType match {
            case SubscriptionRequestChangeType.ChangeSubscriptionForm => ChangeSubscriptionForm
            case SubscriptionRequestChangeType.ChangeSignedDoc        => ChangeSignedDoc
          },
          populatedMessageOpt,
          params.attachedComments
        )
      )
      _ <- FundSubLpFlowStatus.updateLpFlowStatus(
        lpId,
        LpStatus.LPChangeInProgress
      )(natsNotificationService)
      emailId <-
        if (params.sendNotificationEmail) {
          fundSubEmailService.sendEmail(params.fundSubLpId.parent, requestChangeEmailGenerate)
        } else {
          ZIO.attempt(None)
        }
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = lpId.parent,
        params = AddEventParam(
          actor = Some(actor),
          actorType = AuditLogActorType.FundSide,
          eventType = AuditLogEventType.CHANGE_REQUEST_SENT,
          orderId = Option(lpId),
          eventEmail = Seq(
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.requestLpChange,
              emailIds = Seq(emailId).flatMap(_.flatMap(_.internalIdOpt))
            )
          ),
          activityDetail = RequestChange(
            refillFormRequired = refillFormRequired,
            investorUserId = lpModel.mainLp,
            lpId = lpId
          )
        )
      )
      _ <- fundSubLpActivityLogService.logActivity(
        lpId = lpId,
        actorOpt = Option(actor),
        at = None,
        detail = LpRequestedToChange(
          refillFormRequired = refillFormRequired
        )
      )
      _ <- FundSubAdminActivityLogUtils.addActivity(
        fundSubId = lpId.parent,
        actor = actor,
        RequestChange(
          investorUserId = lpModel.mainLp,
          lpId = lpId,
          refillFormRequired = refillFormRequired
        )
      )
      fsModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(_.getFundSubPublicModel(lpId.parent))
      _ <- fsLoggingService.logEventRequestChangeRequestReview(
        actor,
        lpId,
        fsModel.fundName,
        isSoftReview = latestVersion.packageSubmitStatus.isEmpty,
        params.requestChangeType,
        ctx
      )
    } yield ()
  }

  def getSubscriptionDocRequestChangesInfo(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[GetSubscriptionDocRequestChangesInfoResponse] = {
    for {
      _ <- ZIO.logInfo(s"User $actor get request changes info $lpId")
      _ <- fundSubPermissionService.validateFundManagerCanAccessLpSubscriptionDocs(lpId, actor)
      (fsModel, lpModel) <- FDBRecordDatabase.transact(
        FDBOperations[(FundSubModelStoreOperations, FundSubLpModelStoreOperations)].Production
      ) { case (fsOps, lpOps) =>
        for {
          fsModel <- fsOps.getFundSubPublicModel(lpId.parent)
          lpModel <- lpOps.getFundSubLpModel(lpId)
        } yield fsModel -> lpModel
      }
      requestChangeOpt <- FundSubSubscriptionDocUtils.getLastRequestChangeOpt(
        lpModel,
        fsModel.lpFlowType
      )
      participantInfoMap <- userProfileService
        .batchGetUserInfos(requestChangeOpt.map(_.requestBy).toSet)
        .map(_.map { case (userId, userInfo) =>
          userId -> ParticipantInfo.convert(userId, userInfo)
        })
    } yield GetSubscriptionDocRequestChangesInfoResponse(requestChangeOpt, participantInfoMap)
  }

  def calculateAndUpdateSignatureStatus(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor calculates and update signature status for $lpId")
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      isEnabledUnsignedReview <- checkIsEnabledUnsignedReview(lpId)
      latestVersion <- FDBRecordDatabase.transact(LPSubmissionOperations.Production)(
        _.getLastVersion(lpId)
      )
      fundSubModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.getFundSubPublicModel(lpId.parent)
      )
      shouldCalculateSignatureStatus =
        FundSubSignatureDataUtils.shouldCalculateSignatureStatus(
          signatureStatus = FundSubSubscriptionFetchingUtils.convertSignatureStatus(latestVersion.signatureStatus),
          isEnabledUnsignedReview = isEnabledUnsignedReview,
          enableEnforceLpSubmitReviewBeforeSignature =
            fundSubModel.featureSwitch.exists(_.enableEnforceLpSubmitReviewBeforeSignature)
        )
      _ <- ZIO.when(shouldCalculateSignatureStatus) {
        fundSubSubscriptionQueryService.calculateAndUpdateSignatureStatusInternal(
          latestVersion,
          isEnabledUnsignedReview,
          actor
        )
      }
    } yield ()
  }

  def checkIfNeedToSignAgainAfterEditForm(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[Boolean] = {
    for {
      _ <- ZIO.logInfo(s"$actor calculates signature status for $lpId")
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      latestVersion <- FDBRecordDatabase.transact(LPSubmissionOperations.Production)(_.getLastVersion(lpId))
      isEnabledUnsignedReview <- checkIsEnabledUnsignedReview(lpId)
      signatureStatus <- fundSubSubscriptionQueryService.calculateSignatureStatus(
        latestVersion,
        isEnabledUnsignedReview,
        actor
      )
      isRequired <- ZIO.attempt(signatureStatus match {
        case _: SignatureRequired => true
        case _                    => false
      })
      _ <- ZIOUtils.when(isRequired) {
        fundSubAuditLogService.addEvent(
          fundSubId = lpId.parent,
          params = AddEventParam(
            actor = Some(actor),
            orderId = Option(lpId),
            actorType = AuditLogActorType.InvestorSide,
            eventType = AuditLogEventType.SUBSCRIPTION_DOCUMENT_EDITED,
            activityDetail = FormEdited()
          )
        )
      }
    } yield isRequired
  }

  def createSubscriptionDocSignatureRequest(
    params: CreateSubscriptionDocSignatureRequestParams,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext]
  ): Task[CreateSubscriptionDocSignatureRequestResp] = {
    val lpId = params.lpId
    for {
      _ <- ZIO.logInfo(s"$actor is creating a new subscription doc signature request for $lpId")
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      _ <- fundSubPermissionService.validateLpStatus(
        lpId,
        Set(
          LpStatus.LPNotStarted,
          LpStatus.LPInProgress,
          LpStatus.LPFormReviewed,
          LpStatus.LPChangeInProgress,
          LpStatus.LPPendingUnsignedReview,
          LpStatus.LPPendingSubmission
        )
      )
      _ <- fundSubPermissionService.validateFundActive(lpId.parent)
      _ <- ZIOUtils.failWhen(params.fileIds.isEmpty || params.signers.isEmpty) {
        GeneralServiceException("No files/signers found")
      }
      _ <- ZIO.logInfo(s"File IDs to sign: ${params.fileIds.map(_.idString).mkString(", ")}")
      _ <- ZIO.logInfo(s"Fields to sign: ${params.signers.map(_.blocks.map(_._2.fields.size).sum)}")
      (
        fundSubModel: FundSubPublicModel,
        lpModel: FundSubLpModel
      ) <- FDBRecordDatabase.transact(
        FDBOperations[(FundSubModelStoreOperations, FundSubLpModelStoreOperations)].Production
      ) { case (fsOps, lpOps) =>
        for {
          fsModel <- fsOps.getFundSubPublicModel(lpId.parent)
          lpModel <- lpOps.getFundSubLpModel(lpId)
        } yield (fsModel, lpModel)
      }
      version <- getSubscriptionVersionBasicInfo(
        GetSubscriptionVersionBasicInfoParams(lpId, SubscriptionVersionIndex.Latest),
        actor
      )
      formFileIds = version.versionInfoOpt.map(_.formFiles.values.toSet).getOrElse(Set.empty)
      _ <- ZIOUtils.failWhen(params.fileIds.toSet != formFileIds) {
        GeneralServiceException("Signature files do not match form files")
      }
      userIdMap <- fundSubUserService.createSignatureUserIfNeeded(
        fundSubId = lpId.parent,
        signers = params.signers.map(signer => signer.email -> signer.name),
        inviterOpt = httpContext.map(_.actor.userInfo.emailAddressStr)
      )
      signers <- ZIO.foreach(params.signers) { signer =>
        for {
          userId <- ZIOUtils.optionToTask(
            userIdMap.get(signer.email),
            GeneralServiceException(s"No userId for ${signer.email}")
          )
        } yield {
          FundSubSignerParams(
            userId = userId,
            blocks = signer.blocks,
            signatures = signer.signatures,
            authTypeData = signer.authTypeData,
            docusignRecipientAuthDataOpt = signer.docusignRecipientAuthDataOpt,
            canAccessSignatureRequiredDocOnly = signer.canAccessSignatureRequiredDocOnly
          )
        }
      }
      latestVersion <- prepareLatestVersionBeforeSignature(
        lpId,
        actor,
        allSigners = signers.map(_.userId)
      )
      subscriptionDocAuthorizedTeamIds <- FundSubInvestorGroupUtils
        .getAllInvestorGroupsOfLp(lpId)
        .map(_.map(_.subscriptionDocAuthorizedTeamId))
      signatureProviderParams <- FundSubSignatureJvmUtils.resolveSignatureProviderParams(
        fundSubModel,
        backendConfig.docusignIntegrationConfig,
        Some(FundSubSignatureRequestDocType.SubscriptionDoc),
        params.docusignESignatureOptionParamsOpt,
        actor
      )(
        using userProfileService
      )
      namesOfFilesToSign <- fileService.batchGetFileNamesUnsafe(params.fileIds)
      isLpSigningWithSchwabFlow = signatureProviderParams match {
        case _: SignatureProviderParams.Docusign =>
          SchwabTemplateUtils.shouldSignWithSchwabFlow(namesOfFilesToSign.values.toSeq)
        case _: SignatureProviderParams.AnduinSign => false
      }
      requestId <- fundSubSignatureIntegrationService.createSingleDocTypeSignatureRequest(
        singleDocTypeParams = SingleDocTypeEnvelope(
          lpId = lpId,
          docType = FundSubSignatureRequestModels.DocType.SubscriptionDoc,
          subscriptionVersionIndexOpt = Some(latestVersion.versionIndex),
          isSchwabTemplate = isLpSigningWithSchwabFlow
        ),
        fileIds = params.fileIds,
        signers = signers,
        message = params.message,
        teamIds = subscriptionDocAuthorizedTeamIds.toSet ++ lpModel.teamId,
        actor = actor,
        eSignatureProviderParams = signatureProviderParams,
        refDocs = params.refDocs,
        httpContext = httpContext,
        fundSubSignatureDateFormatOpt = fundSubModel.signatureConfig.flatMap(_.dateFormat)
      )
      request <- fundSubSignatureIntegrationService.getFundSubSignatureRequestBasicUnsafe(requestId)
      // If actor has signed the request, we add lp activity log
      _ <- {
        val actorSignedRequest = request.signers.exists { recipient =>
          recipient.userId == actor && recipient.doneSigning
        }
        ZIOUtils.when(request.eSignatureProvider == ESignatureProvider.AnduinSign && actorSignedRequest)(
          addLoggingAfterRecipientSigned(
            lpId,
            actor,
            requestId,
            httpContext
          )
        )
      }
      _ <-
        if (request.doneSigning) {
          updateSignedDocumentAndAttemptToSubmitPackage(
            lpId,
            request,
            LpSignatureType.ESignature,
            versionIndex = latestVersion.versionIndex,
            httpContext
          )
        } else {
          requestOthersSignaturesForSingleDocTypeEnvelope(
            lpId,
            actor,
            request,
            userIdMap,
            params.message,
            httpContext
          )
        }
      _ <- ZIO.logInfo(s"New subscription doc signature request $requestId created for $lpId")
    } yield CreateSubscriptionDocSignatureRequestResp(requestId)
  }

  def eSignSubscriptionDoc(
    params: ESignSubscriptionDocParams,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    val lpId = params.lpId
    for {
      _ <- ZIO.logInfo(s"$actor is e-signing subscription doc request ${params.requestId} of $lpId")
      _ <- fundSubPermissionService.validateLpStatus(
        lpId,
        Set(LpStatus.LPRequestedSignature)
      )
      _ <- fundSubPermissionService.validateFundActive(lpId.parent)
      _ <- ZIOUtils.failWhen(params.signingData.isEmpty)(GeneralServiceException("Signed data should be non empty"))

      fundSubModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.getFundSubPublicModel(lpId.parent)
      )
      lastVersionIndex <- FDBRecordDatabase.transact(LPSubmissionOperations.Production) {
        _.getLastVersionIndex(lpId)
      }
      _ <- fundSubSignatureIntegrationService.eSignSignatureRequest(
        lpId = lpId,
        requestId = params.requestId,
        signatures = params.signingData.toMap,
        actor = actor,
        httpContext = httpContext,
        signingAuthData = params.signingAuthData,
        enabledElectronicSeal = fundSubModel.signatureConfig.exists(_.anduinSignSignatureConfig.enabledElectronicSeal)
      )
      signedRequest <- fundSubSignatureIntegrationService.getFundSubSignatureRequestBasicUnsafe(params.requestId)
      _ <- addLoggingAfterRecipientSigned(
        lpId,
        actor,
        signedRequest.requestId,
        httpContext
      )
      _ <- ZIO.when(params.forQaTesting) {
        ZIO.sleep(zio.Duration.fromSeconds(30))
      }
      _ <-
        if (signedRequest.doneSigning) {
          for {
            _ <- sendEmailAndAddLoggingAfterSubDocSignatureRequestCompleted(
              lpId,
              fundSubModel,
              signedRequest,
              actor,
              httpContext,
              lastVersionIndex
            )
            _ <- updateSignedDocumentAndAttemptToSubmitPackage(
              lpId,
              signedRequest,
              LpSignatureType.ESignature,
              versionIndex = lastVersionIndex,
              httpContext
            )
          } yield ()
        } else {
          // Trigger dashboard refresh to load new signing information
          lpDashboardService.updateLpInfoRecord(lpId, identity)
        }

    } yield ()
  }

  def cancelSubscriptionDocSignatureRequest(
    requestId: SignatureRequestId,
    lpId: FundSubLpId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"$actor is canceling subscription doc request $requestId of $lpId")
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      _ <- fundSubPermissionService.validateLpStatus(
        lpId,
        Set(LpStatus.LPRequestedSignature)
      )
      _ <- cancelLatestVersionSignatureRequest(
        lpId,
        actor,
        requestIdOpt = Some(requestId)
      )
      isVersionReviewed <- getSubscriptionVersionBasicInfo(
        GetSubscriptionVersionBasicInfoParams(lpId = lpId, versionIndex = SubscriptionVersionIndex.Latest),
        actor
      ).map(_.versionInfoOpt.fold(false)(_.reviewStatus match {
        case SubscriptionVersionReviewStatus.Approved(_, _, _) => true
        case _                                                 => false
      }))
      _ <- FundSubLpFlowStatus.updateLpFlowStatus(
        lpId,
        if (isVersionReviewed) {
          LpStatus.LPFormReviewed
        } else {
          LpStatus.LPInProgress
        }
      )(natsNotificationService)
    } yield ()
  }

  def reassignSubscriptionDocSignatureRequest(
    lpId: FundSubLpId,
    requestId: SignatureRequestId,
    oldSigner: UserId,
    newSignerName: String,
    newSignerEmail: String,
    reason: String,
    signerAuthTypeData: SignerAuthTypeData,
    docusignRecipientAuthDataOpt: Option[DocusignRecipientAuthData],
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(
        s"$actor is reassigning subscription doc request $requestId to $newSignerEmail for $lpId"
      )
      _ <- ZIO.when(oldSigner != actor) {
        for {
          given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
          _ <- fundSubPermissionService
            .validateFundManagerCanAccessLpSubscriptionDocsR(lpId, actor)
            .orElse(fundSubPermissionService.validateUserCanServeOnInvestorSideR(lpId, actor))
        } yield ()
      }
      _ <- fundSubPermissionService.validateLpStatus(
        lpId,
        Set(LpStatus.LPRequestedSignature)
      )
      _ <- fundSubPermissionService.validateFundActive(lpId.parent)
      newSigners <- fundSubUserService.createSignatureUserIfNeeded(
        fundSubId = lpId.parent,
        signers = Seq(newSignerEmail -> newSignerName),
        inviterOpt = None
      )
      newSigner <- ZIOUtils.optionToTask(newSigners.headOption.map(_._2), GeneralServiceException("No user"))
      _ <- fundSubSignatureIntegrationService.reassignSignatureRequest(
        fundSubId = lpId.parent,
        requestId = requestId,
        lpIds = Seq(lpId),
        oldSigner = oldSigner,
        newSigner = newSigner,
        signerAuthTypeData = signerAuthTypeData,
        actor = actor,
        docusignRecipientAuthDataOpt = docusignRecipientAuthDataOpt
      )
      request <- fundSubSignatureIntegrationService.getFundSubSignatureRequestBasicUnsafe(requestId)
      lastVersionIndex <- FDBRecordDatabase.transact(LPSubmissionOperations.Production)(_.getLastVersionIndex(lpId))
      signatureFileIds = request.signers
        .find(_.userId == newSigner)
        .map(_.applicableSignatureFileIds)
        .getOrElse {
          request
            .getSubdocPackage(lpId, lastVersionIndex)
            .map(_.getUnsignedSignatureFileIds)
            .getOrElse(Seq.empty)
        }
      newSignerEmailIdOpt <- fundSubEmailService
        .sendFundSubReassignNewSignerEmail(
          fundSubLpId = lpId,
          requestId = request.requestId,
          reason = reason,
          signer = newSigner,
          actor = actor,
          fileIds = signatureFileIds,
          requester = request.requester,
          envelopeType = request.envelopeType
        )
        .map(_.flatMap(_.internalIdOpt))
      requesterEmailIdOpt <-
        if (actor != request.requester) {
          fundSubEmailService
            .sendFundSubReassignRequesterEmail(
              lpId,
              signatureFileIds,
              request.requester,
              newSignerName,
              newSignerEmail,
              reason,
              actor
            )
            .map(_.flatMap(_.internalIdOpt))
        } else {
          ZIO.attempt(None)
        }
      oldSignerEmailIdOpt <-
        if (actor != oldSigner) {
          fundSubEmailService
            .sendCancelSignatureRequestEmail(
              lpId = lpId,
              fileIds = signatureFileIds,
              receiver = oldSigner,
              actor = actor,
              envelopeType = request.envelopeType,
              requestId = requestId
            )
            .map(_.flatMap(_.internalIdOpt))
        } else {
          ZIO.attempt(None)
        }
      isAuthorizedFundManager <- fundSubPermissionService.checkIfUserCanManageInvestor(lpId, actor)
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = lpId.parent,
        params = AddEventParam(
          actor = Some(actor),
          actorType = if (isAuthorizedFundManager) AuditLogActorType.FundSide else AuditLogActorType.InvestorSide,
          eventType = AuditLogEventType.SIGNATURE_REQUEST_REASSIGNED,
          orderId = Option(lpId),
          eventEmail = Seq(
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.lpRequestReassignSigner,
              emailIds = newSignerEmailIdOpt.toSeq
            )
          ) ++ requesterEmailIdOpt.map { emailId =>
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.lpRequestReassignRequester,
              emailIds = Seq(emailId)
            )
          } ++ oldSignerEmailIdOpt.map { emailId =>
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.lpCancelSignatureRequest,
              emailIds = Seq(emailId)
            )
          },
          activityDetail = SignatureRequestReassign(oldSigner, newSigner)
        )
      )
    } yield ()
  }

  def remindSubscriptionSignatureRequest(
    params: RemindSubscriptionDocSignatureRequestParams,
    actor: UserId
  ): Task[Unit] = {
    val lpId = params.lpId
    for {
      _ <- ZIO.logInfo(s"$actor is reminding ${params.signer} in subscription doc request ${params.requestId} for $lpId")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
      _ <- fundSubPermissionService
        .validateFundManagerCanAccessLpSubscriptionDocsR(lpId, actor)
        .orElse(fundSubPermissionService.validateUserCanServeOnInvestorSideR(lpId, actor))
      _ <- fundSubPermissionService.validateLpStatus(
        lpId,
        Set(LpStatus.LPRequestedSignature)
      )
      _ <- fundSubPermissionService.validateFundActive(lpId.parent)
      request <- fundSubSignatureIntegrationService.getFundSubSignatureRequestBasicUnsafe(params.requestId)
      pendingSigners = request.signers.filterNot(_.doneSigning)
      _ <- ZIOUtils.failUnless(pendingSigners.exists(_.userId == params.signer)) {
        GeneralServiceException(s"${params.signer} is not a pending signer")
      }
      emailIdOpt <- fundSubEmailService
        .sendFundSubRemindSignatureRequestEmail(
          lpId,
          params.signer,
          actor,
          request.envelopeType,
          params.requestId
        )
        .map(_.flatMap(_.internalIdOpt))
      _ <- fundSubSignatureIntegrationService.resendEnvelopeForDocusignRecipientIfNeeded(
        params.requestId,
        params.signer,
        actor
      )
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = lpId.parent,
        params = AddEventParam(
          actor = Some(actor),
          actorType = AuditLogActorType.InvestorSide,
          eventType = AuditLogEventType.REMINDER_TO_COMPLETE_SIGNATURE_REQUEST_ON_SUBSCRIPTION_DOCUMENT_SENT,
          orderId = Option(lpId),
          eventEmail = Seq(
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.lpRemindSignatureRequest,
              emailIds = Seq(emailIdOpt).flatten
            )
          ),
          activityDetail = GeneralActivity(Value.Empty)
        )
      )
    } yield ()
  }

  def uploadSignedSubscriptionDoc(
    params: UploadSignedSubscriptionDocParams,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    val lpId = params.fundSubLpId
    val uploadedDocs = params.uploadedDocs
    for {
      _ <- ZIO.logInfo(s"$actor upload signed subscription doc for $lpId")
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      _ <- fundSubPermissionService.validateLpStatus(
        lpId,
        Set(
          LpStatus.LPNotStarted,
          LpStatus.LPInProgress,
          LpStatus.LPChangeInProgress,
          LpStatus.LPPendingUnsignedReview,
          LpStatus.LPPendingSubmission,
          LpStatus.LPFormReviewed
        )
      )
      _ <- fundSubPermissionService.validateFundActive(lpId.parent)
      _ <- ZIOUtils.validate(uploadedDocs.nonEmpty)(
        GeneralServiceException("Uploaded doc should be non empty")
      )
      _ <- prepareLatestVersionBeforeSignature(
        lpId,
        actor,
        allSigners = Seq.empty
      )
      (versionIndex, sharedFiles) <- uploadSignedDocumentAndAttemptToSubmitPackageInternal(
        lpId,
        uploadedDocs.map(LpSignedDocument(_)),
        Seq(actor),
        LpSignatureType.UploadSigned,
        actor,
        httpContext
      )
      _ <- handleDataExtractRequestForNewUpload(
        lpId = lpId,
        fileIds = sharedFiles.map(_.fileId),
        versionIndexOpt = Some(versionIndex),
        valueOptToBypassLiveOcrCheck = None,
        actor = actor
      )
      _ <- fundSubLpActivityLogService.logActivity(
        lpId = lpId,
        actorOpt = Option(actor),
        at = Option(DateCalculator.instantNow),
        detail = SignedSubscriptionDocument(signedByUploadingSignedDoc = true)
      )
      _ <- fsLoggingService.logEventSignForm(
        actor,
        lpId,
        isUpload = true,
        httpContext
      )
    } yield ()
  }

  def gpUploadSignedSubscriptionDoc(
    params: GpUploadSignedSubscriptionDocParams,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext]
  ): Task[Seq[LpSignedDocument]] = {
    val lpId = params.fundSubLpId
    val uploadedDocs = params.uploadedDocs
    for {
      _ <- ZIO.logInfo(s"GP $actor upload signed subscription doc for $lpId on behalf")
      _ <- ZIOUtils.validate(uploadedDocs.nonEmpty)(
        GeneralServiceException("Uploaded doc should be non empty")
      )
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = lpId.parent,
        userId = actor,
        permission = InvestorPermission.UploadSubscriptionDocsOnBehalf,
        obj = Type.Investor(lpId)
      )
      _ <- fundSubPermissionService.validateLpStatus(
        lpId,
        Set(
          LpStatus.LPNotStarted,
          LpStatus.LPInProgress,
          LpStatus.LPChangeInProgress,
          LpStatus.LPPendingUnsignedReview,
          LpStatus.LPRequestedSignature,
          LpStatus.LPPendingSubmission,
          LpStatus.LPPendingReview,
          LpStatus.LPSubmitted,
          LpStatus.LPFormReviewed
        )
      )
      reviewConfigOpt <- fundSubSubscriptionDocReviewService.getActiveReviewConfigForInvestorInternal(
        lpId = lpId,
        reviewType = FundSubSubscriptionDocReviewType.SignedSubscription
      )
      shouldSubmitForReview <- reviewConfigOpt.fold {
        if (params.shouldApprove) {
          ZIO.fail(GeneralServiceException("Signed subscription review is disabled"))
        } else {
          ZIO.succeed(false)
        }
      } { reviewConfig =>
        val firstStepReviewers = reviewConfig.steps.headOption.map(_.reviewers).getOrElse(Seq.empty)
        if (firstStepReviewers.exists(_.userId == actor)) {
          ZIO.succeed(!params.shouldApprove)
        } else if (params.shouldApprove) {
          ZIO.fail(
            GeneralServiceException(s"${actor.id} is not a reviewer of the current step thus unauthorized to approve")
          )
        } else {
          ZIO.succeed(true)
        }
      }
      (versionIndex, signedDocs) <- gpUploadSignedSubscriptionDocUnsafe(
        lpId = lpId,
        uploadedDocs = uploadedDocs,
        actor = actor,
        httpContext = httpContext,
        shouldSubmitForReview = shouldSubmitForReview,
        shouldSubmitWithoutRecord = false
      )
      _ <- ZIO.foreachDiscard(params.submittedAmounts) { case (investmentFundId, commitment) =>
        ZIO.when(commitment.exists(_.value.trim.nonEmpty)) {
          FundSubCommitmentHelper.fundManagerUpdateLpCommitment(
            lpId = lpId,
            investmentFundIdOpt = Some(investmentFundId),
            submittedCommitment = Some(commitment)
          )
        }
      }
      _ <- handleDataExtractRequestForNewUpload(
        lpId = lpId,
        fileIds = signedDocs.map(_.fileId),
        versionIndexOpt = Some(versionIndex),
        valueOptToBypassLiveOcrCheck = Some(params.shouldCreateNewDataExtractRequest),
        actor = actor
      )
    } yield signedDocs
  }

  private def gpUploadSignedSubscriptionDocUnsafe(
    lpId: FundSubLpId,
    uploadedDocs: Seq[FileId],
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext],
    shouldSubmitWithoutRecord: Boolean,
    shouldSubmitForReview: Boolean
  ): Task[(versionIndex: Int, signedDocs: Seq[LpSignedDocument])] = {
    for {
      _ <- cancelLatestVersionSignatureRequest(lpId, actor)
      _ <- fundSubSubscriptionCountersignService.cancelAndRemoveCounterSignRequest(lpId, actor)
      _ <- createNewOrFirstVersion(lpId, actor)
      _ <- fsLoggingService.logEventSignForm(
        actor = actor,
        fundSubLpId = lpId,
        isUpload = true,
        httpContext = httpContext
      )
      (versionIndex, signedDocs) <- uploadSignedDocumentAndAttemptToSubmitPackageInternal(
        lpId = lpId,
        signedDocs = uploadedDocs.map(LpSignedDocument(_)),
        signatureBy = Seq(actor),
        lpSignatureType = LpSignatureType.UploadSigned,
        actor = actor,
        httpContext = httpContext,
        isSubmittingOnBehalfByFundManager = true,
        shouldSubmitForReview = shouldSubmitForReview,
        shouldSubmitWithoutRecord = shouldSubmitWithoutRecord
      )
    } yield (versionIndex, signedDocs)
  }

  def gpUploadExecutedSubscription(
    lpId: FundSubLpId,
    uploadedDocs: Seq[FileId],
    acceptedAmounts: Map[InvestmentFundId, Option[MoneyMessage]],
    shouldDistributeDoc: Boolean = false,
    shouldCreateNewDataExtractRequest: Boolean = true,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext]
  ): Task[Seq[FundSubFile]] = {
    val fundSubId = lpId.parent
    for {
      _ <- ZIO.logInfo(s"GP $actor upload signed subscription doc for $lpId on behalf")
      _ <- fundSubPermissionService.validateUserHasPermission(
        fundId = fundSubId,
        userId = actor,
        permission = InvestorPermission.UploadSubscriptionDocsOnBehalf,
        obj = Type.Investor(lpId)
      )
      _ <- FundSubSignatureJvmUtils.validateShouldCounterSign(fundSubId)
      _ <- fundSubPermissionService.validateLpStatus(lpId, LpStatusSharedUtils.lpStatusAllowGpUploadExecutedDocs)
      _ <- gpSubmitLatestSubscriptionVersionUnsafe(
        lpId = lpId,
        actor = actor,
        httpContext = httpContext
      )
      sharedCountersignedFiles <-
        if (shouldDistributeDoc) {
          fundSubSubscriptionCountersignService.uploadAndDistributeCounterSignedDocs(
            lpId = lpId,
            actor = actor,
            actorFundRole = anduin.protobuf.fundsub.FundSubAdminRole.FundSubAdmin, // TODO: fix this
            countersignedDocs = uploadedDocs.map(FundSubFile(_, FundSubDocType.CounterSignedDoc)),
            acceptedCommitments = acceptedAmounts,
            httpContext = httpContext
          )
        } else {
          fundSubSubscriptionCountersignService.uploadCounterSignedDocs(
            lpId = lpId,
            counterSignedDocs = uploadedDocs.map(FundSubFile(_, FundSubDocType.CounterSignedDoc)),
            actor = actor,
            adminFundRole = anduin.protobuf.fundsub.FundSubAdminRole.FundSubAdmin, // TODO: fix this
            httpContext = httpContext,
            acceptedCommitments = acceptedAmounts
          )
        }
      _ <- handleDataExtractRequestForNewUpload(
        lpId = lpId,
        fileIds = sharedCountersignedFiles.map(_.fileId),
        versionIndexOpt = None,
        valueOptToBypassLiveOcrCheck = Some(shouldCreateNewDataExtractRequest),
        actor = actor
      )
    } yield sharedCountersignedFiles
  }

  private def handleDataExtractRequestForNewUpload(
    lpId: FundSubLpId,
    fileIds: Seq[FileId],
    versionIndexOpt: Option[Int],
    valueOptToBypassLiveOcrCheck: Option[Boolean],
    actor: UserId
  ) = {
    for {
      toCreateDataExtractRequest <- valueOptToBypassLiveOcrCheck match {
        case Some(false) => ZIO.succeed(false)
        case _ =>
          FundSubDataExtractJvmUtils.checkIfDataExtractRequestCanBeCreatedForLp(
            lpId = lpId,
            fileIds = fileIds,
            shouldBypassLiveOcrCheck = valueOptToBypassLiveOcrCheck.contains(true),
            canOverrideCurrentRequest = true,
            actor = actor
          )
      }
      _ <- fundSubSubdocDataExtractService.discardExistingRequestOfLp(
        lpId = lpId,
        reason = FundSubDataExtractRequestDiscardReason.ByNewUpload,
        shouldSendUpdateToDatalake = !toCreateDataExtractRequest
      )
      _ <- ZIOUtils.when(toCreateDataExtractRequest) {
        fundSubSubdocDataExtractService.createSubscriptionDocDataExtractRequestUnsafe(
          lpId = lpId,
          sharedFiles = fileIds,
          subscriptionVersionIndexOpt = versionIndexOpt,
          actorId = actor
        )
      }
    } yield ()
  }

  def gpSubmitLatestSubscriptionVersionUnsafe(
    lpId: FundSubLpId,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    for {
      _ <- cancelLatestVersionSignatureRequest(lpId, actor)
      _ <- fundSubSubscriptionCountersignService.cancelAndRemoveCounterSignRequest(lpId, actor)
      _ <- createFirstVersionIfNotExisted(lpId, actor)
      _ <- fsLoggingService.logEventSignForm(
        actor = actor,
        fundSubLpId = lpId,
        isUpload = true,
        httpContext = httpContext
      )
      _ <- FDBRecordDatabase.transact(LPSubmissionOperations.Production) { lpSubmissionOps =>
        for {
          latestVersion <- lpSubmissionOps.getLastVersion(lpId)
          _ <- RecordIO.unless(
            ScalaUtils.isMatch[SignatureCompleted](latestVersion.signatureStatus)
              || ScalaUtils.isMatch[SignatureReused](latestVersion.signatureStatus)
          ) {
            lpSubmissionOps.updateSignatureStatus(
              lpId = lpId,
              versionIndex = latestVersion.versionIndex,
              actor = actor,
              signatureStatus = SignatureCompleted(
                signedDocuments = Seq.empty,
                signatureType = LpSignatureType.UploadSigned,
                signatureBy = Seq(actor),
                signatureAt = Some(Instant.now)
              )
            )
          }
        } yield ()
      }
      _ <- fundSubSubscriptionSubmitService.submitSubscriptionPackage(
        lpId = lpId,
        actor = actor,
        httpContext = httpContext,
        isManualSubmit = false,
        isSubmittingOnBehalfByFundManager = true,
        submitForReview = false,
        shouldSubmitWithoutRecord = true,
        shouldAttachIncompleteFormFiles = true
      )
      _ <- triggerAutoSaveProfileFromSubscription(actor, lpId)
    } yield ()
  }

  def cancelCompletedSubscriptionSignature(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"User $actor cancels completed subscription signature for $lpId")
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      latestVersion <- FDBRecordDatabase.transact(LPSubmissionOperations.Production)(_.getLastVersion(lpId))
      _ <- ZIOUtils.failWhen(latestVersion.packageSubmitStatus.nonEmpty)(
        GeneralServiceException("Unable to cancel after submitted")
      )
      _ <- ZIOUtils.failUnless(ScalaUtils.isMatch[SignatureCompleted](latestVersion.signatureStatus))(
        GeneralServiceException("Latest version has no completed signature to cancel")
      )
      _ <- fundSubSignatureIntegrationService.cancelSubdocSignatureRequests(
        lpId,
        versionIndex = latestVersion.versionIndex,
        statuses = Set(FundSubSignatureRequestStatus.Completed),
        actor
      )
      isEnabledUnsignedReview <- checkIsEnabledUnsignedReview(lpId)
      _ <- fundSubSubscriptionQueryService.calculateAndUpdateSignatureStatusInternal(
        latestVersion,
        isEnabledUnsignedReview,
        actor
      )
      isVersionReviewed <- getSubscriptionVersionBasicInfo(
        GetSubscriptionVersionBasicInfoParams(lpId = lpId, versionIndex = SubscriptionVersionIndex.Latest),
        actor
      ).map(_.versionInfoOpt.fold(false)(_.reviewStatus match {
        case SubscriptionVersionReviewStatus.Approved(_, _, _) => true
        case _                                                 => false
      }))
      _ <- FundSubLpFlowStatus.updateLpFlowStatus(
        lpId,
        if (isVersionReviewed) {
          LpStatus.LPFormReviewed
        } else {
          LpStatus.LPInProgress
        }
      )(natsNotificationService)
    } yield ()
  }

  def getToSubmitSubscriptionDocuments(
    params: GetToSubmitSubscriptionDocumentsParams,
    actor: UserId
  ): Task[Seq[SubscriptionSubmittedDocument]] = {
    val lpId = params.fundSubLpId
    for {
      _ <- ZIO.logInfo(s"User $actor get subscription documents to submit for $lpId")
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      toSubmitDocuments <- fundSubSubscriptionSubmitService.getDocumentsToSubmit(lpId, actor)
    } yield FundSubSubscriptionFetchingUtils.convertSubmittedDocs(toSubmitDocuments)
  }

  def lpManuallySubmitPackage(
    params: LpManuallySubmitPackageParams,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    val lpId = params.fundSubLpId
    for {
      _ <- ZIO.logInfo(s"User $actor manually submits subscription package for $lpId")
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      _ <- fundSubPermissionService.validateFundActive(lpId.parent)
      lpStatus <- fundSubPermissionService.validateLpStatus(
        lpId,
        Set(
          LpStatus.LPInProgress,
          LpStatus.LPChangeInProgress,
          LpStatus.LPPendingUnsignedReview,
          LpStatus.LPPendingSubmission,
          LpStatus.LPFormReviewed
        )
      )
      // cancel pending unsigned review if needed
      _ <- ZIOUtils.when(lpStatus.isLppendingUnsignedReview) {
        cancelSubmittedSubscriptionReview(
          lpId,
          actor,
          notifyReviewerByEmail = false
        )
      }
      _ <- fundSubSubscriptionSubmitService.submitSubscriptionPackage(
        lpId,
        actor,
        isManualSubmit = true,
        isSubmittingOnBehalfByFundManager = false,
        httpContext,
        submitForReview = true,
        shouldSubmitWithoutRecord = false
      )
    } yield ()
  }

  // After signature step, subscription will move to hard review step (if review enabled),
  // so we don't want to send email when cancelling soft review, except the case of requesting others to sign, or manual submit
  def prepareLatestVersionBeforeSignature(
    lpId: FundSubLpId,
    actor: UserId,
    allSigners: Seq[UserId]
  ): Task[SubmissionVersionModel] = {
    val requestOthers = allSigners.exists(_ != actor)
    for {
      manualSubmitEnabled <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) { ops =>
        ops.getFundSubPublicModel(lpId.parent).map(_.featureSwitch.exists(_.enableLpManualSubmitSubscription))
      }
      notifyCancelSubmittedSubscriptionFormReview = requestOthers || manualSubmitEnabled
      lastVersionIdOpt <- FDBRecordDatabase.transact(LPSubmissionOperations.Production)(_.getLastVersionIndexOpt(lpId))
      _ <- ZIOUtils.when(lastVersionIdOpt.isEmpty)(
        createFirstVersion(lpId, actor)
      )
      _ <- cancelSubmittedSubscriptionReview(
        lpId,
        actor,
        notifyReviewerByEmail = notifyCancelSubmittedSubscriptionFormReview
      )
      latestVersionOpt <- fundSubSubscriptionQueryService.getSubscriptionVersionModelFromVersionIndex(
        lpId,
        SubscriptionVersionIndex.Latest,
        actor,
        GenerateCleanDocDataStrategy.OnlyIfMissing
      )
      latestVersion <- ZIOUtils.optionToTask(
        latestVersionOpt,
        GeneralServiceException("Should have at least one version")
      )
      hasRequestedChangeStatus = ScalaUtils.isMatch[RequestedChanges](latestVersion.requestReviewStatus)
      newLatestVersion <-
        if (hasRequestedChangeStatus) {
          // We create new version if latest version was requested change by GP
          createNewVersion(
            lpId,
            actor,
            GenerateCleanDocDataStrategy.OnlyIfMissing
          )
        } else {
          ZIO.succeed(latestVersion)
        }
    } yield newLatestVersion
  }

  private def updateSubscriptionStatusWhenRequestOthersSignatures(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[Unit] =
    for {
      _ <- FDBRecordDatabase.transact(LPSubmissionOperations.Production)(
        _.updateSignatureStatus(
          lpId,
          actor,
          SignatureRequestPending()
        )
      )
      _ <- FundSubLpFlowStatus.updateLpFlowStatus(lpId, LpStatus.LPRequestedSignature)(natsNotificationService)
    } yield ()

  def requestOthersSignaturesForMultiDocTypeEnvelope(
    lpId: FundSubLpId,
    actor: UserId,
    signerUserIds: Seq[UserId],
    httpContext: Option[AuthenticatedRequestContext]
  ): Task[Unit] =
    for {
      _ <- updateSubscriptionStatusWhenRequestOthersSignatures(
        lpId,
        actor
      )
      _ <- fsLoggingService.logEventRequestSubscriptionDocSignature(
        actor = actor,
        lpId = lpId,
        signers = signerUserIds,
        httpContext
      )
    } yield ()

  private def requestOthersSignaturesForSingleDocTypeEnvelope(
    lpId: FundSubLpId,
    actor: UserId,
    request: FundSubSignatureRequestBasic,
    signerUserIdMap: Map[String, UserId],
    message: String,
    httpContext: Option[AuthenticatedRequestContext]
  ): Task[Unit] = {
    for {
      _ <- updateSubscriptionStatusWhenRequestOthersSignatures(lpId, actor)
      emailIds <- ZIO
        .foreach(request.signers.filterNot(_.userId == actor)) { recipient =>
          fundSubEmailService.sendFundSubSendSignatureRequestSignerEmail(
            lpId,
            recipient.applicableSignatureFileIds,
            recipient.userId,
            actor,
            message,
            request.envelopeType,
            request.requestId
          )
        }
        .map(_.flatMap(_.flatMap(_.internalIdOpt)))
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = lpId.parent,
        params = AddEventParam(
          actor = Some(actor),
          actorType = AuditLogActorType.InvestorSide,
          eventType = AuditLogEventType.SIGNATURE_REQUEST_OF_SUBSCRIPTION_DOCUMENT_SENT,
          orderId = Option(lpId),
          eventEmail = Seq(
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.lpSendSignatureRequest,
              emailIds = emailIds
            )
          ),
          activityDetail = LpRequestedSignature(signerUserIdMap.values.toSeq)
        )
      )
      _ <- fundSubLpActivityLogService.logActivity(
        lpId = lpId,
        actorOpt = Option(actor),
        at = Option(DateCalculator.instantNow),
        detail = LpRequestedSignature(signerUserIdMap.values.toSeq)
      )
      _ <- fsLoggingService.logEventRequestSubscriptionDocSignature(
        actor = actor,
        lpId = lpId,
        signers = request.signers.map(_.userId),
        httpContext
      )
    } yield ()
  }

  // cancel all signature requests of latest submission version
  private def cancelLatestVersionSignatureRequest(
    lpId: FundSubLpId,
    actor: UserId,
    requestIdOpt: Option[SignatureRequestId] = None
  ): Task[Unit] = {
    for {
      latestVersionOpt <- FDBRecordDatabase.transact(LPSubmissionOperations.Production)(
        _.getLastVersionOpt(lpId)
      )
      pendingRequestIdOpt <- requestIdOpt.fold {
        fundSubSignatureIntegrationService
          .getSubscriptionDocSignatureRequestsMetadata(
            lpId,
            statuses = Set(FundSubSignatureRequestStatus.InProgress),
            versionIndex = latestVersionOpt.fold(1)(_.versionIndex)
          )
          .map(_.headOption.map(_.requestId))
      } {
        ZIO.some
      }
      _ <- ZIOUtils.traverseOptionUnit(latestVersionOpt) { latestVersion =>
        ZIOUtils.when(ScalaUtils.isMatch[SignatureRequestPending](latestVersion.signatureStatus)) {
          for {
            isEnabledUnsignedReview <- checkIsEnabledUnsignedReview(lpId)
            _ <- fundSubSubscriptionQueryService.calculateAndUpdateSignatureStatusInternal(
              latestVersion,
              isEnabledUnsignedReview,
              actor
            )
          } yield ()
        }
      }
      _ <- ZIOUtils.traverseOption(pendingRequestIdOpt) { requestId =>
        for {
          _ <- fundSubSignatureIntegrationService.cancelRequest(requestId, actor)
          _ <- sendCancelSignatureNotificationEmails(lpId, requestId, actor)
        } yield ()
      }
    } yield ()
  }

  private def sendCancelSignatureNotificationEmails(
    lpId: FundSubLpId,
    requestId: SignatureRequestId,
    actor: UserId
  ): Task[Unit] =
    for {
      requestBasic <- fundSubSignatureIntegrationService.getFundSubSignatureRequestBasicUnsafe(
        requestId
      )
      signers = requestBasic.getAllSignerUserIds
      requester = requestBasic.requester
      receivers = (requester +: signers).distinct.filter(_ != actor)
      allFileIds = requestBasic.getAllUnsignedSignatureFileIds
      eventEmail <- ZIO
        .foreach(receivers) { receiver =>
          val fileIds = requestBasic.signers
            .find(_.userId == receiver)
            .map(_.applicableSignatureFileIds)
            .getOrElse(allFileIds)
          fundSubEmailService.sendCancelSignatureRequestEmail(
            lpId,
            fileIds,
            receiver,
            actor,
            envelopeType = requestBasic.envelopeType,
            requestId = requestBasic.requestId
          )
        }
        .map(_.flatMap(_.flatMap(_.internalIdOpt)))
        .map(emailIds =>
          Seq(
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.lpCancelSignatureRequest,
              emailIds = emailIds
            )
          )
        )
      _ <- fundSubLpActivityLogService.logActivity(
        lpId,
        actorOpt = Option(actor),
        at = Option(DateCalculator.instantNow),
        detail = CancelledSubscriptionSignatureRequest(Seq(requestBasic.requestId))
      )
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = lpId.parent,
        params = AddEventParam(
          actor = Some(actor),
          actorType = AuditLogActorType.InvestorSide,
          eventType = AuditLogEventType.SIGNATURE_REQUEST_ON_SUBSCRIPTION_DOCUMENT_CANCELED,
          orderId = Option(lpId),
          eventEmail = eventEmail,
          activityDetail = CancelledSubscriptionSignatureRequest(Seq(requestBasic.requestId))
        )
      )
    } yield ()

  private def cancelSubmittedSubscriptionReview(
    lpId: FundSubLpId,
    actor: UserId,
    notifyReviewerByEmail: Boolean = true
  ): Task[Unit] = {
    for {
      (isSubmittedForReviewType, cancelledVersionIndex) <- FDBRecordDatabase.transact(LPSubmissionOperations.Production) {
        ops =>
          for {
            lastVersion <- ops.getLastVersion(lpId)
            isSubmittedForReviewType = lastVersion.requestReviewStatus match {
              case status: SubmittedForReview =>
                Some(
                  status.reviewType match {
                    case ReviewType.SubscriptionForm => FundSubSubscriptionDocReviewType.UnsignedSubscription
                    case _                           => FundSubSubscriptionDocReviewType.SignedSubscription
                  }
                )
              case _ =>
                Option.empty[FundSubSubscriptionDocReviewType]
            }
            _ <- RecordIO.when(isSubmittedForReviewType.nonEmpty)(
              ops.cancelReview(lpId, actor)
            )
          } yield (isSubmittedForReviewType, lastVersion.versionIndex)
      }
      reviewersOpt <- ZIOUtils.traverseOption(isSubmittedForReviewType) { reviewType =>
        fundSubSubscriptionDocReviewService
          .cancelReviewFlow(
            lpId,
            cancelledVersionIndex,
            reviewType
          )
          .map(
            _.filter(_.status == ReviewFlowStatus.Pending)
              .flatMap(_.currentStepOpt)
              .map(_.reviewers)
              .getOrElse(Seq.empty)
          )
      }
      emailIds <-
        if (
          isSubmittedForReviewType.contains(FundSubSubscriptionDocReviewType.UnsignedSubscription)
          && notifyReviewerByEmail
        ) {
          for {
            reviewers <- ZIO.attempt(reviewersOpt.getOrElse(Seq.empty))
            lpUser <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production)(
              _.getFundSubLpModel(lpId).map(_.mainLp)
            )
            emails <- fundSubEmailService.sendCancelSoftReviewEmail(
              lpId,
              lpUser,
              actor,
              reviewers.map(_.userId)
            )
          } yield emails.flatMap(_.internalIdOpt)
        } else {
          ZIO.attempt(Seq.empty)
        }
      _ <- ZIOUtils.when(isSubmittedForReviewType.contains(FundSubSubscriptionDocReviewType.UnsignedSubscription))(
        for {
          _ <- fundSubLpActivityLogService.logActivity(
            lpId,
            actorOpt = Option(actor),
            at = Option(DateCalculator.instantNow),
            detail = LpCancelSoftReview(cancelledVersionIndex)
          )
          _ <- fundSubAuditLogService.addEvent(
            fundSubId = lpId.parent,
            params = AddEventParam(
              actor = Some(actor),
              actorType = AuditLogActorType.InvestorSide,
              eventType = AuditLogEventType.SUBSCRIPTION_DOCUMENT_EDITED,
              orderId = Option(lpId),
              eventEmail = Seq(
                AddEventEmailParam(
                  fundSubEventType = FundSubEvent.cancelSoftReview,
                  emailIds = emailIds
                )
              ),
              activityDetail = LpCancelSoftReview(cancelledVersionIndex)
            )
          )
        } yield ()
      )
    } yield ()
  }

  private def createNewOrFirstVersion(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[SubmissionVersionModel] = {
    for {
      lastVersionIdOpt <- FDBRecordDatabase.transact(LPSubmissionOperations.Production)(_.getLastVersionIndexOpt(lpId))
      newVersion <-
        if (lastVersionIdOpt.isEmpty) {
          createFirstVersion(lpId, actor).map(_.submissionVersionModel)
        } else {
          createNewVersion(
            lpId,
            actor,
            GenerateCleanDocDataStrategy.OnlyIfMissing
          )
        }
    } yield newVersion
  }

  private def createFirstVersionIfNotExisted(
    lpId: FundSubLpId,
    actor: UserId
  ): Task[SubmissionVersionModel] = {
    for {
      latestVersionOpt <- FDBRecordDatabase.transact(LPSubmissionOperations.Production)(_.getLastVersionOpt(lpId))
      latestVersion <- latestVersionOpt.fold {
        createFirstVersion(lpId, actor).map(_.submissionVersionModel)
      }(ZIO.succeed(_))
    } yield latestVersion
  }

  def createFirstVersion(
    lpId: FundSubLpId,
    actor: UserId,
    initialFormData: Option[GaiaState] = None,
    useInitialGaiaStateDirectly: Boolean = false,
    initialFormDataSourceOpt: Option[FormDataSource] = None
  ): Task[CreateFirstVersionResponse] = {
    for {
      lpFormVersionId <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        for {
          lpResModel <- ops.getFundSubLpRestrictedModel(FundSubLpRestrictedId(lpId))
          formVersionId <- RecordIO.fromOption(
            lpResModel.formVersionIdOpt,
            GeneralServiceException(s"Form version id is empty for $lpId")
          )
        } yield formVersionId
      }
      (lpFormData, gaiaFormInfo) <- fundSubFormService.createNewLpFormData(
        lpFormVersionId,
        actor,
        initialFormData,
        useInitialGaiaStateDirectly
      )
      formProgress = FormDataUtils.calculateProgress(gaiaFormInfo.formData.form, gaiaFormInfo.formStates)
      firstVersion <- FDBRecordDatabase.transact(LPSubmissionOperations.Production) { ops =>
        ops.createFirstVersion(
          lpId,
          actor,
          initialFormDataSourceOpt.fold(lpFormData)(dataSource => lpFormData.copy(lastImportSource = dataSource)),
          FormProgress(
            missingRequiredFields = gaiaFormInfo.allErrorFields.map(_._2.size).sum,
            missingRecommendedFields = gaiaFormInfo.allWarningFields.map(_._2.size).sum,
            formFillingProgress = formProgress
          )
        )
      }
      _ <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops.updateFundSubLpRestrictedModel(FundSubLpRestrictedId(lpId)) { model =>
          model.copy(
            formIds = Seq(lpFormData.lpFormId)
          )
        }
      }
      _ <- greylinDataService.runUnit(
        SubscriptionOrderVersionOperations.insert(
          SubscriptionOrderVersion(
            subscriptionOrderId = firstVersion.fundSubLpId,
            versionIndex = firstVersion.versionIndex,
            formDataId = firstVersion.lpFormId.asNewLpFormIdUnsafe()
          )
        )
      )
    } yield CreateFirstVersionResponse(submissionVersionModel = firstVersion, gaiaFormInfo = gaiaFormInfo)
  }

  private def createNewVersion(
    lpId: FundSubLpId,
    actor: UserId,
    generateCleanDocDataStrategy: GenerateCleanDocDataStrategy
  ): Task[SubmissionVersionModel] = {
    for {
      newVersion <- FDBRecordDatabase.transact(LPSubmissionOperations.Production) { ops =>
        ops.createNewVersion(lpId, actor)
      }
      versionModelOpt <- fundSubSubscriptionQueryService.getSubscriptionVersionModelFromVersionIndex(
        lpId,
        SubscriptionVersionIndex.Single(newVersion.versionIndex),
        actor,
        generateCleanDocDataStrategy
      )
      versionModel <- ZIOUtils.optionToTask(versionModelOpt, GeneralServiceException("Fail to create new version"))
      _ <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops.updateFundSubLpRestrictedModel(FundSubLpRestrictedId(lpId)) { model =>
          model.copy(
            formIds = model.formIds :+ versionModel.lpFormId
          )
        }
      }
      // Reset some flags related to saving submitted data to LP profile when Form Data Change Version so the Save to Lp Profile can be triggered again
      _ <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        ops.updateFundSubLpRestrictedModel(FundSubLpRestrictedId(lpId))(model =>
          model.copy(
            // In case we wanna retain previous saveProfileMainFormEvents -> user another mechanism
            // We already have audit logs on LP profile app side -> just skip this for now
            saveProfileMainFormEvents = Seq.empty,
            dismissSaveProfileSuggestionEvents = Seq.empty
          )
        )
      }
      _ <- greylinDataService.runUnit(
        SubscriptionOrderVersionOperations.insert(
          SubscriptionOrderVersion(
            subscriptionOrderId = newVersion.fundSubLpId,
            versionIndex = newVersion.versionIndex,
            formDataId = newVersion.lpFormId.asNewLpFormIdUnsafe()
          )
        )
      )
    } yield versionModel
  }

  private def markLpHasEditedFormIfApplicable(lpId: FundSubLpId, actor: UserId) = {
    for {
      actorIsFundManager <- fundSubPermissionService.checkIfUserHasFundManagerRole(lpId.parent, actor)
      _ <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production) { ops =>
        for {
          lpModel <- ops.getFundSubLpModel(lpId)
          actorIsLpOrCollaborator = (lpModel.mainLp +: lpModel.collaborators).contains(actor)
          _ <- RecordIO.unless(actorIsFundManager && !actorIsLpOrCollaborator)(
            ops.updateFundSubLpRestrictedModel(FundSubLpRestrictedId(lpId))(_.copy(lpEditedForm = true))
          )
        } yield ()
      }
    } yield ()
  }

  private def prepareRequestLpChangeEmailGenerate(
    lpId: FundSubLpId,
    lpFormId: FundSubLpFormIdTrait,
    mainLp: UserId,
    collaborators: Seq[UserId],
    messageOpt: Option[String],
    attachedComments: Seq[IssueId],
    actorId: UserId,
    ccReceivers: Seq[EmailAddress]
  ): Task[RequestLpChangeEmailGenerate] = {
    for {
      commentData <- formCommentService.getFormPublicCommentsThreadFromIssueIdsUnsafe(
        lpId = lpId,
        formId = Left(lpFormId),
        attachedComments,
        actorId
      )
      emailCommentData = commentData.map { case (_, issueMsg, formInfo) =>
        val latestComment =
          issueMsg.comments
            .maxByOption(_.createdAt.map(_.seconds).getOrElse(0L))
            .fold[Either[IssueComment, IssueMessage]](Right(issueMsg))(Left(_))
        val (latestCommentContent, latestCommentCreatorOpt, latestCommentCreatedAtOpt) = latestComment match {
          case Right(issueMsg) =>
            val issueCreateActivityOpt = issueMsg.activities.flatMap(_.asMessage.sealedValue.createActivity).headOption
            (issueMsg.title, issueCreateActivityOpt.map(_.creator), issueCreateActivityOpt.flatMap(_.occurAt))
          case Left(issueComment) =>
            (issueComment.content, Some(issueComment.creator), issueComment.createdAt)
        }
        AttachedCommentsOnRequestChangeEmail(
          tocSection = formInfo.tocSection,
          fieldDescription = formInfo.fieldDescription,
          comment = latestCommentContent,
          creatorOpt = latestCommentCreatorOpt,
          createdAtOpt = latestCommentCreatedAtOpt
        )
      }
    } yield fundSubEmailService.prepareRequestLpChangeEmailGenerate(
      actorId,
      mainLp,
      mainLp +: collaborators,
      lpId,
      messageOpt,
      emailCommentData.toList,
      ccReceivers = ccReceivers
    )
  }

  def sendEmailAndAddLoggingAfterSubDocSignatureRequestCompleted(
    lpId: FundSubLpId,
    fundSubModel: FundSubPublicModel,
    signedRequest: FundSubSignatureRequestBasic,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext],
    versionIndex: Int
  ): Task[Unit] = {
    val subdocPackageOpt = signedRequest.getSubdocPackage(lpId, versionIndex)
    val signedSubdocs = subdocPackageOpt.map(_.files).getOrElse(Seq.empty)
    val unsignedFileIds = subdocPackageOpt.map(_.getUnsignedSignatureFileIds).getOrElse(Seq.empty)
    for {
      // When request is completely signed, we send email to all signers to notify
      emailIdsForSigners <-
        ZIO
          .foreach(signedRequest.signers.distinctBy(_.userId).filterNot(_.userId == signedRequest.requester)) {
            signer =>
              val accessibleSignatureFileIds = signer.applicableSignatureFileIds.toSet
              val accessibleSignedFileIds =
                signedSubdocs
                  .filter(file => accessibleSignatureFileIds.contains(file.originalSignatureFileId))
                  .flatMap(_.signedFileId)
              fundSubEmailService.sendFundSubDoneSigningSignerEmail(
                lpId,
                accessibleSignedFileIds,
                signer.userId,
                actor,
                signedRequest.envelopeType,
                signedRequest.requestId
              )
          }
          .map(_.flatMap(_.flatMap(_.internalIdOpt)))
      // Send email update to requester only when signer is not requester
      emailIdForRequester <-
        ZIO
          .when(actor != signedRequest.requester) {
            fundSubEmailService.sendFundSubDoneSigningRequesterEmail(
              lpId,
              fundSubModel.investorEntity,
              unsignedFileIds,
              signedRequest.requester,
              actor,
              signedRequest.envelopeType,
              signedRequest.requestId
            )
          }
          .map(_.flatten)
      _ <- fundSubAuditLogService.addEvent(
        fundSubId = lpId.parent,
        params = AddEventParam(
          actor = Some(actor),
          actorType = AuditLogActorType.InvestorSide,
          eventType = AuditLogEventType.SUBSCRIPTION_DOCUMENT_SIGNED,
          orderId = Option(lpId),
          eventEmail = Seq(
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.signerDoneSignatureRequest,
              emailIds = emailIdsForSigners
            ),
            AddEventEmailParam(
              fundSubEventType = FundSubEvent.signerDoneSignatureRequestToLp,
              emailIds = Seq(emailIdForRequester).flatMap(_.flatMap(_.internalIdOpt))
            )
          ),
          activityDetail = SignedSubscriptionDocument(
            requestIds = Seq(signedRequest.requestId)
          )
        )
      )
      _ <- fsLoggingService.logEventLpDoneSigning(
        actor,
        lpId,
        httpContext
      )
    } yield ()
  }

  // This method is used for upload signed docs flow (both for LP and GP)
  private def uploadSignedDocumentAndAttemptToSubmitPackageInternal(
    lpId: FundSubLpId,
    signedDocs: Seq[LpSignedDocument],
    signatureBy: Seq[UserId],
    lpSignatureType: LpSignatureType,
    actor: UserId,
    httpContext: Option[AuthenticatedRequestContext],
    isSubmittingOnBehalfByFundManager: Boolean = false,
    shouldSubmitForReview: Boolean = true,
    shouldSubmitWithoutRecord: Boolean = false,
    signatureReusedFromVersionIndexOpt: Option[Int] = None
  ): Task[(versionIndex: Int, signedDocs: Seq[LpSignedDocument])] = {
    for {
      sharedSignDocs <- shareDocumentsToFolder(
        actor,
        FolderId.channelSystemFolderId(FolderType.SubscriptionDoc(lpId)),
        signedDocs,
        httpContext
      )
        .map(_.collect { case signedDoc: LpSignedDocument =>
          signedDoc
        })
      versionIndex <- FDBRecordDatabase.transact(LPSubmissionOperations.Production) { lpSubmissionOps =>
        for {
          latestVersionIndex <- lpSubmissionOps.getLastVersionIndex(lpId)
          _ <- lpSubmissionOps.updateSignatureStatus(
            lpId,
            latestVersionIndex,
            actor,
            SignatureCompleted(
              sharedSignDocs,
              lpSignatureType,
              signatureBy,
              signatureAt = Some(Instant.now),
              signatureReusedFromSubmissionVersionIndexOpt = signatureReusedFromVersionIndexOpt
            )
          )
        } yield latestVersionIndex
      }
      shouldAutoSubmit <- fundSubSubscriptionSubmitService.shouldAutoSubmitSubscriptionPackage(
        lpId,
        isSubmittingOnBehalfByFundManager
      )
      _ <-
        if (shouldAutoSubmit) {
          fundSubSubscriptionSubmitService.submitSubscriptionPackage(
            lpId = lpId,
            actor = actor,
            isManualSubmit = false,
            isSubmittingOnBehalfByFundManager = isSubmittingOnBehalfByFundManager,
            httpContext = httpContext,
            submitForReview = shouldSubmitForReview,
            shouldSubmitWithoutRecord = shouldSubmitWithoutRecord
          )
        } else {
          FundSubLpFlowStatus.updateLpFlowStatus(lpId, LPPendingSubmission)(natsNotificationService)(
            using lpDashboardService,
            dataLakeIngestionService,
            fundSubLpStatusHistoryService,
            greylinDataService
          )
        }
      _ <- triggerAutoSaveProfileFromSubscription(actor, lpId)
    } yield (versionIndex, sharedSignDocs)
  }

  // Asynchronously check autosave criteria and autosave profile
  def triggerAutoSaveProfileFromSubscription(
    actor: UserId,
    fundSubLpId: FundSubLpId
  ): Task[Unit] = {
    for {
      workflowId <- ZIO.attempt(
        TemporalWorkflowId.unsafeFromSuffix(s"FundSubAutoSaveSubscriptionData-${UUID.randomUUID}")
      )
      _ <- ZIO.logInfo(
        s"Prepare starting FundSubIAAutoSaveWorkflow $workflowId, actor = $actor, fundSubLpId = $fundSubLpId"
      )
      workflowStub <- FundSubAutoSaveSubscriptionDataWorkflowImpl.instance
        .getWorkflowStub(workflowId)
        .provideEnvironment(temporalEnvironment.workflowClient)
      _ <- ZIO.logInfo(
        s"WorkflowStub FundSubIAAutoSaveWorkflow created $workflowId, actor = $actor, fundSubLpId = $fundSubLpId"
      )
      _ <- ZWorkflowStub.start(
        workflowStub.autoSaveSubscriptionForm(AutoSaveSubscriptionFormParams(actor = actor, fundSubLpId = fundSubLpId))
      )
      _ <- ZIO.logInfo(
        s"Workflow FundSubIAAutoSaveWorkflow started $workflowId, actor = $actor, fundSubLpId = $fundSubLpId"
      )
    } yield ()
  }

  def updateSignedDocumentAndAttemptToSubmitPackage(
    lpId: FundSubLpId,
    signedRequest: FundSubSignatureRequestBasic,
    lpSignatureType: LpSignatureType,
    versionIndex: Int,
    httpContext: Option[AuthenticatedRequestContext],
    isSubmittingOnBehalfByFundManager: Boolean = false,
    shouldSubmitForReview: Boolean = true
  ): Task[Unit] = {
    val signedSubdocPackageOpt = signedRequest.getSubdocPackage(lpId, versionIndex)
    val subdocSignedFilesAndCertificates = signedSubdocPackageOpt
      .map { signedPackage =>
        signedPackage.getSignedFileAndCertificateTuples.map { case (fileId, certificateFileIdOpt) =>
          LpSignedDocument(fileId = fileId, certificateFileIdOpt = certificateFileIdOpt)
        }
      }
      .getOrElse(Seq.empty)
    val signatureBy = signedRequest.signers.filter(_.status == FundSubSignerStatus.Signed).map(_.userId)
    for {
      hasPermission <- fundSubPermissionService.checkIfUserCanAccessLpView(lpId, signedRequest.requester)
      lpModel <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production)(_.getFundSubLpModel(lpId))
      actorToSubmit = if (hasPermission) signedRequest.requester else lpModel.mainLp
      _ <- uploadSignedDocumentAndAttemptToSubmitPackageInternal(
        lpId,
        subdocSignedFilesAndCertificates,
        signatureBy,
        lpSignatureType,
        actorToSubmit,
        httpContext,
        isSubmittingOnBehalfByFundManager,
        shouldSubmitForReview
      )
      _ <- triggerAutoSaveProfileFromSubscription(actorToSubmit, lpId)
    } yield ()
  }

  def addLoggingAfterRecipientSigned(
    lpId: FundSubLpId,
    actor: UserId,
    requestId: SignatureRequestId,
    httpContext: Option[RequestContext]
  ): Task[Unit] = {
    for {
      _ <- fundSubLpActivityLogService.logActivity(
        lpId = lpId,
        actorOpt = Option(actor),
        at = Option(DateCalculator.instantNow),
        detail = SignedSubscriptionDocument(requestIds = Seq(requestId))
      )
      _ <- fsLoggingService.logEventSignForm(
        actor,
        lpId,
        isUpload = false,
        httpContext
      )
    } yield ()

  }

  def importExtractedDataIntoSubscription(
    requestId: FundSubDataExtractRequestId,
    actorId: UserId,
    actorIpAddressOpt: Option[String]
  ): Task[Unit] = {
    for {
      request <- FundSubDataExtractJvmUtils.getRequest(requestId)
      lpId = request.lpId
      _ <- ZIO.logInfo(s"User $actorId import extracted form data to subscription form of $lpId")
      given RebacStoreOperation <- fundSubPermissionService.getFundSubRebacStore(lpId.parent)
      _ <- fundSubPermissionService.validateFundManagerCanAccessLpSubscriptionDocsR(lpId, actorId)
      _ <- ZIOUtils.validate(request.status != FundSubDataExtractRequestStatus.Approved)(
        GeneralServiceException("Data extract request result is already completed")
      )
      _ <- fundSubPermissionService.validateLpStatus(
        lpId,
        Set(
          LpStatus.LPPendingReview,
          LpStatus.LPCountersigned,
          LpStatus.LPSubmitted,
          LpStatus.LPCompleted,
          LpStatus.LPChangeInProgress
        )
      )
      _ <- request.extractedCueTableMetadataOpt.fold(
        importExtractedFormDataIntoSubscription(request, actorId, actorIpAddressOpt)
      ) { _ =>
        importExtractedCueTableDataIntoSubscription(request, actorId, actorIpAddressOpt)
      }
    } yield ()
  }

  private def importExtractedFormDataIntoSubscription(
    request: FundSubDataExtractRequest,
    actorId: UserId,
    actorIpAddressOpt: Option[String]
  ) = {
    val requestId = request.id
    val lpId = request.lpId
    for {
      extractedFormData <- DataExtractRequestFormDataUtils.getExtractedFormData(requestId)
      getFormResp <- formService.getForm(
        formId = extractedFormData.formVersionId.parent,
        versionIdOpt = Some(extractedFormData.formVersionId),
        actor = actorId,
        shouldCheckPermission = false
      )
      gaiaFormInfo = GaiaFormInfo(getFormResp.formData, extractedFormData.gaiaState)
      formFillingProgress = FormDataUtils.calculateProgress(
        gaiaFormInfo.formData.form,
        gaiaFormInfo.formStates
      )
      formProgress = FormProgress(
        formFillingProgress = formFillingProgress,
        missingRequiredFields = gaiaFormInfo.allErrorFields.map(_._2.size).sum,
        missingRecommendedFields = gaiaFormInfo.allWarningFields.map(_._2.size).sum
      )
      (
        newVersion: SubmissionVersionModel,
        previousVersion: SubmissionVersionModel,
        previousVersionFormDataOpt: Option[LpFormData]
      ) <-
        FDBRecordDatabase.transact(
          LPSubmissionOperations.Production
        )(
          _.createNewVersionFromDataExtraction(
            requestId,
            lpId,
            extractedFormData.formVersionId,
            extractedFormData.gaiaState,
            formProgress,
            actorId
          )
        )
      _ <- FDBRecordDatabase.transact(FundSubLpModelStoreOperations.Production)(
        _.updateFundSubLpRestrictedModel(FundSubLpRestrictedId(lpId))(
          _.copy(formVersionIdOpt = Some(extractedFormData.formVersionId))
        )
      )

      /** Todo @voxuannguyen2001: need UI update - GPs don't need to manually input the commitment amount
        */
      adminResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) {
        _.getFundSubAdminRestrictedModel(lpId.parent)
      }
      _ <- FundSubCommitmentHelper.lpUpdateSubmittedCommitment(
        lpId = lpId,
        subFundAmounts = fundSubFormService.getSubmittedAmounts(adminResModel.investmentFunds, gaiaFormInfo)
      )
      _ <- fundSubSubdocDataExtractService.markRequestAsApprovedUnsafe(requestId, actorId, actorIpAddressOpt)
      _ <- fundSubSubscriptionDocReviewService.copyReviewFlowToNewVersion(
        oldVersionIndex = previousVersion.versionIndex,
        newVersionIndex = newVersion.versionIndex,
        lpId = lpId,
        reviewType = review.FundSubSubscriptionDocReviewType.SignedSubscription
      )

      investmentEntityNameOpt <- fundSubFormService.getInvestmentEntity(gaiaFormInfo)
      _ <- ZIOUtils.traverseOption(investmentEntityNameOpt) { investmentEntityName =>
        InvestmentEntityHelper
          .updateFirmName(
            lpId,
            investmentEntityName,
            isFromFormData = true,
            actor = actorId
          )(natsNotificationService)
          .unit
      }
      _ <- ZIOUtils.when(shouldUpdateRequiredDocList(formProgress)) {
        val requiredFiles = fundSubFormService.getFormRequiredDocs(gaiaFormInfo)
        newSupportingDocService.updateFormRequiredDocs(lpId, requiredFiles)
      }
      _ <- lpDashboardService.updateLpInfoRecord(
        lpId,
        _.copy(formFillingProgress = formFillingProgress)
      )
      _ <- FundSubDataLakeUtils.updateLpFormData(
        lpId = lpId,
        form = gaiaFormInfo.formData.form,
        state = extractedFormData.gaiaState,
        progress = formFillingProgress,
        missingRequiredFields = formProgress.missingRequiredFields,
        missingRecommendedFields = formProgress.missingRecommendedFields,
        existingProgressOpt = previousVersion.formProgress,
        existingFormDataOpt = previousVersionFormDataOpt
      )(dataLakeIngestionService)
      _ <- FundSubGreylinDataService.updateFormProgress(
        lpId = lpId,
        formProgress = formProgress.formFillingProgress
      )
    } yield ()
  }

  private def importExtractedCueTableDataIntoSubscription(
    request: FundSubDataExtractRequest,
    actorId: UserId,
    actorIpAddressOpt: Option[String]
  ) = {
    val requestId = request.id
    for {
      extractedCueTableData <- DataExtractRequestCueTableDataUtils.getExtractedCueTableData(request)
      _ = extractedCueTableData
      // TODO: @voxuannguyen2001: need UI update - GPs don't need to manually input the commitment amount
      _ <- fundSubSubdocDataExtractService.markRequestAsApprovedUnsafe(requestId, actorId, actorIpAddressOpt)
      // TODO: @pikachu, @flashmt to update commitment amount, investment entity name, required supporting doc list (if applicable) using OCR table
    } yield ()
  }

  def checkIfLpHasPreviousESignedVersion(lpId: FundSubLpId, actor: UserId)
    : Task[CheckIfLpHasPreviousESignedVersionResponse] =
    for {
      _ <- ZIO.succeed(s"User $actor check if lp $lpId has previous e-signed version")
      _ <- fundSubPermissionService.validateUserCanAccessLpView(lpId, actor)
      hasPreviousESignedVersion <- FDBRecordDatabase.transact(LPSubmissionOperations.Production)(
        _.getPreviousESignedVersionOpt(lpId)
          .map(_.nonEmpty)
      )
    } yield CheckIfLpHasPreviousESignedVersionResponse(hasPreviousESignedVersion)

  def applySignaturePagesFromPreviousVersions(
    lpId: FundSubLpId,
    updatedSignedDocuments: Seq[FileId],
    actorId: UserId,
    signedVersionIndex: Int
  ): Task[Unit] = {
    for {
      _ <- uploadSignedDocumentAndAttemptToSubmitPackageInternal(
        lpId,
        updatedSignedDocuments.map(fileId => LpSignedDocument(fileId = fileId)),
        signatureBy = Seq(actorId),
        lpSignatureType = LpSignatureType.SignaturePagesReused,
        actor = actorId,
        httpContext = None,
        signatureReusedFromVersionIndexOpt = Some(signedVersionIndex)
      )
      _ <- triggerAutoSaveProfileFromSubscription(actorId, lpId)
    } yield ()
  }

}

object FundSubSubscriptionDocService {

  given convertReviewType: Conversion[FundSubSubscriptionDocReviewType, ReviewType] = {
    case FundSubSubscriptionDocReviewType.UnsignedSubscription => ReviewType.SubscriptionForm
    case FundSubSubscriptionDocReviewType.SignedSubscription   => ReviewType.SubscriptionSignedDoc
  }

  case class CreateFirstVersionResponse(
    submissionVersionModel: SubmissionVersionModel,
    gaiaFormInfo: GaiaFormInfo
  )

}
