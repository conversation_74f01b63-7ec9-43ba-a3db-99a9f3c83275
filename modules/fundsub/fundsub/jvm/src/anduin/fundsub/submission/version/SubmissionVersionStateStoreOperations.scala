// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.submission.version

import anduin.fdb.record.FDBOperations
import anduin.flow.fdb.StateStoreOperations

import anduin.fundsub.submission.version.SubmissionVersionStateStoreProvider.*

final case class SubmissionVersionStateStoreOperations(store: Store)
    extends StateStoreOperations(SubmissionVersionStateStoreProvider)

object SubmissionVersionStateStoreOperations
    extends FDBOperations.Single[RecordEnum, SubmissionVersionStateStoreOperations](SubmissionVersionStateStoreProvider)
