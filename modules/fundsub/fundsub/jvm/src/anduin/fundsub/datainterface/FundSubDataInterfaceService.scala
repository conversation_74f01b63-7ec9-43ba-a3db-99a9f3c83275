// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datainterface

import zio.{Task, ZIO}

import anduin.cue.model.CueSchemaCommonDataTypes.{DateTimeSchema, SubscriptionStatus, SubscriptionStatusSchema}
import anduin.digitization.service.DigitizationImportExportService.cueModuleKeySpace
import anduin.fdb.record.{DefaultCluster, FDBRecordDatabase}
import anduin.forms.service.DataLayerService
import anduin.fundsub.cue.model.CueJsonFundSubTypes.{FundInfoJson, SubscriptionDataJson}
import anduin.fundsub.cue.model.CueSchemaFundSubTypes.{
  FundInfoSchema,
  SubscriptionDataSchema,
  SubscriptionWorkflowDataSchema
}
import anduin.fundsub.datainterface.FundSubDataInterfaceService.{
  FundInfoSchemaWithId,
  SubscriptionDataSchemaWithId,
  toSubscriptionStatusSchema
}
import anduin.fundsub.form.FundSubFormService
import anduin.fundsub.models.{FundInfoSchemaDataStoreOperations, SubscriptionSchemaDataStoreOperations}
import anduin.greylin.core.cdc.TypedEvent
import anduin.greylin.core.event.GreylinEventListener
import anduin.greylin.{GreylinDataService, modelti}
import anduin.id.fundsub.{FundInfoSchemaDataId, FundSubId, FundSubLpId, SubscriptionSchemaDataId}
import anduin.kafka.KafkaService
import anduin.protobuf.fundsub.datainterface.fund.FundInfoSchemaData
import anduin.protobuf.fundsub.datainterface.subscription.SubscriptionSchemaData
import anduin.service.GeneralServiceException
import anduin.utils.DateTimeUtils
import com.anduin.stargazer.service.utils.ZIOUtils
import com.anduin.stargazer.util.date.DateCalculator
import anduin.greylin.operation.fundsub.{FundDataSchemaOperations, SubscriptionDataSchemaOperations}

final case class FundSubDataInterfaceService(
  fundSubFormService: FundSubFormService,
  dataLayerService: DataLayerService,
  kafkaService: KafkaService,
  greylinDataService: GreylinDataService
) extends GreylinEventListener {

  override val groupName: String = "fund-sub-data-interface-service-consumer"

  override val inputs: List[INPUT] = List(
    registerInput(modelti.SubscriptionOrder -> handleSubscriptionOrderEvent),
    registerInput(modelti.FundSubscription -> handleFundSubscriptionEvent)
  )

  private def extractAndSaveSubscriptionSchemaData(subscriptionOrder: modelti.SubscriptionOrder): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"Extracting lp data for ${subscriptionOrder.id.idString} and save to data interface")
      submitTime <- ZIO.succeed(DateCalculator.instantNow)
      subscriptionDataSchemaEither <- fundSubFormService.transformLpDataToSubscriptionData(subscriptionOrder.id)
      _ <- subscriptionDataSchemaEither.fold(
        error => ZIO.logError(s"Failed to transform lp data to subscription data interface: $error"),
        subscriptionDataSchema =>
          for {
            updatedData <- ZIO.succeed(
              subscriptionDataSchema
                .copy(
                  subscriptionWorkflowData = Some(
                    SubscriptionWorkflowDataSchema(
                      dateSubmitted = DateTimeSchema(
                        epochMs = submitTime.toEpochMilli,
                        zoneId = DateTimeUtils.utcTimezone
                      ),
                      subscriptionStatus = toSubscriptionStatusSchema(subscriptionOrder.status)
                    )
                  )
                )
                .toCueJson
            )

            _ <- saveSubscriptionSchemaData(
              lpId = subscriptionOrder.id,
              subscriptionDataJson = updatedData
            )
            _ <- greylinDataService.run(
              SubscriptionDataSchemaOperations.Default.upsert(
                modelti.fundsub.SubscriptionDataSchema(
                  id = subscriptionOrder.id,
                  data = updatedData.json.noSpaces,
                  updatedAt = Option(submitTime)
                )
              )
            )

          } yield ()
      )
    } yield ()
  }

  private def handleSubscriptionOrderEvent(event: TypedEvent[modelti.SubscriptionOrder]): Task[Unit] = {
    event match {
      case TypedEvent.UpdateEvent(oldData, newData) =>
        ZIO
          .when(
            newData.status == modelti.SubscriptionOrderStatus.Submitted &&
              oldData.status != newData.status
          ) {
            extractAndSaveSubscriptionSchemaData(newData)
          }
          .unit
      case _ => ZIO.unit
    }
  }

  private def extractAndSaveFundInfoSchemaData(fundSubscription: modelti.FundSubscription): Task[Unit] = {
    ZIOUtils
      .traverseOption(fundSubscription.latestFormVersionId) { latestFormVersionId =>
        for {
          _ <- ZIO.logInfo(s"Extracting fund data for ${fundSubscription.id.idString} and save to data interface")
          fundInfoSchemaEither <- dataLayerService.getFormVersionFundInfoViaCue(latestFormVersionId)
          _ <- fundInfoSchemaEither.fold(
            error => ZIO.logWarning(s"Failed to transform fund data to fund info interface: $error"),
            fundInfoSchema =>
              for {
                updatedData <- ZIO.succeed(fundInfoSchema.toCueJson)
                _ <- saveFundInfoSchemaData(
                  fundSubId = fundSubscription.id,
                  fundInfoJson = updatedData
                )
                _ <- greylinDataService.run(
                  FundDataSchemaOperations.Default.upsert(
                    modelti.fundsub.FundDataSchema(
                      id = fundSubscription.id,
                      data = updatedData.json,
                      updatedAt = Option(DateCalculator.instantNow)
                    )
                  )
                )
              } yield ()
          )
        } yield ()
      }
      .unit
  }

  private def handleFundSubscriptionEvent(event: TypedEvent[modelti.FundSubscription]): Task[Unit] = {
    event match {
      case TypedEvent.InsertEvent(data) =>
        extractAndSaveFundInfoSchemaData(data)
      case TypedEvent.UpdateEvent(oldData, newData) =>
        ZIO
          .when(oldData.latestFormVersionId != newData.latestFormVersionId) {
            extractAndSaveFundInfoSchemaData(newData)
          }
          .unit
      case _ => ZIO.unit
    }
  }

  // public functions
  def getSubscriptionSchemaData(lpId: FundSubLpId): Task[Option[SubscriptionDataSchemaWithId]] = {
    for {
      _ <- ZIO.logInfo(s"Getting subscription schema data for ${lpId.idString}")
      modelOpt <- FDBRecordDatabase.transact(SubscriptionSchemaDataStoreOperations.Production) {
        _.getOpt(lpId)
      }
    } yield modelOpt.flatMap(SubscriptionDataSchemaWithId.unapply)
  }

  def getSubscriptionSchemaDataForFund(fundSubId: FundSubId): Task[Seq[SubscriptionDataSchemaWithId]] = {
    ZIO.logSpan("getSubscriptionSchemaDataForFund") {
      for {
        _ <- ZIO.logInfo(s"Getting subscription schema data for fund ${fundSubId.idString}")
        models <- FDBRecordDatabase.transact(SubscriptionSchemaDataStoreOperations.Production) {
          _.getByFund(fundSubId)
        }
        _ <- ZIO.logInfo(s"Got ${models.size} subscriptions")
      } yield models.flatMap(SubscriptionDataSchemaWithId.unapply)
    }
  }

  def getFundInfoSchemaData(fundSubId: FundSubId): Task[Option[FundInfoSchemaWithId]] = {
    for {
      _ <- ZIO.logInfo(s"Getting fund info schema data for ${fundSubId.idString}")
      modelOpt <- FDBRecordDatabase.transact(FundInfoSchemaDataStoreOperations.Production) {
        _.getOpt(fundSubId)
      }
    } yield modelOpt.flatMap(FundInfoSchemaWithId.unapply)
  }

  // Fund Sub internal functions
  private[fundsub] def saveSubscriptionSchemaData(
    lpId: FundSubLpId,
    subscriptionDataJson: SubscriptionDataJson
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"Saving subscription schema data for ${lpId.idString}")
      _ <- ZIO.when(subscriptionDataJson.toCueSchema.isLeft) {
        ZIO.fail(GeneralServiceException(s"Invalid subscription data for ${lpId.idString}"))
      }
      _ <- FDBRecordDatabase.transact(SubscriptionSchemaDataStoreOperations.Production) {
        _.upsert(
          SubscriptionSchemaData(
            id = SubscriptionSchemaDataId(lpId),
            fundSubId = lpId.parent,
            data = Some(subscriptionDataJson.json)
          )
        )
      }
    } yield ()
  }

  private[fundsub] def saveFundInfoSchemaData(
    fundSubId: FundSubId,
    fundInfoJson: FundInfoJson
  ): Task[Unit] = {
    for {
      _ <- ZIO.logInfo(s"Saving fund info schema data for ${fundSubId.idString}")
      _ <- ZIO.when(fundInfoJson.toCueSchema.isLeft) {
        ZIO.fail(GeneralServiceException(s"Invalid fund info data for ${fundSubId.idString}"))
      }
      _ <- FDBRecordDatabase.transact(FundInfoSchemaDataStoreOperations.Production) {
        _.upsert(
          FundInfoSchemaData(
            id = FundInfoSchemaDataId(fundSubId),
            data = Some(fundInfoJson.json)
          )
        )
      }
    } yield ()
  }

}

object FundSubDataInterfaceService {

  def fromSubscriptionStatusSchema(status: SubscriptionStatusSchema): modelti.SubscriptionOrderStatus = {
    status.value match {
      case SubscriptionStatus.NotStarted        => modelti.SubscriptionOrderStatus.NotStarted
      case SubscriptionStatus.InProgress        => modelti.SubscriptionOrderStatus.InProgress
      case SubscriptionStatus.ChangeInProgress  => modelti.SubscriptionOrderStatus.ChangeInProgress
      case SubscriptionStatus.FormFilled        => modelti.SubscriptionOrderStatus.FormFilled
      case SubscriptionStatus.PendingReview     => modelti.SubscriptionOrderStatus.PendingReview
      case SubscriptionStatus.FormReviewed      => modelti.SubscriptionOrderStatus.FormReviewed
      case SubscriptionStatus.PendingSignature  => modelti.SubscriptionOrderStatus.PendingSignature
      case SubscriptionStatus.PendingSubmission => modelti.SubscriptionOrderStatus.PendingSubmission
      case SubscriptionStatus.PendingApproval   => modelti.SubscriptionOrderStatus.PendingApproval
      case SubscriptionStatus.Submitted         => modelti.SubscriptionOrderStatus.Submitted
      case SubscriptionStatus.Countersigned     => modelti.SubscriptionOrderStatus.Countersigned
      case SubscriptionStatus.Complete          => modelti.SubscriptionOrderStatus.Complete
      case SubscriptionStatus.Removed           => modelti.SubscriptionOrderStatus.Removed
      case SubscriptionStatus.Unknown           => modelti.SubscriptionOrderStatus.Unrecognized
    }
  }

  def toSubscriptionStatusSchema(status: modelti.SubscriptionOrderStatus): SubscriptionStatusSchema = {
    SubscriptionStatusSchema(
      value = status match {
        case modelti.SubscriptionOrderStatus.NotStarted        => SubscriptionStatus.NotStarted
        case modelti.SubscriptionOrderStatus.InProgress        => SubscriptionStatus.InProgress
        case modelti.SubscriptionOrderStatus.ChangeInProgress  => SubscriptionStatus.ChangeInProgress
        case modelti.SubscriptionOrderStatus.FormFilled        => SubscriptionStatus.FormFilled
        case modelti.SubscriptionOrderStatus.PendingReview     => SubscriptionStatus.PendingReview
        case modelti.SubscriptionOrderStatus.FormReviewed      => SubscriptionStatus.FormReviewed
        case modelti.SubscriptionOrderStatus.PendingSignature  => SubscriptionStatus.PendingSignature
        case modelti.SubscriptionOrderStatus.PendingSubmission => SubscriptionStatus.PendingSubmission
        case modelti.SubscriptionOrderStatus.PendingApproval   => SubscriptionStatus.PendingApproval
        case modelti.SubscriptionOrderStatus.Submitted         => SubscriptionStatus.Submitted
        case modelti.SubscriptionOrderStatus.Countersigned     => SubscriptionStatus.Countersigned
        case modelti.SubscriptionOrderStatus.Complete          => SubscriptionStatus.Complete
        case modelti.SubscriptionOrderStatus.Removed           => SubscriptionStatus.Removed
        case modelti.SubscriptionOrderStatus.Unrecognized      => SubscriptionStatus.Unknown
      }
    )
  }

  final case class SubscriptionDataSchemaWithId(
    id: SubscriptionSchemaDataId,
    data: SubscriptionDataSchema
  )

  object SubscriptionDataSchemaWithId {

    def unapply(model: SubscriptionSchemaData): Option[SubscriptionDataSchemaWithId] = {
      model.toSubscriptionDataSchema.map { schema =>
        SubscriptionDataSchemaWithId(
          id = model.id,
          data = schema
        )
      }
    }

  }

  final case class FundInfoSchemaWithId(
    id: FundInfoSchemaDataId,
    data: FundInfoSchema
  )

  object FundInfoSchemaWithId {

    def unapply(model: FundInfoSchemaData): Option[FundInfoSchemaWithId] = {
      model.toFundInfoSchema.map { schema =>
        FundInfoSchemaWithId(
          id = model.id,
          data = schema
        )
      }
    }

  }

}
