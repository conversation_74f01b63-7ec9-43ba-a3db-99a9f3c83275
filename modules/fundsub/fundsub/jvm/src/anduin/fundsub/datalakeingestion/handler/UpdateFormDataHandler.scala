// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datalakeingestion.handler

import java.time.Instant
import java.time.format.DateTimeFormatter

import fundsub.webhook.*
import io.circe.{<PERSON><PERSON>, parser}
import zio.{Task, ZIO}

import anduin.dashboard.model.{Order as _, *}
import anduin.evendim.client.EvendimClient
import anduin.evendim.model.datalake.*
import anduin.fdb.record.FDBRecordDatabase
import anduin.fundsub.datalakeingestion.model.{FormFieldValue, UpdateFormDataParams}
import anduin.fundsub.datalakeingestion.utils.DataConversionUtils
import anduin.fundsub.models.FundSubModelStoreOperations
import anduin.fundsub.webhook.WebhookKafkaTopic
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.kafka.KafkaAsyncExecutor
import anduin.utils.DateTimeUtils
import com.anduin.stargazer.service.utils.ZIOUtils

private[datalakeingestion] object UpdateFormDataHandler extends DataLakeIngestionHandler[UpdateFormDataParams] {

  override def execute(
    params: UpdateFormDataParams,
    now: Option[Instant]
  )(
    using evendimClient: EvendimClient,
    webhookPayloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload]
  ): Task[Unit] = {
    ZIOUtils
      .traverseOption(params.lpIdOpt) { lpId =>
        for {
          orderFilter <- ZIO.attempt(OrderFilter(id = Some(StringHashFilter(eq = Some(lpId.idString)))))
          (existingFormValueIds, existingFormFieldValues) <- evendimClient.query(
            Query
              .queryOrder(filter = Some(orderFilter))(
                Order.formValues()(
                  FormQuestionData.id
                    ~ (FormQuestionData.namespace ~ FormQuestionData.alias ~ FormQuestionData.value).map {
                      case (namespace, alias, value) =>
                        FormFieldValue(
                          namespace = namespace,
                          alias = alias,
                          json = Some(parser.parse(value).getOrElse(Json.Null))
                        )
                    }
                )
              )
              .map(_.map(_.flatten.flatten).getOrElse(List.empty).unzip)
          )
          updateMutation <- ZIO.attempt(
            Mutation.updateOrder(
              UpdateOrderInput(
                filter = orderFilter,
                set = Some(
                  OrderPatch(
                    formFillingProgress = Some(params.formFillingProgress.toDouble),
                    missingRequiredFields = Some(params.missingRequiredFields),
                    missingRecommendedFields = Some(params.missingRecommendedFields),
                    formValues = Some(params.formFields.map { formField =>
                      FormQuestionDataRef(
                        namespace = Some(formField.namespace),
                        alias = Some(formField.alias),
                        value = Some(formField.json.map(_.noSpaces).getOrElse(""))
                      )
                    }.toList),
                    lastUpdatedAt = now.map(DataConversionUtils.toEvendimDateTime)
                  )
                ),
                remove = Some(
                  OrderPatch(
                    formValues = Some(existingFormValueIds.map { id =>
                      FormQuestionDataRef(id = Some(id))
                    })
                  )
                )
              )
            )(UpdateOrderPayload.numUids)
          )
          _ <- evendimClient.mutation(
            if (existingFormValueIds.nonEmpty) {
              val deleteMutation =
                Mutation.deleteFormQuestionData(filter = FormQuestionDataFilter(id = Some(existingFormValueIds)))(
                  DeleteFormQuestionDataPayload.numUids
                )
              updateMutation ~ deleteMutation
            } else {
              updateMutation
            }
          )
          _ <- sendWebhookEvent(
            lpId = lpId,
            oldFormFieldValues = existingFormFieldValues,
            newFormFieldValues = params.formFields.toList,
            webhookPayloadQueue = webhookPayloadQueue
          )
        } yield ()
      }
      .map(_ => ())
  }

  private def sendWebhookEvent(
    lpId: FundSubLpId,
    oldFormFieldValues: List[FormFieldValue],
    newFormFieldValues: List[FormFieldValue],
    webhookPayloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload]
  ): Task[Unit] = {
    val oldFormValuesMap = oldFormFieldValues.map { formValue =>
      (formValue.namespace, formValue.alias) -> formValue
    }.toMap
    val newFormValuesMap = newFormFieldValues.map { formValue =>
      (formValue.namespace, formValue.alias) -> formValue
    }.toMap
    val keySet = oldFormValuesMap.keySet ++ newFormValuesMap.keySet
    for {
      fsPublicModelOpt <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production)(
        _.getOptFundSubPublicModel(lpId.parent)
      )

      columnFields = (for {
        dashboardConfig <- fsPublicModelOpt.flatMap(_.dashboardConfig).toSeq
        formFieldConfig <- dashboardConfig.formFieldsConfigs
      } yield {
        val namespace = formFieldConfig.namespace
        val alias = formFieldConfig.alias
        (namespace, alias) -> formFieldConfig
      }).toMap

      _ <- ZIOUtils.foreachParN(4)(keySet) { case (namespace, alias) =>
        val key = (namespace, alias)
        ZIOUtils.traverseOption(columnFields.get(Some(namespace), alias)) { columnConfig =>
          val oldValue = oldFormValuesMap.get(key).flatMap(_.json).getOrElse(Json.Null)
          val newValue = newFormValuesMap.get(key).flatMap(_.json).getOrElse(Json.Null)
          ZIOUtils.when(oldValue != newValue) {
            webhookPayloadQueue.send(
              WebhookKafkaTopic.instance.message(
                lpId.parent,
                FundOrderFormDataColumnValueUpdatedPayload(
                  lpIdOpt = Some(lpId),
                  columnName = columnConfig.title,
                  namespace = namespace,
                  alias = alias,
                  oldValue = toWebhookCustomDataType(oldValue, columnConfig),
                  newValue = toWebhookCustomDataType(newValue, columnConfig)
                )
              )
            )
          }
        }
      }
    } yield ()
  }

  private def toWebhookCustomDataType(
    json: Json,
    fieldConfig: FormFieldConfig
  ): OrderCustomDataType = {
    fieldConfig.valueFormat match {
      case FormFieldValueFormat.Empty => OrderCustomDataType.Empty
      case _: TextFormat =>
        json.asString.fold(OrderCustomDataType.Empty) { str =>
          StringType(str)
        }
      case _: MultipleLineTextFormat =>
        json.asArray.map(_.flatMap(_.asString)).fold(OrderCustomDataType.Empty) { values =>
          StringArrayType(values.toSeq)
        }
      case _: IntegerFormat =>
        json.asNumber.flatMap(_.toInt).fold(OrderCustomDataType.Empty) { intValue =>
          IntegerType(intValue)
        }
      case _: FloatFormat =>
        json.asNumber.map(_.toFloat).fold(OrderCustomDataType.Empty) { floatValue =>
          FloatType(floatValue)
        }
      case _: DateFormat =>
        val dateTimeOpt = for {
          dateStr <- json.asString
          dateTime <- DateTimeUtils.parseDateString(dateStr, List(DateTimeFormatter.ISO_LOCAL_DATE))
        } yield dateTime
        dateTimeOpt.fold(OrderCustomDataType.Empty) { dateTime =>
          DateTimeType(Some(DateTimeUtils.fromLocalDateToInstant(dateTime)))
        }
      case format: MoneyFormat =>
        json.asNumber
          .map(_.toDouble)
          .fold(OrderCustomDataType.Empty) { value =>
            CurrencyType(currency = format.unit, amount = value)
          }
      case _: PercentageFormat =>
        json.asNumber
          .map(_.toFloat)
          .fold(OrderCustomDataType.Empty) { value =>
            FloatType(value)
          }
      case _: BooleanFormat =>
        json.asBoolean.fold(OrderCustomDataType.Empty) { boolValue =>
          BooleanType(boolValue)
        }
    }
  }

}
