// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datalakeingestion.handler

import anduin.evendim.client.EvendimClient
import anduin.fundsub.datalakeingestion.model.FundSubDataLakeIngestionParams
import anduin.kafka.KafkaAsyncExecutor
import fundsub.webhook.WebhookPayload
import java.time.Instant

import zio.Task

import anduin.id.fundsub.FundSubId

private[datalakeingestion] trait DataLakeIngestionHandler[P <: FundSubDataLakeIngestionParams] {

  def execute(
    params: P,
    now: Option[Instant] = None
  )(
    using evendimClient: EvendimClient,
    webhookPayloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload]
  ): Task[Unit]

}
