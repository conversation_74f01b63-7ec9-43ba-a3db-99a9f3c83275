// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datalakeingestion.handler

import anduin.dashboard.data
import anduin.dashboard.data.Documents.{FileInfo, OrderDocument}
import anduin.dashboard.data.RequiredDocInfo
import anduin.dashboard.query.CommonQueries
import anduin.evendim.client.EvendimClient
import anduin.fundsub.datalakeingestion.model.UpdateRequiredDocsParams
import anduin.fundsub.datalakeingestion.utils.DataConversionUtils
import anduin.kafka.KafkaAsyncExecutor
import caliban.client.Operations.{RootMutation, RootQuery}
import caliban.client.SelectionBuilder
import fundsub.webhook.WebhookPayload
import java.time.Instant
import scala.annotation.unused

import anduin.evendim.model.datalake.*
import com.anduin.stargazer.service.utils.ZIOUtils
import zio.{Task, ZIO}

import anduin.id.fundsub.FundSubId

object UpdateRequiredDocsHandler extends DataLakeIngestionHandler[UpdateRequiredDocsParams] {

  private case class LpDocDiffInfo(
    newLpDocs: List[OrderDocument],
    removedLpDocs: List[OrderDocument]
  )

  private def getCurrentRequiredDocs(lpIdStr: String): SelectionBuilder[RootQuery, List[RequiredDocInfo]] =
    Query
      .getOrder(id = lpIdStr)(
        Order.supportingDocs()(
          CommonQueries.selectRequiredDoc
        )
      )
      .map(_.getOrElse(List.empty))

  private def getNewFiles(fdbRequiredDocs: List[RequiredDocInfo], dgraphRequiredDocs: List[RequiredDocInfo]) = {
    val dgraphFileIds = dgraphRequiredDocs.flatMap(_.submittedDocs.map(_.file.id))
    val fdbFiles = fdbRequiredDocs.flatMap(_.submittedDocs.map(_.file))
    val fdbFileIds = fdbFiles.map(_.id)
    val newFileIds = fdbFileIds.toSet diff dgraphFileIds.toSet
    fdbFiles.filter { file =>
      newFileIds.contains(file.id)
    }
  }

  private def getLpDocsDiff(fdbRequiredDocs: List[RequiredDocInfo], dgraphRequiredDocs: List[RequiredDocInfo]) = {
    val dgraphLpDocs = dgraphRequiredDocs.flatMap(_.submittedDocs)
    val fdbDocs = fdbRequiredDocs.flatMap(_.submittedDocs)

    val newLpDocs = fdbDocs.filterNot { fdbDoc =>
      dgraphLpDocs.exists { dgraphDoc =>
        dgraphDoc.file.id == fdbDoc.file.id && dgraphDoc.docType == fdbDoc.docType
      }
    }

    val removedLpDocs = dgraphLpDocs.filterNot { dgraphDoc =>
      fdbDocs.exists { doc =>
        dgraphDoc.file.id == doc.file.id && dgraphDoc.docType == doc.docType
      }
    }

    LpDocDiffInfo(
      newLpDocs = newLpDocs,
      removedLpDocs = removedLpDocs
    )
  }

  private def addNewFiles(files: List[FileInfo], evendimClient: EvendimClient) = {
    ZIOUtils.when(files.nonEmpty) {
      val mutation = Mutation.addFile(
        files.map { file =>
          AddFileInput(
            id = file.id.idString,
            name = file.name,
            uploader = file.uploader.map { uploader =>
              UserRef(
                id = Option(uploader.userId.idString),
                email = Option(uploader.emailStr),
                firstName = Option(uploader.firstName),
                lastName = Option(uploader.lastName)
              )
            },
            uploadedAt = file.uploadedAt.map(DataConversionUtils.toEvendimDateTime)
          )
        },
        upsert = Option(true)
      )(
        AddFilePayload.file()(
          File.id ~ File.name
        )
      )
      evendimClient.mutation(mutation).unit
    }
  }

  private def addNewLpDocs(
    docs: List[OrderDocument],
    lpIdStr: String,
    evendimClient: EvendimClient
  ): Task[List[OrderDocument]] = {
    if (docs.nonEmpty) {
      val mutation = Mutation.addOrderDocument(
        docs.map { doc =>
          AddOrderDocumentInput(
            file = Option(
              FileRef(
                id = Option(doc.file.id.idString).filter(_.nonEmpty),
                name = Option(doc.file.name).filter(_.nonEmpty),
                uploader = Option(
                  UserRef(
                    id = doc.file.uploader.map(_.userId.idString),
                    email = doc.file.uploader.map(_.emailStr),
                    firstName = doc.file.uploader.map(_.firstName),
                    lastName = doc.file.uploader.map(_.lastName)
                  )
                ),
                uploadedAt = doc.file.uploadedAt.map(DataConversionUtils.toEvendimDateTime)
              )
            ),
            documentType = Option(DataConversionUtils.toDocType(doc.docType)),
            order = Option(OrderRef(id = Option(lpIdStr)))
          )
        }
      )(
        AddOrderDocumentPayload
          .orderDocument()(CommonQueries.selectLpDocument)
          .map(_.getOrElse(List.empty))
          .map(_.flatMap(_.flatten))
      )
      evendimClient.mutation(mutation).map(_.getOrElse(List.empty))
    } else {
      ZIO.succeed(List.empty)
    }
  }

  private def removeLpDocs(docs: List[OrderDocument], evendimClient: EvendimClient) = {
    ZIOUtils.when(docs.nonEmpty) {
      val mutation = Mutation.deleteOrderDocument(
        OrderDocumentFilter(
          id = Option(docs.map(_.id))
        )
      )(
        DeleteOrderDocumentPayload.numUids
      )
      evendimClient.mutation(mutation).unit
    }
  }

  private def removeDocsMutation(lpIdStr: String, docIds: List[String]): SelectionBuilder[RootMutation, Option[Int]] =
    (
      Mutation.updateOrder(
        UpdateOrderInput(
          filter = OrderFilter(id = Option(StringHashFilter(eq = Option(lpIdStr)))),
          remove = Option(OrderPatch(supportingDocs = Option(docIds.map(id => SupportingDocumentRef(id = Option(id))))))
        )
      )(
        UpdateOrderPayload.numUids
      ) ~ Mutation.deleteSupportingDocument(SupportingDocumentFilter(id = Option(docIds)))(
        DeleteSupportingDocumentPayload.numUids
      )
    ).map(_._2.flatten)

  private def addRequiredDocMutations(
    lpIdStr: String,
    requiredDocsToAdd: List[RequiredDocInfo],
    allLpDocs: List[OrderDocument]
  ): List[SelectionBuilder[RootMutation, Option[Int]]] = {
    requiredDocsToAdd.map { docInfo =>
      val newLpDocRef = docInfo.submittedDocs.flatMap { submittedDoc =>
        allLpDocs
          .find { doc =>
            doc.file.id == submittedDoc.file.id && doc.docType == submittedDoc.docType
          }
          .map { doc =>
            OrderDocumentRef(
              id = Option(doc.id)
            )
          }
      }

      Mutation
        .addSupportingDocument(
          List(
            AddSupportingDocumentInput(
              order = Option(OrderRef(id = Option(lpIdStr))),
              name = docInfo.name,
              markedAsNa = docInfo.markedAsNa,
              submitted = docInfo.submitted,
              submittedDocs = newLpDocRef
            )
          )
        )(
          AddSupportingDocumentPayload.numUids
        )
        .map(_.flatten)
    }
  }

  private def updateRequiredDocMutations(
    lpIdStr: String,
    requiredDocsToUpdate: List[RequiredDocInfo],
    dgraphRequiredDocs: List[RequiredDocInfo],
    allLpDocs: List[OrderDocument]
  ): List[SelectionBuilder[RootMutation, Option[Int]]] = {
    requiredDocsToUpdate.map { fdbRequiredDoc =>
      val dgraphSubmittedDocs = dgraphRequiredDocs
        .find(_.name.trim.equalsIgnoreCase(fdbRequiredDoc.name.trim))
        .map(_.submittedDocs)
        .getOrElse(List.empty)

      val removedLpDocs = dgraphSubmittedDocs.filterNot { doc =>
        fdbRequiredDoc.submittedDocs.exists { fdbSubmittedDoc =>
          fdbSubmittedDoc.docType == doc.docType && fdbSubmittedDoc.file.id == doc.file.id
        }
      }
      val addedLpDocs = fdbRequiredDoc.submittedDocs
        .filterNot { fdbSubmittedDoc =>
          dgraphSubmittedDocs.exists { dgraphSubmittedDoc =>
            dgraphSubmittedDoc.docType == fdbSubmittedDoc.docType && dgraphSubmittedDoc.file.id == fdbSubmittedDoc.file.id
          }
        }
        .map { fdbSubmittedDoc =>
          val id = allLpDocs
            .find { newDoc =>
              newDoc.file.id == fdbSubmittedDoc.file.id &&
              newDoc.docType == fdbSubmittedDoc.docType
            }
            .map(_.id)
            .getOrElse("")
          fdbSubmittedDoc.copy(id = id)
        }

      Mutation
        .updateSupportingDocument(
          UpdateSupportingDocumentInput(
            filter = SupportingDocumentFilter(id = Option(List(fdbRequiredDoc.id))),
            set = Option(
              SupportingDocumentPatch(
                order = Option(OrderRef(id = Option(lpIdStr))),
                name = Option(fdbRequiredDoc.name),
                markedAsNa = Option(fdbRequiredDoc.markedAsNa),
                submitted = Option(fdbRequiredDoc.submitted),
                submittedDocs = Option(addedLpDocs.map(doc => OrderDocumentRef(id = Option(doc.id))))
              )
            ),
            remove = Option(
              SupportingDocumentPatch(
                submittedDocs = Option(removedLpDocs.map(doc => OrderDocumentRef(id = Option(doc.id))))
              )
            )
          )
        )(
          UpdateSupportingDocumentPayload.numUids
        )
        .map(_.flatten)
    }
  }

  private def updateOrder(params: UpdateRequiredDocsParams, now: Option[Instant]) = {
    val lpIdStr = params.lpIdOpt.map(_.idString).getOrElse("")
    Mutation
      .updateOrder(
        UpdateOrderInput(
          filter = OrderFilter(id = Option(StringHashFilter(eq = Option(lpIdStr)))),
          set = Option(
            OrderPatch(
              lastUpdatedAt = now.map(DataConversionUtils.toEvendimDateTime(_))
            )
          )
        )
      )(
        UpdateOrderPayload.numUids
      )
      .map(_.flatten)
  }

  private def convertRequiredDoc(doc: anduin.fundsub.datalakeingestion.model.RequiredDocInfo): RequiredDocInfo = {
    RequiredDocInfo(
      id = "",
      name = doc.name,
      markedAsNa = doc.markedAsNa,
      submitted = doc.submitted,
      submittedDocs = doc.submittedDocs.flatMap { submittedDoc =>
        submittedDoc.file.map { file =>
          OrderDocument(
            id = submittedDoc.id,
            file = FileInfo(
              id = file.id,
              name = file.name,
              uploader = file.uploader.map { uploader =>
                anduin.dashboard.data.User(
                  userId = uploader.id,
                  emailStr = uploader.email,
                  firstName = uploader.firstName,
                  lastName = uploader.lastName
                )
              },
              uploadedAt = file.uploadedAt
            ),
            docType = submittedDoc.docType,
            lpIdOpt = Option(submittedDoc.lpId)
          )
        }
      }.toList
    )
  }

  private def sanitizeRequiredDoc(requiredDoc: data.RequiredDocInfo) = {
    requiredDoc.copy(
      id = "",
      submittedDocs = requiredDoc.submittedDocs
        .sortBy(_.file.name)
        .map(_.copy(id = ""))
    )
  }

  /*
    Because of the nested structure (RequiredDoc contains LpDocument and LpDocument contains File + docType) we'll have to do mutations in following order:
    1. Adding new Files first
    2. Adding LPDocuments
    3. Add or update RequiredDocs
    4. Remove RequiredDocs
    5. Remove LPDocuments
   */
  override def execute(
    params: UpdateRequiredDocsParams,
    now: Option[Instant]
  )(
    using evendimClient: EvendimClient,
    @unused webhookPayloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload]
  ): Task[Unit] = {
    val lpIdStr = params.lpIdOpt.map(_.idString).getOrElse("")

    for {
      dgraphRequiredDocs <- evendimClient.query(getCurrentRequiredDocs(lpIdStr))
      fdbRequiredDocs = params.docs.map(convertRequiredDoc(_))
      newFiles = getNewFiles(fdbRequiredDocs.toList, dgraphRequiredDocs)
      // 1.
      _ <- addNewFiles(newFiles, evendimClient)
      lpDocsDiff = getLpDocsDiff(fdbRequiredDocs.toList, dgraphRequiredDocs)
      // 2.
      addedLpDocs <- addNewLpDocs(
        lpDocsDiff.newLpDocs,
        lpIdStr,
        evendimClient
      )
      existingLpDocs = dgraphRequiredDocs.flatMap(_.submittedDocs)
      docsToRemove = dgraphRequiredDocs.filter { docInfo =>
        !fdbRequiredDocs.exists(_.name.equalsIgnoreCase(docInfo.name))
      }
      requiredDocsToAdd = fdbRequiredDocs
        .filter { docInfo =>
          !dgraphRequiredDocs
            .exists(_.name.equalsIgnoreCase(docInfo.name))
        }
      requiredDocsToUpdate = fdbRequiredDocs
        .filter { docInfo =>
          val existingDocOpt = dgraphRequiredDocs.find(_.name.equalsIgnoreCase(docInfo.name))
          existingDocOpt.exists { existingLpDoc =>
            sanitizeRequiredDoc(existingLpDoc) != sanitizeRequiredDoc(docInfo)
          }
        }
        .map { docInfo =>
          val existingDocOpt = dgraphRequiredDocs.find(_.name.equalsIgnoreCase(docInfo.name))
          docInfo.copy(id = existingDocOpt.map(_.id).getOrElse(""))
        }
      // 3.
      _ <- ZIOUtils.when(docsToRemove.nonEmpty) {
        evendimClient
          .mutation(
            removeDocsMutation(lpIdStr, docsToRemove.map(_.id))
          )
          .unit
      }
      // 4.
      _ <- ZIO.foreach(
        addRequiredDocMutations(
          lpIdStr,
          requiredDocsToAdd.toList,
          addedLpDocs ++ existingLpDocs
        ) ++ updateRequiredDocMutations(
          lpIdStr,
          requiredDocsToUpdate.toList,
          dgraphRequiredDocs,
          addedLpDocs ++ existingLpDocs
        )
      ) { mutation =>
        evendimClient.mutation(mutation)
      }
      // 5.
      _ <- removeLpDocs(lpDocsDiff.removedLpDocs, evendimClient)
      // update Order.lastUpdatedAt
      _ <- evendimClient.mutation(updateOrder(params, now))
    } yield ()
  }

}
