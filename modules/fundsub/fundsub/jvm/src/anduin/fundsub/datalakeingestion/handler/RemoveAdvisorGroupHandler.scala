// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datalakeingestion.handler

import java.time.Instant
import scala.annotation.unused

import fundsub.webhook.WebhookPayload
import zio.Task

import anduin.evendim.client.EvendimClient
import anduin.evendim.model.datalake.*
import anduin.fundsub.datalakeingestion.model.RemoveAdvisorGroupParams
import anduin.id.fundsub.FundSubId
import anduin.kafka.KafkaAsyncExecutor

object RemoveAdvisorGroupHandler extends DataLakeIngestionHandler[RemoveAdvisorGroupParams] {

  private def unlinkAdvisorGroupMutation(params: RemoveAdvisorGroupParams) = {
    val fundIdStr = params.id.parent.idString
    val fundSubFilter = FundSubscriptionFilter(id = Option(StringHashFilter(eq = Option(fundIdStr))))
    Mutation.updateFundSubscription(
      UpdateFundSubscriptionInput(
        filter = fundSubFilter,
        remove = Option(
          FundSubscriptionPatch(
            advisorEntities = Option(List(AdvisorEntityRef(id = Option(params.id.idString))))
          )
        )
      )
    )(
      UpdateFundSubscriptionPayload.numUids
    )
  }

  private def removeAdvisorGroupMutation(params: RemoveAdvisorGroupParams) = {
    Mutation.deleteAdvisorEntity(AdvisorEntityFilter(id = Option(StringHashFilter(eq = Option(params.id.idString)))))(
      DeleteAdvisorEntityPayload.numUids
    )
  }

  override def execute(
    params: RemoveAdvisorGroupParams,
    now: Option[Instant]
  )(
    using evendimClient: EvendimClient,
    @unused webhookPayloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload]
  ): Task[Unit] = {
    evendimClient.mutation(unlinkAdvisorGroupMutation(params) ~ removeAdvisorGroupMutation(params)).map(_ => ())
  }

}
