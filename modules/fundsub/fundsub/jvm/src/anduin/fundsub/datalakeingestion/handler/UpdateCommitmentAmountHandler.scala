// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datalakeingestion.handler

import anduin.evendim.client.EvendimClient
import anduin.fundsub.datalakeingestion.model.UpdateCommitmentAmountParams
import anduin.fundsub.datalakeingestion.utils.DataConversionUtils
import anduin.kafka.KafkaAsyncExecutor
import caliban.client.Operations.RootMutation
import caliban.client.SelectionBuilder
import fundsub.webhook.WebhookPayload
import java.time.Instant
import scala.annotation.unused

import anduin.evendim.model.datalake.*
import zio.{Task, ZIO}

import anduin.id.fundsub.FundSubId

object UpdateCommitmentAmountHandler extends DataLakeIngestionHandler[UpdateCommitmentAmountParams] {

  private def mutation(
    params: UpdateCommitmentAmountParams,
    now: Option[Instant]
  ): SelectionBuilder[RootMutation, Option[Int]] = {
    val lpIdStrOpt = params.lpIdOpt.map(_.idString)
    Mutation
      .updateOrder(
        UpdateOrderInput(
          filter = OrderFilter(id = Option(StringHashFilter(eq = lpIdStrOpt))),
          set = Option(
            OrderPatch(
              lastUpdatedAt = now.map(DataConversionUtils.toEvendimDateTime),
              estimatedCommitmentAmount = params.expectedCommitment,
              submittedCommitmentAmount = params.submittedCommitment,
              acceptedCommitmentAmount = params.acceptedCommitment
            )
          )
        )
      )(
        UpdateOrderPayload.numUids
      )
      .map(_.flatten)
  }

  private def updateSubFundCommitmentAmounts(
    params: UpdateCommitmentAmountParams
  )(
    using evendimClient: EvendimClient
  ): Task[Unit] = {
    val lpIdStr = params.lpIdOpt.map(_.idString).getOrElse("")
    for {
      currentAmountIds <- evendimClient.query(
        Query
          .getOrder(lpIdStr)(
            Order
              .commitmentAmounts()(
                SubFundCommitmentAmount.id
              )
              .map(_.getOrElse(List.empty))
          )
          .map(_.getOrElse(List.empty))
      )
      _ <- ZIO.when(currentAmountIds.nonEmpty || params.commitmentAmounts.nonEmpty) {
        evendimClient.mutation(
          Mutation.updateOrder(
            UpdateOrderInput(
              filter = OrderFilter(id = Option(StringHashFilter(eq = Option(lpIdStr)))),
              set = Option(
                OrderPatch(
                  commitmentAmounts = Option(
                    params.commitmentAmounts.toList.map { amount =>
                      SubFundCommitmentAmountRef(
                        subFund = Option(SubFundRef(id = Option(amount.id.idString))),
                        estimatedCommitmentAmount = amount.expectedCommitment,
                        submittedCommitmentAmount = amount.submittedCommitment,
                        acceptedCommitmentAmount = amount.acceptedCommitment
                      )
                    }
                  )
                )
              ),
              remove = Option(
                OrderPatch(
                  commitmentAmounts = Option(
                    currentAmountIds.map { id =>
                      SubFundCommitmentAmountRef(
                        id = Option(id)
                      )
                    }
                  )
                )
              )
            )
          )(
            UpdateOrderPayload.numUids
          )
        )
      }
    } yield ()
  }

  override def execute(
    params: UpdateCommitmentAmountParams,
    now: Option[Instant]
  )(
    using evendimClient: EvendimClient,
    @unused webhookPayloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload]
  ): Task[Unit] = {
    for {
      _ <- evendimClient.mutation(mutation(params, now))
      _ <- updateSubFundCommitmentAmounts(params)
    } yield ()
  }

}
