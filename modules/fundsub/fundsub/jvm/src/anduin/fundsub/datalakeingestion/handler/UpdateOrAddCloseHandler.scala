// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datalakeingestion.handler

import anduin.evendim.client.EvendimClient
import anduin.fundsub.datalakeingestion.model.UpdateOrAddCloseParams
import anduin.fundsub.datalakeingestion.utils.DataConversionUtils
import anduin.kafka.KafkaAsyncExecutor
import fundsub.webhook.WebhookPayload
import java.time.Instant
import scala.annotation.unused

import anduin.evendim.model.datalake.*
import zio.Task

import anduin.id.fundsub.FundSubId

object UpdateOrAddCloseHandler extends DataLakeIngestionHandler[UpdateOrAddCloseParams] {

  private def mutation(params: UpdateOrAddCloseParams) = {
    Mutation.addClose(
      List(
        AddCloseInput(
          id = params.id.idString,
          fund = Option(FundSubscriptionRef(id = Option(params.id.parent.idString))),
          name = params.name,
          customCloseId = Option(params.customCloseId),
          targetDate = params.targetClosingDate.map(DataConversionUtils.toEvendimDateTime)
        )
      ),
      upsert = Option(true)
    )(
      AddClosePayload.numUids
    )
  }

  override def execute(
    params: UpdateOrAddCloseParams,
    now: Option[Instant] = None
  )(
    using evendimClient: EvendimClient,
    @unused webhookPayloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload]
  ): Task[Unit] = {
    val _ = now
    evendimClient
      .mutation(mutation(params))
      .map(_ => ())
  }

}
