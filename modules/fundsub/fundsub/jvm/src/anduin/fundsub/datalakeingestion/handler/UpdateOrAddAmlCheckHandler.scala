// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datalakeingestion.handler

import java.time.Instant

import fundsub.webhook.WebhookPayload
import zio.Task

import anduin.evendim.client.EvendimClient
import anduin.evendim.model.datalake.*
import anduin.fundsub.datalakeingestion.model.UpdateOrAddAmlCheckParams
import anduin.fundsub.datalakeingestion.utils.DataConversionUtils
import anduin.id.fundsub.FundSubId
import anduin.kafka.KafkaAsyncExecutor

object UpdateOrAddAmlCheckHandler extends DataLakeIngestionHandler[UpdateOrAddAmlCheckParams] {

  private def updateOrAddAmlCheck(params: UpdateOrAddAmlCheckParams) =
    Mutation.addAmlCheck(
      input = params.checks.toList.map { check =>
        AddAmlCheckInput(
          check.id.idString,
          DataConversionUtils.toDataLakeAmlCheckStatus(check.status)
        )
      },
      upsert = Some(true)
    )(
      AddAmlCheckPayload.numUids
    )

  private def updateOrder(params: UpdateOrAddAmlCheckParams, now: Option[Instant]) = {
    val lpIdStrOpt = params.lpIdOpt.map(_.idString)
    val amlCheckRefs = params.checks.toList.map { check => AmlCheckRef(id = Some(check.id.idString)) }
    Mutation.updateOrder(
      UpdateOrderInput(
        filter = OrderFilter(id = Some(StringHashFilter(eq = lpIdStrOpt))),
        set = Some(
          OrderPatch(
            amlCheck = Some(amlCheckRefs),
            lastUpdatedAt = now.map(DataConversionUtils.toEvendimDateTime)
          )
        )
      )
    )(
      UpdateOrderPayload.numUids
    )
  }

  override def execute(
    params: UpdateOrAddAmlCheckParams,
    now: Option[Instant]
  )(
    using evendimClient: EvendimClient,
    webhookPayloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload]
  ): Task[Unit] = {
    val mutations = updateOrAddAmlCheck(params) ~ updateOrder(params, now)
    evendimClient
      .mutation(mutations)
      .map(_ => ())
  }

}
