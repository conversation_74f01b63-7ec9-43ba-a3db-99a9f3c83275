// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datalakeingestion.handler

import anduin.evendim.client.EvendimClient
import anduin.fundsub.datalakeingestion.model.UpdateCollaboratorsParams
import anduin.fundsub.datalakeingestion.utils.DataConversionUtils
import anduin.kafka.KafkaAsyncExecutor
import fundsub.webhook.{ContactType, FundOrderContactsAddedPayload, WebhookPayload}
import java.time.Instant

import io.github.arainko.ducktape.*

import anduin.evendim.model.datalake.*
import com.anduin.stargazer.service.utils.ZIOUtils
import zio.{Task, ZIO}

import anduin.fundsub.webhook.WebhookKafkaTopic
import anduin.id.fundsub.FundSubId

object UpdateCollaboratorsHandler extends DataLakeIngestionHandler[UpdateCollaboratorsParams] {

  override def execute(
    params: UpdateCollaboratorsParams,
    now: Option[Instant] = None
  )(
    using evendimClient: EvendimClient,
    webhookPayloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload]
  ): Task[Unit] = {
    params.lpIdOpt.fold(ZIO.unit) { lpId =>
      ZIOUtils.when(params.add.nonEmpty || params.remove.nonEmpty) {
        val addMembers = params.add.map(DataConversionUtils.toUserRef).toList
        val removedMembers = params.remove.map(DataConversionUtils.toUserRef).toList
        for {
          _ <- evendimClient.mutation(
            Mutation
              .updateOrder(
                UpdateOrderInput(
                  filter = OrderFilter(id = Some(StringHashFilter(eq = Some(lpId.idString)))),
                  set = Some(
                    OrderPatch(
                      contacts = Some(addMembers),
                      pendingContacts = Some(addMembers),
                      lastUpdatedAt = now.map(DataConversionUtils.toEvendimDateTime)
                    )
                  ),
                  remove = Some(
                    OrderPatch(
                      contacts = Some(removedMembers),
                      pendingContacts = Some(removedMembers)
                    )
                  )
                )
              )(
                UpdateOrderPayload.numUids
              )
              .map(_.flatten)
          )
          _ <- webhookPayloadQueue.send(
            WebhookKafkaTopic.instance.message(
              lpId.parent,
              FundOrderContactsAddedPayload(
                Some(lpId),
                userInfo = params.add.toList.map(_.to[fundsub.webhook.UserInfo]),
                contactType = ContactType.Collaborator
              )
            )
          )
        } yield ()
      }
    }
  }

}
