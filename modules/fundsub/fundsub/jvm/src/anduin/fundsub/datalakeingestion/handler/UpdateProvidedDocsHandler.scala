// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datalakeingestion.handler

import anduin.evendim.client.EvendimClient
import anduin.fundsub.datalakeingestion.model.UpdateProvidedDocsParams
import anduin.fundsub.datalakeingestion.utils.DataConversionUtils
import anduin.kafka.KafkaAsyncExecutor
import fundsub.webhook.WebhookPayload
import zio.{Task, ZIO}
import java.time.Instant
import scala.annotation.unused

import anduin.evendim.model.datalake.*
import anduin.id.fundsub.FundSubId

object UpdateProvidedDocsHandler extends DataLakeIngestionHandler[UpdateProvidedDocsParams] {

  override def execute(
    params: UpdateProvidedDocsParams,
    now: Option[Instant]
  )(
    using evendimClient: EvendimClient,
    @unused webhookPayloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload]
  ): Task[Unit] = {
    val lpIdStr = params.lpIdOpt.map(_.idString).getOrElse("")
    val docs = params.docs.map(_.trim)
    for {
      currentProvidedDocs <- evendimClient
        .query(
          Query
            .getOrder(id = lpIdStr)(
              Order.amlKycDocsProvidedOffline
            )
            .map(_.map(_.getOrElse(List.empty[String])))
        )
        .map(_.getOrElse(List.empty[String]))
      removedDocs = currentProvidedDocs.filterNot { currentDoc =>
        docs.exists(_.equalsIgnoreCase(currentDoc))
      }
      addedDocs = docs.filterNot { addedDoc =>
        removedDocs.exists(_.equalsIgnoreCase(addedDoc))
      }
      _ <- ZIO.logInfo(s"current docs ${currentProvidedDocs}")
      _ <- ZIO.logInfo(s"params docs ${docs}")
      _ <- ZIO.logInfo(s"remove docs $removedDocs")
      _ <- ZIO.logInfo(s"add docs $addedDocs")
      _ <- evendimClient.mutation(
        Mutation
          .updateOrder(
            UpdateOrderInput(
              filter = OrderFilter(id = Some(StringHashFilter(eq = params.lpIdOpt.map(_.idString)))),
              set = Some(
                OrderPatch(
                  amlKycDocsProvidedOffline = Option(addedDocs.toList),
                  lastUpdatedAt = now.map(DataConversionUtils.toEvendimDateTime)
                )
              ),
              remove = Some(
                OrderPatch(
                  amlKycDocsProvidedOffline = Option(removedDocs)
                )
              )
            )
          )(
            UpdateOrderPayload.numUids
          )
      )
    } yield ()
  }

}
