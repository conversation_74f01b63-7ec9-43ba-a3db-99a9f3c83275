// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datalakeingestion.handler

import java.time.Instant
import scala.annotation.unused

import caliban.client.Operations.{RootMutation, RootQuery}
import caliban.client.SelectionBuilder
import fundsub.webhook.WebhookPayload
import zio.{Task, ZIO}

import anduin.dashboard.data.Documents.AmlKycReview
import anduin.dashboard.query.CommonQueries
import anduin.evendim.client.EvendimClient
import anduin.evendim.model.datalake.*
import anduin.fundsub.datalakeingestion.model.{AmlKycReviewInfo, UpdateOrAddAmlKycReviewParams}
import anduin.fundsub.datalakeingestion.utils.DataConversionUtils
import anduin.id.fundsub.{FundSubLpId, FundSubId}
import anduin.kafka.KafkaAsyncExecutor
import com.anduin.stargazer.service.utils.ZIOUtils

private[datalakeingestion] object UpdateOrAddAmlKycReviewHandler
    extends DataLakeIngestionHandler[UpdateOrAddAmlKycReviewParams] {

  private def getCurrentAmlKycReviewData(
    lpIdStr: String
  ): SelectionBuilder[RootQuery, List[AmlKycReview]] = {

    Query
      .getOrder(id = lpIdStr)(
        Order.amlKycReviews()(
          CommonQueries.selectAmlKycReview
        )
      )
      .map(_.getOrElse(List.empty))
  }

  private def addAmlKycReviewMutation(
    lpIdOpt: Option[FundSubLpId],
    reviewData: List[AmlKycReviewInfo]
  ): List[SelectionBuilder[RootMutation, Option[Int]]] =
    reviewData.map { reviewInfo =>
      scribe.info(s"Review Info $reviewInfo")
      Mutation
        .addAmlKycReview(
          List(
            AddAmlKycReviewInput(
              docName = reviewInfo.docType,
              order = Some(OrderRef(id = lpIdOpt.map(_.idString))),
              status = Option(DataConversionUtils.toEvenDimReviewStatus(reviewInfo.status)),
              updatedBy = reviewInfo.updatedBy.map(DataConversionUtils.toUserRef),
              updatedAt = reviewInfo.updatedAt.map(DataConversionUtils.toEvendimDateTime)
            )
          )
        )(AddAmlKycReviewPayload.numUids)
        .map(_.flatten)
    }

  private def updateAmlKycReviewMutation(
    lpIdOpt: Option[FundSubLpId],
    reviewData: List[AmlKycReviewInfo]
  ): List[SelectionBuilder[RootMutation, Option[Int]]] =
    reviewData.map { reviewInfo =>
      scribe.info(s"Review Info $reviewInfo")
      Mutation
        .updateAmlKycReview(
          UpdateAmlKycReviewInput(
            filter = AmlKycReviewFilter(id = Option(List(reviewInfo.id))),
            set = Option(
              AmlKycReviewPatch(
                docName = Option(reviewInfo.docType),
                order = Some(OrderRef(id = lpIdOpt.map(_.idString))),
                status = Option(DataConversionUtils.toEvenDimReviewStatus(reviewInfo.status)),
                updatedBy = reviewInfo.updatedBy.map(DataConversionUtils.toUserRef),
                updatedAt = reviewInfo.updatedAt.map(DataConversionUtils.toEvendimDateTime)
              )
            )
          )
        )(UpdateAmlKycReviewPayload.numUids)
        .map(_.flatten)
    }

  private def removeAmlKycReviewMutation(
    lpIdStr: String,
    docReviewIds: List[String]
  ): SelectionBuilder[RootMutation, Option[Int]] =
    (
      Mutation.updateOrder(
        UpdateOrderInput(
          filter = OrderFilter(id = Option(StringHashFilter(eq = Option(lpIdStr)))),
          remove = Option(OrderPatch(amlKycReviews = Option(docReviewIds.map(id => AmlKycReviewRef(id = Option(id))))))
        )
      )(
        UpdateOrderPayload.numUids
      ) ~ Mutation.deleteAmlKycReview(AmlKycReviewFilter(id = Option(docReviewIds)))(
        DeleteAmlKycReviewPayload.numUids
      )
    ).map(_._2.flatten)

  override def execute(
    params: UpdateOrAddAmlKycReviewParams,
    @unused now: Option[Instant]
  )(
    using evendimClient: EvendimClient,
    webhookPayloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload]
  ): Task[Unit] = {
    val lpIdStr = params.lpIdOpt.map(_.idString).getOrElse("")

    for {
      dgraphReviewData <- evendimClient.query(getCurrentAmlKycReviewData(lpIdStr))
      reviewDataToAdd = params.reviews.filter { reviewInfo =>
        !dgraphReviewData
          .exists(_.docType.trim.equalsIgnoreCase(reviewInfo.docType.trim))
      }.toList
      reviewDataToUpdate = params.reviews.collect {
        case reviewInfo: AmlKycReviewInfo
            if dgraphReviewData.exists(_.docType.trim.equalsIgnoreCase(reviewInfo.docType.trim)) =>
          val dgraphReviewInfo =
            dgraphReviewData.find(_.docType.trim.equalsIgnoreCase(reviewInfo.docType.trim))
          reviewInfo.copy(id = dgraphReviewInfo.map(_.id).getOrElse(reviewInfo.id))
      }.toList

      reviewIdsToDelete = dgraphReviewData.map(_.id).filterNot { id =>
        reviewDataToUpdate.map(_.id).contains(id)
      }
      _ <- ZIO.foreach(
        addAmlKycReviewMutation(
          lpIdOpt = params.lpIdOpt,
          reviewData = reviewDataToAdd
        ) ++ updateAmlKycReviewMutation(
          lpIdOpt = params.lpIdOpt,
          reviewData = reviewDataToUpdate
        )
      ) { mutation =>
        evendimClient.mutation(mutation)
      }
      _ <- ZIOUtils.when(reviewIdsToDelete.nonEmpty) {
        evendimClient
          .mutation(
            removeAmlKycReviewMutation(lpIdStr, reviewIdsToDelete)
          )
          .unit
      }

    } yield ()
  }

}
