// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datalakeingestion.handler

import java.time.Instant
import scala.annotation.unused

import fundsub.webhook.WebhookPayload
import zio.Task

import anduin.evendim.client.EvendimClient
import anduin.evendim.model.datalake.*
import anduin.fundsub.datalakeingestion.model.UpdateOrAddInvestorGroupParams
import anduin.id.fundsub.FundSubId
import anduin.kafka.KafkaAsyncExecutor

object UpdateOrAddInvestorGroupHandler extends DataLakeIngestionHandler[UpdateOrAddInvestorGroupParams] {

  private def mutation(params: UpdateOrAddInvestorGroupParams) = {
    Mutation.addInvestorGroup(
      List(
        AddInvestorGroupInput(
          id = params.id.idString,
          name = params.name,
          fund = Option(FundSubscriptionRef(id = Option(params.id.parent.idString)))
        )
      ),
      upsert = Option(true)
    )(
      AddInvestorGroupPayload.numUids
    )
  }

  override def execute(
    params: UpdateOrAddInvestorGroupParams,
    now: Option[Instant] = None
  )(
    using evendimClient: EvendimClient,
    @unused webhookPayloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload]
  ): Task[Unit] = {
    val _ = now
    evendimClient
      .mutation(mutation(params))
      .map(_ => ())
  }

}
