// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datalakeingestion.handler

import java.time.Instant
import scala.annotation.unused

import fundsub.webhook.WebhookPayload
import zio.Task

import anduin.evendim.client.EvendimClient
import anduin.evendim.model.datalake.*
import anduin.fundsub.datalakeingestion.model.UpdateOrAddAdvisorGroupParams
import anduin.id.fundsub.FundSubId
import anduin.kafka.KafkaAsyncExecutor

object UpdateOrAddAdvisorGroupHandler extends DataLakeIngestionHandler[UpdateOrAddAdvisorGroupParams] {

  private def mutation(params: UpdateOrAddAdvisorGroupParams) = {
    Mutation.addAdvisorEntity(
      List(
        AddAdvisorEntityInput(
          id = params.id.idString,
          name = params.name,
          fund = Option(FundSubscriptionRef(id = Option(params.id.parent.idString)))
        )
      ),
      upsert = Option(true)
    )(
      AddAdvisorEntityPayload.numUids
    )
  }

  override def execute(
    params: UpdateOrAddAdvisorGroupParams,
    now: Option[Instant] = None
  )(
    using evendimClient: EvendimClient,
    @unused webhookPayloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload]
  ): Task[Unit] = {
    val _ = now
    evendimClient
      .mutation(mutation(params))
      .map(_ => ())
  }

}
