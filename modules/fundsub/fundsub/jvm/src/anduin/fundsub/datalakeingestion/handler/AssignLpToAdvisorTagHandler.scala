// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.fundsub.datalakeingestion.handler

import java.time.Instant
import scala.annotation.unused

import fundsub.webhook.WebhookPayload
import zio.Task

import anduin.evendim.client.EvendimClient
import anduin.evendim.model.datalake.*
import anduin.fundsub.datalakeingestion.model.AssignLpToAdvisorTagParams
import anduin.id.fundsub.FundSubId
import anduin.kafka.KafkaAsyncExecutor

object AssignLpToAdvisorTagHandler extends DataLakeIngestionHandler[AssignLpToAdvisorTagParams] {

  private def mutation(params: AssignLpToAdvisorTagParams) = {
    val orderFilter = OrderFilter(id = Option(StringHashFilter(eq = Option(params.lpId.idString))))
    Mutation.updateOrder(
      UpdateOrderInput(
        filter = orderFilter,
        set = Option(
          OrderPatch(
            advisorTags = Option(
              List(AdvisorTagRef(id = Option(params.advisorTagId.idString)))
            )
          )
        )
      )
    )(
      UpdateOrderPayload.numUids
    )
  }

  override def execute(
    params: AssignLpToAdvisorTagParams,
    now: Option[Instant] = None
  )(
    using evendimClient: EvendimClient,
    @unused webhookPayloadQueue: KafkaAsyncExecutor[FundSubId, WebhookPayload]
  ): Task[Unit] = {
    val _ = now
    evendimClient
      .mutation(mutation(params))
      .map(_ => ())
  }

}
