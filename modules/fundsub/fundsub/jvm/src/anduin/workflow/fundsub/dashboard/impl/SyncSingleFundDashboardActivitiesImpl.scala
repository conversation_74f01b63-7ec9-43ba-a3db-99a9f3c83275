// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dashboard.impl

import zio.{Task, ZIO}

import anduin.fdb.record.FDBRecordDatabase
import anduin.fundsub.dashboard.FundSubDashboardAdminService
import anduin.fundsub.endpoint.dashboard.v2.{
  CheckDashboardDataParams,
  CheckDashboardDataResponse,
  SyncDashboardFundInfoParams,
  SyncDashboardOrderParams
}
import anduin.fundsub.models.FundSubModelStoreOperations
import anduin.id.fundsub.{FundSubAdminRestrictedId, FundSubId}
import anduin.model.common.user.UserId
import anduin.portaluser.ExecutiveAdmin
import anduin.protobuf.fundsub.models.FundSubPublicModel.FundType
import anduin.workflow.TemporalWorkflowService
import anduin.workflow.fundsub.dashboard.{FundIdParams, FundSyncResponse, SyncSingleFundDashboardActivities}
import com.anduin.stargazer.service.actionlogger.{
  ActionEventDailyMismatchDgraphFundDataParams,
  ActionEventDailyMismatchDgraphOrderDataParams,
  ActionLoggerService,
  LogActionEventsParams
}
import com.anduin.stargazer.service.utils.ZIOUtils

final case class SyncSingleFundDashboardActivitiesImpl(
  fundSubDashboardAdminService: FundSubDashboardAdminService,
  executiveAdmin: ExecutiveAdmin,
  actionLoggerService: ActionLoggerService
)(
  using val temporalWorkflowService: TemporalWorkflowService
) extends SyncSingleFundDashboardActivities {

  private def logSyncFundEvent(
    fundId: FundSubId,
    fundName: String,
    fundType: FundType,
    dashboardDiff: CheckDashboardDataResponse
  ) = {
    for {
      admin <- executiveAdmin.userId
      _ <- actionLoggerService.batchLogNewActionEvent(
        admin,
        LogActionEventsParams(
          Seq(
            ActionEventDailyMismatchDgraphFundDataParams(
              fundSubId = fundId,
              fundName = fundName,
              fundType = fundType.name,
              inconsistentFields = dashboardDiff.checkFundInfo.inconsistentFields,
              missingOrders = dashboardDiff.missingOrders.size,
              redundantOrders = dashboardDiff.redundantOrders.size,
              inconsistentOrders = dashboardDiff.inconsistentOrders.size
            )
          ) ++ dashboardDiff.inconsistentOrders.map { order =>
            ActionEventDailyMismatchDgraphOrderDataParams(
              fundSubId = fundId,
              fundSubLpId = order.orderInfo.lpId,
              fundName = fundName,
              fundType = fundType.name,
              inconsistentFields = order.inconsistentFields
            )
          }
        ),
        httpRequestContextOpt = None
      )
    } yield ()
  }

  private def syncDashboardDataSpecificFund(fundSubId: FundSubId, actor: UserId): Task[FundSyncResponse] = {
    val task = for {
      checkDashboardDataResp <- fundSubDashboardAdminService.checkFundData(
        CheckDashboardDataParams(fundSubId),
        actor
      )
      fsPublicModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) {
        _.getFundSubPublicModel(fundSubId)
      }
      _ <- ZIOUtils.when(checkDashboardDataResp.checkFundInfo.isMismmatched) {
        for {
          _ <- fundSubDashboardAdminService.syncFundInfo(SyncDashboardFundInfoParams(fundSubId), actor)
          _ <- logSyncFundEvent(
            fundSubId,
            fsPublicModel.fundName,
            fsPublicModel.fundType,
            checkDashboardDataResp
          )
        } yield ()
      }
      lpsToSync <- ZIO.succeed {
        checkDashboardDataResp.missingOrders.map(_.lpId) ++
          checkDashboardDataResp.inconsistentOrders.map(_.orderInfo.lpId) ++
          checkDashboardDataResp.redundantOrders.map(_.lpId)
      }
      _ <- ZIO.foreach(lpsToSync) { lpId =>
        fundSubDashboardAdminService
          .syncFundOrder(params = SyncDashboardOrderParams(lpId), actor)
      }
      _ <- actionLoggerService.batchLogNewActionEvent(
        actor,
        LogActionEventsParams(
          checkDashboardDataResp.inconsistentOrders.map { order =>
            ActionEventDailyMismatchDgraphOrderDataParams(
              fundSubId = fundSubId,
              fundSubLpId = order.orderInfo.lpId,
              fundName = fsPublicModel.fundName,
              fundType = fsPublicModel.fundType.name,
              inconsistentFields = order.inconsistentFields
            )
          }
        ),
        httpRequestContextOpt = None
      )
    } yield FundSyncResponse(
      detectedDataMismatch = checkDashboardDataResp.checkFundInfo.isMismmatched || lpsToSync.nonEmpty,
      success = true
    )
    task.catchNonFatalOrDie { ex =>
      ZIO.attempt {
        FundSyncResponse(
          detectedDataMismatch = true,
          success = false,
          error = s"Failed to sync data for fund ${fundSubId.idString}, ${ex.getMessage}"
        )
      }
    }
  }

  private def syncFundDashboardIfNecessary(fundId: FundSubId): Task[FundSyncResponse] = {
    val task = for {
      _ <- ZIO.attempt(scribe.info(s"sync fund ${fundId.idString}"))
      executiveAdminUserId <- executiveAdmin.userId
      fsResModel <- FDBRecordDatabase.transact(FundSubModelStoreOperations.Production) {
        _.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(fundId))
      }
      responseOpt <- ZIOUtils.traverseOption(fsResModel.dashboardId) { _ =>
        syncDashboardDataSpecificFund(fundId, executiveAdminUserId)
      }
    } yield responseOpt.getOrElse(
      FundSyncResponse()
    )

    task
      .catchNonFatalOrDie { ex =>
        ZIO.succeed {
          FundSyncResponse(
            detectedDataMismatch = true,
            success = false,
            error = ex.getMessage
          )
        }
      }
  }

  override def syncSingleFundDashboad(params: FundIdParams): FundSyncResponse = {
    temporalWorkflowService.executeTask(
      syncFundDashboardIfNecessary(params.fundId),
      "SyncSingleFundDashboardActivities#syncSingleFundDashboad"
    )
  }

}
