// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dataimport

import zio.temporal.{activityInterface, activityMethod}

@activityInterface
trait FundSubDataImportActivity {

  @activityMethod
  def startSingleDataImport(param: StartSingleDataImportParam): StartSingleDataImportResponse

  @activityMethod
  def processDataImport(param: ProcessDataImportParam): ProcessDataImportResponse

  @activityMethod
  def markItemAsFailed(param: MarkSingleDataImportAsFailedParam): MarkSingleDataImportAsFailResponse

}
