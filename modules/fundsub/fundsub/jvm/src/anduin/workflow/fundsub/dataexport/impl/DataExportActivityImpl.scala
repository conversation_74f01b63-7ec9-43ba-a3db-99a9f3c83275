// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dataexport.impl

import java.time.Instant

import zio.ZIO

import anduin.fdb.record.{FDBRecordDatabase, DefaultCluster}
import anduin.fdb.record.model.{RecordIO, RecordTask}
import anduin.forms.service.FormService
import anduin.forms.storage.{DataTemplateVersionStoreOperations, FormTemplateMappingContentStoreOperations}
import anduin.forms.utils.FormTemplateMappingUtils
import anduin.fundsub.LpFormDataOperations
import anduin.fundsub.batchaction.dataexport.FundSubDataExportOperations
import anduin.workflow.TemporalWorkflowService
import anduin.workflow.fundsub.dataexport.*
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.utils.ZIOUtils

case class DataExportActivityImpl(
  backendConfig: GondorBackendConfig
)(
  using val formService: FormService,
  val temporalWorkflowService: TemporalWorkflowService
) extends FundSubDataExportActivity {

  private def exportData(exportModel: FundSubDataExportModel, param: ProcessDataExportParam) = {
    param.mappingVersionIdOpt
      .map { mappingVersionId =>
        val formVersionId = mappingVersionId.parent.parent
        for {
          template <- FDBRecordDatabase
            .transact(DataTemplateVersionStoreOperations.Production)(_.get(exportModel.templateVersionId))
            .map(_.template)
          mappingItems <- FDBRecordDatabase
            .transact(FormTemplateMappingContentStoreOperations.Production)(_.get(mappingVersionId))
            .map(_.mappingItems)
          form <- formService
            .getForm(
              formVersionId.parent,
              Some(formVersionId),
              param.actor,
              shouldCheckPermission = false
            )
            .map(_.formData.form)
          results <- ZIO.foreach(param.batchItemIds) { itemId =>
            exportModel.items
              .find(_.itemId == itemId)
              .flatMap(item =>
                item.lpFormIdOpt.map { lpFormId =>
                  for {
                    lpFormDataOpt <- FDBRecordDatabase.transact(LpFormDataOperations.Production)(_.getOpt(lpFormId))
                    lpFormData <- ZIOUtils.optionToTask(
                      lpFormDataOpt,
                      new RuntimeException(s"Form data for ${lpFormId.idString} of lp ${item.lpId.idString} not found")
                    )
                  } yield FormTemplateMappingUtils
                    .exportTemplateData(
                      form = form,
                      mappingItems = mappingItems,
                      formState = lpFormData.gaiaState,
                      exportTemplate = template
                    )
                    .map { state => Option(state) }
                }
              )
              .getOrElse(ZIO.succeed(Right(None)))
              .map(result => itemId -> result)
          }
        } yield results
      }
      .getOrElse(
        ZIO.succeed(param.batchItemIds.map(itemId => itemId -> Right(None)))
      )
  }

  override def processDataExport(
    param: ProcessDataExportParam
  ): ProcessDataExportResponse = {
    val task = for {
      _ <- ZIO.logInfo(s"processDataExport ${param.batchItemIds.map(_.idString).mkString(", ")}")
      exportId <- ZIO.attempt(param.batchItemIds.head.parent)
      dataExportModel <- FDBRecordDatabase.transact(FundSubDataExportOperations.Production)(
        _.get(exportId)
      )
      results <- exportData(
        dataExportModel,
        param
      )
      statuses <- ZIO
        .attempt(results.map { case (itemId, resultEither) =>
          val status: FundSubDataExportItemStatus = resultEither.fold(
            error => {
              scribe.info(s"Cannot export data for item ${itemId.idString}, error = $error")
              DataExportStatusFailed(failedAt = Some(Instant.now), reason = error)
            },
            _ => DataExportStatusSucceeded(Some(Instant.now))
          )
          itemId -> status
        })
        .map(_.toMap)
      _ <- ZIO.foreach(results) { case (itemId, resultEither) =>
        FDBRecordDatabase.transact(FundSubDataExportOperations.Production) { ops =>
          for {
            _ <- resultEither.toOption.flatten.fold[RecordTask[Unit]](RecordIO.unit) { exportState =>
              ops.createResult(itemId, exportState)
            }
          } yield ()
        }
      }
      _ <- FDBRecordDatabase.transact(FundSubDataExportOperations.Production) { ops =>
        for {
          _ <- ops.updateItemsStatus(exportId, statuses)
        } yield ()
      }
    } yield ProcessDataExportResponse(statuses)

    temporalWorkflowService.executeTask(task, "processDataExport")
  }

  override def markItemAsFailed(
    param: MarkSingleDataExportAsFailedParam
  ): MarkSingleDataExportAsFailResponse = {
    val task = for {
      _ <- ZIO.logInfo(s"markItemAsFailed ${param.batchItemIds.map(_.idString).mkString(", ")}")
      status <- ZIO.attempt(DataExportStatusFailed(Some(Instant.now), reason = param.reason))
      statuses <- ZIO.attempt(
        param.batchItemIds.map(id => id -> status).toMap
      )
      _ <- FDBRecordDatabase.transact(FundSubDataExportOperations.Production) { ops =>
        ops.updateItemsStatus(
          id = param.batchItemIds.head.parent,
          items = statuses
        )
      }
    } yield MarkSingleDataExportAsFailResponse(statuses)

    temporalWorkflowService.executeTask(task, "markItemAsFailed")
  }

}
