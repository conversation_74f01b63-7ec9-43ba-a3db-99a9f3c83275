// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dataimport.impl

import java.time.Instant

import anduin.fdb.record.{DefaultCluster, FDBRecordDatabase}
import anduin.forms.Form.FormNamespace
import anduin.forms.FormDataProtoConverter
import anduin.forms.model.template.DataTemplateModels.SpreadsheetTemplateType
import anduin.forms.service.FormService
import anduin.forms.storage.{DataTemplateVersionStoreOperations, FormTemplateMappingContentStoreOperations}
import anduin.forms.utils.{CommonUtils, FormTemplateMappingUtils}
import anduin.fundsub.batchaction.dataimport.FundSubDataImportOperations
import anduin.fundsub.models.FundSubModelStoreOperations
import anduin.id.fundsub.{DataImportItemResultId, FundSubAdminRestrictedId}
import anduin.protobuf.fundsub.lpformdata.{FieldValueMap, LpFormData}
import anduin.workflow.TemporalWorkflowService
import anduin.workflow.fundsub.GaiaStateProto
import com.anduin.stargazer.service.GondorBackendConfig
import anduin.workflow.fundsub.dataimport.*
import com.anduin.stargazer.service.utils.ZIOUtils
import zio.ZIO

import anduin.ontology.service.FormSaProfileMappingService

case class DataImportActivityImpl(
  backendConfig: GondorBackendConfig
)(
  using val formService: FormService,
  val formSaProfileMappingService: FormSaProfileMappingService,
  val temporalWorkflowService: TemporalWorkflowService
) extends FundSubDataImportActivity {

  override def startSingleDataImport(param: StartSingleDataImportParam): StartSingleDataImportResponse = {
    val task = for {
      _ <- ZIO.logInfo(s"startSingleDataImport ${param.dataImportItemId}")
      model <- FDBRecordDatabase.transact(FundSubDataImportOperations.Production) { ops =>
        for {
          _ <- ops.updateItem(param.dataImportItemId) { model =>
            model.copy(status = DataImportStatusRunning(Some(Instant.now)))
          }
          model <- ops.get(param.dataImportItemId.parent)
        } yield model
      }
      formVersionIdOpt <- FDBRecordDatabase
        .transact(FundSubModelStoreOperations.Production)(
          _.getFundSubAdminRestrictedModel(FundSubAdminRestrictedId(model.fundSubId))
        )
        .map(_.formVersions.headOption.map(_.id))
      formVersionId <- ZIOUtils.optionToTask(
        formVersionIdOpt,
        new RuntimeException(s"There is no form version in ${model.fundSubId}")
      )
    } yield StartSingleDataImportResponse(
      formVersionId,
      model.mappingVersionIdOpt,
      model.templateVersionIdOpt,
      model.actor
    )
    temporalWorkflowService.executeTask(task, "startSingleDataImport")
  }

  override def processDataImport(
    param: ProcessDataImportParam
  ): ProcessDataImportResponse = {
    val task = for {
      _ <- ZIO.logInfo(s"processDataImport ${param.dataImportItemId}")
      dataImportItem <- FDBRecordDatabase.transact(FundSubDataImportOperations.Production)(
        _.getItem(param.dataImportItemId)
      )
      mappingItems <- param.mappingVersionIdOpt
        .map { mappingVersionId =>
          FDBRecordDatabase
            .transact(FormTemplateMappingContentStoreOperations.Production)(_.get(mappingVersionId))
            .map(_.mappingItems)
        }
        .getOrElse(ZIO.succeed(Seq.empty))
      templateTypeOpt <- param.templateVersionIdOpt
        .map { templateVersionId =>
          FDBRecordDatabase
            .transact(DataTemplateVersionStoreOperations.Production)(_.get(templateVersionId))
            .map(model => Some(model.template))
        }
        .getOrElse(ZIO.succeed(None))
      form <- formService
        .getForm(
          param.formVersionId.parent,
          Some(param.formVersionId),
          param.actor,
          shouldCheckPermission = false
        )
        .map(_.formData.form)
      asaMapping <- formService.getFormStandardAliasMapping(param.formVersionId, checkValidFormFieldsAndOptions = false)
      sapMapping <- formSaProfileMappingService.queryFormVersionSaMapping(
        param.actor,
        param.formVersionId,
        useSaLocalValueIfExisted = true
      )
      multiSaMapping = CommonUtils.combineSingleSaMappingList(List(sapMapping, asaMapping))
      resultEither <- ZIO.attempt {
        FormTemplateMappingUtils
          .importSingleTemplateData(
            formInfo = Left(form),
            mappingItems = mappingItems,
            multiSaMappingOpt = Some(multiSaMapping),
            importTemplateOpt = templateTypeOpt,
            importData = dataImportItem.data
          )
      }
      importResult <- ZIOUtils.eitherToTask(
        resultEither.fold(err => Left(new RuntimeException(s"Cannot import data err = $err")), res => Right(res))
      )
      _ <- {
        val namespaceDataMap = importResult.importedState.stateMap.map { case (namespace, state) =>
          FormNamespace.unwrap(namespace) -> FieldValueMap(LpFormData.convertToNewData(state.map { case (k, v) =>
            k -> v.json
          }))
        }
        val patchesList = FormDataProtoConverter.patchesListToProto(importResult.importedState.events.toList)
        val gaiaStateProto = GaiaStateProto(namespaceDataMap, patchesList)
        val fieldMap = templateTypeOpt
          .map { case spreadsheet: SpreadsheetTemplateType =>
            spreadsheet.fields.map(field => field.refinedName -> field.name).toMap
          }
          .getOrElse(Map.empty)
        val warnings = importResult
          .displayingWarnings(fieldMap)
          .map(warning =>
            ImportWarning(
              columns = warning.columns,
              values = warning.values.map(valueOpt => WarningValue(valueOpt)),
              error = warning.error
            )
          )
        FDBRecordDatabase.transact(FundSubDataImportOperations.Production) { ops =>
          for {
            _ <- ops.createResult(
              DataImportItemResult(DataImportItemResultId(param.dataImportItemId), Some(gaiaStateProto))
            )
            _ <- ops.updateItem(param.dataImportItemId)(
              _.copy(status =
                DataImportStatusSucceeded(
                  Some(Instant.now()),
                  coverage = importResult.formCoverage,
                  warnings = warnings,
                  visibleNonEmptyFieldCount = importResult.formCompletionStatus.visibleNonEmptyFieldCount,
                  hiddenNonEmptyFieldCount = importResult.formCompletionStatus.hiddenNonEmptyFieldCount
                )
              )
            )
          } yield ()
        }
      }
      status <- FDBRecordDatabase
        .transact(FundSubDataImportOperations.Production)(_.getItem(param.dataImportItemId))
        .map(_.status)
    } yield ProcessDataImportResponse(status)
    temporalWorkflowService.executeTask(task, "processDataImport")
  }

  override def markItemAsFailed(
    param: MarkSingleDataImportAsFailedParam
  ): MarkSingleDataImportAsFailResponse = {
    val task = for {
      _ <- ZIO.logInfo(s"markItemAsFailed ${param.dataImportItemId}")
      item <- FDBRecordDatabase.transact(FundSubDataImportOperations.Production) { ops =>
        ops.updateItem(param.dataImportItemId) { model =>
          model.copy(status = DataImportStatusFailed(Some(Instant.now), reason = param.reason))
        }
      }
    } yield MarkSingleDataImportAsFailResponse(item.status)
    temporalWorkflowService.executeTask(task, "markItemAsFailed")
  }

}
