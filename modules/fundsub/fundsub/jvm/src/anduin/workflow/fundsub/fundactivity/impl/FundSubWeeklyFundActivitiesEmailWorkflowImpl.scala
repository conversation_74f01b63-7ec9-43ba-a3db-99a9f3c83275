// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.fundactivity.impl

import java.time.{DayOfWeek, Duration}

import zio.temporal.ZRetryOptions
import zio.temporal.activity.{ZActivityStub, ZActivityOptions}
import zio.temporal.workflow.ZWorkflow

import anduin.utils.DateTimeUtils
import anduin.workflow.*
import anduin.workflow.fundsub.fundactivity.*

class FundSubWeeklyFundActivitiesEmailWorkflowImpl extends FundSubWeeklyFundActivitiesEmailWorkflow {

  private val activities = ZWorkflow
    .newActivityStub[FundSubWeeklyFundActivitiesEmailActivities](
      ZActivityOptions
        .withStartToCloseTimeout(Duration.ofMinutes(30))
        .withTaskQueue(ActivityQueue.FundSubWeeklyFundActivitiesEmail.queueName)
        .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(2))
    )

  private val runnableDays = List[DayOfWeek](
    DayOfWeek.SUNDAY,
    DayOfWeek.MONDAY,
    DayOfWeek.TUESDAY
  )

  override def sendWeeklyFundActivities(params: SendFundActivityEmailParams): SendFundActivityEmailResponse = {
    val now = ZWorkflow.currentTimeMillis.toInstant
    val localDateTime = now.atZone(DateTimeUtils.defaultTimezone)
    scribe.info(s"${localDateTime.getHour} ${localDateTime.getMinute} ${localDateTime.getDayOfWeek}")

    if (runnableDays.contains(localDateTime.getDayOfWeek)) {
      val windowedEvents = ZActivityStub.execute(activities.getWindowedFundActivities).activities
      val fundIds = windowedEvents.map(_.lpId.parent).toSet
      scribe.info(s"FundSubWeeklyFundActivitiesEmailWorkflow: found ${windowedEvents.size} fund activities")
      fundIds.foreach { fundId =>
        ZActivityStub.execute(activities.sendFundEmail(SendSingleFundActivitiesEmailParams(fundId)))
      }
    } else {
      scribe.info(
        s"Skip FundSubWeeklyFundActivitiesEmailWorkflow: cause day of week = ${localDateTime.getDayOfWeek}"
      )
    }
    SendFundActivityEmailResponse()
  }

}

object FundSubWeeklyFundActivitiesEmailWorkflowImpl {

  lazy val instance =
    WorkflowImpl[FundSubWeeklyFundActivitiesEmailWorkflow, FundSubWeeklyFundActivitiesEmailWorkflowImpl](
      WorkflowQueue.FundSubWeeklyFundActivitiesEmail
    )

}
