// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dataexport.impl

import java.time.Duration

import zio.temporal.ZRetryOptions
import zio.temporal.activity.{ZActivityStub, ZActivityOptions}
import zio.temporal.failure.ActivityFailure
import zio.temporal.workflow.ZWorkflow

import anduin.workflow.{ActivityQueue, WorkflowImpl, WorkflowQueue}
import anduin.workflow.fundsub.dataexport.*

class SingleDataExportWorkflowImpl extends FundSubSingleDataExportWorkflow {

  private val activities = ZWorkflow
    .newActivityStub[FundSubDataExportActivity](
      ZActivityOptions
        .withStartToCloseTimeout(Duration.ofSeconds(60))
        .withTaskQueue(ActivityQueue.FundSubDataExport.queueName)
        .withRetryOptions(
          ZRetryOptions.default.withMaximumAttempts(0)
        )
    )

  override def exportSingle(param: SingleDataExportParam): SingleDataExportResponse = {
    scribe.info(s"Start exportSingle workflow for ${param.batchItemIds.map(_.idString).mkString(", ")}")

    try {
      val processDataResponse = ZActivityStub.execute(
        activities.processDataExport(
          ProcessDataExportParam(
            param.batchItemIds,
            param.mappingVersionIdOpt,
            param.actor
          )
        )
      )
      SingleDataExportResponse(processDataResponse.statuses)
    } catch {
      case e: ActivityFailure =>
        scribe.info(s"Catch activity failure ${e.getCause}")
        val markFailedResp = ZActivityStub.execute(
          activities.markItemAsFailed(MarkSingleDataExportAsFailedParam(param.batchItemIds, e.getCause.getMessage))
        )
        SingleDataExportResponse(markFailedResp.statuses)
    }

  }

}

object SingleDataExportWorkflowImpl {

  lazy val instance =
    WorkflowImpl[FundSubSingleDataExportWorkflow, SingleDataExportWorkflowImpl](WorkflowQueue.FundSubDataExport)

}
