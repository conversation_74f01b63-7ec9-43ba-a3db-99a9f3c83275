// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.newinvestor

import zio.temporal.{activityInterface, activityMethod}

import anduin.workflow.fundsub.invitationevent.{
  AllNewInvestorsEventResponse,
  SendSingleFundReportEmailParams,
  SendSingleFundReportEmailResponse
}

@activityInterface
trait FundSubNewInvestorReportEmailActivities {

  @activityMethod
  def getNewInvestorEvents: AllNewInvestorsEventResponse

  @activityMethod
  def sendEmails(params: SendSingleFundReportEmailParams): SendSingleFundReportEmailResponse

}
