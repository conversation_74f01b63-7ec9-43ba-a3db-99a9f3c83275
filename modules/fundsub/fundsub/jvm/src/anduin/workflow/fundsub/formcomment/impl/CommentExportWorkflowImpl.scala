// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.formcomment.impl

import anduin.workflow.fundsub.formcomment.*
import anduin.workflow.{ActivityQueue, WorkflowImpl, WorkflowQueue}
import zio.temporal.ZRetryOptions
import zio.temporal.activity.{ZActivityOptions, ZActivityStub}
import zio.temporal.workflow.*

import java.time.Duration
import scala.concurrent.duration
import scala.concurrent.duration.FiniteDuration

class CommentExportWorkflowImpl extends CommentExportWorkflow {

  private val activities = ZWorkflow
    .newActivityStub[CommentExportActivities](
      ZActivityOptions
        .withStartToCloseTimeout(Duration.ofMinutes(20))
        .withTaskQueue(ActivityQueue.CommentExport.queueName)
        .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(0))
    )

  override def exportComment(params: ExportCommentParams): ExportCommentResponse = {
    ZActivityStub.execute(
      activities.initTaskTrackingRecord(
        InitTaskTrackingRecordParams(
          actor = params.actor,
          fundId = params.fundId,
          workflowId = params.workflowId
        )
      )
    )

    val prepareCommentFolderResponse = ZActivityStub.execute(
      activities.prepareCommentExportFolders(
        PrepareCommentExportFoldersParams(
          actor = params.actor,
          fundId = params.fundId,
          includingInternalComment = params.includingInternalComment
        )
      )
    )

    val relatedInvestorsResponse = ZActivityStub.execute(
      activities.gettingAllRelatedInvestors(
        GettingAllRelatedInvestorsParams(
          actor = params.actor,
          fundId = params.fundId,
          includingInternalComment = params.includingInternalComment
        )
      )
    )

    // export share comment
    relatedInvestorsResponse.lpIds.foreach { lpId =>
      ZActivityStub.execute(
        activities.exportCommentByInvestor(
          ExportCommentByInvestorParams(
            actor = params.actor,
            lpId = lpId,
            includingInternalComment = params.includingInternalComment,
            containerFolderId = prepareCommentFolderResponse.folderId
          )
        )
      )
    }

    ZActivityStub.execute(
      activities.updateTaskTrackingRecord(
        UpdateTaskTrackingRecordParams(
          actor = params.actor,
          fundId = params.fundId,
          workflowId = params.workflowId,
          containerFolderId = prepareCommentFolderResponse.folderId
        )
      )
    )

    ExportCommentResponse(
      folderIdOpt = Option(prepareCommentFolderResponse.folderId)
    )
  }

}

object CommentExportWorkflowImpl {
  val workflowRunTimeout: FiniteDuration = FiniteDuration(30, duration.MINUTES)

  lazy val instance =
    WorkflowImpl[CommentExportWorkflow, CommentExportWorkflowImpl](WorkflowQueue.FundSubCommentExportWorkflow)

}
