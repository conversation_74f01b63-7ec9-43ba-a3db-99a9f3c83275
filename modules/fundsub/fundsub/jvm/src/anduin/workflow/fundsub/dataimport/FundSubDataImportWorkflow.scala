// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dataimport

import zio.temporal.{workflowInterface, workflowMethod}

@workflowInterface
trait FundSubSingleDataImportWorkflow {

  @workflowMethod
  def importSingle(importParam: SingleDataImportParam): SingleDataImportResponse

}

@workflowInterface
trait FundSubMultipleDataImportWorkflow {

  @workflowMethod
  def importMultiple(importParam: MultipleDataImportParam): MultipleDataImportResponse

}
