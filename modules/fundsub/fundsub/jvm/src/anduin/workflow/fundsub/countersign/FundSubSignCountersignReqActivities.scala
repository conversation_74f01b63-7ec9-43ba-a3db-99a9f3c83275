// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.countersign

import zio.temporal.{activityInterface, activityMethod}

import anduin.workflow.fundsub.batchaction.{
  FundSubBatchActionItemWorkflowParam,
  FundSubBatchActionMarkItemFailParam,
  FundSubBatchActionWorkflowParam,
  ResponseUnit
}

@activityInterface
trait FundSubSignCountersignReqActivities {

  @activityMethod
  def startSingleAction(param: FundSubBatchActionItemWorkflowParam): ResponseUnit

  @activityMethod
  def completeSingleAction(param: FundSubBatchActionItemWorkflowParam): ResponseUnit

  @activityMethod
  def completeBatchAction(param: FundSubBatchActionWorkflowParam): ResponseUnit

  @activityMethod
  def markItemAsFailed(param: FundSubBatchActionMarkItemFailParam): ResponseUnit

}
