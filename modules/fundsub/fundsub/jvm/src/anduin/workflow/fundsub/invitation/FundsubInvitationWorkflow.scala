// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.invitation

import zio.temporal.{workflowInterface, workflowMethod}

@workflowInterface
trait FundSubSingleInvitationWorkflow {

  @workflowMethod
  def inviteSingle(inviteParam: SingleInvitationParam): SingleInvitationResponse

}

@workflowInterface
trait FundSubMultipleInvitationWorkflow {

  @workflowMethod
  def inviteMultiple(inviteParam: MultipleInvitationParam): MultipleInvitationResponse

}
