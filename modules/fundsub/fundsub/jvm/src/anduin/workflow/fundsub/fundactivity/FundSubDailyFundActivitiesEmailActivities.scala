// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.fundactivity

import zio.temporal.{activityInterface, activityMethod}

@activityInterface
trait FundSubDailyFundActivitiesEmailActivities {

  @activityMethod
  def getWindowedFundActivities: AllFundActivitiesResponse

  @activityMethod
  def sendFundEmail(params: SendSingleFundActivitiesEmailParams): SendSingleFundActivitiesEmailResponse

}
