// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dataextract.impl

import anduin.workflow.fundsub.dataextract.{
  FundSubDataExtractRequestNotificationActivities,
  FundSubDataExtractRequestNotificationWorkflow,
  GetDataExtractRequestReadyForReviewEventsParams,
  ResponseUnit
}
import anduin.workflow.{ActivityQueue, WorkflowImpl, WorkflowQueue}
import zio.temporal.ZRetryOptions
import zio.temporal.activity.{ZActivityOptions, ZActivityStub}
import zio.temporal.workflow.ZWorkflow

import java.time.Duration
import java.time.Instant

class FundSubDataExtractRequestNotificationWorkflowImpl extends FundSubDataExtractRequestNotificationWorkflow {

  private val activities = ZWorkflow
    .newActivityStub[FundSubDataExtractRequestNotificationActivities](
      ZActivityOptions
        .withStartToCloseTimeout(Duration.ofMinutes(30))
        .withTaskQueue(ActivityQueue.FundSubDataExtractRequestNotification.queueName)
        .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(2))
    )

  private val TumblingWindowInterval: Duration = Duration.ofHours(1)

  override def sendNewDataExtractRequestsReadyForReviewNotifications: ResponseUnit = {
    val now = Instant.now
    val fromTimestamp = now.minus(TumblingWindowInterval)
    val windowedEventsGroupedByFunds =
      ZActivityStub
        .execute(
          activities.getNewDataExtractRequestsReadyForReview(
            GetDataExtractRequestReadyForReviewEventsParams(Some(fromTimestamp), Some(now))
          )
        )
        .requestsGroupedByFund
    scribe.info(s"FundSubDataExtractRequestNotification: found ${windowedEventsGroupedByFunds.size} events")
    windowedEventsGroupedByFunds.foreach { eventsPerFund =>
      ZActivityStub.execute(
        activities.sendNewDataExtractRequestsReadyForReviewNotificationPerFund(eventsPerFund)
      )
    }
    ResponseUnit.defaultInstance
  }

}

object FundSubDataExtractRequestNotificationWorkflowImpl {

  lazy val instance = WorkflowImpl[
    FundSubDataExtractRequestNotificationWorkflow,
    FundSubDataExtractRequestNotificationWorkflowImpl
  ](
    WorkflowQueue.FundSubDataExtractRequestNotificationEmail
  )

}
