// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dashboard.impl

import java.time.Duration
import scala.concurrent.duration
import scala.concurrent.duration.FiniteDuration

import io.temporal.api.enums.v1.ParentClosePolicy
import zio.temporal.ZRetryOptions
import zio.temporal.activity.{ZActivityStub, ZActivityOptions}
import zio.temporal.workflow.{ZChildWorkflowStub, ZChildWorkflowOptions, ZWorkflow}

import anduin.id.fundsub.FundSubId
import anduin.workflow.fundsub.dashboard.*
import anduin.workflow.*

class FundSubSyncDashboardWorkflowImpl extends FundSubSyncDashboardWorkflow {

  private val activities = ZWorkflow
    .newActivityStub[FundSubSyncDashboardActivities](
      ZActivityOptions
        .withStartToCloseTimeout(Duration.ofMinutes(10))
        .withTaskQueue(ActivityQueue.FundSubSyncDashboardData.queueName)
        .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(2))
    )

  override def syncDashboardData(params: SyncDashboardDataParams): SyncDashboardDataResponse = {
    val allFundIds = ZActivityStub.execute(activities.getFundIds).fundIds
    val affectedFundIds = allFundIds.foldLeft[List[FundSubId]](List.empty) { case (affectedFundIds, fundId) =>
      val syncSingleFundWorkflow = ZWorkflow
        .newChildWorkflowStub[SyncSingleFundDashboardWorkflow](
          ZChildWorkflowOptions
            .withWorkflowId(ZWorkflow.randomUUID.toString)
            .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(3))
            .withWorkflowRunTimeout(Duration.ofMinutes(10))
            .withParentClosePolicy(ParentClosePolicy.PARENT_CLOSE_POLICY_TERMINATE)
            .withWorkflowId(s"sync-fund-${fundId.idString}-${ZWorkflow.randomUUID}")
        )
      val response = ZChildWorkflowStub.execute(
        syncSingleFundWorkflow.syncDashboardData(FundIdParams(fundId))
      )
      if (response.detectedDataMismatch) {
        affectedFundIds :+ fundId
      } else {
        affectedFundIds
      }
    }
    SyncDashboardDataResponse(
      affectedFundIds
    )
  }

}

object FundSubSyncDashboardWorkflowImpl {

  val workflowRunTimeout: FiniteDuration = FiniteDuration(2, duration.HOURS)

  lazy val instance = WorkflowImpl[FundSubSyncDashboardWorkflow, FundSubSyncDashboardWorkflowImpl](
    WorkflowQueue.FundSubSyncDashboardData
  )

}
