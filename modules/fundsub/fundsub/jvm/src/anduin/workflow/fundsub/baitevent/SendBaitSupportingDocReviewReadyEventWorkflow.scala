// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.baitevent

import io.temporal.workflow.{WorkflowInterface, WorkflowMethod}

@WorkflowInterface
trait SendBaitSupportingDocReviewReadyEventWorkflow {

  @WorkflowMethod
  def sendBaitSupportingDocReviewReadyEvent(
    params: SendBaitSupportingDocReviewReadyEventParams
  ): SendBaitSupportingDocReviewReadyEventResponse

}
