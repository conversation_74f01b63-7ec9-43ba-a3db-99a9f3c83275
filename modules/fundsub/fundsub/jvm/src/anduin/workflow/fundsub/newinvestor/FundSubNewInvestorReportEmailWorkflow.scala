// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.newinvestor

import zio.temporal.{workflowInterface, workflowMethod}

import anduin.workflow.fundsub.invitationevent.{SendNewInvestorEmailParams, SendNewInvestorEmailResponse}

@workflowInterface
trait FundSubNewInvestorReportEmailWorkflow {

  @workflowMethod
  def sendEmails(params: SendNewInvestorEmailParams): SendNewInvestorEmailResponse

}
