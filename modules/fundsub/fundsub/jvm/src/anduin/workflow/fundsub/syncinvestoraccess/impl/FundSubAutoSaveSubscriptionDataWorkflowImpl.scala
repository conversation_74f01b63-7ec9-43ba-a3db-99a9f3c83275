// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.syncinvestoraccess.impl

import anduin.id.fundsub.FundSubLpId
import anduin.workflow.fundsub.syncinvestoraccess.{
  AutoSaveSubscriptionFormParams,
  EmptyResponse,
  FundSubAutoSaveSubscriptionDataActivities,
  FundSubAutoSaveSubscriptionDataWorkflow
}
import anduin.workflow.{ActivityQueue, WorkflowImpl, WorkflowQueue}
import zio.temporal.ZRetryOptions
import zio.temporal.activity.{ZActivityOptions, ZActivityStub}
import zio.temporal.failure.ActivityFailure
import zio.temporal.workflow.ZWorkflow

import java.time.Duration

class FundSubAutoSaveSubscriptionDataWorkflowImpl extends FundSubAutoSaveSubscriptionDataWorkflow {

  private val activities = ZWorkflow
    .newActivityStub[FundSubAutoSaveSubscriptionDataActivities](
      ZActivityOptions
        .withStartToCloseTimeout(Duration.ofMinutes(30))
        .withTaskQueue(ActivityQueue.FundSubAutoSaveSubscriptionData.queueName)
        .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(2))
    )

  private def errorHandler(fundSubLpId: FundSubLpId, e: Throwable) = {
    scribe.error(s"Failed to auto save $fundSubLpId", e)
    EmptyResponse()
  }

  override def autoSaveSubscriptionForm(params: AutoSaveSubscriptionFormParams): EmptyResponse = {
    scribe.info(s"Start check and autosave Lp ${params.fundSubLpId} to Profile")
    try {
      ZActivityStub.execute(activities.autoSaveSubscriptionForm(params))
    } catch {
      case e: ActivityFailure => errorHandler(params.fundSubLpId, e)
    }
  }

}

object FundSubAutoSaveSubscriptionDataWorkflowImpl {

  lazy val instance = WorkflowImpl[
    FundSubAutoSaveSubscriptionDataWorkflow,
    FundSubAutoSaveSubscriptionDataWorkflowImpl
  ](WorkflowQueue.FundSubAutoSaveSubscriptionData)

}
