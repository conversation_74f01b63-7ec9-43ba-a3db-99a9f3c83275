// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.batchaction.impl

import java.time.Instant

import zio.ZIO

import anduin.fundsub.batchaction.FundSubBatchActionService
import anduin.fundsub.endpoint.admin.{BatchUpdateInvestorData, BatchUpdateInvestorDataParams}
import anduin.fundsub.endpoint.lp.{RemindLpCompleteFormParams, ResendLpInvitationParams, UserEmailTemplate}
import anduin.fundsub.endpoint.supportingdoc.RemindSupportingDocParams
import anduin.model.optics.iso.RequestContextIso.authenticatedRequestContextIso
import anduin.utils.ScalaUtils
import anduin.workflow.TemporalWorkflowService
import anduin.workflow.fundsub.batchaction.UpdateInvestorValueItemData.{ActionType, ColumnTypeMessage}
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.fundsub.{FundSubAdminService, FundSubLpService, SupportingDocService}
import com.anduin.stargazer.service.fundsub.free.module.ManageFundSubLpM
import com.anduin.stargazer.service.utils.ZIOUtils
import anduin.workflow.fundsub.batchaction.*

case class FundSubBatchActionActivitiesImpl(
  backendConfig: GondorBackendConfig,
  fundSubBatchActionService: FundSubBatchActionService,
  fundSubLpService: FundSubLpService,
  fundSubAdminService: FundSubAdminService,
  supportingDocService: SupportingDocService
)(
  using val lpModule: ManageFundSubLpM,
  val temporalWorkflowService: TemporalWorkflowService
) extends FundSubBatchActionActivities {

  override def startSingleAction(param: FundSubBatchActionItemWorkflowParam): ResponseUnit = {
    val task =
      for {
        itemModel <- fundSubBatchActionService.startSingleAction(param.batchActionItemId)
        _ <- ZIOUtils.when(ScalaUtils.isMatch[FundSubBatchActionStatusRunning](itemModel.status)) {
          itemModel.data.asMessage.sealedValue match {
            case FundSubBatchActionItemDataMessage.SealedValue.Empty |
                _: FundSubBatchActionItemDataMessage.SealedValue.EmptyItemData |
                _: FundSubBatchActionItemDataMessage.SealedValue.SupportingDocReviewFlowItemData |
                _: FundSubBatchActionItemDataMessage.SealedValue.SignCountersignRequestData |
                _: FundSubBatchActionItemDataMessage.SealedValue.SubscriptionDocReviewFlowItemData =>
              ZIO.succeed(())
            case FundSubBatchActionItemDataMessage.SealedValue.ResendInvitationItemData(value) =>
              processResendInvitation(value)
            case FundSubBatchActionItemDataMessage.SealedValue.UpdateInvestorValueItemData(value) =>
              processUpdateInvestorData(value)
            case FundSubBatchActionItemDataMessage.SealedValue.RemindSubmitSubscription(value) =>
              processRemindSubmitSubscription(value)
            case FundSubBatchActionItemDataMessage.SealedValue.RemindSubmitSupportingDoc(value) =>
              processRemindSubmitSupportingDoc(value)
            case FundSubBatchActionItemDataMessage.SealedValue.UploadCounterSignedDoc(value) =>
              processUploadCounterSignedDoc(value)
            case FundSubBatchActionItemDataMessage.SealedValue.DistributeCounterSignedDoc(value) =>
              processDistributeCounterSignedDoc(value)
            case FundSubBatchActionItemDataMessage.SealedValue.ConvertOfflineToNormalOrder(value) =>
              processConvertOfflineToNormalOrder(value)
          }
        }
      } yield ResponseUnit()
    temporalWorkflowService.executeTask(task, "startSingleAction")
  }

  private def processResendInvitation(value: ResendInvitationItemData) = {
    fundSubLpService.resendLpInvitation(
      ResendLpInvitationParams(
        lps = value.lpsInfo.map { info =>
          ResendLpInvitationParams.LpInfo(lpId = info.lpId, recipients = info.recipients.toList)
        }.toList,
        lpEmailTemplate = value.lpEmailTemplate.map(UserEmailTemplate.fromEmailTemplateMessage),
        attachedDocs = value.attachedDocs
      ),
      actor = value.actor
    )
  }

  private def processUpdateInvestorData(value: UpdateInvestorValueItemData) = {
    ZIOUtils.traverseOption(
      for {
        columnType <- value.columnType.asMessage.sealedValue match {
          case ColumnTypeMessage.SealedValue.Empty => None
          case ColumnTypeMessage.SealedValue.SingleSelectColumn(value) =>
            Option(BatchUpdateInvestorData.SingleSelectColumn(value.customColumnId))
          case ColumnTypeMessage.SealedValue.MultipleSelectColumn(value) =>
            Option(BatchUpdateInvestorData.MultipleSelectColumn(value.customColumnId))
          case ColumnTypeMessage.SealedValue.TagColumn(_) => Option(BatchUpdateInvestorData.TagColumn())
        }
        actionType <- value.actionType match {
          case ActionType.ADD             => Option(BatchUpdateInvestorData.AddValue())
          case ActionType.REMOVE          => Option(BatchUpdateInvestorData.RemoveValue())
          case ActionType.Unrecognized(_) => None
        }
      } yield (actionType, columnType)
    ) { case (actionType, columnType) =>
      fundSubAdminService.batchUpdateInvestorData(
        params = BatchUpdateInvestorDataParams(
          fundSubId = value.fundSubId,
          lpIds = value.lpIds,
          actionType = actionType,
          columnType = columnType,
          values = value.values.toSet
        ),
        actor = value.actor
      )
    }
  }

  private def processRemindSubmitSubscription(value: RemindSubmitSubscriptionItemData) = {
    fundSubLpService.remindLpCompleteForm(
      RemindLpCompleteFormParams(
        lps = value.lpsInfo
          .map(lpInfo => RemindLpCompleteFormParams.LpInfo(lpId = lpInfo.lpId, receivers = lpInfo.recipients.toList))
          .toList,
        remindEmailTemplate = value.emailTemplate.map(UserEmailTemplate.fromEmailTemplateMessage),
        attachedDocs = value.attachedDocs
      ),
      value.actor
    )
  }

  private def processRemindSubmitSupportingDoc(value: RemindSubmitSupportingDocItemData) = {
    supportingDocService.remindSupportingDoc(
      RemindSupportingDocParams(
        lps = value.lpsInfo
          .map(lpInfo => RemindSupportingDocParams.LpInfo(lpId = lpInfo.lpId, receivers = lpInfo.recipients.toSet)),
        remindEmailTemplate = value.emailTemplate.map(UserEmailTemplate.fromEmailTemplateMessage),
        attachedDocs = value.attachedDocs,
        ccRecipients = value.ccRecipients
      ),
      value.actor
    )
  }

  private def processUploadCounterSignedDoc(value: UploadCounterSignedDocItemData) = {
    fundSubLpService.uploadCounterSignedDocs(
      lpIds = value.lpIds,
      counterSignedDocs = value.counterSignedDocs,
      httpContext = value.httpContext.map(authenticatedRequestContextIso.reverseGet),
      actor = value.actor
    )
  }

  private def processDistributeCounterSignedDoc(value: DistributeCounterSignedDocItemData) = {
    fundSubLpService.distributeCounterSignedDocs(
      lpIds = value.lpIds,
      emailTemplate = value.emailTemplate.map(UserEmailTemplate.fromEmailTemplateMessage),
      actor = value.actor,
      httpContext = value.httpContext.map(authenticatedRequestContextIso.reverseGet)
    )
  }

  private def processConvertOfflineToNormalOrder(value: ConvertOfflineToNormalOrderItemData) = {
    fundSubAdminService.convertOfflineToNormalOrder(
      lpIds = value.lpIds,
      lpEmailTemplate = value.lpEmailTemplate.map(UserEmailTemplate.fromEmailTemplateMessage),
      actor = value.actor
    )
  }

  override def markItemAsFailed(param: FundSubBatchActionMarkItemFailParam): ResponseUnit = {
    val task =
      for {
        _ <- fundSubBatchActionService.updateSingleActionStatus(
          param.batchActionItemId,
          _ => FundSubBatchActionStatusFailed(Some(Instant.now), param.error)
        )
      } yield ResponseUnit()
    temporalWorkflowService.executeTask(task, "markItemAsFailed")
  }

  override def completeSingleAction(
    param: FundSubBatchActionItemWorkflowParam
  ): ResponseUnit = {
    val task =
      for {
        _ <- fundSubBatchActionService
          .updateSingleActionStatus(
            param.batchActionItemId,
            oldStatus =>
              if (ScalaUtils.isMatch[FundSubBatchActionStatusRunning](oldStatus)) {
                FundSubBatchActionStatusSucceeded(Some(Instant.now))
              } else {
                oldStatus
              }
          )
      } yield ResponseUnit()
    temporalWorkflowService.executeTask(task, "completeSingleAction")
  }

  override def completeBatchAction(
    param: FundSubBatchActionWorkflowParam
  ): ResponseUnit = {
    val task =
      for {
        itemsData <- fundSubBatchActionService.getBatchActionItemsDataGeneric(param.batchActionId)
        _ <- ZIO.foreach(itemsData.headOption.map(_.asMessage.sealedValue)) {
          case FundSubBatchActionItemDataMessage.SealedValue.Empty |
              _: FundSubBatchActionItemDataMessage.SealedValue.EmptyItemData |
              _: FundSubBatchActionItemDataMessage.SealedValue.SupportingDocReviewFlowItemData |
              _: FundSubBatchActionItemDataMessage.SealedValue.SignCountersignRequestData |
              _: FundSubBatchActionItemDataMessage.SealedValue.SubscriptionDocReviewFlowItemData |
              _: FundSubBatchActionItemDataMessage.SealedValue.ResendInvitationItemData |
              _: FundSubBatchActionItemDataMessage.SealedValue.UpdateInvestorValueItemData |
              _: FundSubBatchActionItemDataMessage.SealedValue.RemindSubmitSubscription |
              _: FundSubBatchActionItemDataMessage.SealedValue.RemindSubmitSupportingDoc |
              _: FundSubBatchActionItemDataMessage.SealedValue.UploadCounterSignedDoc |
              _: FundSubBatchActionItemDataMessage.SealedValue.DistributeCounterSignedDoc |
              _: FundSubBatchActionItemDataMessage.SealedValue.ConvertOfflineToNormalOrder =>
            ZIO.succeed(())
        }
      } yield ResponseUnit()
    temporalWorkflowService.executeTask(task, "completeBatchAction")
  }

}
