// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dataextract.impl

import anduin.workflow.TemporalWorkflowService
import anduin.workflow.fundsub.dataextract.{
  FundSubDataExtractRequestNotificationActivities,
  GetDataExtractRequestReadyForReviewEventsParams,
  GetDataExtractRequestsReadyForReviewResponse,
  NewDataExtractRequestsPerFund,
  ResponseUnit
}
import java.time.Instant
import anduin.fundsub.dataextract.service.FundSubDataExtractRequestEmailService

final case class FundSubDataExtractRequestNotificationActivitiesImpl(
  temporalWorkflowService: TemporalWorkflowService,
  fundSubDataExtractRequestEmailService: FundSubDataExtractRequestEmailService
) extends FundSubDataExtractRequestNotificationActivities {

  override def sendNewDataExtractRequestsReadyForReviewNotificationPerFund(
    requestsPerFund: NewDataExtractRequestsPerFund
  ): ResponseUnit = {
    temporalWorkflowService.executeTask(
      fundSubDataExtractRequestEmailService
        .sendNewDataExtractRequestReadyForReviewPerFund(requestsPerFund)
        .as(ResponseUnit.defaultInstance),
      "sendEmailPerFund"
    )
  }

  override def getNewDataExtractRequestsReadyForReview(params: GetDataExtractRequestReadyForReviewEventsParams)
    : GetDataExtractRequestsReadyForReviewResponse = {
    temporalWorkflowService.executeTask(
      fundSubDataExtractRequestEmailService.getNewDataExtractRequestsReadyForReview(
        params.fromTimestamp.getOrElse(Instant.now),
        params.toTimestamp.getOrElse(Instant.now)
      ),
      "getNewDataExtractRequestReadyForReviewEvents"
    )
  }

}
