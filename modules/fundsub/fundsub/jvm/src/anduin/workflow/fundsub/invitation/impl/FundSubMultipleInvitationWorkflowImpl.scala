// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.invitation.impl

import java.time.Duration

import io.temporal.api.enums.v1.ParentClosePolicy
import zio.temporal.ZRetryOptions
import zio.temporal.activity.{ZActivityOptions, ZActivityStub}
import zio.temporal.workflow.{ZChildWorkflowOptions, ZChildWorkflowStub, ZWorkflow}

import anduin.utils.ScalaUtils
import anduin.workflow.fundsub.invitation.*
import anduin.workflow.{ActivityQueue, WorkflowImpl, WorkflowQueue}

class FundSubMultipleInvitationWorkflowImpl extends FundSubMultipleInvitationWorkflow {

  private val activities = ZWorkflow
    .newActivityStub[FundSubInvitationActivities](
      ZActivityOptions
        .withStartToCloseTimeout(Duration.ofSeconds(30))
        .withScheduleToCloseTimeout(Duration.ofMinutes(10))
        .withTaskQueue(ActivityQueue.FundSubInvitation.queueName)
        .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(3))
    )

  // can add activities here too if we want to
  override def inviteMultiple(inviteParam: MultipleInvitationParam): MultipleInvitationResponse = {
    scribe.info(s"Start multiple invite with ${inviteParam.invitations.size} invitations")
    val inviteSingleWorkflow = ZWorkflow
      .newChildWorkflowStub[FundSubSingleInvitationWorkflow](
        ZChildWorkflowOptions
          .withWorkflowId(ZWorkflow.randomUUID.toString)
          .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(3))
          .withWorkflowRunTimeout(FundSubMultipleInvitationWorkflowImpl.SingleInvitationWorkflowRunTimeout)
          .withParentClosePolicy(ParentClosePolicy.PARENT_CLOSE_POLICY_TERMINATE)
      )

    val res = inviteParam.invitations.foldLeft(Seq.empty[SingleInvitationResponse]) { (resList, inviteSingleParam) =>
      resList.lastOption.fold(
        Seq(
          ZChildWorkflowStub.execute(inviteSingleWorkflow.inviteSingle(inviteSingleParam))
        )
      ) { lastRes =>
        val hasCancelledRestOfInvitation = ScalaUtils.isMatch[FundSubBatchInvitationStatusCancelled](lastRes.status)
        if (hasCancelledRestOfInvitation) {
          resList :+ lastRes.copy(lpIdOpt = None)
        } else {
          val thisInvitationRes = ZChildWorkflowStub.execute(inviteSingleWorkflow.inviteSingle(inviteSingleParam))
          resList :+ thisInvitationRes
        }
      }
    }

    ZActivityStub.execute(
      activities.createBatchInviteInvestorActivityLog(
        CreateBatchInviteInvestorActivityLogParam(inviteParam.batchInvitationId)
      )
    )
    ZActivityStub.execute(
      activities.sendBatchEmailAndCreateBatchFundSubAuditLog(
        SendBatchEmailAndCreateBatchFundSubAuditLogParam(
          inviteParam.batchInvitationId,
          res,
          inviteParam.invitations.headOption.map(_.actorIpAddress)
        )
      )
    )
    ZActivityStub.execute(
      activities.completeBatchInvitation(inviteParam)
    )

    MultipleInvitationResponse(res)
  }

}

object FundSubMultipleInvitationWorkflowImpl {

  private val SingleInvitationWorkflowRunTimeout = Duration.ofMinutes(10)

  lazy val instance =
    WorkflowImpl[FundSubMultipleInvitationWorkflow, FundSubMultipleInvitationWorkflowImpl](
      WorkflowQueue.FundSubInvitation
    )

}
