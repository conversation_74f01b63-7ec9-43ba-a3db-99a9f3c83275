// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dataexport

import zio.temporal.{workflowInterface, workflowMethod}

@workflowInterface
trait FundSubSingleDataExportWorkflow {

  @workflowMethod
  def exportSingle(exportParam: SingleDataExportParam): SingleDataExportResponse

}

@workflowInterface
trait FundSubMultipleDataExportWorkflow {

  @workflowMethod
  def exportMultiple(exportParam: MultipleDataExportParam): MultipleDataExportResponse

}
