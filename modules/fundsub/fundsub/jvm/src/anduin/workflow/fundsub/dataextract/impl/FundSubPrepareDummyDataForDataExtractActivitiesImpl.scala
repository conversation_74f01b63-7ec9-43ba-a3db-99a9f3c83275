// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dataextract.impl

import zio.ZIO

import anduin.fdb.record.DefaultCluster
import anduin.forms.service.FormService
import anduin.forms.util.FormDataGenerationUtils
import anduin.fundsub.dataextract.FundSubDataExtractJvmUtils
import anduin.fundsub.dataextract.protocols.{PrepareDummyFormDataInput, PrepareDummyFormDataOutput}
import anduin.fundsub.dataextract.service.FundSubSubdocDataExtractService
import anduin.workflow.TemporalWorkflowService
import anduin.workflow.fundsub.dataextract.FundSubPrepareDummyDataForDataExtractActivities
import com.anduin.stargazer.service.utils.ZIOUtils

case class FundSubPrepareDummyDataForDataExtractActivitiesImpl(
  temporalWorkflowService: TemporalWorkflowService,
  fundSubSubdocDataExtractService: FundSubSubdocDataExtractService,
  formService: FormService
) extends FundSubPrepareDummyDataForDataExtractActivities {

  override def prepareDummyDataForDataExtraction(input: PrepareDummyFormDataInput): PrepareDummyFormDataOutput = {
    val task = for {
      _ <- ZIO.logInfo(s"Preparing dummy data for lp ${input.lpId}")
      requestOpt <- FundSubDataExtractJvmUtils.getActiveRequestOptOfLpUnsafe(input.lpId)
      _ <- ZIOUtils.traverseOption(requestOpt) { request =>
        for {
          getFormResp <- formService.getForm(
            formId = input.formVersionId.parent,
            versionIdOpt = Some(input.formVersionId),
            actor = input.actor,
            shouldCheckPermission = false
          )
          gaiaStateOpt = FormDataGenerationUtils.generateDummyState(form = getFormResp.formData.form)
          _ <- ZIOUtils.traverseOption(gaiaStateOpt) { gaiaState =>
            fundSubSubdocDataExtractService.markRequestAsReadyForReviewUnsafe(
              request = request,
              newExtractedData = Left((newFormVersionId = input.formVersionId, newGaiaState = gaiaState)),
              actorId = input.actor
            )
          }
        } yield ()
      }
    } yield PrepareDummyFormDataOutput()
    temporalWorkflowService.executeTask(task, "prepareDummyDataForDataExtraction")
  }

}
