// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.formcomment.impl

import java.time.Duration

import zio.temporal.ZRetryOptions
import zio.temporal.activity.{ZActivityStub, ZActivityOptions}
import zio.temporal.failure.ActivityFailure
import zio.temporal.workflow.ZWorkflow

import anduin.workflow.*
import anduin.workflow.fundsub.formcomment.{
  FundSubFormCommentNotificationActivities,
  FundSubFormCommentNotificationWorkflow,
  SendFormCommentNotificationParams,
  SendFormCommentNotificationResponse
}

final class FundSubFormCommentNotificationWorkflowImpl extends FundSubFormCommentNotificationWorkflow {

  private val activities = ZWorkflow
    .newActivityStub[FundSubFormCommentNotificationActivities](
      ZActivityOptions
        .withStartToCloseTimeout(Duration.ofMinutes(10))
        .withTaskQueue(ActivityQueue.FundSubFormCommentNotification.queueName)
        .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(1))
    )

  override def sendFormCommentNotification(
    params: SendFormCommentNotificationParams
  ): SendFormCommentNotificationResponse = {
    scribe.info(s"Start sending form comment notifications for fund ${params.fundSubId}")
    try {
      ZActivityStub.execute(activities.sendFormCommentNotification(params))
    } catch {
      case e: ActivityFailure =>
        scribe.error(s"Failed to send form comment notifications for fund ${params.fundSubId}", e)
        SendFormCommentNotificationResponse.defaultInstance
    }
  }

}

object FundSubFormCommentNotificationWorkflowImpl {

  lazy val instance = WorkflowImpl[FundSubFormCommentNotificationWorkflow, FundSubFormCommentNotificationWorkflowImpl](
    WorkflowQueue.FundSubFormCommentNotification
  )

}
