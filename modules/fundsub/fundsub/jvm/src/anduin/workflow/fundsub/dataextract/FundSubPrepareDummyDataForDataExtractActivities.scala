// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dataextract

import zio.temporal.{activityInterface, activityMethod}

import anduin.fundsub.dataextract.protocols.{PrepareDummyFormDataInput, PrepareDummyFormDataOutput}
import anduin.workflow.ActivityQueue
import anduin.workflow.common.{TemporalActivity, TemporalActivityCompanion}

@activityInterface
trait FundSubPrepareDummyDataForDataExtractActivities extends TemporalActivity {

  @activityMethod
  def prepareDummyDataForDataExtraction(input: PrepareDummyFormDataInput): PrepareDummyFormDataOutput

}

object FundSubPrepareDummyDataForDataExtractActivities
    extends TemporalActivityCompanion[FundSubPrepareDummyDataForDataExtractActivities](
      ActivityQueue.FundSubPrepareDummyDataForDataExtract
    )
