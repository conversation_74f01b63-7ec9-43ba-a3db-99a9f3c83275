// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dashboard.impl

import java.time.Duration

import zio.temporal.ZRetryOptions
import zio.temporal.activity.{ZActivityStub, ZActivityOptions}
import zio.temporal.workflow.ZWorkflow

import anduin.workflow.*
import anduin.workflow.fundsub.dashboard.{
  FundIdParams,
  FundSyncResponse,
  SyncSingleFundDashboardActivities,
  SyncSingleFundDashboardWorkflow
}

class SyncSingleFundDashboardWorkflowImpl extends SyncSingleFundDashboardWorkflow {

  private val activities = ZWorkflow
    .newActivityStub[SyncSingleFundDashboardActivities](
      ZActivityOptions
        .withStartToCloseTimeout(Duration.ofMinutes(10))
        .withTaskQueue(ActivityQueue.SyncSingleFundDashboardData.queueName)
        .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(2))
    )

  override def syncDashboardData(params: FundIdParams): FundSyncResponse = {
    ZActivityStub.execute(activities.syncSingleFundDashboad(params))
  }

}

object SyncSingleFundDashboardWorkflowImpl {

  lazy val instance = WorkflowImpl[SyncSingleFundDashboardWorkflow, SyncSingleFundDashboardWorkflowImpl](
    WorkflowQueue.FundSubSyncDashboardData
  )

}
