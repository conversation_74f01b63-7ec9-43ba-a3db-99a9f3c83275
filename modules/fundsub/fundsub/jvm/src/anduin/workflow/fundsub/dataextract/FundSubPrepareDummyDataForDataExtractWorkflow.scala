// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dataextract

import java.time.Duration

import io.temporal.client.WorkflowOptions
import zio.temporal.{workflowInterface, workflowMethod}

import anduin.fundsub.dataextract.protocols.{PrepareDummyFormDataInput, PrepareDummyFormDataOutput}
import anduin.workflow.common.{TemporalWorkflow, TemporalWorkflowCompanion}
import anduin.workflow.WorkflowQueue

@workflowInterface
trait FundSubPrepareDummyDataForDataExtractWorkflow
    extends TemporalWorkflow[PrepareDummyFormDataInput, PrepareDummyFormDataOutput] {

  @workflowMethod
  override def run(input: PrepareDummyFormDataInput): PrepareDummyFormDataOutput

}

object FundSubPrepareDummyDataForDataExtractWorkflow
    extends TemporalWorkflowCompanion[FundSubPrepareDummyDataForDataExtractWorkflow] {

  override val workflowQueue = WorkflowQueue.FundSubPrepareDummyDataForDataExtraction

  override def transformJavaOptions: WorkflowOptions.Builder => WorkflowOptions.Builder =
    _.setStartDelay(Duration.ofSeconds(5))

}
