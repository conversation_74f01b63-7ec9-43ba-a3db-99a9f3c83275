// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.batchaction.impl

import java.time.Duration

import zio.temporal.ZRetryOptions
import zio.temporal.workflow.ZWorkflow
import zio.temporal.activity.{ZActivityStub, ZActivityOptions}
import zio.temporal.failure.ActivityFailure

import anduin.workflow.{ActivityQueue, WorkflowImpl, WorkflowQueue}
import anduin.workflow.fundsub.batchaction.*

class FundSubSingleActionWorkflowImpl extends FundSubSingleActionWorkflow {

  private val activities = ZWorkflow
    .newActivityStub[FundSubBatchActionActivities](
      ZActivityOptions
        .withStartToCloseTimeout(Duration.ofMinutes(2))
        .withScheduleToCloseTimeout(Duration.ofMinutes(10))
        .withTaskQueue(ActivityQueue.FundSubBatchAction.queueName)
        .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(3))
    )

  override def startSingleAction(
    param: FundSubBatchActionItemWorkflowParam
  ): ResponseUnit = {
    try {
      ZActivityStub.execute(activities.startSingleAction(param))
      ZActivityStub.execute(activities.completeSingleAction(param))
    } catch {
      case e: ActivityFailure =>
        scribe.info(s"Catch activity failure ${e.getCause}")
        ZActivityStub.execute(
          activities.markItemAsFailed(
            FundSubBatchActionMarkItemFailParam(
              param.batchActionItemId,
              FundSubBatchActionServerError(e.getCause.getMessage)
            )
          )
        )
    }
  }

}

object FundSubSingleActionWorkflowImpl {

  lazy val instance = WorkflowImpl[FundSubSingleActionWorkflow, FundSubSingleActionWorkflowImpl](
    WorkflowQueue.FundSubBatchAction
  )

}
