// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dataexport.impl

import zio.temporal.workflow.{ZChildWorkflowStub, ZChildWorkflowOptions, ZWorkflow}

import anduin.workflow.{WorkflowImpl, WorkflowQueue}
import anduin.workflow.fundsub.dataexport.*

class MultipleDataExportWorkflowImpl extends FundSubMultipleDataExportWorkflow {

  override def exportMultiple(param: MultipleDataExportParam): MultipleDataExportResponse = {
    scribe.info(
      s"Start multiple export with ${param.batches.map(_.batchItemIds.size).sum} lps, divided into ${param.batches.size} batches"
    )
    val exportSingleWorkflow = ZWorkflow.newChildWorkflowStub[FundSubSingleDataExportWorkflow](
      ZChildWorkflowOptions.withWorkflowId(ZWorkflow.randomUUID.toString)
    )

    val res = param.batches.map { batchParam =>
      ZChildWorkflowStub.execute(exportSingleWorkflow.exportSingle(batchParam))
    }

    MultipleDataExportResponse(res)
  }

}

object MultipleDataExportWorkflowImpl {

  lazy val instance =
    WorkflowImpl[FundSubMultipleDataExportWorkflow, MultipleDataExportWorkflowImpl](WorkflowQueue.FundSubDataExport)

}
