// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.batchaction

import zio.temporal.{activityInterface, activityMethod}

@activityInterface
trait FundSubBatchActionActivities {

  @activityMethod
  def startSingleAction(param: FundSubBatchActionItemWorkflowParam): ResponseUnit

  @activityMethod
  def completeSingleAction(param: FundSubBatchActionItemWorkflowParam): ResponseUnit

  @activityMethod
  def completeBatchAction(param: FundSubBatchActionWorkflowParam): ResponseUnit

  @activityMethod
  def markItemAsFailed(param: FundSubBatchActionMarkItemFailParam): ResponseUnit

}
