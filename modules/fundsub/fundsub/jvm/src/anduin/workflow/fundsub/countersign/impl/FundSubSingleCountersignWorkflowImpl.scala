// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.countersign.impl

import java.time.Duration

import zio.temporal.ZRetryOptions
import zio.temporal.workflow.ZWorkflow
import zio.temporal.activity.{ZActivityStub, ZActivityOptions}
import zio.temporal.failure.ActivityFailure

import anduin.workflow.fundsub.batchaction.{
  FundSubBatchActionItemWorkflowParam,
  FundSubBatchActionMarkItemFailParam,
  FundSubBatchActionServerError,
  ResponseUnit
}
import anduin.workflow.fundsub.countersign.{FundSubSignCountersignReqActivities, FundSubSingleSignCountersignReqWorkflow}
import anduin.workflow.{ActivityQueue, WorkflowImpl, WorkflowQueue}

class FundSubSingleCountersignWorkflowImpl extends FundSubSingleSignCountersignReqWorkflow {

  private val activities = ZWorkflow
    .newActivityStub[FundSubSignCountersignReqActivities](
      ZActivityOptions
        .withStartToCloseTimeout(Duration.ofMinutes(2))
        .withScheduleToCloseTimeout(Duration.ofMinutes(10))
        .withTaskQueue(ActivityQueue.FundSubCountersign.queueName)
        .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(3))
    )

  override def startSingleSignCountersignReq(
    param: FundSubBatchActionItemWorkflowParam
  ): ResponseUnit = {
    try {
      ZActivityStub.execute(activities.startSingleAction(param))
      ZActivityStub.execute(activities.completeSingleAction(param))
    } catch {
      case e: ActivityFailure =>
        scribe.info(s"Catch activity failure ${e.getCause}")
        ZActivityStub.execute(
          activities.markItemAsFailed(
            FundSubBatchActionMarkItemFailParam(
              param.batchActionItemId,
              FundSubBatchActionServerError(e.getCause.getMessage)
            )
          )
        )
    }
  }

}

object FundSubSingleCountersignWorkflowImpl {

  lazy val instance =
    WorkflowImpl[FundSubSingleSignCountersignReqWorkflow, FundSubSingleCountersignWorkflowImpl](
      WorkflowQueue.FundSubCountersign
    )

}
