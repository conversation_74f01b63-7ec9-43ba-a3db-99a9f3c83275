// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.countersign.impl

import java.time.Duration

import io.temporal.api.enums.v1.ParentClosePolicy
import zio.temporal.ZRetryOptions
import zio.temporal.activity.{ZActivityStub, ZActivityOptions}
import zio.temporal.workflow.{ZChildWorkflowStub, ZChildWorkflowOptions, ZWorkflow}

import anduin.workflow.fundsub.batchaction.{
  FundSubBatchActionItemWorkflowParam,
  FundSubBatchActionWorkflowParam,
  ResponseUnit
}
import anduin.workflow.fundsub.countersign.{
  FundSubBatchSignCountersignReqWorkflow,
  FundSubSignCountersignReqActivities,
  FundSubSingleSignCountersignReqWorkflow
}
import anduin.workflow.{ActivityQueue, WorkflowImpl, WorkflowQueue}

class FundSubBatchSignCountersignReqWorkflowImpl extends FundSubBatchSignCountersignReqWorkflow {

  private val activities = ZWorkflow
    .newActivityStub[FundSubSignCountersignReqActivities](
      ZActivityOptions
        .withStartToCloseTimeout(Duration.ofSeconds(30))
        .withScheduleToCloseTimeout(Duration.ofMinutes(10))
        .withTaskQueue(ActivityQueue.FundSubCountersign.queueName)
        .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(3))
    )

  override def startBatchSignCountersignReq(
    param: FundSubBatchActionWorkflowParam
  ): ResponseUnit = {
    val singleCountersignWorkflow = ZWorkflow
      .newChildWorkflowStub[FundSubSingleSignCountersignReqWorkflow](
        ZChildWorkflowOptions
          .withWorkflowId(ZWorkflow.randomUUID.toString)
          .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(3))
          .withWorkflowRunTimeout(FundSubBatchSignCountersignReqWorkflowImpl.SingleCountersignWorkflowRunTimeout)
          .withParentClosePolicy(ParentClosePolicy.PARENT_CLOSE_POLICY_TERMINATE)
      )

    param.batchActionItemIds.map { singleActionId =>
      singleActionId -> ZChildWorkflowStub.execute(
        singleCountersignWorkflow.startSingleSignCountersignReq(FundSubBatchActionItemWorkflowParam(singleActionId))
      )
    }

    ZActivityStub.execute(activities.completeBatchAction(param))
  }

}

object FundSubBatchSignCountersignReqWorkflowImpl {

  private val SingleCountersignWorkflowRunTimeout = Duration.ofMinutes(10)

  lazy val instance =
    WorkflowImpl[FundSubBatchSignCountersignReqWorkflow, FundSubBatchSignCountersignReqWorkflowImpl](
      WorkflowQueue.FundSubCountersign
    )

}
