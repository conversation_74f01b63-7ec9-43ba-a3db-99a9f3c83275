// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dataimport.impl

import java.time.Duration

import zio.temporal.ZRetryOptions
import zio.temporal.activity.{ZActivityStub, ZActivityOptions}
import zio.temporal.failure.ActivityFailure
import zio.temporal.workflow.ZWorkflow

import anduin.workflow.fundsub.dataimport.*
import anduin.workflow.{ActivityQueue, WorkflowImpl, WorkflowQueue}

class SingleDataImportWorkflowImpl extends FundSubSingleDataImportWorkflow {

  private val activities = ZWorkflow
    .newActivityStub[FundSubDataImportActivity](
      ZActivityOptions
        .withStartToCloseTimeout(Duration.ofSeconds(30))
        .withTaskQueue(ActivityQueue.FundSubDataImport.queueName)
        .withRetryOptions(
          ZRetryOptions.default.withMaximumAttempts(3)
        )
    )

  override def importSingle(param: SingleDataImportParam): SingleDataImportResponse = {
    scribe.info(s"Start importSingle workflow for ${param.dataImportId}")

    try {
      val startImportResponse = ZActivityStub.execute(
        activities.startSingleDataImport(StartSingleDataImportParam(param.dataImportId))
      )
      val processDataResponse = ZActivityStub.execute(
        activities.processDataImport(
          ProcessDataImportParam(
            param.dataImportId,
            startImportResponse.formVersionId,
            startImportResponse.mappingVersionIdOpt,
            startImportResponse.templateVersionIdOpt,
            startImportResponse.actor
          )
        )
      )

      SingleDataImportResponse(processDataResponse.status)
    } catch {
      case e: ActivityFailure =>
        scribe.info(s"Catch activity failure ${e.getCause}")
        val markFailedResp =
          ZActivityStub.execute(
            activities.markItemAsFailed(MarkSingleDataImportAsFailedParam(param.dataImportId, e.getCause.getMessage))
          )
        SingleDataImportResponse(markFailedResp.status)
    }

  }

}

object SingleDataImportWorkflowImpl {

  lazy val instance =
    WorkflowImpl[FundSubSingleDataImportWorkflow, SingleDataImportWorkflowImpl](WorkflowQueue.FundSubDataImport)

}
