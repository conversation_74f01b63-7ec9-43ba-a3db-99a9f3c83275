// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.formcomment

import zio.temporal.{activityInterface, activityMethod}

@activityInterface
trait CommentExportActivities {

  @activityMethod
  def initTaskTrackingRecord(params: InitTaskTrackingRecordParams): EmptyResponse

  @activityMethod
  def prepareCommentExportFolders(params: PrepareCommentExportFoldersParams): PrepareCommentExportFoldersResponse

  @activityMethod
  def gettingAllRelatedInvestors(params: GettingAllRelatedInvestorsParams): GettingAllRelatedInvestorsResponse

  @activityMethod
  def exportCommentByInvestor(params: ExportCommentByInvestorParams): ExportCommentByInvestorResponse

  @activityMethod
  def updateTaskTrackingRecord(params: UpdateTaskTrackingRecordParams): EmptyResponse

}
