// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dataextract

import zio.temporal.{activityInterface, activityMethod}

@activityInterface
trait FundSubDataExtractRequestNotificationActivities {

  @activityMethod
  def sendNewDataExtractRequestsReadyForReviewNotificationPerFund(event: NewDataExtractRequestsPerFund): ResponseUnit

  @activityMethod
  def getNewDataExtractRequestsReadyForReview(params: GetDataExtractRequestReadyForReviewEventsParams)
    : GetDataExtractRequestsReadyForReviewResponse

}
