// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.dataexport

import zio.temporal.{activityInterface, activityMethod}

@activityInterface
trait FundSubDataExportActivity {

  @activityMethod
  def processDataExport(param: ProcessDataExportParam): ProcessDataExportResponse

  @activityMethod
  def markItemAsFailed(param: MarkSingleDataExportAsFailedParam): MarkSingleDataExportAsFailResponse

}
