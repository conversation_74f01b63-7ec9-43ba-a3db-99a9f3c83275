// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.invitation

import zio.temporal.{activityInterface, activityMethod}
import anduin.batchaction.ResponseUnit

// Requirement for activity: idempotent and can be compensated
// Activities can be shared between multiple workflows, so that's the design scope
@activityInterface
trait FundSubInvitationActivities {

  @activityMethod
  def startSingleInvitation(param: StartSingleInvitationParam): StartSingleInvitationResponse

  @activityMethod
  def setupUserAndTeam(param: SetupUserAndTeamParam): SetupUserAndTeamResponse

  @activityMethod
  def compensateSetupUserAndTeam(param: SetupUserAndTeamCompensateParam): ResponseUnit

  @activityMethod
  def setUpFolderAndDocuments(param: SetUpFolderAndDocumentsParam): SetUpFolderAndDocumentsResponse

  @activityMethod
  def compensateSetUpFolderAndDocuments(
    param: SetUpFolderAndDocumentsCompensateParam
  ): ResponseUnit

  @activityMethod
  def setUpSignatureModule(param: SetUpSignatureModuleParam): ResponseUnit

  @activityMethod
  def setUpForm(param: SetUpFormParam): SetUpFormResponse

  @activityMethod
  def compensateSetUpForm(param: SetUpFormCompensateParam): ResponseUnit

  @activityMethod
  def setUpFormCommenting(param: SetUpFormCommentingParam): ResponseUnit

  @activityMethod
  def compensateSetUpFormCommenting(param: SetUpFormCommentingCompensateParam): ResponseUnit

  @activityMethod
  def createFundSubModel(param: CreateFundSubModelParam): CreateFundSubModelResponse

  @activityMethod
  def compensateCreateFundSubModel(param: CreateFundSubModelCompensateParam): ResponseUnit

  @activityMethod
  def addOrderDataToDataLake(param: AddOrderDataToDataLakeParam): ResponseUnit

  @activityMethod
  def addOrderDataToDataPipeline(param: AddOrderDataToDataPipelineParam): ResponseUnit

  @activityMethod
  def setUpLpTags(param: SetUpLpTagsParam): ResponseUnit

  @activityMethod
  def setUpAdvisorTags(param: SetUpAdvisorTagsParam): ResponseUnit

  @activityMethod
  def setUpContact(param: SetUpContactParam): ResponseUnit

  @activityMethod
  def compensateSetUpContact(param: SetUpContactCompensateParam): ResponseUnit

  @activityMethod
  def setUpProvidedDocumentTypes(param: SetUpProvidedDocumentTypesParam): SetUpProvidedDocumentTypesResponse

  @activityMethod
  def setUpShareDocumentsWithLp(param: SetUpShareDocumentsWithLpParam): SetUpShareDocumentsWithLpResponse

  @activityMethod
  def setUpImportFromFundData(param: SetUpImportFromFundDataParam): ResponseUnit

  @activityMethod
  def compensateSetUpImportFromFundData(param: SetUpImportFromFundDataCompensateParam): ResponseUnit

  @activityMethod
  def sendEmail(param: SendEmailParam): SendEmailResponse

  @activityMethod
  def createFundSubActivityLog(param: CreateFundSubActivityLogParam): ResponseUnit

  @activityMethod
  def compensateCreateFundSubActivityLog(param: CreateFundSubActivityLogCompensateParam): ResponseUnit

  @activityMethod
  def sendAmplitudeAndZapier(param: SendAmplitudeAndZapierParam): ResponseUnit

  @activityMethod
  def completeSingleInvitation(param: CompleteSingleInvitationParam): CompleteSingleInvitationResponse

  @activityMethod
  def markSingleInvitationFailed(param: MarkSingleInvitationFailedParam): MarkSingleInvitationFailedResponse

  @activityMethod
  def createBatchInviteInvestorActivityLog(param: CreateBatchInviteInvestorActivityLogParam): ResponseUnit

  @activityMethod
  def sendBatchEmailAndCreateBatchFundSubAuditLog(param: SendBatchEmailAndCreateBatchFundSubAuditLogParam): ResponseUnit

  @activityMethod
  def updateSupportingDocsIfNecessary(params: UpdateSupportingDocsParams): ResponseUnit

  @activityMethod
  def completeBatchInvitation(param: MultipleInvitationParam): ResponseUnit

}

object FundSubInvitationActivities {

  final case class DuplicatedLpIdException(message: String) extends RuntimeException {
    override def getMessage: String = message
  }

}
