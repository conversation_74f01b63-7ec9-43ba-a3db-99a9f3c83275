// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow.fundsub.formcomment.impl

import anduin.fundsub.comment.FormCommentEmailService
import anduin.workflow.TemporalWorkflowService
import anduin.workflow.fundsub.formcomment.{
  FundSubFormCommentNotificationActivities,
  SendFormCommentNotificationParams,
  SendFormCommentNotificationResponse
}
import com.anduin.stargazer.service.GondorBackendConfig

final case class FundSubFormCommentNotificationActivitiesImpl(
  backendConfig: GondorBackendConfig,
  formCommentEmailService: FormCommentEmailService
)(
  using val temporalWorkflowService: TemporalWorkflowService
) extends FundSubFormCommentNotificationActivities {

  override def sendFormCommentNotification(
    params: SendFormCommentNotificationParams
  ): SendFormCommentNotificationResponse = {
    temporalWorkflowService.executeTask(
      formCommentEmailService
        .sendNewFormCommentsDigestEmail(1, params.fundSubId)
        .as(SendFormCommentNotificationResponse.defaultInstance),
      "sendFormCommentNotification"
    )
  }

}
