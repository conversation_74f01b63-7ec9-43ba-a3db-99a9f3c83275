[{"pointer": "/entityname", "op": {"op": "add", "path": "/value", "value": "TechSolutions Inc."}, "tpe": "Import", "namespace": "main"}, {"pointer": "/entityinvestortype", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/legalspouse_individuals_subscribernature", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/entityinvestortype1", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/privatefund_entities_subscribernature", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/specify_othertaxexempt_entities_subscribernature", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/specify_other_entities_subscribernature", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/beneficialowner_entities_subscribernature_entity", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/specify_other_beneficialowner_entities_subscribernature", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/individualinvestorname_prefill", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/dob_individual_subscriberinformation", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/othertin1", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/individualinvestorphone", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/individualinvestorphone_alternative", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/individualinvestoremail", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/individualinvestoremail_alternative", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/numberandstreet_applicantaddress", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/city_applicantaddress", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/state_applicantaddress", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/zipcode_applicantaddress", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/country_applicantaddress", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/law_entities_subscriberinformation", "op": {"op": "add", "path": "/value", "value": "Delaware, USA"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/dropdown1", "op": {"op": "add", "path": "/value", "value": "4 Feb"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/integer", "op": {"op": "add", "path": "/value", "value": "Feb"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/entitytin", "op": {"op": "add", "path": "/value", "value": "12-3456789"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/entitytin1", "op": {"op": "add", "path": "/value", "value": "03/10/2010"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/numberandstreet_businessadress", "op": {"op": "add", "path": "/value", "value": "123 Main Street"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/city_businessadress", "op": {"op": "add", "path": "/value", "value": "Anytown"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/state_businessadress", "op": {"op": "add", "path": "/value", "value": "California"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/zipcode_businessadress", "op": {"op": "add", "path": "/value", "value": "12345"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/country_businessadress", "op": {"op": "add", "path": "/value", "value": "United States"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/individualsubscribername_individual_subscriberinformation1", "op": {"op": "add", "path": "/value", "value": "Chief Executive Officer"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/name_primarycontact_entities", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/company_contactinfo_subscriberinformation1", "op": {"op": "add", "path": "/value", "value": "TechSolutions Inc."}, "tpe": "Import", "namespace": "main"}, {"pointer": "/numberandstreet_businessadress1", "op": {"op": "add", "path": "/value", "value": "5678 Beach Boulevard"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/city_businessadress1", "op": {"op": "add", "path": "/value", "value": "Santa Monica"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/state_businessadress1", "op": {"op": "add", "path": "/value", "value": "California"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/zipcode_businessadress1", "op": {"op": "add", "path": "/value", "value": "90401"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/country_businessadress1", "op": {"op": "add", "path": "/value", "value": "United States"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/contactprimaryphone", "op": {"op": "add", "path": "/value", "value": "(08) 9026 5490"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/contactinvestorphone_alternative1", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/contactemail1", "op": {"op": "add", "path": "/value", "value": "<EMAIL>"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/contactemail2", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/additionalcontact", "op": {"op": "add", "path": "/value", "value": [{}]}, "tpe": "Import", "namespace": "main"}, {"pointer": "/bankname_wireinstructions1", "op": {"op": "add", "path": "/value", "value": "Pacific Bank"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/bankaddress", "op": {"op": "add", "path": "/value", "value": "1234 Main Street Los Angeles, CA 90001 United States"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/bankaba_wireinstructions3", "op": {"op": "add", "path": "/value", "value": "PACBUS66XXX"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/accountname_wireinstructions1", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/accountnumber_wireinstructions1", "op": {"op": "add", "path": "/value", "value": "************"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/accountname1_wireinstructions1", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/accountnumber1_wireinstructions1", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio1", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio2", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio3", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio4", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio5", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/multiplecheckbox1", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/multiplecheckbox3", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio9", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio10", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/multiplecheckbox16", "op": {"op": "add", "path": "/value", "value": ["op1"]}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio12", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio13", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio14", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio15", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/multiplecheckbox6", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/textbox4", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/trust_entitiesfinancialinformation", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/specify_trust_entitiesfinancialinformation", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio16", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/name_grantortrustcertificate_entitiesfinancialinformation", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/date_grantortrustcertificate_entitiesfinancialinformation", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/multiplecheckbox4", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/additionalinformation_planinvestor_entitiesfinancialinformation", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/multiplecheckbox5", "op": {"op": "add", "path": "/value", "value": ["op18", "op15", "op2"]}, "tpe": "Import", "namespace": "main"}, {"pointer": "/totalnumber_equityowner_option4", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/textbox8", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/textbox10", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio20", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/textbox13", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/qualifiedpurchaser_options", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/textbox14", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/textbox15", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio21", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio22", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/ifyestob", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio24", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio25", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio26", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/radio27", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/a14_entityfinancialinformation", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/specify_three_a14_entityfinancialinformation", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/nfaid_three_a14_entityfinancialinformation", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/b14_entityfinancialinformation", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/describe_nine_b14_entityfinancialinformation", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/c14_entityfinancialinformation", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/selectfund_delaware1", "op": {"op": "add", "path": "/value", "value": ["delaware_europeanoppo"]}, "tpe": "Import", "namespace": "main"}, {"pointer": "/subscriptionamount_fundone_appendixc1", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/class_fundone_appendixc", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/subscriptionamount_fundthree_appendixc1", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/class_fundthree_appendixc", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/subscriptionamount_fundfive_appendixc1", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/class_fundfive_appendixc", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/subscriptionamount_fundseven_appendixc1", "op": {"op": "add", "path": "/value", "value": "$450,000,000"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/class_fundseven_appendixc", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/selectfundscayman1", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/subscriptionamount_fundtwo_appendixc1", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/class_fundtwo_appendixc", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/subscriptionamount_fundfour_appendixc1", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/class_fundfour_appendixc", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/subscriptionamount_fundsix_appendixc1", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/class_fundsix_appendixc", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/two_appendixc", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/newiss<PERSON>yn", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/canadianinvestoryn", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/signaturepage66", "op": {"op": "add", "path": "/value", "value": "TechSolutions Inc."}, "tpe": "Import", "namespace": "main"}, {"pointer": "/signername_signaturepage", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/signertitle_signaturepage", "op": {"op": "add", "path": "/value", "value": "CEO"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/dateofsignature_signaturepage1", "op": {"op": "add", "path": "/value", "value": "Mar 25, 2024"}, "tpe": "Import", "namespace": "main"}, {"pointer": "/secondsignername_signaturepage1", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/secondsignertitle_signaturepage", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/secondsignerdateofsignature_signaturepage1", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/gpacceptancedate", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/gpname", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/gptitle", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/accountname_individualfatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/dob_individualfatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/placeofbirth_individualfatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/numberandstreet_address_individualfatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/city_address_individualfatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/state_address_individualfatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/zipcode_address_individualfatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/country_address_individualfatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/numberandstreet_mailingaddress_individualfatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/city_mailingaddress_individualfatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/state_mailingaddress_individualfatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/zipcode_mailingaddress_individualfatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/country_mailingaddress_individualfatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/section2_individualfatca", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/ustin_section2_individualfatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/repeatable6", "op": {"op": "add", "path": "/value", "value": [{}]}, "tpe": "Import", "namespace": "main"}, {"pointer": "/individualinvestorname_section4_individualfatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/date_section4_individualfatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/entityname_part1_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/incorporationcountry_part1_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/numberandstreet_address_part1_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/city_address_part1_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/state_address_part1_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/zipcode_address_part1_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/country_address_part1_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/numberandstreet_mailingaddress_part1_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/city_mailingaddress_part1_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/state_mailingaddress_part1_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/zipcode_mailingaddress_part1_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/country_mailingaddress_part1_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/usperson_part2_fatca", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/specify_a_usperson_part2_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/exemption_b_usperson_part2_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/section31_part2_fatca", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/giin_section31_part2_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/section32_part2_fatca", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/a_section32_part2_fatca", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/sponsorname_ia_section32_part2_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/sponsorgiin_ia_section32_part2_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/sponsoringname_iia_section32_part2_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/sponsoringgiin_iia_section32_part2_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/trusteename_b_section32_part2_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/trusteegiin_b_section32_part2_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/for32c", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/section33_part2_fatca", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/status_a_section33_part2_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/criteria_b_section33_part2_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/giin_c_section33_part2_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/sponsoringname_d_section33_part2_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/sponsoringgiin_d_section33_part2_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/repeatable5", "op": {"op": "add", "path": "/value", "value": [{}]}, "tpe": "Import", "namespace": "main"}, {"pointer": "/repeatable7", "op": {"op": "add", "path": "/value", "value": [{}]}, "tpe": "Import", "namespace": "main"}, {"pointer": "/repeatable8", "op": {"op": "add", "path": "/value", "value": [{}]}, "tpe": "Import", "namespace": "main"}, {"pointer": "/section5_part3_fatca", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/section51_part3_fatca", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/b_section51_part3_fatca", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/lawtype_b_section51_part3_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/c_section51_part3_fatca", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/ii_c_section51_part3_fatca", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/repeatable9", "op": {"op": "add", "path": "/value", "value": [{}]}, "tpe": "Import", "namespace": "main"}, {"pointer": "/section52_part3_fatca", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/stockname_section52_part3_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/corporationname_section52_part3_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/criteria_section52_part3_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/repeatable10", "op": {"op": "add", "path": "/value", "value": [{}]}, "tpe": "Import", "namespace": "main"}, {"pointer": "/date1_part3_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/name1_part3_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/title1_part3_fatca", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/partaoptions", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/foroption6_parta_addendumii", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/partboptions", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/foroptionxv", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/individual_partc_affiliationinformation", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/entity_partc_affiliationinformation", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/repeatable11", "op": {"op": "add", "path": "/value", "value": [{}]}, "tpe": "Import", "namespace": "main"}, {"pointer": "/repeatable12", "op": {"op": "add", "path": "/value", "value": [{}]}, "tpe": "Import", "namespace": "main"}, {"pointer": "/canada_accreditedinvestors", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/category_u_accreditedinvestorcertificate", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/date_accreditedinvestorcertificate1", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/name_accreditedinvestorcertificate", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/title_accreditedinvestorcertificate", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/permittedclients", "op": {"op": "add", "path": "/value", "value": []}, "tpe": "Import", "namespace": "main"}, {"pointer": "/date_permittedclientcertificate1", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/name_permittedclientcertificate", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/title_permittedclientcertificate", "op": {"op": "add", "path": "/value", "value": ""}, "tpe": "Import", "namespace": "main"}, {"pointer": "/subscriptioninstructions1", "op": {"op": "add", "path": "/touched", "value": true}, "tpe": "User", "namespace": "main"}, {"pointer": "/generalinformation1", "op": {"op": "add", "path": "/touched", "value": true}, "tpe": "User", "namespace": "main"}, {"pointer": "/sectionv", "op": {"op": "add", "path": "/touched", "value": true}, "tpe": "User", "namespace": "main"}, {"pointer": "/subscriptioninstructions1", "op": {"op": "add", "path": "/touched", "value": true}, "tpe": "User", "namespace": "main"}, {"pointer": "/subscriptioninstructions1", "op": {"op": "add", "path": "/touched", "value": true}, "tpe": "User", "namespace": "main"}]