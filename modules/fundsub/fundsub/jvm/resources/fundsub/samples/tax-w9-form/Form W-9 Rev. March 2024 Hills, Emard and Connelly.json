[{"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Hills, Emard and Connelly"}, "pointer": "/investorname", "tpe": "Import"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/requestfortaxpayeridentificationnumberandcertification", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "other"}, "pointer": "/w9_line3_1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "individual"}, "pointer": "/w9_line3_1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Yes"}, "pointer": "/radio", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "4119 Private Lane"}, "pointer": "/street_permanentaddress", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Albany"}, "pointer": "/w9_city_line7", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Georgia"}, "pointer": "/w9_state_line7", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "31707"}, "pointer": "/w9_zip_line7", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "***********"}, "pointer": "/ssn", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "93-3818741"}, "pointer": "/ein", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "133-__-____"}, "pointer": "/ssn", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "133-81-____"}, "pointer": "/ssn", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "***********"}, "pointer": "/ssn", "tpe": "User"}]