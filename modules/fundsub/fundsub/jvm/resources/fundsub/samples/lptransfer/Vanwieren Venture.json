[{"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/subscriptionbooklet", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["true"]}, "pointer": "/multiplecheckbox3", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/transfereesinformation", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "corporation_notqualified_ownershiptype_transfereesinfo"}, "pointer": "/nonqual_ownership_transferee", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Vanwieren Venture"}, "pointer": "/fullname_investorname_transfereesinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "pointer": "/fullname_primaryinvestor_transfereesinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "United States"}, "pointer": "/country2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "9876 N 54th Street"}, "pointer": "/numberandstreet2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "New York"}, "pointer": "/city2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "9876 N Park Avenye"}, "pointer": "/numberandstreet2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "New York"}, "pointer": "/usstate2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "New York City"}, "pointer": "/city2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "12093-8471"}, "pointer": "/uszipcode2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "ein"}, "pointer": "/tinType1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "12-3412341"}, "pointer": "/ein2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "989767432"}, "pointer": "/telephone_transfereesinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ""}, "pointer": "/email_transfereesinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<EMAIL>"}, "pointer": "/email_transfereesinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "uscitizen_transfereesinfo"}, "pointer": "/usstatus", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/transferorsinformation", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 9000000}, "pointer": "/amounttransferred_transferorsinfopage", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "individual_notqualified_ownershiptype_transfereesinfo"}, "pointer": "/nonqual_ownership_transferee2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "pointer": "/fullname_investorname_transferorsinfopage", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "pointer": "/fullname_primaryinvestor_transferorsinfopage", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": null}, "pointer": "/dateofbirth_transferorsinfopage", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "1991-02-13"}, "pointer": "/dateofbirth_transferorsinfopage", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "ssn"}, "pointer": "/tinType", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "***********"}, "pointer": "/ssn", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "dateofgift_reasonfortransfer_transferorsinfo"}, "pointer": "/reasonfortransfer_transferorsinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "reregistration_reasonfortransfer_transferorsinfo"}, "pointer": "/reasonfortransfer_transferorsinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/subscriptionbooklet", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/transfereesinformation", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/transferorsinformation", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "1991-02-13"}, "pointer": "/dateofbirth_transferorsinfopage", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "pointer": "/fullname_investorname_transferorsinfopage", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "pointer": "/fullname_primaryinvestor_transferorsinfopage", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/subscriptionbooklet", "tpe": "User"}]