[{"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/subscriptionbooklet", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/subscriptionbooklet", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["true"]}, "pointer": "/multiplecheckbox3", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/transfereesinformation", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "individual_notqualified_ownershiptype_transfereesinfo"}, "pointer": "/nonqual_ownership_transferee", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "pointer": "/fullname_investorname_transfereesinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "pointer": "/fullname_primaryinvestor_transfereesinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": null}, "pointer": "/dateofbirth_transfereesinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": null}, "pointer": "/dateofbirth_transfereesinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "1999-11-09"}, "pointer": "/dateofbirth_transfereesinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ""}, "pointer": "/numberandstreet2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "United States"}, "pointer": "/country2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "123 N 45th Street"}, "pointer": "/numberandstreet2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "New York City"}, "pointer": "/city2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "New York"}, "pointer": "/usstate2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "10923-8412"}, "pointer": "/uszipcode2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "ssn"}, "pointer": "/tinType1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "***********"}, "pointer": "/ssn2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "9897675454"}, "pointer": "/telephone_transfereesinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ""}, "pointer": "/email_transfereesinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<EMAIL>"}, "pointer": "/email_transfereesinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "uscitizen_transfereesinfo"}, "pointer": "/usstatus", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/transferorsinformation", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 800000}, "pointer": "/amounttransferred_transferorsinfopage", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "individual_notqualified_ownershiptype_transfereesinfo"}, "pointer": "/nonqual_ownership_transferee2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "pointer": "/fullname_investorname_transferorsinfopage", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "pointer": "/fullname_primaryinvestor_transferorsinfopage", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": null}, "pointer": "/dateofbirth_transferorsinfopage", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "1992-11-10"}, "pointer": "/dateofbirth_transferorsinfopage", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "ssn"}, "pointer": "/tinType", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "***********"}, "pointer": "/ssn", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "dateofgift_reasonfortransfer_transferorsinfo"}, "pointer": "/reasonfortransfer_transferorsinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": null}, "pointer": "/specify_dateofgift_reasonfortransfer_transferorsinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "2023-10-03"}, "pointer": "/specify_dateofgift_reasonfortransfer_transferorsinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/subscriptionbooklet", "tpe": "User"}]