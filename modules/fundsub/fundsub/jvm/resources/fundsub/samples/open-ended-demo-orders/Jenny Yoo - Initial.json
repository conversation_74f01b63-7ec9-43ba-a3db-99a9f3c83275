[{"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/fundselection", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "initial"}, "pointer": "/investmenttype", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "onshorefeeder_fundselection"}, "pointer": "/fundselection_check", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/individualsubscribings", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "cash_subscriptionamount_individualsubscribing"}, "pointer": "/cash_subscriptionamount_individualsubscribing", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 3000000}, "pointer": "/commitmentamount_indi", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "pointer": "/asa_fullname_investorname_generalinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "asa_naturalperson_investortype"}, "pointer": "/asa_investortype_investortype", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "yes"}, "pointer": "/nonuspersonsection7701a30_individualssubscribing", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "pointer": "/individualname4", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ""}, "pointer": "/email2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<EMAIL>"}, "pointer": "/email2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "United States"}, "pointer": "/country_part19", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "5 Golf Ridge Dr"}, "pointer": "/numberandstreet_part19", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON><PERSON><PERSON>"}, "pointer": "/city_part19", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Texas"}, "pointer": "/state_part19", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "77304"}, "pointer": "/zipcode_part19", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "1"}, "pointer": "/textbox14", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "936"}, "pointer": "/textbox15", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "4416193"}, "pointer": "/textbox16", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["no contact"]}, "pointer": "/multiplecheckbox", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/schedulei", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": null}, "pointer": "/date2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "2024-09-27"}, "pointer": "/date2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": null}, "pointer": "/percentage2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "2024-10-01"}, "pointer": "/percentage2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON> "}, "pointer": "/repeatable16/value/0/securityname_schedulei_subscriptioninkindagreement", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ""}, "pointer": "/repeatable16/value/0/currenshares", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "pointer": "/repeatable16/value/0/securityname_schedulei_subscriptioninkindagreement", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "100"}, "pointer": "/repeatable16/value/0/currenshares", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "op2"}, "pointer": "/repeatable16/value/0/radio1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "123"}, "pointer": "/repeatable16/value/0/textbox2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 3000000}, "pointer": "/repeatable16/value/0/originalprincipalamount_schedulei_subscriptioninkindagreement", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": null}, "pointer": "/repeatable16/value/0/maturitydate_schedulei_subscriptioninkindagreement", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "2024-09-30"}, "pointer": "/repeatable16/value/0/maturitydate_schedulei_subscriptioninkindagreement", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "No"}, "pointer": "/repeatable16/value/0/radio6", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "pointer": "/repeatable16/value/0/holderofrecord_schedulei_subscriptioninkindagreement", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "DTC"}, "pointer": "/repeatable16/value/0/settlelocation_schedulei_subscriptioninkindagreement", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "123"}, "pointer": "/repeatable16/value/0/deliveringcustodian_schedulei_subscriptioninkindagreement", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "123"}, "pointer": "/repeatable16/value/0/deliveringcustodianaccount_schedulei_subscriptioninkindagreement", "tpe": "User"}]