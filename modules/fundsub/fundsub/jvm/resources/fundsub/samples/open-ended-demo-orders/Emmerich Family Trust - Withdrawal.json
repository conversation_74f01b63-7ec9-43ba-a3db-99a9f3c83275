[{"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/fundselection", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "withdrawal"}, "pointer": "/investmenttype", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "onshorefeeder_fundselection"}, "pointer": "/fundselection_check", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/formofrequestforwithdrawaloflimitedpartnershipinterest", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Emmerich Family Trust"}, "pointer": "/fullname_investor_withdrawalform", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "other_investortype"}, "pointer": "/asa_investortype_investortype2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["fullamount_withdrawalform"]}, "pointer": "/fullamount_withdrawalform", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 750000}, "pointer": "/previousamount", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["nextavailable_withdrawaldate_withdrawalform"]}, "pointer": "/nextavailable_withdrawaldate_withdrawalform", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Series I Interest"}, "pointer": "/seriesofinterest_additionalform1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "pointer": "/fullname_correspondence_withdrawalform", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "a@b.c"}, "pointer": "/email_correspondence_withdrawalform", "tpe": "User"}]