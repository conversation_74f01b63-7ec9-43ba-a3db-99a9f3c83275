[{"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Cayman Islands"}, "pointer": "/whichfundarea", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["delaware_debtfund"]}, "pointer": "/selectfund_delaware", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 2100000}, "pointer": "/subscriptionamount_fundone_appendixc", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 0}, "pointer": "/subscriptionamount_fundseven_appendixc_delaware", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["cayman_debtfund", "cayman_financialservices", "cayman_<PERSON>oppo"]}, "pointer": "/selectfundscayman", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 350000}, "pointer": "/subscriptionamount_fundtwo_appendixc", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 400000}, "pointer": "/subscriptionamount_fundfour_appendixc", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 100000}, "pointer": "/subscriptionamount_fundsix_appendixc", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 850000}, "pointer": "/subscriptionamount_fundseven_appendixc_cayman", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Tenants in Common"}, "pointer": "/entityinvestortype", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Yes"}, "pointer": "/singleindividual_married<PERSON>ot", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "No"}, "pointer": "/legalspouse_individuals_subscribernature", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": [{"textbox2": {"value": "<PERSON>"}}]}, "pointer": "/repeatable", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "*********"}, "pointer": "/specify_othertaxexempt_entities_subscribernature", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "353256345634563"}, "pointer": "/specify_other_entities_subscribernature", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Watson 1990 Family Trust"}, "pointer": "/entityname", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Hills, Emard and Connelly"}, "pointer": "/individualinvestorname_new", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "3435345353"}, "pointer": "/specify_other_beneficialowner_entities_subscribernature", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "yes"}, "pointer": "/usperson3", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Hills, Emard and Connelly"}, "pointer": "/individualinvestorname_prefill", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "1986-08-29"}, "pointer": "/dob_individual_subscriberinformation", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "itin"}, "pointer": "/asa_tin_generalinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "***********"}, "pointer": "/itin", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "************"}, "pointer": "/individualinvestorphone", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "************"}, "pointer": "/individualinvestorphone_alternative", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<EMAIL>"}, "pointer": "/individualinvestoremail", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<EMAIL>"}, "pointer": "/individualinvestoremail_alternative", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "United States"}, "pointer": "/country_residenceaddress_generalinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "4119 Private Lane"}, "pointer": "/street_residenceaddress_generalinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Albany"}, "pointer": "/city_residenceaddress_generalinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Georgia"}, "pointer": "/usstate_residenceaddress_generalinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "31707"}, "pointer": "/uszip_residenceaddress_generalinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "4119 Private Lane, Albany, Georgia, 31707, United States"}, "pointer": "/combined_address4", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "USA"}, "pointer": "/law_entities_subscriberinformation", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "993-77 0690"}, "pointer": "/itin1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "2014-01-02"}, "pointer": "/entitytin1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "United States"}, "pointer": "/country_businessadress", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "1526 Brookview Drive"}, "pointer": "/numberandstreet_businessadress", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Hempstead"}, "pointer": "/city_businessadress", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Texas"}, "pointer": "/usstate_placeofbusiness_generalinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "77445"}, "pointer": "/uszip_placeofbusiness_generalinfo", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Spouse"}, "pointer": "/individualsubscribername_individual_subscriberinformation1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "pointer": "/name_primarycontact_entities", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "United States"}, "pointer": "/country", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "4126 Cemetery Street"}, "pointer": "/numberandstreet", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "San Francisco"}, "pointer": "/city", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "California"}, "pointer": "/usstate", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "94107"}, "pointer": "/uszipcode", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "************"}, "pointer": "/contactprimaryphone", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "************"}, "pointer": "/contactinvestorphone_alternative1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<EMAIL>"}, "pointer": "/contactemail1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "American Express"}, "pointer": "/bankname_wireinstructions1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "4718 <PERSON> Drive, Florence, 29501, South Carolina"}, "pointer": "/bankaddress", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "ABA Routing Number"}, "pointer": "/typeofidentifier1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "dfgfdg4_534"}, "pointer": "/swiftcode_wireinstructions1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "*********"}, "pointer": "/bankaba_wireinstructions3", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Hills, Emard and Connelly"}, "pointer": "/accountname_wireinstructions1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "3468 1390 9903 592"}, "pointer": "/accountnumber_wireinstructions1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON><PERSON>"}, "pointer": "/accountname1_wireinstructions1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "3441 5225 1023 507"}, "pointer": "/accountnumber1_wireinstructions1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "$5,000,000-$9,999,999"}, "pointer": "/radio1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "20% - 30%"}, "pointer": "/radio2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Yes"}, "pointer": "/radio3", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Yes"}, "pointer": "/radio4", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "No"}, "pointer": "/radio5", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["op3"]}, "pointer": "/multiplecheckbox1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "No"}, "pointer": "/multiplecheckbox3", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "$25,000,000-$99,999,999"}, "pointer": "/radio9", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "30% - 40%"}, "pointer": "/radio10", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["file1"]}, "pointer": "/filegroup2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Applicable"}, "pointer": "/radio11", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "No"}, "pointer": "/radio12", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Yes"}, "pointer": "/radio13", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "No"}, "pointer": "/radio14", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "No"}, "pointer": "/multiplecheckbox6", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 10}, "pointer": "/textbox4", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Grantor Retained Annuity Trust"}, "pointer": "/multiplecheckbox7", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "No"}, "pointer": "/radio16", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Yes"}, "pointer": "/radio17", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "fete<PERSON><PERSON><PERSON>"}, "pointer": "/additionalinformation_planinvestor_entitiesfinancialinformation", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 10}, "pointer": "/totalnumber_equityowner_option4", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["Check if you want to attach a separate sheet including the list of all equity owners."]}, "pointer": "/multiplecheckbox8", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 10}, "pointer": "/textbox8", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Hoang test 123"}, "pointer": "/textbox12", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 3}, "pointer": "/textbox10", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["Check if you want to attach a separate sheet including the list of all participants."]}, "pointer": "/multiplecheckbox10", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Yes"}, "pointer": "/radio20", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 2}, "pointer": "/textbox13", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "aqualifiedpurchaserstatus_entitiesfinancialinformation"}, "pointer": "/qualifiedpurchaser_options", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Not Applicable"}, "pointer": "/radio27", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Yes"}, "pointer": "/nominee", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "optionc_none"}, "pointer": "/radio", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "onea14_entityfinancialinformation"}, "pointer": "/a14_entityfinancialinformation", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "sixb14_entityfinancialinformation"}, "pointer": "/b14_entityfinancialinformation", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "34435543646"}, "pointer": "/c14_entityfinancialinformation", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["I confirm that I have read, understand and agree to the above. *"]}, "pointer": "/multiplecheckbox2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 0}, "pointer": "/subscriptionamount_fundone_appendixc1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["cayman_debtfund", "cayman_financialservices", "cayman_<PERSON>oppo"]}, "pointer": "/selectfundscayman1_appendixc", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 350000}, "pointer": "/subscriptionamount_fundtwo_appendixc1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 400000}, "pointer": "/subscriptionamount_fundfour_appendixc1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 100000}, "pointer": "/subscriptionamount_fundsix_appendixc1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Yes"}, "pointer": "/two_appendixc", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "yesthree_appendixc"}, "pointer": "/newiss<PERSON>yn", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "No"}, "pointer": "/canadianinvestoryn", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["file1"]}, "pointer": "/filegroup1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Hills, Emard and Connelly"}, "pointer": "/accountname_individualfatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "1986-08-29"}, "pointer": "/dob_individualfatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Nebraska, USA"}, "pointer": "/placeofbirth_individualfatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "United States"}, "pointer": "/country_address_individualfatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "4119 Private Lane"}, "pointer": "/numberandstreet_address_individualfatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Albany"}, "pointer": "/city_address_individualfatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Georgia"}, "pointer": "/state_address_individualfatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "31707"}, "pointer": "/zipcode_address_individualfatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "b"}, "pointer": "/section2_individualfatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "454353453453"}, "pointer": "/ustin_section2_individualfatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": [{"country_section3_individualfatca": {"value": "Sweden"}, "haveTin": {"value": "No"}, "specify_section3_individualfatca": {"value": "I will apply or have applied for a TIN but have not yet received it"}, "taxnumber_section3_individualfatca": {"value": "N/A"}, "taxtypecountry_section3_individualfatca": {"value": "N/A"}}]}, "pointer": "/repeatable6", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["true"]}, "pointer": "/moreThan3Tax1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "USA"}, "pointer": "/incorporationcountry_part1_fatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "c"}, "pointer": "/usperson_part2_fatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "op3"}, "pointer": "/section3Q", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "dsection31_part2_fatca"}, "pointer": "/section31_part2_fatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "2353252532"}, "pointer": "/giin_section31_part2_fatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "c"}, "pointer": "/section32_part2_fatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "op1"}, "pointer": "/a_section32_part2_fatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "e"}, "pointer": "/section33_part2_fatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "op2"}, "pointer": "/section33e", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["true"]}, "pointer": "/moreThan3Tax", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "op1"}, "pointer": "/section5_part3_fatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "c"}, "pointer": "/section51_part3_fatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "tenb_section51_part3_fatca"}, "pointer": "/b_section51_part3_fatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "324324234343"}, "pointer": "/lawtype_b_section51_part3_fatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "op2"}, "pointer": "/c_section51_part3_fatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "c"}, "pointer": "/ii_c_section51_part3_fatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["true"]}, "pointer": "/moreThan10Con", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "b"}, "pointer": "/section52_part3_fatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "43546546656456456"}, "pointer": "/criteria_section52_part3_fatca", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "2342345324535345345"}, "pointer": "/foroption6_parta_addendumii", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["xiv_partb_affiliationinformation", "xiii_partb_affiliationinformation", "xii_partb_affiliationinformation"]}, "pointer": "/partboptions", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "2324242421"}, "pointer": "/foroptionxv", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "oneindividual_partc_affiliationinformation"}, "pointer": "/individual_partc_affiliationinformation", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "threeentity_partc_affiliationinformation"}, "pointer": "/entity_partc_affiliationinformation", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["I confirm that I have read, understand and agree to the above. *"]}, "pointer": "/multiplecheckbox11", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["accreditedinvestor"]}, "pointer": "/options", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Hills, Emard and Connelly"}, "pointer": "/syncName", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["file1"]}, "pointer": "/forsubscriptionagreement", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["A separate sheet to provide the full list of jurisdictions of tax residency", "Evidence proving that the Subscriber has been voluntarily surrendered its U.S. citizenship"]}, "pointer": "/forfatcacrsselfcertification", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["file1", "file2", "file3"]}, "pointer": "/subscriptiondocuments", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["file1"]}, "pointer": "/filegroup", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Hills, Emard and Connelly"}, "pointer": "/individualinvestorname", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Hills, Emard and Connelly"}, "pointer": "/investorname", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Hills, Emard and Connelly"}, "pointer": "/investorname1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Hills, Emard and Connelly"}, "pointer": "/investorname2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 850000}, "pointer": "/commitmentamountforsetup", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["accreditedinvestor"]}, "pointer": "/options2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": [{"companyname1_partd_affiliationinformation1": {"value": "324234324324"}, "multiplecheckbox14": {"value": ["executiveofficer_name1_partd_affiliationinformation", "director_name1_partd_affiliationinformation", "materiallysupportedperson_name1_partd_affiliationinformation"]}, "name1_partd_affiliationinformation1": {"value": "32432432423"}}]}, "pointer": "/repeatable13", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": [{"companycountry1_parte_affiliationinformation1": {"value": "United States"}, "companyname1_parte_affiliationinformation1": {"value": "Hoang test 123"}, "multiplecheckbox15": {"value": ["director_name1_parte_affiliationinformation", "materiallysupportedperson_name1_parte_affiliationinformation"]}, "name1_parte_affiliationinformation1": {"value": "<PERSON><PERSON>"}, "tin1_parte_affiliationinformation1": {"value": "3424234234"}}]}, "pointer": "/repeatable14", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/subscriptioninstructions1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/generalinformation1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/iifinancialinformationindividuals", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/subscriptioninstructions4", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/sectionv", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/subscriptioninstructions5", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/appendixe", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/addendumiiaffiliationinformation", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/subscriptioninstructions7", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/suitabilityquestionnaire", "tpe": "User"}]