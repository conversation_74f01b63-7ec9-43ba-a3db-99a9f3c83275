[{"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 100000}, "pointer": "/textbox", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "tic_interestbeheld_subscriptionagreement"}, "pointer": "/multiplecheckbox1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Emmerich Family Trust"}, "pointer": "/jointname", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "United States"}, "pointer": "/country11", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "764 Marconi St."}, "pointer": "/street_contactinfo3", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "764 Marconi St."}, "pointer": "/street_contactinfo3", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "764 Marconi St."}, "pointer": "/street_contactinfo4", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "764 Marconi St."}, "pointer": "/street_contactinfo4", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Albany"}, "pointer": "/city_contactinfo3", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Georgia"}, "pointer": "/permanentstate_parta_investorquestionnaire3", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "31707"}, "pointer": "/permanentzipcode_parta_investorquestionnaire3", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "31707"}, "pointer": "/permanentzipcode_parta_investorquestionnaire3", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "************"}, "pointer": "/phone_questionnaire1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "************"}, "pointer": "/phone_questionnaire1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<EMAIL>"}, "pointer": "/contactname_questionnaire2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<EMAIL>"}, "pointer": "/contactname_questionnaire2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "1986-08-29"}, "pointer": "/textbox11", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ""}, "pointer": "/textbox12", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "United States of America"}, "pointer": "/textbox13", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Georgia, United States"}, "pointer": "/textbox12", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Georgia, United States"}, "pointer": "/textbox12", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "CFO"}, "pointer": "/textbox14", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "988-313-8378"}, "pointer": "/textbox9", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/wiredeliveryinstructionsfordistributions", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "American Express"}, "pointer": "/textbox17", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "American Express"}, "pointer": "/textbox17", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "4718 <PERSON> Drive, Florence, 29501, South Carolina"}, "pointer": "/textbox18", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "4718 <PERSON> Drive, Florence, 29501, South Carolina"}, "pointer": "/textbox18", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "(213) 555-5678"}, "pointer": "/textbox19", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "(213) 555-5678"}, "pointer": "/textbox19", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "108703758"}, "pointer": "/sort", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "108703758"}, "pointer": "/sort", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Emmerich Family Trust"}, "pointer": "/textbox22", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Emmerich Family Trust"}, "pointer": "/textbox22", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "3468 1390 9903 592"}, "pointer": "/textbox23", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "3468 1390 9903 592"}, "pointer": "/textbox23", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON><PERSON>"}, "pointer": "/textbox24", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON><PERSON>"}, "pointer": "/textbox24", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "3441 5225 1023 507"}, "pointer": "/textbox25", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/primarycontact1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON><PERSON>"}, "pointer": "/textbox32", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON><PERSON>"}, "pointer": "/textbox32", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "UBS Group AG"}, "pointer": "/textbox33", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "UBS Group AG"}, "pointer": "/textbox33", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "CFO"}, "pointer": "/textbox34", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "CFO"}, "pointer": "/textbox34", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "(312) 555-8364"}, "pointer": "/textbox36", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Self"}, "pointer": "/textbox38", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "none"}, "pointer": "/radio17", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/investorstatus", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "no"}, "pointer": "/radio3", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "no"}, "pointer": "/radio4", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "no"}, "pointer": "/radio5", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["Confirm11"]}, "pointer": "/multiplecheckbox34", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "N/A"}, "pointer": "/textarea", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "no"}, "pointer": "/radio7", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "noneoftheabove"}, "pointer": "/radio8", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/q10", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "United States of America"}, "pointer": "/country22", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "yes"}, "pointer": "/radio15", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "yes"}, "pointer": "/radio18", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page3", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page4", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page5", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page6", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page7", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/exhibitfriskfactors", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "1986-08-29"}, "pointer": "/textbox11", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "1986-08-29"}, "pointer": "/textbox11", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 5000000}, "pointer": "/textbox", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Emmerich Family Trust"}, "pointer": "/jointname", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page3", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/q10", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/wiredeliveryinstructionsfordistributions", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ""}, "pointer": "/textbox17", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ""}, "pointer": "/textbox18", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "(213) 555-5678"}, "pointer": "/textbox19", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ""}, "pointer": "/textbox19", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ""}, "pointer": "/textbox43", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ""}, "pointer": "/sort", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ""}, "pointer": "/textbox22", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ""}, "pointer": "/textbox23", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ""}, "pointer": "/textbox24", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ""}, "pointer": "/textbox25", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ""}, "pointer": "/textbox17", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "3441 5225 1023 507"}, "pointer": "/textbox25", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/primarycontact1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page", "tpe": "User"}]