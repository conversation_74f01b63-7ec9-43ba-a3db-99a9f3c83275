[{"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page3", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page4", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page5", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page6", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page7", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/exhibitfriskfactors", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 6500000}, "pointer": "/textbox", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 6500000}, "pointer": "/textbox", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ["privatefund"]}, "pointer": "/multiplecheckbox2", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": ""}, "pointer": "/firstname", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Jenkins Family Office"}, "pointer": "/firstname", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Jenkins Family Office"}, "pointer": "/firstname", "tpe": "User"}]