[{"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/touched", "value": true}, "pointer": "/page1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": 2500000}, "pointer": "/textbox", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "individual"}, "pointer": "/radio16", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "individual"}, "pointer": "/multiplecheckbox1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "<PERSON>"}, "pointer": "/firstname1", "tpe": "User"}, {"namespace": "main", "op": {"op": "add", "path": "/value", "value": "Hills"}, "pointer": "/lastname1", "tpe": "User"}]