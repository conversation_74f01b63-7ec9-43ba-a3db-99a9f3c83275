// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.model

import anduin.fundsub.cue.model.CueJsonFundSubTypes.SubscriptionDataJson
import anduin.fundsub.cue.model.CueSchemaFundSubTypes.SubscriptionDataSchema
import anduin.protobuf.fundsub.datainterface.subscription.SubscriptionSchemaData

trait SubscriptionSchemaDataTrait { self: SubscriptionSchemaData =>

  val toSubscriptionDataSchema: Option[SubscriptionDataSchema] = {
    for {
      json <- self.data
      schema <- SubscriptionDataJson(json).toCueSchema.toOption
    } yield schema
  }

}
