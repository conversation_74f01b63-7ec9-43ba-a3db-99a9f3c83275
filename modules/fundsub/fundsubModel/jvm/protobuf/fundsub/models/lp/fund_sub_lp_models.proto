syntax = "proto3";

package anduin.protobuf.fundsub.models.lp;

import "anduin/form/form_version_id.proto";
import "date_time.proto";
import "dynamicform/form_data.proto";
import "fundsub/commitment.proto";
import "fundsub/lp_info.proto";
import "fundsub/models.proto";
import "fundsub/models/submission/version/state.proto";
import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.models.lp"
  single_file: true
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.signature.SignatureRequestId"
  import: "anduin.model.id.*"
  import: "anduin.id.docrequest.DocRequestId"
  import: "anduin.id.fundsub.*"
  import: "anduin.id.fundsub.ria.FundSubRiaGroupId"
  import: "anduin.id.form.FormVersionId"
  import: "anduin.form.id.FormVersionIdTypeMapper.given"
  import: "anduin.fundsub.models.lp.FundSubLpRestrictedModelTrait"
  import: "scalapb.TypeMapper"
  preamble: "trait SubmissionUndoneTypeOptCompanion {"
  preamble: "  implicit val mapper: TypeMapper[SubmissionUndoneTypeOpt, SubmissionUndoneType] = {"
  preamble: "    TypeMapper[SubmissionUndoneTypeOpt, SubmissionUndoneType] { model =>"
  preamble: "      model.tpe"
  preamble: "    } {"
  preamble: "      SubmissionUndoneTypeOpt(_)"
  preamble: "    }"
  preamble: "  }"
  preamble: "}"

  preamble: "trait FundSubLpStateTrait {"
  preamble: "  def lpStatusAndTime: LpStatusAndTime"

  preamble: "  def getLpStatus: anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus = lpStatusAndTime.lpStatus"

  preamble: "  def getLpStatusesAfterThis: List[anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus] = {"
  preamble: "    val thisLpStatus = getLpStatus"
  preamble: "    List("
  preamble: "      anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus.LPNotStarted,"
  preamble: "      anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus.LPInProgress,"
  preamble: "      anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus.LPFilledForm,"
  preamble: "      anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus.LPRequestedSignature,"
  preamble: "      anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus.LPPendingReview,"
  preamble: "      anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus.LPFormReviewed,"
  preamble: "      anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus.LPSubmitted,"
  preamble: "      anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus.LPCompleted"
  preamble: "    ).dropWhile(_ != thisLpStatus)"
  preamble: "  }"
  preamble: ""
  preamble: "}"
};

message SubmissionUndoneTypeOpt {
  option (scalapb.message).type = "SubmissionUndoneType";
  option (scalapb.message).companion_extends = "SubmissionUndoneTypeOptCompanion";
  SubmissionUndoneType tpe = 1;
}

enum SubmissionUndoneType {
  AnduinAdminRequestChange = 0;
  FundAdminRequestChange = 1;
  LpCancelSigning = 2;
}

message LpStatusAndTime {
  anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus lpStatus = 1;
  InstantMessage started_at = 2 [(scalapb.field).type = "java.time.Instant"];
}

message FundSubLpState {
  reserved 6, 8;
  option (scalapb.message).extends = "FundSubLpStateTrait";

  string lp = 1 [(scalapb.field).type = "FundSubLpId"];
  InstantMessage invited_at = 2 [(scalapb.field).type = "java.time.Instant"];
  string invited_by = 3 [(scalapb.field).type = "UserId"];
  LpStatusAndTime lp_status_and_time = 4 [(scalapb.field).no_box = true];
  bool is_removed = 5;
  repeated FundSubFile submitted_docs = 7;
  repeated FundSubFile distributed_countersigned_docs = 9;
  repeated FundSubFile pending_countersigned_docs = 10;
  map<string, string> extra_infos = 11;
  bool pending_notified = 12;
  string submission_undone_by = 13 [(scalapb.field).type = "Option[UserId]"];
  SubmissionUndoneTypeOpt submission_undone_type = 14;
  string documents_mark_as_reviewed_by = 15 [(scalapb.field).type = "Option[UserId]"];
  string documents_mark_review_skipped_by = 16 [(scalapb.field).type = "Option[UserId]"];
  string subscription_marked_as_complete_by = 18 [(scalapb.field).type = "Option[UserId]"];
  anduin.fundsub.submission.version.RequestedChanges last_request_change = 17;
}

message FundSubLpLockStatusModel {
  string fs_lp_lock_id = 1 [(scalapb.field).type = "FundSubItoolLpLockId"];
  InstantMessage last_updated_at = 2 [(scalapb.field).type = "java.time.Instant"];
  string last_updated_by = 3 [(scalapb.field).type = "Option[UserId]"];
  bool is_locked = 4;
}

message FundSubLpNoteModel {
  string fs_lp_note_id = 1 [(scalapb.field).type = "FundSubItoolLpNoteId"];
  InstantMessage last_updated_at = 2 [(scalapb.field).type = "java.time.Instant"];
  string last_updated_by = 3 [(scalapb.field).type = "UserId"];
  string note = 4;
}

message FundSubLpModel {
  reserved 4, 14, 15, 18;

  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string main_lp = 2 [(scalapb.field).type = "UserId"];
  repeated string collaborators = 3 [(scalapb.field).type = "UserId"];
  string team_id = 5 [(scalapb.field).type = "Option[TeamId]"];
  repeated string attached_docs = 6 [(scalapb.field).type = "FileId"];
  repeated string bounced_email_users = 7 [(scalapb.field).type = "UserId"];

  InstantMessage last_activity_at = 8 [(scalapb.field).type = "java.time.Instant"];
  LpOrderType order_type = 9;
  string firm_name = 10;

  InstantMessage added_at = 11 [(scalapb.field).type = "java.time.Instant"];
  InvestorFormSetting form_setting = 12;
  string custom_id = 13;
  S3FormChangeInfo form_change_info = 17;

  FundSubLpState lp_state = 16;

  map<string, anduin.protobuf.fundsub.commitment.LpCommitment> commitments = 26 [(scalapb.field).key_type = "InvestmentFundId"];
  string doc_request_id_opt = 19 [(scalapb.field).type = "Option[DocRequestId]"];
  int32 last_request_change_version_id = 20;
  bool synced_firm_name_from_form = 21;
  string status_history_id = 22 [(scalapb.field).type = "Option[ActivityLogId]"];
  string duplicated_from_lp_id_opt = 23 [(scalapb.field).type = "Option[FundSubLpId]"];
  string creator_opt = 24 [(scalapb.field).type = "Option[UserId]"];
  map<string, string> metadata = 25;
  string advisor_group_id_opt = 27 [(scalapb.field).type = "Option[FundSubRiaGroupId]"];
}

message FundSubLpRestrictedModel {
  option (scalapb.message).companion_extends = "FundSubLpRestrictedModelTrait";

  reserved 2, 4, 5, 6, 7, 10, 14, 23, 24, 25, 21, 39;

  string fund_sub_lp_restricted_id = 1 [(scalapb.field).type = "FundSubLpRestrictedId"];

  repeated string form_ids = 27 [(scalapb.field).type = "FundSubLpFormIdTrait"];

  map<string, string> form_files = 3 [(scalapb.field) = {
    key_type: "FileId"
    value_type: "FileId"
  }];

  FundSubSignatureType signature_type = 8;
  repeated string viewed_sections = 9;

  repeated string subscription_doc_signature_request_ids = 11 [(scalapb.field).type = "SignatureRequestId"];
  repeated string counter_signature_request_ids = 15 [(scalapb.field).type = "SignatureRequestId"];
  repeated string additional_signature_request_ids = 18 [(scalapb.field).type = "SignatureRequestId"];
  repeated string completed_signature_request_ids = 19 [(scalapb.field).type = "SignatureRequestId"];

  LastChangeInfo last_change_info = 12;
  repeated string users_viewed_signing_instruction = 13 [(scalapb.field).type = "UserId"];

  // Pre-calculated lp signature blocks when filling values into form files.
  repeated FundSubSignatureBlockInfo lp_signature_blocks = 16;
  // Pre-calculated gp signature blocks when filling values into form files.
  repeated FundSubSignatureBlockInfo gp_signature_blocks = 17;
  // Emails assigned to these signing roles must be unique (different from other roles')
  repeated string unique_email_signing_roles = 30 [(scalapb.field).collection_type = "Set"];

  repeated string users_viewed_onboarding_instruction = 20 [(scalapb.field).type = "UserId"];
  repeated RequiredSupportingDocGroupInfo required_doc_group_info = 22;

  // New form
  anduin.form.FormVersionIdMessage form_version_id_opt = 26 [(scalapb.field).type = "FormVersionId"];

  // Custom investor ID for importing purpose
  string import_investor_id = 28;

  bool lp_edited_form = 29;

  // LP Profile
  //// Track when submitted subscription data is saved to profile
  repeated SaveProfileFromSubscriptionEvent save_profile_main_form_events = 31;
  repeated SaveProfileFromSupportingFormEvent save_profile_supporting_form_events = 40;
  //// Track when users dismiss the suggestion to save subscription data to profile
  repeated DismissSaveProfileSuggestionEvent dismiss_save_profile_suggestion_events = 32;
  // Track if User skip onboarding flow
  repeated SkipOnboardingFlowEvent skip_onboarding_flow_events = 33;
  // Track the invitation type of LP
  optional anduin.protobuf.flow.fundsub.admin.lpdashboard.LpInvitationType invitationTypeOpt = 34;
  // Track if User have seen disclaimer modal when save data to Investment entity
  repeated SeenDisclaimerWhenSaveDataToProfileEvent seen_disclaimer_when_save_data_to_profile_events = 35;
  // Track if User just saved documents to Investment Entity using call-out box to not show the call-out box again
  repeated UserShouldNotShowSaveDocSuggestion users_should_not_show_save_doc_suggestion = 36;
  // Reset when change IA investment entity or new document uploaded
  bool is_lp_documents_auto_saved_to_profile = 37;
  // Mark seen autofill tour user
  repeated string seen_auto_fill_tour_users = 38 [(scalapb.field).type = "UserId"];
  repeated UserDismissNameMismatchAlertEvent user_dismiss_name_mismatch_events = 41;
}

message RecordTypeUnion {
  FundSubLpModel _FundSubLpModel = 1;
  FundSubLpRestrictedModel _FundSubLpRestrictedModel = 2;
  FundSubLpLockStatusModel _FundSubLpLockStatusModel = 3;
  FundSubLpNoteModel _FundSubLpNoteModel = 4;
}
