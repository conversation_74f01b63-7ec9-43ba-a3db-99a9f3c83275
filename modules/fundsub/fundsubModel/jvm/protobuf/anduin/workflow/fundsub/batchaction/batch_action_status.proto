syntax = "proto3";

package anduin.workflow.fundsub.batchaction;

import "date_time.proto";
import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.workflow.fundsub.batchaction"
  single_file: true
  import: "java.time.Instant"
};

message FundSubBatchActionStatus {
  option (scalapb.message).no_box = true;
  oneof sealed_value {
    FundSubBatchActionStatusWaiting waiting = 1;
    FundSubBatchActionStatusRunning running = 2;
    FundSubBatchActionStatusSucceeded succeeded = 3;
    FundSubBatchActionStatusFailed failed = 4;
    FundSubBatchActionStatusCancelled cancelled = 5;
  }
}

message FundSubBatchActionStatusWaiting {
  InstantMessage created_at = 1 [(scalapb.field).type = "Instant"];
}

message FundSubBatchActionStatusRunning {
  InstantMessage started_at = 1 [(scalapb.field).type = "Instant"];
}

message FundSubBatchActionStatusSucceeded {
  InstantMessage succeeded_at = 1 [(scalapb.field).type = "Instant"];
}

message FundSubBatchActionStatusFailed {
  InstantMessage failed_at = 1 [(scalapb.field).type = "Instant"];
  FundSubBatchActionError error = 2;
}

message FundSubBatchActionStatusCancelled {
  InstantMessage cancelled_at = 1 [(scalapb.field).type = "Instant"];
  FundSubBatchActionStatusCancelledReason reason = 2;
}

enum FundSubBatchActionStatusCancelledReason {
  UserTrigger = 0;
  WorkflowTerminated = 1;
}

message FundSubBatchActionError {
  oneof sealed_value {
    FundSubBatchActionServerError server_error = 1;
    FundSubBatchActionNoPermissionError no_permission_error = 2;
  }
}

message FundSubBatchActionServerError {
  string reason = 1;
}

message FundSubBatchActionNoPermissionError {
  string detail = 1;
}