// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.signature.common

import anduin.model.id.FolderId
import com.anduin.stargazer.client.services.file.FileJsClient
import com.anduin.stargazer.service.FileServiceEndpoints.CreateUserFolderParams
import com.anduin.stargazer.client.utils.ZIOUtils
import zio.Task

object SignatureClientUtils {

  def getUserFolderId: Task[FolderId] = {
    for {
      createResponse <- FileJsClient.createUserFolderIfNecessaryWithCache(CreateUserFolderParams())
      folderIdOpt = createResponse.toOption.map(_.folderId)
      folderId <- ZIOUtils.fromOption(folderIdOpt, new RuntimeException("Can't create or parse FolderId"))
    } yield folderId
  }

}
