// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.signature.prepare

import anduin.model.signature.ESignatureDataCompanion
import anduin.signature.prepare.SignaturePrepViewer.{Mode, PrepareMode, SignMode}
import anduin.signature.sign.SignViewerModels.Rect
import anduin.signature.sign.util.ESignRenderUtils
import com.anduin.stargazer.client.component.viewer.PageSize
import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.EventUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.extra.{EventListener, OnUnmount}
import org.scalajs.dom
import org.scalajs.dom.{HTMLElement, MouseEvent}

import anduin.signature.sign.util.ESignRenderUtils.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

final case class RadioFieldOption(
  key: String,
  value: Boolean,
  size: Rect,
  isHovered: Boolean,
  pageSize: PageSize,
  pageNodeOpt: Option[HTMLElement],
  colorScheme: ColorScheme,
  mode: Mode,
  onMouseEnter: Callback,
  onMouseLeave: Callback,
  onMove: Rect => Callback,
  onMoveEnd: Callback,
  onSelect: Callback,
  showRemoveButton: Boolean,
  onRemove: Callback
) {

  def apply(): VdomElement = RadioFieldOption.component.withKey(key)(this)

}

object RadioFieldOption {

  private type Props = RadioFieldOption

  private final case class State(
    isMoving: Boolean,
    mouseDownBottom: Double,
    mouseDownLeft: Double
  )

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .initialState {
      State(
        isMoving = false,
        mouseDownBottom = 0,
        mouseDownLeft = 0
      )
    }
    .renderBackend[Backend]
    .configure(
      EventListener[MouseEvent].install(
        "mousemove",
        _.backend.onMouseMoveDocument,
        _ => dom.document
      )
    )
    .configure(
      EventListener.install(
        "mouseup",
        _.backend.onMouseUpDocument,
        _ => dom.document
      )
    )
    .build

  private class Backend(scope: BackendScope[Props, State]) extends OnUnmount {

    private val wrapperRef = Ref[HTMLElement]

    def render(props: Props, state: State): VdomElement = {
      <.div.withRef(wrapperRef)(
        tw.absolute.flex.itemsEnd,
        if (state.isMoving) tw.z4 else tw.z3,
        ^.onMouseEnter --> props.onMouseEnter,
        ^.onMouseLeave --> props.onMouseLeave,
        ^.bottom := s"${props.size.bottom}px",
        ^.left := s"${props.size.left}px",
        content(props),
        TagMod.when(props.mode == PrepareMode && !state.isMoving && props.showRemoveButton) {
          actionPopover(props)
        }
      )
    }

    private def content(props: Props): VdomElement = {
      val hoveredBorderColor = if (props.mode == SignMode) ESignRenderUtils.SignFieldColor else props.colorScheme.color
      <.div(
        tw.flex.itemsCenter.justifyCenter.bgGray0,
        tw.borderAll.roundedFull,
        if (props.isHovered || props.value) {
          ^.borderColor := hoveredBorderColor
        } else {
          tw.borderGray4
        },
        ^.height := s"${props.size.height}px",
        ^.width := s"${props.size.width}px",
        ^.fontFamily := ESignatureDataCompanion.CheckMarkFont,
        ^.fontSize := s"${props.size.width}px",
        props.mode match {
          case SignaturePrepViewer.PrepareMode =>
            TagMod(
              ^.cursor.move,
              ^.onMouseDown ==> onMouseDown
            )

          case SignaturePrepViewer.SignMode =>
            TagMod(
              tw.cursorPointer,
              ^.onClick --> props.onSelect
            )

          case SignaturePrepViewer.ViewMode =>
            tw.cursorPointer
        },
        TagMod.when(props.value)(ESignatureDataCompanion.CheckMarkSymbol)
      )
    }

    private def actionPopover(props: Props): VdomElement = {
      <.div(
        ^.onClick ==> (_.stopPropagationCB),
        tw.wAuto.hPc100.borderAll.rounded2.borderGray3,
        tw.ml4.bgGray0,
        removeButton(props)
      )
    }

    private def removeButton(props: Props): VdomElement = {
      TooltipR(
        renderTarget = Button(
          style = Button.Style.Minimal(
            color = Button.Color.Danger,
            height = Button.Height.Fix24,
            icon = Some(Icon.Glyph.Trash)
          ),
          onClick = props.onRemove
        )(),
        renderContent = _("Remove")
      )()
    }

    private def onMouseDown(e: ReactMouseEventFromHtml): Callback = {
      for {
        props <- scope.props
        _ <- e.preventDefaultCB
        _ <- e.stopPropagationCB
        _ <- Callback.when(EventUtils.leftButtonClicked(e) && props.mode == PrepareMode) {
          wrapperRef.foreachCB { wrapper =>
            val containerRect = wrapper.getBoundingClientRect()
            val bottom = containerRect.bottom - e.clientY
            val left = e.clientX - containerRect.left
            scope.modState {
              _.copy(
                mouseDownBottom = bottom,
                mouseDownLeft = left,
                isMoving = true
              )
            }
          }
        }
      } yield ()
    }

    def onMouseUpDocument: Callback = {
      for {
        props <- scope.props
        state <- scope.state
        _ <- Callback.when(props.mode == PrepareMode && state.isMoving) {
          scope.modState(
            _.copy(isMoving = false),
            props.onMoveEnd
          )
        }
      } yield ()
    }

    def onMouseMoveDocument(e: MouseEvent): Callback = {
      for {
        props <- scope.props
        state <- scope.state
        _ <- Callback.when(state.isMoving && props.mode == PrepareMode) {
          wrapperRef.foreachCB { wrapperEle =>
            val pageRect = wrapperEle.parentElement.getBoundingClientRect()
            val bottom = pageRect.bottom - e.clientY - state.mouseDownBottom
            val left = e.clientX - pageRect.left - state.mouseDownLeft
            val newSize = props.size.copy(bottom = bottom, left = left)
            val isInPage = newSize.isInPage(
              pageRect.width,
              pageRect.height,
              RadioField.FieldPadding.toDouble
            )
            Callback.when(isInPage)(props.onMove(newSize))
          }
        }
      } yield ()
    }

  }

}
