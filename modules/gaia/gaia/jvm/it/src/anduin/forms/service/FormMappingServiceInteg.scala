// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.forms.service

import io.circe.Json
import zio.ZIO
import zio.implicits.*

import anduin.fdb.record.FDBRecordDatabase
import anduin.forms.Form.FormNamespace
import anduin.forms.analytics.FormMappingAnalyticService
import anduin.forms.endpoint.*
import anduin.forms.model.Schema
import anduin.forms.model.formmapping.{FormInfo, FormMappingMainAndTaxForms, SameAliasMappingNamespacePair}
import anduin.forms.model.formmeta.{FormForFile, FormForInvestorProfile}
import anduin.forms.rules.FormRule
import anduin.forms.storage.*
import anduin.forms.utils.FormMappingUtils.*
import anduin.forms.{Form, FormData, FormSchema}
import anduin.id.form.*
import anduin.id.role.portal.PortalSectionId
import anduin.model.common.FieldAlias
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.id.{AssetSessionId, FileId, LpProfileIdFactory}
import anduin.portaluser.PortalUserModel
import anduin.portaluser.PortalUserProtocols.{AddPortalUserParams, PortalRole, SetUserRoleParams}
import anduin.service.AuthenticatedRequestContext
import anduin.testing.GondorCoreIntegUtils
import anduin.testing.GaiaBaseInteg
import zio.test.*

object FormMappingServiceInteg extends GaiaBaseInteg with GondorCoreIntegUtils {
  private val formUser = userBM1
  private val formSession = AssetSessionId.FormBuilder("testsession1")
  private val formUserEmail = userBM1Email
  private val otherUser = userIC

  private val formOwners = Seq(
    EmailAddress(Some(userInfoBM1.fullNameString), userBM1Email)
  )

  private val excelFileId = FileId.defaultValue.get
  private val lpProfileId = LpProfileIdFactory.unsafeRandomId

  // Sample data for forms
  private val MainForm1 = FormServiceTestUtils.defaultApply(
    defaultFormSchema = Schema.obj(
      List(
        Schema.obj.Field("investorname", Schema.string()),
        Schema.obj.Field("investoraddress", Schema.string()),
        Schema.obj.Field("capital", Schema.integer()),
        Schema.obj.Field("investorfulladdress", Schema.string())
      )
    ),
    defaultUiSchema = Map.empty,
    rules = List(
      FormRule(
        value = """function(investoraddress)
                  |  atd.add('investorfulladdress', "value", investoraddress.value)
                  |""".stripMargin,
        name = "rule1",
        defaultNamespace = Form.DefaultNamespace
      )
    )
  )

  private val MainForm2 = Form(
    namespaceFormSchemaMap = Map(
      FormNamespace("mainform") -> FormSchema(
        Schema.obj(
          List(
            Schema.obj.Field("investorname", Schema.string()),
            Schema.obj.Field("capital", Schema.integer())
          )
        ),
        Map.empty
      ),
      FormNamespace("mainform2test") -> FormSchema(
        Schema.obj(
          List(
            Schema.obj.Field("investorname2", Schema.string()),
            Schema.obj.Field("capital2", Schema.integer())
          )
        ),
        Map.empty
      )
    ),
    rules = List.empty,
    defaultNamespace = Form.DefaultNamespace,
    triggerRuleByProperty = false
  )

  private val TaxForm1 = FormServiceTestUtils.defaultApply(
    defaultFormSchema = Schema.obj(
      List(
        Schema.obj.Field("investornametax1", Schema.string()),
        Schema.obj.Field("capitaltax1", Schema.integer()),
        Schema.obj.Field("taxperson", Schema.string())
      )
    ),
    defaultUiSchema = Map.empty,
    rules = List(
      FormRule(
        value = """function(investornametax1)
                  |  atd.add('taxperson', "value", investornametax1.value)
                  |""".stripMargin,
        name = "rule1",
        defaultNamespace = Form.DefaultNamespace
      )
    )
  )

  private val TaxForm2 = FormServiceTestUtils.defaultApply(
    defaultFormSchema = Schema.obj(
      List(
        Schema.obj.Field("investornametax2", Schema.string()),
        Schema.obj.Field("capitaltax2", Schema.integer())
      )
    ),
    defaultUiSchema = Map.empty,
    rules = List.empty
  )

  private val ExcelForm = FormServiceTestUtils.defaultApply(
    defaultFormSchema = Schema.obj(
      List(
        Schema.obj.Field("investornameexcel", Schema.string()),
        Schema.obj.Field("investoraddressexcel", Schema.string()),
        Schema.obj.Field("capitalexcel", Schema.integer())
      )
    ),
    defaultUiSchema = Map.empty,
    rules = List.empty
  )

  private val ProfileForm = FormServiceTestUtils.defaultApply(
    defaultFormSchema = Schema.obj(
      List(
        Schema.obj.Field("investorname", Schema.string()),
        Schema.obj.Field("investoraddress", Schema.string()),
        Schema.obj.Field("capital", Schema.integer())
      )
    ),
    defaultUiSchema = Map.empty,
    rules = List.empty
  )

  // scalafix:off DisableSyntax.var
  private var mainForm1Id: FormVersionId = scala.compiletime.uninitialized
  private var mainForm2Id: FormVersionId = scala.compiletime.uninitialized
  private var taxForm1Id: FormVersionId = scala.compiletime.uninitialized
  private var taxForm2Id: FormVersionId = scala.compiletime.uninitialized
  private var excelFormId: FormVersionId = scala.compiletime.uninitialized
  private var profileFormId: FormVersionId = scala.compiletime.uninitialized

  private var mainForm1ExcelMappingId: FormMappingId = scala.compiletime.uninitialized
  private val MainForm1ExcelMappingName = "MainForm1-Excel"
  private var excelMainForm1MappingId: FormMappingId = scala.compiletime.uninitialized
  private val ExcelMainForm1MappingName = "Excel-MainForm1"
  private var mainForm1ProfileMappingId: FormMappingId = scala.compiletime.uninitialized
  private val MainForm1ProfileMappingName = "MainForm1-Profile"
  private var mainForm1TaxMappingId: FormMappingId = scala.compiletime.uninitialized
  private val MainForm1TaxMappingName = "MainForm1-Tax"
  // scalafix:on

  private val MainForm1InitialData = Map(
    "investorname" -> Json.fromString("Big Investor"),
    "investoraddress" -> Json.fromString("Big Investor Address"),
    "capital" -> Json.fromInt(666666)
  )

  private val ExcelFormInitialData = Map(
    "investornameexcel" -> Json.fromString("Big Investor"),
    "investoraddressexcel" -> Json.fromString("Big Investor Address"),
    "capitalexcel" -> Json.fromInt(333333)
  )

  override def spec = suite("FormMappingService")(
    setupSuite,
    initializeSuite,
    importExportSuite,
    mainAndTaxFormSuite,
    inverstorProfileSuite,
    analyticSuite,
    cleanTest
  ) @@ TestAspect.sequential

  private def setupSuite = suite("FormMappingService - Set up test context")(
    test("Create test users") {
      for {
        _ <- portalUserService
          .addPortalUser(
            params = AddPortalUserParams(formUserEmail, None),
            ctx = AuthenticatedRequestContext.defaultInstance
          )
          .unit
          .onErrorHandleWith(_ => ZIO.unit)
        _ <- portalUserService.setUserRole(
          params = SetUserRoleParams(
            userId = formUser,
            roles = Seq(PortalRole(PortalSectionId.Form, PortalUserModel.Relation.Editor)),
            isSuperAdmin = false
          ),
          ctx = AuthenticatedRequestContext.defaultInstance
        )
      } yield assertCompletes
    }
  )

  private def initializeSuite = suite("FormMappingService - Initialize needed forms")(
    test("Create mainForm1") {
      for {
        formRes <- formService.createForm(
          "mainForm1",
          Set.empty,
          formOwners,
          formUser
        )
        formId = formRes.formId
        _ <- FormLockServiceIntegUtils.forceGetLock(
          formId,
          formUser,
          formSession
        )(formLockService)
        draftVersionRes <- formService.getAllVersions(formId, formUser)
        draftVersionId = draftVersionRes.versions.headOption.map(_.formVersionId).get
        _ <- formService.saveFormData(
          formId = formId,
          formData = FormData(MainForm1),
          versionMetadataOpt = None,
          parentVersionId = Some(draftVersionId),
          name = "versionDraft",
          actor = formUser,
          sessionId = formSession
        )
        createVersionRes <- formService.createFormVersion(
          formId,
          "version1",
          "",
          formUser,
          formSession
        )
        formVersionRes <- formService.getForm(
          formId,
          Option(createVersionRes.result.toOption.get.versionId),
          formUser
        )
      } yield {
        mainForm1Id = formVersionRes.formModel.latestVersionId
        assertTrue(
          mainForm1Id == createVersionRes.result.toOption.get.versionId,
          formVersionRes.formModel.name == "mainForm1",
          formVersionRes.versionOpt.get.name == "version1",
          formVersionRes.formData == FormData(MainForm1)
        )
      }
    },
    test("Create mainForm2") {
      for {
        formRes <- formService.createForm(
          "mainForm2",
          Set.empty,
          formOwners,
          formUser
        )
        formId = formRes.formId
        _ <- FormLockServiceIntegUtils.forceGetLock(
          formId,
          formUser,
          formSession
        )(formLockService)
        draftVersionRes <- formService.getAllVersions(formId, formUser)
        draftVersionId = draftVersionRes.versions.headOption.map(_.formVersionId).get
        _ <- formService.saveFormData(
          formId = formId,
          formData = FormData(MainForm2),
          versionMetadataOpt = None,
          parentVersionId = Some(draftVersionId),
          name = "versionDraft",
          actor = formUser,
          sessionId = formSession
        )
        createVersionRes <- formService.createFormVersion(
          formId,
          "version1",
          "",
          formUser,
          sessionId = formSession
        )
        formVersionRes <- formService.getForm(
          formId,
          Option(createVersionRes.result.toOption.get.versionId),
          formUser
        )
      } yield {
        mainForm2Id = formVersionRes.formModel.latestVersionId
        assertTrue(
          mainForm2Id == createVersionRes.result.toOption.get.versionId,
          formVersionRes.formModel.name == "mainForm2",
          formVersionRes.versionOpt.get.name == "version1",
          formVersionRes.formData == FormData(MainForm2)
        )
      }
    },
    test("Create taxForm1") {
      for {
        formRes <- formService.createForm(
          "taxForm1",
          Set.empty,
          formOwners,
          formUser
        )
        formId = formRes.formId
        _ <- FormLockServiceIntegUtils.forceGetLock(
          formId,
          formUser,
          formSession
        )(formLockService)
        draftVersionRes <- formService.getAllVersions(formId, formUser)
        draftVersionId = draftVersionRes.versions.headOption.map(_.formVersionId).get
        _ <- formService.saveFormData(
          formId = formId,
          formData = FormData(TaxForm1),
          versionMetadataOpt = None,
          parentVersionId = Some(draftVersionId),
          name = "versionDraft",
          actor = formUser,
          sessionId = formSession
        )
        createVersionRes <- formService.createFormVersion(
          formId,
          "version1",
          "",
          formUser,
          formSession
        )
        formVersionRes <- formService.getForm(
          formId,
          Option(createVersionRes.result.toOption.get.versionId),
          formUser
        )
      } yield {
        taxForm1Id = formVersionRes.formModel.latestVersionId
        assertTrue(
          taxForm1Id == createVersionRes.result.toOption.get.versionId,
          formVersionRes.formModel.name == "taxForm1",
          formVersionRes.versionOpt.get.name == "version1",
          formVersionRes.formData == FormData(TaxForm1)
        )
      }
    },
    test("Create taxForm2") {
      for {
        formRes <- formService.createForm(
          "taxForm2",
          Set.empty,
          formOwners,
          formUser
        )
        formId = formRes.formId
        _ <- FormLockServiceIntegUtils.forceGetLock(
          formId,
          formUser,
          formSession
        )(formLockService)
        draftVersionRes <- formService.getAllVersions(formId, formUser)
        draftVersionId = draftVersionRes.versions.headOption.map(_.formVersionId).get
        _ <- formService.saveFormData(
          formId = formId,
          formData = FormData(TaxForm2),
          versionMetadataOpt = None,
          parentVersionId = Some(draftVersionId),
          name = "versionDraft",
          actor = formUser,
          sessionId = formSession
        )
        createVersionRes <- formService.createFormVersion(
          formId,
          "version1",
          "",
          formUser,
          formSession
        )
        formVersionRes <- formService.getForm(
          formId,
          Option(createVersionRes.result.toOption.get.versionId),
          formUser
        )
      } yield {
        taxForm2Id = formVersionRes.formModel.latestVersionId
        assertTrue(
          taxForm2Id == createVersionRes.result.toOption.get.versionId,
          formVersionRes.formModel.name == "taxForm2",
          formVersionRes.versionOpt.get.name == "version1",
          formVersionRes.formData == FormData(TaxForm2)
        )
      }
    },
    test("Create excelForm") {
      for {
        formRes <- formService.createForm(
          "excelForm",
          Set.empty,
          formOwners,
          formUser
        )
        formId = formRes.formId
        _ <- FormLockServiceIntegUtils.forceGetLock(
          formId,
          formUser,
          formSession
        )(formLockService)
        draftVersionRes <- formService.getAllVersions(formId, formUser)
        draftVersionId = draftVersionRes.versions.headOption.map(_.formVersionId).get
        _ <- formService.saveFormData(
          formId = formId,
          formData = FormData(ExcelForm),
          versionMetadataOpt = None,
          parentVersionId = Some(draftVersionId),
          name = "versionDraft",
          actor = formUser,
          sessionId = formSession
        )
        createVersionRes <- formService.createFormVersion(
          formId,
          "version1",
          "",
          formUser,
          formSession
        )
        formVersionRes <- formService.getForm(
          formId,
          Option(createVersionRes.result.toOption.get.versionId),
          formUser
        )
      } yield {
        excelFormId = formVersionRes.formModel.latestVersionId
        assertTrue(
          excelFormId == createVersionRes.result.toOption.get.versionId,
          formVersionRes.formModel.name == "excelForm",
          formVersionRes.versionOpt.get.name == "version1",
          formVersionRes.formData == FormData(ExcelForm)
        )
      }
    },
    test("Create investorProfileForm") {
      for {
        formRes <- formService.createForm(
          "investorProfileForm",
          Set.empty,
          formOwners,
          formUser
        )
        formId = formRes.formId
        _ <- FormLockServiceIntegUtils.forceGetLock(
          formId,
          formUser,
          formSession
        )(formLockService)
        draftVersionRes <- formService.getAllVersions(formId, formUser)
        draftVersionId = draftVersionRes.versions.headOption.map(_.formVersionId).get
        _ <- formService.saveFormData(
          formId = formId,
          formData = FormData(ProfileForm),
          versionMetadataOpt = None,
          parentVersionId = Some(draftVersionId),
          name = "versionDraft",
          actor = formUser,
          sessionId = formSession
        )
        createVersionRes <- formService.createFormVersion(
          formId,
          "version1",
          "",
          formUser,
          formSession
        )
        formVersionRes <- formService.getForm(
          formId,
          Option(createVersionRes.result.toOption.get.versionId),
          formUser
        )
      } yield {
        profileFormId = formVersionRes.formModel.latestVersionId
        assertTrue(
          profileFormId == createVersionRes.result.toOption.get.versionId,
          formVersionRes.formModel.name == "investorProfileForm",
          formVersionRes.versionOpt.get.name == "version1",
          formVersionRes.formData == FormData(ProfileForm)
        )
      }
    },
    test("FormMeta create/query") {
      for {
        _ <- formMappingService.createFormMetaForFile(
          CreateFormMetaForFileParams(excelFormId, FormForFile(excelFileId)),
          formUser
        )
        _ <- formMappingService.createFormMetaForInvestorProfile(
          CreateFormMetaForInvestorProfileParams(profileFormId, FormForInvestorProfile(lpProfileId)),
          formUser
        )
        fileFormResOpt <- formMappingService.getFormForFile(excelFileId)
        profileFormResOpt <- formMappingService.getFormForInvestorProfile(lpProfileId)
      } yield {
        assertTrue(fileFormResOpt.get == excelFormId, profileFormResOpt.get == profileFormId)
      }
    }
  )

  private def importExportSuite = {
    val main1ExcelMappingRules = Seq(
      FormRule(
        value = """
                  |function(investorname, investoraddress, capital)
                  |  atd.add('investornameexcel', "value", investorname.value, 'excelForm') +
                  |  atd.add('investoraddressexcel', "value", investoraddress.value, 'excelForm') +
                  |  atd.add('capitalexcel', "value", capital.value/2, 'excelForm')
                  |""".stripMargin,
        name = "",
        defaultNamespace = FormNamespace("mainForm")
      )
    )
    val excelMain1MappingRules = Seq(
      FormRule(
        value = """
                  |function(investornameexcel, investoraddressexcel, capitalexcel)
                  |  atd.add('investorname', "value", investornameexcel.value, 'mainForm') +
                  |  atd.add('investoraddress', "value", investoraddressexcel.value, 'mainForm') +
                  |  atd.add('capital', "value", capitalexcel.value, 'mainForm')
                  |""".stripMargin,
        name = "",
        defaultNamespace = FormNamespace("excelForm")
      )
    )
    suite("FormMappingService - Excel import/export use case")(
      // Note: InvestorProfile use case is quite similar so we don't need to repeat similar
      test("Create/Import/Update mapping between MainForm1 - Excel & vice versa") {
        for {
          // Create MainForm1 - Excel mapping (initialize with full formInfoList)
          formExcelMappingId <- formMappingService.createFormMapping(
            CreateFormMappingParams(
              name = MainForm1ExcelMappingName,
              formInfoList = List(
                FormInfo(
                  formVersionId = mainForm1Id,
                  namespaceMap = Map("main" -> "mainForm")
                ),
                FormInfo(
                  formVersionId = excelFormId,
                  namespaceMap = Map("main" -> "excelForm")
                )
              )
            ),
            formUser
          )

          // Add mapping rules
          _ <- formMappingService.updateMappingRules(
            UpdateMappingRulesParams(
              formExcelMappingId,
              rulesToAdd = main1ExcelMappingRules,
              rulesToRemove = Set.empty
            ),
            formUser
          )

          // Create Excel - MainForm1 mapping (initialize with empty formInfoList then import)
          excelFormMappingId <- formMappingService.createFormMapping(
            CreateFormMappingParams(name = ExcelMainForm1MappingName),
            formUser
          )

          // Import in the desired order
          _ <- formMappingService.importFormIntoMapping(
            ImportFormIntoMappingParams(
              excelFormMappingId,
              excelFormId,
              Map("main" -> "excelForm")
            ),
            formUser
          )
          _ <- formMappingService.importFormIntoMapping(
            ImportFormIntoMappingParams(
              excelFormMappingId,
              mainForm1Id,
              Map("main" -> "mainForm")
            ),
            formUser
          )
          // Add mapping rules
          _ <- formMappingService.updateMappingRules(
            UpdateMappingRulesParams(
              excelFormMappingId,
              rulesToAdd = excelMain1MappingRules,
              rulesToRemove = Set.empty
            ),
            formUser
          )

          // Verify create/update
          formExcelMapping <- formMappingService.getFormMapping(formExcelMappingId)
          excelFormMapping <- formMappingService.getFormMapping(excelFormMappingId)
        } yield {
          mainForm1ExcelMappingId = formExcelMappingId
          excelMainForm1MappingId = excelFormMappingId

          assertTrue(
            formExcelMapping.get.name == MainForm1ExcelMappingName,
            formExcelMapping.get.formInfoList.map(_.formVersionId) == List(mainForm1Id, excelFormId),
            formExcelMapping.get.mappingRules.map(ruleFromMessage) == main1ExcelMappingRules,
            excelFormMapping.get.name == ExcelMainForm1MappingName,
            excelFormMapping.get.formInfoList.map(_.formVersionId) == List(excelFormId, mainForm1Id),
            excelFormMapping.get.mappingRules.map(ruleFromMessage) == excelMain1MappingRules
          )
        }
      },
      test("Construct MultipleNamespaceForm and apply mapping data") {
        for {
          formExcelMappingOpt <- formMappingService.getFormMappingFromFormVersionList(List(mainForm1Id, excelFormId))
          excelFormMappingOpt <- formMappingService.getFormMappingFromFormVersionList(List(excelFormId, mainForm1Id))
          complexForm <- formMappingService.constructMultiNamespaceFromFormMappingUnsafe(formExcelMappingOpt.get)
          applyFormExcelRes <- formMappingService.applyDataMapping(
            complexForm,
            Seq(FormNamespace("mainForm") -> MainForm1InitialData),
            None
          )
          applyExcelFormRes <- formMappingService
            .applyDataMappingUnsafe(
              excelFormMappingOpt.get,
              Seq((FormNamespace("excelForm") -> ExcelFormInitialData)),
              None
            )
        } yield {
          assertTrue( // Including 1 mappingRule & 1 original mainForm1 rule
            complexForm.rules.size == 2,
            complexForm.namespaceFormSchemaMap.keySet.map(_.toString) == Set(
              "main",
              "mainForm",
              "excelForm"
            ),

            // Should get the expected excelFormData
            applyFormExcelRes.namespaceValueMap
              .getOrElse(FormNamespace("excelForm"), Map.empty) == ExcelFormInitialData,
            // mainForm1 rules should also be triggered
            applyFormExcelRes.namespaceValueMap
              .getOrElse(FormNamespace("mainForm"), Map.empty) == MainForm1InitialData +
              ("investorfulladdress" -> Json.fromString("Big Investor Address")),

            // Should get the expected mainFormData
            applyExcelFormRes.namespaceValueMap
              .getOrElse(FormNamespace("mainForm"), Map.empty) == MainForm1InitialData ++ Map(
              "capital" -> Json.fromInt(333333),
              "investorfulladdress" -> Json.fromString("Big Investor Address")
            )
          )
        }
      }
    )
  }

  private def mainAndTaxFormSuite = {
    val taxMappingRules = Seq(
      FormRule(
        value = """
                  |function(capital)
                  |  atd.add('onethirdcapital', "value", capital.value/3, 'main')
                  |""".stripMargin,
        name = "",
        defaultNamespace = FormNamespace("mainForm")
      ),
      FormRule(
        value = """
                  |function(investorname, capital)
                  |  atd.add('investornametax1', "value", investorname.value, 'taxForm1') +
                  |  atd.add('capitaltax1', "value", capital.value, 'taxForm1') +
                  |  atd.add('investornametax2', "value", investorname.value, 'taxForm2')
                  |""".stripMargin,
        name = "",
        defaultNamespace = FormNamespace("mainForm")
      ),
      FormRule(
        value = """
                  |function(onethirdcapital)
                  |  atd.add('capitaltax2', "value", onethirdcapital.value, 'taxForm2')
                  |""".stripMargin,
        name = "",
        defaultNamespace = Form.DefaultNamespace
      )
    )

    suite("FormMappingService - Main form & tax forms use case")(
      test("Create/Import/Update mapping MainForm1 - Tax") {
        for {
          unauthorizedCreateMappingRes <- formMappingService
            .createFormMapping(
              CreateFormMappingParams(name = MainForm1TaxMappingName),
              otherUser
            )
            .map(Option(_))
            .onErrorHandleWith(_ => ZIO.attempt(None))
          mappingTaxId <- formMappingService.createFormMapping(
            CreateFormMappingParams(
              name = MainForm1TaxMappingName,
              formInfoList = List(
                FormInfo(
                  formVersionId = mainForm1Id,
                  namespaceMap = Map("main" -> "mainForm")
                )
              ),
              useCaseType = FormMappingMainAndTaxForms()
            ),
            formUser
          )

          // Import form with multiple namespaces & have conflict "mainForm" namespace (should not be succeed)
          conflictImportRes <- formMappingService
            .importFormIntoMapping(
              ImportFormIntoMappingParams(
                mappingTaxId,
                mainForm2Id,
                Map("main" -> "mainForm", "mainform2test" -> "subForm")
              ),
              formUser
            )
            .map(_ => Option("Succeed"))
            .onErrorHandleWith(_ => ZIO.attempt(None))

          // Import 2 tax forms
          _ <- formMappingService.importFormIntoMapping(
            ImportFormIntoMappingParams(
              mappingTaxId,
              taxForm1Id,
              Map("main" -> "taxForm1")
            ),
            formUser
          )
          _ <- formMappingService.importFormIntoMapping(
            ImportFormIntoMappingParams(
              mappingTaxId,
              taxForm2Id,
              Map("main" -> "taxForm2")
            ),
            formUser
          )

          // Add mapping alias and rules
          _ <- formMappingService.updateMappingAdditionalAliases(
            UpdateMappingAdditionalAliasesParams(
              mappingTaxId,
              aliasesToAdd = Map(FieldAlias("onethirdcapital") -> Schema.integer()),
              aliasesToRemove = Set.empty
            ),
            formUser
          )
          _ <- formMappingService.updateMappingRules(
            UpdateMappingRulesParams(
              mappingTaxId,
              rulesToAdd = taxMappingRules,
              rulesToRemove = Set.empty
            ),
            formUser
          )

          // Verify
          mappingTaxRes <- formMappingService.getFormMapping(mappingTaxId)
        } yield {
          mainForm1TaxMappingId = mappingTaxId
          assertTrue( // Test permission check
            unauthorizedCreateMappingRes.isEmpty == true,
            conflictImportRes.isEmpty == true,
            // Other verifications
            mappingTaxRes.isDefined == true,
            mappingTaxRes.get.name == MainForm1TaxMappingName,
            mappingTaxRes.get.formInfoList.map(_.formVersionId).toSet == Set(
              mainForm1Id,
              taxForm2Id,
              taxForm1Id
            ),
            mappingTaxRes.get.mappingRules.map(ruleFromMessage) == taxMappingRules,
            mappingTaxRes.get.additionalMappingAliases.size == 1
          )
        }
      },
      test("Apply mapping data to MainForm1 - Tax") {
        val expectedTax1Data = Map(
          "investornametax1" -> Json.fromString("Big Investor"),
          "capitaltax1" -> Json.fromInt(666666),
          "taxperson" -> Json.fromString("Big Investor")
        )
        val expectedTax2Data = Map(
          "investornametax2" -> Json.fromString("Big Investor"),
          "capitaltax2" -> Json.fromInt(222222)
        )

        for {
          taxMappingOpt <- formMappingService.getFormMappingFromFormVersionList(
            formVersionIds = List(
              taxForm2Id,
              mainForm1Id,
              taxForm1Id
            ),
            formMappingUseCase = FormMappingMainAndTaxForms()
          )
          applyExcelFormRes <- formMappingService
            .applyDataMappingUnsafe(
              taxMappingOpt.get,
              Seq((FormNamespace("mainForm") -> MainForm1InitialData)),
              None
            )
        } yield {
          // Should get the expected results
          assertTrue(
            applyExcelFormRes.namespaceValueMap
              .getOrElse(FormNamespace("taxForm1"), Map.empty) == expectedTax1Data,
            applyExcelFormRes.namespaceValueMap
              .getOrElse(FormNamespace("taxForm2"), Map.empty) == expectedTax2Data
          )
        }
      }
    )
  }

  private def inverstorProfileSuite =
    suite("FormMappingService - Investor profile with implicit same alias mapping use case")(
      test("Create profile mapping form & apply mapping data") {
        for {
          mappingId <- formMappingService.createFormMapping(
            CreateFormMappingParams(
              name = MainForm1ProfileMappingName,
              formInfoList = List(
                FormInfo(
                  formVersionId = mainForm1Id,
                  namespaceMap = Map("main" -> "mainForm")
                ),
                FormInfo(
                  formVersionId = profileFormId,
                  namespaceMap = Map("main" -> "profileForm")
                )
              ),
              sameAliasMappingNamespacePairs = List(
                SameAliasMappingNamespacePair("mainForm", "profileForm")
              )
            ),
            formUser
          )
          formMappingOpt <- formMappingService.getFormMapping(mappingId)
          profileMappingRes <- formMappingService.applyDataMappingUnsafe(
            formMappingOpt.get,
            Seq((FormNamespace("mainForm") -> MainForm1InitialData)),
            None
          )
        } yield {
          mainForm1ProfileMappingId = mappingId
          assertTrue(
            profileMappingRes.namespaceValueMap
              .getOrElse(FormNamespace("profileForm"), Map.empty) == MainForm1InitialData
          )
        }
      },
      test("Query by mapping name & single formVersionId functions") {
        for {
          queryByNameRes <- formMappingService.queryFormMappingByName("mainform1")
          queryBySingleFormVersionRes <- formMappingService.queryFormMappingByFormVersionId(mainForm1Id)
        } yield {
          assertTrue(
            // Search FormMapping by name
            queryByNameRes.map(_.id).toSet ==
              Set(
                mainForm1ExcelMappingId,
                mainForm1TaxMappingId,
                mainForm1ProfileMappingId
              ),
            // Query FormMapping related to a FormVersionId -> to review those when that form version changes
            queryBySingleFormVersionRes.map(_.id).toSet ==
              Set(
                mainForm1ExcelMappingId,
                excelMainForm1MappingId,
                mainForm1TaxMappingId,
                mainForm1ProfileMappingId
              )
          )
        }
      }
    )

  private def analyticSuite = {
    val formMappingAnalyticService = FormMappingAnalyticService(formService)
    suite("FormMappingAnalyticService")(
      test("getFormAnalyticData") {
        for {
          analyticDataOpt <- formMappingAnalyticService.getFormAnalyticData(formUser, profileFormId)
        } yield {
          assertTrue(analyticDataOpt.isDefined)
        }
      }
    )
  }

  private def cleanTest = test("Clean up Form, FormMapping, and FormMeta") {
    for {
      // Clean up Form records
      _ <- ZIO.foreach(
        Seq(
          mainForm1Id,
          mainForm2Id,
          taxForm1Id,
          taxForm2Id,
          excelFormId,
          profileFormId
        )
      ) { verId =>
        for {
          _ <- formLockService.releaseLock(
            verId.parent,
            formUser,
            formSession
          )
          _ <- FDBRecordDatabase.transact(FormModelStoreOperations.Production) { ops =>
            ops.delete(verId.parent)
          }
        } yield ()
      }
      // Clean up FormMeta records
      _ <- ZIO.foreach(Seq(excelFormId, profileFormId)) { verId =>
        FDBRecordDatabase.transact(FormMetaStoreOperations.Production) { ops =>
          ops.deletePermanently(verId)
        }
      }
      // Clean up FormMapping records
      _ <- ZIO.foreach(
        Seq(
          mainForm1ExcelMappingId,
          excelMainForm1MappingId,
          mainForm1ProfileMappingId,
          mainForm1TaxMappingId
        )
      ) { mappingId =>
        FDBRecordDatabase.transact(FormMappingStoreOperations.Production) { ops =>
          ops.deletePermanently(mappingId)
        }
      }
    } yield assertCompletes
  }

}
