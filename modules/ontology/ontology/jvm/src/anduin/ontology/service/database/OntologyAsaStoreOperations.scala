// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.ontology.service.database

import com.apple.foundationdb.record.{IndexScanType, TupleRange}
import com.apple.foundationdb.tuple.Tuple

import anduin.fdb.record.FDBOperations
import anduin.fdb.record.model.{RecordIO, RecordTask}
import anduin.id.ontology.OntologyAsaId
import anduin.ontology.asa.{EdgeInfo, OntologyAsa}
import anduin.ontology.service.database.OntologyAsaStoreProvider.{*, given}

final case class OntologyAsaStoreOperations(
  store: OntologyAsaStoreProvider.Store
) {

  private val parallelism = 8

  def create(record: OntologyAsa): RecordTask[Unit] = {
    store.create(record.copy(uniqueId = constructUniqueId(record.edges))).unit
  }

  def get(id: OntologyAsaId): RecordTask[OntologyAsa] = {
    store.get(id)
  }

  def find(edges: Seq[EdgeInfo]): RecordTask[Option[OntologyAsa]] = {
    val tupleRange = TupleRange.allOf(
      Tuple.from(constructUniqueId(edges))
    )
    store
      .scanIndexRecordsL(
        OntologyAsaStoreProvider.uniqueIdMapping,
        tupleRange,
        IndexScanType.BY_VALUE
      )
      .map(_.find(_.edges == edges))
  }

  def getOpt(id: OntologyAsaId): RecordTask[Option[OntologyAsa]] = {
    store.getOpt(id)
  }

  def batchGet(
    ids: Seq[OntologyAsaId]
  ): RecordTask[Seq[OntologyAsa]] = {
    RecordIO.parTraverseN(parallelism)(ids.distinct)(id => getOpt(id)).map(_.flatten)
  }

  def update(
    id: OntologyAsaId,
    updateFunc: OntologyAsa => OntologyAsa
  ): RecordTask[OntologyAsa] = {
    store
      .update(
        key = id,
        updateFn = (asa: OntologyAsa) => {
          val updatedAsa = updateFunc(asa)
          updatedAsa.copy(id = id, uniqueId = constructUniqueId(updatedAsa.edges))
        }
      )
  }

  def exist(id: OntologyAsaId): RecordTask[Boolean] = {
    store.exist(id)
  }

  def queryByNodeIdInNodePath(
    // TODO: @hiepbui to use stronger ID type
    nodeId: String
  ): RecordTask[Seq[OntologyAsa]] = {
    val tupleRange = TupleRange.allOf(Tuple.from(nodeId))
    store
      .scanIndexRecordsL(
        OntologyAsaStoreProvider.nodeMapping,
        tupleRange,
        IndexScanType.BY_VALUE
      )
      .map(_.distinctBy(_.id))
  }

  def queryByEdgeInNodePath(
    edge: EdgeInfo
  ): RecordTask[Seq[OntologyAsa]] = {
    val tupleRange = TupleRange.allOf(
      Tuple.from(edge.sourceNode, edge.targetNode, edge.edgeType.value)
    )
    store
      .scanIndexRecordsL(
        OntologyAsaStoreProvider.edgeMapping,
        tupleRange,
        IndexScanType.BY_VALUE
      )
      .map(_.distinctBy(_.id))
  }

  def delete(id: OntologyAsaId): RecordTask[Boolean] = {
    store.delete(id)
  }

  def clean(): RecordTask[Unit] = {
    store.deleteAllRecords()
  }

  def scanAll(): RecordTask[Seq[OntologyAsa]] = {
    store
      .scanL(
        mapping = OntologyAsaStoreProvider.primaryKeyMapping,
        tupleRange = TupleRange.ALL
      )
      .map(_.map(_._2))
  }

  def batchDelete(ids: Seq[OntologyAsaId]): RecordTask[Map[OntologyAsaId, Boolean]] = {
    RecordIO.parTraverseN(parallelism)(ids.distinct)(id => store.delete(id).map(result => id -> result)).map(_.toMap)
  }

  private def constructUniqueId(edges: Seq[EdgeInfo]): String = {
    edges.headOption.fold(
      ""
    ) { head =>
      (head.sourceNode +: edges.flatMap(edge => Seq(edge.edgeType.value.toString, edge.targetNode))).mkString("#")
    }
  }

}

object OntologyAsaStoreOperations
    extends FDBOperations.Single[OntologyAsaStoreProvider.RecordEnum, OntologyAsaStoreOperations](
      OntologyAsaStoreProvider
    )
