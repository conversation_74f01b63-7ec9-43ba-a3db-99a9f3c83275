// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.sa.service.database

import com.apple.foundationdb.record.RecordMetaDataBuilder
import com.apple.foundationdb.record.metadata.Key

import anduin.fdb.record.{FDBRecordEnum, FDBRecordKeySpace, FDBRecordStoreProvider, FDBStoreProviderCompanion}
import anduin.id.sa.SaDataTemplateId
import anduin.sa.model.sadatatemplate.sadatatemplateactivitymessage.{
  SaDataTemplateActivityMessage,
  SaDataTemplateActivityMessageProto
}

private[service] final case class SaDataTemplateActivityStoreProvider(
  override protected val keySpace: FDBRecordKeySpace
) extends FDBRecordStoreProvider[FDBRecordEnum.SaDataTemplateActivityStore.type](
      FDBRecordEnum.SaDataTemplateActivityStore,
      SaDataTemplateActivityMessageProto
    ) {

  override protected def recordBuilderFn(
    builder: RecordMetaDataBuilder
  ): Unit = {
    builder
      .getRecordType(SaDataTemplateActivityStoreProvider.RecordTypeName)
      .setPrimaryKey(SaDataTemplateActivityStoreProvider.primaryKeyExpression)
  }

  override protected def indexes: Seq[IndexMappingWithVersion] = Seq.empty

}

private[service] object SaDataTemplateActivityStoreProvider
    extends FDBStoreProviderCompanion[FDBRecordEnum.SaDataTemplateActivityStore.type] {

  val RecordTypeName: String = SaDataTemplateActivityMessage.scalaDescriptor.name

  private val primaryKeyExpression = Key.Expressions.field("id")

  given primaryKeyMapping: Mapping[SaDataTemplateId, SaDataTemplateActivityMessage] = mappingInstance

}
