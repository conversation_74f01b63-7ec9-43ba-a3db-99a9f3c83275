// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation.funddata

import javax.sql.DataSource
import zio.{RIO, ZIO}

import anduin.greylin.core.operation.{TiDBModelOperation, TiDBModelOperationCompanion, TiDBModelOperationContext}
import anduin.greylin.modelti.funddata.FundDataInvestmentEntityProfile
import anduin.id.funddata.FundDataInvestmentEntityId
import io.getquill.*

import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given

final case class FundDataInvestmentEntityProfileOperations private[greylin] (
  override val ctx: TiDBModelOperationContext
) extends TiDBModelOperation[FundDataInvestmentEntityId, FundDataInvestmentEntityProfile] {

  private inline def fundDataInvestmentEntityProfile = quote(
    querySchema[FundDataInvestmentEntityProfile](FundDataInvestmentEntityProfile.tableName)
  )

  override def getOpt(id: FundDataInvestmentEntityId): RIO[DataSource, Option[FundDataInvestmentEntityProfile]] = {
    run(fundDataInvestmentEntityProfile.filter(_.investmentEntityId == lift(id))).map(_.headOption)
  }

  override def insert(model: FundDataInvestmentEntityProfile): RIO[DataSource, FundDataInvestmentEntityProfile] = {
    for {
      _ <- addDataEvent(model.primaryKey)
      _ <- run(fundDataInvestmentEntityProfile.insertValue(lift(model))).as(model)
    } yield model
  }

  override def update(model: FundDataInvestmentEntityProfile): RIO[DataSource, FundDataInvestmentEntityProfile] = {
    for {
      _ <- addDataEvent(model.primaryKey)
      _ <- run(
        fundDataInvestmentEntityProfile
          .filter(_.investmentEntityId == lift(model.investmentEntityId))
          .updateValue(lift(model))
      )
    } yield model
  }

  override def delete(id: FundDataInvestmentEntityId): RIO[DataSource, Unit] = {
    for {
      _ <- addDataEvent(id)
      _ <- run(fundDataInvestmentEntityProfile.filter(_.investmentEntityId == lift(id)).delete)
    } yield ()
  }

  def get(id: FundDataInvestmentEntityId): RIO[DataSource, FundDataInvestmentEntityProfile] = {
    for {
      modelOpt <- getOpt(id)
      fundDataInvestmentEntityProfile <- ZIO.getOrFail(modelOpt)
    } yield fundDataInvestmentEntityProfile
  }

  def get(ids: List[FundDataInvestmentEntityId]): RIO[DataSource, List[FundDataInvestmentEntityProfile]] = {
    run(fundDataInvestmentEntityProfile.filter { fundDataInvestmentEntityProfile =>
      liftQuery(ids).contains(fundDataInvestmentEntityProfile.investmentEntityId)
    })
  }

}

object FundDataInvestmentEntityProfileOperations
    extends TiDBModelOperationCompanion[FundDataInvestmentEntityProfileOperations]
