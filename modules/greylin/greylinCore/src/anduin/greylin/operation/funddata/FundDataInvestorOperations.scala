// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation.funddata

import javax.sql.DataSource
import zio.{RIO, ZIO}

import anduin.greylin.core.operation.{TiDBModelOperation, TiDBModelOperationCompanion, TiDBModelOperationContext}
import anduin.greylin.modelti.funddata.FundDataInvestor
import anduin.id.funddata.{FundDataFirmId, FundDataInvestorId}
import io.getquill.*

import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given

final case class FundDataInvestorOperations private[greylin] (
  override val ctx: TiDBModelOperationContext
) extends TiDBModelOperation[FundDataInvestorId, FundDataInvestor] {

  private inline def fundDataInvestor = quote(
    querySchema[FundDataInvestor]("pb_fund_data_investor")
  )

  override def getOpt(id: FundDataInvestorId): RIO[DataSource, Option[FundDataInvestor]] = {
    run(fundDataInvestor.filter(_.id == lift(id))).map(_.headOption)
  }

  override def insert(model: FundDataInvestor): RIO[DataSource, FundDataInvestor] = {
    run(fundDataInvestor.insertValue(lift(model))).as(model)
  }

  override def update(model: FundDataInvestor): RIO[DataSource, FundDataInvestor] = {
    run(fundDataInvestor.filter(_.id == lift(model.id)).updateValue(lift(model))).as(model)
  }

  override def delete(id: FundDataInvestorId): RIO[DataSource, Unit] = {
    run(fundDataInvestor.filter(_.id == lift(id)).delete).unit
  }

  def get(id: FundDataInvestorId): RIO[DataSource, FundDataInvestor] = {
    for {
      modelOpt <- getOpt(id)
      fundDataInvestor <- ZIO.getOrFail(modelOpt)
    } yield fundDataInvestor
  }

  def get(ids: List[FundDataInvestorId]): RIO[DataSource, List[FundDataInvestor]] = {
    run(fundDataInvestor.filter { fundDataInvestor =>
      liftQuery(ids).contains(fundDataInvestor.id)
    })
  }

  def get(firmId: FundDataFirmId): RIO[DataSource, List[FundDataInvestor]] = {
    run(fundDataInvestor.filter(ie => sql"${ie.id} LIKE ${lift(firmId.idString + "%")}".asCondition))
  }

  def insert(models: List[FundDataInvestor]): RIO[DataSource, List[FundDataInvestor]] = {
    run(liftQuery(models).foreach(model => fundDataInvestor.insertValue(model)), 1000).map(_ => models)
  }

  def update(id: FundDataInvestorId)(updateFn: FundDataInvestor => FundDataInvestor)
    : RIO[DataSource, FundDataInvestor] = {
    for {
      model <- get(id)
      newModel <- ZIO.attempt(updateFn(model))
      _ <- run(fundDataInvestor.filter(_.id == lift(id)).updateValue(lift(newModel)))
    } yield newModel
  }

  def delete(ids: List[FundDataInvestorId]): ZIO[DataSource, Throwable, Unit] = {
    run(liftQuery(ids).foreach(id => fundDataInvestor.filter(_.id == id).delete)).unit
  }

}

object FundDataInvestorOperations extends TiDBModelOperationCompanion[FundDataInvestorOperations]
