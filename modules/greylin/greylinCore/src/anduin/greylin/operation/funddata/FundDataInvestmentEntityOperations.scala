// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation.funddata

import javax.sql.DataSource
import zio.{RIO, ZIO}

import anduin.greylin.core.operation.{TiDBModelOperation, TiDBModelOperationCompanion, TiDBModelOperationContext}
import anduin.greylin.modelti.funddata.FundDataInvestmentEntity
import anduin.id.funddata.{FundDataFirmId, FundDataFundId, FundDataInvestmentEntityId}
import io.getquill.*

import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given
import anduin.id.tag.TagItemId

final case class FundDataInvestmentEntityOperations private[greylin] (
  override val ctx: TiDBModelOperationContext
) extends TiDBModelOperation[FundDataInvestmentEntityId, FundDataInvestmentEntity] {

  private inline def fundDataInvestmentEntity = quote(
    querySchema[FundDataInvestmentEntity]("pb_fund_data_investment_entity")
  )

  override def getOpt(id: FundDataInvestmentEntityId): RIO[DataSource, Option[FundDataInvestmentEntity]] = {
    run(fundDataInvestmentEntity.filter(_.id == lift(id))).map(_.headOption)
  }

  override def insert(model: FundDataInvestmentEntity): RIO[DataSource, FundDataInvestmentEntity] = {
    run(fundDataInvestmentEntity.insertValue(lift(model))).as(model)
  }

  override def update(model: FundDataInvestmentEntity): RIO[DataSource, FundDataInvestmentEntity] = {
    run(fundDataInvestmentEntity.filter(_.id == lift(model.id)).updateValue(lift(model))).as(model)
  }

  override def delete(id: FundDataInvestmentEntityId): RIO[DataSource, Unit] = {
    run(fundDataInvestmentEntity.filter(_.id == lift(id)).delete).unit
  }

  def get(id: FundDataInvestmentEntityId): RIO[DataSource, FundDataInvestmentEntity] = {
    for {
      modelOpt <- getOpt(id)
      fundDataInvestmentEntity <- ZIO.getOrFail(modelOpt)
    } yield fundDataInvestmentEntity
  }

  def get(ids: List[FundDataInvestmentEntityId]): RIO[DataSource, List[FundDataInvestmentEntity]] = {
    run(fundDataInvestmentEntity.filter { fundDataInvestmentEntity =>
      liftQuery(ids).contains(fundDataInvestmentEntity.id)
    })
  }

  def get(fundId: FundDataFundId): RIO[DataSource, List[FundDataInvestmentEntity]] = {
    run(fundDataInvestmentEntity.filter(ie => sql"${ie.id} LIKE ${lift(fundId.idString + "%")}".asCondition))
  }

  def get(firmId: FundDataFirmId): RIO[DataSource, List[FundDataInvestmentEntity]] = {
    run(fundDataInvestmentEntity.filter(ie => sql"${ie.id} LIKE ${lift(firmId.idString + "%")}".asCondition))
  }

  def insert(models: List[FundDataInvestmentEntity]): RIO[DataSource, List[FundDataInvestmentEntity]] = {
    run(liftQuery(models).foreach(model => fundDataInvestmentEntity.insertValue(model)), 1000).map(_ => models)
  }

  def update(id: FundDataInvestmentEntityId)(updateFn: FundDataInvestmentEntity => FundDataInvestmentEntity)
    : RIO[DataSource, FundDataInvestmentEntity] = {
    for {
      model <- get(id)
      newModel <- ZIO.attempt(updateFn(model))
      _ <- run(fundDataInvestmentEntity.filter(_.id == lift(id)).updateValue(lift(newModel)))
    } yield newModel
  }

  def delete(ids: List[FundDataInvestmentEntityId]): ZIO[DataSource, Throwable, Unit] = {
    run(liftQuery(ids).foreach(id => fundDataInvestmentEntity.filter(_.id == id).delete)).unit
  }

  def removeInvestorType(investorTypes: List[TagItemId]): ZIO[DataSource, Throwable, Unit] = {
    run(
      fundDataInvestmentEntity
        .filter(ie => liftQuery(investorTypes).contains(ie.investorType))
        .update(ie => ie.investorType -> lift(Option.empty[TagItemId]))
    ).unit
  }

  def removeJurisdictionType(jurisdictionTypes: List[TagItemId]): ZIO[DataSource, Throwable, Unit] = {
    run(
      fundDataInvestmentEntity
        .filter(ie => liftQuery(jurisdictionTypes).contains(ie.jurisdictionType))
        .update(ie => ie.jurisdictionType -> lift(Option.empty[TagItemId]))
    ).unit
  }

}

object FundDataInvestmentEntityOperations extends TiDBModelOperationCompanion[FundDataInvestmentEntityOperations]
