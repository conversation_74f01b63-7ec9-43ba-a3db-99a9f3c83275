// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation.funddata

import javax.sql.DataSource
import zio.{RIO, ZIO}

import anduin.greylin.core.operation.{TiDBModelOperation, TiDBModelOperationCompanion, TiDBModelOperationContext}
import anduin.greylin.modelti.funddata.FundDataInvestorTag
import anduin.id.funddata.{FundDataFirmId, FundDataInvestorId}
import io.getquill.*

import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given
import anduin.id.tag.TagItemId

final case class FundDataInvestorTagOperations private[greylin] (
  override val ctx: TiDBModelOperationContext
) extends TiDBModelOperation[(FundDataInvestorId, TagItemId), FundDataInvestorTag] {

  private inline def fundDataInvestorTag = quote(
    querySchema[FundDataInvestorTag]("pb_fund_data_investor_tag")
  )

  override def getOpt(id: (FundDataInvestorId, TagItemId)): RIO[DataSource, Option[FundDataInvestorTag]] = {
    run(
      fundDataInvestorTag.filter(pair => pair.investorId == lift(id._1) && pair.tagId == lift(id._2))
    )
      .map(_.headOption)
  }

  override def insert(model: FundDataInvestorTag): RIO[DataSource, FundDataInvestorTag] = {
    run(fundDataInvestorTag.insertValue(lift(model))).as(model)
  }

  override def update(model: FundDataInvestorTag): RIO[DataSource, FundDataInvestorTag] = {
    run(
      fundDataInvestorTag
        .filter(pair => pair.investorId == lift(model.investorId) && pair.tagId == lift(model.tagId))
        .updateValue(lift(model))
    ).as(model)
  }

  override def delete(id: (FundDataInvestorId, TagItemId)): RIO[DataSource, Unit] = {
    run(
      fundDataInvestorTag
        .filter(pair => pair.investorId == lift(id._1) && pair.tagId == lift(id._2))
        .delete
    ).unit
  }

  def get(investorId: FundDataInvestorId): RIO[DataSource, List[FundDataInvestorTag]] = {
    run(fundDataInvestorTag.filter(_.investorId == lift(investorId)))
  }

  def get(firmId: FundDataFirmId): RIO[DataSource, List[FundDataInvestorTag]] = {
    run(fundDataInvestorTag.filter(tag => sql"${tag.investorId} LIKE ${lift(firmId.idString + "%")}".asCondition))
  }

  def insert(models: List[FundDataInvestorTag]): RIO[DataSource, List[FundDataInvestorTag]] = {
    run(liftQuery(models).foreach(model => fundDataInvestorTag.insertValue(model)), 1000).map(_ => models)
  }

  def delete(ids: List[(FundDataInvestorId, TagItemId)]): ZIO[DataSource, Throwable, Unit] = {
    ZIO.foreachDiscard(ids) { id => delete(id) }
  }

  def delete(investorId: FundDataInvestorId): RIO[DataSource, Unit] = {
    run(fundDataInvestorTag.filter(_.investorId == lift(investorId)).delete).unit
  }

  def removeTagItem(tagIds: List[TagItemId]): RIO[DataSource, Unit] = {
    run(fundDataInvestorTag.filter(tag => liftQuery(tagIds).contains(tag.tagId)).delete).unit
  }

}

object FundDataInvestorTagOperations extends TiDBModelOperationCompanion[FundDataInvestorTagOperations]
