// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation.funddata

import javax.sql.DataSource
import zio.RIO

import anduin.greylin.core.operation.{TiDBModelOperation, TiDBModelOperationCompanion, TiDBModelOperationContext}
import anduin.greylin.modelti.funddata.FundDataFirmGroupMember
import io.getquill.*

import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given
import anduin.id.funddata.FundDataFirmId
import anduin.model.common.user.UserId
import anduin.model.id.TeamId

final case class FundDataFirmGroupMemberOperations private[greylin] (
  override val ctx: TiDBModelOperationContext
) extends TiDBModelOperation[(UserId, TeamId), FundDataFirmGroupMember] {

  private inline def firmGroupMember = quote(
    querySchema[FundDataFirmGroupMember](FundDataFirmGroupMember.tableName)
  )

  private inline def filterById(id: (UserId, TeamId)) = quote(
    firmGroupMember.filter { m => m.userId == lift(id._1) && m.groupId == lift(id._2) }
  )

  override def getOpt(id: (UserId, TeamId)): RIO[DataSource, Option[FundDataFirmGroupMember]] =
    run(filterById(id)).map(_.headOption)

  override def insert(model: FundDataFirmGroupMember): RIO[DataSource, FundDataFirmGroupMember] =
    run(firmGroupMember.insertValue(lift(model))).as(model)

  override def update(model: FundDataFirmGroupMember): RIO[DataSource, FundDataFirmGroupMember] =
    run(filterById(model.userId -> model.groupId).updateValue(lift(model))).as(model)

  override def delete(id: (UserId, TeamId)): RIO[DataSource, Unit] =
    run(filterById(id).delete).unit

  def get(firmId: FundDataFirmId): RIO[DataSource, List[FundDataFirmGroupMember]] =
    run(
      firmGroupMember.filter { member =>
        sql"${member.groupId} LIKE ${lift(firmId.idString + '%')}".asCondition
      }
    )

  def get(userId: UserId): RIO[DataSource, List[FundDataFirmGroupMember]] =
    run(firmGroupMember.filter(_.userId == lift(userId)))

  def getOpt(userId: UserId, firmId: FundDataFirmId): RIO[DataSource, Option[FundDataFirmGroupMember]] =
    run(
      firmGroupMember.filter(model =>
        model.userId == lift(userId) && sql"${model.groupId} LIKE ${lift(firmId.idString + "%")}".asCondition
      )
    ).map(_.headOption)

}

object FundDataFirmGroupMemberOperations extends TiDBModelOperationCompanion[FundDataFirmGroupMemberOperations]
