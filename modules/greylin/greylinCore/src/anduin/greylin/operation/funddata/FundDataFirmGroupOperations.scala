// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation.funddata

import javax.sql.DataSource
import zio.{RIO, ZIO}

import anduin.greylin.core.operation.{TiDBModelOperation, TiDBModelOperationCompanion, TiDBModelOperationContext}
import anduin.greylin.modelti.funddata.FundDataFirmGroup
import io.getquill.*

import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given
import anduin.id.funddata.FundDataFirmId
import anduin.model.id.TeamId

final case class FundDataFirmGroupOperations private[greylin] (
  override val ctx: TiDBModelOperationContext
) extends TiDBModelOperation[TeamId, FundDataFirmGroup] {

  private inline def firmGroup = quote(
    querySchema[FundDataFirmGroup](FundDataFirmGroup.tableName)
  )

  private inline def filterById(id: TeamId) = quote(
    firmGroup.filter(_.id == lift(id))
  )

  override def getOpt(id: TeamId): RIO[DataSource, Option[FundDataFirmGroup]] =
    run(filterById(id)).map(_.headOption)

  override def insert(model: FundDataFirmGroup): RIO[DataSource, FundDataFirmGroup] =
    run(firmGroup.insertValue(lift(model))).as(model)

  override def update(model: FundDataFirmGroup): RIO[DataSource, FundDataFirmGroup] =
    run(filterById(model.id).updateValue(lift(model))).as(model)

  override def delete(id: TeamId): RIO[DataSource, Unit] =
    run(filterById(id)).unit

  def delete(ids: List[TeamId]): ZIO[DataSource, Throwable, Unit] = {
    run(liftQuery(ids).foreach(id => firmGroup.filter(_.id == id).delete)).unit
  }

  def get(id: TeamId): RIO[DataSource, FundDataFirmGroup] = {
    for {
      modelOpt <- getOpt(id)
      fundDataFirmGroup <- ZIO.getOrFail(modelOpt)
    } yield fundDataFirmGroup
  }

  def get(firmId: FundDataFirmId): RIO[DataSource, List[FundDataFirmGroup]] =
    run(firmGroup.filter(_.firmId == lift(firmId)))

  def get(ids: List[TeamId]): RIO[DataSource, List[FundDataFirmGroup]] =
    run(firmGroup.filter(group => liftQuery(ids).contains(group.id)))

}

object FundDataFirmGroupOperations extends TiDBModelOperationCompanion[FundDataFirmGroupOperations]
