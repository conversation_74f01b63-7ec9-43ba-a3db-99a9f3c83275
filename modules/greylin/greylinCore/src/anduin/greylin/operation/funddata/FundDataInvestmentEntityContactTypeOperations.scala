// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation.funddata

import javax.sql.DataSource
import zio.{RIO, ZIO}

import anduin.greylin.core.operation.{TiDBModelOperation, TiDBModelOperationCompanion, TiDBModelOperationContext}
import anduin.greylin.modelti.funddata.FundDataInvestmentEntityContactType
import anduin.id.funddata.{FundDataFirmId, FundDataInvestmentEntityContactId, FundDataInvestmentEntityId}
import io.getquill.*

import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given
import anduin.id.tag.TagItemId

final case class FundDataInvestmentEntityContactTypeOperations private[greylin] (
  override val ctx: TiDBModelOperationContext
) extends TiDBModelOperation[(FundDataInvestmentEntityContactId, TagItemId), FundDataInvestmentEntityContactType] {

  private inline def fundDataInvestmentEntityContactType = quote(
    querySchema[FundDataInvestmentEntityContactType]("pb_fund_data_investment_entity_contact_type")
  )

  override def getOpt(id: (FundDataInvestmentEntityContactId, TagItemId))
    : RIO[DataSource, Option[FundDataInvestmentEntityContactType]] = {
    run(
      fundDataInvestmentEntityContactType.filter(pair => pair.contactId == lift(id._1) && pair.contactType == lift(id._2))
    )
      .map(_.headOption)
  }

  override def insert(model: FundDataInvestmentEntityContactType)
    : RIO[DataSource, FundDataInvestmentEntityContactType] = {
    run(fundDataInvestmentEntityContactType.insertValue(lift(model))).as(model)
  }

  override def update(model: FundDataInvestmentEntityContactType)
    : RIO[DataSource, FundDataInvestmentEntityContactType] = {
    run(
      fundDataInvestmentEntityContactType
        .filter(pair => pair.contactId == lift(model.contactId) && pair.contactType == lift(model.contactType))
        .updateValue(lift(model))
    ).as(model)
  }

  override def delete(id: (FundDataInvestmentEntityContactId, TagItemId)): RIO[DataSource, Unit] = {
    run(
      fundDataInvestmentEntityContactType
        .filter(pair => pair.contactId == lift(id._1) && pair.contactType == lift(id._2))
        .delete
    ).unit
  }

  def get(contactId: FundDataInvestmentEntityContactId): RIO[DataSource, List[FundDataInvestmentEntityContactType]] = {
    run(fundDataInvestmentEntityContactType.filter(_.contactId == lift(contactId)))
  }

  def get(firmId: FundDataFirmId): RIO[DataSource, List[FundDataInvestmentEntityContactType]] = {
    run(
      fundDataInvestmentEntityContactType.filter(contact =>
        sql"${contact.contactId} LIKE ${lift(firmId.idString + "%")}".asCondition
      )
    )
  }

  def get(investmentEntityId: FundDataInvestmentEntityId)
    : RIO[DataSource, List[FundDataInvestmentEntityContactType]] = {
    run(
      fundDataInvestmentEntityContactType.filter(contact =>
        sql"${contact.contactId} LIKE ${lift(investmentEntityId.idString + "%")}".asCondition
      )
    )
  }

  def insert(models: List[FundDataInvestmentEntityContactType])
    : RIO[DataSource, List[FundDataInvestmentEntityContactType]] = {
    run(liftQuery(models).foreach(model => fundDataInvestmentEntityContactType.insertValue(model)), 1000).map(_ =>
      models
    )
  }

  def delete(ids: List[(FundDataInvestmentEntityContactId, TagItemId)]): ZIO[DataSource, Throwable, Unit] = {
    ZIO.foreachDiscard(ids) { id => delete(id) }
  }

  def delete(contactId: FundDataInvestmentEntityContactId): RIO[DataSource, Unit] = {
    run(fundDataInvestmentEntityContactType.filter(_.contactId == lift(contactId)).delete).unit
  }

  def removeContactType(contactTypes: List[TagItemId]): RIO[DataSource, Unit] = {
    run(
      fundDataInvestmentEntityContactType
        .filter(contact => liftQuery(contactTypes).contains(contact.contactType))
        .delete
    ).unit
  }

}

object FundDataInvestmentEntityContactTypeOperations
    extends TiDBModelOperationCompanion[FundDataInvestmentEntityContactTypeOperations]
