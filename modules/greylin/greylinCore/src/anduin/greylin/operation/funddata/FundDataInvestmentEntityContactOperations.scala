// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation.funddata

import javax.sql.DataSource
import zio.{RIO, ZIO}

import anduin.greylin.core.operation.{TiDBModelOperation, TiDBModelOperationCompanion, TiDBModelOperationContext}
import anduin.greylin.modelti.funddata.FundDataInvestmentEntityContact
import anduin.id.funddata.{FundDataFirmId, FundDataInvestmentEntityContactId, FundDataInvestmentEntityId}
import io.getquill.*

import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given

final case class FundDataInvestmentEntityContactOperations private[greylin] (
  override val ctx: TiDBModelOperationContext
) extends TiDBModelOperation[FundDataInvestmentEntityContactId, FundDataInvestmentEntityContact] {

  private inline def fundDataInvestmentEntityContact = quote(
    querySchema[FundDataInvestmentEntityContact]("pb_fund_data_investment_entity_contact")
  )

  override def getOpt(id: FundDataInvestmentEntityContactId): RIO[DataSource, Option[FundDataInvestmentEntityContact]] = {
    run(fundDataInvestmentEntityContact.filter(_.id == lift(id))).map(_.headOption)
  }

  override def insert(model: FundDataInvestmentEntityContact): RIO[DataSource, FundDataInvestmentEntityContact] = {
    run(fundDataInvestmentEntityContact.insertValue(lift(model))).as(model)
  }

  override def update(model: FundDataInvestmentEntityContact): RIO[DataSource, FundDataInvestmentEntityContact] = {
    run(fundDataInvestmentEntityContact.filter(_.id == lift(model.id)).updateValue(lift(model))).as(model)
  }

  override def delete(id: FundDataInvestmentEntityContactId): RIO[DataSource, Unit] = {
    run(fundDataInvestmentEntityContact.filter(_.id == lift(id)).delete).unit
  }

  def get(id: FundDataInvestmentEntityContactId): RIO[DataSource, FundDataInvestmentEntityContact] = {
    for {
      modelOpt <- getOpt(id)
      fundDataInvestmentEntityContact <- ZIO.getOrFail(modelOpt)
    } yield fundDataInvestmentEntityContact
  }

  def get(ids: List[FundDataInvestmentEntityContactId]): RIO[DataSource, List[FundDataInvestmentEntityContact]] = {
    run(fundDataInvestmentEntityContact.filter { fundDataInvestmentEntityContact =>
      liftQuery(ids).contains(fundDataInvestmentEntityContact.id)
    })
  }

  def get(investmentEntityId: FundDataInvestmentEntityId): RIO[DataSource, List[FundDataInvestmentEntityContact]] = {
    run(
      fundDataInvestmentEntityContact.filter(contact =>
        sql"${contact.id} LIKE ${lift(investmentEntityId.idString + "%")}".asCondition
      )
    )
  }

  def get(firmId: FundDataFirmId): RIO[DataSource, List[FundDataInvestmentEntityContact]] = {
    run(
      fundDataInvestmentEntityContact.filter(contact =>
        sql"${contact.id} LIKE ${lift(firmId.idString + "%")}".asCondition
      )
    )
  }

  def insert(models: List[FundDataInvestmentEntityContact]): RIO[DataSource, List[FundDataInvestmentEntityContact]] = {
    run(liftQuery(models).foreach(model => fundDataInvestmentEntityContact.insertValue(model)), 1000).map(_ => models)
  }

  def update(
    id: FundDataInvestmentEntityContactId
  )(
    updateFn: FundDataInvestmentEntityContact => FundDataInvestmentEntityContact
  ): RIO[DataSource, FundDataInvestmentEntityContact] = {
    for {
      model <- get(id)
      newModel <- ZIO.attempt(updateFn(model))
      _ <- run(fundDataInvestmentEntityContact.filter(_.id == lift(id)).updateValue(lift(newModel)))
    } yield newModel
  }

  def delete(ids: List[FundDataInvestmentEntityContactId]): ZIO[DataSource, Throwable, Unit] = {
    run(liftQuery(ids).foreach(id => fundDataInvestmentEntityContact.filter(_.id == id).delete)).unit
  }

  def delete(investmentEntityId: FundDataInvestmentEntityId): ZIO[DataSource, Throwable, Unit] = {
    run(
      fundDataInvestmentEntityContact
        .filter(contact => sql"${contact.id} LIKE ${lift(investmentEntityId.idString + "%")}".asCondition)
        .delete
    ).unit
  }

}

object FundDataInvestmentEntityContactOperations
    extends TiDBModelOperationCompanion[FundDataInvestmentEntityContactOperations]
