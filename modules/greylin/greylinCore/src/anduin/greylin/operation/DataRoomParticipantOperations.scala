// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation

import javax.sql.DataSource
import zio.ZIO

import anduin.greylin.modelti.DataRoomParticipant
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId

import io.getquill.*
import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given

object DataRoomParticipantOperations {

  private inline def dataRoomParticipant = quote(
    querySchema[DataRoomParticipant]("pb_data_room_participant")
  )

  def addParticipants(models: List[DataRoomParticipant]): ZIO[DataSource, Throwable, List[DataRoomParticipant]] = {
    run(
      liftQuery(models).foreach(model =>
        dataRoomParticipant
          .insertValue(model)
          .onConflictUpdate((oldModel, newModel) => oldModel.state -> newModel.state)
      ),
      1000
    ).map(_ => models)
  }

  def removeParticipants(users: List[(UserId, DataRoomWorkflowId)]): ZIO[DataSource, Throwable, Unit] =
    run(
      liftQuery(users).foreach { case (userId, dataRoomId) =>
        dataRoomParticipant.filter { participant =>
          participant.userId == userId && participant.dataRoomId == dataRoomId
        }.delete
      },
      1000
    ).unit

  def getOpt(
    userId: UserId,
    dataRoomId: DataRoomWorkflowId
  ): ZIO[DataSource, Throwable, Option[DataRoomParticipant]] = {
    run(
      dataRoomParticipant.filter(participant =>
        participant.userId == lift(userId) && participant.dataRoomId == lift(dataRoomId)
      )
    ).map(_.headOption)
  }

  def get(userId: UserId, dataRoomId: DataRoomWorkflowId): ZIO[DataSource, Throwable, DataRoomParticipant] = {
    for {
      modelOpt <- getOpt(userId, dataRoomId)
      participant <- ZIO.getOrFail(modelOpt)
    } yield participant
  }

  def getByUserId(userId: UserId): ZIO[DataSource, Throwable, List[DataRoomParticipant]] = {
    run(dataRoomParticipant.filter(_.userId == lift(userId)))
  }

  def getByDataRoomId(dataRoomId: DataRoomWorkflowId): ZIO[DataSource, Throwable, List[DataRoomParticipant]] = {
    run(dataRoomParticipant.filter(_.dataRoomId == lift(dataRoomId)))
  }

  def countByDataRoomId(dataRoomId: DataRoomWorkflowId): ZIO[DataSource, Throwable, Long] = {
    run(dataRoomParticipant.filter(_.dataRoomId == lift(dataRoomId)).size)
  }

  def update(
    userId: UserId,
    dataRoomId: DataRoomWorkflowId
  )(
    updateFn: DataRoomParticipant => DataRoomParticipant
  ): ZIO[DataSource, Throwable, DataRoomParticipant] = {
    for {
      model <- get(userId, dataRoomId)
      newModel <- ZIO.attempt(updateFn(model))
      _ <- run(
        dataRoomParticipant
          .filter(participant => participant.userId == lift(userId) && participant.dataRoomId == lift(dataRoomId))
          .updateValue(lift(newModel))
      )
    } yield newModel
  }

}
