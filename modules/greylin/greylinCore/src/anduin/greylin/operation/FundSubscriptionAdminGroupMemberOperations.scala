// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation

import javax.sql.DataSource
import zio.ZIO

import anduin.greylin.modelti.FundSubscriptionAdminGroupMember
import anduin.model.common.user.UserId
import anduin.model.id.TeamId
import io.getquill.*

import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given
import anduin.id.fundsub.FundSubId

object FundSubscriptionAdminGroupMemberOperations {

  private inline def adminGroupMember = quote(
    querySchema[FundSubscriptionAdminGroupMember]("pb_fund_subscription_admin_group_member")
  )

  def addMembers(users: List[(UserId, TeamId)]): ZIO[DataSource, Throwable, Unit] = {
    val models = users.map { case (userId, teamId) =>
      FundSubscriptionAdminGroupMember(userId, teamId)
    }
    run(
      liftQuery(models).foreach(model => adminGroupMember.insertValue(model)),
      1000
    ).unit
  }

  def moveMembers(users: List[(UserId, TeamId)], newGroupId: TeamId): ZIO[DataSource, Throwable, Unit] = {
    ZIO.foreachDiscard(users) { case (userId, groupId) =>
      run(
        adminGroupMember
          .filter { member =>
            member.userId == lift(userId) && member.groupId == lift(groupId)
          }
          .updateValue(lift(FundSubscriptionAdminGroupMember(userId, newGroupId)))
      )
    }
  }

  def removeMembers(users: List[(UserId, TeamId)]): ZIO[DataSource, Throwable, Unit] = {
    run(
      liftQuery(users).foreach { case (userId, groupId) =>
        adminGroupMember.filter { member =>
          member.userId == userId && member.groupId == groupId
        }.delete
      },
      1000
    ).unit
  }

  def isMember(
    userId: UserId,
    groupId: TeamId
  ): ZIO[DataSource, Throwable, Option[FundSubscriptionAdminGroupMember]] = {
    run(
      adminGroupMember.filter(member => member.userId == lift(userId) && member.groupId == lift(groupId))
    ).map(_.headOption)
  }

  def getByGroupIds(groupIds: List[TeamId]): ZIO[DataSource, Throwable, List[FundSubscriptionAdminGroupMember]] = {
    run(adminGroupMember.filter(member => liftQuery(groupIds).contains(member.groupId)))
  }

  def getByUserId(userId: UserId): ZIO[DataSource, Throwable, List[FundSubscriptionAdminGroupMember]] = {
    run(adminGroupMember.filter(_.userId == lift(userId)))
  }

  def getByUserIdAndFundSubId(
    userId: UserId,
    fundSubId: FundSubId
  ): ZIO[DataSource, Throwable, Option[FundSubscriptionAdminGroupMember]] = {
    run(
      adminGroupMember.filter(member =>
        member.userId == lift(userId) && sql"${member.groupId} LIKE ${lift(fundSubId.idString + "%")}".asCondition
      )
    ).map(_.headOption)
  }

}
