// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation

import javax.sql.DataSource
import zio.{RIO, ZIO}

import anduin.greylin.modelti.TestModel
import io.getquill.*

import anduin.greylin.core.operation.{TiDBModelOperation, TiDBModelOperationCompanion, TiDBModelOperationContext}
import anduin.tidb.TiDatabase.QuillContext.*

final case class TestModelOperations private[greylin] (
  override val ctx: TiDBModelOperationContext
) extends TiDBModelOperation[String, TestModel] {

  private inline def testModel = quote(
    querySchema[TestModel](TestModel.tableName)
  )

  override def insert(
    model: TestModel
  ): RIO[DataSource, TestModel] = {
    for {
      _ <- addDataEvent(model.primaryKey)
      _ <- run(testModel.insertValue(lift(model)))
    } yield model
  }

  override def update(
    model: TestModel
  ): RIO[DataSource, TestModel] = {
    for {
      _ <- addDataEvent(model.primaryKey)
      _ <- run(testModel.filter(_.id == lift(model.id)).updateValue(lift(model))).as(model)
    } yield model

  }

  def get(
    id: String
  ): ZIO[DataSource, Throwable, TestModel] = {
    for {
      modelOpt <- getOpt(id)
      fund <- ZIO.getOrFail(modelOpt)
    } yield fund
  }

  override def getOpt(
    id: String
  ): RIO[DataSource, Option[TestModel]] = {
    run(testModel.filter(m => m.id == lift(id))).map(_.headOption)
  }

  override def delete(
    id: String
  ): RIO[DataSource, Unit] = {
    for {
      _ <- addDataEvent(id)
      _ <- run(testModel.filter(m => m.id == lift(id)).delete).unit
    } yield ()
  }

}

object TestModelOperations extends TiDBModelOperationCompanion[TestModelOperations]
