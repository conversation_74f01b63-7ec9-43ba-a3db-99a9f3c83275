package anduin.greylin.operation.fundsub

import io.getquill.*
import javax.sql.DataSource
import squants.market.Currency
import zio.{RIO, ZIO}

import anduin.greylin.core.model.Codec.given
import anduin.greylin.core.operation.{TiDBModelOperation, TiDBModelOperationCompanion, TiDBModelOperationContext}
import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.modelti.{SubscriptionOrder, SubscriptionOrderStatus}
import anduin.greylin.modelti.fundsub.{FundSubscriptionSubFund, SubscriptionOrderSubFundInvestment}
import anduin.id.fundsub.{FundSubId, FundSubLpId, InvestmentFundId}

final case class SubscriptionOrderSubFundInvestmentOperations private[greylin] (
  override val ctx: TiDBModelOperationContext
) extends TiDBModelOperation[(FundSubLpId, InvestmentFundId), SubscriptionOrderSubFundInvestment] {

  private inline def subscriptionOrderSubFundInvestment = quote(
    querySchema[SubscriptionOrderSubFundInvestment](SubscriptionOrderSubFundInvestment.tableName)
  )

  private inline def fundSubscriptionSubFund = quote(
    querySchema[FundSubscriptionSubFund](FundSubscriptionSubFund.tableName)
  )

  private inline def subscriptionOrder = quote(
    querySchema[SubscriptionOrder](SubscriptionOrder.tableName)
  )

  override def getOpt(pk: (FundSubLpId, InvestmentFundId))
    : RIO[DataSource, Option[SubscriptionOrderSubFundInvestment]] = {
    run(subscriptionOrderSubFundInvestment.filter { model =>
      model.subscriptionOrderId == lift(pk._1) && model.subFundId == lift(pk._2)
    }).map(_.headOption)
  }

  override def insert(model: SubscriptionOrderSubFundInvestment)
    : RIO[DataSource, SubscriptionOrderSubFundInvestment] = {
    run(subscriptionOrderSubFundInvestment.insertValue(lift(model))).as(model)
  }

  override def update(model: SubscriptionOrderSubFundInvestment)
    : RIO[DataSource, SubscriptionOrderSubFundInvestment] = {
    run(
      subscriptionOrderSubFundInvestment
        .filter { investment =>
          investment.subscriptionOrderId == lift(model.subscriptionOrderId) && investment.subFundId == lift(
            model.subFundId
          )
        }
        .updateValue(lift(model))
    ).as(model)
  }

  override def delete(pk: (FundSubLpId, InvestmentFundId)): RIO[DataSource, Unit] = {
    run(
      subscriptionOrderSubFundInvestment.filter { investment =>
        investment.subscriptionOrderId == lift(pk._1) && investment.subFundId == lift(pk._2)
      }.delete
    ).unit
  }

  def insert(models: List[SubscriptionOrderSubFundInvestment])
    : RIO[DataSource, List[SubscriptionOrderSubFundInvestment]] = {
    run(
      liftQuery(models).foreach(model => subscriptionOrderSubFundInvestment.insertValue(model)),
      1000
    ).as(models)
  }

  def getByFund(fundSubId: FundSubId): RIO[DataSource, List[SubscriptionOrderSubFundInvestment]] = {
    run(
      subscriptionOrderSubFundInvestment.filter { investment =>
        sql"${investment.subscriptionOrderId} LIKE ${lift(fundSubId.idString + "%")}".asCondition
      }
    )
  }

  def getBySubscriptionOrder(subscriptionOrderId: FundSubLpId)
    : RIO[DataSource, List[SubscriptionOrderSubFundInvestment]] = {
    run(
      subscriptionOrderSubFundInvestment.filter { investment =>
        investment.subscriptionOrderId == lift(subscriptionOrderId)
      }
    )
  }

  def getBySubscriptionOrders(subscriptionOrderIds: List[FundSubLpId])
    : RIO[DataSource, List[SubscriptionOrderSubFundInvestment]] = {
    run(
      subscriptionOrderSubFundInvestment.filter { investment =>
        liftQuery(subscriptionOrderIds).contains(investment.subscriptionOrderId)
      }
    )
  }

  def getBySubFund(subFundId: InvestmentFundId, orderIds: List[FundSubLpId])
    : RIO[DataSource, List[SubscriptionOrderSubFundInvestment]] = {
    run(
      subscriptionOrderSubFundInvestment.filter { investment =>
        investment.subFundId == lift(subFundId) && liftQuery(orderIds).contains(investment.subscriptionOrderId)
      }
    )
  }

  def updateBySubscriptionOrder(
    subscriptionOrderId: FundSubLpId,
    investments: List[SubscriptionOrderSubFundInvestment]
  ): RIO[DataSource, Unit] = {
    for {
      inPipelineInvestments <- getBySubscriptionOrder(subscriptionOrderId)
      toInserts = investments.filterNot { investment =>
        inPipelineInvestments.exists(_.subFundId == investment.subFundId)
      }
      toUpdates = investments.filter { investment =>
        inPipelineInvestments.exists { inPipeline =>
          inPipeline.subFundId == investment.subFundId && inPipeline != investment
        }
      }
      toDeletes = inPipelineInvestments.filterNot { inPipeline =>
        investments.exists(_.subFundId == inPipeline.subFundId)
      }
      _ <- ZIO.foreach(toInserts)(insert)
      _ <- ZIO.foreach(toUpdates)(update)
      _ <- ZIO.foreach(toDeletes)(investment => delete(investment.subscriptionOrderId -> investment.subFundId))
    } yield ()
  }

  def getSubFundInvestmentsByFund(
    fundSubId: FundSubId
  ): RIO[DataSource, List[
    (FundSubscriptionSubFund, List[(SubscriptionOrderSubFundInvestment, SubscriptionOrderStatus)])
  ]] = {
    for {
      subFunds <- run(
        fundSubscriptionSubFund.filter(_.fundSubscriptionId == lift(fundSubId))
      )
      investments <- run(
        subscriptionOrderSubFundInvestment
          .filter(investment =>
            sql"${investment.subscriptionOrderId} LIKE ${lift(fundSubId.idString + "%")}".asCondition
          )
          .join(subscriptionOrder)
          .on(_.subscriptionOrderId == _.id)
          .map { case (investment, order) =>
            investment -> order.status
          }
      )
      subFundInvestments = investments.groupBy(_._1.subFundId)
    } yield subFunds.map { subFund =>
      subFund -> subFundInvestments.getOrElse(subFund.id, List.empty)
    }
  }

  def getInvestmentsBySubscriptionOrders(
    subscriptionOrderIds: List[FundSubLpId]
  ): RIO[DataSource, List[(SubscriptionOrderSubFundInvestment, Currency, SubscriptionOrderStatus)]] = {
    run(
      subscriptionOrderSubFundInvestment
        .filter { investment =>
          liftQuery(subscriptionOrderIds).contains(investment.subscriptionOrderId)
        }
        .join(fundSubscriptionSubFund)
        .on(_.subFundId == _.id)
        .map { case (investment, subFund) =>
          investment -> subFund.currency
        }
        .join(subscriptionOrder)
        .on(_._1.subscriptionOrderId == _.id)
        .map { case ((investment, currency), subscription) =>
          (investment, currency, subscription.status)
        }
    )
  }

}

object SubscriptionOrderSubFundInvestmentOperations
    extends TiDBModelOperationCompanion[SubscriptionOrderSubFundInvestmentOperations]
