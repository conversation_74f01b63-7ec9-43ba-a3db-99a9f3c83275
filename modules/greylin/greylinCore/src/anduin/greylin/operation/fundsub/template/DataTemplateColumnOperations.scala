// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation.fundsub.template

import anduin.greylin.core.operation.{TiDBModelOperation, TiDBModelOperationCompanion, TiDBModelOperationContext}
import anduin.greylin.modelti.fundsub.template.DataTemplateColumn
import io.getquill.*
import zio.ZIO

import anduin.greylin.core.model.Codec.given
import anduin.tidb.TiDatabase.QuillContext.*
import anduin.id.form.DataTemplateVersionId

final case class DataTemplateColumnOperations private[greylin] (
  override val ctx: TiDBModelOperationContext
) extends TiDBModelOperation[(DataTemplateVersionId, Short), DataTemplateColumn] {

  private inline def dataTemplateColumn = quote(
    querySchema[DataTemplateColumn](DataTemplateColumn.tableName)
  )

  private inline def queryByKey(inline pk: (DataTemplateVersionId, Short)) = {
    dataTemplateColumn.filter(c => c.templateVersionId == lift(pk._1) && c.columnIndex == lift(pk._2))
  }

  override def getOpt(pk: (DataTemplateVersionId, Short)) = {
    run(queryByKey(pk)).map(_.headOption)
  }

  override def insert(column: DataTemplateColumn) = {
    run(
      dataTemplateColumn.insertValue(lift(column))
    ).as(column)
  }

  def batchInsert(columns: Seq[DataTemplateColumn]) = {
    if (columns.nonEmpty) {
      run(
        liftQuery(columns).foreach(col => dataTemplateColumn.insertValue(col)),
        1000
      ).as(columns)
    } else {
      ZIO.succeed(columns)
    }
  }

  override def update(column: DataTemplateColumn) = {
    run(
      queryByKey(column.templateVersionId -> column.columnIndex).updateValue(lift(column))
    ).as(column)
  }

  def batchDelete(templateVersionId: DataTemplateVersionId) = {
    run(
      dataTemplateColumn.filter(col => col.templateVersionId == lift(templateVersionId)).delete
    )
  }

  override def delete(pk: (DataTemplateVersionId, Short)) = {
    run(queryByKey(pk).delete).unit
  }

}

object DataTemplateColumnOperations extends TiDBModelOperationCompanion[DataTemplateColumnOperations]
