// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.greylin.operation.fundsub

import javax.sql.DataSource
import zio.{RIO, ZIO}
import io.getquill.*

import anduin.greylin.core.operation.{TiDBModelOperation, TiDBModelOperationCompanion, TiDBModelOperationContext}
import anduin.greylin.modelti.fundsub.FundSubscriptionLpAdvisorTagAssignment
import anduin.id.fundsub.{FundSubAdvisorTagId, FundSubId, FundSubLpId}
import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given

final case class FundSubscriptionLpAdvisorTagAssignmentOperations private[greylin] (
  override val ctx: TiDBModelOperationContext
) extends TiDBModelOperation[(FundSubLpId, FundSubAdvisorTagId), FundSubscriptionLpAdvisorTagAssignment] {

  private inline def fundSubscriptionLpAdvisorTagAssignment = quote(
    querySchema[FundSubscriptionLpAdvisorTagAssignment](FundSubscriptionLpAdvisorTagAssignment.tableName)
  )

  private inline def queryByKey(inline pk: (FundSubLpId, FundSubAdvisorTagId)) = {
    fundSubscriptionLpAdvisorTagAssignment.filter(assignment =>
      assignment.lpId == lift(pk._1) && assignment.advisorTagId == lift(pk._2)
    )
  }

  override def getOpt(key: (FundSubLpId, FundSubAdvisorTagId))
    : RIO[DataSource, Option[FundSubscriptionLpAdvisorTagAssignment]] = {
    run(queryByKey(key)).map(_.headOption)
  }

  override def insert(model: FundSubscriptionLpAdvisorTagAssignment)
    : RIO[DataSource, FundSubscriptionLpAdvisorTagAssignment] = {
    run(fundSubscriptionLpAdvisorTagAssignment.insertValue(lift(model))).as(model)
  }

  override def update(model: FundSubscriptionLpAdvisorTagAssignment)
    : RIO[DataSource, FundSubscriptionLpAdvisorTagAssignment] = {
    run(
      fundSubscriptionLpAdvisorTagAssignment
        .filter { assignment =>
          assignment.lpId == lift(model.lpId) && assignment.advisorTagId == lift(model.advisorTagId)
        }
        .updateValue(lift(model))
    ).as(model)
  }

  override def delete(key: (FundSubLpId, FundSubAdvisorTagId)): RIO[DataSource, Unit] = {
    run(queryByKey(key).delete).unit
  }

  def insert(models: List[FundSubscriptionLpAdvisorTagAssignment])
    : RIO[DataSource, List[FundSubscriptionLpAdvisorTagAssignment]] = {
    run(
      liftQuery(models).foreach(model => fundSubscriptionLpAdvisorTagAssignment.insertValue(model)),
      1000
    ).as(models)
  }

  def getByLpId(lpId: FundSubLpId): RIO[DataSource, List[FundSubscriptionLpAdvisorTagAssignment]] = {
    run(
      fundSubscriptionLpAdvisorTagAssignment.filter(_.lpId == lift(lpId))
    )
  }

  def getByAdvisorTagId(advisorTagId: FundSubAdvisorTagId)
    : RIO[DataSource, List[FundSubscriptionLpAdvisorTagAssignment]] = {
    run(
      fundSubscriptionLpAdvisorTagAssignment.filter(_.advisorTagId == lift(advisorTagId))
    )
  }

  def getByFundSubscriptionId(fundSubId: FundSubId): RIO[DataSource, List[FundSubscriptionLpAdvisorTagAssignment]] = {
    run(
      fundSubscriptionLpAdvisorTagAssignment.filter { assignment =>
        sql"${assignment.lpId} LIKE ${lift(fundSubId.idString + "%")}".asCondition
      }
    )
  }

  def deleteByAdvisorTagId(advisorTagId: FundSubAdvisorTagId): RIO[DataSource, Unit] = {
    run(
      fundSubscriptionLpAdvisorTagAssignment
        .filter(_.advisorTagId == lift(advisorTagId))
        .delete
    ).unit
  }

  def deleteByLpId(lpId: FundSubLpId): RIO[DataSource, Unit] = {
    run(
      fundSubscriptionLpAdvisorTagAssignment
        .filter(_.lpId == lift(lpId))
        .delete
    ).unit
  }

  def deleteByKeys(keys: List[(FundSubLpId, FundSubAdvisorTagId)]): RIO[DataSource, Unit] = {
    ZIO.foreachDiscard(keys) { key =>
      run(queryByKey(key).delete)
    }
  }

}

object FundSubscriptionLpAdvisorTagAssignmentOperations
    extends TiDBModelOperationCompanion[FundSubscriptionLpAdvisorTagAssignmentOperations]
