// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation.fundsub

import javax.sql.DataSource
import zio.RIO
import io.getquill.*

import anduin.greylin.core.operation.{TiDBModelOperation, TiDBModelOperationCompanion, TiDBModelOperationContext}
import anduin.greylin.modelti.fundsub.FundSubscriptionAdvisorTag
import anduin.id.fundsub.{FundSubAdvisorTagId, FundSubId}
import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given

final case class FundSubscriptionAdvisorTagOperations private[greylin] (
  override val ctx: TiDBModelOperationContext
) extends TiDBModelOperation[FundSubAdvisorTagId, FundSubscriptionAdvisorTag] {

  private inline def fundSubscriptionAdvisorTag = quote(
    querySchema[FundSubscriptionAdvisorTag](FundSubscriptionAdvisorTag.tableName)
  )

  override def getOpt(id: FundSubAdvisorTagId): RIO[DataSource, Option[FundSubscriptionAdvisorTag]] = {
    run(fundSubscriptionAdvisorTag.filter(_.id == lift(id))).map(_.headOption)
  }

  override def insert(model: FundSubscriptionAdvisorTag): RIO[DataSource, FundSubscriptionAdvisorTag] = {
    run(fundSubscriptionAdvisorTag.insertValue(lift(model))).as(model)
  }

  override def update(model: FundSubscriptionAdvisorTag): RIO[DataSource, FundSubscriptionAdvisorTag] = {
    run(
      fundSubscriptionAdvisorTag
        .filter(tag => tag.id == lift(model.id))
        .updateValue(lift(model))
    ).as(model)
  }

  override def delete(id: FundSubAdvisorTagId): RIO[DataSource, Unit] = {
    run(
      fundSubscriptionAdvisorTag
        .filter(tag => tag.id == lift(id))
        .delete
    ).unit
  }

  def insert(models: List[FundSubscriptionAdvisorTag]): RIO[DataSource, List[FundSubscriptionAdvisorTag]] = {
    run(
      liftQuery(models).foreach(model => fundSubscriptionAdvisorTag.insertValue(model)),
      1000
    ).as(models)
  }

  def getByFundSubscriptionId(fundSubId: FundSubId): RIO[DataSource, List[FundSubscriptionAdvisorTag]] = {
    run(
      fundSubscriptionAdvisorTag.filter(_.fundSubscriptionId == lift(fundSubId))
    )
  }

  def delete(advisorTagIds: List[FundSubAdvisorTagId]): RIO[DataSource, Unit] = {
    run(
      fundSubscriptionAdvisorTag.filter { tag =>
        liftQuery(advisorTagIds).contains(tag.id)
      }.delete
    ).unit
  }

}

object FundSubscriptionAdvisorTagOperations extends TiDBModelOperationCompanion[FundSubscriptionAdvisorTagOperations]
