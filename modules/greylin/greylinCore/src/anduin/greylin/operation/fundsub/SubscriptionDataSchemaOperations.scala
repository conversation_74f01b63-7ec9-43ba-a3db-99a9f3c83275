// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.greylin.operation.fundsub

import java.time.Instant

import javax.sql.DataSource
import zio.RIO
import io.getquill.*

import anduin.greylin.core.operation.{TiDBModelOperation, TiDBModelOperationCompanion, TiDBModelOperationContext}
import anduin.greylin.modelti.fundsub.SubscriptionDataSchema
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given
import anduin.fundsub.cue.model.CueSchemaFundSubTypes

final case class SubscriptionDataSchemaOperations(
  override val ctx: TiDBModelOperationContext
) extends TiDBModelOperation[FundSubLpId, SubscriptionDataSchema] {

  private inline def subscriptionDataSchema = quote(
    querySchema[SubscriptionDataSchema](SubscriptionDataSchema.tableName)
  )

  override def getOpt(id: FundSubLpId): RIO[DataSource, Option[SubscriptionDataSchema]] = {
    run(subscriptionDataSchema.filter(_.id == lift(id))).map(_.headOption)
  }

  def get(ids: List[FundSubLpId]): RIO[DataSource, List[SubscriptionDataSchema]] = {
    run(subscriptionDataSchema.filter { data =>
      liftQuery(ids).contains(data.id)
    })
  }

  override def insert(model: SubscriptionDataSchema): RIO[DataSource, SubscriptionDataSchema] = {
    run(subscriptionDataSchema.insertValue(lift(model))).as(model)
  }

  override def update(model: SubscriptionDataSchema): RIO[DataSource, SubscriptionDataSchema] = {
    run(subscriptionDataSchema.filter(_.id == lift(model.id)).updateValue(lift(model))).as(model)
  }

  override def delete(id: FundSubLpId): RIO[DataSource, Unit] = {
    run(subscriptionDataSchema.filter(_.id == lift(id)).delete).unit
  }

  def upsert(model: SubscriptionDataSchema): RIO[DataSource, SubscriptionDataSchema] = {
    for {
      modelExisted <- getOpt(model.id).map(_.nonEmpty)
      _ <-
        if (modelExisted) {
          run(
            subscriptionDataSchema.filter(_.id == lift(model.id)).updateValue(lift(model))
          )
        } else {
          run(
            subscriptionDataSchema.insertValue(lift(model))
          )
        }
    } yield model
  }

  def getByFundSubId(fundSubId: FundSubId): RIO[DataSource, List[SubscriptionDataSchema]] = {
    run(
      subscriptionDataSchema.filter { schema =>
        sql"${schema.id} LIKE ${lift(fundSubId.idString + "%")}".asCondition
      }
    )
  }

  def updateSubscriptionData(
    id: FundSubLpId,
    data: CueSchemaFundSubTypes.SubscriptionDataSchema
  ): RIO[DataSource, SubscriptionDataSchema] = {
    for {
      modelOpt <- getOpt(id)
      now = Instant.now()
      model = modelOpt match {
        case Some(existingModel) => existingModel.copy(data = data.toCueJson.json.noSpaces, updatedAt = Some(now))
        case None => SubscriptionDataSchema(id = id, data = data.toCueJson.json.noSpaces, updatedAt = Some(now))
      }
      result <- upsert(model)
    } yield result
  }

}

object SubscriptionDataSchemaOperations extends TiDBModelOperationCompanion[SubscriptionDataSchemaOperations]
