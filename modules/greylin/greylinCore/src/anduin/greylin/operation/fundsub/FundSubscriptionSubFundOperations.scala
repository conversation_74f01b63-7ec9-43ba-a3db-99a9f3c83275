package anduin.greylin.operation.fundsub

import anduin.greylin.core.model.Codec.given
import anduin.greylin.core.operation.{TiDBModelOperation, TiDBModelOperationCompanion, TiDBModelOperationContext}
import anduin.greylin.modelti.fundsub.FundSubscriptionSubFund
import anduin.id.fundsub.{FundSubId, InvestmentFundId}
import anduin.tidb.TiDatabase.QuillContext.*
import io.getquill.*
import zio.{RIO, ZIO}

import javax.sql.DataSource

final case class FundSubscriptionSubFundOperations private[greylin] (
  override val ctx: TiDBModelOperationContext
) extends TiDBModelOperation[InvestmentFundId, FundSubscriptionSubFund] {

  private inline def fundSubscriptionSubFund = quote(
    querySchema[FundSubscriptionSubFund](FundSubscriptionSubFund.tableName)
  )

  override def getOpt(pk: InvestmentFundId): RIO[DataSource, Option[FundSubscriptionSubFund]] = {
    run(fundSubscriptionSubFund.filter(model => model.id == lift(pk))).map(_.headOption)
  }

  override def insert(model: FundSubscriptionSubFund): RIO[DataSource, FundSubscriptionSubFund] = {
    run(fundSubscriptionSubFund.insertValue(lift(model))).as(model)
  }

  override def update(model: FundSubscriptionSubFund): RIO[DataSource, FundSubscriptionSubFund] = {
    run(
      fundSubscriptionSubFund
        .filter(subFund => subFund.id == lift(model.id))
        .updateValue(lift(model))
    ).as(model)
  }

  override def delete(pk: InvestmentFundId): RIO[DataSource, Unit] = {
    run(
      fundSubscriptionSubFund
        .filter(subFund => subFund.id == lift(pk))
        .delete
    ).unit
  }

  def insert(models: List[FundSubscriptionSubFund]): RIO[DataSource, List[FundSubscriptionSubFund]] = {
    run(
      liftQuery(models).foreach(model => fundSubscriptionSubFund.insertValue(model)),
      1000
    ).as(models)
  }

  def get(investmentFundIds: List[InvestmentFundId]): RIO[DataSource, List[FundSubscriptionSubFund]] = {
    run(
      fundSubscriptionSubFund.filter { subFund => liftQuery(investmentFundIds).contains(subFund.id) }
    )
  }

  def getByFund(fundSubId: FundSubId): RIO[DataSource, List[FundSubscriptionSubFund]] = {
    run(
      fundSubscriptionSubFund
        .filter(subFund => subFund.fundSubscriptionId == lift(fundSubId))
    )
  }

  def getByFunds(fundSubIds: List[FundSubId]): RIO[DataSource, List[FundSubscriptionSubFund]] = {
    run(
      fundSubscriptionSubFund
        .filter { subFund => liftQuery(fundSubIds).contains(subFund.fundSubscriptionId) }
    )
  }

  def updateByFund(
    fundSubId: FundSubId,
    subFunds: List[FundSubscriptionSubFund]
  ): RIO[DataSource, Unit] = {
    for {
      inPipelineSubFunds <- getByFund(fundSubId)
      toInserts = subFunds.filterNot { subFund =>
        inPipelineSubFunds.exists(_.id == subFund.id)
      }
      toUpdates = subFunds.filter { subFund =>
        inPipelineSubFunds.exists { inPipeline =>
          inPipeline.id == subFund.id && inPipeline != subFund
        }
      }
      toDeletes = inPipelineSubFunds.filterNot { inPipeline =>
        subFunds.exists(_.id == inPipeline.id)
      }
      _ <- ZIO.foreach(toInserts)(insert)
      _ <- ZIO.foreach(toUpdates)(update)
      _ <- ZIO.foreach(toDeletes)(subFund => delete(subFund.id))
    } yield ()
  }

}

object FundSubscriptionSubFundOperations extends TiDBModelOperationCompanion[FundSubscriptionSubFundOperations]
