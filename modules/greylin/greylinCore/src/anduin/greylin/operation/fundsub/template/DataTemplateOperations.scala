// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation.fundsub.template

import anduin.greylin.core.operation.{TiDBModelOperation, TiDBModelOperationCompanion, TiDBModelOperationContext}
import anduin.greylin.modelti.fundsub.template.DataTemplate
import anduin.id.form.DataTemplateVersionId
import io.getquill.*

import anduin.greylin.core.model.Codec.given
import anduin.tidb.TiDatabase.QuillContext.*

final case class DataTemplateOperations private[greylin] (
  override val ctx: TiDBModelOperationContext
) extends TiDBModelOperation[DataTemplateVersionId, DataTemplate] {

  private inline def dataTemplate = quote(
    querySchema[DataTemplate](DataTemplate.tableName)
  )

  private inline def queryByKey(inline templateVersionId: DataTemplateVersionId) = {
    dataTemplate.filter(t => t.templateVersionId == lift(templateVersionId))
  }

  override def getOpt(templateVersionId: DataTemplateVersionId) = {
    run(queryByKey(templateVersionId)).map(_.headOption)
  }

  override def insert(template: DataTemplate) = {
    run(
      dataTemplate.insertValue(lift(template))
    ).as(template)
  }

  override def update(template: DataTemplate) = {
    run(
      queryByKey(template.templateVersionId).updateValue(lift(template))
    ).as(template)
  }

  override def delete(templateVersionId: DataTemplateVersionId) = {
    run(
      queryByKey(templateVersionId).delete
    ).unit
  }

}

object DataTemplateOperations extends TiDBModelOperationCompanion[DataTemplateOperations]
