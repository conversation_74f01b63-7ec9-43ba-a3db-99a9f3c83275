// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation

import io.getquill.*
import javax.sql.DataSource
import zio.ZIO

import anduin.greylin.core.model.Codec.given
import anduin.greylin.modelti.DataRoom
import anduin.id.entity.EntityId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.tidb.TiDatabase.QuillContext.*

object DataRoomOperations {

  private inline def dataRoom = quote(
    querySchema[DataRoom]("pb_data_room")
  )

  def insert(model: DataRoom): ZIO[DataSource, Throwable, DataRoom] = {
    run(dataRoom.insertValue(lift(model))).map(_ => model)
  }

  def insert(models: List[DataRoom]): ZIO[DataSource, Throwable, List[DataRoom]] = {
    run(
      liftQuery(models).foreach(model => dataRoom.insertValue(model)),
      1000
    ).map(_ => models)
  }

  def update(
    id: DataRoomWorkflowId
  )(
    updateFn: DataRoom => DataRoom
  ): ZIO[DataSource, Throwable, DataRoom] = {
    for {
      model <- get(id)
      newModel <- ZIO.attempt(updateFn(model))
      _ <- run(
        dataRoom.filter(_.id == lift(id)).updateValue(lift(newModel))
      )
    } yield newModel
  }

  def get(
    id: DataRoomWorkflowId
  ): ZIO[DataSource, Throwable, DataRoom] = {
    for {
      modelOpt <- getOpt(id)
      dataRoom <- ZIO.getOrFail(modelOpt)
    } yield dataRoom
  }

  def get(
    ids: List[DataRoomWorkflowId]
  ): ZIO[DataSource, Throwable, List[DataRoom]] = {
    run(dataRoom.filter { dataRoom => liftQuery(ids).contains(dataRoom.id) })
  }

  def get(
    entityId: EntityId
  ): ZIO[DataSource, Throwable, List[DataRoom]] = {
    run(dataRoom.filter { dataRoom => dataRoom.entityId == lift(entityId) })
  }

  def getOpt(
    id: DataRoomWorkflowId
  ): ZIO[DataSource, Throwable, Option[DataRoom]] = {
    run(dataRoom.filter(_.id == lift(id))).map(_.headOption)
  }

}
