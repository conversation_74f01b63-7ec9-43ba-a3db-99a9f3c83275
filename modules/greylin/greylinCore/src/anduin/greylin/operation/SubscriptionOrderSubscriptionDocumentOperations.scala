// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation

import javax.sql.DataSource
import zio.RIO
import zio.ZIO

import anduin.greylin.modelti.{SubscriptionOrderSubscriptionDocument, SubscriptionOrderSubscriptionDocumentType}
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.greylin.core.model.Codec.given
import io.getquill.*

import anduin.tidb.TiDatabase.QuillContext.*
import anduin.model.id.FileId

object SubscriptionOrderSubscriptionDocumentOperations {

  private inline def subscriptionDocument = quote(
    querySchema[SubscriptionOrderSubscriptionDocument]("pb_subscription_order_subscription_document")
  )

  def getOpt(fileId: FileId): RIO[DataSource, Option[SubscriptionOrderSubscriptionDocument]] = {
    run(
      subscriptionDocument.filter(_.fileId == lift(fileId))
    ).map(_.headOption)
  }

  def get(fileId: FileId): RIO[DataSource, SubscriptionOrderSubscriptionDocument] = {
    for {
      modelOpt <- getOpt(fileId)
      document <- ZIO.getOrFail(modelOpt)
    } yield document
  }

  def get(fileIds: List[FileId]): RIO[DataSource, List[SubscriptionOrderSubscriptionDocument]] = {
    run(
      subscriptionDocument.filter(doc => liftQuery(fileIds).contains(doc.fileId))
    )
  }

  def getByOrderId(subscriptionOrderId: FundSubLpId): RIO[DataSource, List[SubscriptionOrderSubscriptionDocument]] = {
    run(
      subscriptionDocument.filter(_.subscriptionOrderId == lift(subscriptionOrderId))
    )
  }

  def getByOrderIds(subscriptionOrderIds: List[FundSubLpId])
    : RIO[DataSource, List[SubscriptionOrderSubscriptionDocument]] = {
    run(
      subscriptionDocument.filter(doc => liftQuery(subscriptionOrderIds).contains(doc.subscriptionOrderId))
    )
  }

  def get(fundSubId: FundSubId): RIO[DataSource, List[SubscriptionOrderSubscriptionDocument]] = {
    run(
      subscriptionDocument.filter(doc =>
        sql"${doc.subscriptionOrderId} LIKE ${lift(fundSubId.idString + "%")}".asCondition
      )
    )
  }

  def insert(
    documents: List[SubscriptionOrderSubscriptionDocument]
  ): RIO[DataSource, List[SubscriptionOrderSubscriptionDocument]] = {
    run(
      liftQuery(documents).foreach(document => subscriptionDocument.insertValue(document)),
      100
    ).as(documents)
  }

  def update(
    id: FileId
  )(
    updateFn: SubscriptionOrderSubscriptionDocument => SubscriptionOrderSubscriptionDocument
  ): ZIO[DataSource, Throwable, SubscriptionOrderSubscriptionDocument] = {
    for {
      model <- get(id)
      newModel <- ZIO.attempt(updateFn(model))
      _ <- run(
        subscriptionDocument.filter(_.fileId == lift(id)).updateValue(lift(newModel))
      )
    } yield newModel
  }

  def delete(
    subscriptionOrderId: FundSubLpId,
    documentTypes: List[SubscriptionOrderSubscriptionDocumentType]
  ): RIO[DataSource, Long] = {
    run(
      subscriptionDocument.filter { document =>
        document.subscriptionOrderId == lift(subscriptionOrderId) && liftQuery(documentTypes).contains(
          document.documentType
        )
      }.delete
    )
  }

  def delete(
    fileIds: List[FileId]
  ): RIO[DataSource, Long] = {
    run(
      subscriptionDocument.filter { document =>
        liftQuery(fileIds).contains(document.fileId)
      }.delete
    )
  }

}
