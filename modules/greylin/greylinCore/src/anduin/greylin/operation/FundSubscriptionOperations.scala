// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation

import javax.sql.DataSource
import zio.{RIO, ZIO}

import anduin.greylin.modelti.FundSubscription
import anduin.id.fundsub.FundSubId
import io.getquill.*

import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given
import anduin.id.entity.EntityId

object FundSubscriptionOperations {

  private inline def fundSubscription = quote(
    querySchema[FundSubscription]("pb_fund_subscription")
  )

  def insert(model: FundSubscription): RIO[DataSource, FundSubscription] =
    run(fundSubscription.insertValue(lift(model))).map(_ => model)

  def insert(models: List[FundSubscription]): RIO[DataSource, Unit] =
    run(liftQuery(models).foreach(model => fundSubscription.insertValue(model))).unit

  def update(
    id: FundSubId
  )(
    updateFn: FundSubscription => FundSubscription
  ): RIO[DataSource, FundSubscription] =
    for {
      model <- get(id)
      newModel <- ZIO.attempt(updateFn(model))
      _ <- run(
        quote(fundSubscription.filter(_.id == lift(id)).updateValue(lift(newModel)))
      )
    } yield newModel

  def get(entityIdOpt: Option[EntityId]): RIO[DataSource, List[FundSubscription]] =
    run(fundSubscription.filter { fund => fund.entityId == lift(entityIdOpt) })

  def getAll: RIO[DataSource, List[FundSubscription]] =
    run(fundSubscription)

  def get(
    id: FundSubId
  ): RIO[DataSource, FundSubscription] =
    for {
      modelOpt <- getOpt(id)
      fund <- ZIO.getOrFail(modelOpt)
    } yield fund

  def get(
    ids: List[FundSubId]
  ): RIO[DataSource, List[FundSubscription]] =
    run(fundSubscription.filter { fund => liftQuery(ids).contains(fund.id) })

  def getOpt(
    id: FundSubId
  ): RIO[DataSource, Option[FundSubscription]] =
    run(fundSubscription.filter(_.id == lift(id))).map(_.headOption)

  def delete(id: FundSubId): RIO[DataSource, Unit] =
    run(fundSubscription.filter(_.id == lift(id)).delete).unit

}
