// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation

import javax.sql.DataSource
import zio.ZIO

import anduin.greylin.modelti.DataRoomGroup
import anduin.id.dataroom.DataRoomGroupId

import io.getquill.*
import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given

object DataRoomGroupOperations {

  private inline def dataRoomGroup = quote(
    querySchema[DataRoomGroup]("pb_data_room_group")
  )

  def insert(model: DataRoomGroup): ZIO[DataSource, Throwable, DataRoomGroup] = {
    run(dataRoomGroup.insertValue(lift(model))).map(_ => model)
  }

  def insert(models: List[DataRoomGroup]): ZIO[DataSource, Throwable, List[DataRoomGroup]] = {
    run(liftQuery(models).foreach(model => dataRoomGroup.insertValue(model)), 1000).map(_ => models)
  }

  def update(id: DataRoomGroupId)(updateFn: DataRoomGroup => DataRoomGroup): ZIO[DataSource, Throwable, DataRoomGroup] = {
    for {
      model <- get(id)
      newModel <- ZIO.attempt(updateFn(model))
      _ <- run(
        quote(dataRoomGroup.filter(_.id == lift(id)).updateValue(lift(newModel)))
      )
    } yield newModel
  }

  def get(id: DataRoomGroupId): ZIO[DataSource, Throwable, DataRoomGroup] = {
    for {
      modelOpt <- getOpt(id)
      dataRoomGroup <- ZIO.getOrFail(modelOpt)
    } yield dataRoomGroup
  }

  def get(ids: List[DataRoomGroupId]): ZIO[DataSource, Throwable, List[DataRoomGroup]] = {
    run(dataRoomGroup.filter { dataRoomGroup => liftQuery(ids).contains(dataRoomGroup.id) })
  }

  def getOpt(id: DataRoomGroupId): ZIO[DataSource, Throwable, Option[DataRoomGroup]] = {
    run(dataRoomGroup.filter(_.id == lift(id))).map(_.headOption)
  }

  def delete(id: DataRoomGroupId): ZIO[DataSource, Throwable, Unit] = {
    run(dataRoomGroup.filter(_.id == lift(id)).delete).unit
  }

  def delete(ids: List[DataRoomGroupId]): ZIO[DataSource, Throwable, Unit] = {
    run(
      liftQuery(ids).foreach(id => dataRoomGroup.filter(_.id == id).delete)
    ).unit
  }

}
