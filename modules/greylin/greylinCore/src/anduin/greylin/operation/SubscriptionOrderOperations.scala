// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation

import javax.sql.DataSource
import zio.{RIO, ZIO}

import anduin.greylin.modelti.{SubscriptionOrder, SubscriptionOrderStatus}
import anduin.id.fundsub.{FundSubCloseId, FundSubId, FundSubLpId}
import anduin.model.common.user.UserId
import io.getquill.*

import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given

object SubscriptionOrderOperations {

  private inline def subscriptionOrder = quote(
    querySchema[SubscriptionOrder]("pb_subscription_order")
  )

  def insert(model: SubscriptionOrder): ZIO[DataSource, Throwable, SubscriptionOrder] = {
    run(subscriptionOrder.insertValue(lift(model))).map(_ => model)
  }

  def insert(models: List[SubscriptionOrder]): ZIO[DataSource, Throwable, List[SubscriptionOrder]] = {
    run(
      liftQuery(models).foreach(model => subscriptionOrder.insertValue(model)),
      1000
    ).map(_ => models)
  }

  def update(
    id: FundSubLpId
  )(
    updateFn: SubscriptionOrder => SubscriptionOrder
  ): ZIO[DataSource, Throwable, SubscriptionOrder] = {
    for {
      model <- get(id)
      newModel <- ZIO.attempt(updateFn(model))
      _ <- run(
        subscriptionOrder.filter(_.id == lift(id)).updateValue(lift(newModel))
      )
    } yield newModel
  }

  def updateStatus(id: FundSubLpId, status: SubscriptionOrderStatus): ZIO[DataSource, Throwable, SubscriptionOrder] =
    update(id)(_.copy(status = status))

  def updateFormProgress(id: FundSubLpId, formProgress: Double): ZIO[DataSource, Throwable, SubscriptionOrder] =
    update(id)(_.copy(formProgress = Some(formProgress)))

  def upsert(model: SubscriptionOrder): ZIO[DataSource, Throwable, SubscriptionOrder] = {
    for {
      modelExisted <- getOpt(model.id).map(_.nonEmpty)
      _ <-
        if (modelExisted) {
          run(
            subscriptionOrder.filter(_.id == lift(model.id)).updateValue(lift(model))
          )
        } else {
          run(
            subscriptionOrder.insertValue(lift(model))
          )
        }
    } yield model
  }

  def delete(id: FundSubLpId): ZIO[DataSource, Throwable, Unit] = {
    run(subscriptionOrder.filter(_.id == lift(id)).delete).unit
  }

  def get(
    id: FundSubLpId
  ): ZIO[DataSource, Throwable, SubscriptionOrder] = {
    for {
      modelOpt <- getOpt(id)
      fund <- ZIO.getOrFail(modelOpt)
    } yield fund
  }

  def get(ids: List[FundSubLpId]): RIO[DataSource, List[SubscriptionOrder]] =
    run(subscriptionOrder.filter { m => liftQuery(ids).contains(m.id) })

  def getOpt(
    id: FundSubLpId
  ): ZIO[DataSource, Throwable, Option[SubscriptionOrder]] = {
    run(subscriptionOrder.filter(_.id == lift(id))).map(_.headOption)
  }

  def getByFundId(
    id: FundSubId,
    includeStatusRemoved: Boolean = false
  ): ZIO[DataSource, Throwable, List[SubscriptionOrder]] = {
    if (!includeStatusRemoved) {
      run(subscriptionOrder.filter { orderModel =>
        orderModel.fundSubscriptionId == lift(id) && orderModel.status != lift(
          SubscriptionOrderStatus.Removed: SubscriptionOrderStatus
        )
      })
    } else {
      run(subscriptionOrder.filter(_.fundSubscriptionId == lift(id)))
    }
  }

  // TODO: @poor remove after migration
  def batchUpdateClose(
    params: List[(FundSubLpId, Option[FundSubCloseId])]
  ): ZIO[DataSource, Throwable, Unit] =
    run(
      liftQuery(params).foreach { case (lpId, closeIdOpt) =>
        subscriptionOrder
          .filter(_.id == lpId)
          .update(_.closeId -> closeIdOpt)
      },
      1000
    ).unit

}
