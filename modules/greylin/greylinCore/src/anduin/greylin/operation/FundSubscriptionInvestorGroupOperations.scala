// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation

import javax.sql.DataSource
import zio.ZIO

import anduin.greylin.modelti.FundSubscriptionInvestorGroup
import anduin.id.fundsub.FundSubId
import anduin.id.fundsub.group.FundSubInvestorGroupId

import io.getquill.*
import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given

// Deprecated, do not use
object FundSubscriptionInvestorGroupOperations {

  private inline def investorGroup = quote(
    querySchema[FundSubscriptionInvestorGroup]("pb_fund_subscription_investor_group")
  )

  def insert(model: FundSubscriptionInvestorGroup): ZIO[DataSource, Throwable, Unit] = {
    run(investorGroup.insertValue(lift(model))).unit
  }

  def insert(models: List[FundSubscriptionInvestorGroup]): ZIO[DataSource, Throwable, Unit] = {
    run(
      liftQuery(models).foreach(model => investorGroup.insertValue(model)),
      1000
    ).unit
  }

  def update(
    id: FundSubInvestorGroupId
  )(
    updateFn: FundSubscriptionInvestorGroup => FundSubscriptionInvestorGroup
  ): ZIO[DataSource, Throwable, FundSubscriptionInvestorGroup] = {
    for {
      model <- get(id)
      newModel <- ZIO.attempt(updateFn(model))
      _ <- run(investorGroup.filter(_.groupId == lift(id)).updateValue(lift(newModel)))
    } yield newModel
  }

  def delete(id: FundSubInvestorGroupId): ZIO[DataSource, Throwable, Unit] = {
    run(investorGroup.filter(_.groupId == lift(id)).delete).unit
  }

  private[operation] def get(id: FundSubInvestorGroupId): ZIO[DataSource, Throwable, FundSubscriptionInvestorGroup] = {
    for {
      modelOpt <- run(investorGroup.filter(_.groupId == lift(id))).map(_.headOption)
      model <- ZIO.getOrFail(modelOpt)
    } yield model
  }

  private[operation] def getByFundId(fundSubId: FundSubId)
    : ZIO[DataSource, Throwable, List[FundSubscriptionInvestorGroup]] = {
    run(investorGroup.filter(_.fundSubscriptionId == lift(fundSubId)))
  }

}
