// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation

import javax.sql.DataSource
import zio.ZIO

import anduin.greylin.modelti.FundSubscriptionInvestorGroupOrder
import anduin.id.fundsub.FundSubLpId
import anduin.id.fundsub.group.FundSubInvestorGroupId

import io.getquill.*
import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given

// Deprecated, do not use
object FundSubscriptionInvestorGroupOrderOperations {

  private inline def investorGroupOrder = quote(
    querySchema[FundSubscriptionInvestorGroupOrder]("pb_fund_subscription_investor_group_order")
  )

  def addOrders(orderIds: List[FundSubLpId], groupId: FundSubInvestorGroupId): ZIO[DataSource, Throwable, Unit] = {
    val models = orderIds.map(FundSubscriptionInvestorGroupOrder(_, groupId))
    run(
      liftQuery(models).foreach(model => investorGroupOrder.insertValue(model)),
      1000
    ).unit
  }

  def addOrders(orders: List[(FundSubLpId, FundSubInvestorGroupId)]): ZIO[DataSource, Throwable, Unit] = {
    val models = orders.map(order => FundSubscriptionInvestorGroupOrder(order._1, order._2))
    run(
      liftQuery(models).foreach(model => investorGroupOrder.insertValue(model)),
      1000
    ).unit
  }

  def removeOrders(orders: List[(FundSubLpId, FundSubInvestorGroupId)]): ZIO[DataSource, Throwable, Unit] = {
    run(
      liftQuery(orders).foreach { case (orderId, groupId) =>
        investorGroupOrder.filter { groupOrder =>
          groupOrder.subscriptionOrderId == orderId && groupOrder.groupId == groupId
        }.delete
      },
      1000
    ).unit
  }

  private[operation] def getByGroupIds(
    groupIds: List[FundSubInvestorGroupId]
  ): ZIO[DataSource, Throwable, List[FundSubscriptionInvestorGroupOrder]] = {
    run(investorGroupOrder.filter(groupOrder => liftQuery(groupIds).contains(groupOrder.groupId)))
  }

}
