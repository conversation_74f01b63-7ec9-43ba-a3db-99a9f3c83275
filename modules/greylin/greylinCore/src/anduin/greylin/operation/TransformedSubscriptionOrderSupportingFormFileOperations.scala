// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation

import javax.sql.DataSource
import zio.ZIO

import anduin.greylin.modelti.TransformedSubscriptionOrderSupportingFormFile
import anduin.id.docrequest.FormSubmissionId
import anduin.id.fundsub.FundSubLpId
import anduin.model.id.FileId

import io.getquill.*
import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given

object TransformedSubscriptionOrderSupportingFormFileOperations {

  private inline def transformedSubscriptionOrderSupportingFormFile = quote(
    querySchema[TransformedSubscriptionOrderSupportingFormFile]("tf_subscription_order_supporting_form_file")
  )

  def insert(
    model: TransformedSubscriptionOrderSupportingFormFile
  ): ZIO[DataSource, Throwable, TransformedSubscriptionOrderSupportingFormFile] = {
    run(transformedSubscriptionOrderSupportingFormFile.insertValue(lift(model))).as(model)
  }

  def insert(
    models: List[TransformedSubscriptionOrderSupportingFormFile]
  ): ZIO[
    DataSource,
    Throwable,
    List[TransformedSubscriptionOrderSupportingFormFile]
  ] = {
    run(
      liftQuery(models).foreach(model => transformedSubscriptionOrderSupportingFormFile.insertValue(model)),
      1000
    ).map(_ => models)
  }

  def update(
    id: FileId
  )(
    updateFn: TransformedSubscriptionOrderSupportingFormFile => TransformedSubscriptionOrderSupportingFormFile
  ): ZIO[DataSource, Throwable, TransformedSubscriptionOrderSupportingFormFile] = {
    for {
      model <- get(id)
      newModel <- ZIO.attempt(updateFn(model))
      _ <- run(
        transformedSubscriptionOrderSupportingFormFile.filter(_.fileId == lift(id)).updateValue(lift(newModel))
      )
    } yield newModel
  }

  def updateDocType(
    formSubmissionId: FormSubmissionId,
    docType: String
  ): ZIO[DataSource, Throwable, Unit] = {
    for {
      _ <- run(
        transformedSubscriptionOrderSupportingFormFile
          .filter(_.formSubmissionId == lift(formSubmissionId))
          .update(_.docType -> lift(docType))
      )
    } yield ()
  }

  def get(
    id: FileId
  ): ZIO[DataSource, Throwable, TransformedSubscriptionOrderSupportingFormFile] = {
    for {
      modelOpt <- getOpt(id)
      transformedSubscriptionOrderSupportingFormFile <- ZIO.getOrFail(modelOpt)
    } yield transformedSubscriptionOrderSupportingFormFile
  }

  def get(
    ids: List[FileId]
  ): ZIO[
    DataSource,
    Throwable,
    List[TransformedSubscriptionOrderSupportingFormFile]
  ] = {
    run(transformedSubscriptionOrderSupportingFormFile.filter { transformedSubscriptionOrderSupportingFormFile =>
      liftQuery(ids).contains(transformedSubscriptionOrderSupportingFormFile.fileId)
    })
  }

  def get(
    subscriptionOrderId: FundSubLpId
  ): ZIO[
    DataSource,
    Throwable,
    List[TransformedSubscriptionOrderSupportingFormFile]
  ] = {
    run(
      transformedSubscriptionOrderSupportingFormFile.filter(_.subscriptionOrderId == lift(subscriptionOrderId))
    )
  }

  def getOpt(
    id: FileId
  ): ZIO[
    DataSource,
    Throwable,
    Option[TransformedSubscriptionOrderSupportingFormFile]
  ] = {
    run(transformedSubscriptionOrderSupportingFormFile.filter(_.fileId == lift(id))).map(_.headOption)
  }

  def delete(id: FileId): ZIO[DataSource, Throwable, Unit] = {
    run(transformedSubscriptionOrderSupportingFormFile.filter(_.fileId == lift(id)).delete).unit
  }

  def delete(ids: List[FileId]): ZIO[DataSource, Throwable, Unit] = {
    run(
      liftQuery(ids).foreach(id => transformedSubscriptionOrderSupportingFormFile.filter(_.fileId == id).delete)
    ).unit
  }

}
