// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation

import javax.sql.DataSource
import zio.ZIO

import anduin.greylin.modelti.FundSubscriptionInvestorGroupPermission

import io.getquill.*
import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given

object FundSubscriptionInvestorGroupPermissionOperations {

  private inline def adminGroup = quote(
    querySchema[FundSubscriptionInvestorGroupPermission]("pb_fund_subscription_investor_group_permission")
  )

  def insert(model: FundSubscriptionInvestorGroupPermission): ZIO[DataSource, Throwable, Unit] = {
    run(adminGroup.insertValue(lift(model))).unit
  }

}
