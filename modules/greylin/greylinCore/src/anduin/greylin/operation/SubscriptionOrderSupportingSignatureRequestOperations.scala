// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation

import javax.sql.DataSource
import zio.ZIO

import anduin.greylin.modelti.SubscriptionOrderSupportingSignatureRequest
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.id.signature.SignatureRequestId

import io.getquill.*
import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given

object SubscriptionOrderSupportingSignatureRequestOperations {

  private inline def subscriptionOrderSupportingSignatureRequest = quote(
    querySchema[SubscriptionOrderSupportingSignatureRequest]("pb_subscription_order_supporting_signature_request")
  )

  def insert(
    model: SubscriptionOrderSupportingSignatureRequest
  ): ZIO[DataSource, Throwable, SubscriptionOrderSupportingSignatureRequest] = {
    run(subscriptionOrderSupportingSignatureRequest.insertValue(lift(model))).as(model)
  }

  def insert(
    models: List[SubscriptionOrderSupportingSignatureRequest]
  ): ZIO[
    DataSource,
    Throwable,
    List[SubscriptionOrderSupportingSignatureRequest]
  ] = {
    run(
      liftQuery(models).foreach(model => subscriptionOrderSupportingSignatureRequest.insertValue(model)),
      1000
    ).map(_ => models)
  }

  def update(
    id: SignatureRequestId
  )(
    updateFn: SubscriptionOrderSupportingSignatureRequest => SubscriptionOrderSupportingSignatureRequest
  ): ZIO[DataSource, Throwable, SubscriptionOrderSupportingSignatureRequest] = {
    for {
      model <- get(id)
      newModel <- ZIO.attempt(updateFn(model))
      _ <- run(
        subscriptionOrderSupportingSignatureRequest.filter(_.id == lift(id)).updateValue(lift(newModel))
      )
    } yield newModel
  }

  def get(
    id: SignatureRequestId
  ): ZIO[DataSource, Throwable, SubscriptionOrderSupportingSignatureRequest] = {
    for {
      modelOpt <- getOpt(id)
      subscriptionOrderSupportingSignatureRequest <- ZIO.getOrFail(modelOpt)
    } yield subscriptionOrderSupportingSignatureRequest
  }

  def get(
    ids: List[SignatureRequestId]
  ): ZIO[
    DataSource,
    Throwable,
    List[SubscriptionOrderSupportingSignatureRequest]
  ] = {
    run(subscriptionOrderSupportingSignatureRequest.filter { subscriptionOrderSupportingSignatureRequest =>
      liftQuery(ids).contains(subscriptionOrderSupportingSignatureRequest.id)
    })
  }

  def get(
    subscriptionOrderId: FundSubLpId
  ): ZIO[
    DataSource,
    Throwable,
    List[SubscriptionOrderSupportingSignatureRequest]
  ] = {
    run(
      subscriptionOrderSupportingSignatureRequest.filter(_.subscriptionOrderId == lift(subscriptionOrderId))
    )
  }

  def get(fundSubId: FundSubId): ZIO[
    DataSource,
    Throwable,
    List[SubscriptionOrderSupportingSignatureRequest]
  ] = {
    run(
      subscriptionOrderSupportingSignatureRequest.filter(model =>
        sql"${model.subscriptionOrderId} LIKE ${lift(fundSubId.idString + "%")}".asCondition
      )
    )
  }

  def getOpt(
    id: SignatureRequestId
  ): ZIO[
    DataSource,
    Throwable,
    Option[SubscriptionOrderSupportingSignatureRequest]
  ] = {
    run(subscriptionOrderSupportingSignatureRequest.filter(_.id == lift(id))).map(_.headOption)
  }

  def delete(id: SignatureRequestId): ZIO[DataSource, Throwable, Unit] = {
    run(subscriptionOrderSupportingSignatureRequest.filter(_.id == lift(id)).delete).unit
  }

  def delete(ids: List[SignatureRequestId]): ZIO[DataSource, Throwable, Unit] = {
    run(
      liftQuery(ids).foreach(id => subscriptionOrderSupportingSignatureRequest.filter(_.id == id).delete)
    ).unit
  }

}
