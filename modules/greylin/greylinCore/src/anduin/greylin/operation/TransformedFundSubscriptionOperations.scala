// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation

import javax.sql.DataSource
import zio.ZIO

import anduin.greylin.modelti.TransformedFundSubscription
import anduin.id.fundsub.FundSubId

import io.getquill.*
import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given

object TransformedFundSubscriptionOperations {

  private inline def transformedFundSubscription = quote(
    querySchema[TransformedFundSubscription]("tf_fund_subscription")
  )

  def insert(model: TransformedFundSubscription): ZIO[DataSource, Throwable, TransformedFundSubscription] = {
    run(transformedFundSubscription.insertValue(lift(model))).map(_ => model)
  }

  def insert(models: List[TransformedFundSubscription]): ZIO[DataSource, Throwable, List[TransformedFundSubscription]] = {
    run(
      liftQuery(models).foreach(model => transformedFundSubscription.insertValue(model)),
      1000
    ).map(_ => models)
  }

  def update(
    id: FundSubId
  )(
    updateFn: TransformedFundSubscription => TransformedFundSubscription
  ): ZIO[DataSource, Throwable, TransformedFundSubscription] = {
    for {
      model <- get(id)
      newModel <- ZIO.attempt(updateFn(model))
      _ <- run(
        transformedFundSubscription.filter(_.id == lift(id)).updateValue(lift(newModel))
      )
    } yield newModel
  }

  def updateIfExist(
    id: FundSubId
  )(
    updateFn: TransformedFundSubscription => TransformedFundSubscription
  ): ZIO[DataSource, Throwable, Option[TransformedFundSubscription]] = {
    for {
      modelOpt <- getOpt(id)
      newModelOpt <- ZIO.foreach(modelOpt) { model =>
        val newModel = updateFn(model)
        run(transformedFundSubscription.filter(_.id == lift(id)).updateValue(lift(newModel))).map(_ => newModel)
      }
    } yield newModelOpt
  }

  def delete(id: FundSubId): ZIO[DataSource, Throwable, Unit] =
    run(transformedFundSubscription.filter(_.id == lift(id)).delete).unit

  def get(
    id: FundSubId
  ): ZIO[DataSource, Throwable, TransformedFundSubscription] = {
    for {
      modelOpt <- getOpt(id)
      fund <- ZIO.getOrFail(modelOpt)
    } yield fund
  }

  def get(
    ids: List[FundSubId]
  ): ZIO[DataSource, Throwable, List[TransformedFundSubscription]] = {
    run(transformedFundSubscription.filter { fund => liftQuery(ids).contains(fund.id) })
  }

  def getOpt(
    id: FundSubId
  ): ZIO[DataSource, Throwable, Option[TransformedFundSubscription]] = {
    run(transformedFundSubscription.filter(_.id == lift(id))).map(_.headOption)
  }

}
