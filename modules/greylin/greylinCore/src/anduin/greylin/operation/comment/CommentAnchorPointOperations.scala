// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation.comment

import anduin.greylin.core.operation.{TiDBModelOperation, TiDBModelOperationCompanion, TiDBModelOperationContext}
import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given
import anduin.greylin.modelti.comment.CommentAnchorPoint
import anduin.id.issuetracker.CommentAssignmentId
import io.getquill.*
import zio.RIO
import zio.ZIO

import javax.sql.DataSource

final case class CommentAnchorPointOperations private[greylin] (
  override val ctx: TiDBModelOperationContext
) extends TiDBModelOperation[CommentAssignmentId, CommentAnchorPoint] {

  private val BatchInsertSize = 1000

  inline def commentAnchorPoint = quote(
    querySchema[CommentAnchorPoint](CommentAnchorPoint.tableName)
  )

  def insert(models: List[CommentAnchorPoint]): ZIO[DataSource, Throwable, List[CommentAnchorPoint]] = {
    ZIO
      .foreach(models.sliding(BatchInsertSize).toList) { segment =>
        run(
          liftQuery(segment).foreach(model => commentAnchorPoint.insertValue(model)),
          BatchInsertSize
        )
      }
      .map(_ => models)
  }

  override def insert(model: CommentAnchorPoint): RIO[DataSource, CommentAnchorPoint] = {
    for {
      _ <- run(commentAnchorPoint.insertValue(lift(model)))
    } yield model
  }

  override def update(
    model: CommentAnchorPoint
  ): RIO[DataSource, CommentAnchorPoint] = {
    for {
      _ <- run(commentAnchorPoint.filter(_.id == lift(model.id)).updateValue(lift(model))).as(model)
    } yield model

  }

  def get(
    id: CommentAssignmentId
  ): ZIO[DataSource, Throwable, CommentAnchorPoint] = {
    for {
      modelOpt <- getOpt(id)
      fund <- ZIO.getOrFail(modelOpt)
    } yield fund
  }

  override def getOpt(
    id: CommentAssignmentId
  ): RIO[DataSource, Option[CommentAnchorPoint]] = {
    run(commentAnchorPoint.filter(m => m.id == lift(id))).map(_.headOption)
  }

  override def delete(
    id: CommentAssignmentId
  ): RIO[DataSource, Unit] = {
    for {
      _ <- run(commentAnchorPoint.filter(m => m.id == lift(id)).delete).unit
    } yield ()
  }

}

object CommentAnchorPointOperations extends TiDBModelOperationCompanion[CommentAnchorPointOperations]
