// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation.comment

import anduin.greylin.core.model.Codec.given
import anduin.greylin.core.operation.{TiDBModelOperation, TiDBModelOperationCompanion, TiDBModelOperationContext}
import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.modelti.comment.CommentThread
import anduin.id.issuetracker.IssueId
import io.getquill.*
import zio.{RIO, ZIO}

import javax.sql.DataSource

final case class CommentThreadOperations private[greylin] (
  override val ctx: TiDBModelOperationContext
) extends TiDBModelOperation[IssueId, CommentThread] {

  private val BatchInsertSize = 1000

  inline def commentThread = quote(
    querySchema[CommentThread](CommentThread.tableName)
  )

  def insert(models: List[CommentThread]): ZIO[DataSource, Throwable, List[CommentThread]] = {
    ZIO
      .foreach(models.sliding(BatchInsertSize).toList) { segment =>
        run(
          liftQuery(segment).foreach(model => commentThread.insertValue(model)),
          BatchInsertSize
        )
      }
      .map(_ => models)
  }

  override def insert(model: CommentThread): RIO[DataSource, CommentThread] = {
    for {
      _ <- run(commentThread.insertValue(lift(model)))
    } yield model
  }

  override def update(
    model: CommentThread
  ): RIO[DataSource, CommentThread] = {
    for {
      _ <- run(commentThread.filter(_.id == lift(model.id)).updateValue(lift(model))).as(model)
    } yield model

  }

  def get(
    id: IssueId
  ): ZIO[DataSource, Throwable, CommentThread] = {
    for {
      modelOpt <- getOpt(id)
      fund <- ZIO.getOrFail(modelOpt)
    } yield fund
  }

  override def getOpt(
    id: IssueId
  ): RIO[DataSource, Option[CommentThread]] = {
    run(commentThread.filter(m => m.id == lift(id))).map(_.headOption)
  }

  override def delete(
    id: IssueId
  ): RIO[DataSource, Unit] = {
    for {
      _ <- run(commentThread.filter(m => m.id == lift(id)).delete).unit
    } yield ()
  }

}

object CommentThreadOperations extends TiDBModelOperationCompanion[CommentThreadOperations]
