// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.operation

import javax.sql.DataSource
import zio.ZIO

import anduin.greylin.modelti.SubscriptionOrderVersion
import anduin.id.fundsub.{FundSubId, FundSubLpFormVersionId, FundSubLpId}
import com.anduin.stargazer.service.utils.ZIOUtils

import io.getquill.*
import anduin.tidb.TiDatabase.QuillContext.*
import anduin.greylin.core.model.Codec.given

object SubscriptionOrderVersionOperations {

  private inline def subscriptionOrderVersion = quote(
    querySchema[SubscriptionOrderVersion]("pb_subscription_order_version")
  )

  def insert(version: SubscriptionOrderVersion): ZIO[DataSource, Throwable, Unit] = {
    run(subscriptionOrderVersion.insertValue(lift(version))).unit
  }

  def insert(versions: List[SubscriptionOrderVersion]): ZIO[DataSource, Throwable, Unit] = {
    run(
      liftQuery(versions).foreach(orderVersion => subscriptionOrderVersion.insertValue(orderVersion)),
      100
    ).unit
  }

  def updateFormDataId(
    subscriptionOrderId: FundSubLpId,
    versionIndex: Int,
    formDataId: FundSubLpFormVersionId
  ): ZIO[DataSource, Throwable, Unit] = {
    run(
      subscriptionOrderVersion
        .filter { orderVersion =>
          orderVersion.subscriptionOrderId == lift(subscriptionOrderId) && orderVersion.versionIndex == lift(
            versionIndex
          )
        }
        .update(_.formDataId -> lift(formDataId))
    ).unit
  }

  def getByOrder(subscriptionOrderId: FundSubLpId): ZIO[DataSource, Throwable, List[SubscriptionOrderVersion]] = {
    run(
      subscriptionOrderVersion.filter(_.subscriptionOrderId == lift(subscriptionOrderId))
    )
  }

  def getByFundSub(fundSubscriptionId: FundSubId): ZIO[DataSource, Throwable, List[SubscriptionOrderVersion]] = {
    run(
      subscriptionOrderVersion.filter { orderVersion =>
        sql"${orderVersion.subscriptionOrderId} LIKE ${lift(fundSubscriptionId.idString + '%')}".asCondition
      }
    )
  }

  def getLatestVersion(subscriptionOrderId: FundSubLpId): ZIO[DataSource, Throwable, Option[SubscriptionOrderVersion]] = {
    for {
      latestVersionIndexOpt <- run(
        subscriptionOrderVersion
          .filter(_.subscriptionOrderId == lift(subscriptionOrderId))
          .map(orderVersion => max(orderVersion.versionIndex))
      ).map(_.headOption)

      latestVersionOpt <- ZIOUtils.traverseOption2(latestVersionIndexOpt) { latestVersionIndex =>
        run(
          subscriptionOrderVersion.filter { orderVersion =>
            orderVersion.subscriptionOrderId == lift(subscriptionOrderId) && orderVersion.versionIndex == lift(
              latestVersionIndex
            )
          }
        ).map(_.headOption)
      }
    } yield latestVersionOpt
  }

}
