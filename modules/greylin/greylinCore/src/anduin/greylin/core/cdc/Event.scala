// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.core.cdc

import io.circe.{<PERSON>ursor<PERSON>p, DecodingFailure, Error, Json, parser}

import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}

private[cdc] final case class Event[M <: TiDBCdcModel](
  eventType: EventType,
  old: Option[M],
  data: Option[M]
) {
  lazy val getOldData: M = old.getOrElse(throw new RuntimeException(s"Old data is empty in $this"))
  lazy val getNewData: M = data.getOrElse(throw new RuntimeException(s"New data is empty in $this"))
}

private[cdc] object Event {

  def fromCdcEvent[M <: TiDBCdcModel](
    value: String
  )(
    using mCompanion: TiDBCdcModelCompanion[M]
  ): Either[Error, Event[M]] = {
    for {
      json <- parser.parse(value)
      cursor = json.hcursor
      eventType <- cursor.get[String]("type").flatMap { eventType =>
        EventType.values
          .find(_.value == eventType)
          .toRight(DecodingFailure("Invalid type field of cdc event", List(CursorOp.DownField("type"))))
      }
      old <- cursor.downField("old").focus match {
        case Some(Json.Null) =>
          Right(None)
        case Some(json) =>
          if (json.hcursor.values.exists(_.size > 1)) {
            scribe.info(s"Received more than 1 models for cdc event $value")
          }
          mCompanion.fromCdcEvent(json.hcursor.downArray).map(Some(_))
        case None =>
          Left(DecodingFailure("Invalid old field of cdc event", List(CursorOp.DownField("old"))))
      }
      data <- cursor.downField("data").focus match {
        case Some(Json.Null) =>
          Right(None)
        case Some(json) =>
          if (json.hcursor.values.exists(_.size > 1)) {
            scribe.info(s"Received more than 1 models for cdc event $value")
          }
          mCompanion.fromCdcEvent(json.hcursor.downArray).map(Some(_))
        case None =>
          Left(DecodingFailure("Invalid data field of cdc event", List(CursorOp.DownField("data"))))
      }
    } yield Event(
      eventType,
      old,
      data
    )
  }

}
