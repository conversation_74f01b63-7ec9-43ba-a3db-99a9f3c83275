// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.core.cdc

import zio.{Task, ZIO}

import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.service.GeneralServiceException
import com.anduin.stargazer.service.utils.ZIOUtils

sealed trait TypedEvent[M <: TiDBCdcModel]

object TypedEvent {

  case class InsertEvent[M <: TiDBCdcModel](data: M) extends TypedEvent[M]
  case class UpdateEvent[M <: TiDBCdcModel](old: M, data: M) extends TypedEvent[M]
  case class DeleteEvent[M <: TiDBCdcModel](data: M) extends TypedEvent[M]

  def fromCdcEventRaw[M <: TiDBCdcModel](
    value: String
  )(
    using mCompanion: TiDBCdcModelCompanion[M]
  ): Task[TypedEvent[M]] = {
    for {
      event <- Event
        .fromCdcEvent[M](value)
        .fold(
          error => ZIO.fail(GeneralServiceException(error.getMessage)),
          ZIO.succeed(_)
        )
      typedEvent <- event.eventType match {
        case EventType.Insert =>
          for {
            data <- ZIOUtils.fromOption(event.data, GeneralServiceException(s"Data is empty in $event"))
          } yield InsertEvent[M](data)
        case EventType.Update =>
          for {
            old <- ZIOUtils.fromOption(event.old, GeneralServiceException(s"Old is empty in $event"))
            data <- ZIOUtils.fromOption(event.data, GeneralServiceException(s"Data is empty in $event"))
          } yield UpdateEvent[M](old, data)
        case EventType.Delete =>
          for {
            data <- ZIOUtils.fromOption(event.data, GeneralServiceException(s"Data is empty in $event"))
          } yield DeleteEvent[M](data)
      }
    } yield typedEvent
  }

}
