// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.core.operation

private[greylin] sealed trait TiDBModelOperationContext derives CanEqual {
  def canAddCorrectionEvent: Boolean
}

private[greylin] object TiDBModelOperationContext {

  case object Default extends TiDBModelOperationContext {
    override def canAddCorrectionEvent: Boolean = true
  }

  case object Correction extends TiDBModelOperationContext {
    override def canAddCorrectionEvent: Boolean = false
  }

  case object Migration extends TiDBModelOperationContext {
    override def canAddCorrectionEvent: Boolean = false
  }

  case object Test extends TiDBModelOperationContext {
    override def canAddCorrectionEvent: Boolean = false
  }

}
