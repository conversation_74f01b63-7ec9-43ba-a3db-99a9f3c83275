// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.core.event

import java.nio.charset.StandardCharsets

import zio.{Task, ZIO}

import anduin.greylin.core.cdc.TypedEvent
import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.kafka.{KafkaFiber, KafkaService, KafkaSimpleConsumer}
import com.anduin.stargazer.service.utils.ZIOUtils
import com.anduin.stargazer.utils.DeploymentUtils

abstract class GreylinEventListener extends KafkaFiber { self =>

  protected type INPUT = (TiDBCdcModelCompanion[?], String => Task[Unit])

  def groupName: String

  def kafkaService: KafkaService

  def inputs: List[INPUT]

  def enabled: Boolean = true

  private lazy val consumers = inputs.map(_._1.cdcTopic).map { inputTopic =>
    KafkaSimpleConsumer[Array[Byte], Array[Byte]](
      kafkaService,
      inputTopic,
      consumerGroupName = consumerGroupName,
      handler = (_, message) => {
        val value = new String(message, StandardCharsets.UTF_8)
        val inputOpt = inputs.find(_._1.cdcTopic.name == inputTopic.name)
        inputOpt.fold(
          ZIO.fail(new RuntimeException(s"$className: Unhandled topic ${inputTopic.name}"))
        )(
          _._2.apply(value)
        )
      }
    )
  }

  final protected def registerInput[M <: TiDBCdcModel](
    input: (TiDBCdcModelCompanion[M], TypedEvent[M] => Task[Unit])
  )(
    using mCompanion: TiDBCdcModelCompanion[M]
  ): INPUT = {
    val handler = { (message: String) =>
      for {
        typedEvent <- TypedEvent.fromCdcEventRaw[M](message)
        _ <- ZIO.logInfo(s"$className: Handle ${typedEvent.getClass.getSimpleName} of topic ${input._1.cdcTopic.name}")
        _ <- input._2(typedEvent)
      } yield ()
    }
    input._1 -> handler
  }

  private lazy val className = self.getClass.getSimpleName

  private def consumerGroupName = groupName + DeploymentUtils.tenantSuffix

  override final def start(): Task[Unit] = {
    ZIOUtils.when(enabled)(
      ZIO.collectAllParDiscard(consumers.map(_.start()))
    )
  }

  override final def close(): Task[Unit] = {
    ZIOUtils.when(enabled)(
      ZIO.collectAllParDiscard(consumers.map(_.close()))
    )
  }

}
