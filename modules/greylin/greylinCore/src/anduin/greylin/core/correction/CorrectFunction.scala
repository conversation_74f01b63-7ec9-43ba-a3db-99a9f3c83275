// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.core.correction

import io.circe.{Code<PERSON>, Decoder, Encoder}

import anduin.greylin.core.model.{TiDBModel, TiDBModelCompanion}
import anduin.greylin.core.operation.TiDBModelOperation
import anduin.greylin.GreylinDataService
import zio.{Task, ZIO}

import anduin.greylin.core.correction.CorrectFunction.CorrectResult

private[greylin] abstract class CorrectFunction[PrimaryKey: {Encoder, Decoder}, Model <: TiDBModel: SelfEqual](
  using mCompanion: TiDBModelCompanion[Model]
) {

  def defaultPrimaryKey: PrimaryKey

  def greylinDataService: GreylinDataService

  def modelOps: TiDBModelOperation[PrimaryKey, Model]

  def compute(pk: PrimaryKey): Task[Option[Model]]

  final def correct(pk: PrimaryKey): Task[CorrectResult[PrimaryKey, Model]] = {
    val task = for {
      correctModelOpt <- compute(pk)
      curModelOpt <- greylinDataService.run(
        modelOps.getOpt(pk)
      )

      result <- correctModelOpt match {
        case Some(correctModel) =>
          curModelOpt.fold(
            insertModel(pk, correctModel)
          ) { curModel =>
            if (curModel != correctModel) {
              updateModel(pk, correctModel)
            } else {
              ZIO.succeed(CorrectResult.NoUpdate(pk, Some(correctModel)))
            }
          }
        case None =>
          curModelOpt.fold(
            ZIO.succeed(CorrectResult.NoUpdate(pk, None))
          ) { curModel =>
            deleteModel(pk, curModel)
          }
      }
    } yield result

    task.catchAllCause { err =>
      ZIO.succeed(CorrectResult.Failed[PrimaryKey, Model](pk, err.prettyPrint))
    }

  }

  private def insertModel(pk: PrimaryKey, model: Model): Task[CorrectResult[PrimaryKey, Model]] = {
    for {
      _ <- ZIO.logInfo(s"[GreylinCorrection] insert model with PK $pk")
      _ <- greylinDataService.run(
        modelOps.insert(model)
      )
    } yield CorrectResult.Inserted(pk, model)
  }

  private def updateModel(pk: PrimaryKey, model: Model): Task[CorrectResult[PrimaryKey, Model]] = {
    for {
      _ <- ZIO.logInfo(s"[GreylinCorrection] update model with PK $pk")
      _ <- greylinDataService.run(
        modelOps.update(model)
      )
    } yield CorrectResult.Updated(pk, model)
  }

  private def deleteModel(pk: PrimaryKey, curModel: Model): Task[CorrectResult[PrimaryKey, Model]] = {
    for {
      _ <- ZIO.logInfo(s"[GreylinCorrection] delete model with PK $pk")
      _ <- greylinDataService.run(
        modelOps.delete(pk)
      )
    } yield CorrectResult.Deleted(pk, curModel)
  }

  final def getTableName: String = mCompanion.tableName

  final def primaryKeyCodec: Codec[PrimaryKey] = Codec.from(
    summon[Decoder[PrimaryKey]],
    summon[Encoder[PrimaryKey]]
  )

}

private[greylin] object CorrectFunction {

  sealed trait CorrectResult[PK, M]

  given [PK, M] => (SelfEqual[PK], SelfEqual[M]) => SelfEqual[CorrectResult[PK, M]] = CanEqual.derived

  object CorrectResult {
    case class Inserted[PK, M](pk: PK, model: M) extends CorrectResult[PK, M]
    case class Updated[PK, M](pk: PK, model: M) extends CorrectResult[PK, M]
    case class Deleted[PK, M](pk: PK, curModel: M) extends CorrectResult[PK, M]
    case class NoUpdate[PK, M](pk: PK, modelOpt: Option[M]) extends CorrectResult[PK, M]
    case class Failed[PK, M](pk: PK, error: String) extends CorrectResult[PK, M]
  }

}
