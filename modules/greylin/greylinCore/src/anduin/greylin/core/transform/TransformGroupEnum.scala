// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.core.transform

import com.anduin.stargazer.utils.DeploymentUtils

private[greylin] enum TransformGroupEnum {
  def consumerGroupName: String = toString + DeploymentUtils.tenantSuffix

  case FundSubscriptionTransform

  case DataRoomTransform

  case FundDataInvestmentEntityDocumentTransform

  case SubscriptionOrderSupportingFormFile
}
