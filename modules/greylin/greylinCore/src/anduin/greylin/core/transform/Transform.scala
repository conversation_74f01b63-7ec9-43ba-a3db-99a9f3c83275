// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.core.transform

import java.nio.charset.StandardCharsets

import zio.{Task, ZIO}

import anduin.greylin.core.cdc.TypedEvent
import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.kafka.{KafkaService, KafkaSimpleConsumer}

abstract class Transform { self =>

  protected type INPUT = (TiDBCdcModelCompanion[?], String => Task[Unit])

  def groupName: TransformGroupEnum

  def kafkaService: KafkaService

  def inputs: List[INPUT]

  private lazy val consumers = inputs.map(_._1.cdcTopic).map { inputTopic =>
    KafkaSimpleConsumer[Array[Byte], Array[Byte]](
      kafkaService,
      inputTopic,
      consumerGroupName = groupName.consumerGroupName,
      handler = (_, message) => {
        val value = new String(message, StandardCharsets.UTF_8)
        val inputOpt = inputs.find(_._1.cdcTopic.name == inputTopic.name)
        inputOpt.fold(
          ZIO.fail(new RuntimeException(s"$className: Unhandled topic ${inputTopic.name}"))
        )(
          _._2.apply(value)
        )
      }
    )
  }

  final protected def registerInput[M <: TiDBCdcModel](
    input: (TiDBCdcModelCompanion[M], TypedEvent[M] => Task[Unit])
  )(
    using mCompanion: TiDBCdcModelCompanion[M]
  ): INPUT = {
    val handler = { (message: String) =>
      for {
        typedEvent <- TypedEvent.fromCdcEventRaw[M](message)
        _ <- ZIO.logInfo(s"$className: Handle ${typedEvent.getClass.getSimpleName} of topic ${input._1.cdcTopic.name}")
        _ <- input._2(typedEvent)
      } yield ()
    }
    input._1 -> handler
  }

  private lazy val className = self.getClass.getSimpleName

  final def start(): Task[Unit] =
    ZIO.collectAllParDiscard(consumers.map(_.start()))

  final def close(): Task[Unit] =
    ZIO.collectAllParDiscard(consumers.map(_.close()))

}
