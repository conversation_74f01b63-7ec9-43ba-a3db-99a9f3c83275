// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti

import io.circe.{ACursor, DecodingFailure}
import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.model.common.user.UserId
import anduin.model.id.TeamId

final case class FundSubscriptionAdminGroupMember(
  userId: UserId,
  groupId: TeamId
) extends TiDBCdcModel

object FundSubscriptionAdminGroupMember extends TiDBCdcModelCompanion[FundSubscriptionAdminGroupMember] {

  override val tableName: String = "pb_fund_subscription_admin_group_member"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, FundSubscriptionAdminGroupMember] = {
    for {
      userId <- value.get[UserId]("user_id")
      groupId <- value.get[TeamId]("group_id")
    } yield FundSubscriptionAdminGroupMember(
      userId,
      groupId
    )
  }

}
