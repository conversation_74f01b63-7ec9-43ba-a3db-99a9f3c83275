// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti

import anduin.greylin.core.const.Constants
import anduin.id.docrequest.FormSubmissionId
import anduin.id.fundsub.FundSubLpId
import anduin.model.id.FileId

final case class TransformedSubscriptionOrderSupportingFormFile(
  fileId: FileId,
  formSubmissionId: FormSubmissionId,
  subscriptionOrderId: FundSubLpId,
  docType: String
) {

  def withDocType(docType: String): TransformedSubscriptionOrderSupportingFormFile =
    copy(docType = docType.take(Constants.MAX_DESCRIPTION_LENGTH))

}
