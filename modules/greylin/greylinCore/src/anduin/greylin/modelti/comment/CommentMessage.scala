// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.comment

import anduin.greylin.core.model.{TiDBModel, TiDBModelCompanion}
import anduin.id.issuetracker.IssueId
import anduin.model.common.user.UserId

import java.time.Instant

final case class CommentMessage(
  id: String,
  threadId: IssueId,
  isRemoved: Boolean = false,
  creator: Option[UserId] = None,
  createdAt: Option[Instant] = None
) extends TiDBModel derives CanEqual {
  val primaryKey: String = id
}

object CommentMessage extends TiDBModelCompanion[CommentMessage] {

  override inline def tableName: String = "comment_message"

  def makeCommentId(threadId: IssueId, commentId: String = "root_comment"): String = {
    s"${threadId.idString}.$commentId"
  }

}
