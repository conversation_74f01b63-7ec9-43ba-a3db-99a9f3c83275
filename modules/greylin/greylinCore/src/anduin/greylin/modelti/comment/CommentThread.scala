// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.comment

import anduin.greylin.core.model.{TiDBModel, TiDBModelCompanion}
import anduin.id.issuetracker.{CommentAssignmentId, IssueId}
import anduin.model.common.user.UserId

import java.time.Instant

final case class CommentThread(
  id: IssueId,
  anchorPointId: CommentAssignmentId,
  isInternal: Bo<PERSON>an,
  isResolved: Boolean = false,
  isRemoved: <PERSON><PERSON><PERSON> = false,
  creator: Option[UserId],
  createdAt: Option[Instant]
) extends TiDBModel derives CanEqual {
  val primaryKey: IssueId = id
}

object CommentThread extends TiDBModelCompanion[CommentThread] {

  override inline def tableName: String = "comment_thread"

}
