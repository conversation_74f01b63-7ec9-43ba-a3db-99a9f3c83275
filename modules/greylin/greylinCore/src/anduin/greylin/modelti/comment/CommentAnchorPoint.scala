// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.comment

import anduin.greylin.core.const.Constants
import anduin.greylin.core.model.{TiDBModel, TiDBModelCompanion}
import anduin.id.form.FormVersionId
import anduin.id.fundsub.{FundSubId, FundSubLpId}
import anduin.id.issuetracker.CommentAssignmentId

final case class CommentAnchorPoint(
  id: CommentAssignmentId,
  fundId: FundSubId,
  lpId: FundSubLpId,
  anchorPointCategory: String = "",
  doctype: String = "",
  formVersionIdOpt: Option[FormVersionId] = None,
  formFieldAlias: String = "",
  formFieldDescription: String = "",
  formTocSection: String = ""
) extends TiDBModel derives CanEqual {
  val primaryKey: CommentAssignmentId = id

  def withAnchorPointCategory(category: String): CommentAnchorPoint = {
    val anchorPointCategory = if (category.trim.equalsIgnoreCase(CommentAnchorPoint.AmlKycTarget)) {
      CommentAnchorPoint.AmlKycTarget
    } else {
      CommentAnchorPoint.FormQuestionTarget
    }
    copy(
      anchorPointCategory = anchorPointCategory
    )
  }

  def withDoctype(value: String): CommentAnchorPoint = {
    copy(doctype = value.take(Constants.MAX_DESCRIPTION_LENGTH))
  }

  def withFieldAlias(value: String): CommentAnchorPoint = {
    copy(formFieldAlias = value.take(Constants.MAX_NAME_LENGTH))
  }

  def withFormFieldDescription(value: String): CommentAnchorPoint = {
    copy(formFieldDescription = value.take(Constants.MAX_DESCRIPTION_LENGTH))
  }

  def withFormTocSection(value: String): CommentAnchorPoint = {
    copy(formTocSection = value.take(Constants.MAX_NAME_LENGTH))
  }

  def truncate(): CommentAnchorPoint = {
    copy(
      doctype = doctype.take(Constants.MAX_DESCRIPTION_LENGTH),
      formFieldAlias = formFieldAlias.take(Constants.MAX_NAME_LENGTH),
      formFieldDescription = formFieldDescription.take(Constants.MAX_DESCRIPTION_LENGTH),
      formTocSection = formTocSection.take(Constants.MAX_NAME_LENGTH)
    )
  }

}

object CommentAnchorPoint extends TiDBModelCompanion[CommentAnchorPoint] {

  val AmlKycTarget = "AML_KYC"
  val FormQuestionTarget = "FORM_QUESTION"

  override inline def tableName: String = "comment_anchor_point"

}
