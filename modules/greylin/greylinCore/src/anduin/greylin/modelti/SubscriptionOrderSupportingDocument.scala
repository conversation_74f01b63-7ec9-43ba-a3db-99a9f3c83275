// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti

import io.circe.{ACursor, DecodingFailure}

import anduin.greylin.core.const.Constants
import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.fundsub.FundSubLpId
import anduin.model.id.FileId

final case class SubscriptionOrderSupportingDocument(
  fileId: FileId,
  subscriptionOrderId: FundSubLpId,
  docType: String,
  isFundShared: Boolean
) extends TiDBCdcModel {

  def withDocType(docType: String): SubscriptionOrderSupportingDocument =
    copy(docType = docType.take(Constants.MAX_DESCRIPTION_LENGTH))

}

object SubscriptionOrderSupportingDocument extends TiDBCdcModelCompanion[SubscriptionOrderSupportingDocument] {

  override val tableName: String = "pb_subscription_order_supporting_document"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, SubscriptionOrderSupportingDocument] = {
    for {
      fileId <- value.get[FileId]("file_id")
      subscriptionOrderId <- value.get[FundSubLpId]("subscription_order_id")
      docType <- value.get[String]("doc_type")
      isFundShared <- value.get[Int]("is_fund_shared")
    } yield SubscriptionOrderSupportingDocument(
      fileId,
      subscriptionOrderId,
      docType,
      isFundShared != 0
    )
  }

}
