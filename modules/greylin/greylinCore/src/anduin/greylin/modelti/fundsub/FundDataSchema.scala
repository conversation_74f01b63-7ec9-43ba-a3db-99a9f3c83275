// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.greylin.modelti.fundsub

import java.time.Instant

import io.circe.{ACursor, DecodingFailure, Json}

import anduin.fundsub.cue.model.CueSchemaFundSubTypes.FundInfoSchema
import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.fundsub.FundSubId

final case class FundDataSchema(
  id: FundSubId,
  data: Json,
  updatedAt: Option[Instant]
) extends TiDBCdcModel derives CanEqual {
  val refinedData: Option[FundInfoSchema] = data.as[FundInfoSchema].toOption
}

object FundDataSchema extends TiDBCdcModelCompanion[FundDataSchema] {

  override inline def tableName: String = "pb_fund_data_schema"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, FundDataSchema] = {
    for {
      id <- value.get[FundSubId]("id")
      data <- value.get[Json]("data")
      updatedAt <- value.get[Option[Instant]]("updated_at")
    } yield FundDataSchema(id, data, updatedAt)
  }

}
