// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.fundsub.template

import io.circe.{ACursor, DecodingFailure}

import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.form.DataTemplateVersionId
import anduin.id.fundsub.FundSubId

final case class FundSubscriptionDataTemplate(
  fundSubscriptionId: FundSubId,
  templateVersionId: DataTemplateVersionId
) extends TiDBCdcModel derives CanEqual

object FundSubscriptionDataTemplate extends TiDBCdcModelCompanion[FundSubscriptionDataTemplate] {

  override inline def tableName: String = "pb_fund_subscription_data_template"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, FundSubscriptionDataTemplate] = {
    for {
      fundSubscriptionId <- value.get[FundSubId]("fund_subscription_id")
      templateVersionId <- value.get[DataTemplateVersionId]("template_version_id")
    } yield FundSubscriptionDataTemplate(fundSubscriptionId, templateVersionId)
  }

}
