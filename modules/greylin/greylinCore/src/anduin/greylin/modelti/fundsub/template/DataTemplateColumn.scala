// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.fundsub.template

import io.circe.{ACursor, Decoder}

import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.form.DataTemplateVersionId

final case class DataTemplateColumn(
  templateVersionId: DataTemplateVersionId,
  columnIndex: Short,
  title: String
) extends TiDBCdcModel derives CanEqual

object DataTemplateColumn extends TiDBCdcModelCompanion[DataTemplateColumn] {

  override inline def tableName = "pb_data_template_column"

  override def fromCdcEvent(value: ACursor) = {
    for {
      templateVersionId <- value.get[DataTemplateVersionId]("template_version_id")
      index <- value.get[Short]("column_index")
      title <- value.get[String]("title")
    } yield DataTemplateColumn(templateVersionId, index, title)
  }

}
