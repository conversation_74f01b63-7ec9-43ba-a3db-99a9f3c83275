// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.greylin.modelti.fundsub

import java.time.Instant

import io.circe.{ACursor, DecodingFailure}

import anduin.greylin.core.model.JsonDecoder.given
import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.fundsub.{FundSubAdvisorTagId, FundSubId}
import anduin.model.codec.ProtoCodecs.given
import anduin.model.common.user.UserId
import anduin.protobuf.tag.TagColor

final case class FundSubscriptionAdvisorTag(
  id: FundSubAdvisorTagId,
  fundSubscriptionId: FundSubId,
  name: String,
  color: TagColor,
  updatedBy: Option[UserId],
  updatedAt: Option[Instant]
) extends TiDBCdcModel derives CanEqual

object FundSubscriptionAdvisorTag extends TiDBCdcModelCompanion[FundSubscriptionAdvisorTag] {

  override inline def tableName: String = "pb_fund_subscription_advisor_tag"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, FundSubscriptionAdvisorTag] = {
    for {
      id <- value.get[FundSubAdvisorTagId]("id")
      fundSubscriptionId <- value.get[FundSubId]("fund_subscription_id")
      name <- value.get[String]("name")
      color <- value.get[TagColor]("color")
      updatedBy <- value.get[Option[UserId]]("updated_by")
      updatedAt <- value.get[Option[Instant]]("updated_at")
    } yield FundSubscriptionAdvisorTag(
      id,
      fundSubscriptionId,
      name,
      color,
      updatedBy,
      updatedAt
    )
  }

}
