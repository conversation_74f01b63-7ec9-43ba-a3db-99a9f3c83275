// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.greylin.modelti.fundsub

import java.time.Instant

import io.circe.{ACursor, DecodingFailure, parser}

import anduin.fundsub.cue.model.CueJsonFundSubTypes.SubscriptionDataJson
import anduin.fundsub.cue.model.CueSchemaFundSubTypes
import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.fundsub.FundSubLpId
import anduin.greylin.core.model.JsonDecoder.given

final case class SubscriptionDataSchema(
  id: FundSubLpId,
  data: String,
  updatedAt: Option[Instant]
) extends TiDBCdcModel derives CanEqual {

  val refinedData: Option[CueSchemaFundSubTypes.SubscriptionDataSchema] = {
    parser.parse(data).toOption.flatMap { json =>
      SubscriptionDataJson(json).toCueSchema.toOption
    }
  }

}

object SubscriptionDataSchema extends TiDBCdcModelCompanion[SubscriptionDataSchema] {

  override inline def tableName: String = "pb_subscription_data_schema"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, SubscriptionDataSchema] = {
    for {
      id <- value.get[FundSubLpId]("id")
      data <- value.get[String]("data")
      updatedAt <- value.get[Option[Instant]]("updated_at")
    } yield SubscriptionDataSchema(id, data, updatedAt)
  }

}
