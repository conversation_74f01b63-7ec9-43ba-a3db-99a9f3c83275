// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.fundsub.template

import java.time.Instant

import io.circe.{ACursor, Decoder}

import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.form.DataTemplateVersionId
import anduin.model.common.user.UserId

final case class DataTemplate(
  templateVersionId: DataTemplateVersionId,
  name: String,
  templateType: DataTemplateType,
  lastChangeDescription: String,
  createdBy: Option[UserId],
  createdAt: Option[Instant],
  lastUpdatedAt: Option[Instant]
) extends TiDBCdcModel derives CanEqual

object DataTemplate extends TiDBCdcModelCompanion[DataTemplate] {

  override inline def tableName = "pb_data_template"

  override def fromCdcEvent(value: ACursor) = {
    for {
      templateVersionId <- value.get[DataTemplateVersionId]("template_version_id")
      name <- value.get[String]("name")
      templateType <- value.get[DataTemplateType]("template_type")
      lastChangeDescription <- value.get[String]("last_change_description")
      createdBy <- value.get[Option[UserId]]("created_by")
      createdAt <- value.get[Option[Instant]]("created_at")
      lastUpdatedAt <- value.get[Option[Instant]]("last_updated_at")
    } yield DataTemplate(
      templateVersionId,
      name,
      templateType,
      lastChangeDescription,
      createdBy,
      createdAt,
      lastUpdatedAt
    )
  }

}
