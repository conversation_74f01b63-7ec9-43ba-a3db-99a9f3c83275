// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.fundsub

import java.time.Instant

import io.circe.{ACursor, DecodingFailure}

import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.fundsub.{FundSubId, FundSubLpTagId}
import anduin.model.common.user.UserId

final case class FundSubscriptionTag(
  id: FundSubLpTagId,
  fundSubscriptionId: FundSubId,
  name: String,
  creatorOpt: Option[UserId],
  createdAt: Option[Instant]
) extends TiDBCdcModel derives CanEqual

object FundSubscriptionTag extends TiDBCdcModelCompanion[FundSubscriptionTag] {

  override inline def tableName: String = "pb_fund_subscription_tag"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, FundSubscriptionTag] = {
    for {
      id <- value.get[FundSubLpTagId]("id")
      fundSubscriptionId <- value.get[FundSubId]("fund_subscription_id")
      name <- value.get[String]("name")
      creatorOpt <- value.get[Option[UserId]]("creator_opt")
      createdAt <- value.get[Option[Instant]]("created_at")
    } yield FundSubscriptionTag(id, fundSubscriptionId, name, creatorOpt, createdAt)
  }

}
