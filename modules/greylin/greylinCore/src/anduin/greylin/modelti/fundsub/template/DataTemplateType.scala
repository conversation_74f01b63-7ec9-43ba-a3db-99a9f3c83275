// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.fundsub.template

import io.circe.Decoder
import io.getquill.MappedEncoding

import anduin.enumeration.{StringEnum, StringEnumCompanion}

enum DataTemplateType(val value: String) extends StringEnum {
  case Import extends DataTemplateType("IMPORT")
  case Export extends DataTemplateType("EXPORT")
  case Unrecognized extends DataTemplateType("UNRECOGNIZED")
}

object DataTemplateType extends StringEnumCompanion[DataTemplateType] {
  given templateTypeEncode: MappedEncoding[DataTemplateType, String] = MappedEncoding(_.value)

  given templateTypeDecode: MappedEncoding[String, DataTemplateType] =
    MappedEncoding(s => fromValueOpt(s).getOrElse(Unrecognized))

  given jsonDecoder: Decoder[DataTemplateType] = Decoder.decodeString.map(templateTypeDecode.f)

}
