package anduin.greylin.modelti.fundsub

import io.circe.{ACursor, DecodingFailure}
import squants.market.Currency

import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.fundsub.{FundSubId, InvestmentFundId}
import anduin.greylin.core.model.JsonDecoder.given

final case class FundSubscriptionSubFund(
  id: InvestmentFundId,
  fundSubscriptionId: FundSubId,
  name: String,
  currency: Currency
) extends TiDBCdcModel derives CanEqual

object FundSubscriptionSubFund extends TiDBCdcModelCompanion[FundSubscriptionSubFund] {

  override inline def tableName: String = "pd_fund_subscription_sub_fund"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, FundSubscriptionSubFund] = {
    for {
      id <- value.get[InvestmentFundId]("id")
      fundSubscriptionId <- value.get[FundSubId]("fund_subscription_id")
      name <- value.get[String]("name")
      currency <- value.get[Currency]("currency")
    } yield FundSubscriptionSubFund(
      id,
      fundSubscriptionId,
      name,
      currency
    )
  }

}
