// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.fundsub

import java.time.LocalDate

import io.circe.{ACursor, DecodingFailure}

import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.fundsub.{FundSubCloseId, FundSubId}

final case class FundSubscriptionClose(
  id: FundSubCloseId,
  fundSubscriptionId: FundSubId,
  name: String,
  targetCloseDate: Option[LocalDate],
  customCloseId: String
) extends TiDBCdcModel derives CanEqual

object FundSubscriptionClose extends TiDBCdcModelCompanion[FundSubscriptionClose] {

  override inline def tableName: String = "pb_fund_subscription_close"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, FundSubscriptionClose] = {
    for {
      id <- value.get[FundSubCloseId]("id")
      fundSubscriptionId <- value.get[FundSubId]("fund_subscription_id")
      name <- value.get[String]("name")
      targetCloseDate <- value.get[Option[LocalDate]]("target_close_date")
      customCloseId <- value.get[String]("custom_close_id")
    } yield FundSubscriptionClose(
      id,
      fundSubscriptionId,
      name,
      targetCloseDate,
      customCloseId
    )
  }

}
