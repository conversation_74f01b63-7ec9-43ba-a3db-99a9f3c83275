package anduin.greylin.modelti.fundsub

import io.circe.{ACursor, DecodingFailure}
import squants.market.Money

import anduin.greylin.core.const.Constants
import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.fundsub.{FundSubLpId, InvestmentFundId}

final case class SubscriptionOrderSubFundInvestment(
  subscriptionOrderId: FundSubLpId,
  subFundId: InvestmentFundId,
  expectedCommitmentAmount: Option[BigDecimal] = None,
  commitmentAmount: Option[BigDecimal] = None,
  acceptedCommitmentAmount: Option[BigDecimal] = None
) extends TiDBCdcModel derives CanEqual {

  def withExpectedCommitment(expectedCommitment: Option[Money]): SubscriptionOrderSubFundInvestment = copy(
    expectedCommitmentAmount = expectedCommitment.map(_.amount).filter(_ <= Constants.MAX_MONEY_AMOUNT)
  )

  def withCommitment(commitment: Option[Money]): SubscriptionOrderSubFundInvestment = copy(
    commitmentAmount = commitment.map(_.amount).filter(_ <= Constants.MAX_MONEY_AMOUNT)
  )

  def withAcceptedCommitment(acceptedCommitment: Option[Money]): SubscriptionOrderSubFundInvestment = copy(
    acceptedCommitmentAmount = acceptedCommitment.map(_.amount).filter(_ <= Constants.MAX_MONEY_AMOUNT)
  )

}

object SubscriptionOrderSubFundInvestment extends TiDBCdcModelCompanion[SubscriptionOrderSubFundInvestment] {

  override inline def tableName: String = "pd_subscription_order_sub_fund_investment"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, SubscriptionOrderSubFundInvestment] = {
    for {
      subscriptionOrderId <- value.get[FundSubLpId]("subscription_order_id")
      subFundId <- value.get[InvestmentFundId]("sub_fund_id")
      expectedCommitmentAmount <- value.get[Option[BigDecimal]]("expected_commitment_amount")
      commitmentAmount <- value.get[Option[BigDecimal]]("commitment_amount")
      acceptedCommitmentAmount <- value.get[Option[BigDecimal]]("accepted_commitment_amount")
    } yield SubscriptionOrderSubFundInvestment(
      subscriptionOrderId,
      subFundId,
      expectedCommitmentAmount,
      commitmentAmount,
      acceptedCommitmentAmount
    )
  }

}
