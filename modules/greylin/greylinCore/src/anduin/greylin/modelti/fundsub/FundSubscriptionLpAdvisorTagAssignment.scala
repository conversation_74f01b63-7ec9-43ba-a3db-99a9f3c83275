// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.fundsub

import java.time.Instant

import io.circe.{ACursor, DecodingFailure}

import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.fundsub.{FundSubAdvisorTagId, FundSubLpId}
import anduin.model.common.user.UserId
import anduin.greylin.core.model.JsonDecoder.given

final case class FundSubscriptionLpAdvisorTagAssignment(
  lpId: FundSubLpId,
  advisorTagId: FundSubAdvisorTagId,
  assignedBy: Option[UserId],
  assignedAt: Option[Instant]
) extends TiDBCdcModel derives CanEqual

object FundSubscriptionLpAdvisorTagAssignment extends TiDBCdcModelCompanion[FundSubscriptionLpAdvisorTagAssignment] {

  override inline def tableName: String = "pb_fund_subscription_lp_advisor_tag_assignment"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, FundSubscriptionLpAdvisorTagAssignment] = {
    for {
      lpId <- value.get[FundSubLpId]("lp_id")
      advisorTagId <- value.get[FundSubAdvisorTagId]("advisor_tag_id")
      assignedBy <- value.get[Option[UserId]]("assigned_by")
      assignedAt <- value.get[Option[Instant]]("assigned_at")
    } yield FundSubscriptionLpAdvisorTagAssignment(
      lpId,
      advisorTagId,
      assignedBy,
      assignedAt
    )
  }

}
