// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti

import io.circe.{ACursor, DecodingFailure}

import anduin.greylin.core.const.Constants
import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.fundsub.FundSubId
import anduin.id.role.fundsub.FundManagerRoleId
import anduin.model.id.TeamId

final case class FundSubscriptionAdminGroup(
  groupId: TeamId,
  name: String,
  fundSubscriptionId: FundSubId,
  groupRoleId: FundManagerRoleId
) extends TiDBCdcModel {

  def withName(name: String): FundSubscriptionAdminGroup = copy(
    name = name.take(Constants.MAX_NAME_LENGTH)
  )

}

object FundSubscriptionAdminGroup extends TiDBCdcModelCompanion[FundSubscriptionAdminGroup] {

  override val tableName: String = "pb_fund_subscription_admin_group"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, FundSubscriptionAdminGroup] = {
    for {
      groupId <- value.get[TeamId]("group_id")
      name <- value.get[String]("name")
      fundSubscriptionId <- value.get[FundSubId]("fund_subscription_id")
      groupRoleId <- value.get[FundManagerRoleId]("group_role_id")
    } yield FundSubscriptionAdminGroup(
      groupId,
      name,
      fundSubscriptionId,
      groupRoleId
    )
  }

}
