// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti

import io.circe.Decoder
import io.getquill.MappedEncoding
import anduin.enumeration.StringEnum

enum SubscriptionOrderStatus(val value: String) extends StringEnum {

  case NotStarted extends SubscriptionOrderStatus("NOT_STARTED")

  case InProgress extends SubscriptionOrderStatus("IN_PROGRESS")

  case ChangeInProgress extends SubscriptionOrderStatus("CHANGE_IN_PROGRESS")

  case FormFilled extends SubscriptionOrderStatus("FORM_FILLED")

  case PendingReview extends SubscriptionOrderStatus("PENDING_REVIEW")

  case FormReviewed extends SubscriptionOrderStatus("FORM_REVIEWED")

  case PendingSignature extends SubscriptionOrderStatus("PENDING_SIGNATURE")

  case PendingSubmission extends SubscriptionOrderStatus("PENDING_SUBMISSION")

  case PendingApproval extends SubscriptionOrderStatus("PENDING_APPROVAL")

  case Submitted extends SubscriptionOrderStatus("SUBMITTED")

  case Countersigned extends SubscriptionOrderStatus("COUNTERSIGNED")

  case Complete extends SubscriptionOrderStatus("DISTRIBUTED")

  case Removed extends SubscriptionOrderStatus("REMOVED")

  case Unrecognized extends SubscriptionOrderStatus("UNRECOGNIZED")
}

object SubscriptionOrderStatus {

  given statusEncode: MappedEncoding[SubscriptionOrderStatus, String] = MappedEncoding(_.value)

  given statusDecode: MappedEncoding[String, SubscriptionOrderStatus] = MappedEncoding { s =>
    values.find(_.value == s).getOrElse(Unrecognized)
  }

  given jsonDecoder: Decoder[SubscriptionOrderStatus] = Decoder.decodeString.map(statusDecode.f)

  given toOrdering: Conversion[SubscriptionOrderStatus, Int] = {
    case SubscriptionOrderStatus.NotStarted        => 0
    case SubscriptionOrderStatus.InProgress        => 1
    case SubscriptionOrderStatus.ChangeInProgress  => 2
    case SubscriptionOrderStatus.FormFilled        => 3
    case SubscriptionOrderStatus.PendingReview     => 4
    case SubscriptionOrderStatus.FormReviewed      => 5
    case SubscriptionOrderStatus.PendingSignature  => 6
    case SubscriptionOrderStatus.PendingSubmission => 7
    case SubscriptionOrderStatus.PendingApproval   => 8
    case SubscriptionOrderStatus.Submitted         => 9
    case SubscriptionOrderStatus.Countersigned     => 10
    case SubscriptionOrderStatus.Complete          => 11
    case SubscriptionOrderStatus.Removed           => -1
    case _                                         => -2
  }

}
