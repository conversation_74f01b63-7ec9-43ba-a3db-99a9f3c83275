// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.funddata

import anduin.greylin.core.const.Constants
import anduin.greylin.core.model.{TiDBModel, TiDBModelCompanion}
import anduin.id.funddata.{FundDataFirmId, FundDataGroupRoleId}
import anduin.model.id.TeamId

final case class FundDataFirmGroup(
  id: TeamId,
  name: String = "",
  firmId: FundDataFirmId,
  roleId: FundDataGroupRoleId
) extends TiDBModel {
  def withName(name: String): FundDataFirmGroup = copy(name = name.take(Constants.MAX_NAME_LENGTH))
}

object FundDataFirmGroup extends TiDBModelCompanion[FundDataFirmGroup] {
  override inline def tableName: String = "pb_fund_data_firm_group"
}
