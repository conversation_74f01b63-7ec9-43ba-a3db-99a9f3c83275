// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.funddata

import anduin.greylin.core.model.{TiDBModel, TiDBModelCompanion}
import anduin.id.funddata.FundDataInvestorId
import anduin.id.tag.TagItemId

final case class FundDataInvestorTag(
  investorId: FundDataInvestorId,
  tagId: TagItemId
) extends TiDBModel

object FundDataInvestorTag extends TiDBModelCompanion[FundDataInvestorTag] {
  override val tableName: String = "pb_fund_data_investor_tag"
}
