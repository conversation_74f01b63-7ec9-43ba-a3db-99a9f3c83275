// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.funddata

import anduin.greylin.core.const.Constants
import anduin.greylin.core.model.{TiDBModel, TiDBModelCompanion}
import anduin.id.funddata.FundDataInvestmentEntityContactId

final case class FundDataInvestmentEntityContact(
  id: FundDataInvestmentEntityContactId,
  firstName: String,
  lastName: String,
  email: String,
  role: String
) extends TiDBModel {

  def withFirstName(firstName: String): FundDataInvestmentEntityContact =
    copy(firstName = firstName.take(Constants.MAX_NAME_LENGTH))

  def withLastName(lastName: String): FundDataInvestmentEntityContact =
    copy(lastName = lastName.take(Constants.MAX_NAME_LENGTH))

  def withEmail(email: String): FundDataInvestmentEntityContact = copy(email = email.take(Constants.MAX_EMAIL_LENGTH))

  def withRole(role: String): FundDataInvestmentEntityContact = copy(role = role.take(Constants.MAX_NAME_LENGTH))

}

object FundDataInvestmentEntityContact extends TiDBModelCompanion[FundDataInvestmentEntityContact] {
  override val tableName: String = "pb_fund_data_investment_entity_contact"
}
