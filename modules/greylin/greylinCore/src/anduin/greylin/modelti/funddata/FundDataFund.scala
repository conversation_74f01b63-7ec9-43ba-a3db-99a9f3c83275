// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.funddata

import anduin.greylin.core.const.Constants
import anduin.greylin.core.model.{TiDBModel, TiDBModelCompanion}
import anduin.id.funddata.FundDataFundId

final case class FundDataFund(
  id: FundDataFundId,
  name: String,
  customId: String
) extends TiDBModel {
  def withName(name: String): FundDataFund = copy(name = name.take(Constants.MAX_NAME_LENGTH))
  def withCustomId(customId: String): FundDataFund = copy(customId = customId.take(Constants.MAX_CUSTOM_ID_LENGTH))
}

object FundDataFund extends TiDBModelCompanion[FundDataFund] {
  override val tableName: String = "pb_fund_data_fund"
}
