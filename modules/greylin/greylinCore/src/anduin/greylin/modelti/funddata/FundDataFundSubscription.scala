// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.funddata

import io.circe.{ACursor, DecodingFailure}

import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.funddata.{FundDataFundSubscriptionId, FundDataInvestmentEntityId, FundDataInvestorId}
import anduin.id.fundsub.FundSubLpId

final case class FundDataFundSubscription(
  id: FundDataFundSubscriptionId,
  linkedFundSubOrderId: Option[FundSubLpId],
  linkedInvestmentEntityId: Option[FundDataInvestmentEntityId],
  linkedInvestorId: Option[FundDataInvestorId]
) extends TiDBCdcModel {

  def getLinkedInvestorId: Option[FundDataInvestorId] = {
    linkedInvestmentEntityId.fold(linkedInvestorId)(ieId => Option(ieId.parent))
  }

}

object FundDataFundSubscription extends TiDBCdcModelCompanion[FundDataFundSubscription] {
  override inline def tableName: String = "pb_fund_data_fund_subscription"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, FundDataFundSubscription] = {
    for {
      id <- value.get[FundDataFundSubscriptionId]("id")
      linkedFundSubOrderId <- value.get[Option[FundSubLpId]]("linked_fund_sub_order_id")
      linkedInvestmentEntityId <- value.get[Option[FundDataInvestmentEntityId]]("linked_investment_entity_id")
      linkedInvestorId <- value.get[Option[FundDataInvestorId]]("linked_investor_id")
    } yield FundDataFundSubscription(
      id,
      linkedFundSubOrderId,
      linkedInvestmentEntityId,
      linkedInvestorId
    )
  }

}
