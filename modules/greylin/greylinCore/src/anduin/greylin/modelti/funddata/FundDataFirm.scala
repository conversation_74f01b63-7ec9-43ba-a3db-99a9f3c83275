// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.funddata

import anduin.greylin.core.const.Constants
import anduin.greylin.core.model.{TiDBModel, TiDBModelCompanion}
import anduin.id.funddata.FundDataFirmId
import anduin.id.tag.TagListId

final case class FundDataFirm(
  id: FundDataFirmId,
  name: String,
  riskLevelAssessmentListId: TagListId,
  genericTagListId: TagListId,
  contactTypeListId: TagListId,
  jurisdictionListId: TagListId,
  documentTypeListId: TagListId,
  investorTypeListId: TagListId
) extends TiDBModel {
  def withName(name: String): FundDataFirm = copy(name = name.take(Constants.MAX_NAME_LENGTH))
}

object FundDataFirm extends TiDBModelCompanion[FundDataFirm] {
  override inline def tableName: String = "pb_fund_data_firm"
}
