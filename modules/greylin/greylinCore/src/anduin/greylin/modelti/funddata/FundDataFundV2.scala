// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.funddata

import io.circe.{ACursor, DecodingFailure}

import anduin.greylin.core.const.Constants
import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.funddata.FundDataFundId
import anduin.id.fundsub.FundSubId

final case class FundDataFundV2(
  fundId: FundDataFundId,
  name: String,
  customId: String,
  fundSubscriptionId: Option[FundSubId]
) extends TiDBCdcModel {
  def withName(name: String): FundDataFundV2 = copy(name = name.take(Constants.MAX_NAME_LENGTH))
  def withCustomId(customId: String): FundDataFundV2 = copy(customId = customId.take(Constants.MAX_CUSTOM_ID_LENGTH))
}

object FundDataFundV2 extends TiDBCdcModelCompanion[FundDataFundV2] {
  override val tableName: String = "pb_fund_data_fund_v2"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, FundDataFundV2] = {
    for {
      fundId <- value.get[FundDataFundId]("fund_id")
      name <- value.get[String]("name")
      customId <- value.get[String]("custom_id")
      fundSubscriptionId <- value.get[Option[FundSubId]]("fund_subscription_id")
    } yield FundDataFundV2(
      fundId,
      name,
      customId,
      fundSubscriptionId
    )
  }

}
