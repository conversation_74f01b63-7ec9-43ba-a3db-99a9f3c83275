// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.funddata

import java.time.Instant

import anduin.id.funddata.{FundDataFirmId, FundDataInvestmentEntityDocumentId, FundDataInvestorId}

final case class TransformedFundDataInvestmentEntityDocument(
  id: FundDataInvestmentEntityDocumentId,
  firmId: FundDataFirmId,
  investorId: FundDataInvestorId,
  expireAt: Option[Instant]
)
