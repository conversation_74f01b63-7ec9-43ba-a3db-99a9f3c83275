// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.funddata

import anduin.greylin.core.model.{TiDBModel, TiDBModelCompanion}
import anduin.model.common.user.UserId
import anduin.model.id.TeamId

final case class FundDataFirmGroupMember(
  userId: UserId,
  groupId: TeamId
) extends TiDBModel

object FundDataFirmGroupMember extends TiDBModelCompanion[FundDataFirmGroupMember] {
  override inline def tableName: String = "pb_fund_data_firm_group_member"
}
