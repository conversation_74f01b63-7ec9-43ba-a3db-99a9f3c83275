// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti.funddata

import io.circe.{ACursor, DecodingFailure}

import anduin.greylin.core.const.Constants
import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.funddata.FundDataInvestorId

final case class FundDataInvestor(
  id: FundDataInvestorId,
  name: String,
  customId: String
) extends TiDBCdcModel {
  def withName(name: String): FundDataInvestor = copy(name = name.take(Constants.MAX_NAME_LENGTH))
  def withCustomId(customId: String): FundDataInvestor = copy(customId = customId.take(Constants.MAX_CUSTOM_ID_LENGTH))
}

object FundDataInvestor extends TiDBCdcModelCompanion[FundDataInvestor] {
  override val tableName: String = "pb_fund_data_investor"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, FundDataInvestor] = {
    for {
      id <- value.get[FundDataInvestorId]("id")
      name <- value.get[String]("name")
      customId <- value.get[String]("custom_id")
    } yield FundDataInvestor(
      id,
      name,
      customId
    )
  }

}
