// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti

import io.circe.{ACursor, DecodingFailure}

import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.fundsub.FundSubLpId
import anduin.id.signature.SignatureRequestId

final case class SubscriptionOrderSupportingSignatureRequest(
  id: SignatureRequestId,
  subscriptionOrderId: FundSubLpId
) extends TiDBCdcModel

object SubscriptionOrderSupportingSignatureRequest
    extends TiDBCdcModelCompanion[SubscriptionOrderSupportingSignatureRequest] {

  override val tableName: String = "pb_subscription_order_supporting_signature_request"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, SubscriptionOrderSupportingSignatureRequest] = {
    for {
      id <- value.get[SignatureRequestId]("id")
      subscriptionOrderId <- value.get[FundSubLpId]("subscription_order_id")
    } yield SubscriptionOrderSupportingSignatureRequest(
      id,
      subscriptionOrderId
    )
  }

}
