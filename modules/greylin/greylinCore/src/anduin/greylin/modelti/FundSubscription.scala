// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti

import java.time.{Instant, LocalDate}

import io.circe.{ACursor, DecodingFailure}
import squants.market.Currency

import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.fundsub.FundSubId
import anduin.greylin.core.model.JsonDecoder.given
import anduin.id.entity.EntityId
import anduin.id.environment.EnvironmentId
import anduin.id.form.FormVersionId

final case class FundSubscription(
  id: FundSubId,
  entityId: Option[EntityId],
  name: String,
  currency: Currency,
  targetClosingDate: Option[LocalDate],
  lastActivityAt: Option[Instant],
  fundType: Option[FundSubscriptionFundType],
  flowType: FundSubscriptionFlowType,
  isArchived: Boolean = false,
  isClosed: Boolean = false,
  environmentId: Option[EnvironmentId] = None,
  metaData: Option[String] = None,
  latestFormVersionId: Option[FormVersionId] = None
) extends TiDBCdcModel

object FundSubscription extends TiDBCdcModelCompanion[FundSubscription] {

  override val tableName: String = "pb_fund_subscription"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, FundSubscription] = {
    for {
      id <- value.get[FundSubId]("id")
      entityId <- value.get[Option[EntityId]]("entity_id")
      name <- value.get[String]("name")
      currency <- value.get[Currency]("currency")
      targetClosingDate <- value.get[Option[LocalDate]]("target_closing_date")
      lastActivityAt <- value.get[Option[Instant]]("last_activity_at")
      fundType <- value.get[Option[FundSubscriptionFundType]]("fund_type")
      flowType <- value.get[FundSubscriptionFlowType]("flow_type")
      isArchived <- value.get[Int]("is_archived")
      isClosed <- value.get[Int]("is_closed")
      environmentId <- value.get[Option[EnvironmentId]]("environment_id")
      metaData <- value.get[Option[String]]("metaData")
      latestFormVersionId <- value.get[Option[FormVersionId]]("latest_form_version_id")
    } yield FundSubscription(
      id,
      entityId,
      name,
      currency,
      targetClosingDate,
      lastActivityAt,
      fundType,
      flowType,
      isArchived != 0,
      isClosed != 0,
      environmentId,
      metaData,
      latestFormVersionId
    )
  }

}
