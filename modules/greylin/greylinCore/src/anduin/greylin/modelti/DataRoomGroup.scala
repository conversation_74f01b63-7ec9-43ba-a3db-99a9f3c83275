// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti

import io.circe.{ACursor, DecodingFailure}

import anduin.greylin.core.const.Constants
import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.dataroom.DataRoomGroupId
import anduin.model.id.stage.DataRoomWorkflowId

final case class DataRoomGroup(
  id: DataRoomGroupId,
  dataRoomId: DataRoomWorkflowId,
  name: String,
  groupRole: DataRoomRole
) extends TiDBCdcModel {
  def withName(name: String): DataRoomGroup = copy(name = name.take(Constants.MAX_NAME_LENGTH))
}

object DataRoomGroup extends TiDBCdcModelCompanion[DataRoomGroup] {

  override val tableName: String = "pb_data_room_group"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, DataRoomGroup] = {
    for {
      id <- value.get[DataRoomGroupId]("id")
      dataRoomId <- value.get[DataRoomWorkflowId]("data_room_id")
      name <- value.get[String]("name")
      groupRole <- value.get[DataRoomRole]("group_role")
    } yield DataRoomGroup(
      id,
      dataRoomId,
      name,
      groupRole
    )
  }

}
