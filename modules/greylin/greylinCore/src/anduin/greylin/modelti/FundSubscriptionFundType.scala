// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti

import io.circe.Decoder
import io.getquill.MappedEncoding

import anduin.enumeration.StringEnum

enum FundSubscriptionFundType(val value: String) extends StringEnum {
  case Internal extends FundSubscriptionFundType("INTERNAL")
  case External extends FundSubscriptionFundType("EXTERNAL")
  case Production extends FundSubscriptionFundType("PRODUCTION")
  case Unrecognized extends FundSubscriptionFundType("UNRECOGNIZED")
}

object FundSubscriptionFundType {
  given statusEncode: MappedEncoding[FundSubscriptionFundType, String] = MappedEncoding(_.value)

  given statusDecode: MappedEncoding[String, FundSubscriptionFundType] = MappedEncoding { s =>
    values.find(_.value == s).getOrElse(Unrecognized)
  }

  given jsonDecoder: Decoder[FundSubscriptionFundType] = Decoder.decodeString.map(statusDecode.f)
}
