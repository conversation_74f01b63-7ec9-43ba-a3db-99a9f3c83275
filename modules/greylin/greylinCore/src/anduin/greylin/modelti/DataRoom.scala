// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti

import java.time.LocalDate

import io.circe.{ACursor, DecodingFailure}

import anduin.greylin.core.const.Constants
import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.entity.EntityId
import anduin.model.id.stage.DataRoomWorkflowId

final case class DataRoom(
  id: DataRoomWorkflowId,
  name: String,
  entityId: EntityId,
  isArchived: <PERSON>olean,
  isExpirationDateInheritedFromOrg: Boolean,
  expirationDate: Option[LocalDate]
) extends TiDBCdcModel {
  def withName(name: String): DataRoom = copy(name = name.take(Constants.MAX_NAME_LENGTH))
}

object DataRoom extends TiDBCdcModelCompanion[DataRoom] {

  override val tableName: String = "pb_data_room"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, DataRoom] = {
    for {
      id <- value.get[DataRoomWorkflowId]("id")
      name <- value.get[String]("name")
      entityId <- value.get[EntityId]("entity_id")
      isArchived <- value.get[Int]("is_archived")
      expirationDate <- value.get[Option[LocalDate]]("expiration_date")
      isExpirationDateInheritedFromOrg <- value.get[Int]("is_expiration_date_inherited_from_org")
    } yield DataRoom(
      id,
      name,
      entityId,
      isArchived != 0,
      isExpirationDateInheritedFromOrg != 0,
      expirationDate
    )
  }

}
