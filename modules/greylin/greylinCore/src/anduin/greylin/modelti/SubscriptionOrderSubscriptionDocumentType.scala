// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti

import io.circe.Decoder
import io.getquill.MappedEncoding

import anduin.enumeration.StringEnum

enum SubscriptionOrderSubscriptionDocumentType(val value: String) extends StringEnum {

  case LpFilled extends SubscriptionOrderSubscriptionDocumentType("LP_FILLED")

  case LpSigned extends SubscriptionOrderSubscriptionDocumentType("LP_SIGNED")

  case LpSignedCertificate extends SubscriptionOrderSubscriptionDocumentType("LP_SIGNED_CERTIFICATE")

  case GpCountersigned extends SubscriptionOrderSubscriptionDocumentType("GP_COUNTERSIGNED")

  case GpCountersignedCertificate extends SubscriptionOrderSubscriptionDocumentType("GP_COUNTERSIGNED_CERTIFICATE")

  case Unrecognized extends SubscriptionOrderSubscriptionDocumentType("UNRECOGNIZED")
}

object SubscriptionOrderSubscriptionDocumentType {

  given statusEncode: MappedEncoding[SubscriptionOrderSubscriptionDocumentType, String] = MappedEncoding(_.value)

  given statusDecode: MappedEncoding[String, SubscriptionOrderSubscriptionDocumentType] = MappedEncoding { s =>
    values.find(_.value == s).getOrElse(Unrecognized)
  }

  given jsonDecoder: Decoder[SubscriptionOrderSubscriptionDocumentType] = Decoder.decodeString.map(statusDecode.f)
}
