// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti

import io.circe.{ACursor, DecodingFailure}

import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.docrequest.FormSubmissionId
import anduin.model.id.FileId

final case class SubscriptionOrderSupportingFormFile(
  fileId: FileId,
  formSubmissionId: FormSubmissionId
) extends TiDBCdcModel

object SubscriptionOrderSupportingFormFile extends TiDBCdcModelCompanion[SubscriptionOrderSupportingFormFile] {

  override val tableName: String = "pb_subscription_order_supporting_form_file"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, SubscriptionOrderSupportingFormFile] = {
    for {
      fileId <- value.get[FileId]("file_id")
      formSubmissionId <- value.get[FormSubmissionId]("form_submission_id")
    } yield SubscriptionOrderSupportingFormFile(
      fileId,
      formSubmissionId
    )
  }

}
