// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti

import io.circe.Decoder
import io.getquill.MappedEncoding
import anduin.enumeration.StringEnum

enum DataRoomRole(val value: String) extends StringEnum {

  case Admin extends DataRoomRole("ADMIN")

  case Member extends DataRoomRole("MEMBER")

  case Guest extends DataRoomRole("GUEST")

  case Restricted extends DataRoomRole("RESTRICTED")

  case Empty extends DataRoomRole("EMPTY")

  case Unrecognized extends DataRoomRole("UNRECOGNIZED")
}

object DataRoomRole {

  given statusEncode: MappedEncoding[DataRoomRole, String] = MappedEncoding(_.value)

  given statusDecode: MappedEncoding[String, DataRoomRole] = MappedEncoding { s =>
    values.find(_.value == s).getOrElse(Unrecognized)
  }

  given jsonDecoder: Decoder[DataRoomRole] = Decoder.decodeString.map(statusDecode.f)

}
