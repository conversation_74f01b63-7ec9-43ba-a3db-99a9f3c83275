// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti

import java.time.Instant

import io.circe.{ACursor, DecodingFailure}
import squants.market.{Currency, Money}

import anduin.greylin.core.const.Constants
import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.fundsub.{FundSubCloseId, FundSubId, FundSubLpId}
import anduin.model.common.user.UserId
import anduin.greylin.core.model.JsonDecoder.given
import anduin.id.ModelIdRegistry

final case class SubscriptionOrder(
  id: FundSubLpId,
  fundSubscriptionId: FundSubId,
  mainUserId: UserId,
  investmentEntityName: String = "",
  expectedCommitmentAmount: Option[BigDecimal] = None,
  expectedCommitmentCurrency: Option[Currency] = None,
  commitmentAmount: Option[BigDecimal] = None,
  commitmentCurrency: Option[Currency] = None,
  acceptedCommitmentAmount: Option[BigDecimal] = None,
  acceptedCommitmentCurrency: Option[Currency] = None,
  status: SubscriptionOrderStatus,
  lastActivityAt: Option[Instant] = None,
  metaData: Option[String] = None,
  closeId: Option[FundSubCloseId] = None,
  formProgress: Option[Double] = None
) extends TiDBCdcModel {

  val expectedCommitment: Option[Money] =
    expectedCommitmentAmount.zip(expectedCommitmentCurrency).map { case (amount, currency) =>
      Money(amount, currency)
    }

  val commitment: Option[Money] = commitmentAmount.zip(commitmentCurrency).map { case (amount, currency) =>
    Money(amount, currency)
  }

  val acceptedCommitment: Option[Money] =
    acceptedCommitmentAmount.zip(acceptedCommitmentCurrency).map { case (amount, currency) =>
      Money(amount, currency)
    }

  val commitmentByStatus: Option[Money] = status match {
    case SubscriptionOrderStatus.NotStarted        => expectedCommitment
    case SubscriptionOrderStatus.InProgress        => expectedCommitment
    case SubscriptionOrderStatus.ChangeInProgress  => expectedCommitment
    case SubscriptionOrderStatus.FormFilled        => commitment
    case SubscriptionOrderStatus.PendingReview     => commitment
    case SubscriptionOrderStatus.FormReviewed      => commitment
    case SubscriptionOrderStatus.PendingSignature  => commitment
    case SubscriptionOrderStatus.PendingSubmission => commitment
    case SubscriptionOrderStatus.PendingApproval   => commitment
    case SubscriptionOrderStatus.Submitted         => commitment
    case SubscriptionOrderStatus.Countersigned     => commitment
    case SubscriptionOrderStatus.Complete          => acceptedCommitment
    case SubscriptionOrderStatus.Removed           => None
    case SubscriptionOrderStatus.Unrecognized      => None
  }

  def withExpectedCommitment(expectedCommitment: Option[Money]): SubscriptionOrder = copy(
    expectedCommitmentAmount = expectedCommitment.map(_.amount).filter(_ <= Constants.MAX_MONEY_AMOUNT),
    expectedCommitmentCurrency = expectedCommitment.map(_.currency)
  )

  def withCommitment(commitment: Option[Money]): SubscriptionOrder = copy(
    commitmentAmount = commitment.map(_.amount).filter(_ <= Constants.MAX_MONEY_AMOUNT),
    commitmentCurrency = commitment.map(_.currency)
  )

  def withAcceptedCommitment(acceptedCommitment: Option[Money]): SubscriptionOrder = copy(
    acceptedCommitmentAmount = acceptedCommitment.map(_.amount).filter(_ <= Constants.MAX_MONEY_AMOUNT),
    acceptedCommitmentCurrency = acceptedCommitment.map(_.currency)
  )

  def withInvestmentEntityName(investmentEntityName: String): SubscriptionOrder = copy(
    investmentEntityName = investmentEntityName.take(Constants.MAX_NAME_LENGTH)
  )

}

object SubscriptionOrder extends TiDBCdcModelCompanion[SubscriptionOrder] {

  override inline def tableName: String = "pb_subscription_order"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, SubscriptionOrder] = {
    for {
      id <- value.get[FundSubLpId]("id")
      fundSubscriptionId <- value.get[FundSubId]("fund_subscription_id")
      mainUserId <- value.get[UserId]("main_user_id")
      investmentEntityName <- value.get[String]("investment_entity_name")
      expectedCommitmentAmount <- value.get[Option[BigDecimal]]("expected_commitment_amount")
      expectedCommitmentCurrency <- value.get[Option[Currency]]("expected_commitment_currency")
      commitmentAmount <- value.get[Option[BigDecimal]]("commitment_amount")
      commitmentCurrency <- value.get[Option[Currency]]("commitment_currency")
      acceptedCommitmentAmount <- value.get[Option[BigDecimal]]("accepted_commitment_amount")
      acceptedCommitmentCurrency <- value.get[Option[Currency]]("accepted_commitment_currency")
      status <- value.get[SubscriptionOrderStatus]("status")
      lastActivityAt <- value.get[Option[Instant]]("last_activity_at")
      metaData <- value.get[Option[String]]("metaData")
      closeId <- value.get[Option[String]]("close_id")
      formProgress <- value.get[Option[Double]]("form_progress")
    } yield SubscriptionOrder(
      id = id,
      fundSubscriptionId = fundSubscriptionId,
      mainUserId = mainUserId,
      investmentEntityName = investmentEntityName,
      expectedCommitmentAmount = expectedCommitmentAmount,
      expectedCommitmentCurrency = expectedCommitmentCurrency,
      commitmentAmount = commitmentAmount,
      commitmentCurrency = commitmentCurrency,
      acceptedCommitmentAmount = acceptedCommitmentAmount,
      acceptedCommitmentCurrency = acceptedCommitmentCurrency,
      status = status,
      lastActivityAt = lastActivityAt,
      metaData = metaData,
      closeId = closeId.flatMap { closeIdString =>
        ModelIdRegistry.parser.parseAs[FundSubCloseId](closeIdString)
      },
      formProgress = formProgress
    )
  }

}
