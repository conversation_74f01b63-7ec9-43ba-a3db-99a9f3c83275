// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti

import io.circe.Decoder
import io.getquill.MappedEncoding

import anduin.enumeration.StringEnum

enum SubscriptionOrderSupportingDocumentStatusType(val value: String) extends StringEnum {

  case PendingSubmission extends SubscriptionOrderSupportingDocumentStatusType("PENDING_SUBMISSION")

  case ChangesInProgress extends SubscriptionOrderSupportingDocumentStatusType("CHANGES_IN_PROGRESS")

  case PendingReview extends SubscriptionOrderSupportingDocumentStatusType("PENDING_REVIEW")

  case Complete extends SubscriptionOrderSupportingDocumentStatusType("COMPLETE")

  case Unrecognized extends SubscriptionOrderSupportingDocumentStatusType("UNRECOGNIZED")
}

object SubscriptionOrderSupportingDocumentStatusType {

  given statusEncode: MappedEncoding[SubscriptionOrderSupportingDocumentStatusType, String] = MappedEncoding(_.value)

  given statusDecode: MappedEncoding[String, SubscriptionOrderSupportingDocumentStatusType] = MappedEncoding { s =>
    values.find(_.value == s).getOrElse(Unrecognized)
  }

  given jsonDecoder: Decoder[SubscriptionOrderSupportingDocumentStatusType] = Decoder.decodeString.map(statusDecode.f)
}
