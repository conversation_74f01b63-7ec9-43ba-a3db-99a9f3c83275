// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.modelti

import io.circe.{ACursor, DecodingFailure}

import anduin.greylin.core.model.{TiDBCdcModel, TiDBCdcModelCompanion}
import anduin.id.fundsub.FundSubLpId
import anduin.model.common.user.UserId

final case class SubscriptionOrderMember(
  subscriptionOrderId: FundSubLpId,
  userId: UserId
) extends TiDBCdcModel

object SubscriptionOrderMember extends TiDBCdcModelCompanion[SubscriptionOrderMember] {

  override val tableName: String = "pb_subscription_order_member"

  override def fromCdcEvent(value: ACursor): Either[DecodingFailure, SubscriptionOrderMember] = {
    for {
      subscriptionOrderId <- value.get[FundSubLpId]("subscription_order_id")
      userId <- value.get[UserId]("user_id")
    } yield SubscriptionOrderMember(
      subscriptionOrderId,
      userId
    )
  }

}
