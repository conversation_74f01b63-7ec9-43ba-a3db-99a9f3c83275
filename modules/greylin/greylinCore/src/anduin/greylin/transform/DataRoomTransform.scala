// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin.transform

import zio.{Task, ZIO}

import anduin.greylin.GreylinDataService
import anduin.greylin.core.cdc.TypedEvent
import anduin.greylin.core.transform.{Transform, TransformGroupEnum}
import anduin.greylin.modelti.{DataRoom, DataRoomParticipant, TransformedDataRoom}
import anduin.greylin.operation.{DataRoomParticipantOperations, TransformedDataRoomOperations}
import anduin.kafka.KafkaService
import anduin.model.id.stage.DataRoomWorkflowId

private[greylin] final case class DataRoomTransform(
)(
  using override val kafkaService: KafkaService,
  greylinDataService: GreylinDataService
) extends Transform {

  override val groupName: TransformGroupEnum = TransformGroupEnum.DataRoomTransform

  override val inputs: List[INPUT] = List(
    registerInput(DataRoom -> handleDataRoomEvent),
    registerInput(DataRoomParticipant -> handleDataRoomParticipantEvent)
  )

  private def handleDataRoomEvent(event: TypedEvent[DataRoom]): Task[Unit] = {
    event match {
      case TypedEvent.InsertEvent(newDataRoom) =>
        for {
          participants <- greylinDataService.run(
            DataRoomParticipantOperations.getByDataRoomId(newDataRoom.id)
          )
          _ <- greylinDataService.runUnit(
            TransformedDataRoomOperations.insert(
              TransformedDataRoom(
                id = newDataRoom.id,
                name = newDataRoom.name,
                totalParticipant = participants.length
              )
            )
          )
        } yield ()
      case TypedEvent.UpdateEvent(_, newDataRoom) =>
        greylinDataService.runUnit(
          TransformedDataRoomOperations.update(newDataRoom.id) { curModel =>
            curModel.copy(
              name = newDataRoom.name
            )
          }
        )
      case TypedEvent.DeleteEvent(dataRoom) =>
        greylinDataService.runUnit(
          TransformedDataRoomOperations.delete(dataRoom.id)
        )
    }
  }

  private def handleDataRoomParticipantEvent(event: TypedEvent[DataRoomParticipant]): Task[Unit] = {
    event match {
      case TypedEvent.InsertEvent(participant) => updateTotalParticipant(participant.dataRoomId)
      case TypedEvent.DeleteEvent(participant) => updateTotalParticipant(participant.dataRoomId)
      case TypedEvent.UpdateEvent(_, _)        => ZIO.attempt(())
    }
  }

  private def updateTotalParticipant(dataRoomId: DataRoomWorkflowId) = {
    for {
      count <- greylinDataService.run(
        DataRoomParticipantOperations.countByDataRoomId(dataRoomId)
      )
      _ <- greylinDataService.runUnit(
        TransformedDataRoomOperations.updateIfExist(dataRoomId) { curModel =>
          curModel.copy(totalParticipant = count.toInt)
        }
      )
    } yield ()
  }

}
