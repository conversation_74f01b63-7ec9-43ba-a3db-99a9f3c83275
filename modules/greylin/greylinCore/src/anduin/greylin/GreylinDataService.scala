// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin

import javax.sql.DataSource
import zio.{Task, ZIO}

import anduin.tidb.{TiDBEnvironment, TiDatabase}
import com.anduin.stargazer.service.GondorBackendConfig

final case class GreylinDataService(
  backendConfig: GondorBackendConfig,
  tiDBEnvironment: TiDBEnvironment
) {

  def run[A](
    task: ZIO[DataSource, Throwable, A]
  ): Task[A] =
    TiDatabase.run(task).provideEnvironment(tiDBEnvironment.environment)

  def runUnit[A](
    task: ZIO[DataSource, Throwable, A]
  ): Task[Unit] = {
    // Since data pipeline is in development, we log and ignore all errors for not interrupting production
    run(task).unit
      .catchAll {
        case TiDatabase.DataPipelineDisabledException =>
          ZIO.logInfo(TiDatabase.DataPipelineDisabledException.getMessage)
        case ex: Throwable => ZIO.fail(ex)
      }
      .catchAllCause(err => ZIO.logInfo(s"Got error in Data Pipeline $err"))
  }

}
