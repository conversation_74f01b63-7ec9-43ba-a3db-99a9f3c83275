// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.greylin

import zio.{Task, ZIO}

import anduin.greylin.core.transform.Transform
import anduin.greylin.transform.{
  DataRoomTransform,
  FundDataInvestmentEntityDocumentTransform,
  FundSubscriptionTransform,
  SubscriptionOrderSupportingFormFileTransform
}
import anduin.kafka.{KafkaFiber, KafkaService}
import com.anduin.stargazer.service.GondorBackendConfig

final case class GreylinTransformService(
  backendConfig: GondorBackendConfig
)(
  using val kafkaService: KafkaService,
  val greylinDataService: GreylinDataService
) extends KafkaFiber {

  private lazy val transformations: List[Transform] = List(
    FundSubscriptionTransform(),
    DataRoomTransform(),
    FundDataInvestmentEntityDocumentTransform(),
    SubscriptionOrderSupportingFormFileTransform()
  )

  override def start(): Task[Unit] =
    ZIO
      .when(backendConfig.dataPipelineConfig.enableDataTransform)(
        ZIO.collectAllParDiscard(
          transformations.map(_.start())
        )
      )
      .unit

  override def close(): Task[Unit] =
    ZIO
      .when(backendConfig.dataPipelineConfig.enableDataTransform)(
        ZIO.collectAllParDiscard(
          transformations.map(_.close())
        )
      )
      .unit

}
