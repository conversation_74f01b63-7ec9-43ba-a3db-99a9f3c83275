// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub

import anduin.fdb.record.FDBOperations
import anduin.fdb.record.model.{RecordTask, RecordIO, RecordReadTask}
import anduin.fundsub.LpFormDataStoreProvider.lpFormDataKeyMapping
import anduin.id.fundsub.FundSubLpFormIdTrait
import anduin.protobuf.fundsub.lpformdata.LpFormData

final case class LpFormDataOperations(store: LpFormDataStoreProvider.Store) {

  def getOpt(formId: FundSubLpFormIdTrait): RecordReadTask[Option[LpFormData]] = {
    store.getOpt(formId)
  }

  def getMultiple[I <: FundSubLpFormIdTrait](
    formIds: List[I]
  ): RecordTask[List[(I, LpFormData)]] = {
    RecordIO
      .parTraverseN(32)(formIds.distinct) { id =>
        getOpt(id).map(_.map(id -> _))
      }
      .map(_.flatten.toList)
  }

  def add(record: LpFormData): RecordTask[Unit] = {
    store.create(record.withoutPatchEffects).unit
  }

  def updateOrAdd(
    formId: FundSubLpFormIdTrait,
    defaultData: LpFormData,
    updateFunc: LpFormData => LpFormData
  ): RecordTask[Unit] = {
    getOpt(formId).flatMap {
      _.map { oldMsg =>
        store.update(updateFunc(oldMsg).withoutPatchEffects).unit
      } getOrElse add(defaultData.withoutPatchEffects)
    }
  }

  def updateIgnoreNonExisted(
    formId: FundSubLpFormIdTrait,
    updateFunc: LpFormData => LpFormData
  ): RecordTask[Unit] = {
    for {
      oldMsgOpt <- getOpt(formId)
      _ <- oldMsgOpt.map { oldMsg =>
        store.update(updateFunc(oldMsg).withoutPatchEffects).unit
      } getOrElse RecordIO.logWarning(s"Form ID ${formId.idString} is not existed")
    } yield ()
  }

  def upsert(
    record: LpFormData
  ): RecordTask[Unit] = {
    store.upsert(record).unit
  }

  def idExisted(formId: FundSubLpFormIdTrait): RecordTask[Boolean] = {
    store.exist(formId)
  }

  def delete(formId: FundSubLpFormIdTrait): RecordTask[Boolean] = {
    store.delete(formId)
  }

}

object LpFormDataOperations
    extends FDBOperations.Single[LpFormDataStoreProvider.RecordEnum, LpFormDataOperations](LpFormDataStoreProvider)
