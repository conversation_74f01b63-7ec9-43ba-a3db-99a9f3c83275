import mill.*, scalalib.*
import coursier.maven.MavenRepository
import mill.meta.MillBuildRootModule

object `package` extends MillBuildRootModule {

  override def scalacOptions = super.scalacOptions() ++ Seq("-no-indent", "-old-syntax")

  override def repositoriesTask = Task.Anon {
    super.repositoriesTask() ++ Seq(
      MavenRepository(
        "https://artifactory.anduin.co/artifactory/anduin-internal-libraries-maven/",
        authentication = Some(
          coursier.core.Authentication(
            user = "build",
            password = "AP5bBJhttbUSbELN98ypHmjSFHWEYnCwtGeS9H",
            optional = false,
            realmOpt = Option("Artifactory Realm"),
            httpsOnly = true,
            passOnRedirect = true
          )
        )
      ),
      MavenRepository("https://oss.sonatype.org/content/repositories/snapshots/")
    )
  }

}
